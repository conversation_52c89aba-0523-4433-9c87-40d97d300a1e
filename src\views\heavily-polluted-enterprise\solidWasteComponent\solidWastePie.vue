<template>
  <div class="app_rank">
    <div id="wastebehaviour"></div>
    <div class="center">
      <div style="font-size: 12px;font-family: PingFang SC;font-weight: 400;color: #C5CECD;">固废总数(kg)</div>
      <div style="font-size: 23px;font-family: DIN;font-weight: 500;color: #E3F4F3;">{{total}}</div>
    </div>
  </div>
</template>

<script>
import * as echarts from "echarts";
export default {
  name: "carRank",
  props: {
    echartData:{
      type: Object|Array,
      default:()=>({})
    }
  },
  components: {},
  data() {
    return {
      colorArr: ["#53E8BF", "#53D0F0", "#F2B949", "#FFFFFF", "#A374B7", "#5EA952", "#6896EC", "#7157E4", "#C35DAC", "#D8E661", "#9D3A1C", "#EB905F"],
      setitiem: 0,
      myChart1: "",
      echartsData: [
        {name:'废机油格类',value:15, color:'#53E8BF'},
        {name:'废矿物油',value:25, color:'#53D0F0'},
        {name:'废铅蓄电池',value:14, color:'#F2B949'},
        {name:'废机油壶类',value:5, color:'#FFFFFF'},
        {name:'废漆渣',value:25, color:'#A374B7'},
        {name:'废活性炭类',value:25, color:'#5EA952'},
        {name:'沾油棉纱类',value:25, color:'#6896EC'},
        {name:'废电池',value:25, color:'#7157E4'},
        {name:'废机油',value:25, color:'#C35DAC'},
        {name:'废有机溶剂',value:25, color:'#D8E661'},
        {name:'废过滤棉类',value:25, color:'#9D3A1C'},
        {name:'废漆渣桶(含金属罐)',value:25, color:'#EB905F'},
      ],
      total:0,
    };
  },
  watch: {
    echartData: {
      handler(nval, oval) {
        if (nval) {
          if (this.myChart1) {
            this.myChart1.dispose();
          }
          this.echartsData.forEach((v,i)=>{
            if(i===0){
              v.value = nval.wasteOilFilter||0
            }
            if(i===1){
              v.value = nval.wasteMineralOil||0
            }
            if(i===2){
              v.value = nval.wasteLeadBattery||0
            }
            if(i===3){
              v.value = nval.wasteOilCan||0
            }
            if(i===4){
              v.value = nval.wastePaint||0
            }
            if(i===5){
              v.value = nval.wasteActivatedCarbon||0
            }
            if(i===6){
              v.value = nval.wasteOiledCottonYarn||0
            }
            if(i===7){
              v.value = nval.wasteBattery||0
            }
            if(i===8){
              v.value = nval.wasteEngineOil||0
            }
            if(i===9){
              v.value = nval.wasteOrganicSolvents||0
            }
            if(i===10){
              v.value = nval.wasteFilterCotton||0
            }
            if(i===11){
              v.value = nval.wastePaintBucket||0
            }
          })
          this.initEchart();
        }else{
          if (this.myChart1) {
            this.myChart1.dispose();
          }
          this.echartsData.forEach((v,i)=>{
            if(i===0){
              v.value = 0
            }
            if(i===1){
              v.value = 0
            }
            if(i===2){
              v.value = 0
            }
            if(i===3){
              v.value = 0
            }
            if(i===4){
              v.value = 0
            }
            if(i===5){
              v.value = 0
            }
            if(i===6){
              v.value = 0
            }
            if(i===7){
              v.value = 0
            }
            if(i===8){
              v.value = 0
            }
            if(i===9){
              v.value = 0
            }
            if(i===10){
              v.value = 0
            }
            if(i===11){
              v.value = 0
            }
          })
          this.initEchart();
        }
      },
      deep: true,
      // immediate: true,
    },
  },
  mounted() {
    // this.initEchart();
  },
  beforeUnmount() {
    clearInterval(this.setitiem);
  },
  methods: {
    initEchart() {
      const chartMonth = echarts.init(document.getElementById("wastebehaviour"));
      this.myChart1 = chartMonth;
      chartMonth.clear();
      chartMonth.showLoading();
      this.total = this.echartData?(this.echartData.wasteTotal||0):0
      let option = {
        color: this.colorArr,
        tooltip: {
          show: true,
          trigger: "item",
          formatter:(params)=>{
            // style="color:${params.data.color}"
            const str = `
              <div style="padding:0.02rem;">
                <div>${params.name}</div>
                <div>
                  ${params.marker} ${params.value}Kg <span style="margin-left:0.07rem;">${params.percent}%</span>
                </div>
              </div>
            `
            return str
          }
        },
        legend: {
          show: true,
          bottom: 0,
          left: 5,
          textStyle: {
            color: "#86BED1",
          },
          icon: "roundRect",
          itemWidth: 15,
          itemHeight: 2,
          formatter(name){
            if(name!='废漆渣桶(含金属罐)'&&name.length<5){
              return name.length==4?`${name}   `:`${name}      `
            }
            return name
          }
        },
        grid: {
          // containLabel: true,
          left: "8%",
          top: "5%",
          right: "0%",
          bottom: "2%",
          // backgroundColor:'rgba(33,64,121, 0.8)'
        },
        xAxis: {
          // data: this.chartX,
          axisTick: {
            show: false,
          },
          axisLabel: {
            color: "#80B6C8",
            interval: "auto",
            // rotate: -35,
            fontSize: 12,
          },
          axisLine: {
            show: false,
            lineStyle: {
              color: "rgba(56,89,130,1)",
              type: [5, 5],
            },
          },
        },
        yAxis: {
          // name:'单位:'+'',
          nameTextStyle: {
            color: "#80B6C8",
            padding: [0, 40, 0, 0],
          },
          splitLine: {
            lineStyle: {
              color: "rgba(56,89,130,0.7)",
              type: [5, 5],
            },
          },
          axisLine: {
            show: false,
            lineStyle: {
              color: "#1B5BBA",
            },
          },
          axisLabel: {
            color: "#80B6C8",
            interval: 0,
          },
        },
        series: [
          {
            name: "固废1",
            type: "pie",
            // radius: ["55%", "65%"],
            radius: ["53%", "65%"],
            center: ['50%', '37%'],
            avoidLabelOverlap: false,
            selectedOffset: 0,
            itemStyle: {
              // borderRadius: 10,
              borderColor: "#010614",
              borderWidth: 3,
            },
            label: {
              show: false,
              position: "center",
            },
            emphasis: {
              scale: false,
              scaleSize: 2,
              label: {
                show: false,
                fontSize: "30",
                fontWeight: "bold",
                formatter: ["{a1|{b}}", "{b1|{c}}"].join("\n"),
                rich: {
                  a1: {
                    color: "#C5CECD",
                    fontSize: 14,
                    lineHeight: 25,
                  },
                  b1: {
                    color: "#E3F4F3",
                    fontSize: 22,
                  },
                },
              },
            },
            labelLine: {
              show: false,
            },
            data: this.echartsData,
          },
          {
            name: "固废2",
            type: "pie",
            radius: ["39%", "55%"],
            center: ['50%', '37%'],
            avoidLabelOverlap: false,
            selectedOffset: 7,
            silent: true,
            itemStyle: {
              color: (val) => {
                // const rgba = this.toRgba(this.colorArr[val.dataIndex])
                const rgba = this.toRgba(val.data.color);
                // console.log(rgba,val,'颜色');
                return rgba + ",0.3)";
              },
              // borderRadius: 10,
              borderColor: "#010614",
              borderWidth: 3,
            },
            label: {
              show: false,
              position: "center",
            },
            emphasis: {
              scale: false,
              scaleSize: 3,
              label: {
                show: false,
                fontSize: "30",
                fontWeight: "bold",
                formatter: ["{a1|{b}}", "{b1|{c}}"].join("\n"),
                rich: {
                  a1: {
                    color: "#C5CECD",
                    fontSize: 14,
                    lineHeight: 25,
                  },
                  b1: {
                    color: "#E3F4F3",
                    fontSize: 22,
                  },
                },
              },
            },
            labelLine: {
              show: false,
            },
            data: this.echartsData,
          },
        ],
      };
      chartMonth.setOption(option);
      chartMonth.hideLoading();
      const _this=this
      chartMonth.on('legendselectchanged', function(obj) {
          const {selected,name} = obj
          const arr  = _this.echartsData.filter(v=>(selected[v.name])).map(o=>o.value)
          _this.total = arr.reduce( (prev,cur) => prev+cur ).toFixed(1)
      });
      this.myChart1.on("highlight", { seriesIndex: 1 }, function(params) {
        // console.log('触发高亮');
      });
      if (!this.myChart1) {
        return;
      }
    },
    toRgba(colors) {
      // 16进制颜色值的正则
      let reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/;
      // 把颜色值变成小写
      let color = colors.toLowerCase();
      if (reg.test(color)) {
        // 如果只有三位的值，需变成六位，如：#fff => #ffffff
        if (color.length === 4) {
          let colorNew = "#";
          for (let i = 1; i < 4; i += 1) {
            colorNew += color.slice(i, i + 1).concat(color.slice(i, i + 1));
          }
          color = colorNew;
        }
        // 处理六位的颜色值，转为RGB
        let colorChange = [];
        for (let i = 1; i < 7; i += 2) {
          colorChange.push(parseInt("0x" + color.slice(i, i + 2)));
        }
        return "rgba(" + colorChange.join(",");
      } else {
        return color;
      }
    },
  },
};
</script>
<style scoped>
#wastebehaviour {
  width: 100%;
  height: 2.8rem;
  background-image: url('../../../assets/solid-waste/<EMAIL>');
  background-position: 50% 0%;
  background-size: 2.1rem 2.1rem;
  background-repeat: no-repeat;
}
.app_rank{
  position: relative;
}
.no-data {
  font-size: 0.2rem;
  margin-top: 100px;
  margin-left: 160px;
}
.center{
  position: absolute;
  text-align: center;
  pointer-events: none;
  left: 0;
  right: 0;
  top: 0.8rem;
}
</style>
