
import {
  getTaskDetail
} from '@/api/emergencyDetails/emergencyEvent'

import { shallowRef, watch } from 'vue'

/**
 * 获取应急任务详情
 * @returns getEventInfo
 */
export default function getEventInfo() {

  // 任务id
  const taskId = shallowRef('')

  // 详情加载状态
  const getTaskDetailLoading = shallowRef(false)

  // 任务详情数据
  const taskDetail = shallowRef({})

  /**
   * 获取应急任务分类统计
   */
  function getInfo() {
    // if (!taskId.value) return

    getTaskDetailLoading.value = true

    getTaskDetail(taskId.value)
      .then((res) => {
        if (!res) throw new Error('请求事件详情失败,无返回数据')
        const { code, data } = res
        console.log('详情数据----36', data);
        if (code === 200) {
          taskDetail.value = data;
        }else{
          taskDetail.value = {
            departmentExecutors:[],
            excutor:[],
            maxTime: null,
            minTime: null,
            task:{},
            taskId: '',
            taskTimeShaft:[]
          }
        }
      })
      .catch((err) => {
        console.log('获取事件详情数据失败', err)
      })
      .finally(() => {
        // console.log('获取事件详情数据请求完成')
        getTaskDetailLoading.value = false
      })
  }

  watch(taskId, getInfo)

  return {
    taskId,
    getTaskDetailLoading,
    taskDetail
  }
}