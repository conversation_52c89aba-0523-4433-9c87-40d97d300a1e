<style lang="less" scoped>
#myvideo {
  width: 410px;
  background: #000;
}
.water-video {
  padding: 0.2rem 0.2rem 0.2rem;
  position: relative;
  .water-video-conetnt {
    box-sizing: border-box;
    padding: 0.5rem 1rem;
    height: 100%;
    background-image: url(../../assets/water-video.png) !important;
    background-size: 100% 100% !important;
    .video-select {
      display: flex;
    }
    .video-list {
      margin-top: 0.2rem;
      height: calc(100% - 0.8rem);
      display: flex;
      flex-wrap: wrap;
      overflow-y: scroll;
      .video-list-content {
        width: 3.1rem;
        height: 2.7rem;
        margin: 0 0.08rem;
        margin-bottom: 0.3rem;
        > :nth-of-type(2) {
          margin-top: 0.15rem;
          p {
            margin-bottom: 0;
            font-size: 0.18rem;
          }
        }
      }
    }
  }
  video {
    outline: none;
  }
}
</style>

<style lang="less">
.water-video {
  .video-select {
    .title-video-select {
      display: flex;
      justify-content: space-between;
      margin-right: 0.1rem;
      .ant-select-selection {
        min-width: 1.5rem;
        height: 0.3rem;
        // background: rgba(14, 139, 255, 0.32);
        border: none;
        border-radius: unset;
        // clip-path: polygon(100% 0, 100% 100%, 12% 100%, 0% 70%, 0 0);
        background: url(../../assets/<EMAIL>);
        background-size: 100% 100%;
        background-repeat: no-repeat;
        display: flex;
        justify-content: center;
      }
      .ant-select-selection-selected-value {
        color: rgba(0, 234, 255, 1);
      }
      .ant-select-focused .ant-select-selection,
      .ant-select-selection:focus,
      .ant-select-selection:active {
        border-color: #40a9ff;
        border-right-width: 0 !important;
        outline: 0;
        box-shadow: none;
      }
    }
  }
  // ::-webkit-scrollbar {
  //   width: 0 !important;
  // }
  .video-list-content {
    .video-list-content {
      .vjs-custom-skin {
        height: 1.9rem;
      }
    }
    .video-js.vjs-fluid {
      height: 100% !important;
    }
  }
  .vjs-poster {
    background-size: 100% 100%;
  }
}
</style>

<template>
  <section class="water-video">
    <section class="water-video-conetnt">
      <section class="video-select">
        <!-- 街道 -->
        <a-select
          v-model="defaultStreetValue"
          class="title-video-select"
          @change="streetChange"
        >
          <a-icon
            slot="suffixIcon"
            type="caret-down"
            style="color: rgba(0, 234, 255, 1); width: 14px; height: 8px"
          />
          <a-select-option
            :value="String(index)"
            v-for="(item, index) in deptCarList"
            :key="index"
          >
            {{ item.deptName }}</a-select-option
          >
        </a-select>
        <!-- 车辆 -->
        <a-select
          v-model="defaultCarValue"
          class="title-video-select"
          @change="carChange"
        >
          <a-icon
            slot="suffixIcon"
            type="caret-down"
            style="color: rgba(0, 234, 255, 1); width: 14px; height: 8px"
          />
          <a-select-option
            :value="String(index)"
            v-for="(item, index) in deptCarList[defaultStreetValue].carList"
            :key="index"
          >
            {{ item.license }}</a-select-option
          >
        </a-select>
        <a-select
          v-model="defaultStationValue"
          class="title-video-select"
          @change="waterStationChange"
        >
          <a-icon
            slot="suffixIcon"
            type="caret-down"
            style="color: rgba(0, 234, 255, 1); width: 14px; height: 8px"
          />
          <a-select-option
            :value="item.cameraSerial"
            v-for="(item, index) in cameraList"
            :key="index"
          >
            {{ item.cameraName }}</a-select-option
          >
        </a-select>
      </section>
      <section class="video-list">
        <!-- <video
          id="videoElement"
          controls="true"
          height="205"
          width="auto"
        ></video> -->
        <div style="display: flex; flex-wrap: wrap">
          <section
            v-for="(item, index) in urlList"
            :key="item.url"
            class="video-list-content"
          >
            <div v-if="!liveTimeOut && item.online">
              <div style="height: 2rem">
                <!-- <video id="myvideo" controls="true" @click="click"></video> -->
                <video
                  :id="`myvideo${item.url}`"
                  controls="true"
                  style="width: 3.1rem; height: 2rem; background: #000"
                ></video>
              </div>
              <div>
                <p style="text-align: center">
                  {{
                    (videoText.length > 0 ? videoText[index].street : '') +
                    ' ' +
                    (videoText.length > 0 ? videoText[index].license : '')
                  }}
                </p>
              </div>
            </div>
            <div v-if="liveTimeOut">
              <div
                style="
                  background: rgba(8, 45, 120, 0.5);
                  width: 3.1rem;
                  height: 2rem;
                  text-align: center;
                  padding-top: 0.5rem;
                  box-sizing: border-box;
                "
              >
                <img src="@/assets/noCamera.png" alt="" />
                <br />
                <br />
                观看已超时，请切换摄像头或刷新
              </div>
            </div>
            <div v-if="!item.online">
              <div
                style="
                  background: rgba(8, 45, 120, 0.5);
                  width: 3.1rem;
                  height: 2rem;
                  text-align: center;
                  padding-top: 0.5rem;
                  box-sizing: border-box;
                "
              >
                <img src="@/assets/noCamera.png" alt="" />
                <br />
                <br />
                {{ item.license + (item.online ? `（在线）` : `（不在线）`) }}
              </div>
            </div>
          </section>
        </div>
      </section>
    </section>
  </section>
</template>

<script lang="ts">
/* eslint-disable @typescript-eslint/ban-ts-ignore */
import { Component, Vue, Watch } from 'vue-property-decorator'
import { Table, Radio, Select, Icon, message } from 'ant-design-vue'
import 'video.js/dist/video-js.css'
// import "videojs-contrib-hls";
// import "videojs-flash";
import { getCameraList, getCameraSerial, keepAlive } from '@/api/vehicles'
import flvjs from 'flv.js'
import { socketUrl } from '@/utils/index'
@Component({
  name: 'waterVideo',
  components: {
    ATable: Table,
    ARadioGroup: Radio.Group,
    ARadioButton: Radio.Button,
    ASelect: Select,
    AIcon: Icon,
    ASelectOption: Select.Option
  }
})
export default class extends Vue {
  private videoListUrl: any[] = []
  mounted() {
    this.getStationList()
    this.getVideo()
    this.connect()
  }
  beforeDestroy() {
    clearInterval(this.requestTime)
    if (this.flvPlayer.length > 0) {
      this.urlList.forEach((item: any, index: number) => {
        this.flvPlayer[index].pause()
        this.flvPlayer[index].unload()
        this.flvPlayer[index].detachMediaElement()
        this.flvPlayer[index].destroy()
        this.flvPlayer[index] = null
      })
    }
  }
  private requestTime: any = ''
  private socket: any = null
  // 建立连接
  private connect() {
    // this.socket = new WebSocket("wss://www.longyaniot.com/wss/websocket");
    // this.socket = new WebSocket("ws://192.168.0.135:22000/ws");
    // this.socket = new WebSocket("ws://ep.vankeytech.com:8835/ws");
    // this.socket = new WebSocket("ws://www.jinnq.com:8834/ws");
    this.socket = new WebSocket(socketUrl())
    // 监听socket连接
    this.socket.onopen = this.open
    // 监听socket错误信息
    this.socket.onerror = this.error
    // 监听socket消息
    this.socket.onmessage = this.getMessage
    window.onbeforeunload = () => {
            this.socket.onopen = () => {}
            this.socket.onerror = () => {}
            this.socket.onmessage = () => {}
            this.socket.close();
        };
  }
  private open() {
    console.log('socket连接成功')
    this.send()
  }
  private error() {
    console.log('系统连接错误')
  }
  private send() {
    this.socket.send(JSON.stringify({ code: 1, token: 'hello123' }))
    this.socket.send(JSON.stringify({ code: 11 }))
    // this.socket.send(
    //   JSON.stringify({
    //     token: 123456,
    //     params: { tdh: "1", tid: "015882039356", type: 1 }
    //   })
    // );
  }
  private cameraSerial: any = []
  // 街道
  private deptCarList: any = [{ carlist: [] }]
  private defaultStreetValue: any = '0'
  private defaultCarValue: any = '0'
  // 获得数据
  private getMessage(msg: any) {
    const res: any = JSON.parse(msg.data)
    if (res.code === -11) {
      // 车辆监控列表
      this.deptCarList = res.cameraMap.carList
      this.deptCarList.unshift({
        deptName: '全部',
        carList: [{ license: '全部' }]
      })
      // for (const item of this.deptCarList) {
      //   item.carList.unshift({ license: "全部" });
      // }
    }
  }
  private carId = ''
  private streetChange(val: any) {
    this.defaultStreetValue = val
    this.defaultCarValue = '0'
    const carId: any =
      this.deptCarList[this.defaultStreetValue].carList[this.defaultCarValue]
        .carId
    this.carId = carId
    this.getVideo(carId)
  }
  // 车辆筛选
  private carChange(val: any) {
    this.defaultCarValue = val
    const carId: any =
      this.deptCarList[this.defaultStreetValue].carList[this.defaultCarValue]
        .carId
    this.getVideo(carId)
  }
  private urlList: any[] = []
  private videoText: any[] = []
  private flvPlayer: any = []
  private addVideoListUrl() {
    // this.videoListUrl = [];
    // this.flvPlayer = [];
    // for (const item of this.urlList) {
    //   this.flvPlayer.push(null);
    // }
    if (this.urlList.length === 0) {
      return
    }
    console.log(this.urlList, 323232)
    this.urlList.forEach((item: any, index: number) => {
      this.$nextTick(() => {
        if (this.flvPlayer[index]) {
          this.flvPlayer[index].pause()
          this.flvPlayer[index].unload()
          this.flvPlayer[index].detachMediaElement()
          this.flvPlayer[index].destroy()
          this.flvPlayer[index] = null
        }
        if (flvjs.isSupported() && item.online) {
          const videoElement = document.getElementById(`myvideo${item.url}`)
          this.flvPlayer[index] = flvjs.createPlayer({
            type: 'flv',
            isLive: true,
            hasVideo: true,
            url: item.url
          })
          this.flvPlayer[index].attachMediaElement(videoElement)
          this.flvPlayer[index].load()
          this.flvPlayer[index].play()
          this.flvPlayer[index].on('error', (err: any) => {
            message.error('播放失败请重试或切换其他视频')
            this.flvPlayer.pause()
            this.flvPlayer.unload()
            this.flvPlayer.detachMediaElement()
            this.flvPlayer.destroy()
            this.flvPlayer = null
          })
        }
      })
    })
    clearInterval(this.requestTime)
    this.count = 0
    this.requestTime = setInterval(() => {
      // this.count++;
      // console.log(this.count > 8, this.flvPlayer[0], 55555);
      // if (this.flvPlayer[0]) {
      //   this.urlList.forEach((item: any, index: number) => {
      //     // this.liveTimeOut = true;
      //     this.flvPlayer[index].pause();
      //     this.flvPlayer[index].unload();
      //     this.flvPlayer[index].detachMediaElement();
      //     this.flvPlayer[index].destroy();
      //   });
      //   clearInterval(this.requestTime);
      // }
      this.getKeepAlive()
    }, 10 * 1000)
  }
  private count = 0
  private liveTimeOut = false
  // 摄像头列表
  private cameraList: Array<any> = []
  // 默认选中摄像头
  private defaultStationValue = ''
  // 获取所有摄像头列表
  private getVideo(carId?: any) {
    this.urlList = []
    this.videoText = []
    this.cameraSerial = []
    getCameraList(carId).then((res: any) => {
      this.videoListUrl = []
      this.cameraList = []
      this.cameraList.unshift({
        cameraName: '全部',
        cameraSerial: '全部'
      })
      if (carId) {
        this.cameraList.push(...res.data.data)
        this.cameraList[1].cameraName = '前'
        this.cameraList[2].cameraName = '后'
      }
      this.defaultStationValue = this.cameraList[0].cameraSerial
      const data: any = res.data.data
      this.socket.send(
        JSON.stringify({
          code: 4
        })
      )
      data.forEach((item: any, index: number) => {
        if (item.cameraSerial != '全部') {
          this.cameraSerial.push(item.cameraSerial)
          getCameraSerial(item.cameraSerial).then((res1: any) => {
            if (res1.data.data) {
              this.urlList.push({
                url: res1.data.data.flv,
                online: res1.data.data.online,
                license: item.license
              })
              this.videoText.push({
                street: item.deptName,
                license: item.license
              })
              if (index === data.length - 1) {
                this.addVideoListUrl()
              }
            }
          })
        }
      })
    })
  }
  // 获取站点列表
  private getStationList(): void {
    getCameraList().then((res) => {
      // this.cameraList = res.data.data;
      // this.cameraList.unshift({
      //   cameraName: "全部",
      //   cameraSerial: "全部"
      // });
      // console.log(this.cameraList);
      // this.defaultStationValue = this.cameraList[0].cameraSerial;
    })
  }
  // 监控设备切换
  private waterStationChange(value: string): void {
    this.cameraSerial = []
    this.defaultStationValue = value + ''
    if (value == '全部') {
      this.getVideo(this.carId)
      return
    }
    this.fetchCameraUrl(value)
    this.cameraSerial.push(value)
  }
  // 获取摄像头播放地址
  private fetchCameraUrl(cameraSerial: string): void {
    this.videoListUrl = []
    this.urlList = []
    this.videoText = []
    const channelIdIndex = this.cameraList.findIndex((item: any) => {
      return item.cameraSerial == cameraSerial
    })
    getCameraSerial(cameraSerial).then((res) => {
      if (res.data.data) {
        this.urlList.push({
          url: res.data.data.flvHttps,
          online: res.data.data.online,
          license:
            res.data.data.license || this.cameraList[channelIdIndex].license
        })
        this.videoText.push({
          street: this.cameraList[channelIdIndex].deptName,
          license: this.cameraList[channelIdIndex].license
        })
        this.addVideoListUrl()
      }
    })
  }
  // 摄像头保活
  private getKeepAlive() {
    for (const item of this.cameraSerial) {
      keepAlive(item)
    }
  }
}
</script>
