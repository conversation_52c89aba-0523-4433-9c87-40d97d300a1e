<style lang="less" scoped>
* {
  font-size: 0.16rem;
}
.main {
  height: 100%;
  width: 4.5rem;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  .panel-top {
    display: flex;
    > div {
      margin-right: 0.3rem;
      color: #b3b3b3;
      display: flex;
      align-items: center;
      > div {
        margin-right: 0.2rem;
      }
      .icon {
        width: 0.6rem;
        height: 0.6rem;
      }
    }
  }
  .panel-center {
    > .sites {
      display: flex;
      .areaSite,
      .tinySite {
        text-align: center;
        padding-top: 0.3rem;
        box-sizing: border-box;
        width: 1.3rem;
        height: 1.3rem;
      }
      .areaSite {
        margin: 0 0.5rem 0 0.3rem;
        background-image: url("../../assets/<EMAIL>");
      }
      .tinySite {
        background-image: url("../../assets/<EMAIL>");
      }
    }
  }
  .panel-center2 {
    .table {
      width: 85%;
      .table-title {
        background: #1742be;
        height: 0.43rem;
        line-height: 0.43rem;
        width: 100%;
        display: flex;
        > div {
          width: 33%;
          box-sizing: border-box;
          text-align: center;
        }
      }
      .table-body {
        width: 100%;
        height: 1.72rem;
        .table-tr {
          width: 100%;
          display: flex;
          justify-content: space-between;
          &:nth-child(2n) {
            background-color: #0F245E;
          }
          > div {
            width: 33%;
            height: 0.43rem;
            line-height: 0.43rem;
            text-align: center;
            padding: 0.05rem 0;
            box-sizing: border-box;
          }
        }
      }
    }
    > .list-content {
      margin-bottom: 0.1rem;
      color: #b3b3b3;
      .river-data {
        display: flex;
        align-items: center;
        > div {
          display: flex;
          align-items: center;
          margin-right: 1rem;
          .icon-temperature {
            margin-right: 0.1rem;
          }
        }
      }
    }
  }
  .panel-bottom {
    .table {
      width: 85%;
      .table-title {
        background: #1742be;
        padding: 0.1rem;
        width: 100%;
        display: flex;
        justify-content: space-between;
      }
      .table-tr {
        width: 100%;
        display: flex;
        justify-content: space-between;
        > div:nth-child(odd) {
          background: #0f245e;
        }
        > div {
          width: 20%;
          text-align: center;
          padding: 0.05rem 0;
          box-sizing: border-box;
        }
      }
    }
  }
  .chart-title {
    color: #cccccc;
    font-size: 0.2rem;
    margin-bottom: 0.05rem;
  }
  .count {
    font-size: 0.3rem;
    color: #fff;
  }
}
</style>

<template>
  <section class="main">
    <section class="panel-top">
      <div>
        <div><img class="icon" src="@/assets/<EMAIL>" alt="" /></div>
        <div>
          <div>摄像头数量</div>
          <div><span class="count">31</span></div>
        </div>
      </div>
      <div>
        <div><img class="icon" src="@/assets/<EMAIL>" alt="" /></div>
        <div>
          <div>企业数量</div>
          <div><span class="count">32</span></div>
        </div>
      </div>
    </section>
    <section class="panel-center">
      <div class="chart-title">站点种类分布</div>
      <div class="sites">
        <div class="areaSite">
          <div><span class="count">2</span></div>
          <div>区控站</div>
        </div>
        <div class="tinySite">
          <div><span class="count">8</span></div>
          <div>微控站</div>
        </div>
      </div>
    </section>
    <section class="panel-bottom">
      <div class="chart-title">水类目标</div>
      <div class="table">
        <div class="table-title">
          <div>所属河流</div>
          <div>断面名称</div>
          <div>本年目标</div>
          <div>{{ currentYear }}年</div>
          <div>{{ currentYear - 1 }}年</div>
        </div>
        <div class="table-tr" v-for="(item, index) in waterAssesData" :key="index">
          <div>{{item.riverName}}</div>
          <div>{{item.crossSection}}</div>
          <div>{{item.thisYearAssessmentGoal}}</div>
          <div>{{item.waterTypeThisYear}}</div>
          <div>{{item.waterTypeLastYear}}</div>
        </div>
      </div>
    </section>
    <section class="panel-center2">
      <div class="chart-title">水质告警统计</div>
      <div class="table">
        <div class="table-title">
          <div>告警站点</div>
          <div>告警内容</div>
          <div>告警时间</div>
        </div>
        <swiper :options="swiperOption" class="table-body">
          <swiper-slide class="table-tr" v-for="(item, index) in alarmList" :key="index">
            <div>{{ item.stationName }}</div>
            <div style="overflow: hidden;text-overflow:ellipsis;white-space: nowrap;" :title="item.waterAlarmContent">{{ item.waterAlarmContent }}</div>
            <div>{{ item.time }}</div>
          </swiper-slide>
        </swiper>
      </div>
      <!--      <div-->
      <!--              class="list-content"-->
      <!--              v-for="(item, index) in riverInfoList"-->
      <!--              :key="index"-->
      <!--      >-->
      <!--        <div class="river-name">{{ item.riverName }}</div>-->
      <!--        <div class="river-data">-->
      <!--          <div>-->
      <!--            <countTo-->
      <!--                    :startVal="0"-->
      <!--                    style="font-size: 0.3rem; color:rgba(72,199,255,1);margin-right:0.1rem"-->
      <!--                    :endVal="item.flow"-->
      <!--                    :duration="3000"-->
      <!--            ></countTo>-->
      <!--            m³/s-->
      <!--          </div>-->
      <!--          <div>-->
      <!--            <img class="icon-temperature" src="../../assets/<EMAIL>" alt="" />-->
      <!--            <span class="count"-->
      <!--            ><countTo-->
      <!--                    :startVal="0"-->
      <!--                    :endVal="item.waterTemperature"-->
      <!--                    :duration="3000"-->
      <!--                    class="count"-->
      <!--            ></countTo-->
      <!--            >℃</span-->
      <!--            >-->
      <!--          </div>-->
      <!--        </div>-->
      <!--      </div>-->
      <!-- <div class="list-content">
        <div class="river-name">沙河</div>
        <div class="river-data">
          <div>
            <countTo
              :startVal="0"
              style="font-size: 0.3rem; color:#53F9C3;margin-right:0.1rem"
              :endVal="1473"
              :duration="3000"
            ></countTo>
            m³/h
          </div>
          <div>
            <img class="icon-temperature" src="../../assets/<EMAIL>" alt="" />
            <span class="count"
              ><countTo
                :startVal="0"
                :endVal="20"
                :duration="3000"
                class="count"
              ></countTo
              >℃</span
            >
          </div>
        </div>
      </div>
      <div class="list-content">
        <div class="river-name">东风渠</div>
        <div class="river-data">
          <div>
            <countTo
              :startVal="0"
              style="font-size: 0.3rem; color:#80A4FF;margin-right:0.1rem"
              :endVal="2343"
              :duration="3000"
            ></countTo>
            m³/h
          </div>
          <div>
            <img class="icon-temperature" src="../../assets/<EMAIL>" alt="" />
            <span class="count"
              ><countTo
                :startVal="0"
                :endVal="17"
                :duration="3000"
                class="count"
              ></countTo
              >℃</span
            >
          </div>
        </div>
      </div> -->
    </section>
  </section>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import SitePie from "@/components/Charts/SitePie.vue";
import MapHomeAqi from "@/components/Charts/MapHomeAqi.vue";
import AirDoublePie from "@/components/Charts/AirDoublePie.vue";
import RotateBarSolid from "@/components/Charts/RotateBarSolid.vue";
import LineChartCylinder from "@/components/Charts/LineChartCylinder.vue";
import LineChartDouble from "@/components/Charts/LineChartDouble.vue";
import { Swiper, SwiperSlide } from "vue-awesome-swiper";
import countTo from "vue-count-to";
import { getAlarmList } from '@/api/water'
import {
  fetchStationWithRecord,
  getTodayMonitorItemRecord,
  fetchRecentAlarm,
  fetchHeavilyPollutingEnterpriseList, // deprecated
  getHeavilyPollutingEnterpriseList,
  getStationList,
  fetchCameraList,
  fetchCameraUrl,
  getMonitorItemDetailRecord,
  getAllStationRealTimeRecordList,
  getRiverInfoList,
  getAssesThisYear
} from "@/api/water";
@Component({
  name: "WarterPage",
  components: {
    SitePie,
    MapHomeAqi,
    AirDoublePie,
    RotateBarSolid,
    LineChartCylinder,
    LineChartDouble,
    countTo,
    Swiper,
    SwiperSlide
  }
})
export default class extends Vue {
  // 获取金牛区所有站点和对应列表
  private mapWaterStations: any = [];
  private currentYear: any =  new Date().getFullYear()
  // 获取重污染企业信息
  private heavyPollutionEnterpriseList: any = [];
  private riverInfoList: any = [];
  private swiperOption = {
    direction: "vertical",
    slidesPerView: 4,
    slidesPerGroup: 1,
    loop: true,
    autoplay: {
      delay: 2800,
      disableOnInteraction: false
    }
  };
  // 水质告警数据
  private alarmList:any = []
  mounted() {
    this.getAllStationRealTimeRecordList();
    this.fetchHeavilyPollutingEnterpriseList();
    this.getRiverInfoList();
    this.getAlarmList()
    this.getAssesThisYear()
  }
  private waterAssesData:any = []
  /**
   * 获取水质目标
   */
  private getAssesThisYear() {
    getAssesThisYear().then(res => {
       this.waterAssesData = res.data.data.list || []
    })
  }
  // 获取水质告警统计
  getAlarmList() {
    getAlarmList({pageNum: 1, pageSize: 20}).then(res => {
      this.alarmList = (res.data.data.records || []).map((item:any) => {
        item.time = item.waterAlarmTime.substr(5, 11)
        return item
      })
    })
  }
  // 获取金牛区所有站点和对应列表
  private getAllStationRealTimeRecordList() {
    getAllStationRealTimeRecordList()
      .then(res => {
        if (res.data.data) {
          this.mapWaterStations = res.data.data.map((list: any) => {
            const retMap = list.station;
            list.realtimeMonitorRecords.forEach((item: any) => {
              switch (item.itemCode) {
                case "003":
                  retMap["cod"] = item.value;
                  retMap["codAlarm"] = item.alarmStatus === 1;
                  break;
                case "002":
                  retMap["nh3n"] = item.value;
                  retMap["nh3nAlarm"] = item.alarmStatus === 1;
                  break;
                case "004":
                  retMap["totalPhosphorus"] = item.value;
                  retMap["totalPhosphorusAlarm"] = item.alarmStatus === 1;
                  break;
                case "007":
                  retMap["ph"] = item.value;
                  retMap["phAlarm"] = item.alarmStatus === 1;
                  break;
              }
            });
            return retMap;
          });
          console.log(this.mapWaterStations, 'mapWaterStations')
          /* eslint-disable @typescript-eslint/ban-ts-ignore */
          //@ts-ignore
          this.$bus.emit("sendWaterMapMarker", this.mapWaterStations);
        }
      })
      .catch(err => {
        console.error(err);
      });
  }
  // 获取重污染企业信息 deprecated
  private fetchHeavilyPollutingEnterpriseList(): void {
    getHeavilyPollutingEnterpriseList().then(res => {
      if (res.data.data && res.data.data.length > 0) {
        this.heavyPollutionEnterpriseList = res.data.data.map(
          (enterprise: any) => {
            return {
              name: enterprise.heavilyPollutingEnterpriseNickname,
              value: enterprise.heavilyPollutingEnterpriseId,
              lng: enterprise.lng,
              lat: enterprise.lat
            };
          }
        );
        /* eslint-disable @typescript-eslint/ban-ts-ignore */
        //@ts-ignore
        this.$bus.emit(
          "sendPollutingMapMarker",
          this.heavyPollutionEnterpriseList
        );
      }
    });
  }
  // 获取河流信息（流量、水温）列表
  private getRiverInfoList() {
    getRiverInfoList().then((res: any) => {
      console.log(res.data.data);
      this.riverInfoList = res.data.data;
    });
  }
}
</script>
