<style lang="less">
.amap-info-content,
.amap-info-outer {
  padding: 0;
}
.amap-info-content {
  background: transparent !important;
}
.amap-info-outer,
.amap-menu-outer {
  box-shadow: none !important;
}
// .amap-info-close {
//   top: 0.02rem !important;
//   right: 0.1rem !important;
// }
.amap-info-sharp {
  display: none !important;
}
/* 空气 */
.marker-content {
  opacity: 0.89;
  box-sizing: border-box;
  color: #00eaff;
  overflow: -Scroll;
  overflow-x: hidden;
  overflow-y: hidden;
  position: relative;
  // clip-path: polygon(4% 0, 96% 0, 100% 7%, 100% 100%, 0 100%, 0 7%);
  width: 3.6rem;
  height: 2.3rem;
  background: url(../../assets/<EMAIL>);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  .closeInfoWindow {
    position: absolute;
    top: 0.2rem;
    right: 0.25rem;
    width: 0.25rem !important;
    cursor: pointer;
  }
}
// .triangle {
//   position: absolute;
//   width: 0;
//   height: 0;
//   border-style: solid;
//   border-width: 100px 100px 0 100px;
//   border-color: rgba(76, 143, 222, 1) transparent transparent transparent;
// }
// .marker-content::before {
//   position: absolute;
//   top: 0;
//   left: -0.168rem;
//   content: "";
//   width: 0.6rem;
//   transform: rotate(130deg);
//   height: 1px;
//   background: #4c8fde;
// }
// .marker-content::after {
//   position: absolute;
//   top: 0;
//   right: -0.168rem;
//   content: "";
//   width: 0.6rem;
//   transform: rotate(-130deg);
//   height: 1px;
//   background: #4c8fde;
// }
.marker-top {
  width: 3rem;
  height: 0.54rem;
  // background: rgba(10, 56, 147, 1);
  margin-top: 0.18rem;
  // margin-left: 0.1rem;
}
.marker-top-top {
  display: flex;
  justify-content: space-between;
  box-sizing: border-box;
  padding: 0.1rem 0 0.01rem 0.27rem;
}
.update-time {
  width: 0.9rem;
  color: #fff;
  font-size: 0.16rem;
}
.marker-top-name {
  font-weight: 500;
  width: 1.9rem;
  display: flex;
  align-items: center;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
.marker-top-name1 {
  font-size: 0.16rem;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
.marker-top-state {
  width: 0.35rem;
  height: 0.16rem;
  background: #22b331;
  border-radius: 0.05rem;
  line-height: 0.16rem;
  color: #fff;
  font-size: 0.12rem;
  text-align: center;
  margin-left: 0.05rem;
  flex: none;
}
.marker-top-bottom {
  padding-left: 0.27rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 0.05rem;
}
.marker-top-left {
  font-size: 0.14rem;
  display: flex;
  align-items: center;
}
.dian {
  width: 0.06rem;
  height: 0.06rem;
  background: rgba(0, 234, 255, 1);
  border-radius: 50%;
  margin-right: 0.06rem;
}
.marker-top-right {
  display: flex;
  align-items: center;
  // margin-left: 0.28rem;
}
.marker-top-right img {
  width: 0.225rem;
  height: 0.225rem;
}
.marker-top-right div {
  font-size: 0.16rem;
  margin-left: 0.1rem;
}
.marker-bottom {
  padding-top: 0.2rem;
  width: 3.2rem;
  margin-left: 0.1rem;
  // padding-bottom: 0.12rem;
  p {
    margin-bottom: 0;
  }
  .marker-top-detail {
    font-size: 0.14rem;
    width: 0.7rem !important;
    height: 0.25rem;
    line-height: 0.25rem;
    text-align: center;
    background: rgba(9, 72, 171, 0.6);
    border: 1px solid rgba(0, 234, 255, 1);
    border-radius: 0.06rem;
    cursor: pointer;
    margin: 0 auto;
    display: flex;
    justify-content: center;
  }
}
.marker-bottom > div {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
}
.marker-bottom > div > div {
  width: 33.33%;
  text-align: center;
  margin-bottom: 0;
  // margin-top: 0.08rem;
}
.marker-bottom > div p:nth-of-type(1) {
  font-size: 0.14rem;
  color: #ffffff;
}
.marker-bottom > div p:nth-of-type(2) {
  font-size: 0.16rem;
  // color: #4adb4e;
  // display: none;
}
/* 水质监测 */
.water-content {
  // background: rgba(2, 34, 98, 1);
  // border: 1px solid rgba(76, 143, 222, 1);
  opacity: 0.89;
  box-sizing: border-box;
  color: #00eaff;
  overflow: -Scroll;
  overflow-x: hidden;
  overflow-y: hidden;
  position: relative;
  // clip-path: polygon(4% 0, 96% 0, 100% 7%, 100% 100%, 0 100%, 0 7%);
  width: 3.2rem;
  height: 1.6rem;
  background: url(../../assets/<EMAIL>);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  .closeInfoWindow {
    position: absolute;
    top: 0.15rem;
    right: 0.15rem;
    width: 0.2rem !important;
    cursor: pointer;
  }
}
// .water-content::before {
//   position: absolute;
//   top: 0;
//   left: -0.15rem;
//   content: "";
//   width: 0.6rem;
//   transform: rotate(141deg);
//   height: 1px;
//   background: #4c8fde;
// }
// .water-content::after {
//   position: absolute;
//   top: 0;
//   right: -0.15rem;
//   content: "";
//   width: 0.6rem;
//   transform: rotate(-141deg);
//   height: 1px;
//   background: #4c8fde;
// }
.water-top {
  width: 2.8rem;
  height: 0.36rem;
  // margin-top: 0.2rem;
  margin-left: 0.1rem;
  box-sizing: border-box;
  padding: 0 0.1rem;
}
.water-top-top {
  display: flex;
  justify-content: space-between;
  box-sizing: border-box;
  margin-top: 0.15rem;
  // padding: 0.1rem 0.17rem 0.11rem 0.27rem;
}
.water-top-name1 {
  font-size: 0.16rem;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
.water-top-state {
  width: 0.35rem;
  height: 0.16rem;
  background: #22b331;
  border-radius: 0.05rem;
  line-height: 0.16rem;
  color: #fff;
  font-size: 0.12rem;
  text-align: center;
  margin-left: 0.05rem;
  flex: none;
}
.water-top-name {
  font-size: 0.15rem;
  font-weight: 500;
  width: 1.5rem;
  display: flex;
  align-items: center;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
.water-top-type {
  font-size: 0.13rem;
  font-weight: 500;
  color: rgba(0, 234, 255, 1);
  display: flex;
  align-items: center;
}
.dian {
  width: 0.06rem;
  height: 0.06rem;
  background: rgb(0, 255, 0);
  border-radius: 50%;
  margin-right: 0.06rem;
}
.water-bottom {
  width: 3rem;
  margin-left: 0.12rem;
  p {
    margin-bottom: 0;
  }
}
.water-bottom > div:nth-of-type(1) {
  width: 100%;
  display: flex;
  margin-bottom: 0.1rem;
}
.water-bottom > div:nth-of-type(1) > div {
  width: 28.33%;
  text-align: center;
}
.water-bottom > div p:nth-of-type(1) {
  font-size: 0.14rem;
  color: #ffffff;
}
.water-bottom > div p:nth-of-type(2) {
  font-size: 0.16rem;
  color: #4adb4e;
}
.water-bottom-btn {
  width: 1rem;
  height: 0.25rem;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(9, 72, 171, 0.6);
  border: 1px solid rgba(0, 234, 255, 1);
  font-weight: 500;
  font-size: 0.14rem;
  color: rgba(0, 234, 255, 1);
  margin: 0 auto;
  cursor: pointer;
}
.amap-marker-label {
  position: absolute;
  z-index: 2;
  // width: 2rem;
  border: 1px solid transparent;
  // background-image: url("../../assets/szjcd.png");
  // background-repeat: no-repeat;
  // background-size: 100% 100%;
  background-color: transparent;
  white-space: nowrap;
  cursor: default;
  padding: 0.03rem;
  font-size: 0.12rem;
  line-height: 0.14rem;
  .info {
    font-size: 0.12rem;
    font-family: Source Han Sans CN;
    font-weight: 500;
    color: rgba(255, 255, 255, 1);
    text-align: center;
  }
}
.amap-controlbar-zoom {
  display: none;
}
.amap-luopan,
.amap-luopan-bg {
  background: url(../../assets/luopan.png) -44px -60px no-repeat;
}
.amap-compass {
  background: url(../../assets/luopan.png) -462px -52px no-repeat;
}
.amap-pointers {
  background: url(../../assets/luopan.png) -562px -52px no-repeat;
}
.amap-pitchDown,
.amap-pitchUp {
  background: url(../../assets/luopan.png) -605px -98px no-repeat;
}
.amap-rotateLeft,
.amap-rotateRight {
  background: url(../../assets/luopan.png) -603px -154px no-repeat;
}
.amap-rotateLeft,
.amap-rotateRight {
  background: url(../../assets/luopan.png) -603px -154px no-repeat;
}
.siteMsgWindow {
  width: 3.5rem;
  height: 1.8rem;
  background: url("../../assets/<EMAIL>") no-repeat;
  background-size: 100% 100%;
  box-sizing: border-box;
  padding: 0.1rem 0.2rem 0.2rem;
  > div:not(:last-child) {
    display: flex;
    align-items: center;
  }
  > div:first-child {
    justify-content: space-between;
    align-items: flex-end;
    margin-top: 0.05rem;
    > img {
      width: 0.24rem;
      cursor: pointer;
    }
  }
  > div:nth-child(2) {
    font-size: 0.16rem;
    // margin: 0.1rem 0;
    margin-top: 0.24rem;
    margin-bottom: 0.15rem;
    img {
      width: 0.16rem;
      height: 0.13rem;
    }
    > span:nth-child(2) {
      margin: 0 0.1rem 0 0.1rem;
    }
    > span:nth-child(3) {
      font-size: 0.2rem;
      color: rgba(232, 14, 14, 1);
    }
    > span:nth-child(4) {
      margin: 0 0.2rem 0 0.1rem;
    }
    > span:last-child {
      background-color: rgb(24, 224, 74);
      padding: 0 0.1rem;
    }
    > .exceedStandard {
      background-color: rgba(232, 14, 14, 1);
    }
  }
  > div:nth-child(3) {
    font-size: 0.16rem;
  }
  .title-site {
    font-size: 0.17rem;
    font-family: Source Han Sans CN;
    font-weight: 500;
    color: rgba(0, 234, 255, 1);
    flex: 1;
    /*width: 2rem;*/
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }
  .title-time {
    width: 0.9rem;
    font-size: 0.15rem;
  }
  .sign-out {
    width: 0.24rem;
    height: 0.24rem;
  }
}
</style>
<style lang="less" scoped>
.container {
  width: 100%;
  height: 100%;
}
.marker-selected {
  width: 0.5rem;
  height: 0.5rem;
  background-image: url("../../assets/car-selected.png");
  background-size: 100% 100%;
}
</style>

<template>
  <div class="container" ref="container"></div>
</template>

<script lang="ts">
/* eslint-disable @typescript-eslint/ban-ts-ignore */
//@ts-ignore
// import AMap from 'AMap'
//@ts-ignore
// import AMapUI from 'AMapUI'
//@ts-ignore
import jinniuStreet from "@/assets/map-geojson/jinniu_street_other";
//@ts-ignore
import riversJinniu from "@/assets/rivers_jinniu/rivers_jinniu";
import { Vue, Component, Prop, Watch } from "vue-property-decorator";
import { setInterval } from "timers";
import { Icon, message } from "ant-design-vue";
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import marker from "@/assets/marker.png";
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import qkz from "@/assets/qkz.png";
import qkzHui from "@/assets/qkz_hui.png";
import qkz1 from "@/assets/qkz1.png";
import qkz2 from "@/assets/qkz2.png";
import qkz3 from "@/assets/qkz3.png";
import qkz4 from "@/assets/qkz4.png";
import qkz5 from "@/assets/qkz5.png";
import qkz6 from "@/assets/qkz6.png";
import wkz from "@/assets/wkz.png";
import wkzHui from "@/assets/wkz_hui.png";
import wkz1 from "@/assets/wkz1.png";
import wkz2 from "@/assets/wkz2.png";
import wkz3 from "@/assets/wkz3.png";
import wkz4 from "@/assets/wkz4.png";
import wkz5 from "@/assets/wkz5.png";
import wkz6 from "@/assets/wkz6.png";
import skz from "@/assets/skz.png";
import skzHui from "@/assets/skz_hui.png";
import skz1 from "@/assets/skz1.png";
import skz2 from "@/assets/skz2.png";
import skz3 from "@/assets/skz3.png";
import skz4 from "@/assets/skz4.png";
import skz5 from "@/assets/skz5.png";
import skz6 from "@/assets/skz6.png";
import gkzHui from "@/assets/<EMAIL>";
import gkz1 from "@/assets/<EMAIL>";
import gkz2 from "@/assets/<EMAIL>";
import gkz3 from "@/assets/<EMAIL>";
import gkz4 from "@/assets/<EMAIL>";
import gkz5 from "@/assets/<EMAIL>";
import gkz6 from "@/assets/<EMAIL>";
import laji from "@/assets/<EMAIL>";
import sashui from "@/assets/<EMAIL>";
import xisao from "@/assets/<EMAIL>";
import zhifa from "@/assets/<EMAIL>";
import wupao from "@/assets/<EMAIL>";
import canchu from "@/assets/<EMAIL>";
import carSelected from "@/assets/car-selected.png";
import carIcons from "@/assets/<EMAIL>";
import qd from "@/assets/qd.png";
import zd from "@/assets/zd.png";
import offLine from "@/assets/ws-offline-s.png";
import stationIconPic from "@/assets/<EMAIL>"; // deprecated
import buildingIconPic from "@/assets/zwqy.png";
import buildingActiveIconPic from "@/assets/-s-zwqy.png";
import districtRiver from "@/assets/jinniu-river.png";
// import waterSatationNormal from "@/assets/<EMAIL>"; deprecated
import waterSatationNormal from "@/assets/ws-normal.png";
import waterSatationActive from "@/assets/ws-normal-s.png";
// import offlineWaterStation from "@/assets/offline-station.png"; deprecated
import offlineWaterStation from "@/assets/ws-offline.png";
import warningWaterStation from "@/assets/ws-warn.png";
import warningWaterStationSelect from "@/assets/ws-warn-s.png";
import dong from "@/assets/fengxiang/dong.png";
import xi from "@/assets/fengxiang/xi.png";
import nan from "@/assets/fengxiang/nan.png";
import bei from "@/assets/fengxiang/bei.png";
import dongnan from "@/assets/fengxiang/dongnan.png";
import dongbei from "@/assets/fengxiang/dongbei.png";
import xinan from "@/assets/fengxiang/xinan.png";
import xibei from "@/assets/fengxiang/xibei.png";
import s1 from "@/assets/s1.gif";
import s2 from "@/assets/s2.gif";
import s3 from "@/assets/s3.gif";
import s4 from "@/assets/s4.gif";
import s5 from "@/assets/s5.gif";
import s6 from "@/assets/s6.gif";
import chaobiao from "@/assets/chaobiao.png";
import chaobiaoActive from "@/assets/chaobiao-active.png";
import weichaobiao from "@/assets/weichaobiao.png";
import weichaobiaoActive from "@/assets/weichaobiao-active.png";
import diaoxian from "@/assets/diaoxian.png";
import diaoxianActive from "@/assets/diaoxian-active.png";
import gdgjIcon from "@/assets/heavyImages/<EMAIL>";
import gdzcIcon from "@/assets/heavyImages/<EMAIL>";
import gdlxIcon from "@/assets/heavyImages/<EMAIL>";
import mc from "@/assets/<EMAIL>";
import taskImg from "@/assets/<EMAIL>";
import siteImg from "@/assets/siteMarker.png";
import emergencyCenter from "@/assets/<EMAIL>";
import siteImgActive from "@/assets/siteMarker_ac.png";
import noCamera from "@/assets/marker-nocamera.png";
import hasCamera from "@/assets/marker-hasnocamera.png";
import yjhong from "@/assets/yjhong.png";
import yjlv from "@/assets/yjlv.png";
import taskBg from "@/assets/<EMAIL>";
import xungangIcon from "@/assets/xungang.png";
import xungangActiveIcon from "@/assets/xungang_active.png";
import jianzhuIcon from "@/assets/jianzhu.png";
import jianzhuActiveIcon from "@/assets/jianzhu_active.png";
import shuizhanIcon from "@/assets/shuizhan.png";
import shuizhanActiveIcon from "@/assets/shuizhan_active.png";
import daqiIcon from "@/assets/daqi.png";
import daqiActiveIcon from "@/assets/daqi_active.png";
import shuichang from "@/assets/shuichangbg.png";
import sthjjIcon from "@/assets/<EMAIL>";
import zhzfjIcon from "@/assets/<EMAIL>";
import zjjIcon from "@/assets/<EMAIL>";
import shwIcon from "@/assets/<EMAIL>";
import ljyszIcon from "@/assets/ljysz.png";
import ljyszActiveIcon from "@/assets/ljyszx-active.png";
import emergencyIcon from "@/assets/<EMAIL>";
import xuanzhong from "@/assets/xuanzhong.png";
import emergencyBg from "@/assets/emergency/bg-bo.gif";
import jingbao from "@/assets/jingbao.gif";
import markerIcon1 from "@/assets/heavily-polluted-enterprise/<EMAIL>"; //印刷图标
import markerIcon2 from "@/assets/heavily-polluted-enterprise/<EMAIL>"; //汽修图标
import markerIcon3 from "@/assets/heavily-polluted-enterprise/<EMAIL>"; //餐饮图标
import markerIcon4 from "@/assets/heavily-polluted-enterprise/<EMAIL>"; //加油站图标
import markerIcon5 from "@/assets/heavily-polluted-enterprise/<EMAIL>"; //餐饮图标
import yszcIcon from "@/assets/heavyImages/<EMAIL>";
import ysgjIcon from "@/assets/heavyImages/<EMAIL>";
import yslxIcon from "@/assets/heavyImages/<EMAIL>";
import qxzcIcon from "@/assets/heavyImages/<EMAIL>";
import qxgjIcon from "@/assets/heavyImages/<EMAIL>";
import qxlxIcon from "@/assets/heavyImages/<EMAIL>";
import yyzcIcon from "@/assets/heavyImages/<EMAIL>";
import yygjIcon from "@/assets/heavyImages/<EMAIL>";
import yylxIcon from "@/assets/heavyImages/<EMAIL>";
import jyzcIcon from "@/assets/heavyImages/<EMAIL>";
import jygjIcon from "@/assets/heavyImages/<EMAIL>";
import jylxIcon from "@/assets/heavyImages/<EMAIL>";
import { airStationDetails } from "@/api/air";
import { getCurSitePM10, getSiteVideoList } from "@/api/headvily-pollution";
import { webglcontextlostHandle } from "@/utils/index";
let AMap: any;
interface HeatMapData {
  lng: number;
  lat: number;
  count: number;
}
interface ColorList {
  [key: number]: string;
}
interface MapCenter {
  lng: number;
  lat: number;
}
enum EnterType {
  AIR = 1,
  WATER = 2,
  VEHICLE = 3,
  HOME = 4,
  PAGEHOME = 5,
  POLLUTE = 6,
  TASK = 7,
  EMERGENCY = 8,
  EQUIPMENT = 9,
}
@Component({
  name: "GaoDeMap",
  components: {
    AIcon: Icon,
  },
})
export default class extends Vue {
  @Prop({ required: false, default: () => [] }) heatMapData!: any[];
  @Prop({ required: false, default: 1 }) enterType!: number | string;
  @Prop({ required: false }) typeColor!: string;
  @Prop({ required: false, default: "3D" }) viewMode!: string;
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  @Prop({ required: false, default: () => {} }) viewCenter!: MapCenter;
  @Prop({ required: false, default: "" }) mapColor!: string;
  @Prop({ required: false, default: "" }) wind!: string;
  @Prop({ required: false, default: () => [] }) companyList!: any[];
  @Prop({ required: false, default: () => [] }) printFactoryList!: any[];
  @Prop({ required: false, default: () => [] }) garageList!: any[];
  @Prop({ required: false, default: () => [] }) gasList!: any[];
  @Prop({ required: false, default: () => [] }) restaurantList!: any[];
  @Prop({ required: false, default: () => [] }) mapWaterStationsVideo!: any[];
  @Prop({ required: false, default: () => [] }) taskMarkerList!: any[];
  @Prop({ required: false, default: () => [] }) emergencyList!: any[];
  @Prop({ required: false, default: () => [] }) emergencyStationList!: any[];
  @Prop({ required: false, default: null }) currentSelectedBuildingMarker!: any;
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  @Prop({ required: false, default: () => {} })
  currentSelectedBuildingObj!: any;
  @Prop({ required: false, default: "" }) curMarkerClickMarker!: any;
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  @Prop({ required: false, default: () => {} }) curMarkerClickObj!: any;
  @Prop({ required: false, default: null }) currentSelectedStationMarker!: any;
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  @Prop({ required: false, default: () => {} }) currentSelectedStationObj!: any;
  @Prop({ required: false, default: true }) airState!: boolean;
  @Prop({ required: false, default: "" }) mapIndex!: string | number;
  @Prop({ required: false, default: () => [] }) mapMarker!: any[];
  @Prop({ required: false, default: () => [] }) queryResultCar!: any;
  @Prop({ required: false, default: () => [] }) jointEnforcementMarker!: any[];
  @Prop({ required: false, default: () => [] }) refuseStationList!: any[];
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  @Prop({ required: false, default: () => {} }) reportDetails!: any;
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  @Prop({ required: false, default: () => {} }) rangeObj!: any;
  @Prop({ required: false, default: () => [] })
  locationRecordList!: any[];
  @Prop({
    required: false,
    default: () => {
      return {};
    },
  })
  newCarPoint!: any[];
  @Prop({
    required: false,
    default: () => {
      return {};
    },
  })
  oldCaPoint!: any[];
  @Prop({ required: false })
  mapStyle!: string;
  @Prop({
    required: false,
    type: Number,
    default: 12.5,
  })
  private mapZoom!: number;
  @Prop({ required: false, default: () => [] }) buildingMarker!: Array<any>;
  private maps: any = "";
  private heatMapDataList: any = [];
  private buildingMarkerList: Array<any> = [];
  private colorList!: ColorList;
  private colorType!: string;

  @Watch("mapIndex", { immediate: true, deep: true })
  private onMapIndex(newValue: any, oldValue: any) {
    if (newValue) {
      this.mapIndex = newValue;
    }
  }
  private reportDetailsText: any = null;
  @Watch("reportDetails", { immediate: true, deep: true })
  private onReportDetails(newValue: any, oldValue: any) {
    if (newValue) {
      if (this.reportDetailsText != null) {
        this.maps.remove(this.reportDetailsText);
      }
      this.reportDetails = newValue;
      const markerIcon = new AMap.Icon({
        size: new AMap.Size(250, 250),
        image: emergencyBg,
        imageSize: new AMap.Size(250, 250),
        imageOffset: new AMap.Pixel(0, 0),
      });
      this.reportDetailsText = new AMap.Marker({
        map: this.maps,
        icon: markerIcon,
        position: [newValue.longitude, newValue.latitude],
        offset: new AMap.Pixel(-125, -125),
        zIndex: 9999,
      });
    }
  }
  @Watch("rangeObj", { immediate: true, deep: true })
  private onrangeObj(newValue: any, oldValue: any) {
    if (newValue) {
      this.createCircle(newValue.lng, newValue.lat, newValue.number);
    }
  }
  // 垃圾运输站信息
  @Watch("refuseStationList", { immediate: true, deep: true })
  private onRefuseStationList(newValue: any, oldValue: any) {
    if (newValue) {
      this.refuseStationList = newValue;
      setTimeout(() => {
        this.creatRefuseStation();
      }, 2500);
    }
  }
  // 执法联动marker
  @Watch("jointEnforcementMarker", { immediate: false, deep: false })
  private onjointEnforcementMarker(newValue: any, oldValue: any) {
    if (newValue) {
      this.jointEnforcementMarker = newValue;
      if (this.taskMarkerClear !== null) {
        this.maps.remove(this.taskMarkerClear);
        this.maps.remove(this.taskTextClear);
        this.taskMarkerClear = null;
        this.taskTextClear = null;
      }
      this.createJointEnforcement();
    }
  }
  // 重污企业选中
  @Watch("currentSelectedBuildingMarker", { immediate: false, deep: true })
  private onCurrentSelectedBuildingMarker(newValue: any, oldValue: any) {
    if (newValue) {
      this.currentSelectedBuilding = newValue;
      this.creatBuildingMarker();
    }
  }
  // 重污企业弹框
  @Watch("currentSelectedBuildingObj", { immediate: false, deep: true })
  private onCurrentSelectedBuildingObj(newValue: any, oldValue: any) {
    if (newValue && newValue.target.w.data.value) {
      if (newValue.target.w.data.value !== oldValue.target.w.data.value) {
        // this.buildMapClick(newValue);
        console.log(newValue.target.w.data);
        if (this.text !== null) {
          this.maps.remove(this.text);
          this.maps.remove(this.enterpriseMarker);
          this.text = null;
          this.enterpriseMarker = null;
        }
        const enterprise = new AMap.Icon({
          size: new AMap.Size(144, 27),
          image: mc,
          imageSize: new AMap.Size(144, 27),
          imageOffset: new AMap.Pixel(0, 0),
        });
        this.enterpriseMarker = new AMap.Marker({
          map: this.maps,
          icon: enterprise,
          zIndex: 999,
          position: [newValue.target.w.data.lng, newValue.target.w.data.lat],
          offset: new AMap.Pixel(-72, 15),
        });
        this.text = new AMap.Text({
          text: newValue.target.w.data.name,
          anchor: "center", // 设置文本标记锚点
          cursor: "pointer",
          zIndex: 9999,
          style: {
            width: "144px",
            height: "27px",
            "background-color": "transparent",
            "border-width": 0,
            "text-align": "center",
            "font-size": "14px",
            color: "#fff",
          },
          position: [newValue.target.w.data.lng, newValue.target.w.data.lat],
          offset: new AMap.Pixel(0, 30),
        });
        this.text.setMap(this.maps);
      }
    } else if (newValue && this.enterpriseMarker) {
      this.maps.remove(this.text);
      this.maps.remove(this.enterpriseMarker);
      this.text = null;
      this.enterpriseMarker = null;
    }
  }
  // 工地选中
  @Watch("curMarkerClickMarker", { immediate: false, deep: true })
  private oncurMarkerClickMarker(newValue: any, oldValue: any) {
    if (newValue) {
      this.curMarkerClick = newValue;
      this.setConstructionSiteMarker();
    }
  }
  // 工地弹框
  @Watch("curMarkerClickObj", { immediate: false, deep: true })
  private oncurMarkerClickObj(newValue: any, oldValue: any) {
    if (newValue) {
      this.siteClick(newValue);
    }
  }
  // 水质选中
  @Watch("currentSelectedStationMarker", { immediate: false, deep: true })
  private oncurrentSelectedStationMarker(newValue: any, oldValue: any) {
    if (newValue) {
      this.currentSelectedStation = newValue;
      this.createWaterSationMarker();
    }
  }
  // 水质弹框
  @Watch("currentSelectedStationObj", { immediate: false, deep: true })
  private oncurrentSelectedStationObj(newValue: any, oldValue: any) {
    if (newValue) {
      this.showInfo(newValue);
    }
  }

  @Watch("companyList", { immediate: true, deep: true })
  private onCompanyList(newValue: any, oldValue: any) {
    if (newValue) {
      this.companyList = newValue;
      setTimeout(() => {
        this.setConstructionSiteMarker();
      }, 192);
    }
  }
  // 印刷
  @Watch("printFactoryList", { immediate: true, deep: true })
  private onPrintFactoryList(newValue: any, oldValue: any) {
    if (newValue) {
      this.printFactoryList = newValue;
      setTimeout(() => {
        this.setPrintFactoryMarker();
      }, 192);
    }
  }
  @Watch("garageList", { immediate: true, deep: true })
  private onGarageList(newValue: any, oldValue: any) {
    if (newValue) {
      this.garageList = newValue;
      setTimeout(() => {
        this.setGarageMarker();
      }, 192);
    }
  }
  @Watch("gasList", { immediate: true, deep: true })
  private onGasList(newValue: any, oldValue: any) {
    if (newValue) {
      this.gasList = newValue;
      setTimeout(() => {
        this.setGasMarker();
      }, 192);
    }
  }
  @Watch("restaurantList", { immediate: true, deep: true })
  private onRestaurantList(newValue: any, oldValue: any) {
    if (newValue) {
      this.restaurantList = newValue;
      setTimeout(() => {
        this.setRestaurantMarker();
      }, 192);
    }
  }
  @Watch("mapWaterStationsVideo", { immediate: true, deep: true })
  private onMapWaterStationsVideo(newValue: any, oldValue: any) {
    if (newValue) {
      this.mapWaterStationsVideo = newValue;
      setTimeout(() => {
        this.createMapWaterStationsVideo();
      }, 192);
    }
  }
  @Watch("emergencyList", { immediate: true, deep: true })
  private onEmergencyList(newValue: any, oldValue: any) {
    if (newValue) {
      this.emergencyList = newValue;
      // this.createEmergencyMarker();
    }
  }
  @Watch("emergencyStationList", { immediate: true, deep: true })
  private onEmergencyStationList(newValue: any, oldValue: any) {
    if (newValue) {
      this.emergencyStationList = newValue;
      console.log(this.emergencyStationList, "emergencyStationList");
      setTimeout(() => {
        this.createEmergencyStationList();
      }, 192);
    }
  }
  @Watch("carItem", { immediate: true, deep: true })
  private onCarItem(newValue: any, oldValue: any) {
    if (newValue) {
      oldValue.setLabel(null);
    }
  }
  @Watch("taskMarkerList", { immediate: true, deep: true })
  private onTaskMarkerList(newValue: any, oldValue: any) {
    if (newValue) {
      this.taskMarkerList = newValue;
      setTimeout(() => {
        this.createTask();
      }, 192);
    }
  }
  // @Watch("queryResultCar", { immediate: false, deep: true })
  // private onQueryResultCar(newValue: any, oldValue: any) {
  //   // 通过车牌号搜索车辆后的处理
  //   if (newValue) {
  //     const selectedCar = this.mList.filter((item: any) => {
  //       return item.data.license === newValue.license;
  //     });
  //     selectedCar[0].emit("click");
  //     const selectedCarPosition = new AMap.LngLat(
  //       Number(selectedCar[0].data.longitude),
  //       Number(selectedCar[0].data.latitude)
  //     );
  //     this.maps.setCenter(selectedCarPosition);
  //   }
  // }
  @Watch("mapMarker", { immediate: true, deep: true })
  private onMapMarker(newValue: any, oldValue: any) {
    if (newValue) {
      this.mapMarker = newValue;
      setTimeout(() => {
        if (Number(this.enterType) === EnterType.AIR || this.mapIndex === 0) {
          this.checkSiteMarker();
          return;
        }
        if (Number(this.enterType) === EnterType.WATER || this.mapIndex === 1) {
          this.mapMarker.forEach((station: any) => {
            station["type"] = EnterType.WATER;
          });
          this.createWaterSationMarker();
        } else {
          console.log(newValue);
          this.creatMarker();
        }
      }, 192);
    }
  }
  @Watch("newCarPoint", { immediate: false, deep: true })
  private onNewCarPoint(newValue: any, oldValue: any) {
    if (newValue) {
      let marker: any;
      this.mList.forEach((item: any) => {
        if (item.data.carId == newValue.carId) {
          marker = item;
        }
      });
      console.log(
        "原点：" + marker.data.longitude + "," + marker.data.latitude,
        "新点：" + newValue.longitude + "," + newValue.latitude
      );
      const lngLat = new AMap.LngLat(newValue.longitude, newValue.latitude);
      marker.moveTo(lngLat, 200);
    }
  }
  @Watch("enterType", { immediate: false, deep: true })
  private onEnterType(newValue: any, oldValue: any) {
    if (newValue != oldValue) {
      this.enterType = Number(newValue);
      if (Number(this.enterType) === EnterType.WATER) {
        this.map();
        this.createOverlay();
        if (Number(this.enterType) === EnterType.WATER || this.mapIndex === 1) {
          const waterCenterPosition = new AMap.LngLat(104.11, 30.72);
          this.maps.setCenter(waterCenterPosition);
          this.dynamicRivderLayer();
        }
      }
    }
    if (newValue != oldValue && newValue == 4) {
      this.map();
      this.createOverlay();
      if (
        Number(this.enterType) !== EnterType.WATER &&
        Number(this.enterType) !== EnterType.POLLUTE &&
        Number(this.enterType) !== EnterType.EQUIPMENT
      ) {
        this.creatGeojson();
      }
      if (Number(this.enterType) === EnterType.WATER) {
        const waterCenterPosition = new AMap.LngLat(104.11, 30.72);
        this.maps.setCenter(waterCenterPosition);
        this.dynamicRivderLayer();
      } else if (Number(this.enterType) === EnterType.VEHICLE) {
        const waterCenterPosition = new AMap.LngLat(104.04, 30.725);
        this.maps.setCenter(waterCenterPosition);
      } else if (Number(this.enterType) === EnterType.PAGEHOME) {
        const waterCenterPosition = new AMap.LngLat(104.04, 30.8);
        this.maps.setCenter(waterCenterPosition);
      } else if (Number(this.enterType) === EnterType.AIR) {
        // this.creatControlBar();
        // this.creatImg();
        // this.windDirection();
      } else if (Number(this.enterType) === EnterType.POLLUTE) {
        // this.dynamicRivderLayer();
      }
    }
  }
  public pathList = [];
  public carRecordList = [];
  private playbackMarker: any = null;
  private oldJointEnforcementMarker = this.jointEnforcementMarker;
  @Watch("locationRecordList", { immediate: false, deep: true })
  private onLocationRecordList(newValue: any, oldValue: any) {
    this.carRecordList = newValue;
    // 清空执法联动的点
    const map = this.maps;
    if (this.clearJointEnforcementMarker.length > 0) {
      map.remove(this.clearJointEnforcementMarker);
      this.clearJointEnforcementMarker = [];
    }
    const pathList: any = [];
    newValue.forEach((item: any) => {
      const arr: Array<number> = [];
      arr[0] = Number(item.coordinate[1].lng);
      arr[1] = Number(item.coordinate[1].lat);
      pathList.push(arr);
    });
    this.pathList = pathList;
    if (newValue) {
      if (this.Polyline) {
        this.maps.remove(this.Polyline);
      }
      if (this.startPoint) {
        this.maps.remove(this.startPoint);
      }
      if (this.endPoint) {
        this.maps.remove(this.endPoint);
      }
      const iconqd = new AMap.Icon({
        // 图标尺寸
        size: new AMap.Size(60, 60),
        // 图标的取图地址
        image: qd,
        // 图标所用图片大小
        imageSize: new AMap.Size(60, 60),
      });
      const iconzd = new AMap.Icon({
        // 图标尺寸
        size: new AMap.Size(60, 60),
        // 图标的取图地址
        image: zd,
        // 图标所用图片大小
        imageSize: new AMap.Size(60, 60),
        // 图标取图偏移量
        // imageOffset: new AMap.Pixel(-20, -10)
      });
      const startLng: number = pathList[0][0];
      const startLat: number = pathList[0][1];
      const endLng: number = pathList[pathList.length - 1][0];
      const endLat: number = pathList[pathList.length - 1][1];
      this.Polyline = new AMap.Polyline({
        map: this.maps,
        path: JSON.parse(JSON.stringify(pathList)),
        showDir: true,
        strokeColor: "#FF9900", //线颜色
        // strokeOpacity: 1,     //线透明度
        strokeWeight: 6, //线宽
        strokeStyle: "solid", //线样式
      });
      this.startPoint = new AMap.Marker({
        map: this.maps,
        icon: iconqd,
        zIndex: 999,
        position: new AMap.LngLat(startLng, startLat),
        offset: new AMap.Pixel(-30, -50),
      });
      this.endPoint = new AMap.Marker({
        map: this.maps,
        icon: iconzd,
        zIndex: 999,
        position: new AMap.LngLat(endLng, endLat),
        offset: new AMap.Pixel(-30, -50),
      });

      // 回放
      if (this.playbackMarker) {
        return;
      }
      this.mList.forEach((item) => {
        item.hide();
      });
      let icon: any;
      const lajiCar = new AMap.Icon({
        // 图标尺寸
        size: new AMap.Size(36, 20),
        // 图标的取图地址
        image: laji,
        // a.amap.com/jsapi_demos/static/demo-center/icons/dir-marker.png
        // 图标所用图片大小
        imageSize: new AMap.Size(36, 20),
        // 图标取图偏移量
        imageOffset: new AMap.Pixel(0, 0),
      });
      const wupaoCar = new AMap.Icon({
        // 图标尺寸
        size: new AMap.Size(36, 20),
        // 图标的取图地址
        image: wupao,
        // a.amap.com/jsapi_demos/static/demo-center/icons/dir-marker.png
        // 图标所用图片大小
        imageSize: new AMap.Size(36, 20),
        // 图标取图偏移量
        imageOffset: new AMap.Pixel(0, 0),
      });
      const sashuiCar = new AMap.Icon({
        // 图标尺寸
        size: new AMap.Size(36, 20),
        // 图标的取图地址
        image: sashui,
        // a.amap.com/jsapi_demos/static/demo-center/icons/dir-marker.png
        // 图标所用图片大小
        imageSize: new AMap.Size(36, 20),
        // 图标取图偏移量
        imageOffset: new AMap.Pixel(0, 0),
      });
      const zhifaCar = new AMap.Icon({
        // 图标尺寸
        size: new AMap.Size(36, 20),
        // 图标的取图地址
        image: zhifa,
        // a.amap.com/jsapi_demos/static/demo-center/icons/dir-marker.png
        // 图标所用图片大小
        imageSize: new AMap.Size(36, 20),
        // 图标取图偏移量
        imageOffset: new AMap.Pixel(0, 0),
      });
      const xisaoCar = new AMap.Icon({
        // 图标尺寸
        size: new AMap.Size(36, 20),
        // 图标的取图地址
        image: xisao,
        // a.amap.com/jsapi_demos/static/demo-center/icons/dir-marker.png
        // 图标所用图片大小
        imageSize: new AMap.Size(36, 20),
        // 图标取图偏移量
        imageOffset: new AMap.Pixel(0, 0),
      });
      const canchuCar = new AMap.Icon({
        // 图标尺寸
        size: new AMap.Size(36, 20),
        // 图标的取图地址
        image: canchu,
        // a.amap.com/jsapi_demos/static/demo-center/icons/dir-marker.png
        // 图标所用图片大小
        imageSize: new AMap.Size(36, 20),
        // 图标取图偏移量
        imageOffset: new AMap.Pixel(0, 0),
      });
      if (this.carItem.data.type == 1) {
        // 垃圾清运车
        icon = lajiCar;
      } else if (this.carItem.data.type == 2) {
        // 雾炮车
        icon = wupaoCar;
      } else if (this.carItem.data.type == 3) {
        // 餐厨清运车
        icon = canchuCar;
      } else if (this.carItem.data.type == 4) {
        // 洒水车
        icon = sashuiCar;
      } else if (this.carItem.data.type == 5) {
        // 洗扫车
        icon = xisaoCar;
      } else if (this.carItem.data.type == 6) {
        // 执法车
        icon = zhifaCar;
      }
      this.playbackMarker = new AMap.Marker({
        map: this.maps,
        position: [this.carItem.data.longitude, this.carItem.data.latitude],
        icon: icon,
        offset: new AMap.Pixel(-26, -13),
        autoRotation: true,
      });
    }
  }
  @Watch("typeColor", { immediate: true, deep: false })
  private onTypeColor(newValue: any, oldValue: any) {
    this.typeColor = newValue;
  }
  @Watch("wind", { immediate: true, deep: false })
  private onWind(newValue: any, oldValue: any) {
    this.wind = newValue;
    // this.windDirection();
  }
  @Watch("mapColor", { immediate: true, deep: false })
  private onMapColor(newValue: any, oldValue: any) {
    // this.creatGeojson();
  }
  @Watch("buildingMarker", { immediate: false, deep: true })
  private onBuildingMarkerChange(newValue: Array<any>, oldValue: Array<any>) {
    if (newValue) {
      this.buildingMarkerList = newValue;
      setTimeout(() => {
        if (Number(this.enterType) !== EnterType.WATER) {
          this.creatBuildingMarker();
        }
      }, 192);
    }
  }
  @Watch("viewCenter", { immediate: true, deep: true })
  private onviewCenterChange(newValue: MapCenter, oldValue: MapCenter) {
    if (newValue && this.maps) {
    }
  }
  @Watch("mapZoom", { immediate: true, deep: false })
  private onmapZoomChange(newValue: number, oldValue: number) {
    if (newValue && this.maps) {
      this.maps.setZoom(newValue);
    }
  }
  private enterprise = mc;
  private Polyline: any;
  private startPoint: any;
  private endPoint: any;
  private heathMapMax = 5;
  // AQI
  readonly AQIAirColors: Array<any> = [
    {
      color: "rgb(0,255,0)",
      text: "优",
      type: 1,
      min: 0,
      max: 50,
    },
    {
      color: "rgb(255,255,0)",
      text: "良",
      type: 2,
      min: 51,
      max: 100,
    },
    {
      color: "rgb(255,126,0)",
      text: "轻度污染",
      type: 3,
      min: 101,
      max: 150,
    },
    {
      color: "rgb(255,0,0)",
      text: "中度污染",
      type: 4,
      min: 151,
      max: 200,
    },
    {
      color: "rgb(153,0,76)",
      text: "重度污染",
      type: 5,
      min: 201,
      max: 300,
    },
    {
      color: "rgb(126,0,35)",
      text: "严重污染",
      type: 6,
      min: 300,
      max: 999,
    },
  ];
  // SO2
  readonly SO2AirColors: Array<any> = [
    {
      color: "rgb(0,255,0)",
      type: 1,
      min: 0,
      max: 150,
    },
    {
      color: "rgb(255,255,0)",
      type: 2,
      min: 151,
      max: 500,
    },
    {
      color: "rgb(255,126,0)",
      type: 3,
      min: 501,
      max: 650,
    },
    {
      color: "rgb(255,0,0)",
      type: 4,
      min: 651,
      max: 800,
    },
    {
      color: "rgb(153,0,76)",
      type: 5,
      min: 801,
      max: 1600,
    },
    {
      color: "rgb(126,0,35)",
      type: 6,
      min: 1601,
      max: 2620,
    },
  ];
  // NO2
  readonly NO2AirColors: Array<any> = [
    {
      color: "rgb(0,255,0)",
      type: 1,
      min: 0,
      max: 100,
    },
    {
      color: "rgb(255,255,0)",
      type: 2,
      min: 101,
      max: 200,
    },
    {
      color: "rgb(255,126,0)",
      type: 3,
      min: 201,
      max: 700,
    },
    {
      color: "rgb(255,0,0)",
      type: 4,
      min: 701,
      max: 1200,
    },
    {
      color: "rgb(153,0,76)",
      type: 5,
      min: 1201,
      max: 2340,
    },
    {
      color: "rgb(126,0,35)",
      type: 6,
      min: 2341,
      max: 3090,
    },
  ];
  // PM10
  readonly PM10AirColors: Array<any> = [
    {
      color: "rgb(0,255,0)",
      type: 1,
      min: 0,
      max: 50,
    },
    {
      color: "rgb(255,255,0)",
      type: 2,
      min: 51,
      max: 150,
    },
    {
      color: "rgb(255,126,0)",
      type: 3,
      min: 151,
      max: 250,
    },
    {
      color: "rgb(255,0,0)",
      type: 4,
      min: 251,
      max: 350,
    },
    {
      color: "rgb(153,0,76)",
      type: 5,
      min: 351,
      max: 420,
    },
    {
      color: "rgb(126,0,35)",
      type: 6,
      min: 421,
      max: 999,
    },
  ];
  // PM2.5
  readonly PM25AirColors: Array<any> = [
    {
      color: "rgb(0,255,0)",
      type: 1,
      min: 0,
      max: 35,
    },
    {
      color: "rgb(255,255,0)",
      type: 2,
      min: 36,
      max: 75,
    },
    {
      color: "rgb(255,126,0)",
      type: 3,
      min: 76,
      max: 115,
    },
    {
      color: "rgb(255,0,0)",
      type: 4,
      min: 116,
      max: 150,
    },
    {
      color: "rgb(153,0,76)",
      type: 5,
      min: 151,
      max: 250,
    },
    {
      color: "rgb(126,0,35)",
      type: 6,
      min: 251,
      max: 999,
    },
  ];
  // CO
  readonly COAirColors: Array<any> = [
    {
      color: "rgb(0,255,0)",
      type: 1,
      min: 0,
      max: 5,
    },
    {
      color: "rgb(255,255,0)",
      type: 2,
      min: 6,
      max: 10,
    },
    {
      color: "rgb(255,126,0)",
      type: 3,
      min: 11,
      max: 35,
    },
    {
      color: "rgb(255,0,0)",
      type: 4,
      min: 36,
      max: 60,
    },
    {
      color: "rgb(153,0,76)",
      type: 5,
      min: 61,
      max: 90,
    },
    {
      color: "rgb(126,0,35)",
      type: 6,
      min: 91,
      max: 999,
    },
  ];
  // O3
  readonly O3AirColors: Array<any> = [
    {
      color: "rgb(0,255,0)",
      type: 1,
      min: 0,
      max: 160,
    },
    {
      color: "rgb(255,255,0)",
      type: 2,
      min: 161,
      max: 200,
    },
    {
      color: "rgb(255,126,0)",
      type: 3,
      min: 201,
      max: 300,
    },
    {
      color: "rgb(255,0,0)",
      type: 4,
      min: 301,
      max: 400,
    },
    {
      color: "rgb(153,0,76)",
      type: 5,
      min: 401,
      max: 800,
    },
    {
      color: "rgb(126,0,35)",
      type: 6,
      min: 801,
      max: 9999,
    },
  ];
  private airPollutionLevel = [
    {
      name: "优",
      color: "rgb(0,255,0)",
    },
    {
      name: "良",
      color: "rgb(255,255,0)",
    },
    {
      name: "轻度污染",
      color: "rgb(255,126,0)",
    },
    {
      name: "中度污染",
      color: "rgb(255,0,0)",
    },
    {
      name: "重度污染",
      color: "rgb(153,0,76)",
    },
    {
      name: "严重污染",
      color: "rgb(126,0,35)",
    },
  ];
  private carMarkerList: any[] = [];
  // 水质监测MarkerList
  private waterStationMarkerList: any[] = [];
  // 当前选中的水质站点
  private currentSelectedStation: any = null;

  created() {}
  mounted() {
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    (window as any)._this = this;
    setTimeout(() => {
      AMap = window.AMap;
      console.log(AMap, "not-water--------------1578");
      this.carIconsObj = {
        lajiCar: new AMap.Icon({
          size: new AMap.Size(36, 20),
          image: laji,
          imageSize: new AMap.Size(36, 20),
          imageOffset: new AMap.Pixel(0, 0),
        }),
        wupaoCar: new AMap.Icon({
          size: new AMap.Size(36, 20),
          image: wupao,
          imageSize: new AMap.Size(36, 20),
          imageOffset: new AMap.Pixel(0, 0),
        }),
        sashuiCar: new AMap.Icon({
          size: new AMap.Size(36, 20),
          image: sashui,
          imageSize: new AMap.Size(36, 20),
          imageOffset: new AMap.Pixel(0, 0),
        }),
        zhifaCar: new AMap.Icon({
          size: new AMap.Size(36, 20),
          image: zhifa,
          imageSize: new AMap.Size(36, 20),
          imageOffset: new AMap.Pixel(0, 0),
        }),
        canchuCar: new AMap.Icon({
          size: new AMap.Size(36, 20),
          image: canchu,
          imageSize: new AMap.Size(36, 20),
          imageOffset: new AMap.Pixel(0, 0),
        }),
        xisaoCar: new AMap.Icon({
          size: new AMap.Size(36, 20),
          image: xisao,
          imageSize: new AMap.Size(36, 20),
          imageOffset: new AMap.Pixel(0, 0),
        }),
        lajiSelected: new AMap.Icon({
          size: new AMap.Size(50, 50),
          image: carIcons,
          imageSize: new AMap.Size(340, 352),
          imageOffset: new AMap.Pixel(-233.5, -176),
        }),
        sashuiSelected: new AMap.Icon({
          size: new AMap.Size(50, 50),
          image: carIcons,
          imageSize: new AMap.Size(340, 352),
          imageOffset: new AMap.Pixel(-115, -176),
        }),
        xisaoSelected: new AMap.Icon({
          size: new AMap.Size(50, 50),
          image: carIcons,
          imageSize: new AMap.Size(340, 352),
          imageOffset: new AMap.Pixel(-55, -176),
        }),
        wupaoSelected: new AMap.Icon({
          size: new AMap.Size(50, 50),
          image: carIcons,
          imageSize: new AMap.Size(340, 352),
          imageOffset: new AMap.Pixel(0, -176),
        }),
        canchuSelected: new AMap.Icon({
          size: new AMap.Size(50, 50),
          image: carIcons,
          imageSize: new AMap.Size(340, 352),
          imageOffset: new AMap.Pixel(-290, -176),
        }),
        zhifaSelected: new AMap.Icon({
          size: new AMap.Size(50, 50),
          image: carIcons,
          imageSize: new AMap.Size(340, 352),
          imageOffset: new AMap.Pixel(-170, -176),
        }),
      };
      this.map();
      this.createOverlay();
      const waterCenterPosition = new AMap.LngLat(
        this.viewCenter.lng,
        this.viewCenter.lat
      );
      this.maps.setCenter(waterCenterPosition);
      if (Number(this.enterType) === EnterType.AIR || this.mapIndex === 0) {
        this.checkSiteMarker();
        return;
      }
      this.setGasMarker();
      if (
        Number(this.enterType) !== EnterType.WATER &&
        Number(this.enterType) !== EnterType.EQUIPMENT
      ) {
        this.creatGeojson();
      }
      if (Number(this.enterType) === EnterType.WATER) {
        const waterCenterPosition = new AMap.LngLat(104.08, 30.725);
        this.maps.setCenter(waterCenterPosition);
        this.dynamicRivderLayer();
        this.createMapWaterStationsVideo();
      } else if (Number(this.enterType) === EnterType.VEHICLE) {
        const waterCenterPosition = new AMap.LngLat(104.04, 30.725);
        this.maps.setCenter(waterCenterPosition);
      } else if (Number(this.enterType) === EnterType.PAGEHOME) {
        const waterCenterPosition = new AMap.LngLat(104.04, 30.74);
        this.maps.setCenter(waterCenterPosition);
      } else if (Number(this.enterType) === EnterType.AIR) {
        this.creatControlBar();
        // this.creatImg();
        // this.windDirection();
      } else if (Number(this.enterType) === EnterType.POLLUTE) {
        // this.dynamicRivderLayer();
        const waterCenterPosition = new AMap.LngLat(104.08, 30.73);
        this.maps.setCenter(waterCenterPosition);
        this.creatControlBar();
      } else if (Number(this.enterType) === EnterType.TASK) {
        const waterCenterPosition = new AMap.LngLat(104.04, 30.725);
        this.maps.setCenter(waterCenterPosition);
        this.createJointEnforcement();
        // this.createTask();
      } else if (Number(this.enterType) === EnterType.EMERGENCY) {
        const waterCenterPosition = new AMap.LngLat(104.04, 30.685);
        this.maps.setCenter(waterCenterPosition);
        // this.createEmergencyHome();
        // this.createEmergencyMarker();
      } else if (Number(this.enterType) === EnterType.EQUIPMENT) {
        const waterCenterPosition = new AMap.LngLat(104.08, 30.71);
        this.maps.setCenter(waterCenterPosition);
        this.dynamicRivderLayer();
      }
      if (this.mapIndex === 4) {
        // this.createTask();
      }
      // this.creatMarker();
    }, 1500);
  }

  beforeDestroy() {
    this.maps = null;
  }

  // 高德地图
  private map(): void {
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const _this = this;
    // 初始化地图
    const map = new AMap.Map(this.$refs.container, {
      center: [104.05, 30.73],
      position: [104.05, 30.73],
      zoom: this.mapZoom,
      viewMode: this.viewMode,
      pitch:
        Number(this.enterType) === EnterType.AIR
          ? 40
          : Number(this.enterType) === EnterType.WATER
          ? 0
          : Number(this.enterType) === EnterType.VEHICLE
          ? 0
          : Number(this.enterType) === EnterType.PAGEHOME
          ? 0
          : Number(this.enterType) === EnterType.POLLUTE
          ? 40
          : Number(this.enterType) === EnterType.EQUIPMENT
          ? 0
          : 40,
      zoomEnable: Number(this.enterType) === EnterType.PAGEHOME ? false : true,
      dragEnable: Number(this.enterType) === EnterType.PAGEHOME ? false : true,
      zooms: [12, 18],
    });

    // 处理webgl上下文丢失事件
    webglcontextlostHandle.call(this);

    // 设置地图样式
    map.setMapStyle(this.mapStyle);
    if (
      Number(this.enterType) === EnterType.VEHICLE ||
      Number(this.enterType) === EnterType.TASK ||
      Number(this.enterType) === EnterType.EMERGENCY
    ) {
      const trafficLayer = new AMap.TileLayer.Traffic({
        zIndex: 1,
      });
      trafficLayer.setMap(map);
    }
    // 添加3D图层
    if (
      Number(this.enterType) === EnterType.HOME ||
      Number(this.enterType) === EnterType.AIR ||
      Number(this.enterType) === EnterType.POLLUTE
    ) {
      // eslint-disable-next-line @typescript-eslint/no-this-alias
      const that = this;
      const object3Dlayer = new AMap.Object3DLayer();
      map.add(object3Dlayer);
      new AMap.GeoJSON({
        geoJSON: jinniuStreet,
        getPolygon: function(geojson: any, lnglats: any) {
          // const area: any = AMap.GeometryUtil.ringArea(lnglats[0]);
          if (geojson.properties.name === "金牛区") {
            const bounds = geojson.geometry.coordinates[0].map((item) =>
              item[0]
                ? new AMap.LngLat(item[0], item[1])
                : new AMap.LngLat(item.lng, item.lat)
            );
            const height = -5000;
            const color = "rgba(47, 110, 238, 0.45)"; // rgba
            const prism = new AMap.Object3D.Prism({
              path: bounds,
              height: height,
              color: color,
            });

            prism.transparent = true;
            object3Dlayer.add(prism);
          }
        },
      });
    }
    // 添加雷达图
    if (
      Number(this.enterType) === EnterType.WATER ||
      Number(this.enterType) === EnterType.AIR ||
      Number(this.enterType) === EnterType.HOME ||
      Number(this.enterType) === EnterType.PAGEHOME ||
      this.mapIndex === 1
    ) {
      const object3Dlayer = new AMap.Object3DLayer();
      map.add(object3Dlayer);
      // eslint-disable-next-line @typescript-eslint/no-this-alias
      const that = this;

      let radar: any;
      if (
        Number(this.enterType) === EnterType.AIR ||
        Number(this.enterType) === EnterType.PAGEHOME ||
        Number(this.enterType) === EnterType.EMERGENCY ||
        Number(this.enterType) === EnterType.HOME
      ) {
        // 添加雷达扫描2D网格
        // 构造雷达地形
        const buildRadar = function() {
          radar = new AMap.Object3D.Mesh();
          radar.transparent = true;
          radar.backOrFront = "front";

          const geometry = radar.geometry;
          let radius = 9 * 1200; // 半径 * 米
          radius = radius / map.getResolution(map.getCenter(), 20);
          const unit = 0.1; // 单位
          const range = 100; // 扇形角度
          const count = range / unit;

          const getOpacity = function(scale: number): number {
            return 1 - Math.pow(scale, 0.2);
          };

          for (let i = 0; i < count; i += 1) {
            const angle1 = (i * unit * Math.PI) / 180;
            const angle2 = ((i + 1) * unit * Math.PI) / 180;
            const p1x = Math.cos(angle1) * radius;
            const p1y = Math.sin(angle1) * radius;
            const p2x = Math.cos(angle2) * radius;
            const p2y = Math.sin(angle2) * radius;

            geometry.vertices.push(0, 0, 0);
            geometry.vertices.push(p1x, p1y, 0);
            geometry.vertices.push(p2x, p2y, 0);

            const opacityStart = getOpacity(i / count);
            const opacityEnd = getOpacity((i + 1) / count);

            geometry.vertexColors.push(0, 0.5, 0.2, opacityStart);
            geometry.vertexColors.push(0, 0.5, 0.2, opacityStart);
            geometry.vertexColors.push(0, 0.5, 0.2, opacityEnd);
          }

          radar.position(map.getCenter());

          object3Dlayer.add(radar);
        };
        // 雷达扫描处理
        const scan = function(): void {
          radar.rotateZ(-1);
          AMap.Util.requestAnimFrame(scan);
        };

        buildRadar();
        scan();
      }
    }
    this.maps = map;
  }

  // 添加行政区外的覆盖物
  private createOverlay() {
    const map = this.maps;
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const that = this;
    // 添加金牛区地理信息数据 3
    new AMap.DistrictSearch({
      extensions: "all",
      subdistrict: 0,
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    }).search("金牛区", function(status: any, result: any) {
      // 外多边形坐标数组和内多边形坐标数组
      const outer = [
        new AMap.LngLat(-360, 90, true),
        new AMap.LngLat(-360, -90, true),
        new AMap.LngLat(360, -90, true),
        new AMap.LngLat(360, 90, true),
      ];
      const holes = result.districtList[0].boundaries;

      const pathArray: any = [outer];
      // eslint-disable-next-line prefer-spread
      pathArray.push.apply(pathArray, holes);
      const polygon = new AMap.Polygon({
        pathL: pathArray,
        //线条颜色，使用16进制颜色代码赋值。默认值为#006600
        strokeColor: "rgb(255,255,255)",
        strokeWeight: 0,
        //轮廓线透明度，取值范围[0,1]，0表示完全透明，1表示不透明。默认为0.9
        strokeOpacity: 0,
        //多边形填充颜色，使用16进制颜色代码赋值，如：#FFAA00
        fillColor: "rgba(3,4,130)",
        // fillColor: "rgba(4,20,50)",
        // fillColor: "#0A1C5F",
        //多边形填充透明度，取值范围[0,1]，0表示完全透明，1表示不透明。默认为0.9
        fillOpacity:
          that.enterType === EnterType.VEHICLE ||
          that.enterType === EnterType.EMERGENCY ||
          that.enterType === EnterType.TASK
            ? 0.4
            : 0,
        //轮廓线样式，实线:solid，虚线:dashed
        strokeStyle: "solid",
        strokeDasharray: [10, 2, 10],
      });
      polygon.setPath(pathArray);
      map.add(polygon);
      //限制地图显示范围
      function lockMapBounds() {
        const bounds = map.getBounds();
        map.setLimitBounds(bounds);
      }
      if (
        that.enterType !== EnterType.VEHICLE &&
        that.enterType !== EnterType.EMERGENCY &&
        that.enterType !== EnterType.TASK
      ) {
        //启用地图范围限定
        lockMapBounds();
      }
    });
  }

  // 添加空气水波纹
  private heatImg: any = [];
  private creatImg(): void {
    const map = this.maps;
    if (this.heatImg.length > 0) {
      map.remove(this.heatImg);
    }
    // 区控站
    const sq1 = new AMap.Icon({
      // 图标尺寸
      size: new AMap.Size(150, 150),
      // 图标的取图地址
      image: s1,
      // 图标所用图片大小
      imageSize: new AMap.Size(150, 150),
      // 图标取图偏移量
      imageOffset: new AMap.Pixel(0, 0),
    });
    const sq2 = new AMap.Icon({
      // 图标尺寸
      size: new AMap.Size(150, 150),
      // 图标的取图地址
      image: s2,
      // 图标所用图片大小
      imageSize: new AMap.Size(150, 150),
      // 图标取图偏移量
      imageOffset: new AMap.Pixel(0, 0),
    });
    const sq3 = new AMap.Icon({
      // 图标尺寸
      size: new AMap.Size(150, 150),
      // 图标的取图地址
      image: s3,
      // 图标所用图片大小
      imageSize: new AMap.Size(150, 150),
      // 图标取图偏移量
      imageOffset: new AMap.Pixel(0, 0),
    });
    const sq4 = new AMap.Icon({
      // 图标尺寸
      size: new AMap.Size(150, 150),
      // 图标的取图地址
      image: s4,
      // 图标所用图片大小
      imageSize: new AMap.Size(150, 150),
      // 图标取图偏移量
      imageOffset: new AMap.Pixel(0, 0),
    });
    const sq5 = new AMap.Icon({
      // 图标尺寸
      size: new AMap.Size(150, 150),
      // 图标的取图地址
      image: s5,
      // 图标所用图片大小
      imageSize: new AMap.Size(150, 150),
      // 图标取图偏移量
      imageOffset: new AMap.Pixel(0, 0),
    });
    const sq6 = new AMap.Icon({
      // 图标尺寸
      size: new AMap.Size(150, 150),
      // 图标的取图地址
      image: s6,
      // 图标所用图片大小
      imageSize: new AMap.Size(150, 150),
      // 图标取图偏移量
      imageOffset: new AMap.Pixel(0, 0),
    });

    // 微控站
    const sw1 = new AMap.Icon({
      // 图标尺寸
      size: new AMap.Size(120, 120),
      // 图标的取图地址
      image: s1,
      // 图标所用图片大小
      imageSize: new AMap.Size(120, 120),
      // 图标取图偏移量
      imageOffset: new AMap.Pixel(0, 0),
    });
    const sw2 = new AMap.Icon({
      // 图标尺寸
      size: new AMap.Size(120, 120),
      // 图标的取图地址
      image: s2,
      // 图标所用图片大小
      imageSize: new AMap.Size(120, 120),
      // 图标取图偏移量
      imageOffset: new AMap.Pixel(0, 0),
    });
    const sw3 = new AMap.Icon({
      // 图标尺寸
      size: new AMap.Size(120, 120),
      // 图标的取图地址
      image: s3,
      // 图标所用图片大小
      imageSize: new AMap.Size(120, 120),
      // 图标取图偏移量
      imageOffset: new AMap.Pixel(0, 0),
    });
    const sw4 = new AMap.Icon({
      // 图标尺寸
      size: new AMap.Size(120, 120),
      // 图标的取图地址
      image: s4,
      // 图标所用图片大小
      imageSize: new AMap.Size(120, 120),
      // 图标取图偏移量
      imageOffset: new AMap.Pixel(0, 0),
    });
    const sw5 = new AMap.Icon({
      // 图标尺寸
      size: new AMap.Size(120, 120),
      // 图标的取图地址
      image: s5,
      // 图标所用图片大小
      imageSize: new AMap.Size(120, 120),
      // 图标取图偏移量
      imageOffset: new AMap.Pixel(0, 0),
    });
    const sw6 = new AMap.Icon({
      // 图标尺寸
      size: new AMap.Size(120, 120),
      // 图标的取图地址
      image: s6,
      // 图标所用图片大小
      imageSize: new AMap.Size(120, 120),
      // 图标取图偏移量
      imageOffset: new AMap.Pixel(0, 0),
    });

    // 市控站
    const ss1 = new AMap.Icon({
      // 图标尺寸
      size: new AMap.Size(180, 180),
      // 图标的取图地址
      image: s1,
      // 图标所用图片大小
      imageSize: new AMap.Size(180, 180),
      // 图标取图偏移量
      imageOffset: new AMap.Pixel(0, 0),
    });
    const ss2 = new AMap.Icon({
      // 图标尺寸
      size: new AMap.Size(180, 180),
      // 图标的取图地址
      image: s2,
      // 图标所用图片大小
      imageSize: new AMap.Size(180, 180),
      // 图标取图偏移量
      imageOffset: new AMap.Pixel(0, 0),
    });
    const ss3 = new AMap.Icon({
      // 图标尺寸
      size: new AMap.Size(180, 180),
      // 图标的取图地址
      image: s3,
      // 图标所用图片大小
      imageSize: new AMap.Size(180, 180),
      // 图标取图偏移量
      imageOffset: new AMap.Pixel(0, 0),
    });
    const ss4 = new AMap.Icon({
      // 图标尺寸
      size: new AMap.Size(180, 180),
      // 图标的取图地址
      image: s4,
      // 图标所用图片大小
      imageSize: new AMap.Size(180, 180),
      // 图标取图偏移量
      imageOffset: new AMap.Pixel(0, 0),
    });
    const ss5 = new AMap.Icon({
      // 图标尺寸
      size: new AMap.Size(180, 180),
      // 图标的取图地址
      image: s5,
      // 图标所用图片大小
      imageSize: new AMap.Size(180, 180),
      // 图标取图偏移量
      imageOffset: new AMap.Pixel(0, 0),
    });
    const ss6 = new AMap.Icon({
      // 图标尺寸
      size: new AMap.Size(180, 180),
      // 图标的取图地址
      image: s6,
      // 图标所用图片大小
      imageSize: new AMap.Size(180, 180),
      // 图标取图偏移量
      imageOffset: new AMap.Pixel(0, 0),
    });

    if (this.mapMarker.length !== 0) {
      this.mapMarker.forEach((item: any) => {
        if (item.stationTypeId == 1) {
          if (item.type == 1) {
            item.icon = sq1;
          } else if (item.type == 2) {
            item.icon = sq2;
          } else if (item.type == 3) {
            item.icon = sq3;
          } else if (item.type == 4) {
            item.icon = sq4;
          } else if (item.type == 5) {
            item.icon = sq5;
          } else if (item.type == 6) {
            item.icon = sq6;
          }
          item.offset = new AMap.Pixel(-75, -75);
        } else if (item.stationTypeId == 2) {
          if (item.type == 1) {
            item.icon = sw1;
          } else if (item.type == 2) {
            item.icon = sw2;
          } else if (item.type == 3) {
            item.icon = sw3;
          } else if (item.type == 4) {
            item.icon = sw4;
          } else if (item.type == 5) {
            item.icon = sw5;
          } else if (item.type == 6) {
            item.icon = sw6;
          }
          item.offset = new AMap.Pixel(-60, -60);
        } else if (item.stationTypeId == 3) {
          if (item.type == 1) {
            item.icon = ss1;
          } else if (item.type == 2) {
            item.icon = ss2;
          } else if (item.type == 3) {
            item.icon = ss3;
          } else if (item.type == 4) {
            item.icon = ss4;
          } else if (item.type == 5) {
            item.icon = ss5;
          } else if (item.type == 6) {
            item.icon = ss6;
          }
          item.offset = new AMap.Pixel(-90, -90);
        }
      });
      this.mapMarker.forEach((marker: any) => {
        if (marker.type !== null) {
          const stationMarker = new AMap.Marker({
            map: map,
            icon: marker.icon,
            zIndex: 999,
            position: [marker.gcLng, marker.gcLat],
            offset: marker.offset,
          });
          this.heatImg.push(stationMarker);
        }
      });
    }
  }

  // 添加 3D 罗盘控制
  private creatControlBar() {
    AMap.plugin(["AMap.ControlBar"], () => {
      this.maps.addControl(
        new AMap.ControlBar({
          position: {
            bottom:
              Number(this.enterType) === EnterType.AIR ? "-0.65rem" : "-0.1rem",
            left:
              Number(this.enterType) === EnterType.AIR ? "3.85rem" : "1.7rem",
            zIndex: 9,
            transition: "1.5s",
          },
        })
      );
    });
  }

  // 添加街道划分区域地图数据
  private creatGeojson() {
    const map = this.maps;
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const that = this;
    // 添加金牛区地理信息数据 1
    const geojson = new AMap.GeoJSON({
      geoJSON: jinniuStreet,
      getPolygon: function(geojson: any, lnglats: any) {
        // const area: any = AMap.GeometryUtil.ringArea(lnglats[0]);
        if (geojson.properties.name === "金牛区") {
          const text = new AMap.Text({
            text: geojson.properties.name,
            anchor: "center", // 设置文本标记锚点
            draggable: false,
            cursor: "pointer",
            angle: 0,
            style: {
              padding: ".75rem 1.25rem",
              "margin-bottom": "1rem",
              "border-radius": ".25rem",
              "background-color": "transparent",
              "border-width": 0,
              "text-align": "center",
              "font-size": "14px",
              color: "#36E9EF",
            },
            position: [
              geojson.properties.center.lng,
              geojson.properties.center.lat,
            ],
          });
          text.setMap(map);

          const path = geojson.geometry.coordinates[0].map((item) =>
            item[0]
              ? new AMap.LngLat(item[0], item[1])
              : new AMap.LngLat(item.lng, item.lat)
          );
          const polygon = new AMap.Polygon({
            path,
            // strokeColor: "#0ea9f9",
            strokeColor: "#2fbeb5",
            strokeWeight: 2,
            strokeOpacity: 1,
            fillOpacity:
              that.enterType === EnterType.VEHICLE ||
              that.enterType === EnterType.EMERGENCY ||
              that.enterType === EnterType.POLLUTE
                ? 0
                : 0.5, // 多边形填充透明度
            fillColor:
              that.enterType === EnterType.AIR ||
              that.enterType === EnterType.HOME
                ? geojson.properties.bgColor
                : "rgba(0,49,113, 0.15)",
            // that.enterType === EnterType.AIR
            //   ? that.mapColor
            //   : "rgba(0,49,113, 0.15)",
            zIndex: 10,
          });
          map.add(polygon);
        }
      },
    });

    // 添加金牛区地理信息数据 2
    geojson.setMap(map);
  }
  // 判断站点的颜色并添加空气站点和热力图
  private checkSiteMarker() {
    const map = this.maps;
    const maplist: any = [];
    if (map.w) {
      map.w.layers.forEach((item: any, index: number) => {
        if (item.w.type !== "heatMap3DLayer") {
          maplist.push(item);
        }
      });
      map.w.layers = maplist;
    }

    if (this.clickMar && this.clickMar.length > 0) {
      this.maps.remove(this.clickMar);
      this.clickMar = [];
      this.markList = [];
    }
    this.heatMapDataList = [];
    for (const item of this.mapMarker) {
      this.heatMapDataList.push({
        lng: item.gcLng,
        lat: item.gcLat,
        count: item.concentration,
      });
    }
    if (this.typeColor == "aqi") {
      this.heathMapMax = 151;
      // AQI
      for (const item of this.mapMarker) {
        // AQI
        for (const item1 of this.AQIAirColors) {
          // AQI
          if (item.concentration !== null) {
            if (
              item.concentration >= item1.min &&
              item.concentration <= item1.max
            ) {
              /* eslint-disable @typescript-eslint/ban-ts-ignore */
              //@ts-ignore
              item.type = item1.type;
              item.textColor = item1.color;
            }
          } else {
            item.type = null;
          }
        }
      }
    } else if (this.typeColor == "100") {
      this.heathMapMax = 300;
      // SO2
      for (const item of this.mapMarker) {
        // SO2
        for (const item1 of this.SO2AirColors) {
          // SO2
          if (item.concentration !== null) {
            if (
              item.concentration >= item1.min &&
              item.concentration <= item1.max
            ) {
              /* eslint-disable @typescript-eslint/ban-ts-ignore */
              //@ts-ignore
              item.type = item1.type;
              item.textColor = item1.color;
            }
          } else {
            item.type = null;
          }
        }
      }
    } else if (this.typeColor == "101") {
      this.heathMapMax = 200;
      // NO2
      for (const item of this.mapMarker) {
        // NO2
        for (const item1 of this.NO2AirColors) {
          // NO2
          if (item.concentration !== null) {
            if (
              item.concentration >= item1.min &&
              item.concentration <= item1.max
            ) {
              /* eslint-disable @typescript-eslint/ban-ts-ignore */
              //@ts-ignore
              item.type = item1.type;
              item.textColor = item1.color;
            }
          } else {
            item.type = null;
          }
        }
      }
    } else if (this.typeColor == "102") {
      this.heathMapMax = 300;
      // O3
      for (const item of this.mapMarker) {
        // O3
        for (const item1 of this.O3AirColors) {
          // O3
          if (item.concentration !== null) {
            if (
              item.concentration >= item1.min &&
              item.concentration <= item1.max
            ) {
              /* eslint-disable @typescript-eslint/ban-ts-ignore */
              //@ts-ignore
              item.type = item1.type;
              item.textColor = item1.color;
            }
          } else {
            item.type = null;
          }
        }
      }
    } else if (this.typeColor == "103") {
      this.heathMapMax = 10;
      // CO
      for (const item of this.mapMarker) {
        // CO
        for (const item1 of this.COAirColors) {
          // CO
          if (item.concentration !== null) {
            if (
              item.concentration >= item1.min &&
              item.concentration <= item1.max
            ) {
              /* eslint-disable @typescript-eslint/ban-ts-ignore */
              //@ts-ignore
              item.type = item1.type;
              item.textColor = item1.color;
            }
          } else {
            item.type = null;
          }
        }
      }
    } else if (this.typeColor == "104") {
      this.heathMapMax = 250;
      // PM10
      for (const item of this.mapMarker) {
        // PM10
        for (const item1 of this.PM10AirColors) {
          // PM10
          if (item.concentration !== null) {
            if (
              item.concentration >= item1.min &&
              item.concentration <= item1.max
            ) {
              /* eslint-disable @typescript-eslint/ban-ts-ignore */
              //@ts-ignore
              item.type = item1.type;
              item.textColor = item1.color;
            }
          } else {
            item.type = null;
          }
        }
      }
    } else if (this.typeColor == "105") {
      this.heathMapMax = 115;
      // PM2.5
      for (const item of this.mapMarker) {
        // PM2.5
        for (const item1 of this.PM25AirColors) {
          // PM2.5
          if (item.concentration !== null) {
            if (
              item.concentration >= item1.min &&
              item.concentration <= item1.max
            ) {
              /* eslint-disable @typescript-eslint/ban-ts-ignore */
              //@ts-ignore
              item.type = item1.type;
              item.textColor = item1.color;
            }
          } else {
            item.type = null;
          }
        }
      }
    }
    if (this.mapIndex !== 0) {
      // const maplist: any = [];
      if (map.w) {
        map.w.layers.forEach((item: any, index: number) => {
          if (item.w.type == "heatMap3DLayer") {
            map.w.layers.splice(index, 1);
            // maplist.push(item);
          }
        });
        // map.w.layers = maplist;
      }
      if (map.Ce) {
        map.Ce.layers.forEach((item: any, index: number) => {
          if (item.Ce.type == "heatMap3DLayer") {
            map.Ce.layers.splice(index, 1);
            // maplist.push(item);
          }
        });
        // map.w.layers = maplist;
      }
      if (this.heatMapDataList.length !== 0) {
        this.heathMap();
      }
    }
    this.creatMarker();
  }

  // 图上Marker绘制
  private clickMar: Array<any> = [];
  private markList: Array<any> = [];
  private airTextList: Array<any> = [];
  private mList: Array<any> = [];
  private carItem: any;
  private carActiveMarker: any = null;
  // 图上Marker绘制
  private creatMarker(): void {
    const map = this.maps;
    if (this.clickMar && this.clickMar.length > 0) {
      map.remove(this.clickMar);
      this.clickMar = [];
      this.markList = [];
    }
    if (this.mList && this.mList.length > 0) {
      map.remove(this.mList);
      this.mList = [];
    }
    if (this.carActiveMarker !== null) {
      map.remove(this.carActiveMarker);
    }
    if (this.airTextList && this.airTextList.length > 0) {
      map.remove(this.airTextList);
      this.airTextList = [];
    }
    // 图上添加空气marker
    if (
      Number(this.enterType) !== EnterType.VEHICLE &&
      Number(this.enterType) !== EnterType.TASK
    ) {
      if (this.maps) {
        this.maps.clearInfoWindow();
      }
      // 设置空气Marker-Icon(1、区控站)
      const markerIcon11 = new AMap.Icon({
        size: new AMap.Size(45, 45),
        image: qkz1,
        imageSize: new AMap.Size(45, 45),
        imageOffset: new AMap.Pixel(0, 0),
      });
      const markerIcon12 = new AMap.Icon({
        size: new AMap.Size(45, 45),
        image: qkz2,
        imageSize: new AMap.Size(45, 45),
        imageOffset: new AMap.Pixel(0, 0),
      });
      const markerIcon13 = new AMap.Icon({
        size: new AMap.Size(45, 45),
        image: qkz3,
        imageSize: new AMap.Size(45, 45),
        imageOffset: new AMap.Pixel(0, 0),
      });
      const markerIcon14 = new AMap.Icon({
        size: new AMap.Size(45, 45),
        image: qkz4,
        imageSize: new AMap.Size(45, 45),
        imageOffset: new AMap.Pixel(0, 0),
      });
      const markerIcon15 = new AMap.Icon({
        size: new AMap.Size(45, 45),
        image: qkz5,
        imageSize: new AMap.Size(45, 45),
        imageOffset: new AMap.Pixel(0, 0),
      });
      const markerIcon16 = new AMap.Icon({
        size: new AMap.Size(45, 45),
        image: qkz6,
        imageSize: new AMap.Size(45, 45),
        imageOffset: new AMap.Pixel(0, 0),
      });

      // 设置空气Marker-Icon(2、微控站)
      const markerIcon21 = new AMap.Icon({
        size: new AMap.Size(54, 44),
        image: wkz1,
        imageSize: new AMap.Size(54, 44),
        imageOffset: new AMap.Pixel(0, 0),
      });
      const markerIcon22 = new AMap.Icon({
        size: new AMap.Size(54, 44),
        image: wkz2,
        imageSize: new AMap.Size(54, 44),
        imageOffset: new AMap.Pixel(0, 0),
      });
      const markerIcon23 = new AMap.Icon({
        size: new AMap.Size(54, 44),
        image: wkz3,
        imageSize: new AMap.Size(54, 44),
        imageOffset: new AMap.Pixel(0, 0),
      });
      const markerIcon24 = new AMap.Icon({
        size: new AMap.Size(54, 44),
        image: wkz4,
        imageSize: new AMap.Size(54, 44),
        imageOffset: new AMap.Pixel(0, 0),
      });
      const markerIcon25 = new AMap.Icon({
        size: new AMap.Size(54, 44),
        image: wkz5,
        imageSize: new AMap.Size(54, 44),
        imageOffset: new AMap.Pixel(0, 0),
      });
      const markerIcon26 = new AMap.Icon({
        size: new AMap.Size(54, 44),
        image: wkz6,
        imageSize: new AMap.Size(54, 44),
        imageOffset: new AMap.Pixel(0, 0),
      });

      // 设置空气Marker-Icon(3、市控站)
      const markerIcon31 = new AMap.Icon({
        size: new AMap.Size(51, 37),
        image: skz1,
        imageSize: new AMap.Size(51, 37),
        imageOffset: new AMap.Pixel(0, 0),
      });
      const markerIcon32 = new AMap.Icon({
        size: new AMap.Size(51, 37),
        image: skz2,
        imageSize: new AMap.Size(51, 37),
        imageOffset: new AMap.Pixel(0, 0),
      });
      const markerIcon33 = new AMap.Icon({
        size: new AMap.Size(51, 37),
        image: skz3,
        imageSize: new AMap.Size(51, 37),
        imageOffset: new AMap.Pixel(0, 0),
      });
      const markerIcon34 = new AMap.Icon({
        size: new AMap.Size(51, 37),
        image: skz4,
        imageSize: new AMap.Size(51, 37),
        imageOffset: new AMap.Pixel(0, 0),
      });
      const markerIcon35 = new AMap.Icon({
        size: new AMap.Size(51, 37),
        image: skz5,
        imageSize: new AMap.Size(51, 37),
        imageOffset: new AMap.Pixel(0, 0),
      });
      const markerIcon36 = new AMap.Icon({
        size: new AMap.Size(51, 37),
        image: skz6,
        imageSize: new AMap.Size(51, 37),
        imageOffset: new AMap.Pixel(0, 0),
      });

      // 设置空气Marker-Icon(1、区控站)离线
      const markerIcon11Hui = new AMap.Icon({
        size: new AMap.Size(45, 45),
        image: qkzHui,
        imageSize: new AMap.Size(45, 45),
        imageOffset: new AMap.Pixel(0, 0),
      });

      // 设置空气Marker-Icon(2、微控站)离线
      const markerIcon21Hui = new AMap.Icon({
        size: new AMap.Size(54, 44),
        image: wkzHui,
        imageSize: new AMap.Size(54, 44),
        imageOffset: new AMap.Pixel(0, 0),
      });

      // 设置空气Marker-Icon(3、市控站)离线
      const markerIcon31Hui = new AMap.Icon({
        size: new AMap.Size(51, 37),
        image: skzHui,
        imageSize: new AMap.Size(51, 37),
        imageOffset: new AMap.Pixel(0, 0),
      });
      // 设置空气Marker-Icon(3、国控站)离线
      const markerIcon41Hui = new AMap.Icon({
        size: new AMap.Size(42, 35),
        image: gkzHui,
        imageSize: new AMap.Size(42, 35),
        imageOffset: new AMap.Pixel(0, 0),
      });
      // 设置空气Marker-Icon(3、国控站)
      const markerIcon41 = new AMap.Icon({
        size: new AMap.Size(42, 35),
        image: gkz1,
        imageSize: new AMap.Size(42, 35),
        imageOffset: new AMap.Pixel(0, 0),
      });
      const markerIcon42 = new AMap.Icon({
        size: new AMap.Size(42, 35),
        image: gkz2,
        imageSize: new AMap.Size(42, 35),
        imageOffset: new AMap.Pixel(0, 0),
      });
      const markerIcon43 = new AMap.Icon({
        size: new AMap.Size(42, 35),
        image: gkz3,
        imageSize: new AMap.Size(42, 35),
        imageOffset: new AMap.Pixel(0, 0),
      });
      const markerIcon44 = new AMap.Icon({
        size: new AMap.Size(42, 35),
        image: gkz4,
        imageSize: new AMap.Size(42, 35),
        imageOffset: new AMap.Pixel(0, 0),
      });
      const markerIcon45 = new AMap.Icon({
        size: new AMap.Size(42, 35),
        image: gkz5,
        imageSize: new AMap.Size(42, 35),
        imageOffset: new AMap.Pixel(0, 0),
      });
      const markerIcon46 = new AMap.Icon({
        size: new AMap.Size(42, 35),
        image: gkz6,
        imageSize: new AMap.Size(42, 35),
        imageOffset: new AMap.Pixel(0, 0),
      });

      // 报警
      const jingbaoIcon = new AMap.Icon({
        size: new AMap.Size(62.9, 78.2),
        image: jingbao,
        imageSize: new AMap.Size(62.9, 78.2),
        imageOffset: new AMap.Pixel(0, 0),
      });
      const ResolvedMakerList = this.mapMarker ? this.mapMarker : [];
      const markers: Array<any> = ResolvedMakerList;
      for (const item of markers) {
        if (Number(this.enterType) === EnterType.AIR || this.mapIndex === 0) {
          if (item.stationTypeId == 1) {
            // 微控站
            if (item.online) {
              if (item.type == 1) {
                item.icon = markerIcon21;
              } else if (item.type == 2) {
                item.icon = markerIcon22;
              } else if (item.type == 3) {
                item.icon = markerIcon23;
              } else if (item.type == 4) {
                item.icon = markerIcon24;
              } else if (item.type == 5) {
                item.icon = markerIcon25;
              } else if (item.type == 6) {
                item.icon = markerIcon26;
              } else {
                item.icon = markerIcon21Hui;
              }
            } else {
              item.icon = markerIcon21Hui;
            }
            item.zIndex = 99994;
            item.offset = new AMap.Pixel(-27, -22);
            item.textOffset = new AMap.Pixel(0, 8);
          } else if (item.stationTypeId == 2) {
            // 区控站
            if (item.online) {
              if (item.type == 1) {
                item.icon = markerIcon11;
              } else if (item.type == 2) {
                item.icon = markerIcon12;
              } else if (item.type == 3) {
                item.icon = markerIcon13;
              } else if (item.type == 4) {
                item.icon = markerIcon14;
              } else if (item.type == 5) {
                item.icon = markerIcon15;
              } else if (item.type == 6) {
                item.icon = markerIcon16;
              } else {
                item.icon = markerIcon11Hui;
              }
            } else {
              item.icon = markerIcon11Hui;
            }
            item.zIndex = 99996;
            item.offset = new AMap.Pixel(-22.5, -22.5);
            item.textOffset = new AMap.Pixel(0, 3);
          } else if (item.stationTypeId == 4) {
            // 国控站
            if (item.online) {
              if (item.type == 1) {
                item.icon = markerIcon41;
              } else if (item.type == 2) {
                item.icon = markerIcon42;
              } else if (item.type == 3) {
                item.icon = markerIcon43;
              } else if (item.type == 4) {
                item.icon = markerIcon44;
              } else if (item.type == 5) {
                item.icon = markerIcon45;
              } else if (item.type == 6) {
                item.icon = markerIcon46;
              } else {
                item.icon = markerIcon41Hui;
              }
            } else {
              item.icon = markerIcon41Hui;
            }
            item.zIndex = 99998;
            item.offset = new AMap.Pixel(-25.5, -18.5);
            item.textOffset = new AMap.Pixel(-5, 2);
          } else if (item.stationTypeId == 3) {
            // 市控站
            if (item.online) {
              if (item.type == 1) {
                item.icon = markerIcon31;
              } else if (item.type == 2) {
                item.icon = markerIcon32;
              } else if (item.type == 3) {
                item.icon = markerIcon33;
              } else if (item.type == 4) {
                item.icon = markerIcon34;
              } else if (item.type == 5) {
                item.icon = markerIcon35;
              } else if (item.type == 6) {
                item.icon = markerIcon36;
              } else {
                item.icon = markerIcon31Hui;
              }
            } else {
              item.icon = markerIcon31Hui;
            }
            item.zIndex = 99998;
            item.offset = new AMap.Pixel(-25.5, -18.5);
            item.textOffset = new AMap.Pixel(0, 3);
          }
        }
      }
      // 空气地图添加站点marker
      markers.forEach((marker: any) => {
        let stationMarker = "";
        // if (marker.isAlarm) {
        //   stationMarker = new AMap.Marker({
        //     map: map,
        //     icon: jingbaoIcon,
        //     zIndex: marker.zIndex,
        //     position: [marker.gcLng, marker.gcLat],
        //     offset: new AMap.Pixel(-31.45, -39.1),
        //     data: marker
        //   });
        // } else {
        //   stationMarker = new AMap.Marker({
        //     map: map,
        //     icon: marker.icon,
        //     zIndex: marker.zIndex,
        //     position: [marker.gcLng, marker.gcLat],
        //     // offset: new AMap.Pixel(-12.8, -15.2),
        //     offset: marker.offset,
        //     data: marker
        //   });
        // }
        stationMarker = new AMap.Marker({
          map: map,
          icon: marker.icon,
          zIndex: marker.zIndex,
          position: [marker.gcLng, marker.gcLat],
          // offset: new AMap.Pixel(-12.8, -15.2),
          offset: marker.offset,
          data: marker,
        });
        this.clickMar.push(stationMarker);
        // 显示每项指标的数字
        let textColor: any = "";
        if (marker.type == 1) {
          textColor = "#09960A";
        } else if (marker.type == 2) {
          textColor = "#c59c0a";
        } else if (marker.type == 3) {
          textColor = "#ffffff";
        } else if (marker.type == 4) {
          textColor = "#ffffff";
        } else if (marker.type == 5) {
          textColor = "#ffffff";
        } else if (marker.type == 6) {
          textColor = "#ffffff";
        }
        // if (!marker.isAlarm) {}
        const airText = new AMap.Text({
          text: !marker.online
            ? "—"
            : marker.concentration
            ? marker.concentration
            : "—",
          anchor: "center", // 设置文本标记锚点
          cursor: "pointer",
          zIndex:
            marker.stationTypeId == 1
              ? 99995
              : marker.stationTypeId == 2
              ? 99997
              : 99999,
          style: {
            width: "144px",
            height: "27px",
            "background-color": "transparent",
            "border-width": 0,
            "text-align": "center",
            "font-size": "12px",
            color: !marker.online
              ? "#a8a8a8"
              : textColor
              ? textColor
              : "#a8a8a8",
            "font-weight": "bold",
          },
          position: [marker.gcLng, marker.gcLat],
          offset: marker.textOffset,
          data: marker,
        });
        airText.setMap(map);
        this.airTextList.push(airText);
        AMap.event.addListener(airText, "click", this.showInfo);
      });
      // 标记点事件队列
      for (const item of this.clickMar) {
        this.markList.push({
          lnglat: {
            lng: Number(item.getPosition().lng),
            lat: Number(item.getPosition().lat),
          },
        });
        AMap.event.addListener(item, "click", this.showInfo);
      }
      // 地图Label轮播处理
      const autoTimer = () => {
        for (let index = 0; index < this.markList.length; index++) {
          const timer = setTimeout(() => {
            this.showInfo(this.clickMar[index]);
            if (index === this.markList.length - 1) {
              clearTimeout(timer);
              setTimeout(autoTimer, 30000);
            }
          }, index * 30000);
        }
      };
      // 启动轮播
      if (Number(this.enterType) === EnterType.AIR) {
        // autoTimer();
      }
    } else {
      // 车辆的marker;
      const markers: Array<any> = this.mapMarker;
      for (const item of markers) {
        switch (item.type) {
          case 1:
            item.icon = this.carIconsObj["lajiCar"];
            break;
          case 2:
            item.icon = this.carIconsObj["wupaoCar"];
            break;
          case 3:
            item.icon = this.carIconsObj["canchuCar"];
            break;
          case 4:
            item.icon = this.carIconsObj["sashuiCar"];
            break;
          case 5:
            item.icon = this.carIconsObj["xisaoCar"];
            break;
          case 6:
            item.icon = this.carIconsObj["zhifaCar"];
            break;
        }
      }
      markers.forEach((marker: any) => {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const m = new AMap.Marker({
          map: map,
          position: [marker.longitude, marker.latitude],
          icon: marker.icon,
          offset: new AMap.Pixel(-18, -10),
          autoRotation: true,
          topWhenClick: true,
          zIndex: 999999,
        });
        m.data = marker;
        this.mList.push(m);
        // m.setMap(this.maps);
        AMap.event.addListener(m, "click", () => {
          this.$emit("showTaskDetail", {
            license: marker.license,
            carId: marker.carId,
          });
          //@ts-ignore
          // eslint-disable-next-line no-undef
          this.$bus.emit("checkTypeCar", {
            bottomDivType: "car",
          });
          this.jointEnforcementValue = null;
          this.createJointEnforcement();
          if (this.taskMarkerClear !== null) {
            this.maps.remove(this.taskMarkerClear);
            this.maps.remove(this.taskTextClear);
            this.taskMarkerClear = null;
            this.taskTextClear = null;
          }
          // 若之前选中过车辆，恢复之前点中的图标
          if (this.carItem) {
            this.clearCarSelected();
          }
          // 替换新点击图标
          this.carItem = m;
          let selectedCarIcon: any;
          if (m.data.type === 1) {
            selectedCarIcon = this.carIconsObj["lajiSelected"];
          }
          if (m.data.type === 2) {
            selectedCarIcon = this.carIconsObj["wupaoSelected"];
          }
          if (m.data.type === 3) {
            selectedCarIcon = this.carIconsObj["canchuSelected"];
          }
          if (m.data.type === 4) {
            selectedCarIcon = this.carIconsObj["sashuiSelected"];
          }
          if (m.data.type === 5) {
            selectedCarIcon = this.carIconsObj["xisaoSelected"];
          }
          if (m.data.type === 6) {
            selectedCarIcon = this.carIconsObj["zhifaSelected"];
          }
          m.setIcon(selectedCarIcon);
          m.setOffset(new AMap.Pixel(-25, -25));
        });
      });
    }
  }
  // 车辆搜索命中或点击
  private showCarDetail(car: any) {
    console.log(car);
    const selectedCar = this.mList.filter((item: any) => {
      return item.data.license === car.license;
    });
    selectedCar[0].emit("click");
    const selectedCarPosition = new AMap.LngLat(
      Number(selectedCar[0].data.longitude),
      Number(selectedCar[0].data.latitude)
    );
    this.maps.setCenter(selectedCarPosition);
  }
  // 清除车辆选中效果
  private clearCarSelected() {
    let carOldIcon: any;
    if (this.carItem.data.type === 1) {
      carOldIcon = this.carIconsObj["lajiCar"];
    }
    if (this.carItem.data.type === 2) {
      carOldIcon = this.carIconsObj["wupaoCar"];
    }
    if (this.carItem.data.type === 3) {
      carOldIcon = this.carIconsObj["canchuCar"];
    }
    if (this.carItem.data.type === 4) {
      carOldIcon = this.carIconsObj["sashuiCar"];
    }
    if (this.carItem.data.type === 5) {
      carOldIcon = this.carIconsObj["xisaoCar"];
    }
    if (this.carItem.data.type === 6) {
      carOldIcon = this.carIconsObj["zhifaCar"];
    }
    this.carItem.setIcon(carOldIcon);
    this.carItem.setOffset(new AMap.Pixel(-18, -10));
  }
  // 车辆图标对象
  private carIconsObj = {};
  // 开始
  private startPlayback(): void {
    // 清空执法联动的点
    const map = this.maps;
    if (this.clearJointEnforcementMarker.length > 0) {
      map.remove(this.clearJointEnforcementMarker);
      this.clearJointEnforcementMarker = [];
    }
    this.playbackMarker.moveAlong(this.pathList, 3000);
  }
  // 暂停
  private suspendPlayback(): void {
    console.log("执行暂停");
    if (this.playbackMarker) {
      this.playbackMarker.pauseMove();
      const position = this.playbackMarker.getPosition();
      for (const item of this.carRecordList) {
        /* eslint-disable @typescript-eslint/ban-ts-ignore */
        //@ts-ignore
        const latOffset = console.log(item.coordinate[1].lat, position);
      }
    }
  }
  // 继续
  private resumePlayback(): void {
    if (this.playbackMarker) {
      this.playbackMarker.resumeMove();
    }
  }
  // 结束
  private stopPlayback(): void {
    this.jointEnforcementValue = null;
    this.createJointEnforcement();
    if (this.playbackMarker) {
      this.playbackMarker.stopMove();
    } else {
      return;
    }
    this.mList.forEach((item) => {
      item.show();
    });
    this.maps.remove([this.playbackMarker]);
    this.playbackMarker = null;
    if (this.Polyline) {
      this.maps.remove(this.Polyline);
    }
    if (this.startPoint) {
      this.maps.remove(this.startPoint);
    }
    if (this.endPoint) {
      this.maps.remove(this.endPoint);
    }
  }
  // 倍速播放
  private multipleSpeed(multiple: number): void {
    this.playbackMarker.stopMove();
    this.playbackMarker.moveAlong(this.pathList, 3000 * multiple);
  }

  // 水质监测站Marker
  private createWaterSationMarker(): void {
    const map = this.maps;
    if (this.waterStationMarkerList.length > 0) {
      map.remove(this.waterStationMarkerList);
    }
    // 水质监测点Icon
    const building = new AMap.Icon({
      // 图标尺寸
      // size: new AMap.Size(76, 48),
      size: new AMap.Size(72, 72),
      // 图标的取图地址
      image: waterSatationNormal,
      // 图标所用图片大小
      // imageSize: new AMap.Size(28, 35),
      imageSize: new AMap.Size(72, 72),
      // 图标取图偏移量
      imageOffset: new AMap.Pixel(0, 0),
    });
    const stationIconAtive = new AMap.Icon({
      // 图标尺寸
      size: new AMap.Size(72, 72),
      // 图标的取图地址
      image: waterSatationActive,
      // 图标所用图片大小
      imageSize: new AMap.Size(72, 72),
      // 图标取图偏移量
      imageOffset: new AMap.Pixel(0, 0),
    });
    const offlineWaterStationIcon = new AMap.Icon({
      // 图标尺寸
      size: new AMap.Size(72, 72),
      // 图标的取图地址
      image: offlineWaterStation,
      // 图标所用图片大小
      imageSize: new AMap.Size(72, 72),
      // 图标取图偏移量
      imageOffset: new AMap.Pixel(0, 0),
    });
    const warningWaterStationIcon = new AMap.Icon({
      // 图标尺寸
      size: new AMap.Size(72, 72),
      // 图标的取图地址
      image: warningWaterStation,
      // 图标所用图片大小
      imageSize: new AMap.Size(72, 72),
      // 图标取图偏移量
      imageOffset: new AMap.Pixel(0, 0),
    });
    const warningWaterStationSelectIcon = new AMap.Icon({
      // 图标尺寸
      size: new AMap.Size(72, 72),
      // 图标的取图地址
      image: warningWaterStationSelect,
      // 图标所用图片大小
      imageSize: new AMap.Size(72, 72),
      // 图标取图偏移量
      imageOffset: new AMap.Pixel(0, 0),
    });
    const offLineIcon = new AMap.Icon({
      // 图标尺寸
      size: new AMap.Size(72, 72),
      // 图标的取图地址
      image: offLine,
      // 图标所用图片大小
      imageSize: new AMap.Size(72, 72),
      // 图标取图偏移量
      imageOffset: new AMap.Pixel(0, 0),
    });
    let waterSationMarker: any = null;
    this.mapMarker.forEach((waterSation: any) => {
      let offsetMarker = new AMap.Pixel(-36, -72);
      // 连心桥 stationId: "20171207000002"
      // 高桥 stationId: "60681008"
      // 洞子口 stationId: "60681004"
      // 沙河大桥 stationId: "60681006"
      if (waterSation.stationId == "60681008") {
        // 高桥
        offsetMarker = new AMap.Pixel(-36, -82);
        console.log("高桥 ", waterSation);
        waterSation.lat = "30.71883199";
      } else if (waterSation.stationId == "60681004") {
        // 洞子口
        offsetMarker = new AMap.Pixel(-36, -82);
        waterSation.lng = "104.0569359";
        console.log("洞子口 ", waterSation);
      } else if (waterSation.stationId == "60681006") {
        // 沙河大桥
        offsetMarker = new AMap.Pixel(-36, -82);
        waterSation.lat = "30.71908199";
        console.log("沙河大桥 ", waterSation);
      }
      console.log("超标站点数据", waterSation.phAlarm);
      // 站点是否有超项
      const warnState =
        waterSation.codAlarm ||
        waterSation.nh3nAlarm ||
        waterSation.totalPhosphorusAlarm ||
        waterSation.phAlarm;
      // 当前选中站点 未超标
      if (
        this.currentSelectedStation &&
        this.currentSelectedStation === waterSation.stationId &&
        !warnState
      ) {
        if (waterSation.online == 0) {
          waterSationMarker = new AMap.Marker({
            // map: map,
            icon: offLineIcon,
            position: [waterSation.lng, waterSation.lat],
            offset: new AMap.Pixel(-36, -72),
            data: waterSation,
          });
        } else {
          waterSationMarker = new AMap.Marker({
            // map: map,
            icon: stationIconAtive,
            position: [waterSation.lng, waterSation.lat],
            offset: new AMap.Pixel(-36, -72),
            data: waterSation,
          });
        }
      } else if (
        // 当前选中站点 超标
        this.currentSelectedStation &&
        this.currentSelectedStation === waterSation.stationId &&
        warnState &&
        waterSation.online
      ) {
        waterSationMarker = new AMap.Marker({
          // map: map,
          icon: warningWaterStationSelectIcon,
          position: [waterSation.lng, waterSation.lat],
          offset: new AMap.Pixel(-36, -72),
          data: waterSation,
        });
      } else if (warnState && waterSation.online) {
        // 未选中站点超标
        waterSationMarker = new AMap.Marker({
          // map: map,
          icon: warningWaterStationIcon,
          position: [waterSation.lng, waterSation.lat],
          offset: new AMap.Pixel(-36, -72),
          data: waterSation,
        });
      } else if (waterSation.online === 0) {
        // 离线
        waterSationMarker = new AMap.Marker({
          // map: map,
          icon: offlineWaterStationIcon,
          position: [waterSation.lng, waterSation.lat],
          offset: new AMap.Pixel(-36, -72),
          data: waterSation,
        });
      } else {
        waterSationMarker = new AMap.Marker({
          // map: map,
          icon: building,
          position: [waterSation.lng, waterSation.lat],
          offset: new AMap.Pixel(-36, -72),
          data: waterSation,
        });
      }
      this.waterStationMarkerList.push(waterSationMarker);
      waterSationMarker.setTitle(`${waterSation.stationName}`);
      if (
        this.currentSelectedStation &&
        this.currentSelectedStation === waterSation.stationId
      ) {
        waterSationMarker.setLabel({
          offset: new AMap.Pixel(0, 29), //设置文本标注偏移量
          content: `<div>${waterSation.stationName}</div>`, //设置文本标注内容
          direction: "top", //设置文本标注方位
        });
      } else {
        waterSationMarker.setLabel({
          offset: new AMap.Pixel(0, 25), //设置文本标注偏移量
          content: `<div>${waterSation.stationName}</div>`, //设置文本标注内容
          direction: "top", //设置文本标注方位
        });
      }
      waterSationMarker.setMap(map);
      AMap.event.addListener(waterSationMarker, "click", this.showInfo);
    });
  }
  private mapWaterStationsVideoList: any = [];
  private videoActiveMarker: any = null;
  // 水质监控
  private createMapWaterStationsVideo() {
    const map = this.maps;
    if (this.mapWaterStationsVideoList.length > 0) {
      map.remove(this.mapWaterStationsVideoList);
      this.mapWaterStationsVideoList = [];
    }
    // 水质监测点Icon
    const sxt = new AMap.Icon({
      // 图标尺寸
      // size: new AMap.Size(76, 48),
      size: new AMap.Size(28, 28),
      // 图标的取图地址
      image: stationIconPic,
      // 图标所用图片大小
      // imageSize: new AMap.Size(28, 35),
      imageSize: new AMap.Size(28, 28),
      // 图标取图偏移量
      imageOffset: new AMap.Pixel(0, 0),
    });
    this.mapWaterStationsVideo.forEach((waterSation: any) => {
      const m: any = new AMap.Marker({
        map: map,
        icon: sxt,
        position: [waterSation.lng, waterSation.lat],
        offset: new AMap.Pixel(-36, -20),
        data: waterSation,
        zIndex: 99999,
      });
      this.mapWaterStationsVideoList.push(m);
      const carActive = new AMap.Icon({
        // 图标尺寸
        size: new AMap.Size(36, 36),
        // 图标的取图地址
        image: carSelected,
        // a.amap.com/jsapi_demos/static/demo-center/icons/dir-marker.png
        // 图标所用图片大小
        imageSize: new AMap.Size(36, 36),
        // 图标取图偏移量
        imageOffset: new AMap.Pixel(0, 0),
      });
      AMap.event.addListener(m, "click", (data: any) => {
        if (this.videoActiveMarker !== null) {
          map.remove(this.videoActiveMarker);
        }
        this.videoActiveMarker = new AMap.Marker({
          map: map,
          position: [m.getPosition().lng, m.getPosition().lat],
          icon: carActive,
          offset: new AMap.Pixel(-40, -24),
          autoRotation: true,
          zIndex: 99998,
        });
        this.$emit("cameraChange", data.target.w.data);
      });
    });
  }
  private heatmap: any = "";
  // 热力图
  private heathMap(): void {
    const map = this.maps;
    const maplist: any = [];
    if (map.w) {
      map.w.layers.forEach((item: any, index: number) => {
        if (item.w.type !== "heatMap3DLayer") {
          maplist.push(item);
        }
      });
      map.w.layers = maplist;
    }

    // let heatmap: any;
    const that = this;
    const heathMapMax = this.heathMapMax;
    const heatMapData: any = this.heatMapDataList;
    map.plugin(["AMap.Heatmap"], function() {
      if (that.heatmap) {
        that.heatmap.setMap(null);
      }
      //初始化heatmap对象
      that.heatmap = new AMap.Heatmap(map, {
        radius: 65, //给定半径
        opacity: [0.4, 1],
        gradient: {
          0.1: "rgb(0,255,0)",
          0.2: "rgb(0,255,0)",
          0.3: "rgb(0,255,0)",
          0.4: "rgb(0,255,0)",
          0.6: "rgb(255,255,0)",
          0.8: "rgb(255,126,0)",
          1: "rgb(255,0,0)",
        },
        zIndex: 9999,
      });
      //设置数据集
      that.heatmap.setDataSet({
        data: heatMapData,
        max: heathMapMax,
      });
    });
  }

  // marker的点击事件
  private stationCode: any;
  private async showInfo(data: any): Promise<any> {
    let details: any;
    if (data.target) {
      // 鼠标滑过事件
      details = data.target.w.data;
    } else {
      // 定时函数触发
      details = data.w.data;
    }
    // if(Number(this.enterType) === EnterType.AIR){
    // }
    this.stationCode =
      Number(this.enterType) === EnterType.AIR || this.mapIndex === 0
        ? details.stationCode
        : details.stationId;
    if (Number(this.enterType) === EnterType.AIR || this.mapIndex === 0) {
      await airStationDetails(this.stationCode).then((res: any) => {
        res.data.data.dataTime = res.data.data.dataTime
          ? res.data.data.dataTime.slice(5, 16)
          : "-";
        details.pollutionDetailsList = res.data.data;
      });
    }
    if (Number(this.enterType) === EnterType.AIR || this.mapIndex === 0) {
      // SO2
      for (const item of this.SO2AirColors) {
        // SO2
        if (details.pollutionDetailsList["SO2"] !== null) {
          if (
            details.pollutionDetailsList["SO2"] >= item.min &&
            details.pollutionDetailsList["SO2"] <= item.max
          ) {
            details.pollutionDetailsList["SO2Color"] = item.color;
          }
        } else {
          details.pollutionDetailsList["SO2Color"] = "#fff";
        }
      }
      // NO2
      for (const item of this.NO2AirColors) {
        // NO2
        if (details.pollutionDetailsList["NO2"] !== null) {
          if (
            details.pollutionDetailsList["NO2"] >= item.min &&
            details.pollutionDetailsList["NO2"] <= item.max
          ) {
            details.pollutionDetailsList["NO2Color"] = item.color;
          }
        } else {
          details.pollutionDetailsList["SO2Color"] = "#fff";
        }
      }
      // PM2.5
      for (const item of this.PM25AirColors) {
        // PM2.5
        if (details.pollutionDetailsList["PM2.5"] !== null) {
          if (
            details.pollutionDetailsList["PM2.5"] >= item.min &&
            details.pollutionDetailsList["PM2.5"] <= item.max
          ) {
            details.pollutionDetailsList["PM2.5Color"] = item.color;
          }
        } else {
          details.pollutionDetailsList["SO2Color"] = "#fff";
        }
      }
      // PM10
      for (const item of this.PM10AirColors) {
        // PM10
        if (details.pollutionDetailsList["PM10"] !== null) {
          if (
            details.pollutionDetailsList["PM10"] >= item.min &&
            details.pollutionDetailsList["PM10"] <= item.max
          ) {
            details.pollutionDetailsList["PM10Color"] = item.color;
          }
        } else {
          details.pollutionDetailsList["SO2Color"] = "#fff";
        }
      }
      // O3
      for (const item of this.O3AirColors) {
        // O3
        if (details.pollutionDetailsList["03"] !== null) {
          if (
            details.pollutionDetailsList["03"] >= item.min &&
            details.pollutionDetailsList["03"] <= item.max
          ) {
            details.pollutionDetailsList["O3Color"] = item.color;
          }
        } else {
          details.pollutionDetailsList["SO2Color"] = "#fff";
        }
      }
      // CO
      for (const item of this.COAirColors) {
        // CO
        if (details.pollutionDetailsList["CO"] !== null) {
          if (
            details.pollutionDetailsList["CO"] >= item.min &&
            details.pollutionDetailsList["CO"] <= item.max
          ) {
            details.pollutionDetailsList["COColor"] = item.color;
          }
        } else {
          details.pollutionDetailsList["SO2Color"] = "#fff";
        }
      }
      // AQI
      for (const item of this.AQIAirColors) {
        // AQI
        if (details.pollutionDetailsList["AQI"] !== null) {
          if (
            details.pollutionDetailsList["AQI"] >= item.min &&
            details.pollutionDetailsList["AQI"] <= item.max
          ) {
            details.pollutionDetailsList["AQIColor"] = item.color;
            details.pollutionDetailsList["AQIText"] = item.text;
          }
        } else {
          details.pollutionDetailsList["SO2Color"] = "#fff";
        }
      }
    }
    if (Number(this.enterType) === EnterType.WATER || this.mapIndex === 1) {
      // if (details.online === 0) {
      //   message.warning("站点处于离线状态！", 3);
      //   this.currentSelectedStation = null;
      //   this.createWaterSationMarker();
      //   return;
      // }
      this.currentSelectedStation = details.stationId;
      this.$emit("waterStationSelectChange", details.stationId);
      this.createWaterSationMarker();
    }
    if (Number(this.enterType) === EnterType.AIR || this.mapIndex === 0) {
      // if (details.online === 0) {
      //   message.warning("站点处于离线状态！", 3);
      //   return;
      // }
    }
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const that: any = this;
    let infoWindow: any;
    const map = this.maps;
    //在指定位置打开信息窗体
    function openInfo() {
      //构建信息窗体中显示的内容
      let html: Array<string> = [];
      // enterType:1--空气质量监测     2--水质监测
      if (that.enterType === EnterType.AIR || that.mapIndex === 0) {
        // <div class="marker-top-state" style="background:red;">异常</div>
        // <div class="marker-top-state" style="background:#a8a8a8;">离线</div>
        // 空气质量监测
        html = [
          `<div class="marker-content">
          <img src="https://oss-chengdu.vankeytech.com:9000/vankeytech-ep/sys/shanchu3.png" alt class="closeInfoWindow" onclick="closeInfoWindow()"/>
          <div class="marker-top">
            <div class="marker-top-top">
              <div class="marker-top-name">
                <div class="marker-top-name1">${details.positionName}</div>
                <div class="marker-top-state" style="background:${
                  details.online ? "#22B331" : "#a8a8a8"
                };">${details.online ? "在线" : "离线"}</div>
              </div>
              <div class="update-time">${
                details.pollutionDetailsList.dataTime
              }</div>
            </div>
            <div class="marker-top-bottom">
              <div class="marker-top-left">
                <div class="dian"></div>
                <div>当前空气质量：<text style="color:${
                  details.pollutionDetailsList["AQIColor"]
                }">${
            details.pollutionDetailsList["AQI"] === undefined
              ? "－"
              : details.pollutionDetailsList["AQI"]
          }</text></div>
              </div>
              <div class="marker-top-right">
                <div style="color:${
                  details.pollutionDetailsList["AQIColor"]
                }">${
            details.pollutionDetailsList["AQIText"] === undefined
              ? "－"
              : details.pollutionDetailsList["AQIText"]
          }</div>
              </div>
            </div>
          </div>
          <div class="marker-bottom">
            <div>
              <div>
                <p style="color:${
                  details.pollutionDetailsList["SO2"] === undefined ||
                  details.pollutionDetailsList["SO2"] === null
                    ? "#A9A9A9"
                    : "#ffffff"
                }">SO₂</p>
                <p style="color:${details.pollutionDetailsList["SO2Color"]}">${
            details.pollutionDetailsList["SO2"] === undefined ||
            details.pollutionDetailsList["SO2"] === null
              ? "－"
              : details.pollutionDetailsList["SO2"]
          }</p>
              </div>
              <div>
                <p style="color:${
                  details.pollutionDetailsList["NO2"] === undefined ||
                  details.pollutionDetailsList["NO2"] === null
                    ? "#A9A9A9"
                    : "#ffffff"
                }">NO₂</p>
                <p style="color:${details.pollutionDetailsList["NO2Color"]}">${
            details.pollutionDetailsList["NO2"] === undefined ||
            details.pollutionDetailsList["NO2"] === null
              ? "－"
              : details.pollutionDetailsList["NO2"]
          }</p>
              </div>
              <div>
                <p style="color:${
                  details.pollutionDetailsList["PM2.5"] === undefined ||
                  details.pollutionDetailsList["PM2.5"] === null
                    ? "#A9A9A9"
                    : "#ffffff"
                }">PM₂.₅</p>
                <p style="color:${
                  details.pollutionDetailsList["PM2.5Color"]
                }">${
            details.pollutionDetailsList["PM2.5"] === undefined ||
            details.pollutionDetailsList["PM2.5"] === null
              ? "－"
              : details.pollutionDetailsList["PM2.5"]
          }</p>
              </div>
              <div>
                <p style="color:${
                  details.pollutionDetailsList["03"] === undefined ||
                  details.pollutionDetailsList["03"] === null
                    ? "#A9A9A9"
                    : "#ffffff"
                }">O₃</p>
                <p style="color:${details.pollutionDetailsList["O3Color"]}">${
            details.pollutionDetailsList["03"] === undefined ||
            details.pollutionDetailsList["03"] === null
              ? "－"
              : details.pollutionDetailsList["03"]
          }</p>
              </div>
              <div>
                <p style="color:${
                  details.pollutionDetailsList["CO"] === undefined ||
                  details.pollutionDetailsList["CO"] === null
                    ? "#A9A9A9"
                    : "#ffffff"
                }">CO</p>
                <p style="color:${details.pollutionDetailsList["COColor"]}">${
            details.pollutionDetailsList["CO"] === undefined ||
            details.pollutionDetailsList["CO"] === null
              ? "－"
              : details.pollutionDetailsList["CO"]
          }</p>
              </div>
              <div>
                <p style="color:${
                  details.pollutionDetailsList["PM10"] === undefined ||
                  details.pollutionDetailsList["PM10"] === null
                    ? "#A9A9A9"
                    : "#ffffff"
                }">PM₁₀</p>
                <p style="color:${details.pollutionDetailsList["PM10Color"]}">${
            details.pollutionDetailsList["PM10"] === undefined ||
            details.pollutionDetailsList["PM10"] === null
              ? "－"
              : details.pollutionDetailsList["PM10"]
          }</p>
              </div>
            </div>
          <div class="marker-top-detail" onclick="toAirDetails(1, 1)">查看详情</div>
          </div>
          <div class="triangle"></div>
        </div>`,
        ];
      } else if (that.enterType === EnterType.WATER || that.mapIndex === 1) {
        // 水质监测
        // <div class="water-top-type">
        //         <div class="dian"></div>
        //         <div>
        //           水质分类：暂无
        //         </div>
        //       </div>
        const waterTime = details.time !== "" ? details.time.slice(5, 16) : "-";
        html = [
          `<div class="water-content">
          <img src="https://oss-chengdu.vankeytech.com:9000/vankeytech-ep/sys/shanchu3.png" alt class="closeInfoWindow" onclick="closeInfoWindow()"/>
          <div class="water-top">
            <div class="water-top-top">
              <div class="water-top-name">
                <div class="water-top-name1">${details.stationName}</div>
                <div class="water-top-state" style="background:${
                  details.online ? "#22B331" : "#a8a8a8"
                };">${details.online ? "在线" : "离线"}</div>
              </div>
              <div class="update-time">${waterTime}</div>
            </div>
          </div>
          <div class="water-bottom">
            <div>
              <div style="width:15%;">
                <p>PH</p>
                <p style="color: ${details.phAlarm ? "red" : ""}">${
            details.ph ? details.ph : " - "
          }</p>
              </div>
              <div>
                <p>氨氮(mg/L)</p>
                <p style="color: ${details.nh3nAlarm ? "red" : ""}">${
            details.nh3n ? details.nh3n : " - "
          }</p>
              </div>
              <div>
                <p>总磷(mg/L)</p>
                <p style="color: ${
                  details.totalPhosphorusAlarm ? "red" : ""
                }">${
            details.totalPhosphorus ? details.totalPhosphorus : " - "
          }</p>
              </div>
              <div>
                <p>COD(mg/L)</p>
                <p style="color: ${details.codAlarm ? "red" : ""}">${
            details.cod ? details.cod : " - "
          }</p>
              </div>
            </div>
            <div class="water-bottom-btn" onclick="toAirDetails(11,2)">查看详情</div>;
          </div>
        </div>`,
        ];
      }
      infoWindow = new AMap.InfoWindow({
        // anchor: "bottom-center",
        offset:
          that.enterType === EnterType.WATER
            ? new AMap.Pixel(0, -50)
            : new AMap.Pixel(0, 0),
        isCustom: true,
        content: html.join(""), //使用默认信息窗体框样式，显示信息内容
      });
      if (that.enterType === EnterType.WATER || that.mapIndex === 1) {
        if (data.target) {
          infoWindow.open(map, [
            data.target.w.data.lng,
            data.target.w.data.lat,
          ]);
        } else {
          infoWindow.open(map, [data.w.data.lng, data.w.data.lat]);
        }
      } else if (that.enterType === EnterType.AIR || that.mapIndex === 0) {
        if (data.target) {
          infoWindow.open(map, [data.lnglat.lng, data.lnglat.lat]);
        } else {
          infoWindow.open(map, [data.w.data.gcLng, data.w.data.gcLat]);
        }
      }
    }
    openInfo();
    setTimeout(() => {
      infoWindow.close();
      if (Number(this.enterType) === EnterType.WATER || this.mapIndex === 1) {
        this.currentSelectedStation = null;
        this.createWaterSationMarker();
      }
    }, 60 * 1000);
  }
  private text: any = null;
  private enterpriseMarker: any = null;

  // 关闭地图信息弹框
  private closeInfoWindow() {
    console.log("closeInfoWindow");
  }

  // 添加河流水系图层
  private dynamicRivderLayer() {
    // const bounds = new AMap.Bounds([103.9, 30.647], [104.1635, 30.823]);
    // const bounds = new AMap.Bounds([103.933, 30.647], [104.15, 30.805]);
    const bounds = new AMap.Bounds([103.95, 30.65], [104.148, 30.808]);
    const imageLayer = new AMap.ImageLayer({
      url: districtRiver, // districtRiver
      bounds: bounds,
      zooms: [3, 18],
      opacity: 1,
    });
    imageLayer.setMap(this.maps);
    // const VideoLayer = new AMap.VideoLayer({
    //   autoplay: true,
    //   loop: true,
    //   zIndex: 999,
    //   url: [
    //     "http://party-building.oss-cn-hangzhou.aliyuncs.com/static/river.avi"
    //   ],
    //   bounds: bounds,
    //   zooms: [3, 18],
    //   opacity: 0.7
    // });
    // VideoLayer.setMap(this.maps);
    // "https://a.amap.com/jsapi_demos/static/video/cloud.m4v",
    // "https://a.amap.com/jsapi_demos/static/video/cloud.mov"
  }

  // 空气的风向
  private windDirection() {
    const bounds: any = [104.151299, 30.799];
    const wind: any = this.wind == "无持续风向" ? "" : this.wind;
    let url: any;

    if (wind === "东风") {
      url = new AMap.Icon({
        // 图标尺寸
        size: new AMap.Size(130, 74),
        // 图标的取图地址
        image: dong,
        // a.amap.com/jsapi_demos/static/demo-center/icons/dir-marker.png
        // 图标所用图片大小
        imageSize: new AMap.Size(130, 74),
        // 图标取图偏移量
        imageOffset: new AMap.Pixel(0, 0),
      });
      // bounds = [104.120946, 30.73448];
    } else if (wind === "南风") {
      url = new AMap.Icon({
        // 图标尺寸
        size: new AMap.Size(74, 130),
        // 图标的取图地址
        image: nan,
        // a.amap.com/jsapi_demos/static/demo-center/icons/dir-marker.png
        // 图标所用图片大小
        imageSize: new AMap.Size(74, 130),
        // 图标取图偏移量
        imageOffset: new AMap.Pixel(0, 0),
      });
      // bounds = [104.058976, 30.686919];
    } else if (wind === "西风") {
      url = new AMap.Icon({
        // 图标尺寸
        size: new AMap.Size(130, 74),
        // 图标的取图地址
        image: xi,
        // a.amap.com/jsapi_demos/static/demo-center/icons/dir-marker.png
        // 图标所用图片大小
        imageSize: new AMap.Size(130, 74),
        // 图标取图偏移量
        imageOffset: new AMap.Pixel(0, 0),
      });
      // bounds = [103.953405, 30.738169];
    } else if (wind === "北风") {
      url = new AMap.Icon({
        // 图标尺寸
        size: new AMap.Size(74, 130),
        // 图标的取图地址
        image: bei,
        // a.amap.com/jsapi_demos/static/demo-center/icons/dir-marker.png
        // 图标所用图片大小
        imageSize: new AMap.Size(74, 130),
        // 图标取图偏移量
        imageOffset: new AMap.Pixel(0, 0),
      });
      // bounds = [104.044385, 30.787437];
    } else if (wind === "东南风") {
      url = new AMap.Icon({
        // 图标尺寸
        size: new AMap.Size(95, 107),
        // 图标的取图地址
        image: dongnan,
        // a.amap.com/jsapi_demos/static/demo-center/icons/dir-marker.png
        // 图标所用图片大小
        imageSize: new AMap.Size(95, 107),
        // 图标取图偏移量
        imageOffset: new AMap.Pixel(0, 0),
      });
      // bounds = [104.104295, 30.697143];
    } else if (wind === "东北风") {
      url = new AMap.Icon({
        // 图标尺寸
        size: new AMap.Size(107, 95),
        // 图标的取图地址
        image: dongbei,
        // a.amap.com/jsapi_demos/static/demo-center/icons/dir-marker.png
        // 图标所用图片大小
        imageSize: new AMap.Size(107, 95),
        // 图标取图偏移量
        imageOffset: new AMap.Pixel(0, 0),
      });
      // bounds = [104.131246, 30.788469];
    } else if (wind === "西北风") {
      url = new AMap.Icon({
        // 图标尺寸
        size: new AMap.Size(95, 107),
        // 图标的取图地址
        image: xibei,
        // a.amap.com/jsapi_demos/static/demo-center/icons/dir-marker.png
        // 图标所用图片大小
        imageSize: new AMap.Size(95, 107),
        // 图标取图偏移量
        imageOffset: new AMap.Pixel(0, 0),
      });
      // bounds = [103.984304, 30.782717];
    } else if (wind === "西南风") {
      url = new AMap.Icon({
        // 图标尺寸
        size: new AMap.Size(107, 95),
        // 图标的取图地址
        image: xinan,
        // a.amap.com/jsapi_demos/static/demo-center/icons/dir-marker.png
        // 图标所用图片大小
        imageSize: new AMap.Size(107, 95),
        // 图标取图偏移量
        imageOffset: new AMap.Pixel(0, 0),
      });
      // bounds = [103.958211, 30.713231];
    }
    if (wind !== "") {
      const m = new AMap.Marker({
        map: this.maps,
        icon: url,
        position: bounds,
        offset: new AMap.Pixel(0, 0),
        data: marker,
        zIndex: 999999,
      });
      m.setMap(this.maps);
    }
  }
  //点击工地打开弹窗
  private openSite(marker: any) {
    getCurSitePM10(marker.companyId).then((res) => {
      const data = res.data.data;
      if (data) {
        data.inhalableParticles = Math.round(data.inhalableParticles);
        let infoWindow: any = null;
        const time = data.createTime
          ? data.createTime.slice(11, 16) + "更新"
          : "-";
        const str = `
      <div class="siteMsgWindow">
        <div>
          <div class="title-site" title="${marker.companyName}">${
          marker.companyName
        }</div>
          <div class="title-time">${time}</div>
          <img src="https://oss-chengdu.vankeytech.com:9000/vankeytech-ep/sys/shanchu3.png" alt onclick="closeInfoWindow()"/>
        </div>
        <div>
          <img src="https://oss-chengdu.vankeytech.com:9000/vankeytech-ep/sys/PM10.png" alt="" />
          <span>PM₁₀：</span>
          <span style="color:${data.exceedStandard ? "#e80e0e" : "#18e04a"}">${
          data.inhalableParticles
        }</span>
          <span>ug/m³</span>
          <span style="background:${
            data.exceedStandard ? "#e80e0e" : "#18e04a"
          }">${data.exceedStandard ? "超标" : "未超标"}</span>
        </div>
        <div>工地类型：砂浆混凝土</div>
      </div>
      `;
        infoWindow = new AMap.InfoWindow({
          // anchor: "bottom-center",
          isCustom: true,
          content: str, //使用默认信息窗体框样式，显示信息内容
        });
        infoWindow.open(this.maps, [marker.lng, marker.lat]);
      }
    });
    // getSiteVideoList(marker.companyId).then(res => {
    // const data = res.data.data;
    /* eslint-disable @typescript-eslint/ban-ts-ignore */
    //@ts-ignore
    // this.$bus.emit("getVideoList", data);
    // });
  }
  // 重污染企业MarkerList
  private waterBuildingMarkerList: any[] = [];
  // 当前选中的重污染企业
  private currentSelectedBuilding: any = null;
  // 重污染企业绘制Marker
  private factoryMarkerClear: any = null;
  private factoryTextClear: any = null;
  private fiveMarkerClear: any = null;
  private fiveTextClear: any = null;
  private creatBuildingMarker(): void {
    // if (this.siteMarkerList.length !== 0) {
    //   this.maps.remove(this.siteMarkerList);
    //   this.siteMarkerList = [];
    // }
    if (this.maps) {
      this.maps.clearInfoWindow();
    }
    // if (this.text !== null) {
    //   this.maps.remove(this.text);
    //   this.maps.remove(this.enterpriseMarker);
    //   this.text = null;
    //   this.enterpriseMarker = null;
    // }
    if (this.factoryTextClear !== null) {
      this.maps.remove(this.factoryTextClear);
      this.maps.remove(this.factoryMarkerClear);
      this.factoryTextClear = null;
      this.factoryMarkerClear = null;
    }
    if (this.fiveTextClear !== null) {
      this.maps.remove(this.fiveMarkerClear);
      this.maps.remove(this.fiveTextClear);
      this.fiveMarkerClear = null;
      this.fiveTextClear = null;
    }
    if (this.waterBuildingMarkerList.length !== 0) {
      this.maps.remove(this.waterBuildingMarkerList);
      this.waterBuildingMarkerList = [];
    }
    // 重污染企业
    const building = new AMap.Icon({
      // 图标尺寸
      size: new AMap.Size(32, 32),
      // 图标的取图地址
      image: buildingIconPic,
      // 图标所用图片大小
      imageSize: new AMap.Size(32, 32),
      // 图标取图偏移量
      imageOffset: new AMap.Pixel(0, 0),
    });
    // 重污染企业Active
    const buildingActiveIcon = new AMap.Icon({
      // 图标尺寸
      size: new AMap.Size(32, 28),
      // 图标的取图地址
      image: buildingActiveIconPic,
      // 图标所用图片大小
      imageSize: new AMap.Size(32, 28),
      // 图标取图偏移量
      imageOffset: new AMap.Pixel(0, 0),
    });
    // 水五（七）厂
    const shuichangBg = new AMap.Icon({
      // 图标尺寸
      size: new AMap.Size(111, 39),
      // 图标的取图地址
      image: shuichang,
      // 图标所用图片大小
      imageSize: new AMap.Size(111, 39),
      // 图标取图偏移量
      imageOffset: new AMap.Pixel(0, 0),
    });
    this.buildingMarkerList.forEach((build: any) => {
      let waterbuildingMarker: any = null;
      if (this.currentSelectedBuilding === build.value) {
        waterbuildingMarker = new AMap.Marker({
          map: this.maps,
          icon: buildingActiveIcon,
          position: [build.lng, build.lat],
          offset: new AMap.Pixel(-16, -14),
          data: build,
          zIndex: 9999,
        });
      } else {
        waterbuildingMarker = new AMap.Marker({
          map: this.maps,
          icon: building,
          position: [build.lng, build.lat],
          offset: new AMap.Pixel(-16, -16),
          data: build,
          zIndex: 9999,
        });
      }
      AMap.event.addListener(waterbuildingMarker, "click", this.buildMapClick);
      this.waterBuildingMarkerList.push(waterbuildingMarker);
      // 水七厂
      if (build.value == "510100000004") {
        this.factoryMarkerClear = new AMap.Marker({
          map: this.maps,
          icon: shuichangBg,
          zIndex: 999999,
          position: [build.lng, build.lat],
          offset: new AMap.Pixel(-55.5, -50),
          data: marker,
        });
        this.factoryTextClear = new AMap.Text({
          text: "水七厂",
          anchor: "center", // 设置文本标记锚点
          cursor: "pointer",
          zIndex: 9999999,
          style: {
            width: "111px",
            height: "39px",
            "background-color": "transparent",
            "border-width": 0,
            "text-align": "center",
            "font-size": "16px",
            color: "#fff",
          },
          position: [build.lng, build.lat],
          offset: new AMap.Pixel(0, -30),
        });
        this.factoryTextClear.setMap(this.maps);
      } else if (build.value == "020") {
        // 水五厂
        this.fiveMarkerClear = new AMap.Marker({
          map: this.maps,
          icon: shuichangBg,
          zIndex: 999999,
          position: [build.lng, build.lat],
          offset: new AMap.Pixel(-55.5, -50),
          data: marker,
        });
        this.fiveTextClear = new AMap.Text({
          text: "水五厂",
          anchor: "center", // 设置文本标记锚点
          cursor: "pointer",
          zIndex: 9999999,
          style: {
            width: "111px",
            height: "39px",
            "background-color": "transparent",
            "border-width": 0,
            "text-align": "center",
            "font-size": "16px",
            color: "#fff",
          },
          position: [build.lng, build.lat],
          offset: new AMap.Pixel(0, -30),
        });
        this.fiveTextClear.setMap(this.maps);
      }
    });
  }

  // 重污染企业Marker点击事件
  private buildMapClick(data: any): void {
    const map = this.maps;
    if (data.target) {
      this.currentSelectedBuilding = data.target.w.data.value;
      this.curMarkerClick = "";
      // this.maps.remove(this.siteMarkerList);
      // this.siteMarkerList = [];
      // this.maps.remove(this.waterBuildingMarkerList);
      // this.waterBuildingMarkerList = [];
      this.creatBuildingMarker();
      this.setConstructionSiteMarker();
      this.$emit("curCompanyChange", this.currentSelectedBuilding);
      if (this.text !== null) {
        map.remove(this.text);
        map.remove(this.enterpriseMarker);
        this.text = null;
        this.enterpriseMarker = null;
      }
      const enterprise = new AMap.Icon({
        size: new AMap.Size(144, 27),
        image: mc,
        imageSize: new AMap.Size(144, 27),
        imageOffset: new AMap.Pixel(0, 0),
      });
      this.enterpriseMarker = new AMap.Marker({
        map: this.maps,
        icon: enterprise,
        zIndex: 999,
        position: [data.target.w.data.lng, data.target.w.data.lat],
        offset: new AMap.Pixel(-72, 15),
      });
      this.text = new AMap.Text({
        text: data.target.w.data.name,
        anchor: "center", // 设置文本标记锚点
        cursor: "pointer",
        zIndex: 9999,
        style: {
          width: "144px",
          height: "27px",
          "background-color": "transparent",
          "border-width": 0,
          "text-align": "center",
          "font-size": "14px",
          color: "#fff",
        },
        position: [data.target.w.data.lng, data.target.w.data.lat],
        offset: new AMap.Pixel(0, 30),
      });
      this.text.setMap(this.maps);
    }
  }
  //工地点击事件
  private siteClick(marker: any) {
    // if (this.curMarkerClick == marker.target.w.data.companyId) {
    //   return false;
    // }
    this.curMarkerClick = marker.target.w.data.companyId;
    if (this.siteMarkerList && this.siteMarkerList.length !== 0) {
      this.maps.remove(this.siteMarkerList);
      this.siteMarkerList = [];
    }
    this.currentSelectedBuilding = "";
    if (this.text !== null) {
      this.maps.remove(this.text);
      this.maps.remove(this.enterpriseMarker);
      this.text = null;
      this.enterpriseMarker = null;
    }
    if (this.waterBuildingMarkerList.length !== 0) {
      this.maps.remove(this.waterBuildingMarkerList);
      this.waterBuildingMarkerList = [];
    }
    this.creatBuildingMarker();
    this.setConstructionSiteMarker();
    this.$emit("curSiteChange", this.curMarkerClick);
    this.openSite(marker.target.w.data);
  }
  //工地marker列表
  private siteMarkerList: any[] = [];
  private curMarkerClick = "";
  private setConstructionSiteMarker(): void {
    // if (this.text !== null) {
    //   this.maps.remove(this.text);
    //   this.maps.remove(this.enterpriseMarker);
    //   this.text = null;
    //   this.enterpriseMarker = null;
    // }
    if (this.siteMarkerList && this.siteMarkerList.length !== 0) {
      this.maps.remove(this.siteMarkerList);
      this.siteMarkerList = [];
    }
    // const setSiteIcon = new AMap.Icon({
    //   size: new AMap.Size(35, 35),
    //   image: siteImg,
    //   imageSize: new AMap.Size(35, 35),
    //   imageOffset: new AMap.Pixel(0, 0)
    // });
    // const setSiteIconActive = new AMap.Icon({
    //   size: new AMap.Size(35, 35),
    //   image: siteImgActive,
    //   imageSize: new AMap.Size(35, 35),
    //   imageOffset: new AMap.Pixel(0, 0)
    // });
    // const noCameraIcon = new AMap.Icon({
    //   // 图标尺寸
    //   size: new AMap.Size(25, 25),
    //   // 图标的取图地址
    //   image: noCamera,
    //   // a.amap.com/jsapi_demos/static/demo-center/icons/dir-marker.png
    //   // 图标所用图片大小
    //   imageSize: new AMap.Size(25, 25),
    //   // 图标取图偏移量
    //   imageOffset: new AMap.Pixel(0, 0)
    // });
    // const hasCameraIcon = new AMap.Icon({
    //   // 图标尺寸
    //   size: new AMap.Size(30, 30),
    //   // 图标的取图地址
    //   image: hasCamera,
    //   // a.amap.com/jsapi_demos/static/demo-center/icons/dir-marker.png
    //   // 图标所用图片大小
    //   imageSize: new AMap.Size(30, 30),
    //   // 图标取图偏移量
    //   imageOffset: new AMap.Pixel(0, 0)
    // });
    // 超标
    const chaobiaoMarker = new AMap.Icon({
      size: new AMap.Size(31, 31),
      image: chaobiao,
      imageSize: new AMap.Size(31, 31),
      imageOffset: new AMap.Pixel(0, 0),
    });
    // 超标选中
    const chaobiaoActiveMarker = new AMap.Icon({
      size: new AMap.Size(31, 31),
      image: chaobiaoActive,
      imageSize: new AMap.Size(31, 31),
      imageOffset: new AMap.Pixel(0, 0),
    });
    // 未超标
    const weichaobiaoMarker = new AMap.Icon({
      size: new AMap.Size(31, 31),
      image: weichaobiao,
      imageSize: new AMap.Size(31, 31),
      imageOffset: new AMap.Pixel(0, 0),
    });
    // 未超标选中
    const weichaobiaoActiveMarker = new AMap.Icon({
      size: new AMap.Size(31, 31),
      image: weichaobiaoActive,
      imageSize: new AMap.Size(31, 31),
      imageOffset: new AMap.Pixel(0, 0),
    });
    // 掉线
    const diaoxianMarker = new AMap.Icon({
      size: new AMap.Size(31, 31),
      image: diaoxian,
      imageSize: new AMap.Size(31, 31),
      imageOffset: new AMap.Pixel(0, 0),
    });
    // 未掉线
    const diaoxianActiveMarker = new AMap.Icon({
      size: new AMap.Size(31, 31),
      image: diaoxianActive,
      imageSize: new AMap.Size(31, 31),
      imageOffset: new AMap.Pixel(0, 0),
    });
    const gdzcMarker = new AMap.Icon({
      size: new AMap.Size(31, 31),
      image: gdzcIcon,
      imageSize: new AMap.Size(31, 31),
      imageOffset: new AMap.Pixel(0, 0),
    });
    const gdlxMarker = new AMap.Icon({
      size: new AMap.Size(31, 31),
      image: gdlxIcon,
      imageSize: new AMap.Size(31, 31),
      imageOffset: new AMap.Pixel(0, 0),
    });
    const gdgjMarker = new AMap.Icon({
      size: new AMap.Size(31, 31),
      image: gdgjIcon,
      imageSize: new AMap.Size(31, 31),
      imageOffset: new AMap.Pixel(0, 0),
    });
    // 摄像头
    const sxt = new AMap.Icon({
      size: new AMap.Size(30, 30),
      image: stationIconPic,
      imageSize: new AMap.Size(30, 30),
      imageOffset: new AMap.Pixel(0, 0),
    });
    this.companyList.forEach((item: any, index: number) => {
      let siteMarkerItem: any = null;
      if (item.cameraCount) {
        // 摄像头
        siteMarkerItem = new AMap.Marker({
          map: this.maps,
          icon: sxt,
          position: [item.gcLng, item.gcLat],
          offset:
            item.companyId == this.curMarkerClick
              ? new AMap.Pixel(-15.5, -15.5)
              : new AMap.Pixel(-15.5, -15.5),
          data: item,
          zIndex: 9990,
        });
      } else if (item.online) {
        // 判断是否掉线
        if (item.companyId == this.curMarkerClick) {
          if (item.pollutantState) {
            // 超标选中
            siteMarkerItem = new AMap.Marker({
              map: this.maps,
              icon: gdgjIcon,
              position: [item.gcLng, item.gcLat],
              offset:
                item.companyId == this.curMarkerClick
                  ? new AMap.Pixel(-15.5, -15.5)
                  : new AMap.Pixel(-15.5, -15.5),
              data: item,
              zIndex: 9990,
            });
          } else {
            // 未超标选中
            siteMarkerItem = new AMap.Marker({
              map: this.maps,
              icon: gdzcMarker,
              position: [item.gcLng, item.gcLat],
              offset:
                item.companyId == this.curMarkerClick
                  ? new AMap.Pixel(-15.5, -15.5)
                  : new AMap.Pixel(-15.5, -15.5),
              data: item,
              zIndex: 9990,
            });
          }
        } else {
          if (item.pollutantState) {
            // 超标
            siteMarkerItem = new AMap.Marker({
              map: this.maps,
              icon: gdgjIcon,
              position: [item.gcLng, item.gcLat],
              offset:
                item.companyId == this.curMarkerClick
                  ? new AMap.Pixel(-15.5, -15.5)
                  : new AMap.Pixel(-15.5, -15.5),
              data: item,
              zIndex: 9990,
            });
          } else {
            // 未超标
            siteMarkerItem = new AMap.Marker({
              map: this.maps,
              icon: gdzcIcon,
              position: [item.gcLng, item.gcLat],
              offset:
                item.companyId == this.curMarkerClick
                  ? new AMap.Pixel(-15.5, -15.5)
                  : new AMap.Pixel(-15.5, -15.5),
              data: item,
              zIndex: 9990,
            });
          }
        }
      } else {
        if (item.companyId == this.curMarkerClick) {
          // 掉线选中
          siteMarkerItem = new AMap.Marker({
            map: this.maps,
            icon: gdlxIcon,
            position: [item.gcLng, item.gcLat],
            offset:
              item.companyId == this.curMarkerClick
                ? new AMap.Pixel(-15.5, -15.5)
                : new AMap.Pixel(-15.5, -15.5),
            data: item,
            zIndex: 9990,
          });
        } else {
          // 掉线
          siteMarkerItem = new AMap.Marker({
            map: this.maps,
            icon: gdlxIcon,
            position: [item.gcLng, item.gcLat],
            offset:
              item.companyId == this.curMarkerClick
                ? new AMap.Pixel(-15.5, -15.5)
                : new AMap.Pixel(-15.5, -15.5),
            data: item,
            zIndex: 9990,
          });
        }
      }
      // const siteMarkerItem = new AMap.Marker({
      //   map: this.maps,
      //   // ? hasCameraIcon
      //   // : noCameraIcon
      //   icon: item.cameraCount
      //     ? item.companyId == this.curMarkerClick
      //       ? sxt
      //       : sxt
      //     : item.companyId == this.curMarkerClick
      //     ? weichaobiaoActiveMarker
      //     : weichaobiaoMarker,
      //   position: [item.gcLng, item.gcLat],
      //   offset:
      //     item.companyId == this.curMarkerClick
      //       ? new AMap.Pixel(-15.5, -15.5)
      //       : new AMap.Pixel(-15.5, -15.5),
      //   data: item,
      //   zIndex: 999999
      // });
      AMap.event.addListener(siteMarkerItem, "click", this.siteClick);
      this.siteMarkerList.push(siteMarkerItem);
    });
  }
  private PrintFactoryMarkerList: any[] = [];
  // 印刷企业列表
  private setPrintFactoryMarker(): void {
    const yszcMarker = new AMap.Icon({
      size: new AMap.Size(23.8, 29.4),
      image: yszcIcon,
      imageSize: new AMap.Size(23.8, 29.4),
      imageOffset: new AMap.Pixel(0, 0),
    });
    const ysgjMarker = new AMap.Icon({
      size: new AMap.Size(23.8, 29.4),
      image: ysgjIcon,
      imageSize: new AMap.Size(23.8, 29.4),
      imageOffset: new AMap.Pixel(0, 0),
    });
    const yslxMarker = new AMap.Icon({
      size: new AMap.Size(23.8, 29.4),
      image: yslxIcon,
      imageSize: new AMap.Size(23.8, 29.4),
      imageOffset: new AMap.Pixel(0, 0),
    });
    if (this.PrintFactoryMarkerList.length !== 0) {
      this.maps.remove(this.PrintFactoryMarkerList);
      this.PrintFactoryMarkerList = [];
    }
    this.printFactoryList.forEach((item: any, index: number) => {
      let printMarkerItem: any = null;
      printMarkerItem = new AMap.Marker({
        map: this.maps,
        icon: item.isAlarm ? ysgjMarker : yszcMarker,
        position: [item.lng, item.lat],
        offset:
          item.companyId == this.curMarkerClick
            ? new AMap.Pixel(-15.5, -15.5)
            : new AMap.Pixel(-15.5, -15.5),
        data: item,
        zIndex: 9990,
      });
      this.PrintFactoryMarkerList.push(printMarkerItem);
    });
  }
  private GarageMarkerList: any[] = [];
  private setGarageMarker(): void {
    const qxzcMarker = new AMap.Icon({
      size: new AMap.Size(23.8, 29.4),
      image: qxzcIcon,
      imageSize: new AMap.Size(23.8, 29.4),
      imageOffset: new AMap.Pixel(0, 0),
    });
    const qxgjMarker = new AMap.Icon({
      size: new AMap.Size(23.8, 29.4),
      image: qxgjIcon,
      imageSize: new AMap.Size(23.8, 29.4),
      imageOffset: new AMap.Pixel(0, 0),
    });
    if (this.GarageMarkerList.length !== 0) {
      this.maps.remove(this.GarageMarkerList);
      this.GarageMarkerList = [];
    }
    this.garageList.forEach((item: any, index: number) => {
      let garageMarkerItem: any = null;
      garageMarkerItem = new AMap.Marker({
        map: this.maps,
        icon: item.isAlarm ? qxgjMarker : qxzcMarker,
        position: [item.lng, item.lat],
        offset:
          item.companyId == this.curMarkerClick
            ? new AMap.Pixel(-15.5, -15.5)
            : new AMap.Pixel(-15.5, -15.5),
        data: item,
        zIndex: 9990,
      });
      this.GarageMarkerList.push(garageMarkerItem);
    });
  }
  private gasMarkerList: any[] = [];
  private setGasMarker(): void {
    const jyzcMarker = new AMap.Icon({
      size: new AMap.Size(23.8, 29.4),
      image: jyzcIcon,
      imageSize: new AMap.Size(23.8, 29.4),
      imageOffset: new AMap.Pixel(0, 0),
    });
    const jygjMarker = new AMap.Icon({
      size: new AMap.Size(23.8, 29.4),
      image: jygjIcon,
      imageSize: new AMap.Size(23.8, 29.4),
      imageOffset: new AMap.Pixel(0, 0),
    });
    if (this.gasMarkerList.length !== 0) {
      this.maps.remove(this.gasMarkerList);
      this.gasMarkerList = [];
    }
    this.garageList.forEach((item: any, index: number) => {
      let gasMarkerItem: any = null;
      gasMarkerItem = new AMap.Marker({
        map: this.maps,
        icon: item.isAlarm ? jygjMarker : jyzcMarker,
        position: [item.lng, item.lat],
        offset:
          item.companyId == this.curMarkerClick
            ? new AMap.Pixel(-15.5, -15.5)
            : new AMap.Pixel(-15.5, -15.5),
        data: item,
        zIndex: 9990,
      });
      this.gasMarkerList.push(gasMarkerItem);
    });
  }
  private restaurantMarkerList: any[] = [];
  private setRestaurantMarker(): void {
    const yyzcMarker = new AMap.Icon({
      size: new AMap.Size(23.8, 29.4),
      image: yyzcIcon,
      imageSize: new AMap.Size(23.8, 29.4),
      imageOffset: new AMap.Pixel(0, 0),
    });
    const yygjMarker = new AMap.Icon({
      size: new AMap.Size(23.8, 29.4),
      image: yygjIcon,
      imageSize: new AMap.Size(23.8, 29.4),
      imageOffset: new AMap.Pixel(0, 0),
    });
    if (this.restaurantMarkerList.length !== 0) {
      this.maps.remove(this.restaurantMarkerList);
      this.restaurantMarkerList = [];
    }
    this.restaurantList.forEach((item: any, index: number) => {
      let restaurantMarkerItem: any = null;
      restaurantMarkerItem = new AMap.Marker({
        map: this.maps,
        icon: item.isAlarm ? yygjMarker : yyzcMarker,
        position: [item.lng, item.lat],
        offset:
          item.companyId == this.curMarkerClick
            ? new AMap.Pixel(-15.5, -15.5)
            : new AMap.Pixel(-15.5, -15.5),
        data: item,
        zIndex: 9990,
      });
      this.restaurantMarkerList.push(restaurantMarkerItem);
    });
  }

  private emergencyMarker: any = null;
  // 应急指挥中心
  private createEmergencyHome() {
    if (this.emergencyMarker != null) {
      this.maps.remove(this.emergencyMarker);
    }
    const setSiteIcon = new AMap.Icon({
      // 图标尺寸
      size: new AMap.Size(162, 162),
      // 图标的取图地址
      image: emergencyCenter,
      // 图标所用图片大小
      imageSize: new AMap.Size(162, 162),
      // 图标取图偏移量
      imageOffset: new AMap.Pixel(0, 0),
    });
    this.emergencyMarker = new AMap.Marker({
      map: this.maps,
      icon: setSiteIcon,
      position: [104.052269, 30.691354],
      offset: new AMap.Pixel(-81, -81),
      // data: item,
      zIndex: 999999,
    });
  }
  private emergencyMarkerList: any[] = [];
  private emergencyBgMarkerClear: any = null;
  private emergencyTextClear: any = null;
  // 添加应急指挥
  private createEmergencyMarker() {
    if (this.emergencyMarkerList.length != 0) {
      this.maps.remove(this.emergencyMarkerList);
      this.emergencyMarkerList = [];
    }
    if (this.emergencyTextClear != null) {
      this.maps.remove(this.emergencyBgMarkerClear);
      this.maps.remove(this.emergencyTextClear);
      this.emergencyBgMarkerClear = null;
      this.emergencyTextClear = null;
    }
    // const red = new AMap.Icon({
    //   // 图标尺寸
    //   size: new AMap.Size(62, 139),
    //   // 图标的取图地址
    //   image: yjhong,
    //   // 图标所用图片大小
    //   imageSize: new AMap.Size(62, 139),
    //   // 图标取图偏移量
    //   imageOffset: new AMap.Pixel(0, 0)
    // });
    // const green = new AMap.Icon({
    //   // 图标尺寸
    //   size: new AMap.Size(62, 139),
    //   // 图标的取图地址
    //   image: yjlv,
    //   // 图标所用图片大小
    //   imageSize: new AMap.Size(62, 139),
    //   // 图标取图偏移量
    //   imageOffset: new AMap.Pixel(0, 0)
    // });
    // 综合行政执法局
    const zhzfj = new AMap.Icon({
      size: new AMap.Size(54, 60),
      image: zhzfjIcon,
      imageSize: new AMap.Size(54, 60),
      imageOffset: new AMap.Pixel(0, 0),
    });
    // 生态环境局
    const sthjj = new AMap.Icon({
      size: new AMap.Size(54, 60),
      image: sthjjIcon,
      imageSize: new AMap.Size(54, 60),
      imageOffset: new AMap.Pixel(0, 0),
    });
    // 住房建设和交通运输局
    const zjj = new AMap.Icon({
      size: new AMap.Size(54, 60),
      image: zjjIcon,
      imageSize: new AMap.Size(54, 60),
      imageOffset: new AMap.Pixel(0, 0),
    });
    // 住房建设和交通运输局
    const swj = new AMap.Icon({
      size: new AMap.Size(54, 60),
      image: shwIcon,
      imageSize: new AMap.Size(54, 60),
      imageOffset: new AMap.Pixel(0, 0),
    });
    // 文字背景
    const textBg = new AMap.Icon({
      size: new AMap.Size(170, 47),
      image: emergencyIcon,
      imageSize: new AMap.Size(170, 47),
      imageOffset: new AMap.Pixel(0, 0),
    });
    this.emergencyList.forEach((marker: any, index: number) => {
      const m: any = new AMap.Marker({
        map: this.maps,
        icon:
          index === 0 ? zhzfj : index === 1 ? sthjj : index === 2 ? zjj : swj,
        position: [marker.lng, marker.lat],
        offset: new AMap.Pixel(-27, -30), //69.5
        zIndex: 999999,
      });
      this.emergencyMarkerList.push(m);
      this.createLine(
        marker.lng,
        marker.lat,
        marker.type,
        index === 0 ? 1500 : index === 1 ? 800 : index === 2 ? 500 : 1500
      );
      this.emergencyBgMarkerClear = new AMap.Marker({
        map: this.maps,
        icon: textBg,
        zIndex: 999,
        position: [marker.lng, marker.lat],
        offset: new AMap.Pixel(-85, -80),
      });
      this.emergencyTextClear = new AMap.Text({
        text: marker.name,
        anchor: "center", // 设置文本标记锚点
        cursor: "pointer",
        zIndex: 9999,
        style: {
          width: "144px",
          height: "27px",
          "background-color": "transparent",
          "border-width": 0,
          "text-align": "center",
          "font-size": "13px",
          color: "#fff",
        },
        position: [marker.lng, marker.lat],
        offset: new AMap.Pixel(0, -55),
      });
      this.emergencyTextClear.setMap(this.maps);
    });
  }
  // 添加了应急指挥的线
  private createLine(longitude: any, latitude: any, type: any, height: any) {
    const map = this.maps;
    function pointOnCubicBezier(cp: any, t: any) {
      let ax: any, bx: any, cx: any;
      let ay: any, by: any, cy: any;
      let tSquared: any, tCubed: any;
      // eslint-disable-next-line prefer-const
      cx = 3.0 * (cp[1].lng - cp[0].lng);
      // eslint-disable-next-line prefer-const
      bx = 3.0 * (cp[2].lng - cp[1].lng) - cx;
      // eslint-disable-next-line prefer-const
      ax = cp[3].lng - cp[0].lng - cx - bx;

      // eslint-disable-next-line prefer-const
      cy = 3.0 * (cp[1].lat - cp[0].lat);
      // eslint-disable-next-line prefer-const
      by = 3.0 * (cp[2].lat - cp[1].lat) - cy;
      // eslint-disable-next-line prefer-const
      ay = cp[3].lat - cp[0].lat - cy - by;

      // eslint-disable-next-line prefer-const
      tSquared = t * t;
      // eslint-disable-next-line prefer-const
      tCubed = tSquared * t;

      const lng = ax * tCubed + bx * tSquared + cx * t + cp[0].lng;
      const lat = ay * tCubed + by * tSquared + cy * t + cp[0].lat;

      return new AMap.LngLat(lng, lat);
    }

    function computeBezier(points: any, numberOfPoints: any) {
      let dt;
      let i;
      const curve = [];

      // eslint-disable-next-line prefer-const
      dt = 1.0 / (numberOfPoints - 1);

      for (i = 0; i < numberOfPoints; i++) {
        curve[i] = pointOnCubicBezier(points, i * dt);
      }
      return curve;
    }
    function getEllipseHeight(count: any, maxHeight: any, minHeight: any) {
      const height = [];
      const radionUnit = Math.PI / 180;

      for (let i = 0; i < count; i++) {
        const radion = i * radionUnit;
        height.push(minHeight + Math.sin(radion) * maxHeight);
      }
      return height;
    }
    const points = [
      new AMap.LngLat(longitude, latitude),
      new AMap.LngLat(longitude, latitude),
      new AMap.LngLat(104.052269, 30.691354),
      new AMap.LngLat(104.052269, 30.691354),
    ];
    const object3Dlayer = new AMap.Object3DLayer();
    const meshLine = new AMap.Object3D.MeshLine({
      path: computeBezier(points, 180),
      height: getEllipseHeight(180, height, 0),
      color: type ? "#24FFA4" : "#EA160B", //#EA160B
      width: 7,
    });

    meshLine.transparent = true;
    object3Dlayer.add(meshLine);
    meshLine["backOrFront"] = "both";
    map.add(object3Dlayer);
  }
  private TaskList: any = [];
  // 添加任务调度marker
  private createTask() {
    if (this.TaskList.length != 0) {
      this.maps.remove(this.TaskList);
      this.TaskList = [];
    }
    const setSiteIcon = new AMap.Icon({
      // 图标尺寸
      size: new AMap.Size(60, 60),
      // 图标的取图地址
      image: taskImg,
      // 图标所用图片大小
      imageSize: new AMap.Size(60, 60),
      // 图标取图偏移量
      imageOffset: new AMap.Pixel(0, 0),
    });
    this.taskMarkerList.forEach((marker: any, index: number) => {
      const m: any = new AMap.Marker({
        map: this.maps,
        icon: setSiteIcon,
        position: [marker.lng, marker.lat],
        offset: new AMap.Pixel(-30, -30),
        // data: item,
        zIndex: 999999,
      });
      this.TaskList.push(m);
    });
  }
  // 联动执法
  private clearJointEnforcementMarker: any[] = [];
  private createJointEnforcement() {
    const map = this.maps;
    if (this.clearJointEnforcementMarker.length > 0) {
      map.remove(this.clearJointEnforcementMarker);
      this.clearJointEnforcementMarker = [];
    }
    // 巡岗未选中
    const xungang = new AMap.Icon({
      size: new AMap.Size(34, 30),
      image: xungangIcon,
      imageSize: new AMap.Size(34, 30),
      imageOffset: new AMap.Pixel(0, 0),
    });
    // 巡岗选中
    const xungangActive = new AMap.Icon({
      size: new AMap.Size(34, 30),
      image: xungangActiveIcon,
      imageSize: new AMap.Size(34, 30),
      imageOffset: new AMap.Pixel(0, 0),
    });

    // 建筑未选中
    const jianzhu = new AMap.Icon({
      // size: new AMap.Size(76, 48),
      size: new AMap.Size(35, 31),
      image: jianzhuIcon,
      imageSize: new AMap.Size(35, 31),
      imageOffset: new AMap.Pixel(0, 0),
    });
    // 建筑选中
    const jianzhuActive = new AMap.Icon({
      size: new AMap.Size(35, 31),
      image: jianzhuActiveIcon,
      imageSize: new AMap.Size(35, 31),
      imageOffset: new AMap.Pixel(0, 0),
    });

    // 水站未选中
    const shuizhan = new AMap.Icon({
      // size: new AMap.Size(76, 48),
      size: new AMap.Size(34, 30),
      image: shuizhanIcon,
      imageSize: new AMap.Size(34, 30),
      imageOffset: new AMap.Pixel(0, 0),
    });
    // 水站选中
    const shuizhanActive = new AMap.Icon({
      size: new AMap.Size(34, 30),
      image: shuizhanActiveIcon,
      imageSize: new AMap.Size(34, 30),
      imageOffset: new AMap.Pixel(0, 0),
    });

    // 大气未选中
    const daqi = new AMap.Icon({
      size: new AMap.Size(46, 30),
      image: daqiIcon,
      imageSize: new AMap.Size(46, 30),
      imageOffset: new AMap.Pixel(0, 0),
    });
    // 大气选中
    const daqiActive = new AMap.Icon({
      size: new AMap.Size(46, 30),
      image: daqiActiveIcon,
      imageSize: new AMap.Size(46, 30),
      imageOffset: new AMap.Pixel(0, 0),
    });
    this.jointEnforcementMarker.forEach((marker: any) => {
      let m: any = null;
      if (marker.type === 1) {
        if (this.jointEnforcementValue === marker.id) {
          // 水站选中
          m = new AMap.Marker({
            map: map,
            icon: shuizhanActive,
            position: [marker.longitude, marker.latitude],
            offset: new AMap.Pixel(-17, -15),
            data: marker,
            zIndex: 99999,
          });
        } else {
          // 水站未选中
          m = new AMap.Marker({
            map: map,
            icon: shuizhan,
            position: [marker.longitude, marker.latitude],
            offset: new AMap.Pixel(-17, -15),
            data: marker,
            zIndex: 99999,
          });
        }
      } else if (marker.type === 2) {
        if (this.jointEnforcementValue === marker.id) {
          // 大气选中
          m = new AMap.Marker({
            map: map,
            icon: daqiActive,
            position: [marker.longitude, marker.latitude],
            offset: new AMap.Pixel(-23, -15),
            data: marker,
            zIndex: 99999,
          });
        } else {
          // 大气未选中
          m = new AMap.Marker({
            map: map,
            icon: daqi,
            position: [marker.longitude, marker.latitude],
            offset: new AMap.Pixel(-23, -15),
            data: marker,
            zIndex: 99999,
          });
        }
      } else if (marker.type === 3) {
        if (this.jointEnforcementValue === marker.id) {
          // 重点污染源选中
          m = new AMap.Marker({
            map: map,
            icon: jianzhuActive,
            position: [marker.longitude, marker.latitude],
            offset: new AMap.Pixel(-17.5, -15.5),
            data: marker,
            zIndex: 99999,
          });
        } else {
          // 重点污染源未选中
          m = new AMap.Marker({
            map: map,
            icon: jianzhu,
            position: [marker.longitude, marker.latitude],
            offset: new AMap.Pixel(-17.5, -15.5),
            data: marker,
            zIndex: 99999,
          });
        }
      } else if (marker.type === 4) {
        if (this.jointEnforcementValue === marker.id) {
          // 巡岗选中
          m = new AMap.Marker({
            map: map,
            icon: xungangActive,
            position: [marker.longitude, marker.latitude],
            offset: new AMap.Pixel(-17, -15),
            data: marker,
            zIndex: 99999,
          });
        } else {
          // 巡岗未选中
          m = new AMap.Marker({
            map: map,
            icon: xungang,
            position: [marker.longitude, marker.latitude],
            offset: new AMap.Pixel(-17, -15),
            data: marker,
            zIndex: 99999,
          });
        }
      }
      if (m) {
        this.clearJointEnforcementMarker.push(m);
        AMap.event.addListener(m, "click", this.jointEnforcementClick);
      }
    });
    this.jointEnforcementValue = null;
  }
  private jointEnforcementValue: any = null;
  private taskMarkerClear: any = null;
  private taskTextClear: any = null;
  private jointEnforcementClick(data: any) {
    if (this.carItem) {
      this.clearCarSelected();
    }
    // console.log(data.target.w.data);
    if (this.taskMarkerClear !== null) {
      this.maps.remove(this.taskMarkerClear);
      this.maps.remove(this.taskTextClear);
      this.taskMarkerClear = null;
      this.taskTextClear = null;
    }
    this.jointEnforcementValue = data.target.w.data.id;
    this.createJointEnforcement();
    console.log(this.jointEnforcementValue);
    const taskbg = new AMap.Icon({
      size: new AMap.Size(168, 27),
      image: taskBg,
      imageSize: new AMap.Size(168, 27),
      imageOffset: new AMap.Pixel(0, 0),
    });
    this.taskMarkerClear = new AMap.Marker({
      map: this.maps,
      icon: taskbg,
      zIndex: 999991,
      position: [data.target.w.data.longitude, data.target.w.data.latitude],
      offset: new AMap.Pixel(-84, 25),
    });
    if (data.target.w.data.address) {
      data.target.w.data.address =
        data.target.w.data.address.indexOf("四川省") === 0
          ? data.target.w.data.address.replace("四川省", "")
          : data.target.w.data.address;
    }
    this.taskTextClear = new AMap.Text({
      text: data.target.w.data.address,
      anchor: "center", // 设置文本标记锚点
      cursor: "pointer",
      zIndex: 999992,
      style: {
        width: "168px",
        height: "27px",
        "background-color": "transparent",
        "border-width": 0,
        "text-align": "center",
        "font-size": "14px",
        color: "#fff",
      },
      position: [data.target.w.data.longitude, data.target.w.data.latitude],
      offset: new AMap.Pixel(0, 40),
    });
    this.taskTextClear.setMap(this.maps);
    if (data.target.w.data.type === 4) {
      //@ts-ignore
      // eslint-disable-next-line no-undef
      _this.$bus.emit("checkType", {
        type: "patrol",
        id: data.target.w.data.id,
        taskId: data.target.w.data.taskId,
        bottomDivType: "site",
      });
    } else {
      //@ts-ignore
      // eslint-disable-next-line no-undef
      _this.$bus.emit("checkType", {
        type: "site",
        id: data.target.w.data.id,
        taskId: data.target.w.data.taskId,
        bottomDivType: "site",
      });
    }
  }
  // 垃圾运输站
  private clearRefuseStationMarker: any[] = [];
  private refuseStationId: any = null;
  private creatRefuseStation() {
    const map = this.maps;
    if (
      this.clearRefuseStationMarker &&
      this.clearRefuseStationMarker.length > 0
    ) {
      map.remove(this.clearRefuseStationMarker);
      this.clearRefuseStationMarker = [];
    }
    // 未选中
    const ljysz = new AMap.Icon({
      size: new AMap.Size(22, 18),
      image: ljyszIcon,
      imageSize: new AMap.Size(22, 18),
      imageOffset: new AMap.Pixel(0, 0),
    });
    // 选中
    const ljyszActive = new AMap.Icon({
      size: new AMap.Size(32, 32),
      image: ljyszActiveIcon,
      imageSize: new AMap.Size(32, 32),
      imageOffset: new AMap.Pixel(0, 0),
    });
    this.refuseStationList.forEach((marker: any) => {
      let m: any = null;
      if (marker.refuseStationId == this.refuseStationId) {
        // 选中
        m = new AMap.Marker({
          map: map,
          icon: ljyszActive,
          position: [marker.longitude, marker.latitude],
          offset: new AMap.Pixel(-16, -16),
          data: marker,
          zIndex: 9999999,
        });
      } else {
        // 未选中
        m = new AMap.Marker({
          map: map,
          icon: ljysz,
          position: [marker.longitude, marker.latitude],
          offset: new AMap.Pixel(-11, -9),
          data: marker,
          zIndex: 9999999,
        });
      }
      this.clearRefuseStationMarker.push(m);
      AMap.event.addListener(m, "click", this.refuseStationMarkerClick);
    });
  }
  private refuseText: any = null;
  private refuseMarker: any = null;
  private refuseStationMarkerClick(data: any) {
    const map = this.maps;
    const detail = data.target.w.data;
    this.refuseStationId = detail.refuseStationId;
    this.creatRefuseStation();
    if (this.refuseText !== null) {
      map.remove(this.refuseText);
      map.remove(this.refuseMarker);
      this.refuseText = null;
      this.refuseMarker = null;
    }

    const enterprise = new AMap.Icon({
      size: new AMap.Size(160, 27),
      image: mc,
      imageSize: new AMap.Size(160, 27),
      imageOffset: new AMap.Pixel(0, 0),
    });
    this.refuseMarker = new AMap.Marker({
      map: map,
      icon: enterprise,
      zIndex: 999,
      position: [detail.longitude, detail.latitude],
      offset: new AMap.Pixel(-80, 15),
    });
    this.refuseText = new AMap.Text({
      text: detail.refuseStationName,
      anchor: "center", // 设置文本标记锚点
      cursor: "pointer",
      zIndex: 9999,
      style: {
        width: "160px",
        height: "27px",
        "background-color": "transparent",
        "border-width": 0,
        "text-align": "center",
        "font-size": "14px",
        color: "#fff",
      },
      position: [detail.longitude, detail.latitude],
      offset: new AMap.Pixel(0, 30),
    });
    this.refuseText.setMap(map);
  }
  // 添加应急指挥点
  private clearEmergencyStationListMarker: any[] = [];
  private emergencyAirText: any = [];
  private emergencyAirMarker: any = null;
  private createEmergencyStationList() {
    const map = this.maps;
    if (this.clearEmergencyStationListMarker.length > 0) {
      map.remove(this.clearEmergencyStationListMarker);
      this.clearEmergencyStationListMarker = [];
    }
    if (this.emergencyAirText.length > 0) {
      map.remove(this.emergencyAirText);
      this.emergencyAirText = [];
    }
    if (this.emergencyAirMarker !== null) {
      map.remove(this.emergencyAirMarker);
      this.emergencyAirMarker = null;
    }
    // 建筑未选中
    const jianzhu = new AMap.Icon({
      size: new AMap.Size(31, 31),
      image: weichaobiao,
      imageSize: new AMap.Size(31, 31),
      imageOffset: new AMap.Pixel(0, 0),
    });
    // 建筑选中
    const jianzhuActive = new AMap.Icon({
      size: new AMap.Size(31, 31),
      image: weichaobiaoActive,
      imageSize: new AMap.Size(31, 31),
      imageOffset: new AMap.Pixel(0, 0),
    });

    // 水站未选中
    const shuizhan = new AMap.Icon({
      size: new AMap.Size(72, 72),
      image: waterSatationNormal,
      imageSize: new AMap.Size(72, 72),
      imageOffset: new AMap.Pixel(0, 0),
    });
    // 水站选中
    const shuizhanActive = new AMap.Icon({
      size: new AMap.Size(72, 72),
      image: waterSatationActive,
      imageSize: new AMap.Size(72, 72),
      imageOffset: new AMap.Pixel(0, 0),
    });

    // 设置空气Marker-Icon(1、区控站)
    const markerIcon11 = new AMap.Icon({
      size: new AMap.Size(45, 45),
      image: qkz1,
      imageSize: new AMap.Size(45, 45),
      imageOffset: new AMap.Pixel(0, 0),
    });
    const markerIcon12 = new AMap.Icon({
      size: new AMap.Size(45, 45),
      image: qkz2,
      imageSize: new AMap.Size(45, 45),
      imageOffset: new AMap.Pixel(0, 0),
    });
    const markerIcon13 = new AMap.Icon({
      size: new AMap.Size(45, 45),
      image: qkz3,
      imageSize: new AMap.Size(45, 45),
      imageOffset: new AMap.Pixel(0, 0),
    });
    const markerIcon14 = new AMap.Icon({
      size: new AMap.Size(45, 45),
      image: qkz4,
      imageSize: new AMap.Size(45, 45),
      imageOffset: new AMap.Pixel(0, 0),
    });
    const markerIcon15 = new AMap.Icon({
      size: new AMap.Size(45, 45),
      image: qkz5,
      imageSize: new AMap.Size(45, 45),
      imageOffset: new AMap.Pixel(0, 0),
    });
    const markerIcon16 = new AMap.Icon({
      size: new AMap.Size(45, 45),
      image: qkz6,
      imageSize: new AMap.Size(45, 45),
      imageOffset: new AMap.Pixel(0, 0),
    });

    // 设置空气Marker-Icon(2、微控站)
    const markerIcon21 = new AMap.Icon({
      size: new AMap.Size(54, 44),
      image: wkz1,
      imageSize: new AMap.Size(54, 44),
      imageOffset: new AMap.Pixel(0, 0),
    });
    const markerIcon22 = new AMap.Icon({
      size: new AMap.Size(54, 44),
      image: wkz2,
      imageSize: new AMap.Size(54, 44),
      imageOffset: new AMap.Pixel(0, 0),
    });
    const markerIcon23 = new AMap.Icon({
      size: new AMap.Size(54, 44),
      image: wkz3,
      imageSize: new AMap.Size(54, 44),
      imageOffset: new AMap.Pixel(0, 0),
    });
    const markerIcon24 = new AMap.Icon({
      size: new AMap.Size(54, 44),
      image: wkz4,
      imageSize: new AMap.Size(54, 44),
      imageOffset: new AMap.Pixel(0, 0),
    });
    const markerIcon25 = new AMap.Icon({
      size: new AMap.Size(54, 44),
      image: wkz5,
      imageSize: new AMap.Size(54, 44),
      imageOffset: new AMap.Pixel(0, 0),
    });
    const markerIcon26 = new AMap.Icon({
      size: new AMap.Size(54, 44),
      image: wkz6,
      imageSize: new AMap.Size(54, 44),
      imageOffset: new AMap.Pixel(0, 0),
    });

    // 设置空气Marker-Icon(3、市控站)
    const markerIcon31 = new AMap.Icon({
      size: new AMap.Size(51, 37),
      image: skz1,
      imageSize: new AMap.Size(51, 37),
      imageOffset: new AMap.Pixel(0, 0),
    });
    const markerIcon32 = new AMap.Icon({
      size: new AMap.Size(51, 37),
      image: skz2,
      imageSize: new AMap.Size(51, 37),
      imageOffset: new AMap.Pixel(0, 0),
    });
    const markerIcon33 = new AMap.Icon({
      size: new AMap.Size(51, 37),
      image: skz3,
      imageSize: new AMap.Size(51, 37),
      imageOffset: new AMap.Pixel(0, 0),
    });
    const markerIcon34 = new AMap.Icon({
      size: new AMap.Size(51, 37),
      image: skz4,
      imageSize: new AMap.Size(51, 37),
      imageOffset: new AMap.Pixel(0, 0),
    });
    const markerIcon35 = new AMap.Icon({
      size: new AMap.Size(51, 37),
      image: skz5,
      imageSize: new AMap.Size(51, 37),
      imageOffset: new AMap.Pixel(0, 0),
    });
    const markerIcon36 = new AMap.Icon({
      size: new AMap.Size(51, 37),
      image: skz6,
      imageSize: new AMap.Size(51, 37),
      imageOffset: new AMap.Pixel(0, 0),
    });
    // 设置空气Marker-Icon(1、区控站)离线
    const markerIcon11Hui = new AMap.Icon({
      size: new AMap.Size(45, 45),
      image: qkzHui,
      imageSize: new AMap.Size(45, 45),
      imageOffset: new AMap.Pixel(0, 0),
    });

    // 设置空气Marker-Icon(2、微控站)离线
    const markerIcon21Hui = new AMap.Icon({
      size: new AMap.Size(54, 44),
      image: wkzHui,
      imageSize: new AMap.Size(54, 44),
      imageOffset: new AMap.Pixel(0, 0),
    });

    // 设置空气Marker-Icon(3、市控站)离线
    const markerIcon31Hui = new AMap.Icon({
      size: new AMap.Size(51, 37),
      image: skzHui,
      imageSize: new AMap.Size(51, 37),
      imageOffset: new AMap.Pixel(0, 0),
    });
    // 选中
    const xuanzhongIcon = new AMap.Icon({
      size: new AMap.Size(35, 35),
      image: xuanzhong,
      imageSize: new AMap.Size(35, 35),
      imageOffset: new AMap.Pixel(0, 0),
    });
    this.emergencyStationList.forEach((marker: any) => {
      let m: any = null;
      if (marker.stationType === 2) {
        if (this.emergencyStationListValue === marker.id) {
          // 水站选中
          m = new AMap.Marker({
            map: map,
            icon: shuizhanActive,
            position: [marker.longitude, marker.latitude],
            offset: new AMap.Pixel(-36, -72),
            data: marker,
            zIndex: 99999,
          });
          m.setLabel({
            offset: new AMap.Pixel(0, 29), //设置文本标注偏移量
            content: `<div>${marker.stationName}</div>`, //设置文本标注内容
            direction: "top", //设置文本标注方位
          });
        } else {
          // 水站未选中
          m = new AMap.Marker({
            map: map,
            icon: shuizhan,
            position: [marker.longitude, marker.latitude],
            offset: new AMap.Pixel(-36, -72),
            data: marker,
            zIndex: 99999,
          });
          m.setLabel({
            offset: new AMap.Pixel(0, 25), //设置文本标注偏移量
            content: `<div>${marker.stationName}</div>`, //设置文本标注内容
            direction: "top", //设置文本标注方位
          });
        }
      } else if (marker.stationType === 1) {
        for (const item of this.emergencyStationList) {
          for (const item1 of this.AQIAirColors) {
            if (item.aqi !== null) {
              if (item.aqi >= item1.min && item.aqi <= item1.max) {
                /* eslint-disable @typescript-eslint/ban-ts-ignore */
                //@ts-ignore
                item.type = item1.type;
                item.textColor = item1.color;
              }
            } else {
              item.type = null;
            }
          }
        }
        if (marker.stationType == 1) {
          // 微控站
          if (marker.type == 1) {
            marker.icon = markerIcon21;
          } else if (marker.type == 2) {
            marker.icon = markerIcon22;
          } else if (marker.type == 3) {
            marker.icon = markerIcon23;
          } else if (marker.type == 4) {
            marker.icon = markerIcon24;
          } else if (marker.type == 5) {
            marker.icon = markerIcon25;
          } else if (marker.type == 6) {
            marker.icon = markerIcon26;
          } else {
            marker.icon = markerIcon21Hui;
          }
          marker.zIndex = 99994;
          marker.offset = new AMap.Pixel(-27, -22);
          marker.textOffset = new AMap.Pixel(0, 8);
        } else if (marker.stationType == 2) {
          // 区控站
          if (marker.type == 1) {
            marker.icon = markerIcon11;
          } else if (marker.type == 2) {
            marker.icon = markerIcon12;
          } else if (marker.type == 3) {
            marker.icon = markerIcon13;
          } else if (marker.type == 4) {
            marker.icon = markerIcon14;
          } else if (marker.type == 5) {
            marker.icon = markerIcon15;
          } else if (marker.type == 6) {
            marker.icon = markerIcon16;
          } else {
            marker.icon = markerIcon11Hui;
          }
          marker.zIndex = 99996;
          marker.offset = new AMap.Pixel(-22.5, -22.5);
          marker.textOffset = new AMap.Pixel(0, 3);
        } else if (marker.stationType == 3) {
          // 市控站
          if (marker.type == 1) {
            marker.icon = markerIcon31;
          } else if (marker.type == 2) {
            marker.icon = markerIcon32;
          } else if (marker.type == 3) {
            marker.icon = markerIcon33;
          } else if (marker.type == 4) {
            marker.icon = markerIcon34;
          } else if (marker.type == 5) {
            marker.icon = markerIcon35;
          } else if (marker.type == 6) {
            marker.icon = markerIcon36;
          } else {
            marker.icon = markerIcon31Hui;
          }
          marker.zIndex = 99998;
          marker.offset = new AMap.Pixel(-25.5, -18.5);
          marker.textOffset = new AMap.Pixel(0, 3);
        }
        if (this.emergencyStationListValue === marker.id) {
          // 水站选中
          m = new AMap.Marker({
            map: map,
            icon: marker.icon,
            zIndex: marker.zIndex,
            position: [marker.longitude, marker.latitude],
            offset: marker.offset,
            data: marker,
          });
          this.emergencyAirMarker = new AMap.Marker({
            map: map,
            icon: xuanzhongIcon,
            zIndex: 9999,
            position: [marker.longitude, marker.latitude],
            offset: marker.offset,
            data: marker,
          });
        } else {
          // 水站未选中
          m = new AMap.Marker({
            map: map,
            icon: marker.icon,
            zIndex: marker.zIndex,
            position: [marker.longitude, marker.latitude],
            offset: marker.offset,
            data: marker,
          });
        }
        // 显示每项指标的数字
        let textColor: any = "";
        if (marker.type == 1) {
          textColor = "#09960A";
        } else if (marker.type == 2) {
          textColor = "#c59c0a";
        } else if (marker.type == 3) {
          textColor = "#ffffff";
        } else if (marker.type == 4) {
          textColor = "#ffffff";
        } else if (marker.type == 5) {
          textColor = "#ffffff";
        } else if (marker.type == 6) {
          textColor = "#ffffff";
        }
        const emergencyAirText = new AMap.Text({
          text: marker.aqi ? marker.aqi : "—",
          anchor: "center", // 设置文本标记锚点
          cursor: "pointer",
          zIndex:
            marker.stationType == 1
              ? 99995
              : marker.stationType == 2
              ? 99997
              : 99999,
          style: {
            width: "144px",
            height: "27px",
            "background-color": "transparent",
            "border-width": 0,
            "text-align": "center",
            "font-size": "12px",
            color: !marker.online
              ? "#a8a8a8"
              : textColor
              ? textColor
              : "#a8a8a8",
          },
          position: [marker.longitude, marker.latitude],
          offset: marker.textOffset,
          data: marker,
        });
        this.emergencyAirText.push(emergencyAirText);
        emergencyAirText.setMap(map);
        AMap.event.addListener(
          emergencyAirText,
          "click",
          this.emergencyStationListClick
        );
      } else if (marker.stationType === 3) {
        if (this.emergencyStationListValue === marker.id) {
          // 重点污染源选中
          m = new AMap.Marker({
            map: map,
            icon: jianzhuActive,
            position: [marker.longitude, marker.latitude],
            offset: new AMap.Pixel(-15.5, -15.5),
            data: marker,
            zIndex: 99999,
          });
        } else {
          // 重点污染源未选中
          m = new AMap.Marker({
            map: map,
            icon: jianzhu,
            position: [marker.longitude, marker.latitude],
            offset: new AMap.Pixel(-15.5, -15.5),
            data: marker,
            zIndex: 99999,
          });
        }
      }
      this.clearEmergencyStationListMarker.push(m);
      AMap.event.addListener(m, "click", this.emergencyStationListClick);
    });
  }
  private emergencyStationListValue: any = null;
  private emergencyStationListClick(data: any) {
    console.log(data, "data");
    this.emergencyStationListValue = data.target.w.data.id;
    //@ts-ignore
    // eslint-disable-next-line no-undef
    _this.$bus.emit("stationType", {
      type: data.target.w.data.stationType,
      stationId: data.target.w.data.stationCode,
    });
    this.createEmergencyStationList();
  }
  // 添加应急指挥距离范围
  private emergencyCircle: any = null;
  private createCircle(lng: any, lat: any, number: any) {
    if (this.emergencyCircle !== null) {
      this.maps.remove(this.emergencyCircle);
    }
    this.emergencyCircle = new AMap.Circle({
      center: new AMap.LngLat(lng, lat), // 圆心位置
      radius: number, // 圆半径
      fillColor: "rgba(251, 246, 97, 0.26)", // 圆形填充颜色
      strokeColor: "transparent", // 描边颜色
      strokeWeight: 0, // 描边宽度
    });
    this.maps.add(this.emergencyCircle);
  }
}
// 跳转空气/水详情
(window as any).toAirDetails = (data: any, type: number) => {
  //@ts-ignore
  // eslint-disable-next-line no-undef
  _this.$router.push({
    //@ts-ignore
    // eslint-disable-next-line no-undef
    path:
      //@ts-ignore
      // eslint-disable-next-line no-undef
      _this.enterType === EnterType.WATER || _this.mapIndex === 1
        ? "/waterDetails"
        : "/airDetails",
    // path: "/details",
    query: {
      data,
      type,
      //@ts-ignore
      // eslint-disable-next-line no-undef
      stationCode: _this.stationCode,
    },
  });
  //@ts-ignore
  // eslint-disable-next-line no-undef
  // if (_this.enterType === EnterType.WATER) {
  //   localStorage.setItem(
  //     "currentRoute",
  //     JSON.stringify({
  //       name: "水环境质量详情页面",
  //       value: "/waterDetails"
  //     })
  //   );
  // } else {
  //   localStorage.setItem(
  //     "currentRoute",
  //     JSON.stringify({
  //       name: "大气环境质量详情页面",
  //       value: "/airDetails"
  //     })
  //   );
  // }
};

// 关闭地图信息弹框
(window as any).closeInfoWindow = () => {
  //@ts-ignore
  // eslint-disable-next-line no-undef
  if (_this.maps) {
    //@ts-ignore
    // eslint-disable-next-line no-undef
    _this.maps.clearInfoWindow();
  }
  //@ts-ignore
  // eslint-disable-next-line no-undef
  if (_this.enterType === 2) {
    //@ts-ignore
    // eslint-disable-next-line no-undef
    _this.currentSelectedStation = null;
    //@ts-ignore
    // eslint-disable-next-line no-undef
    _this.createWaterSationMarker();
    //@ts-ignore
    // eslint-disable-next-line no-undef
  } else if (_this.enterType === 6) {
    //关闭工地弹窗
    //@ts-ignore
    // eslint-disable-next-line no-undef
    _this.$bus.emit("getVideoList", []);
    //@ts-ignore
    // eslint-disable-next-line no-undef
    _this.curMarkerClick = "";
    //@ts-ignore
    // eslint-disable-next-line no-undef
    _this.maps.remove(_this.siteMarkerList);
    //@ts-ignore
    // eslint-disable-next-line no-undef
    _this.siteMarkerList = [];
    //@ts-ignore
    // eslint-disable-next-line no-undef
    _this.setConstructionSiteMarker();
  }
};
</script>
