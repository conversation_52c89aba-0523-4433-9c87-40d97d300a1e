<template>
  <div>
    <div v-if="taskData.length" class="gongditable">
      <div class="table-header">
        <!-- <div>任务编号</div> -->
        <div>任务标题</div>
        <div style="width:8.2%">告警类型</div>
        <div>上报时间</div>
        <div style="width:18.2%">任务内容</div>
        <!-- <div>监测值</div> -->
        <div>发生站点</div>
        <div style="width:22.2%">发生地址</div>
        <div style="width:8.2%">反馈</div>
      </div>
      <!-- <swiper :options="swiperOptionAlert" style="height: 2rem" v-if="taskData.length > 5">
        <swiper-slide
          class="table-body"
          v-for="item in taskData"
          :key="item.taskId"
        >
          <div>{{ item.title }}</div>
          <div style="width:8.2%">{{ item.typeName }}</div>
          <div>{{ item.createTime }}</div>
          <div style="width:18.2%">{{ item.content }}</div>
          <div>{{ item.stationName }}</div>
          <div style="width:22.2%">{{ item.address }}</div>
          <div style="display:flex;justify-content:center;width: 8.2%;">
            <div
              class="statusbg"
              :style="{
                backgroundImage: `url(${item.completeStatusName == '已完成' ? ywc : item.completeStatusName == '进行中' ? jxz : ygb})`,width: '79px'
              }"
            >
              {{ item.completeStatusName }}
            </div>
          </div>
        </swiper-slide>
      </swiper> -->
      <div style="height: 158px;margin-bottom: 60px; overflow-y:auto">
        <div
          class="table-body"
          v-for="item in taskData"
          :key="item.taskId"
          @click="handleClickSlide2(item)"
        >
          <!-- <div :title="item.alarmContent">{{ item.alarmContent }}</div> -->
          <!-- <div>{{ item.taskId }}</div> -->
          <div>{{ item.title }}</div>
          <div style="width:8.2%">{{ item.typeName }}</div>
          <div>{{ item.createTime }}</div>
          <div style="width:18.2%">{{ item.content }}</div>
          <div>{{ item.stationName }}</div>
          <div style="width:22.2%">{{ item.address }}</div>
          <div style="display:flex;justify-content:center;width: 8.2%;">
            <div
              class="statusbg"
              :style="{
                backgroundImage: `url(${item.completeStatusName == '已完成' ? ywc : item.completeStatusName == '进行中' ? jxz : ygb})`,width: '79px'
              }"
            >
              {{ item.completeStatusName }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- <div v-else class="no-data">暂无告警信息</div> -->
  </div>
</template>

<script>
let vm = null
import { Swiper, SwiperSlide } from "vue-awesome-swiper";
export default {
  name: '',
  props: {
    taskData: {
      type: Array,
      default: () => { return []}
    }
  },
  data() {
    return {
        ywc: require('@/assets/department/ywc.png'),
        jxz: require('@/assets/department/jxz.png'),
        ygb: require('@/assets/department/ygb.png'),
        swiperOptionAlert: {
            direction: "vertical",
            slidesPerView: 5,
            slidesPerGroup: 1,
            loop: true,
            autoplay: {
                delay: 3500,
                disableOnInteraction: false
            },
             on: {
                click: function () {
                  // 这里有坑，需要注意的是：this 指向的是 swpier 实例，而不是当前的 vue， 因此借助 vm，来调用 methods 里的方法 
                  // console.log(this); // -> Swiper
                  // 当前活动块的索引，与activeIndex不同的是，在loop模式下不会将 复制的块 的数量计算在内。
                  const clickedIndex = this.clickedIndex;
                  vm.handleClickSlide(clickedIndex);
                }
              }
        }
    }
  },
  created() {
    vm = this
  },
  methods: {
    handleClick(item){
    },
    handleClickSlide(index){
      this.$emit('handleActiveMaker',this.taskData[index])
    },
    handleClickSlide2(data){
      this.$emit('handleActiveMaker',data)
    }
  },
  computed: {},
  watch: {},
  components: {
    Swiper,
    SwiperSlide
  }
}
</script>

<style lang="less" scoped>
.container {
  .gongditable {
    width: 100%;
    .table-header {
      display: flex;
      text-align: center;
      align-items: center;
      height: 0.4rem;
      line-height: 0.4rem;
      color: #3be6ff;
      font-size: 0.16rem;
      background-color: rgba(15, 36, 94, 1);
      > div {
        width: 14.2%;
      }
    }
    .table-body {
      display: flex;
      align-items: center;
      font-size: 0.14rem;
      text-align: center;
      height: 0.4rem;
      line-height: 0.4rem;
      cursor: pointer;
      &:nth-child(2n) {
        background: #0f245e;
      }
      div {
        // &:nth-child(3n + 1) {
        //   padding-left: 0.15rem;
        //   box-sizing: border-box;
        // }
        width: 14.2%;
        text-align: center;
        overflow: hidden; //超出的文本隐藏
        text-overflow: ellipsis; //溢出用省略号显示
        white-space: nowrap; //溢出不换行
      }
    }
    .thead,
    .tbody {
      font-size: 0.14rem;
      text-align: left;
      height: 0.43rem;
      line-height: 0.43rem;
    }
    .tbody {
      .td:nth-of-type(1) {
        width: 48%;
        overflow: hidden; //超出的文本隐藏
        text-overflow: ellipsis; //溢出用省略号显示
        white-space: nowrap; //溢出不换行
        display: flex;
        align-items: center;
      }
      .td:nth-of-type(2) {
        width: 19%;
        display: flex;
        align-items: center;
      }
      .td:nth-of-type(3) {
        width: 0.02rem;
        height: 0.3rem;
        margin-top: 0.05rem;
        background: rgba(0, 234, 255, 1);
        margin-right: 0.05rem;
      }
      .td:nth-of-type(4) {
        width: 30%;
        display: flex;
        align-items: center;
      }
    }
    .tbody:nth-child(odd) {
      background: #082f61;
    }
    .tbody:nth-child(even) {
      // background: #041433;
    }
  }
  .statusbg {
        width: 79px;
        height: 29px;
        text-align: center;
        line-height: 29px;
        background-repeat: no-repeat;
        background-position: center center;
        background-size: cover;
    }
}
.no-data {
  width: 100%;
  text-align: center;
}
</style>
