{"compilerOptions": {"target": "esnext", "module": "esnext", "strict": false, "jsx": "preserve", "importHelpers": true, "moduleResolution": "node", "experimentalDecorators": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "suppressImplicitAnyIndexErrors": true, "sourceMap": true, "skipLibCheck": true, "baseUrl": ".", "types": ["node", "jest", "webpack-env"], "paths": {"@/*": ["src/*"]}, "lib": ["esnext", "dom", "dom.iterable", "scripthost"]}, "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.vue", "tests/**/*.ts", "tests/**/*.tsx", "src/api/emergencyDetails/emergencyEvent.js", "src/api/emergencyDetails/emergencyEvent.js", "src/components/baseMap/buildingModel.js", "src/plugins/swiper.js"], "exclude": ["node_modules"]}