<style lang="less" scoped>
@font-face {
  font-family: 'CAI978';
  src: url('../../assets/font/DS-DIGII.ttf');
  font-display: swap;
}
.quanping {
  position: absolute;
  top: -0.87rem;
  right: -5.5rem;
  transition: 1.5s;
  z-index: 99;
}
.div-transform-right {
  transform: translateX(3.5rem);
  transition: 1.5s;
}
.div-transform-left {
  transform: translateX(-3.9rem);
  transition: 1.5s;
}
.air-types {
  height: 0.24rem;
  display: flex;
  justify-content: space-around;
  align-items: center;
  background-color: #0c275c;
  font-size: 0.12rem;
}
.common-main .middle-part {
  background-color: transparent;
  box-shadow: none;
}
.common-main {
  padding: 0;
  .left-part {
    padding-right: 0.1rem;
    padding-left: 0.6rem;
  }
  .right-part {
    padding-left: 0.1rem;
    padding-right: 0.6rem;
  }
}
// .common-main .middle-center
.middle-map {
  top: auto;
  // margin-top: 0.1rem;
  height: calc(1080px - 1rem);
  position: absolute;
  width: 1920px;
  // margin-left: -1.25rem;
  z-index: 1;
  .map-wind {
    transition: 1.5s;
    width: 2.6rem;
    height: 1.1rem;
    background: rgba(23, 56, 92, 0.5);
    position: absolute;
    bottom: 0.2rem;
    left: 4.7rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 0.1rem 0;
    > p {
      color: rgba(0, 192, 255, 1);
      margin: 0;
      font-size: 0.18rem;
      margin-left: 1.1rem;
      span {
        margin-left: 0.15rem;
        margin-right: 0.1rem;
        font-size: 0.18rem;
        color: #ffffff;
      }
    }
  }
}
.middle-center {
  margin: 0 auto;
  .middle-part {
    padding-left: 0.9rem;
    height: 1.3rem;
    display: flex;
    > div {
      width: 1.3rem;
      position: relative;
      text-align: center;
      > img {
        width: 100%;
        height: 1.3rem;
        position: absolute;
        top: 0;
        left: 0;
        z-index: -1;
      }
      > .air-name {
        font-size: 0.18rem;
        font-family: Adobe Heiti Std;
        line-height: 0.45rem;
      }
      > .air-number {
        font-size: 0.35rem;
        font-family: CAI978;
      }
    }
  }
}
.main-content {
  display: flex;
  height: 100%;
  justify-content: space-between;
  > div {
    width: 11%;
    background-color: #14407e;
  }
  .line-one {
    display: flex;
    justify-content: space-between;
    font-size: 0.22rem;
    font-family: PingFang SC;
    font-weight: 500;
    color: rgba(255, 255, 255, 1);
  }
  .line-two {
    height: 0.07rem;
    background-color: #14407e;
    border-radius: 0.12rem;
    > div {
      height: 0.07rem;
      border-radius: 0.12rem;
    }
  }
}
.monitor {
  display: flex;
  color: #e3ebf5;
  padding: 0.12rem;
  height: 0.6rem;
  align-items: center;
  .spot {
    width: 0.08rem;
    height: 0.08rem;
    background: rgba(242, 83, 22, 1);
    border-radius: 50%;
    margin-right: 0.12rem;
  }
  .line {
    width: 1px;
    height: 0.48rem;
    background: rgba(0, 234, 255, 1);
    margin-right: 0.12rem;
    margin-left: 0.12rem;
  }
}
.middle-center .middle-part .middle-content .middle-content-right {
  padding: 0.3rem 0.5rem 0.3rem 0;
}
.monitor:nth-child(odd) {
  background: rgba(15, 36, 94, 1);
}
.monitor:nth-child(even) {
  background: rgba(4, 20, 51, 1);
}
.common-content > div {
  font-size: 0.16rem;
  text-align: center;
}
.table-data {
  width: 100%;
  .table-data-thead {
    .tr {
      display: flex;
      justify-content: space-between;
      .th {
        background: transparent !important;
        color: #ffffff;
        padding: 0;
        text-align: center;
        border: none;
        line-height: 0.34rem;
        font-size: 0.14rem;
        height: 0.34rem;
      }
      > :nth-of-type(1) {
        width: 20.33%;
      }
      > :nth-of-type(2) {
        width: 48.33%;
      }
      > :nth-of-type(3) {
        width: 20.33%;
      }
      > :nth-of-type(4) {
        width: 20.33%;
      }
    }
  }
  .table-data-tbody {
    height: calc(2rem - 0.35rem);
    .tr {
      display: flex;
      justify-content: space-between;
      .td {
        color: #ffffff;
        padding: 0;
        font-size: 0.14rem;
        text-align: center;
        height: 0.34rem;
        line-height: 0.34rem;
        border: none;
      }
      > :nth-of-type(1) {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 20.33%;
      }
      > :nth-of-type(2) {
        width: 48.33%;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
      > :nth-of-type(3) {
        width: 20.33%;
      }
      > :nth-of-type(4) {
        width: 20.33%;
      }
    }
  }
  .table-data-tbody .tr:nth-child(odd) {
    background: rgba(5, 47, 97, 1);
  }
  .table-data-tbody .tr:nth-child(even) {
    background: transparent;
  }
  .tdBefore {
    //前三
    width: 0.26rem;
    display: block;
    text-align: center;
    height: 0.2rem;
    line-height: 0.2rem;
    background: rgba(255, 133, 9, 1);
    border-radius: 0.06rem;
    margin: 0 auto;
  }
  .tdAfter {
    //前三外
    width: 0.26rem;
    display: block;
    text-align: center;
    height: 0.2rem;
    line-height: 0.2rem;
    background: rgba(18, 136, 226, 1);
    border-radius: 0.06rem;
    margin: 0 auto;
  }
}
.common-content1 {
  width: 100%;
  height: 6.5rem;
  margin-top: 0.8rem;
  .spans {
    font-size: 0.26rem;
  }
}
.type-radio {
  .water-monitor-tab {
    height: 0.24rem;
    font-size: 0.12rem;
    color: rgba(255, 255, 255, 1);
    background: rgba(12, 39, 92, 1);
    border-radius: 0;
    padding: 0 0.1rem;
    border: none;
  }
  .ant-radio-group-solid .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
    background: #0084ff;
    border: none;
  }
  .ant-radio-button-wrapper:not(:first-child)::before {
    background: transparent;
  }
  .ant-radio-button-wrapper-checked::before {
    background: transparent !important;
  }
  .ant-radio-button-wrapper-checked {
    z-index: 1;
    border-color: #0084ff !important;
    -webkit-box-shadow: -1px 0 0 0 #0084ff;
    box-shadow: -1px 0 0 0 #0084ff;
  }
  .ant-radio-group-small .ant-radio-button-wrapper {
    // width: 14.28%;
    width: 16.66%;
    text-align: center;
  }
  .ant-radio-group {
    display: flex;
  }
  .ant-radio-group-small .ant-radio-button-wrapper {
    padding: 0;
  }
}
.air-state-main {
  width: 1.65rem;
  height: 2.02rem;
  transition: 1.5s;
  // background: rgba(23, 56, 92, 0.5);
  position: absolute;
  bottom: 0rem;
  right: -1.65rem;
  z-index: 100;
  > img {
    position: absolute;
    height: 1.6rem;
    left: 0.35rem;
    top: 0.25rem;
  }
  > div {
    display: flex;
    align-items: center;
    margin-top: 0.06rem;
  }
  padding: 0.2rem 0 0 0.15rem;
}
.air-station-main {
  transition: 1.5s;
  width: 1.6rem;
  height: 1.3rem;
  // background: rgba(23, 56, 92, 0.5);
  position: absolute;
  bottom: 1.8rem;
  right: -1.5rem;
  z-index: 100;
  .gou {
    width: 0.16rem;
    margin-right: 0.1rem;
    height: 0.16rem;
  }
  .list-air {
    display: flex;
  }
  > div {
    display: inline-block;
    align-items: center;
    margin-top: 0.06rem;
    > input {
      margin-right: 0.1rem;
      width: 0.16rem;
      height: 0.16rem;
    }
    > :nth-of-type(2) {
      width: 0.5rem;
      padding-left: 8px;
    }
  }
  padding: 0.05rem 0 0 0.25rem;
}
.div-hide-left {
  transform: scale(0); // rotate(360deg)
  transition: 1.5s;
}
.div-hide-right {
  transform: scale(0); // rotate(360deg)
  transition: 1.5s;
}
.conner-left-top {
  position: absolute;
  top: -2px;
  left: -2px;
  width: 0.1rem;
  height: 0.1rem;
  border-top: 2px solid #00eaff;
  border-left: 2px solid #00eaff;
}
.conner-right-top {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 0.1rem;
  height: 0.1rem;
  border-top: 2px solid #00eaff;
  border-right: 2px solid #00eaff;
}
.conner-left-bot {
  position: absolute;
  left: -2px;
  bottom: -2px;
  width: 0.1rem;
  height: 0.1rem;
  border-bottom: 2px solid #00eaff;
  border-left: 2px solid #00eaff;
}
.conner-right-bot {
  position: absolute;
  right: -2px;
  bottom: -2px;
  width: 0.1rem;
  height: 0.1rem;
  border-right: 2px solid #00eaff;
  border-bottom: 2px solid #00eaff;
}
.common-title {
  position: relative;
}
.check-air-day {
  display: flex;
  width: 100%;
  align-items: center;
  white-space: nowrap;
}
.box-color {
  position: absolute;
  width: 0.93rem;
  height: 1.19rem;
  top: 0.06rem;
  left: 0.05rem;
  box-shadow: 0px 0px 0.2rem 0px rgba(14, 252, 255, 1);
}
.box-color1 {
  position: absolute;
  width: 1.2rem;
  height: 1.19rem;
  top: 0.06rem;
  left: 0.05rem;
  box-shadow: 0px 0px 0.2rem 0px rgba(14, 252, 255, 1);
}
.title-flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
  > div {
    display: flex;
    font-size: 0.14rem;
    font-weight: 400;

    > div {
      width: 0.8rem;
      height: 0.28rem;
      line-height: 0.3rem;
      color: #0e8bff;
      // background: rgba(14, 139, 255, 0.32);
      // border: 1px solid rgba(14, 139, 255, 1);
      text-align: center;
      // clip-path: polygon(100% 0, 100% 100%, 12% 100%, 0% 70%, 0 0);
      background: url(../../assets/<EMAIL>);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      cursor: pointer;
    }
    div.type-active {
      background: url(../../assets/<EMAIL>);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      color: rgba(0, 234, 255, 1);
    }
    :nth-of-type(2) {
      margin-left: 0.1rem;
    }
  }
}
.left-part {
  background-image: url(../../assets/air_bg_left.png) !important;
  background-size: 100% 100% !important;
  padding-left: 0 !important;
  .common-title {
    padding-left: 0.6rem;
    // background-image: url(../../assets/air-ng-mb-left.png) !important;
    // background-size: 100% 100% !important;
  }
}
.right-part {
  padding-right: 0 !important;
  background-image: url(../../assets/air_bg.png) !important;
  background-size: 100% 100% !important;
  .common-title {
    padding-right: 0.6rem;
    // background-image: url(../../assets/air-ng-mb.png) !important;
    // background-size: 100% 100% !important;
  }
}
.wind-direction {
  position: absolute;
  top: 1.5rem;
  left: -1.5rem;
  z-index: 9;
}
.check-air-table {
  .check-air-th {
    width: 100%;
    height: 0.24rem;
    line-height: 0.24rem;
    background: rgba(23, 66, 190, 1);
    margin-bottom: 0.05rem;
  }
  .check-air-tr {
    > :nth-of-type(odd) {
      height: 0.34rem;
      line-height: 0.34rem;
      background: rgba(21, 44, 110, 1);
    }
    > :nth-of-type(even) {
      height: 0.34rem;
      line-height: 0.34rem;
      background: rgba(3, 20, 59, 1);
    }
  }
  .check-air-th,
  .check-air-tr {
    display: flex;
    justify-content: space-between;
    > div {
      width: 25%;
      font-size: 0.14rem;
    }
  }
}
.left-part-three {
  .grade {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 0.1rem;
    box-sizing: border-box;
    .list {
      display: flex;
      align-items: center;
      .text {
        font-size: 0.14rem;
        font-weight: 400;
        color: #e6f0ff;
        margin-left: 0.06rem;
      }
      .block-out {
        width: 12px;
        height: 8px;
        background: rgba(220, 240, 255, 0);
        border: 1px solid #00ff00;
        border-radius: 2px;
        display: flex;
        align-items: center;
        justify-content: center;
        .block-in {
          width: 8px;
          height: 4px;
          background: #00ff00;
          border-radius: 2px;
        }
      }
      &:nth-child(2) {
        .block-out {
          background: rgba(220, 240, 255, 0);
          border: 1px solid #ffff00;
          .block-in {
            background: #ffff00;
          }
        }
      }
      &:nth-child(3) {
        .block-out {
          background: rgba(220, 240, 255, 0);
          border: 1px solid #ff7e00;
          .block-in {
            background: #ff7e00;
          }
        }
      }
      &:nth-child(4) {
        .block-out {
          background: rgba(220, 240, 255, 0);
          border: 1px solid #ff0000;
          .block-in {
            background: #ff0000;
          }
        }
      }
      &:nth-child(5) {
        .block-out {
          background: rgba(220, 240, 255, 0);
          border: 1px solid #99004c;
          .block-in {
            background: #99004c;
          }
        }
      }
      &:nth-child(6) {
        .block-out {
          background: rgba(220, 240, 255, 0);
          border: 1px solid #7e0023;
          .block-in {
            background: #7e0023;
          }
        }
      }
    }
  }
  .date {
    margin-top: 0.15rem;
    height: 1.9rem;
    /*padding: 0.09rem 0.09rem 0.09rem 0.19rem ;*/
    box-sizing: border-box;
    /*background: rgba(2,34,99, .6);*/
    .week {
      display: flex;
      align-items: center;
      justify-content: space-around;
      padding: 0 0.06rem;
      span {
        display: inline-block;
        justify-content: space-around;
        font-size: 0.13rem;
        font-weight: 500;
        color: #ffffff;
        background-color: #1742be;
        width: 0.5rem;
        height: 0.35rem;
        box-sizing: border-box;
        text-align: center;
        line-height: 0.35rem;
        border: 0.6px solid rgba(11, 141, 211, 0.6);
        border-right: none;
        &:last-child {
          border-right: 1px solid rgba(11, 141, 211, 0.6);
        }
      }
    }
    .days {
      padding: 0 0.06rem;
      > div {
        display: flex;
        align-items: center;
        justify-content: space-around;
        /*margin-top: 0.03rem;*/
        /*&:nth-child(1) {*/
        /*  margin-top: 0.05rem;*/
        /*}*/
      }
      span {
        display: inline-block;
        justify-content: space-around;
        font-size: 0.15rem;
        width: 0.5rem;
        height: 0.29rem;
        box-sizing: border-box;
        text-align: center;
        line-height: 0.29rem;
        font-weight: 500;
        color: #ffffff;
        border: 0.6px solid rgba(11, 141, 211, 0.6);
        border-right: none;
        border-top: none;
        &:last-child {
          border-right: 1px solid rgba(11, 141, 211, 0.6);
        }
      }
    }
  }
}
</style>
<style lang="less">
.alert-model {
  .ant-modal-content {
    .ant-modal-close {
      top: 0.4rem !important;
      right: 0.4rem !important;
      svg {
        font-size: 0.3rem !important;
        color: #fff;
      }
    }
    .ant-modal-close-x {
      background: RGBA(12, 39, 94, 1);
      border-radius: 50%;
      display: flex;
      align-items: center;
      width: 0.42rem;
      height: 0.42rem;
      justify-content: center;
      border: 0.015rem solid #01dbef;
    }
    width: 14.48rem !important;
    height: 8.01rem !important;
    background: url(../../assets/<EMAIL>) !important;
    background-size: 100% 100% !important;
    position: relative;
    .title {
      position: absolute;
      top: 0.1rem;
      font-size: 0.26rem;
      font-family: Source Han Sans CN;
      font-weight: bold;
      color: #ffffff;
      left: 50%;
      margin-left: -1rem;
    }
    .table-data {
      width: 100%;
      margin-top: 0.5rem;
      .table-data-thead {
        .tr {
          display: flex;
          justify-content: space-between;
          .th {
            background: transparent !important;
            color: #ffffff;
            padding: 0;
            text-align: center;
            border: none;
            line-height: 0.58rem;
            font-size: 0.28rem;
            height: 0.58rem;
          }
          > :nth-of-type(1) {
            width: 23.33%;
          }
          > :nth-of-type(2) {
            width: 33.33%;
          }
          > :nth-of-type(3) {
            width: 20%;
          }
          > :nth-of-type(4) {
            width: 23.33%;
          }
        }
      }
      .table-data-tbody {
        height: 5.8rem;
        .tr {
          display: flex;
          justify-content: space-between;
          .td {
            color: #ffffff;
            padding: 0;
            font-size: 0.22rem;
            text-align: center;
            height: 0.58rem;
            line-height: 0.58rem;
            border: none;
          }
          > :nth-of-type(1) {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 23.33%;
          }
          > :nth-of-type(2) {
            width: 33.33%;
          }
          > :nth-of-type(3) {
            width: 20%;
          }
          > :nth-of-type(4) {
            width: 23.33%;
          }
        }
      }
      .table-data-tbody .tr:nth-child(odd) {
        background: rgba(5, 47, 97, 1);
      }
      .table-data-tbody .tr:nth-child(even) {
        background: transparent;
      }
      .tdBefore {
        //前三
        width: 0.4rem;
        display: block;
        text-align: center;
        height: 0.3rem;
        line-height: 0.3rem;
        background: rgba(255, 133, 9, 1);
        border-radius: 0.06rem;
        margin: 0 auto;
      }
      .tdAfter {
        //前三外
        width: 0.4rem;
        display: block;
        text-align: center;
        height: 0.3rem;
        line-height: 0.3rem;
        background: rgba(18, 136, 226, 1);
        border-radius: 0.06rem;
        margin: 0 auto;
      }
    }
  }
}
.ant-tooltip-inner {
  background-color: #f5f5f5;
  color: #333;
}
</style>
<template>
  <!-- 空气质量 -->
  <section class="common-main">
    <!-- 左侧部分background-image: linear-gradient(to right, rgba(0, 0, 0, 0.5), rgba(0, 15, 79, 0.5)); -->
    <section :class="{ 'left-part': true, 'div-hide-left': displayState }" style="margin-top: 0.15rem; transition: 1.5s">
      <div class="common-title left-part-one" style>
        <div class="title">{{ `24小时空气质量` }}</div>
        <div class="sub-title">
          <img src="@/assets/biaoti.png" alt class="title-img" />
        </div>
        <div class="common-content type-radio" style="border: none">
          <a-radio-group v-model="airType" size="small" buttonStyle="solid" style="width: 100%" @change="airTypeChange">
            <a-radio-button
              v-for="item in airTypes"
              :key="item.code"
              :value="item.code"
              :class="{
                'water-monitor-tab': true,
              }"
              >{{ item.name }}</a-radio-button
            >
          </a-radio-group>
          <LineChartDashed :id="'air-trend'" :width="'3.5rem'" :height="'1.75rem'" :propData="airData" :smooth="true" :xText="'现在'"></LineChartDashed>
        </div>
      </div>
      <div class="common-title left-part-two" style="">
        <div class="title">空气质量预报</div>
        <div class="sub-title">
          <img src="@/assets/biaoti.png" alt class="title-img" />
        </div>
        <div class="common-content" style="border: none">
          <LineChartColor :id="'air-predict'" :width="'3.5rem'" :height="'2rem'" :propData="qualityData"></LineChartColor>
        </div>
      </div>
      <div class="common-title left-part-three" style>
        <div class="title title-flex">
          <span>{{ `空气质量综合指数` }}</span>
          <div>
            <div :class="{ 'type-active': airQualityType === '同比' }" @click="airQualityType = '同比'">同比</div>
            <div :class="{ 'type-active': airQualityType === '环比' }" @click="airQualityType = '环比'">环比</div>
          </div>
        </div>
        <div class="sub-title" style="margin: 0">
          <img src="@/assets/biaoti.png" alt class="title-img" />
        </div>
        <div class="common-content" style="border: none">
          <LineAndBarChart v-if="airQualityType == '同比'" :id="'air-proportion-1'" :width="'3.5rem'" :height="'2rem'" :LineAndBarData="airContrast" />
          <LineAndBarChartOne v-if="airQualityType == '环比'" :id="'LineAndBarChartOne-2'" :width="'3.5rem'" :height="'2rem'" :propData="airContrast" />
        </div>
      </div>
    </section>
    <!-- 地图部分 -->
    <section class="middle-map">
      <!-- <keep-alive> -->
      <air-map
        :mapZoom="mapZoom"
        :mapStyle="mapStyle"
        :viewCenter="mapViewCenter"
        :enterType="1"
        :mapMarker="mapMarker"
        :typeColor="airTypeNum"
        :mapColor="mapColor"
        :wind="wind"
        :area-data="areaData"
        :radarData="radarData"
        :CameraData="CameraData"
        :areaCodeList="areaCodeList"
        @type="changeType"
      ></air-map>
      <!-- </keep-alive> -->
      <div :class="{ 'map-wind': true, 'div-transform-left': displayState }">
        <p style="display: flex; align-items: center">
          风向<span>{{ todayWeather.windDirectionMark ? todayWeather.windDirectionMark : '-' }}</span>
          <img
            src="@/assets/fengxiang.png"
            alt=""
            v-if="todayWeather.windDirectionMark"
            :style="{
              width: '0.2rem',
              height: '0.2rem',
              'margin-bottom': '0.06rem',
              transform: windDirection,
            }"
          />
        </p>
        <p>
          风级<span>{{ todayWeather.windLevel ? `${todayWeather.windLevel}级` : '-' }}</span>
        </p>
        <!--        <p>-->
        <!--          风速<span>{{-->
        <!--            todayWeather.instantWindSpeed-->
        <!--              ? `${todayWeather.instantWindSpeed}km/h`-->
        <!--              : "-"-->
        <!--          }}</span>-->
        <!--        </p>-->
      </div>
    </section>
    <!-- 中间部分 -->
    <!--  @click="displayState = !displayState" -->
    <section class="middle-center">
      <!-- 顶部类型 -->
      <section class="middle-part" style="width: 7.5rem; padding-left: 0">
        <div style="width: 1.3rem; height: 1.2rem; position: relative" @click="clickSelectAirTypeHandle(0)">
          <img :src="require('@/assets/' + (airTypeNumber[0].selected ? 'aqixz' : 'aqiwxz') + '.png')" alt />
          <AQIChart :id="'aqi-check'" :width="'1.3rem'" :height="'1.15rem'" :propData="aqiCharts" />
          <div class="box-color1" v-if="airTypeNumber[0].selected"></div>
        </div>

        <div
          v-for="(item, index) in airTypeNumber"
          :key="index"
          @click="clickSelectAirTypeHandle(index)"
          style="cursor: pointer; position: relative"
          v-show="index != 0"
        >
          <img :src="require('@/assets/' + (item.selected ? 'air-bg-selected' : 'air-bg') + '.png')" alt />
          <div class="air-name">{{ item.name }}</div>
          <div class="air-number" :style="{ color: item.bgColor }">
            {{ item.number == 0 || !item.number ? '-' : item.number }}
          </div>
          <div style="margin-top: -0.05rem">{{ item.unit }}</div>
          <div class="box-color" v-if="item.selected"></div>
        </div>
      </section>
      <section :class="{ 'air-state-main': true, 'div-transform-right': displayState }">
        <!-- <img src="@/assets/<EMAIL>" alt /> -->
        <div v-for="(item, index) in airPollutionLevel" :key="index">
          <div style="width: 0.22rem; height: 0.06rem; margin-right: 0.1rem" :style="{ background: item.color }"></div>
          <div>{{ item.name }}</div>
        </div>
      </section>
      <section
        :class="{
          'air-station-main': true,
          'div-transform-right': displayState,
        }"
      >
        <div class="list-air">
          <!-- <input type="checkbox" v-model="stationType" value="3" /> -->
          <img src="@/assets/heavily/<EMAIL>" alt="" class="gou" @click="siteTypeChange(4)" v-if="!stationTypeState[3]" />
          <img src="@/assets/gou_active.png" alt="" class="gou" @click="siteTypeChange(4)" v-if="stationTypeState[3]" />
          <div style="display: inline-block">国控</div>
          <div>{{ stationData.gk }}个</div>
          <div style="width: 0.27rem; display: flex; justify-content: center">
            <img src="@/assets/<EMAIL>" alt style="width: 0.27rem" />
          </div>
        </div>
        <div class="list-air">
          <!-- <input type="checkbox" v-model="stationType" value="3" /> -->
          <img src="@/assets/heavily/<EMAIL>" alt="" class="gou" @click="siteTypeChange(3)" v-if="!stationTypeState[2]" />
          <img src="@/assets/gou_active.png" alt="" class="gou" @click="siteTypeChange(3)" v-if="stationTypeState[2]" />
          <div style="display: inline-block">市控</div>
          <div>{{ stationData.sk }}个</div>
          <div style="width: 0.27rem; display: flex; justify-content: center">
            <img src="@/assets/skz1.png" alt style="width: 0.27rem" />
          </div>
        </div>
        <div class="list-air">
          <!-- <input type="checkbox" v-model="stationType" value="1" /> -->
          <img src="@/assets/heavily/<EMAIL>" alt="" class="gou" @click="siteTypeChange(2)" v-if="!stationTypeState[1]" />
          <img src="@/assets/gou_active.png" alt="" class="gou" @click="siteTypeChange(2)" v-if="stationTypeState[1]" />
          <div style="display: inline-block">区控</div>
          <div>{{ stationData.qk }}个</div>
          <div style="width: 0.27rem; display: flex; justify-content: center">
            <img src="@/assets/qkz1.png" alt style="width: 0.27rem" />
          </div>
        </div>
        <div class="list-air">
          <!-- <input type="checkbox" v-model="stationType" value="2" /> -->
          <img src="@/assets/heavily/<EMAIL>" alt="" class="gou" @click="siteTypeChange(1)" v-if="!stationTypeState[0]" />
          <img src="@/assets/gou_active.png" alt="" class="gou" @click="siteTypeChange(1)" v-if="stationTypeState[0]" />
          <div style="display: inline-block">微站</div>
          <div>{{ stationData.wk }}个</div>
          <div style="width: 0.27rem; display: flex; justify-content: center">
            <img src="@/assets/wkz1.png" alt style="width: 0.27rem; height: 0.22rem" />
          </div>
        </div>
      </section>
      <!-- 风向图片 -->
      <section class="wind-direction">
        <!-- <img :src="windDirection" alt="" /> -->
      </section>
      <section @click="fullScreen" :class="{ quanping: true }">
        <img src="@/assets/quanping.png" alt />
      </section>
    </section>
    <!-- 右侧部分background-image: linear-gradient(to left, rgba(0, 0, 0, 0.5), rgba(0, 15, 79, 0.5)); -->
    <section :class="{ 'right-part': true, 'div-hide-right': displayState }" style="transition: 1.5s; margin-top: 0.15rem">
      <!-- 空气质量考核指标 S -->
      <div class="common-title left-part-one" style="height: 3.2rem">
        <div class="title" style="display: flex; justify-content: space-between">
          <div>{{ `空气质量考核指标` }}</div>
          <div style="font-size: 0.14rem; display: flex; align-items: flex-end">{{ airCheckList.now }}截止</div>
        </div>
        <div class="sub-title">
          <img src="@/assets/biaoti.png" alt class="title-img" />
        </div>
        <div class="common-content" style="height: 2.2rem; border: none">
          <!-- <div style="text-align: right;">{{ airCheckList.now }}截止</div> -->
          <div style="display: flex; justify-content: space-between">
            <airPieChart :id="'air-check'" :width="'1.3rem'" :height="'1.8rem'" :PieChartData="aqiDay"></airPieChart>
            <!-- <AirDoublePieQuality
              :id="'AirDoublePieQuality'"
              :width="'1.3rem'"
              :height="'1.3rem'"
              :propData="aqiDay"
            /> -->
            <RotateBarSolid :id="'airCheck3'" :width="'2.2rem'" :height="'1.7rem'" :airData="airCheckList1"></RotateBarSolid>
          </div>
          <div class="check-air-table">
            <div class="check-air-thead">
              <div class="check-air-th">
                <div>指标项</div>
                <div>数值</div>
                <div>同比</div>
                <div>目标</div>
              </div>
            </div>
            <div class="check-air-tbody">
              <div class="check-air-tr">
                <div>优良天数</div>
                <div>{{ airCheckList.thisYearGoodDay }}天</div>
                <div>
                  <img v-if="airCheckList.YearOnYearQuality >= 0" :src="ssaqi" alt style="height: 0.25rem" />
                  <img v-if="airCheckList.YearOnYearQuality < 0" :src="xjaqi" alt style="height: 0.25rem" />
                  <span v-if="airCheckList.YearOnYearQuality >= 0" style="color: rgb(0, 255, 0)">{{ airCheckList.YearOnYearAQI }}天</span
                  ><span v-if="airCheckList.YearOnYearQuality < 0" style="color: red">{{ airCheckList.YearOnYearAQI }}天</span>
                </div>
                <div>{{ airCheckList.thisYearTagDay }}天</div>
              </div>
              <div class="check-air-tr">
                <div>PM₂.₅</div>
                <div>{{ airCheckList.thisYearFineParticulateMatter }}ug/m³</div>
                <div>
                  <img v-if="airCheckList['YearOnYearPM2.5'] >= 0" :src="ss1" alt style="height: 0.25rem" />
                  <img v-if="airCheckList['YearOnYearPM2.5'] < 0" :src="xj1" alt style="height: 0.25rem" />
                  <span v-if="airCheckList['YearOnYearPM2.5'] >= 0" style="color: red"> {{ airCheckList.YearOnYearPM }}% </span>
                  <span v-if="airCheckList['YearOnYearPM2.5'] < 0" style="color: rgb(0, 255, 0)"> {{ airCheckList.YearOnYearPM }}% </span>
                </div>
                <div>{{ airCheckList.thisYearFineParticulateMatterTag }}ug/m³</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 空气质量考核指标 E -->

      <!-- 本月污染日历 S -->
      <div class="common-title left-part-three">
        <div class="title" style="cursor: pointer">
          <!--          @click="monitoringAlarm = true"-->
          {{ `本月污染日历分布` }}
        </div>
        <div class="sub-title">
          <img src="@/assets/biaoti.png" alt class="title-img" />
        </div>
        <!--        <div class="grade">-->
        <!--          <div class="list">-->
        <!--            <div class="block-out">-->
        <!--              <div class="block-in"></div>-->
        <!--            </div>-->
        <!--            <div class="text">优</div>-->
        <!--          </div>-->
        <!--          <div class="list">-->
        <!--            <div class="block-out">-->
        <!--              <div class="block-in"></div>-->
        <!--            </div>-->
        <!--            <div class="text">良</div>-->
        <!--          </div>-->
        <!--          <div class="list">-->
        <!--            <div class="block-out">-->
        <!--              <div class="block-in"></div>-->
        <!--            </div>-->
        <!--            <div class="text">轻度</div>-->
        <!--          </div>-->
        <!--          <div class="list">-->
        <!--            <div class="block-out">-->
        <!--              <div class="block-in"></div>-->
        <!--            </div>-->
        <!--            <div class="text">中度</div>-->
        <!--          </div>-->
        <!--          <div class="list">-->
        <!--            <div class="block-out">-->
        <!--              <div class="block-in"></div>-->
        <!--            </div>-->
        <!--            <div class="text">重度</div>-->
        <!--          </div>-->
        <!--          <div class="list">-->
        <!--            <div class="block-out">-->
        <!--              <div class="block-in"></div>-->
        <!--            </div>-->
        <!--            <div class="text">严重</div>-->
        <!--          </div>-->
        <!--        </div>-->
        <div class="date">
          <div class="week">
            <span>日</span>
            <span>一</span>
            <span>二</span>
            <span>三</span>
            <span>四</span>
            <span>五</span>
            <span>六</span>
          </div>
          <div class="days">
            <div v-for="(item, index) in dateDataFilter" :key="index">
              <a-tooltip
                v-for="(items, indexs) in item"
                :key="indexs"
                :destroyTooltipOnHide="true"
                :style="{
                  color: items.curr ? '#FFFFFF' : '#767D96',
                  backgroundColor: items.curr && items.dates ? color[items.level - 1] : items.curr ? '#0C215E' : '#041942',
                }"
                style="cursor: pointer"
              >
                <template v-if="items.level" slot="title">
                  <div class="notice" style="display: flex; flex-direction: column">
                    <span>{{ items.dates }}</span>
                    <span
                      >AQI:{{ items.aqi }} <span>{{ colorText[items.level - 1] }}</span></span
                    >
                    <span>首要污染物: {{ items.primaryPollutant }}</span>
                  </div>
                </template>
                <span>{{ items.date }}</span>
              </a-tooltip>
            </div>
          </div>
        </div>
      </div>
      <!-- 本月污染日历 E -->

      <!-- 空气质量指数排行 S -->
      <div class="common-title left-part-two" style>
        <div class="title title-flex">
          <span @click="siteRanking = true" style="cursor: pointer">{{ `空气质量指数排行>>` }}</span>
          <div>
            <div :class="{ 'type-active': tableType === 'site' }" @click="tableType = 'site'">按站点</div>
            <div :class="{ 'type-active': tableType === 'street' }" @click="tableType = 'street'">按街道</div>
          </div>
        </div>
        <div class="sub-title" style="margin: 0 auto 0.1rem">
          <img src="@/assets/biaoti.png" alt class="title-img" />
        </div>
        <div class="common-content" style="border: none">
          <div class="table-data" v-if="tableType === 'site'">
            <div class="table-data-thead">
              <div class="tr">
                <div class="th">排行</div>
                <div class="th">站点</div>
                <div class="th">AQI</div>
                <div class="th">等级</div>
              </div>
            </div>
            <swiper :options="swiperOptionAirs" class="table-data-tbody" v-if="airTable.length >= 5">
              <swiper-slide class="tr" v-for="(item, index) in airTable" :key="index">
                <div class="td">
                  <div class="tdAfter">{{ index + 1 }}</div>
                </div>
                <div class="td">{{ item.positionName }}</div>
                <div class="td">
                  {{ item.airQualityIndex ? item.airQualityIndex : '--' }}
                </div>
                <div class="td">{{ item.aqiText ? item.aqiText : '--' }}</div>
              </swiper-slide>
            </swiper>
            <div class="table-data-tbody" v-else>
              <div class="tr" v-for="(item, index) in airTable" :key="index">
                <div class="td">
                  <div class="tdAfter">{{ index + 1 }}</div>
                </div>
                <div class="td" :title="item.positionName">
                  {{ item.positionName }}
                </div>
                <div class="td">
                  {{ item.airQualityIndex ? item.airQualityIndex : '--' }}
                </div>
                <div class="td">{{ item.aqiText ? item.aqiText : '--' }}</div>
              </div>
            </div>
          </div>
          <div class="table-data" v-if="tableType === 'street'">
            <div class="table-data-thead">
              <div class="tr">
                <div class="th">排行</div>
                <div class="th">街道</div>
                <div class="th">AQI</div>
                <div class="th">等级</div>
              </div>
            </div>
            <swiper :options="swiperOptionAirs" class="table-data-tbody" v-if="airTable.length >= 5">
              <swiper-slide class="tr" v-for="(item, index) in airTable" :key="index">
                <div class="td">
                  <!-- <div
                    :class="{
                      tdBefore: index <= 2,
                      tdAfter: index > 2
                    }"
                  >-->
                  <div class="tdAfter">{{ index + 1 }}</div>
                </div>
                <div class="td" :title="item.street">{{ item.street }}</div>
                <div class="td">
                  {{ item.airQualityIndex ? item.airQualityIndex : '--' }}
                </div>
                <div class="td">{{ item.aqiText ? item.aqiText : '--' }}</div>
              </swiper-slide>
            </swiper>
            <div class="table-data-tbody" v-else>
              <div class="tr" v-for="(item, index) in airTable" :key="index">
                <div class="td">
                  <div class="tdAfter">{{ index + 1 }}</div>
                </div>
                <div class="td" :title="item.street">
                  {{ item.street }}
                </div>
                <div class="td">
                  {{ item.airQualityIndex ? item.airQualityIndex : '--' }}
                </div>
                <div class="td">{{ item.aqiText ? item.aqiText : '--' }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 空气质量指数排行 E -->

      <div v-if="false" class="common-title left-part-three">
        <div class="title" style="cursor: pointer" @click="monitoringAlarm = true">
          {{ `监测告警>>` }}
        </div>
        <div class="sub-title">
          本月污染日历分布
          <img src="@/assets/biaoti.png" alt class="title-img" />
        </div>
        <swiper :options="swiperOption" class="common-content" style="border: none">
          <swiper-slide class="monitor" v-for="(item, index) in airWarning" :key="index">
            <div class="spot"></div>
            <div style="flex: 1; text-overflow: ellipsis; overflow: hidden; white-space: nowrap" v-html="item.airAlarmContent"></div>
            <div class="line"></div>
            <div style="width: 1rem; text-align: center" v-html="item.airAlarmTime" />
          </swiper-slide>
        </swiper>
      </div>
    </section>
    <mapControlBoxArea @handleGetSelectArea="handleGetSelectArea"></mapControlBoxArea>
    <a-modal
      v-if="siteRanking"
      v-model="siteRanking"
      :footer="null"
      :destroyOnClose="true"
      width="14.48rem"
      height="8.01rem"
      style="width: 14.48rem; height: 8.01rem"
      class="alert-model"
    >
      <div class="title">空气质量指数排行</div>
      <div class="table-data" v-if="tableType === 'site'">
        <div class="table-data-thead">
          <div class="tr">
            <div class="th">排行</div>
            <div class="th">站点</div>
            <div class="th">AQI</div>
            <div class="th">等级</div>
          </div>
        </div>
        <swiper :options="swiperOptionAlert" class="table-data-tbody" v-if="airTableAlert.length >= 10">
          <swiper-slide class="tr" v-for="(item, index) in airTableAlert" :key="index">
            <div class="td">
              <div class="tdAfter">{{ index + 1 }}</div>
            </div>
            <div class="td">{{ item.positionName }}</div>
            <div class="td">
              {{ item.airQualityIndex ? item.airQualityIndex : '--' }}
            </div>
            <div class="td">{{ item.aqiText ? item.aqiText : '--' }}</div>
          </swiper-slide>
        </swiper>
        <div class="table-data-tbody" v-else>
          <div class="tr" v-for="(item, index) in airTableAlert" :key="index">
            <div class="td">
              <!-- <div
                    :class="{
                      tdBefore: index <= 2,
                      tdAfter: index > 2
                    }"
              >-->
              <div class="tdAfter">{{ index + 1 }}</div>
            </div>
            <div class="td">{{ item.positionName }}</div>
            <div class="td">
              {{ item.airQualityIndex ? item.airQualityIndex : '--' }}
            </div>
            <div class="td">{{ item.aqiText ? item.aqiText : '--' }}</div>
            fix
          </div>
        </div>
      </div>
      <div class="table-data" v-if="tableType === 'street'">
        <div class="table-data-thead">
          <div class="tr">
            <div class="th">排行</div>
            <div class="th">街道</div>
            <div class="th">AQI</div>
            <div class="th">等级</div>
          </div>
        </div>
        <swiper :options="swiperOptionAlert" class="table-data-tbody" v-if="airTableAlert.length >= 10">
          <swiper-slide class="tr" v-for="(item, index) in airTableAlert" :key="index">
            <div class="td">
              <div class="tdAfter">{{ index + 1 }}</div>
            </div>
            <div class="td" :title="item.street">{{ item.street }}</div>
            <div class="td">
              {{ item.airQualityIndex ? item.airQualityIndex : '--' }}
            </div>
            <div class="td">{{ item.aqiText ? item.aqiText : '--' }}</div>
          </swiper-slide>
        </swiper>
        <div class="table-data-tbody" v-else>
          <div class="tr" v-for="(item, index) in airTableAlert" :key="index">
            <div class="td">
              <div class="tdAfter">{{ index + 1 }}</div>
            </div>
            <div class="td" :title="item.street">{{ item.street }}</div>
            <div class="td">
              {{ item.airQualityIndex ? item.airQualityIndex : '--' }}
            </div>
            <div class="td">{{ item.aqiText ? item.aqiText : '--' }}</div>
          </div>
        </div>
      </div>
    </a-modal>
    <!-- monitoringAlarm -->
    <a-modal
      v-if="monitoringAlarm"
      v-model="monitoringAlarm"
      :footer="null"
      :destroyOnClose="true"
      width="14.48rem"
      height="8.01rem"
      style="width: 14.48rem; height: 8.01rem"
      class="alert-model"
    >
      <div class="title" style="margin-left: -0.6rem">监测告警</div>
      <swiper :options="swiperOption1" class="common-content1" style="border: none" v-if="airWarning.length > 10">
        <swiper-slide class="monitor" v-for="(item, index) in airWarning" :key="index" style="padding: 0 1.5rem">
          <div class="spot"></div>
          <div style="flex: 1; text-overflow: ellipsis; overflow: hidden; white-space: nowrap; font-size: 0.22rem" v-html="item.airAlarmContent"></div>
          <div class="line"></div>
          <div style="width: 2rem; text-align: center; font-size: 0.22rem; padding-left: 0.5rem" v-html="item.airAlarmTime"></div>
        </swiper-slide>
      </swiper>
      <div v-else class="common-content1" style="border: none">
        <div class="monitor" v-for="(item, index) in airWarning" :key="index" style="padding: 0 1.5rem">
          <div class="spot"></div>
          <div style="flex: 1; text-overflow: ellipsis; overflow: hidden; white-space: nowrap; font-size: 0.22rem" v-html="item.airAlarmContent"></div>
          <div class="line"></div>
          <div style="width: 2rem; text-align: center; font-size: 0.22rem; padding-left: 0.5rem" v-html="item.airAlarmTime"></div>
        </div>
      </div>
    </a-modal>
  </section>
</template>
<script lang="ts">
interface TaskData {
  type: number
  showNumber: number
  name: string
  number: number
  bgColor?: string
}
interface EchartData {
  // bottomList: string[];
  // dataList: string[];
  [propName: string]: string | number[]
}
interface InData {
  name: string
  dataList: any[]
  colorList: string[]
}
/* eslint-disable @typescript-eslint/ban-ts-ignore */
//@ts-ignore
import AMap from 'AMap'
import { Component, Vue, Watch } from 'vue-property-decorator'
import AirMap from '@/components/GaoDeMap/airMap.vue'
import LineChartDashed from '@/components/Charts/LineChartDashed.vue'
// import LineChartColor from "@/components/Charts/LineChartColor.vue";
import LineChartColor from '@/components/Charts/LineChartColorTest.vue'
import ProportionChart from '@/components/Charts/ProportionChart.vue'
import DashboardChart from '@/components/Charts/DashboardChart.vue'
import AirQualityTop from '@/components/Charts/AirQualityTop.vue'
import airPieChart from '@/components/Charts/airPieChart.vue'
import LineAndBarChart from '@/components/Charts/LineAndBarChart.vue'
import { Swiper, SwiperSlide } from 'vue-awesome-swiper'
import RotateBarSolid from '@/components/Charts/RotateBarSolid.vue'
import DashboardChartAir from '@/components/Charts/DashboardChartAir.vue'
import AirDoublePieQuality from '@/components/Charts/AirDoublePieQuality.vue'
import LineAndBarChartOne from '@/components/Charts/LineAndBarChartOne.vue'
// import DashboardChartAir from "@/components/Charts/DashboardChartAir.vue";
import AQIChart from '@/components/Charts/AQIChart.vue'
import xj from '@/assets/xj.png'
import ss from '@/assets/ss.png'
import ssaqi from '@/assets/ssaqi.png'
import xjaqi from '@/assets/xjaqi.png'
import dong from '@/assets/fengxiang/dong.png'
import xi from '@/assets/fengxiang/xi.png'
import nan from '@/assets/fengxiang/nan.png'
import bei from '@/assets/fengxiang/bei.png'
import dongnan from '@/assets/fengxiang/dongnan.png'
import dongbei from '@/assets/fengxiang/dongbei.png'
import xinan from '@/assets/fengxiang/xinan.png'
import xibei from '@/assets/fengxiang/xibei.png'
import { Table, Radio, Modal, Icon, Tooltip } from 'ant-design-vue'
import mapControlBoxArea from './components/mapControlBoxArea.vue'
import {
  getAllRecent,
  getOneRecent,
  getAllAqiInfo,
  aqiRanking,
  aqiQualityTrend,
  airCheck,
  qualityForecast,
  airIndexAverage,
  pollutionDistributed,
  airStation,
  aqci,
  allAirStation,
  ringAqci,
  getmonthAqiCalendar,
  getAreaData,
  getPredict,
  getRadar,
  getCamera,
} from '@/api/air'
import { pollutionCount, recentWeather } from '@/api/homeTable'
import { getStatistics } from '@/api/homeTable'

interface AirData {
  bottomList: string[]
  dataList: string[]
}
interface AirContrast {
  monthList: string | number[]
  thisYear: string | number[]
  lastYear: string | number[]
  thisYearName?: string
  lastYearName?: string
}
@Component({
  name: 'airQuality',
  components: {
    LineChartDashed,
    LineChartColor,
    LineAndBarChart,
    ProportionChart,
    AirMap,
    ATable: Table,
    AIcon: Icon,
    ARadioGroup: Radio.Group,
    ARadioButton: Radio.Button,
    ATooltip: Tooltip,
    DashboardChart,
    AirQualityTop,
    Swiper,
    SwiperSlide,
    airPieChart,
    RotateBarSolid,
    DashboardChartAir,
    AModal: Modal,
    AirDoublePieQuality,
    LineAndBarChartOne,
    AQIChart,
    mapControlBoxArea,
  },
})
export default class extends Vue {
  @Watch('airType', { immediate: true, deep: true })
  public onAirType(newValue: any, oldValue: any) {
    if (newValue) {
      this.getAqiTrend()
    }
  }
  @Watch('tableType', { immediate: true, deep: true })
  public onTableType(newValue: any, oldValue: any) {
    if (newValue) {
      this.getAqiRanking()
    }
  }
  @Watch('airTypeNum', { deep: true })
  public onAirTypeNum(newValue: any, oldValue: any) {
    if (newValue) {
      // if (!this.isShowArea) {
      //   this.getAirStation();
      // } else {
      //   this.getAreadata();
      // }
      this.getAirStation()
    }
  }
  @Watch('stationType', { deep: true })
  public onStationType(newValue: any, oldValue: any) {
    if (newValue) {
      // if (!this.isShowArea) {
      this.getAirStation()
      // }
    }
  }
  // 空气质量对比
  @Watch('airQualityType', { immediate: true, deep: true })
  public onAirQualityType(newValue: any, oldValue: any) {
    if (newValue) {
      this.getYearOnYear()
    }
  }
  private monitoringAlarm: any = false
  private windDirection: any = ''
  private mapZoom: any = 13
  private tableType = 'site' //site
  private ss1 = ss
  private xj1 = xj
  private ssaqi = ssaqi
  private xjaqi = xjaqi
  private siteRanking = false
  /* eslint-disable @typescript-eslint/ban-ts-ignore */
  //@ts-ignore
  private aqiDay: any = {
    // name: "空气优良天数",
    // dataList: [
    //   { name: "优", value: "50" },
    //   { name: "良", value: "60" },
    //   { name: "轻度污染", value: "10" },
    //   { name: "中度污染", value: "6" },
    //   { name: "重度污染", value: "8" },
    //   { name: "严重污染", value: "0" }
    // ],
    // colorList: [
    //   "rgb(0,255,0)",
    //   "rgb(255,255,0)",
    //   "rgb(255,126,0)",
    //   "rgb(255,0,0)",
    //   "rgb(153,0,76)",
    //   "rgb(126,0,35)"
    // ]
  }
  private airTypeNamePub = {
    NO2: 'NO₂',
    O3: 'O₃',
    PM10: 'PM₁₀',
    'PM2.5': 'PM₂.₅',
    NOx: 'NOx',
    NO: 'NO',
    SO2: 'SO₂',
    CO: 'CO',
  }
  // 热力图type
  private airTypeNumber = [
    {
      code: 'aqi',
      name: 'aqi',
      number: '0',
      selected: true,
    },
    {
      code: '105',
      name: 'PM₂.₅',
      unit: '(μg/m³)',
      number: '0',
    },
    {
      code: '104',
      name: 'PM₁₀',
      unit: '(μg/m³)',
      number: '0',
    },
    {
      code: '102',
      name: 'O₃',
      unit: '(μg/m³)',
      number: '0',
    },
    {
      code: '101',
      name: 'NO₂',
      unit: '(μg/m³)',
      number: '0',
    },
    {
      code: '103',
      name: 'CO',
      unit: '(mg/m³)',
      number: '0',
    },
    {
      code: '100',
      name: 'SO₂',
      unit: '(μg/m³)',
      number: '0',
    },
  ]
  // 空气质量趋势type
  private airTypes: any[] = [
    // {
    //   code: "aqi",
    //   name: "AQI"
    // },
    {
      code: '105',
      name: 'PM₂.₅',
    },
    {
      code: '104',
      name: 'PM₁₀',
    },
    {
      code: '102',
      name: 'O₃',
    },
    {
      code: '101',
      name: 'NO₂',
    },
    {
      code: '103',
      name: 'CO',
    },
    {
      code: '100',
      name: 'SO₂',
    },
  ]
  // 空气质量趋势
  private airData: EchartData = {
    bottomList: [],
    dataList: [],
    unit: '',
    colorType: '',
  }
  // 七天空气质量预测
  private qualityData: any[] = []
  private airQualityType = '同比'
  private mapStyle = 'amap://styles/0739a6632d849ae1b5e6f1c52f7d12c3'
  // private mapStyle = "amap://styles/0739a6632d849ae1b5e6f1c52f7d12c3";
  private colorList: Array<string> = ['#34D160', '#FF9F7F', '#FACD33', '#1D9DFF', '#24E3E1', '#FB7293']
  private swiperOption = {
    direction: 'vertical',
    slidesPerView: 4,
    slidesPerGroup: 1,
    loop: true,
    autoplay: {
      delay: 2800,
      disableOnInteraction: false,
    },
  }
  private dateData: any = {}
  private color: any = ['rgba(0,255,0, .6)', 'rgba(255,255,0, .6)', 'rgba(255,126,0, .6)', 'rgba(255,0,0, .6)', 'rgba(153,0,76, .6)', 'rgba(126,0,35, .6)']
  private colorText: any = ['优', '良', '轻度', '中度', '重度', '严重']
  private swiperOption1 = {
    direction: 'vertical',
    slidesPerView: 10,
    slidesPerGroup: 1,
    loop: true,
    autoplay: {
      delay: 2800,
      disableOnInteraction: false,
    },
  }
  private airWarning = []
  private swiperOptionAirs = {
    direction: 'vertical',
    slidesPerView: 5,
    slidesPerGroup: 1,
    loop: true,
    autoplay: {
      delay: 3000,
      disableOnInteraction: false,
    },
  }
  private swiperOptionAlert = {
    direction: 'vertical',
    slidesPerView: 10,
    slidesPerGroup: 1,
    loop: true,
    autoplay: {
      delay: 3500,
      disableOnInteraction: false,
    },
  }
  private airTableAlert = []
  private airTable = []
  private stationData: any = {}
  // AQI
  readonly AQIAirColors: Array<any> = [
    {
      color: 'rgb(0,255,0)',
      text: '优',
      type: 1,
      min: 0,
      max: 50,
    },
    {
      color: 'rgb(255,255,0)',
      text: '良',
      type: 2,
      min: 51,
      max: 100,
    },
    {
      color: 'rgb(255,126,0)',
      text: '轻度污染',
      type: 3,
      min: 101,
      max: 150,
    },
    {
      color: 'rgb(255,0,0)',
      text: '中度污染',
      type: 4,
      min: 151,
      max: 200,
    },
    {
      color: 'rgb(153,0,76)',
      text: '重度污染',
      type: 5,
      min: 201,
      max: 300,
    },
    {
      color: 'rgb(126,0,35)',
      text: '严重污染',
      type: 6,
      min: 300,
      max: 999,
    },
  ]
  // SO2
  readonly SO2AirColors: Array<any> = [
    {
      color: 'rgb(0,255,0)',
      type: 1,
      min: 0,
      max: 150,
    },
    {
      color: 'rgb(255,255,0)',
      type: 2,
      min: 151,
      max: 500,
    },
    {
      color: 'rgb(255,126,0)',
      type: 3,
      min: 501,
      max: 650,
    },
    {
      color: 'rgb(255,0,0)',
      type: 4,
      min: 651,
      max: 800,
    },
    {
      color: 'rgb(153,0,76)',
      type: 5,
      min: 801,
      max: 1600,
    },
    {
      color: 'rgb(126,0,35)',
      type: 6,
      min: 1601,
      max: 2620,
    },
  ]
  // NO2
  readonly NO2AirColors: Array<any> = [
    {
      color: 'rgb(0,255,0)',
      type: 1,
      min: 0,
      max: 100,
    },
    {
      color: 'rgb(255,255,0)',
      type: 2,
      min: 101,
      max: 200,
    },
    {
      color: 'rgb(255,126,0)',
      type: 3,
      min: 201,
      max: 700,
    },
    {
      color: 'rgb(255,0,0)',
      type: 4,
      min: 701,
      max: 1200,
    },
    {
      color: 'rgb(153,0,76)',
      type: 5,
      min: 1201,
      max: 2340,
    },
    {
      color: 'rgb(126,0,35)',
      type: 6,
      min: 2341,
      max: 3090,
    },
  ]
  // PM10
  readonly PM10AirColors: Array<any> = [
    {
      color: 'rgb(0,255,0)',
      type: 1,
      min: 0,
      max: 50,
    },
    {
      color: 'rgb(255,255,0)',
      type: 2,
      min: 51,
      max: 150,
    },
    {
      color: 'rgb(255,126,0)',
      type: 3,
      min: 151,
      max: 250,
    },
    {
      color: 'rgb(255,0,0)',
      type: 4,
      min: 251,
      max: 350,
    },
    {
      color: 'rgb(153,0,76)',
      type: 5,
      min: 351,
      max: 420,
    },
    {
      color: 'rgb(126,0,35)',
      type: 6,
      min: 421,
      max: 999,
    },
  ]
  // PM2.5
  readonly PM25AirColors: Array<any> = [
    {
      color: 'rgb(0,255,0)',
      type: 1,
      min: 0,
      max: 35,
    },
    {
      color: 'rgb(255,255,0)',
      type: 2,
      min: 36,
      max: 75,
    },
    {
      color: 'rgb(255,126,0)',
      type: 3,
      min: 76,
      max: 115,
    },
    {
      color: 'rgb(255,0,0)',
      type: 4,
      min: 116,
      max: 150,
    },
    {
      color: 'rgb(153,0,76)',
      type: 5,
      min: 151,
      max: 250,
    },
    {
      color: 'rgb(126,0,35)',
      type: 6,
      min: 251,
      max: 999,
    },
  ]
  // CO
  readonly COAirColors: Array<any> = [
    {
      color: 'rgb(0,255,0)',
      type: 1,
      min: 0,
      max: 5,
    },
    {
      color: 'rgb(255,255,0)',
      type: 2,
      min: 6,
      max: 10,
    },
    {
      color: 'rgb(255,126,0)',
      type: 3,
      min: 11,
      max: 35,
    },
    {
      color: 'rgb(255,0,0)',
      type: 4,
      min: 36,
      max: 60,
    },
    {
      color: 'rgb(153,0,76)',
      type: 5,
      min: 61,
      max: 90,
    },
    {
      color: 'rgb(126,0,35)',
      type: 6,
      min: 91,
      max: 999,
    },
  ]
  // O3
  readonly O3AirColors: Array<any> = [
    {
      color: 'rgb(0,255,0)',
      type: 1,
      min: 0,
      max: 160,
    },
    {
      color: 'rgb(255,255,0)',
      type: 2,
      min: 161,
      max: 200,
    },
    {
      color: 'rgb(255,126,0)',
      type: 3,
      min: 201,
      max: 300,
    },
    {
      color: 'rgb(255,0,0)',
      type: 4,
      min: 301,
      max: 400,
    },
    {
      color: 'rgb(153,0,76)',
      type: 5,
      min: 401,
      max: 800,
    },
    {
      color: 'rgb(126,0,35)',
      type: 6,
      min: 801,
      max: 9999,
    },
  ]
  private airPollutionLevel = [
    {
      name: '优',
      color: 'rgb(0,255,0)',
    },
    {
      name: '良',
      color: 'rgb(255,255,0)',
    },
    {
      name: '轻度污染',
      color: 'rgb(255,126,0)',
    },
    {
      name: '中度污染',
      color: 'rgb(255,0,0)',
    },
    {
      name: '重度污染',
      color: 'rgb(153,0,76)',
    },
    {
      name: '严重污染',
      color: 'rgb(126,0,35)',
    },
  ]
  private mapMarker: any = []
  private wind = ''
  private airType = '101'
  private airCheckList = {}
  private airTypeNum = 'aqi'
  private airContrast: any = {}
  private airCheckList1 = {}
  private radarData: any = []
  private CameraData: any = []
  private mapColor = ''
  private centerTimer: any
  private aiRtimer: any
  private displayState = false
  /* eslint-disable @typescript-eslint/ban-ts-ignore */
  //@ts-ignore
  private aqiCharts: any = {
    // type: 1,
    // showNumber: 56 / 500,
    // number: 56,
    // name: "AQI",
    // bgColor: ""
  }
  private todayWeather: any = {}
  private mapViewCenter: any = {
    lng: 104.05,
    lat: 30.73,
  }
  private areaCodeList: string[] = []
  mounted() {
    this.getAllAirRecent()
    // this.getAirStation();
    this.getAqiRanking()
    this.getAqiTrend()
    this.getAirCheck()
    this.getQualityForecast()
    this.getRadarList()
    this.getCameraList()
    this.getAirIndexAverage()
    this.getStatistics()
    this.output(new Date().getFullYear() + '-' + (new Date().getMonth() + 1))
    this.getmonthAqiCalendar()
    this.getYearOnYear()
    this.getRecentWeather()
    // this.autoTime();
    this.autoTime1()
    // this.getPollutionCount();
  }
  private loadHtml: any = null
  beforeDestroy() {
    clearInterval(this.aiRtimer)
    clearInterval(this.centerTimer)
  }

  get dateDataFilter() {
    const dateArr: any = []

    if (!this.dateData || this.dateData.length === 0 || !this.dateData.filter) return dateArr

    for (let index: any = 0; index < 6; index++) {
      // @ts-ignore
      dateArr.push(this.dateData.filter((e: any, i: any) => parseInt((i / 7).toString()) === index))
    }
    return dateArr
  }

  // 切换区域
  handleGetSelectArea(arr: string[]) {
    this.areaCodeList = arr
    this.getStatistics()
    this.getAirStation()
  }
  private getmonthAqiCalendar() {
    getmonthAqiCalendar().then((res) => {
      let data = (res.data.data || []).reverse()
      let indexs = this.dateData.findIndex((item: any) => item.date == 1)
      this.dateData = this.dateData.map((item: any, index: number) => {
        item.level = (index - indexs > 0 || index - indexs == 0) && index - indexs < data.length ? data[index - indexs].level : ''
        item.dates = (index - indexs > 0 || index - indexs == 0) && index - indexs < data.length ? data[index - indexs].date : ''
        item.aqi = (index - indexs > 0 || index - indexs == 0) && index - indexs < data.length ? data[index - indexs].aqi : ''
        item.primaryPollutant = (index - indexs > 0 || index - indexs == 0) && index - indexs < data.length ? data[index - indexs].primaryPollutant : ''
        return item
      })
    })
  }

  private i: any = 0
  //切换地图的空气类型
  private selectAirType(_index: number) {
    this.i = _index
    this.airTypeNum = this.airTypeNumber[_index].code
    this.mapColor = (this.airTypeNumber[_index] as any).bgColor
    const airTypeNumber = JSON.parse(JSON.stringify(this.airTypeNumber))
    const selectedIndex = airTypeNumber.findIndex((item: any) => item.selected)
    if (selectedIndex != _index) {
      airTypeNumber.forEach((item: any) => {
        item.selected = false
      })
      airTypeNumber[_index].selected = true
      this.airTypeNumber = airTypeNumber
    }
    document.querySelectorAll('.amap-e').forEach((e) => e.remove())
  }
  /**
   * 点击切换空气类型
   */
  private clickSelectAirTypeHandle(_index: number) {
    clearInterval(this.centerTimer)
    this.i = _index
    this.autoTime()
  }

  // 中间定时器
  private autoTime() {
    this.selectAirType(this.i)
    clearInterval(this.centerTimer)

    const autoTimer = () => {
      this.centerTimer = setInterval(() => {
        this.i = this.i + 1

        if (this.i === this.airTypeNumber.length - 2) {
          this.i = 0
          this.selectAirType(this.i)
          return
        }

        this.selectAirType(this.i)
      }, 15 * 1000)
    }
    autoTimer()
  }
  private isShowArea: any = true
  private changeType(args: any) {
    this.getAirStation()
    // this.isShowArea = args == 1;
    // if (this.isShowArea) {
    //   this.mapMarker = [];
    //   this.getAreadata();
    // } else {
    //   this.areaData = [];
    //   this.getAirStation();
    // }
  }
  private areaData: any = []
  private getAreadata() {
    getAreaData(this.airTypeNum).then((res) => {
      this.areaData = (res.data.data || []).map((item: any) => {
        item.concentration = item.concentration || '--'
        return item
      })
    })
  }
  private i1: any = 0
  // 空气质量趋势定时器
  private autoTime1() {
    this.airType = this.airTypes[this.i1].code
    const autoTimer = () => {
      this.aiRtimer = setInterval(() => {
        this.airType = this.airTypes[this.i1].code
        if (this.i1 === this.airTypes.length - 1) {
          setTimeout(() => {
            clearInterval(this.aiRtimer)
            this.i1 = 0
            this.airType = this.airTypes[this.i1].code
            autoTimer()
          }, 30 * 1000)
        }
        if (this.i1 < this.airTypes.length - 1) {
          this.i1 = this.i1 + 1
        }
      }, 30 * 1000)
    }
    autoTimer()
  }

  // 获取空气预警最近20条记录 全部站点
  private getAllAirRecent(): void {
    getAllRecent().then((res: any) => {
      for (const item of res.data.data) {
        if (item.keyWord !== null && item.airAlarmContent) {
          item.airAlarmContent = `<span>${item.airAlarmContent.replace(
            item.keyWord,
            `<span style="font-size:0.18rem;" class="spans"> ${this.airTypeNamePub[item.keyWord]} </span>`
          )}</span>`
        }
        const str: any = item.airAlarmTime
        const month: any = new Date(str).getMonth() + 1 < 10 ? '0' + String(new Date(str).getMonth() + 1) : new Date(str).getMonth() + 1
        const day: any = new Date(str).getDate() < 10 ? '0' + String(new Date(str).getDate()) : new Date(str).getDate()
        const hours: any = new Date(str).getHours() < 10 ? '0' + String(new Date(str).getHours()) : new Date(str).getHours()
        const minutes: any = new Date(str).getMinutes() < 10 ? '0' + String(new Date(str).getMinutes()) : new Date(str).getMinutes()
        item.airAlarmTime = `<div style="margin-bottom: 0 !important;text-align:left;">
        ${month}-${day}\n${hours}:${minutes}
        </div>`
      }
      this.airWarning = res.data.data
    })
  }

  // 获取地图的风
  private getRecentWeather() {
    recentWeather().then((res: any) => {
      if (res.data.data) {
        this.todayWeather = res.data.data
        this.wind = res.data.data.windDirectionMark ? res.data.data.windDirectionMark : ''
        this.windDirection = ''
        if (this.wind === '东风') {
          // this.windDirection = dong;
          this.windDirection = 'rotate(-90deg)'
        } else if (this.wind === '南风') {
          // this.windDirection = nan;
          this.windDirection = 'rotate(0deg)'
        } else if (this.wind === '西风') {
          // this.windDirection = xi;
          this.windDirection = 'rotate(90deg)'
        } else if (this.wind === '北风') {
          // this.windDirection = bei;
          this.windDirection = 'rotate(180deg)'
        } else if (this.wind === '东南风') {
          // this.windDirection = dongnan;
          this.windDirection = 'rotate(-45deg)'
        } else if (this.wind === '东北风') {
          // this.windDirection = dongbei;
          this.windDirection = 'rotate(-135deg)'
        } else if (this.wind === '西北风') {
          // this.windDirection = xibei;
          this.windDirection = 'rotate(-225deg)'
        } else if (this.wind === '西南风') {
          // this.windDirection = xinan;
          this.windDirection = 'rotate(-315deg)'
        }
      }
    })
  }

  /**
   * 获取地图站点和热力图
   */
  private getAirStation() {
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const _this = this
    allAirStation({
      stationTypeId: this.stationType.join(),
      pollutionCode: this.airTypeNum,
      districtCode: this.areaCodeList.join(','),
    }).then((res: any) => {
      _this.mapMarker = res.data.data
    })
  }

  // 质量指数排行
  private getAqiRanking() {
    this.airTable = []
    aqiRanking({ type: this.tableType === 'site' ? '1' : '2' }).then((res: any) => {
      // console.log(res.data.data, 'dd');
      for (const item of res.data.data) {
        for (const item1 of this.AQIAirColors) {
          if (item.airQualityIndex) {
            if (item.airQualityIndex >= item1.min && item.airQualityIndex <= item1.max) {
              item.aqiText = item1.text
            }
          }
        }
      }
      this.airTable = JSON.parse(JSON.stringify(res.data.data))
      this.airTable = this.airTable.splice(0, 10)
      this.airTableAlert = res.data.data || []
      // this.airTableAlert = this.airTableAlert.splice(0,10)
    })
  }
  private stationTypeState: any[] = [false, true, true, true]
  private stationType: any[] = [2, 3, 4]

  // 站点类型选择
  private siteTypeChange(index: any) {
    if (index == 1) {
      this.stationTypeState[0] = !this.stationTypeState[0]
    } else if (index == 2) {
      this.stationTypeState[1] = !this.stationTypeState[1]
    } else if (index == 3) {
      this.stationTypeState[2] = !this.stationTypeState[2]
    } else if (index == 4) {
      this.stationTypeState[3] = !this.stationTypeState[3]
    }
    const stationType: any = []
    this.stationTypeState.forEach((item: any, index: number) => {
      if (item) {
        stationType.push(index + 1)
      }
    })
    this.stationType = stationType
  }

  // 24小时空气质趋势
  private getAqiTrend() {
    aqiQualityTrend({
      pollutantCode: this.airType,
    }).then((res: any) => {
      const airData: EchartData = {
        bottomList: [],
        dataList: [],
        unit: '',
        colorType: '',
      }
      for (const item in res.data.data) {
        // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
        // @ts-ignore
        airData.bottomList.push(item.replace('_', '') + '时')
        // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
        // @ts-ignore
        airData.dataList.push(res.data.data[item])
        if (this.airType == '103') {
          airData.unit = 'mg/m³'
        } else {
          airData.unit = 'μg/m³'
        }
        if (this.airType == '100') {
          airData.colorType = 'SO2'
        } else if (this.airType == '101') {
          airData.colorType = 'NO2'
        } else if (this.airType == '102') {
          airData.colorType = 'O3'
        } else if (this.airType == '103') {
          airData.colorType = 'CO'
        } else if (this.airType == '104') {
          airData.colorType = 'PM10'
        } else if (this.airType == '105') {
          airData.colorType = 'PM25'
        }
      }
      this.airData = airData
    })
  }

  // 切换标签
  private airTypeChange(e: any) {
    this.airTypes.find((item: any, index: number) => {
      if (item.code == e.target.value) {
        this.i1 = index
        return
      }
    })
    this.airType = e.target.value
  }

  // 空气质量考核
  private getAirCheck() {
    airCheck().then((res: any) => {
      if (res.data.data) {
        res.data.data.now = res.data.data.now.slice(0, 11)
        // AQI
        res.data.data.YearOnYearAQI =
          res.data.data.YearOnYearQuality < 0 ? String(res.data.data.YearOnYearQuality).replace('-', '') : res.data.data.YearOnYearQuality
        // PM2.5
        res.data.data.YearOnYearPM =
          res.data.data['YearOnYearPM2.5'] < 0 ? String(res.data.data['YearOnYearPM2.5']).replace('-', '') : res.data.data['YearOnYearPM2.5']
        this.airCheckList = res.data.data
        // 饼状图
        const aqiDay: InData = {
          name: '空气优良天数',
          dataList: [
            {
              name: '优',
              value: res.data.data.pollutionDegreeCount.excellentDays,
            },
            {
              name: '良',
              value: res.data.data.pollutionDegreeCount.goodDays,
            },
            {
              name: '轻度污染',
              value: res.data.data.pollutionDegreeCount.lightPollutionDays,
            },
            {
              name: '中度污染',
              value: res.data.data.pollutionDegreeCount.moderatelyPollutedDays,
            },
            {
              name: '重度污染',
              value: res.data.data.pollutionDegreeCount.heavyPollutionDays,
            },
            {
              name: '严重污染',
              value: res.data.data.pollutionDegreeCount.seriousPollutionDays,
            },
          ],
          colorList: ['rgb(0,255,0)', 'rgb(255,255,0)', 'rgb(255,126,0)', 'rgb(255,0,0)', 'rgb(153,0,76)', 'rgb(126,0,35)'],
        }
        const newDataList: any = []
        const newColorList: any = []
        aqiDay.dataList.forEach((item: any, index: any) => {
          if (item.value != 0) {
            newDataList.push(item)
            newColorList.push(aqiDay.colorList[index])
          }
        })
        aqiDay.dataList = newDataList
        aqiDay.colorList = newColorList
        this.aqiDay = aqiDay
        // C形图
        // const aqiDay: any = {
        //   name: "空气优良天数",
        //   dataList: [
        //     {
        //       name: "优",
        //       value: (
        //         (res.data.data.pollutionDegreeCount.excellentDays /
        //           res.data.data.pollutionDegreeCount.totalDays) *
        //         100
        //       ).toFixed(0)
        //     },
        //     {
        //       name: "良",
        //       value: (
        //         (res.data.data.pollutionDegreeCount.goodDays /
        //           res.data.data.pollutionDegreeCount.totalDays) *
        //         100
        //       ).toFixed(0)
        //     },
        //     {
        //       name: "轻度污染",
        //       value: (
        //         (res.data.data.pollutionDegreeCount.lightPollutionDays /
        //           res.data.data.pollutionDegreeCount.totalDays) *
        //         100
        //       ).toFixed(0)
        //     },
        //     {
        //       name: "中度污染",
        //       value: (
        //         (res.data.data.pollutionDegreeCount.moderatelyPollutedDays /
        //           res.data.data.pollutionDegreeCount.totalDays) *
        //         100
        //       ).toFixed(0)
        //     },
        //     {
        //       name: "重度污染",
        //       value: (
        //         (res.data.data.pollutionDegreeCount.heavyPollutionDays /
        //           res.data.data.pollutionDegreeCount.totalDays) *
        //         100
        //       ).toFixed(0)
        //     },
        //     {
        //       name: "严重污染",
        //       value: (
        //         (res.data.data.pollutionDegreeCount.seriousPollutionDays /
        //           res.data.data.pollutionDegreeCount.totalDays) *
        //         100
        //       ).toFixed(0)
        //     }
        //   ],
        //   bottomList: []
        // };
        // const newDataList: any = [];
        // const bottomList: any = [];
        // aqiDay.dataList.forEach((item: any, index: any) => {
        //   if (item.value != 0) {
        //     newDataList.push(item);
        //     bottomList.push(item.name);
        //   }
        // });
        // if (bottomList.length !== 6) {
        //   const leng = 6 - bottomList.length;
        //   for (let index = 0; index < leng; index++) {
        //     bottomList.push("");
        //   }
        // }
        // aqiDay.dataList = newDataList;
        // aqiDay.bottomList = bottomList;
        // this.aqiDay = aqiDay;
        // 柱状图
        const list: any = {
          bottomList: ['O₃', 'NO₂', 'PM₂.₅', 'PM₁₀', 'CO', 'SO₂'],
          dataList: [
            res.data.data.pollutantCount.ozoneEightCount,
            res.data.data.pollutantCount.nitrogenDioxideCount,
            res.data.data.pollutantCount.fineParticulateMatterCount,
            res.data.data.pollutantCount.inhalableParticlesCount,
            res.data.data.pollutantCount.carbonMonoxideCount,
            res.data.data.pollutantCount.sulfurDioxideCount,
          ],
          colorList: [],
        }
        this.airCheckList1 = list
      }
    })
  }

  // 未来空气质量预测
  private getQualityForecast() {
    getPredict().then((res: any) => {
      this.qualityData = res.data.data
      // const qualityData: any = {
      //   bottomList: [],
      //   dataList: []
      // };
      // for (const item in res.data.data) {
      //   qualityData.bottomList.push(item.replace("_", "") + "日");
      //   qualityData.dataList.push(res.data.data[item]);
      // }
      // qualityData.bottomList.find((item: any, index: number) => {
      //   if (new Date().getDate() + "日" === item) {
      //     qualityData.bottomList[index] = "今日";
      //   }
      // });
      // this.qualityData = qualityData;
    })
  }

  /* 获取雷达列表 */
  private getRadarList() {
    getRadar().then((res: any) => {
      this.radarData = res.data.data
    })
  }

  /* 获取站点监控列表 */
  private getCameraList() {
    getCamera().then((res: any) => {
      this.CameraData = res.data.data
    })
  }
  // 站点统计
  private getStatistics() {
    const params = {
      districtCode: this.areaCodeList.join(','),
    }
    getStatistics(params).then((res) => {
      this.stationData = res.data.data
    })
  }
  // 首页污染物数据展示
  private getAirIndexAverage() {
    airIndexAverage().then((res: any) => {
      const obj = res.data.data || {}
      const airQualityConfig = {
        aqi: this.AQIAirColors,
        'NO₂': this.NO2AirColors,
        'PM₂.₅': this.PM25AirColors,
        'PM₁₀': this.PM10AirColors,
        'O₃': this.O3AirColors,
        'SO₂': this.SO2AirColors,
        CO: this.COAirColors,
      }
      this.airTypeNumber.forEach((item: any) => {
        item.number = obj[item.name] || 0
        if (item.name === 'aqi') {
          this.aqiCharts = {
            type: 1,
            showNumber: Number(item.number) / 500,
            number: Number(item.number),
            name: 'AQI',
            bgColor: item.bgColor,
            aqiText: '',
          }
        }
        airQualityConfig[item.name].forEach((it: any) => {
          if (it.min <= item.number && item.number <= it.max) {
            item.bgColor = it.color
            if (item.name === 'aqi') {
              this.aqiCharts.aqiText = it.text
            }
          }
        })
      })
      this.autoTime()
    })
  }
  // 上个月要显示的天数
  // 求出本月第一天是星期几
  // 求出上个月最大的天数 把日期设为0
  private getPrevDays(dates: any) {
    var date = new Date(dates)
    // 把日期设为第一天，为了获取第一天是星期几
    date.setDate(1)
    var week = date.getDay()
    // 把日期设为0，为了得到上个月的最后一天
    date.setDate(0)
    var maxDay = date.getDate()
    var list = []
    // 遍历红色日期的范围 push进数组
    for (var i = maxDay - week + 1; i <= maxDay; i++) {
      list.push(i)
    }
    return list
  }

  // 求本月的天数
  // 月份推到下个月
  // 日期设为0
  private getNowDays(dates: any) {
    var date = new Date(dates)
    date.setMonth(date.getMonth() + 1)
    date.setDate(0)
    var maxDay = date.getDate()
    var list = []
    //
    for (var i = 1; i <= maxDay; i++) {
      list.push(i)
    }
    return list
  }

  // 下个月要显示的天数
  private getNextDays(prevDays: any, nowDays: any) {
    var list = []
    // 一页日历42天，42 - 上月天数 - 这个月天数 = 最后显示剩余的下个月天数
    for (var i = 1; i <= 42 - prevDays - nowDays; i++) {
      list.push(i)
    }
    return list
  }
  // 生成日历
  private output(x: any) {
    let arr1 = this.getPrevDays(x).map((item: any) => {
      return {
        date: item,
        curr: 0,
      }
    })
    let arr2 = this.getNowDays(x).map((item: any) => {
      return {
        date: item,
        curr: 1,
      }
    })
    let arr3 = this.getNextDays(arr1.length, arr2.length).map((item: any) => {
      return {
        date: item,
        curr: 0,
      }
    })
    // console.log(arr2);
    // var res = '';
    // for (var i = 0; i < arr1.length; i++) {
    //   res += '<span style="color:red;">';
    //   res += arr1[i];
    //   res += '</span>';
    // }
    //
    // for (var i = 0; i < arr2.length; i++) {
    //   res += '<span>';
    //   res += arr2[i];
    //   res += '</span>';
    // }
    //
    // for (var i = 0; i < arr3.length; i++) {
    //   res += '<span style="color:red;">';
    //   res += arr3[i];
    //   res += '</span>';
    // }
    this.dateData = arr1.concat(arr2).concat(arr3)
  }

  private currYearName: number = new Date().getFullYear()

  // 空气质量对比
  private getYearOnYear() {
    if (this.airQualityType == '同比') {
      aqci().then((res: any) => {
        if (res.data.data) {
          const airContrast: any = {
            bottomList: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'],
            currentYearName: `${this.currYearName}年`,
            currentYearDataList: [],
            beforeOneYearName: `${this.currYearName - 1}年`,
            beforeOneYearDataList: [],
            sameRatioName: '同比率',
            sameRatio: [],
            unit: '指数',
            unit1: '同比率(%)',
          }
          for (const item of res.data.data.thisYear) {
            ;(airContrast.currentYearDataList as any).push(item.aqci)
          }
          for (const item of res.data.data.lastYear) {
            ;(airContrast.beforeOneYearDataList as any).push(item.aqci)
          }
          res.data.data.sameRatio.forEach((item: any, index: number) => {
            if (new Date().getMonth() >= index) {
              ;(airContrast.sameRatio as any).push(item.value)
            }
          })
          // 折线图
          // const airContrast: any = {
          //   monthList: [
          //     "1月",
          //     "2月",
          //     "3月",
          //     "4月",
          //     "5月",
          //     "6月",
          //     "7月",
          //     "8月",
          //     "9月",
          //     "10月",
          //     "11月",
          //     "12月"
          //   ],
          //   thisYearName: "2020年",
          //   thisYear: [],
          //   lastYearName: "2019年",
          //   lastYear: []
          // };
          // for (const item of res.data.data.thisYear) {
          //   (airContrast.thisYear as any).push(item.aqci);
          // }
          // for (const item of res.data.data.lastYear) {
          //   (airContrast.lastYear as any).push(item.aqci);
          // }
          this.airContrast = airContrast
        }
      })
    } else {
      ringAqci().then((res: any) => {
        const airContrast: any = {
          bottomList: [],
          monthName: '',
          monthList: [],
          ringRatioName: '环比率',
          ringRatioList: [],
          unit: '指数',
          unit1: '环比率(%)',
          textList: [],
        }
        if (res.data.data.ringRatio.length !== res.data.data.aqci.length) {
          const leng = res.data.data.aqci.length - res.data.data.ringRatio.length
          for (let index = 0; index < leng; index++) {
            airContrast.ringRatioList.push('-')
          }
        }
        for (const item of res.data.data.ringRatio) {
          airContrast.ringRatioList.push(item.value)
        }
        for (const item of res.data.data.aqci) {
          airContrast.textList.push(item.createDate)
        }
        for (const item of res.data.data.aqci) {
          airContrast.monthList.push(item.aqci)
          // item.createDate = item.createDate.slice(5, 7);
          // if (item.createDate != 10) {
          //   if (item.createDate.indexOf("0") != -1) {
          //     item.createDate = item.createDate.slice(
          //       item.createDate.indexOf("0") + 1,
          //       item.createDate.indexOf("0") + 2
          //     );
          //   }
          // }
          airContrast.bottomList.push(item.createDate)
        }
        this.airContrast = airContrast
      })
    }
  }
  // 获取首要污染物
  private getPollutionCount() {
    pollutionCount().then((res: any) => {
      const list: any = {
        bottomList: ['O3', 'NO2', 'PM2.5', 'PM10', 'CO', 'SO2'],
        dataList: [
          res.data.data.ozoneEightCount,
          res.data.data.nitrogenDioxideCount,
          res.data.data.fineParticulateMatterCount,
          res.data.data.inhalableParticlesCount,
          res.data.data.carbonMonoxideCount,
          res.data.data.sulfurDioxideCount,
        ],
        colorList: [],
      }
      this.airCheckList1 = list
    })
  }
  private fullScreen() {
    this.displayState = !this.displayState
    if (this.displayState) {
      /* eslint-disable @typescript-eslint/ban-ts-ignore */
      //@ts-ignore
      document.querySelector('.amap-controlbar').style.transform = 'scale(0.5) translateX(-7.8rem)'
      this.mapZoom = 13.4
      this.mapViewCenter = {
        lng: 104.04,
        lat: 30.71,
      }
    } else {
      /* eslint-disable @typescript-eslint/ban-ts-ignore */
      //@ts-ignore
      document.querySelector('.amap-controlbar').style.transform = 'scale(0.5)'
      this.mapZoom = 13
      this.mapViewCenter = {
        lng: 104.05,
        lat: 30.73,
      }
    }
  }
}
</script>
