<style lang="less" scoped>
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  // text-align: center;
  color: #fff;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  user-select: none;
}

#nav {
  padding: 30px;

  a {
    font-weight: bold;
    color: #2c3e50;

    &.router-link-exact-active {
      color: #42b983;
    }
  }
}
</style>

<template>
  <dv-full-screen-container>
    <a-config-provider :locale="locale" id="app">
      <!-- <div id="app"> -->
      <!-- <router-view /> -->
      <!-- </div> -->
      <router-view />
    </a-config-provider>
  </dv-full-screen-container>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import { LocaleProvider } from "ant-design-vue";
import { ConfigProvider } from "ant-design-vue";
// eslint-disable-next-line @typescript-eslint/camelcase
import zh_CN from "ant-design-vue/lib/locale-provider/zh_CN";
// window.console.log = function(){}
window._AMapSecurityConfig = {
  securityJsCode: "dcb520bf7aae9d0bbcc9901d0e366daa",
};

@Component({
  components: {
    ALocaleProvider: LocaleProvider,
    AConfigProvider: ConfigProvider,
  },
})
export default class App extends Vue {
  locale: any = zh_CN;
  public mounted(): void {
    interface ConsoleOption {
      title: string;
      content: string;
      backgroundColor: string;
    }

    /**
     * @param {option.title} 标题
     * @param {option.content} 内容
     * @param {option.backgroundColor} 内容背景色
     */
    function Console(option: ConsoleOption): void {
      const title = option.title;
      const content = option.content;
      const backgroundColor = option.backgroundColor;
      const arg = [
        "%c ".concat(title, " %c ").concat(content, " "),
        "padding: 1px; border-radius: 3px 0 0 3px; color: #fff; background: ".concat(
          "#606060",
          ";"
        ),
        "padding: 1px; border-radius: 0 3px 3px 0; color: #fff; background: ".concat(
          backgroundColor,
          ";"
        ),
      ];
      window.console &&
        typeof window.console.log === "function" &&
        window.console.log.call(null, ...arg);
    }
    Console({
      title: "项目环境",
      content: process.env.NODE_ENV as string,
      backgroundColor: "#1475b2",
    });
    Console({ title: "Version", content: "1.0.0", backgroundColor: "#42c02e" });
    Console({
      title: "金牛区环保-大数据",
      content: `${new Date().toISOString()}`,
      backgroundColor: "#42c02e",
    });
  }
}
</script>
