import Vue from "vue";
import VueRouter from "vue-router";
import Layout from "@/layout/index.vue";

Vue.use(VueRouter);

export const routes: any[] = [
  {
    path: "/404",
    component: () => import("@/views/error-page/404.vue"),
  },
  {
    path: "/401",
    component: () => import("@/views/error-page/401.vue"),
  },
  {
    path: "/login",
    component: () => import("@/views/login/index.vue"),
  },
  {
    path: "/testpage",
    component: () => import("@/views/testpage.vue"),
  },
  {
    path: "/",
    meta: { title: "首页" },
    redirect: "/homeMap",
    component: Layout,
    children: [
      {
        path: "homeMap",
        name: "homeMap",
        meta: {
          title: "指挥沙盘",
        },
        component: () => import("@/views/MapHome.vue"),
      },
      {
        path: "homeTable",
        name: "homeTable",
        meta: {
          title: "数据统计",
        },
        // component: () => import("@/views/tableHome.vue"),
        component: () => import("@/views/swiperHome.vue"),
      },
      {
        path: "rehearseAnalyse",
        name: "rehearseAnalyse",
        meta: {
          title: "演变复盘",
        },
        // component: () => import("@/views/tableHome.vue"),
        component: () => import("@/views/rehearseAnalyse.vue"),
      },
      {
        path: "dataTable",
        name: "dataTable",
        meta: {
          title: "数据展示首页",
        },
        component: () => import("@/views/TableHome.vue"),
      },
      {
        path: "waterQuality",
        name: "waterQuality",
        meta: {
          title: "水环境质量",
        },
        component: () => import("@/views/water-quality/index.vue"),
      },
      {
        path: "airQuality",
        name: "airQuality",
        meta: {
          title: "大气环境质量",
        },
        component: () => import("@/views/air-quality/index.vue"),
      },
      {
        path: "vehicleManage",
        name: "vehicleManage",
        meta: {
          title: "车辆管理",
        },
        component: () => import("@/views/vehicles/index1.vue"),
      },
      {
        path: "patrolKanban",
        name: "patrolKanban",
        meta: {
          title: "巡岗看板",
        },
        component: () => import("@/views/patrol-kanban/index.vue"),
      },
      // {
      //   path: "heavilyPollutedEnterprise",
      //   name: "heavilyPollutedEnterprise",
      //   meta: {
      //     title: "重点污染源",
      //   },
      //   component: () =>
      //     import("@/views/heavily-polluted-enterprise/index.vue"),
      // },
      {
        path: "SoundEnvironment",
        name: "SoundEnvironment",
        meta: {
          title: "声环境质量",
        },
        component: () =>
          import("@/views/SoundEnvironment/index.vue"),
      },
      {
        path: "otherPages",
        name: "otherPages",
        meta: {
          title: "污染源监管",
        },
        component: () =>
          import("@/views/heavily-polluted-enterprise/other-pages.vue"),
      },
      {
        path: "taskManagement",
        name: "taskManagement",
        meta: {
          title: "部门联动",
        },
        // component: () => import("@/views/task-management/index1.vue"),
        component: () => import("@/views/task-management/index2.vue"),
      },
      {
        path: "env-health",
        name: "env-health",
        meta: {
          title: "环境健康",
        },
        // component: () => import("@/views/task-management/index1.vue"),
        component: () => import("@/views/health/health.vue"),
      },
      {
        path: "emergencyCommand",
        name: "emergencyCommand",
        meta: {
          title: "应急指挥",
        },
        // component: () => import("@/views/emergency-command/index2.vue"),
        // component: () => import("@/views/emergency-command/index3.vue"),
        component: () => import("@/views/emergencyCommand/index.vue"),
      },
      {
        path: "waterDetails",
        name: "waterDetails",
        meta: {
          title: "水环境质量详情页面",
        },
        component: () => import("@/views/details/water-details.vue"),
      },
      {
        path: "waterOutlet",
        name: "waterOutlet",
        meta: {
          title: "排口监测详情页面",
        },
        component: () => import("@/views/details/water-outlet.vue"),
      },
      {
        path: "airDetails",
        name: "airDetails",
        meta: {
          title: "大气环境质量详情页面",
        },
        component: () => import("@/views/details/air-details.vue"),
      },
      {
        path: "waterVideo",
        name: "waterVideo",
        meta: {
          title: "水质监测更多视频",
        },
        component: () => import("@/views/water-quality/water-video.vue"),
      },
      {
        path: "waterRealVideo",
        name: "waterRealVideo",
        meta: {
          title: "水质监测更多视频",
        },
        component: () => import("@/views/water-quality/water-real-video.vue"),
      },
      {
        path: "StationPatrolRecord",
        name: "StationPatrolRecord",
        meta: {
          title: "巡站记录",
        },
        component: () => import("@/views/water-quality/station-patrol-record.vue"),
      },
      {
        path: "equipmentCondition",
        name: "equipmentCondition",
        meta: {
          title: "设备工况",
        },
        component: () => import("@/views/equipment-condition/index.vue"),
      },
      {
        path: "departmentDetail",
        name: "departmentDetail",
        meta: {
          title: "部门任务",
        },
        component: () => import("@/views/task-management/department-detail.vue"),
      },
      {
        path: "streetDetail",
        name: "streetDetail",
        meta: {
          title: "街道办任务",
        },
        component: () => import("@/views/task-management/street-detail.vue"),
      },
      {
        path: "vehiclesVideo",
        name: "vehiclesVideo",
        meta: {
          title: "更多视频",
        },
        component: () => import("@/views/vehicles/vehicles-video.vue"),
      },
      {
        path: "moreVideo",
        name: "moreVideo",
        meta: {
          title: "更多视频",
        },
        component: () => import("@/views/water-quality/more-video.vue"),
      },
      {
        path: "videoList",
        name: "videoList",
        meta: {
          title: "回放录像",
        },
        component: () => import("@/views/videoList/videoList.vue"),
      },
      {
        path: "monitorDetails",
        name: "monitorDetails",
        meta: {
          title: "监测详情",
        },
        component: () => import("@/views/details/monitor-details.vue"),
      },
      {
        path: "pollutionVideo",
        name: "pollutionVideo",
        meta: {
          title: "污染源监测视频",
        },
        component: () => import("@/views/heavily-polluted-enterprise/videoComponent/index.vue"),
      },
      {
        path: "AtmosphericMonitor",
        name: "AtmosphericMonitor",
        meta: {
          title: "大气监控",
        },
        component: () => import("@/views/heavily-polluted-enterprise/AtmosphericMonitor/index.vue"),
      },
      {
        path: "MonitorVideo",
        name: "MonitorVideo",
        meta: {
          title: "更多视频",
        },
        component: () => import("@/views/heavily-polluted-enterprise/MonitorVideo/index.vue"),
      }
    ],
  },
  { path: "*", redirect: "/404" }, // * 404 必须放在路由最后
];

const router = new VueRouter({
  routes: routes,
});

export default router;
