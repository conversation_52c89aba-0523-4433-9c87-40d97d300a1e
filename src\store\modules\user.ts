import { ActionContext } from "vuex";

export interface State {
  name: string;
  password: string;
  pageUnm: Number
}

const state = {
  name: null,
  password: null,
  pageUnm: 1
};

const mutations = {
  SET_NAME: (state: State, name: string) => {
    state.name = name;
  },
  SET_PAGEUNM:(state: State, num: Number) => {
    state.pageUnm = num;
  },
};

const actions = {
  setName(action: ActionContext<State, any>, name: string) {
    action.commit("SET_NAME", name);
  },
  setPageUnm(action: ActionContext<State, any>, num: Number) {
    action.commit("SET_PAGEUNM", num);
  },
};

export default {
  namespaced: true,
  state,
  mutations,
  actions
};
