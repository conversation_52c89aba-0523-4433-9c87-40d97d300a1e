<template>
    <div class="video-tem">
        <div v-if="vidoeUrl" :id="'aliplayer_'+playerId" class="prism-player" :style="{width: type ==2 ? '3.49rem' : '3.49rem',
         height: type ==2 ? '1.86rem' : '1.86rem'}" />
        <div v-else :style="{width: type ==2 ? '3.49rem' : '3.49rem',
         height: type ==2 ? '1.86rem' : '1.86rem'}" style="line-height: 1.4rem;font-size: 0.16rem;text-align: center">该摄像头暂不在线</div>
    </div>
</template>

<script>
// import LivePlayer from '@liveqing/liveplayer'
// import VueAliplayer from "vue-aliplayer";
export default {
    name: "livePlayer",
    // components: { "ali-player": VueAliplayer },
    props: {
        playerId: {
            type: Number | String,
            default: 1
        },
        vidoeUrl: {
            type: String,
            default: ''
        },
        snapUrl: {
            type: String,
            default: ''
        },
        online: {
            type: Boolean,
            default: true
        },
        type: {
            type: Number,
            default: 1
        },
        outPage: {
            type: Boolean,
            default: false
        }
    },
    watch: {
        playerId: {
            handler(newVal,oldVal){
                if(newVal){
                    this.$nextTick(() => {
                        this.initVideo()
                    })
                }
            }
        }
    },
    data() {
        return {
            // playerId: 'aliplayer_' + Math.random().toString(36).substr(2),
            scriptTagStatus: 0,
            isReload: false,
            instance: null,
            player: null,
        }
    },
    mounted() {
        this.initVideo()
    },
    methods: {
      initVideo() {
          if (this.vidoeUrl) {
              const _this = this
              if(this.player){
                  this.player.dispose()
              }
              this.player = new Aliplayer({
                  id: 'aliplayer_' + this.playerId,
                  width: '100%',
                  useH5Prism: true,
                  // 支持播放地址播放,此播放优先级最高
                  source: this.vidoeUrl,
                  autoplay: true,
                  isLive: true,
                  cover: this.snapUrl,
                  rePlay: false,
                  // playsinline: true,
                  // preload: false,
                  // controlBarVisibility: "hover",
                  skinLayout: [
                      {
                          "name": "H5Loading",
                          "align": "cc"
                      },
                      {
                          "name": "thumbnail"
                      },
                      {
                          "name": "controlBar",
                          "align": "blabs",
                          "x": 0,
                          "y": 0,
                          "children": [
                              {
                                  "name": "playButton",
                                  "align": "tl",
                                  "x": 15,
                                  "y": 12
                              },
                              {
                                  "name": "fullScreenButton",
                                  "align": "tr",
                                  "x": 10,
                                  "y": 12
                              },
                              {
                                  "name": "volume",
                                  "align": "tr",
                                  "x": 5,
                                  "y": 10
                              }
                          ]
                      }
                  ]
              })

          }
      }
    },
    beforeDestroy() {
        this.player.pause()
        this.player.dispose()
    }
}
</script>
<style lang="postcss" scoped>
    @import 'https://g.alicdn.com/de/prismplayer/2.7.2/skins/default/aliplayer-min.css';
</style>
