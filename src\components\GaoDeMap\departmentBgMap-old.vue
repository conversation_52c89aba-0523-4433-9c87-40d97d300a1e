<template>
  <div class="container" ref="container"></div>
</template>

<script lang="ts">
  //@ts-ignore
  import AMap from "AMap";
  //@ts-ignore
  import jinniuStreet from "@/assets/map-geojson/jinniu_street";
  import { Vue, Component, Prop, Watch } from "vue-property-decorator";
  import { Icon } from "ant-design-vue";
  import { webglcontextlostHandle } from "@/utils/index"

  // 地图实例
  let maps: any= null;



  @Component({
    name: "DepartmentBgMap",
    components: {
      AIcon: Icon
    }
  })
  export default class extends Vue {
    // private maps: any = "";
    @Prop({
      required: false,
      type: Number,
      default: 12.5
    })
    private mapZoom!: number;
    @Prop({ required: false })
    mapStyle!: string;
    @Prop({ required: false, default: "3D" }) viewMode!: string;

    @Watch("mapZoom", { immediate: true, deep: false })
    private onmapZoomChange(newValue: number, oldValue: number) {
      if (newValue && maps) {
        maps.setZoom(newValue);
      }
    }

    
    mounted() {
      // eslint-disable-next-line @typescript-eslint/no-this-alias
      setTimeout(_ => {
        this.map();
        maps.on("complete", () => {
          this.createOverlay();

          this.creatGeojson();
          
          const waterCenterPosition = new AMap.LngLat(104.04, 30.74);
          maps.setCenter(waterCenterPosition);
          
        })
      
      }, 0)
    }

    beforeDestroy() {
      console.log('新地图组件');
      
      maps.clearMap();
      
      // 销毁地图
      maps.destroy()
      maps = null;
    }

    // 高德地图
    private map(): void {
      // eslint-disable-next-line @typescript-eslint/no-this-alias
      const _this = this;
      // 初始化地图
      const map = new AMap.Map(this.$refs.container, {
        center: [104.05, 30.73],
        position: [104.05, 30.73],
        zoom: this.mapZoom,
        viewMode: this.viewMode,
        pitch: 0,
        zoomEnable: false,
        dragEnable: false,
        zooms: [12, 18]
      });
      
      // 处理webgl上下文丢失事件
      webglcontextlostHandle.call(this)

      // 设置地图样式
      map.setMapStyle(this.mapStyle);

      // 添加雷达图
      const object3Dlayer = new AMap.Object3DLayer();
      map.add(object3Dlayer);
      // eslint-disable-next-line @typescript-eslint/no-this-alias
      const that = this;

      let radar: AMap.Object3D.Mesh;

      // 添加雷达扫描2D网格
      // 构造雷达地形
      const buildRadar = function() {
        radar = new AMap.Object3D.Mesh();
        radar.transparent = true;
        radar.backOrFront = "front";

        const geometry = radar.geometry;
        let radius = 9 * 1200; // 半径 * 米
        radius = radius / map.getResolution(map.getCenter(), 20);
        const unit = 0.1; // 单位
        const range = 100; // 扇形角度
        const count = range / unit;

        const getOpacity = function(scale: number): number {
          return 1 - Math.pow(scale, 0.2);
        };

        for (let i = 0; i < count; i += 1) {
          const angle1 = (i * unit * Math.PI) / 180;
          const angle2 = ((i + 1) * unit * Math.PI) / 180;
          const p1x = Math.cos(angle1) * radius;
          const p1y = Math.sin(angle1) * radius;
          const p2x = Math.cos(angle2) * radius;
          const p2y = Math.sin(angle2) * radius;

          geometry.vertices.push(0, 0, 0);
          geometry.vertices.push(p1x, p1y, 0);
          geometry.vertices.push(p2x, p2y, 0);

          const opacityStart = getOpacity(i / count);
          const opacityEnd = getOpacity((i + 1) / count);

          geometry.vertexColors.push(0, 0.5, 0.2, opacityStart);
          geometry.vertexColors.push(0, 0.5, 0.2, opacityStart);
          geometry.vertexColors.push(0, 0.5, 0.2, opacityEnd);
        }

        radar.position(map.getCenter());

        object3Dlayer.add(radar);
      };
      // 雷达扫描处理
      const scan = function(): void {
        radar.rotateZ(-1);
        AMap.Util.requestAnimFrame(scan);
      };

      buildRadar();
      scan();

      maps = map;
    }

    // 添加行政区外的覆盖物
    private createOverlay() {
      const map = maps;
      // eslint-disable-next-line @typescript-eslint/no-this-alias
      const that = this;
      // 添加金牛区地理信息数据 3
      new AMap.DistrictSearch({
        extensions: "all",
        subdistrict: 0
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
      }).search("金牛区", function(status: any, result: any) {
        // 外多边形坐标数组和内多边形坐标数组
        const outer = [
          new AMap.LngLat(-360, 90, true),
          new AMap.LngLat(-360, -90, true),
          new AMap.LngLat(360, -90, true),
          new AMap.LngLat(360, 90, true)
        ];
        const holes = result.districtList[0].boundaries;

        const pathArray: any = [outer];
        // eslint-disable-next-line prefer-spread
        pathArray.push.apply(pathArray, holes);
        const polygon = new AMap.Polygon({
          pathL: pathArray,
          //线条颜色，使用16进制颜色代码赋值。默认值为#006600
          strokeColor: "rgb(255,255,255)",
          strokeWeight: 0,
          //轮廓线透明度，取值范围[0,1]，0表示完全透明，1表示不透明。默认为0.9
          strokeOpacity: 0,
          //多边形填充颜色，使用16进制颜色代码赋值，如：#FFAA00
          fillColor: "rgba(3,4,130)",
          // fillColor: "rgba(4,20,50)",
          // fillColor: "#0A1C5F",
          //多边形填充透明度，取值范围[0,1]，0表示完全透明，1表示不透明。默认为0.9
          fillOpacity: 0,
          //轮廓线样式，实线:solid，虚线:dashed
          strokeStyle: "solid",
          strokeDasharray: [10, 2, 10]
        });
        polygon.setPath(pathArray);
        map.add(polygon);
      });
    }

    
  // 添加街道划分区域地图数据
  private creatGeojson() {
    const map = maps;
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const that = this;
    // 添加金牛区地理信息数据 1
    const geojson = new AMap.GeoJSON({
      geoJSON: jinniuStreet,
      getPolygon: function(geojson: any, lnglats: any) {
        AMap.convertFrom(geojson.geometry.coordinates[0], "gps", function(
          status: any,
          result: any
        ) {
          // const area: any = AMap.GeometryUtil.ringArea(lnglats[0]);
          if (geojson.properties.name !== "金牛区") {
            const text = new AMap.Text({
              text: geojson.properties.name,
              anchor: "center", // 设置文本标记锚点
              draggable: false,
              cursor: "pointer",
              angle: 0,
              style: {
                padding: ".75rem 1.25rem",
                "margin-bottom": "1rem",
                "border-radius": ".25rem",
                "background-color": "transparent",
                "border-width": 0,
                "text-align": "center",
                "font-size": "14px",
                "pointer-events": "none",
                color: "#36E9EF"
              },
              position: [
                geojson.properties.center.lng,
                geojson.properties.center.lat
              ]
            });
            text.setMap(map);
          }

          if (result.info === "ok") {
            const polygon = new AMap.Polygon({
              path: result.locations,
              // strokeColor: "#0ea9f9",
              strokeColor: "#2fbeb5",
              strokeWeight: 2,
              strokeOpacity: 1,
              fillOpacity: 0.5, // 多边形填充透明度
              fillColor: "rgba(0,49,113, 0.15)",
              zIndex: 10
            });
            map.add(polygon);
          }
        });
      }
    });

    // 添加金牛区地理信息数据 2
    geojson.setMap(map);
  }
  }
</script>

<style lang="less">
  @import url(./map.less);
</style>
<style lang="less" scoped>
.container {
  width: 100%;
  height: 100%;
}
</style>
