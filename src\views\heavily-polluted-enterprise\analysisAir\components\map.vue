<template>
  <div class="analysisMap-container">
    <div ref="analysisMapContainer" class="analysisMapContainer"></div>
  </div>
</template>

<script>
// import AMap from 'AMap'
import AMapLoader from '@amap/amap-jsapi-loader'
import jinniuStreet from '@/assets/map-geojson/jinniu_street_other'
import jinniuArea from '@/assets/map-geojson/area_five.json'
import noiseClose from '@/assets/noise/<EMAIL>'; // 噪声弹窗关闭
import QYCB from '@/assets/heavily-polluted-enterprise/<EMAIL>'  // 企业超标
import QYZC from '@/assets/heavily-polluted-enterprise/<EMAIL>'  // 企业正常
import QYLX from '@/assets/heavily-polluted-enterprise/<EMAIL>'  // 企业离线
import electricityBg from '@/assets/noise/<EMAIL>'; // 用电弹窗背景
const TEXT_COLOR = {
  offLine: {
    text: '离线',
    color: '#ACACAC'
  },
  0: {
    text: '优',
    color: '#01FF01'
  },
  50: {
    text: '良',
    color: '#E8D505'
  },
  100: {
    text: '轻度污染',
    color: '#FD8200'
  },
  150: {
    text: '中度污染',
    color: '#FD0001'
  },
  200: {
    text: '重度污染',
    color: '#95014B'
  },
  300: {
    text: '严重污染',
    color: '#7E0226'
  },
}
const AQI_LEVEL = [ 0, 50, 100, 150, 200, 300, 500 ]
export default {
  name: '',
  data() {
    return {
      // 地图配置
      mapConfig: {
        zoom: 13.8,
        zooms: [12, 20],
        center: [104.061111, 30.714222],
        mapStyle: 'amap://styles/0739a6632d849ae1b5e6f1c52f7d12c3',
        viewMode: '2D',
        zoomEnable: true,
        dragEnable: true,
        // rotation: 45,
        // pitch: 30, // 地图倾斜
      },
      // 区控 2
      mapIcon: {
        1: require('@/assets/map_icon/icon_1.png'),
        2: require('@/assets/map_icon/icon_2.png'),
        3: require('@/assets/map_icon/icon_3.png'),
        4: require('@/assets/map_icon/icon_4.png'),
        5: require('@/assets/map_icon/icon_5.png'),
        6: require('@/assets/map_icon/icon_6.png'),
        7: require('@/assets/map_icon/icon_7.png')
      },
      // 国控 4
      mapIconGk: {
        1: require('@/assets/<EMAIL>'),
        2: require('@/assets/<EMAIL>'),
        3: require('@/assets/<EMAIL>'),
        4: require('@/assets/<EMAIL>'),
        5: require('@/assets/<EMAIL>'),
        6: require('@/assets/<EMAIL>'),
        7: require('@/assets/<EMAIL>')
      },
      // 市控 3
      mapIconSk: {
        1: require('@/assets/skz_hui.png'),
        2: require('@/assets/skz1.png'),
        3: require('@/assets/skz2.png'),
        4: require('@/assets/skz3.png'),
        5: require('@/assets/skz4.png'),
        6: require('@/assets/skz5.png'),
        7: require('@/assets/skz6.png')
      },
      // 微站 1
      mapIconWz: {
        1: require('@/assets/wkz_hui.png'),
        2: require('@/assets/wkz1.png'),
        3: require('@/assets/wkz2.png'),
        4: require('@/assets/wkz3.png'),
        5: require('@/assets/wkz4.png'),
        6: require('@/assets/wkz5.png'),
        7: require('@/assets/wkz6.png')
      },
      currentMarker: {},
      stationMarkerList: [],
      infoWindow: null,
      AMap: null,
      maps: null,
      circleMap: null,
      companyMarkerList: [],
      myCanvas: null,
      myContext: null,
      canvasRadius: 0,
      CanvasLayer: null,
      animFrameId: 0,
      lineLayer: null,
      jinniuStreet
    }
  },
  created() {
    AMapLoader.reset()
    // // load 加载
    AMapLoader.load({
      "key": "777fec7ef3cc29281d60ae900fa33925",              // 申请好的Web端开发者Key，首次调用 load 时必填
      "version": "1.4.15",   // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
      "plugins": ['AMap.DistrictSearch','AMap.Heatmap','AMap.ControlBar','AMap.Object3DLayer','Map3D','AMap.Geocoder','AMap.CircleMarker','AMap.MouseTool'],           // 需要使用的的插件列表，如比例尺'AMap.Scale'等
      "AMapUI": {             // 是否加载 AMapUI，缺省不加载
        "version": '1.0',   // AMapUI 版本
        // "plugins":['overlay/SimpleMarker'],       // 需要加载的 AMapUI ui插件
      },
      "Loca":{                // 是否加载 Loca， 缺省不加载
        "version": '1.3.2'  // Loca 版本
      },
    }).then((AMap)=>{
      this.initMap(AMap)
    }).catch(e => {
      console.log(e);
    })
    window.closeInfoWindow1 = this.closeInfoWindow1
  },
  mounted() {
    // this.initMap()
  },
  props: {
    markerList: {
        type: Array,
        default: () => []
    },
    distance: {
      type: Number | String,
      default: 500
    },
    stationList: {
      type: Array,
      default: () => []
    },
    stationOpen: {
      type: Number | String,
      default: ''
    },
  },
  methods: {
    // 初始化地图
    initMap(amaps) {
       this.$nextTick(() => {
            const map = new AMap.Map(this.$refs.analysisMapContainer, this.mapConfig);
            this.AMap = map;
            // map.on('click', () => {
            //     console.log('点击地图')
            // })
            this.maps = amaps
            map.on('complete', () => {
               // this.createOverlay();
               this.creatGeojson()
             })
            if(this.markerList.length) {
                this.$nextTick(() => {

                })
            }
            })
    },
    // 添加区域行政区内覆盖
    creatGeojson() {
      const map = this.AMap
      // eslint-disable-next-line @typescript-eslint/no-this-alias
      const that = this
      // 添加金牛区地理信息数据 1
      const geojson = new AMap.GeoJSON({
        geoJSON: this.jinniuStreet,
        getPolygon: function (geojson, lnglats) {
          that.maps.convertFrom(
            geojson.geometry.coordinates[0],
            'gps',
            function (status, result) {
              if (result.info === 'ok' && geojson.properties.name !== '金牛区') {
                const polygon = new AMap.Polygon({
                  path: result.locations,
                  strokeColor: '#2266AB',
                  strokeWeight: 3,
                  strokeOpacity: 1,
                  // strokeStyle: 'dashed',
                  // strokeDasharray: [10,10],
                  // fillOpacity: 0.5, // 多边形填充透明度
                  fillColor: 'rgba(3,70,109,0.3)',
                  zIndex: 1,
                })
                map.add(polygon)
              }
            }
          )
        },
      })
      // 添加金牛区地理信息数据 2
      geojson.setMap(map)
    },
    // 区域覆盖物点击事件
    handleClick(e){
        console.log('区域点击事件')
    },
    //  空气等级过滤
    airLevelFilter(concentration,stationTypeId){
      let mapIcon=[]
      if(stationTypeId===1){
        mapIcon=this.mapIconWz
      }else if(stationTypeId===2){
        mapIcon=this.mapIcon
      }else if(stationTypeId===3){
        mapIcon=this.mapIconSk
      }else if(stationTypeId===4){
        mapIcon=this.mapIconGk
      }else{
        mapIcon=this.mapIcon
      }
      if(!concentration) return mapIcon[1]
      let icon = mapIcon[1]
      if(concentration < AQI_LEVEL[1]) icon = mapIcon[2]
      if(concentration >= AQI_LEVEL[1]) icon = mapIcon[3]
      if(concentration >= AQI_LEVEL[2]) icon = mapIcon[4]
      if(concentration >= AQI_LEVEL[3]) icon = mapIcon[5]
      if(concentration >= AQI_LEVEL[4]) icon = mapIcon[6]
      if(concentration >= AQI_LEVEL[5]) icon = mapIcon[7]
      if(concentration >= AQI_LEVEL[6]) icon = mapIcon[7]
      return icon
    },
    // 添加站点icon
    setMarkers() {
        if(this.stationMarkerList.length) {
            this.AMap.remove(this.stationMarkerList)
            this.stationMarkerList = []
        }
        this.stationMarkerList = []
         console.log('this.markerList', this.markerList)
        this.markerList.forEach((item, index) => {
            this.addMaker(item)
        })
        // if(this.markerList.length) {
        //   const sortMarkerList = [...this.markerList].sort((a, b) => b.concentration - a.concentration)
        //   this.currentMarker = sortMarkerList[0]
        //   this.setINfoWindow(sortMarkerList[0])
        //   this.setCircle(sortMarkerList[0])
        //   this.setCanvas(sortMarkerList[0])
        //   this.AMap.setCenter([sortMarkerList[0].gcLng, sortMarkerList[0].gcLat])
        //   this.$emit('handleChangeStation', sortMarkerList[0].stationCode)
        // }
    },
    addMaker(item) {
      // console.log(item,'itemitemitemitemitemitemitemitemitemitemitem')
      let image = this.airLevelFilter(item.concentration,item.stationTypeId)
      let mapIcon=[]
      let size=25
      if(item.stationTypeId===1){
        mapIcon=this.mapIconWz
      }else if(item.stationTypeId===2){
        mapIcon=this.mapIcon
        size=17
      }else if(item.stationTypeId===3){
        mapIcon=this.mapIconSk
      }else if(item.stationTypeId===4){
        mapIcon=this.mapIconGk
      }else{
        mapIcon=this.mapIcon
      }
        if(!item.online) image = mapIcon[1]
        const icon = new AMap.Icon({
          // 图标尺寸
          size: new AMap.Size(size, size),
          // 图标的取图地址
          image: image,
          // 图标所用图片大小
          imageSize: new AMap.Size(size, size),
          // 图标取图偏移量
          imageOffset: new AMap.Pixel(0, 0)
        })
        const markerOptions = {
          map: this.AMap,
          position: new AMap.LngLat(Number(item.gcLng),Number(item.gcLat)),
          offset: new AMap.Pixel(-8.5, -8.5),
          icon: icon,
          zIndex: 9,
          data: item
        }
        let markerIcon = new AMap.Marker(markerOptions)
        // AMap.event.addListener(markerIcon, ()=> {
        //     this.handleClickMarker
        // })
        markerIcon.on('click', this.handleClickMarker)
        this.stationMarkerList.push(markerIcon)

    },
    closeInfoWindow1(type) {
      if (this.AMap) {
        this.AMap.clearInfoWindow()
        if(this.circleMap) {
          this.AMap.remove(this.circleMap)
          this.circleMap = null
        }
        if(this.animFrameId) {
          AMap.Util.cancelAnimFrame(this.animFrameId);
          this.animFrameId = null
        }
        if(this.CanvasLayer) {
          this.AMap.remove(this.CanvasLayer)
          this.CanvasLayer = null
          this.myContext = null
        }
      }
      // 关闭站点弹框  请求站点
      if(type){
         this.$emit('handleChangeStation', {stationCode: undefined, gcLng: undefined, gcLat: undefined,type:1
      })
      }

    },
    // 计算颜色 污染程度
    computedTextColor(data) {
        let pollutionType = ''
        let currentColor = '#01FF01'
        if(data.concentration >= 0 && data.concentration <= 50) {
          pollutionType = TEXT_COLOR[0].text
          currentColor = TEXT_COLOR[0].color
        } else if(data.concentration > 50 && data.concentration <= 100) {
          pollutionType = TEXT_COLOR[50].text
          currentColor = TEXT_COLOR[50].color
        } else if(data.concentration > 100 && data.concentration <= 150) {
          pollutionType = TEXT_COLOR[100].text
          currentColor = TEXT_COLOR[100].color
        } else if(data.concentration > 150 && data.concentration <= 200) {
          pollutionType = TEXT_COLOR[150].text
          currentColor = TEXT_COLOR[150].color
        } else if(data.concentration > 200 && data.concentration <= 300) {
          pollutionType = TEXT_COLOR[200].text
          currentColor = TEXT_COLOR[200].color
        } else if(data.concentration > 300) {
          pollutionType = TEXT_COLOR[300].text
          currentColor = TEXT_COLOR[300].color
        }
        if(!data.online) {
          pollutionType = TEXT_COLOR['offLine'].text
          currentColor = TEXT_COLOR['offLine'].color
        }
        return { pollutionType, currentColor }
    },
    // 添加弹窗
    setINfoWindow(data) {
      const { pollutionType, currentColor } = this.computedTextColor(data)
      let infoWindowContent = `
          <div class="infoWindow" style="background: url('${electricityBg}') no-repeat;">
            <span class="title" style="">站点信息</span>
            <img class="close" src="${noiseClose}" style="" onclick="closeInfoWindow1(1)"/>
            <div class="content">
                <div class="time">监测时间：${data.createTime ? data.createTime.substring(0, 16) : "--"}</div>
                <div class="item">
                    <span class="label">站点名称：</span>
                    <span class="value">${data.positionName || '--'}</span>
                </div>
                <div class="item">
                    <span class="label">AQI：</span>
                    <span class="value" style="color:#ffffff"><b class="number" style="color: ${currentColor}">${(data.concentration || data.concentration === 0 || data.concentration === '0') ? data.concentration : '--'}</b><span style="background: #0E5EAF; display: inline-block;padding: 0 10px;border-radius 5px;">${pollutionType}</span></span>
                </div>
                <div class="item">
                    <span class="label">所在街道：</span>
                    <span class="value">${data.streetName || '--'}</span>
                </div>
                <div class="item">
                    <span class="label">详细地址：</span>
                    <span class="value">${data.address || '--'}</span>
                </div>
            </div>
          </div>
          `
        if (this.infoWindow) {
        this.infoWindow.setContent(infoWindowContent)
        this.infoWindow.setPosition([data.gcLng, data.gcLat]) // 更新点标记位置
        this.infoWindow.open(this.AMap)
      } else {
        // 创建一个自定义内容的 infowindow 实例
        this.infoWindow = new AMap.InfoWindow({
          position: [data.gcLng, data.gcLat],
          offset: new AMap.Pixel(-1, -2),
          content: infoWindowContent,
          zIndex: 10
        })
        this.infoWindow.open(this.AMap)
        // this.infoWindow.on('close', this.handlecloseInfoWindow1)
      }
    },
    // 添加圆圈
    setCircle(data) {
      if(this.circleMap) {
        this.circleMap.setCenter([data.gcLng, data.gcLat])
      } else {
        //  添加圆圈
        let circle = new AMap.Circle({
            map: this.AMap,
            center: [data.gcLng, data.gcLat],         //设置线覆盖物路径
            radius: this.distance,
            strokeColor: "#fff", //边框线颜色
            strokeStyle: 'dashed',
            strokeOpacity: 0.5,       //边框线透明度
            strokeWeight: 1,        //边框线宽
            fillColor: "#fff", //填充色
            fillOpacity: 0//填充透明度
        });
        this.circleMap = circle
      }
    },
    // 波纹动画
    setCanvas(item) {
      if(this.animFrameId) {
        AMap.Util.cancelAnimFrame(this.animFrameId);
      }
      if(!this.myContext) {
        this.myCanvas = document.createElement('canvas');
        this.myCanvas.setAttribute('width', 200)
        this.myCanvas.setAttribute('height', 200)
        this.myContext = this.myCanvas.getContext('2d')
        this.myContext.fillStyle = 'rgba(255,30,30, 0.2)';
        this.myContext.strokeStyle = 'rgba(255,30,30, 0.2)';
        this.myContext.globalAlpha = 1;
        this.myContext.lineWidth = 2;
        const centerPoint = new AMap.LngLat(item.gcLng, item.gcLat)
        const pointEN = centerPoint.offset(this.distance,this.distance) //向东1000m，向北1000m的位置的经纬度
        const pointWS = centerPoint.offset(-this.distance,-this.distance) //向西1000m，向南1000m的位置的经纬度
        this.CanvasLayer = new AMap.CanvasLayer({
          canvas: this.myCanvas,
          bounds: new AMap.Bounds(pointWS, pointEN),
          // bounds: new AMap.Bounds([104.08876,30.726911], [104.07831,30.717928]),
          zooms: [12, 18],
          zIndex: 9999
        });
         this.AMap.add(this.CanvasLayer)
         this.drawCanvas()
      } else {
        const centerPoint = new AMap.LngLat(item.gcLng, item.gcLat)
        const pointEN = centerPoint.offset(this.distance,this.distance) //向东1000m，向北1000m的位置的经纬度
        const pointWS = centerPoint.offset(-this.distance,-this.distance) //向西1000m，向南1000m的位置的经纬度
        this.CanvasLayer.setOptions({
          bounds: new AMap.Bounds(pointWS, pointEN),
        })
        this.drawCanvas()
      }

    },
    drawCanvas() {
      this.myContext.clearRect(0, 0, 200, 200)
      this.myContext.globalAlpha = (this.myContext.globalAlpha - 0.01 + 1) % 1;
      this.canvasRadius = (this.canvasRadius + 1) % 100;
      this.myContext.beginPath();
      this.myContext.arc(100, 100, this.canvasRadius, 0, 2 * Math.PI);
      this.myContext.fill();
      this.myContext.stroke();
      this.CanvasLayer.reFresh();
      this.animFrameId = AMap.Util.requestAnimFrame(this.drawCanvas);
    },
    // 点击站点
    handleClickMarker(e) {
      const { data } = e.target.w
      this.currentMarker = data
      this.setINfoWindow(data)
      this.setCircle(data)
      this.setCanvas(data)
      this.AMap.setCenter([data.gcLng, data.gcLat])
      this.$emit('handleChangeStation', {stationCode: data.stationCode, gcLng: data.gcLng, gcLat: data.gcLat,type:1})
    },
    // 添加站点列表marker
    setStationMarkers() {
      if(this.companyMarkerList.length) {
          this.AMap.remove(this.companyMarkerList)
          this.companyMarkerList = []
      }
      console.log('this.stationList', this.stationList)
      this.stationList.forEach((item) => {
        let image = QYZC
        if(item.isAlarm) {
          image = QYCB
        } else {
          image = QYZC
        }
        if(!item.online) image = QYLX
        const icon = new AMap.Icon({
          // 图标尺寸
          size: new AMap.Size(41, 51),
          // 图标的取图地址
          image: image,
          // 图标所用图片大小
          imageSize: new AMap.Size(41, 51),
          // 图标取图偏移量
          imageOffset: new AMap.Pixel(0, 0)
        })
        const markerOptions = {
          map: this.AMap,
          position: new AMap.LngLat(+item.lng, +item.lat),
          offset: new AMap.Pixel(-20.5, -43.5),
          icon: icon,
          zIndex: 9,
          data: item
        }
        let markerIcon = new AMap.Marker(markerOptions)
        this.companyMarkerList.push(markerIcon)
        markerIcon.on('click', (e) => {
          const { data } = e.target.w
          this.$emit('handleClickCompanyMarker', data)
        })
      })

    },
    // 创建飞线
    addFlyLine() {
      return
      if(!this.AMap) return
      const data = this.stationList.map(item => {
        return {
          lnglat: [
            [this.currentMarker.gcLng, this.currentMarker.gcLat],
            [item.lng, item.lat]
          ]
        }
      })
      if(!this.lineLayer) {
        // 创建飞线
        this.lineLayer = new Loca.LinkLayer({
            map: this.AMap,
            fitView: false,
          });
        this.lineLayer.setData(data, {
          lnglat: 'lnglat'
        });
        this.lineLayer.setOptions({
          blendMode: 'lighter',
          style: {
            // 曲率 [-1, 1] 区间
            curveness: 0.005,
            opacity: 0.8,
            color: '#5DFBF9'
          }
        });
      } else {
        this.lineLayer.setData(data, {
          lnglat: 'lnglat'
        });
      }
      this.lineLayer.setzIndex(1000)
      this.lineLayer.render();
    },
    // 重新设置地图中心点
    setNewCenter(data){

      let lat=data.lat
      let lng=data.lng

      // 定义新的中心点坐标
      let newCenter = new AMap.LngLat(lng, lat);
      // 设置新的中心点
      this.AMap.setCenter(newCenter);

    },
  },
  computed: {},
  watch: {
    // 获取站点改变（污染源相关）
    stationList: {
        handler(val,old) {
          // console.log('val',val,'old',old)
            this.$nextTick(() => {
              this.setStationMarkers()
              this.addFlyLine()
              // this.closeInfoWindow1() // 纯关闭弹框
              if(val && val.length>0){
                this.setNewCenter(val[0])
              }
            })
        },
        deep: true,
        immediate: true
    },
    markerList: {
      handler(val,old) {
        // 关闭弹框
        this.$nextTick(() => {
          if(old.length!==0){
            this.closeInfoWindow1()
          }
          setTimeout(()=>{
            this.setMarkers()
          },1000)
        })
      },
      deep: true,
      immediate: true
    },
    // 是否关闭空气站点弹框()
    stationOpen: {
      handler(val,old) {
        console.log(val,'stationOpen')
        // 关闭弹框
        if(val===1){
          this.closeInfoWindow1()
        }
      },
      deep: true,
      immediate: true
    },
    distance(val) {
      if(this.circleMap) {
        this.circleMap.setRadius(this.distance)
      }
      this.setCanvas(this.currentMarker)
      this.addFlyLine()
    }
  },
  components: {  },
  beforeDestroy() {
    this.stationMarkerList.length && this.AMap.remove(this.stationMarkerList)
    this.infoWindow && this.AMap.remove(this.infoWindow)
    this.circleMap && this.AMap.remove(this.circleMap)
    this.companyMarkerList.length && this.AMap.remove(this.companyMarkerList)
    this.lineLayer && this.AMap.remove(this.lineLayer)
    document.querySelector(`canvas.amap-layer`)?.getContext('webgl')?.getExtension('WEBGL_lose_context')?.loseContext()
    this.AMap.remove(this.AMap.getLayers(), this.AMap.getAllOverlays())
    this.AMap && this.AMap.destroy()
    this.AMap = null
    window.closeInfoWindow1 = null
  }
}
</script>

<style lang="less" scoped>
.analysisMap-container {
  width: 100%;
  height: 100%;
  //   position: relative;
  .analysisMapContainer {
    width: 100%;
    height: 100%;
  }
}
</style>
<style lang="less">
.analysisMap-container {
  .amap-info-close {
    display: none;
  }
  .infoWindow {
    pointer-events: auto;
    width: 330px;
    // height: 240px;
    background-repeat: no-repeat;
    background-size: 100% 100% !important;
    padding: 15px 25px;
    position: relative;
    box-sizing: border-box;
    .title {
      position: absolute;
      top: 35px;
      left: 38px;
      font-size: 18px;
      font-family: YouSheBiaoTiHei;
      font-weight: 400;
      color: #ffffff;
      background: linear-gradient(0deg, #56d1ed 0%, #caf6ff 100%);
      background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .close {
      position: absolute;
      width: 22px;
      height: 22px;
      top: 47px;
      right: 25px;
      cursor: pointer;
    }
    .content {
      margin-top: 50px;
      padding: 0 20px 20px;
      .time {
        font-size: 12px;
        font-family: PingFang SC;
        font-weight: 400;
        color: #7798b9;
      }
      .item {
        display: flex;
        .label {
          font-size: 12px;
          font-family: PingFang SC;
          font-weight: 400;
          color: #dcf0ff;
          line-height: 20px;
        }
        .value {
          font-size: 12px;
          font-family: PingFang SC;
          font-weight: 400;
          color: #56a7f1;
          line-height: 20px;
          flex: 1;
        }
        .number {
          font-family: YouSheBiaoTiHei;
          font-size: 18px;
          font-weight: bold;
          margin-right: 10px;
        }
      }
    }
  }
}
</style>
