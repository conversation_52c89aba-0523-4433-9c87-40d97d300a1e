<template>
  <div :id="id" />
</template>

<script>
import AMapLoader from '@amap/amap-jsapi-loader'
import { Scene } from '@antv/l7'
import { GaodeMap as AMap } from '@antv/l7-maps'

import {
  initBridge,
  initWaterStationBuilding,
  initRiver,
} from './buildingModel'
export default {
  name: 'baseMap',
  props: {
    mapSeting: {
      type: Object,
      default: () => {},
    },
    id: {
      type: String,
      default: 'map',
    },
  },
  data() {
    return {
      // 地图相关设置 S
      // 地图中心
      mapCenter: [104.069738, 30.715603],
      // mapStyle: 'amap://styles/0739a6632d849ae1b5e6f1c52f7d12c3',
      // mapToken: '4a7187e9dca35f72b98443b118a0fd9f',
      mapStyle: 'amap://styles/83bd2183c46b8370a2efad5959b8b153',
      mapToken: '8ad4fb917ca55ab7412a69c3fef11230',
      mapPlugin: ['Map3D', 'AMap.CircleEditor', 'AMap.Object3DLayer'],
      scene: {},
    }
  },
  beforeCreate(){
    AMapLoader.reset()
  },
  mounted() {
    this.initScene()
  },
  beforeDestroy() {
    this.scene.destroy()
  },
  methods: {
    /**
     * 初始化场景
     */
    initScene() {
      const setting = Object.assign(
        {
          // 燃气地图样式
          // style: 'amap://styles/e93ce7f15a8a0212d1d464d1af829942',
          style: this.mapStyle,
          // style: 'dark',
          // center: [120.173104, 30.244072],
          viewMode: '3D', // 使用3D视图
          center: this.mapCenter,
          pitch: 65,
          zoom: 17,
          expandZoomRange: true,
          zooms: [3, 20],
          // rotation: 2.24, // 358.7459759480504
          // minZoom: 17,
          token: this.mapToken,
          plugin: this.mapPlugin,
          showLabel: false,
        },
        this.mapSeting
      )
      const sceneObj = new Scene({
        id: this.id,
        logoVisible: false,
        map: new AMap(setting),
      })
      this.scene = sceneObj
      this.scene.on('loaded',()=>{
        initRiver(this.scene)
        initBridge(this.scene)
        initWaterStationBuilding(this.scene)
      })
    },
  },
}
</script>

<style lang="less" scoped>
#emergencyCommandMap {
  width: 100%;
  height: 100%;
  position: relative;
}
</style>
