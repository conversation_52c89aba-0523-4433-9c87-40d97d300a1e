<template>
  <div>
    <div id="statEachrts"></div>
    <footer class="footer">
      <div>
        <span>本年度同比增长率：</span>
        <span style="color: #2cffc1">{{ statisticsData.yearOnYear }}%</span>
        <el-tooltip content="与去年同周期相比较" style="margin-left: 6px">
          <i class="el-icon-question"></i>
        </el-tooltip>
      </div>
      <div>
        <span>上年度达标率：</span>
        <span>{{ statisticsData.lastYearRate }}%</span>
      </div>
    </footer>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import 'echarts-gl'
import dayjs from 'dayjs'
export default {
  name: 'cardBox',
  props: {
    statisticsData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      thisYear: dayjs().format('YYYY') + '年',
      lastYear: Number(dayjs().format('YYYY') - 1) + '年',
    }
  },
  watch: {
    statisticsData: {
      handler(newVal) {
        this.creatEharts(newVal)
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {
    this.creatEharts(this.statisticsData)
  },
  methods: {
    creatEharts(data) {
      let option = {}
      let myChart = echarts.init(document.getElementById('statEachrts'))

      // 获取当前年份达标率
      const currentValue = data.thisYearRate || 0

      option = {
        backgroundColor: 'transparent',
        series: [
          {
            type: 'gauge',
            startAngle: 188,
            endAngle: -8,
            center: ['50%', '80%'],
            radius: '135%',
            min: 0,
            max: 100,
            splitNumber: 10,
            itemStyle: {
              color: '#58D9F9',
            },
            progress: {
              show: true,
              roundCap: false,
              width: 8,
            },
            pointer: {
              // 梯形指针：从中心细到外端宽（更短更紧凑）
              icon: 'path://M-2,0 L2,0 L6,-10 L-6,-10 Z',
              length: '23%', // 增加长度让变化更明显
              width: 8,
              offsetCenter: [0, '-60%'], // 减少偏移让指针更可见
              itemStyle: {
                color: '#24D8FF',
                shadowColor: 'rgba(36, 216, 255, 0.3)',
                shadowBlur: 8,
              },
            },
            axisLine: {
              roundCap: false,
              lineStyle: {
                width: 8,
                color: [[1, '#173255']],
              },
            },
            axisLabel: {
              show: false,
            },
            axisTick: {
              show: false,
              splitNumber: 2,
              lineStyle: {
                width: 2,
                color: '#B6D0D8',
              },
            },
            splitLine: {
              show: false,
              length: 12,
              lineStyle: {
                width: 3,
                color: '#B6D0D8',
              },
            },
            title: {
              show: false,
            },
            detail: {
              width: '60%',
              // lineHeight: -80,
              height: 40,
              offsetCenter: [0, '-10%'],
              valueAnimation: true,
              formatter: function (value) {
                return '{value|' + value.toFixed(1) + '}{unit|%}'
              },
              rich: {
                value: {
                  fontSize: 32,
                  fontWeight: 'bolder',
                  color: '#24D8FF',
                },
                unit: {
                  fontSize: 16,
                  color: '#24D8FF',
                  padding: [0, 0, -10, 5],
                },
              },
            },
            data: [
              {
                value: currentValue,
                name: '达标率',
              },
            ],
          },
        ],
      }

      if (option) {
        myChart.setOption(option)
        // 仪表盘动画效果
        this.animateGauge(myChart, currentValue)
      }
    },
    animateGauge(chart, targetValue) {
      let currentValue = 0
      const increment = targetValue / 50 // 分50步完成动画
      const timer = setInterval(() => {
        currentValue += increment
        if (currentValue >= targetValue) {
          currentValue = targetValue
          clearInterval(timer)
        }
        chart.setOption({
          series: [
            {
              data: [
                {
                  value: currentValue,
                  name: '达标率',
                },
              ],
            },
          ],
        })
      }, 30)
    },
  },
}
</script>

<style lang="less">
#statEachrts {
  position: relative;
  height: 190px;
  width: 100%;
  &::after {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    content: '';
    width: 291px;
    height: 170px;
    background: url(~@/assets/noise/<EMAIL>) center / 100% 100% no-repeat;
  }
}

.footer {
  height: 65px;
  background: url(~@/assets/noise/<EMAIL>) center / 100% 100% no-repeat;
  color: #a6cbe2;
  text-align: center;
}
</style>
