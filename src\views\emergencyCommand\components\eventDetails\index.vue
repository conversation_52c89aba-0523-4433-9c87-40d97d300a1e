<template>
  <card title="事件详情">
    <!-- 事件详情卡片 -->
    <eventDetailsCar
      :title="detail.title || '本地区暂无应急事件发生'"
      :address="detail.address || '————'"
      :content="detail.content || '暂无任务描述'"
      :eventType="detail.eventType || '————'"
      :eventLevel="detail.eventLevel || '————'"
      :eventTime="detail.eventTime || ''"
    ></eventDetailsCar>
  </card>
</template>

<script>
  import card from '@/components/card/index';
  import eventDetailsCar from '@/components/eventDetailsCar/index.vue'
  export default {
    name: 'eventDetails',
    components: {
      eventDetailsCar,
      card
    },
    props: {
      detail: {
        type: Object,
        default: () => ({}),
      },
    }
  }
</script>

<style lang="less" scoped></style>
