<template>
  <card title="任务反馈">
    <!-- <Swiper
      style="height: 480px"
      :slides-per-view="2"
      :mousewheel="true"
      direction="vertical"
      :space-between="0"
    >
      <SwiperSlide
        style="height: 240px"
        v-for="(obj, ind) in excutorArr"
        :key="ind + 'fbi'"
      > -->
      <div class="task-scoll-box">
        <template v-if="excutorArr.length">
          <taskFeedbackItem
            v-for="(obj, ind) in excutorArr"
            :key="ind + 'tfbi'"
            :hiddenbtom="ind == excutorArr.length - 1"
            :excutor="obj"
          ></taskFeedbackItem>
        </template>
        <template v-else>
          <taskFeedbackItem
            v-for="i in 2"
            :key="i"
            :hiddenbtom="i==2"
            :excutor="noneObj"
          ></taskFeedbackItem>
        </template>
      </div>
      <!-- </SwiperSlide>
    </Swiper> -->

    <!-- 日历组件 -->
    <!-- <calendar></calendar> -->
    <!--  巡查任务组件-->
    <!-- <patrolTask></patrolTask> -->
    <!--  应急事件组件-->
    <!-- <emergencyEvent></emergencyEvent> -->
    <!-- 事件详情卡片 -->
    <!-- <eventDetailsCar></eventDetailsCar> -->
    <!-- 人员信息卡片 -->
    <!-- <personInfoCar></personInfoCar> -->
    <!-- 巡查计划卡片 -->
    <!-- <inspectStatistical></inspectStatistical> -->
  </card>
</template>

<script name="taskFeedback">
  import card from '@/components/card/index.vue'
  import taskFeedbackItem from '@/components/taskFeedbackItem/index.vue'
  // import calendar from '@/components/calendar/index.vue'
  // import patrolTask from '@/components/patrolTask/index.vue'
  // import emergencyEvent from '@/components/emergencyEvent/index.vue'
  // import eventDetailsCar from '@/components/eventDetailsCar/index.vue'
  // import personInfoCar from '@/components/personInfoCar/index.vue'
  // import inspectStatistical from '@/components/InspectStatistical/index.vue'
  import zwtp from '@/assets/images/<EMAIL>';
  export default {
    name: 'taskFeedback',
    components: {
      taskFeedbackItem,
      card,
    },
    props: {
      taskbackdata: {
        type: Object,
        default: () => ({
          data: [],
        }),
      },
    },
    data() {
      return {
        noneObj: {
          userName: '',
          departmentName: '',
          completeTime: '',
          content: '该任务暂无人员回复',
          taskAnnexList: [
              {
                taskAnnexId: 1,
                annexUrl: zwtp,
              },
            ],
        },
        excutorArr: [
          // {
          //   userName: '徐金红',
          //   departmentName: '金牛生态环境局',
          //   completeTime: '2022-03-04 14:24',
          //   content: '晚点我测温完成完成完成完成',
          //   taskAnnexList: [
          //     {
          //       taskAnnexId: 1,
          //       annexUrl:
          //         'https://oss-chengdu.vankeytech.com:9000/vankeytech-ep/information/jt8i3y163kcc3z8spaefjt16jjwh9bvo.jpg',
          //     },
          //   ],
          // },
        ],
      }
    },
    watch: {
      taskbackdata: {
        handler(nval, oval) {
          // console.log(nval, '反馈的数据改变')
          if (nval && nval.data && nval.data.length) {
            this.excutorArr = nval.data
          } else {
            this.excutorArr = []
          }
        },
        deep: true,
        immediate: true,
      },
    },
    mounted() {},
    methods: {},
  }
</script>

<style lang="less" scoped>
.task-scoll-box{
  height: 480px;
  overflow: scroll;
  &::-webkit-scrollbar{
    display: none !important;
  }
}
</style>
