<template>
  <div v-if="(propData.data1 && propData.data1.length) || (propData.data2 && propData.data2.length)" id="charts" :style="{ width, height }" />
  <div v-else :style="{ height: height, width: width }" class="no-data">
    <span>暂无数据</span>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  components: {},
  props: {
    width: {
      type: String,
      default: '100%',
    },
    height: {
      type: String,
      default: '330px',
    },
    propData: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      myChart: null,
      options: null,
    }
  },
  watch: {
    propData(newValue, oldValue) {
      if (this.myChart) {
        this.myChart.clear()
        this.myChart = null
      }
      if ((this.propData && this.propData.data1 && this.propData.data1.length) || (this.propData && this.propData.data2 && this.propData.data2.length)) {
        this.$nextTick(() => {
          this.charts()
        })
      }
    },
  },
  mounted() {
    if ((this.propData && this.propData.data1 && this.propData.data1.length) || (this.propData && this.propData.data2 && this.propData.data2.length)) {
      this.$nextTick(() => {
        this.charts()
      })
    }
  },
  methods: {
    charts() {
      if (this.myChart) {
        this.myChart.dispose()
        this.myChart = null
      }
      this.myChart = echarts.init(document.querySelector('#charts'))

      // 安全处理data1
      const data1 =
        this.propData.data1 && this.propData.data1.length
          ? this.propData.data1.map((v, i) => ({
              value: v,
              itemStyle: {
                color: (this.propData.max || this.propData.max === 0 || this.propData.max === '0') && v > this.propData.max ? 'red' : '#3BB66F',
                borderWidth: 2,
              },
            }))
          : []

      // 安全处理data2
      const data2 =
        this.propData.data2 && this.propData.data2.length
          ? this.propData.data2.map((v, i) => ({
              value: v,
              itemStyle: {
                color: '#1890FF',
                borderWidth: 2,
              },
            }))
          : []

      this.options = {
        color: ['#3BB66F', '#1890FF'],
        grid: {
          left: '5%',
          bottom: '0%',
          containLabel: true,
        },
        legend: {
          data: [this.propData.name1, this.propData.name2],
          right: '10%',
          textStyle: {
            color: 'white',
          },
          lineStyle: {
            color: '#fff',
          },
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          axisTick: {
            show: false,
          },
          axisLabel: {
            color: 'white',
          },
          splitLine: {
            show: true,
            lineStyle: {
              type: 'dashed',
            },
          },
          data: this.propData.xData,
        },
        yAxis: [
          {
            name: this.propData.unit1,
            type: 'value',
            position: 'left',
            axisLine: {
              show: true,
              lineStyle: {
                color: '#3BB66F',
              },
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              show: true,
              lineStyle: {
                type: 'dashed',
              },
            },
          },
          {
            name: this.propData.unit2,
            type: 'value',
            position: 'right',
            axisLine: {
              show: true,
              lineStyle: {
                color: '#1890FF',
              },
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              show: false,
            },
          },
        ],
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985',
            },
          },
        },
        series: [
          {
            name: this.propData.name1,
            type: 'line',
            smooth: true,
            symbolSize: 8,
            zlevel: 1,
            yAxisIndex: 0,
            lineStyle: {
              normal: {
                color: '#3BB66F',
                width: 2,
              },
            },
            areaStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: 'rgba(59, 182, 111, 0.3)',
                    },
                    {
                      offset: 1,
                      color: 'rgba(255, 255, 255, 0)',
                    },
                  ],
                  false
                ),
              },
            },
            data: data1,
          },
          {
            name: this.propData.name2,
            type: 'line',
            smooth: true,
            symbolSize: 8,
            zlevel: 1,
            yAxisIndex: 1,
            lineStyle: {
              normal: {
                color: '#1890FF',
                width: 2,
              },
            },
            areaStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: 'rgba(24, 144, 255, 0.3)',
                    },
                    {
                      offset: 1,
                      color: 'rgba(255, 255, 255, 0)',
                    },
                  ],
                  false
                ),
              },
            },
            data: data2,
          },
        ],
      }
      this.myChart.setOption(this.options)
    },
    setOption(myChart) {
      myChart.setOption(this.options)
    },
  },
}
</script>
<style lang="less" scoped>
#charts {
  width: 100%;
}
.no-data {
  position: relative;
  span {
    position: absolute;
    top: 50%;
    left: 50%;
    font-size: 20px;
    color: #999999;
    transform: translate(-50%, -50%);
  }
}
</style>
