<template>
  <a-spin :spinning="loading" style="min-height: 200px">
    <div v-if="dataList.length" class="log-list" style="padding-left: 18px">
      <div v-for="item in dataList" :key="item.planId" style="cursor: pointer" @click="detail(item)">
        <header style="font-size: 18px; color: #3b8dea">
          <img src="@/assets/waterPng/<EMAIL>" alt="" style="width: 35px; height: 35px; margin-left: -34px" />
          <span>{{ item.createTime }}</span>
        </header>
        <section class="section" style="padding: 17px 20px">
          <div style="color: #d9ebff; font-size: 16px; display: flex; justify-content: space-between">
            <span>{{ item.title }}</span>
            <button v-if="item.completeStatus === 0" class="status-btn" style="color: #fac568">处理中</button>
            <button v-if="item.completeStatus === 1" class="status-btn ok" style="color: #00e5e8">已完成</button>
          </div>
          <div style="font-size: 14px; margin-top: 10px">
            <span style="color: #95aabe">站点类型：</span>
            <span style="color: #dcf0ff">{{ item.stationTypeName }}</span>
          </div>
        </section>
      </div>
      <a-modal v-if="dialogVisible" v-model="dialogVisible" class="water-detail-dialog" :footer="null" centered destroy-on-close>
        <header class="header">
          <span class="title">任务执行流程</span>
          <a-icon type="close" style="color: #1598cf; font-size: 18px; cursor: pointer; margin-top: 10px" @click="dialogVisible = false" />
        </header>
        <OrgTree v-if="formData" :orgTreeList="formData.taskNodeList" :typeId="formData.typeId" style="height: 670px; margin-top: 12px;" />
      </a-modal>
    </div>
    <div v-else class="empty">暂无数据</div>
  </a-spin>
</template>

<script>
import {getTaskDetail, monitorPageTask} from '@/api/water'
import OrgTree from '@/views/task-management/components/org-tree/index.vue'

export default {
  name: 'MonitorLogList',
  props: {
    data: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      dataList: [],
      loading: false,
      formData: null,
      detailLoading: false,
      dialogVisible: false,
    }
  },
  created() {
    this.handleQuery()
  },
  computed: {},
  watch: {
    // currentStation:{
    //
    // },
  },
  components: {
    OrgTree,
  },

  methods: {
    handleQuery() {
      this.loading = true
      monitorPageTask(this.data.monitorId)
        .then((res) => {
          this.dataList = res.data.data.records
        })
        .finally(() => {
          this.loading = false
        })
    },

    detail(row) {
      this.formData = null
      this.detailLoading = true
      getTaskDetail(row.taskId).then(res => {
        this.formData = res.data.data
      }).finally(() => {
        this.detailLoading = false
      })

      this.dialogVisible = true
    }
  },
}
</script>

<style lang="less" scoped>
.section {
  background: linear-gradient(-90deg, rgba(0, 52, 106, 0) 0%, rgba(7, 67, 142, 0.2) 100%);
}

.status-btn {
  width: 79px;
  height: 26px;
  background: transparent;
  background: url(~@/assets/waterPng/<EMAIL>) center / 100% 100% no-repeat;
  outline: none;
  border: none;
  font-size: 14px;
  cursor: pointer;
  &.ok {
    background: url(~@/assets/waterPng/<EMAIL>) center / 100% 100% no-repeat;
  }
}

.log-list {
  position: relative;
  &::after {
    content: '';
    position: absolute;
    width: 2px;
    inset: 20px 0 0;
    border-left: 2px solid #3181d8;
  }
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-left: 30px;
  padding-top: 4px;
  padding-right: 8px;
  .title {
    font-size: 16px;
    color: #dcf0ff;
  }
}

.empty {
  display: grid;
  place-items: center;
  min-height: 400px;
  letter-spacing: 1.5px;
  user-select: none;
}
</style>
