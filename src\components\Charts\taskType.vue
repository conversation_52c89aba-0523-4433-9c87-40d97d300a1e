<style lang="less" scoped>
.no-data {
  font-size: 0.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
<template>
  <div v-if="propData.dataList.length > 0" :id="id" :style="{ height: height, width: width }" />
  <div v-else class="no-data" :style="{ height: height, width: width }">暂无联动任务</div>
</template>

<script lang="ts">
import * as echarts from 'echarts'
import { Component, Prop, Watch } from 'vue-property-decorator'
import { mixins } from 'vue-class-component'
import ResizeMixin from './mixins/resize'
interface AirData {
  bottomList: string[]
  dataList: string[]
  total: number | string
}
@Component({
  name: 'LineChartDashed',
})
export default class extends mixins(ResizeMixin) {
  // @Prop({ default: "chart" }) private className!: string;
  @Prop({ default: 'chart' }) private id!: string
  @Prop({ default: '200px' }) private width!: string
  @Prop({ default: '200px' }) private height!: string
  @Prop({ required: true }) private propData!: AirData
  @Prop({ required: false, default: 'rgba(14,156,255,1)' })
  private bgColor!: string
  @Prop({ required: false, default: false }) private smooth!: boolean
  @Prop({ required: false, default: true }) private bgColorState!: boolean
  @Prop({ required: false, default: '' }) private xText!: string
  @Watch('propData', { immediate: true, deep: true }) public onMsgChanged(newValue: AirData, oldValue: AirData) {
    if (this.propData) {
      if (this.chart) {
        this.chart.clear()
        this.chart.dispose()
        this.chart = null
      }
      this.$nextTick(() => {
        this.initChart()
      })
    }
  }
  mounted() {
    if (this.propData) {
      this.initChart()
    }
  }
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.clear()
    this.chart.dispose()
    this.chart = null
  }

  private initChart() {
    let color = ['#24A4FF', '#00CFB9', '#E8B072', '#FF6969']
    let chartData = this.propData.dataList
    let arrName = []
    let arrValue = []
    let sum = 0
    let pieSeries: any = [],
      lineYAxis: any = []

    // 数据处理
    chartData.forEach((v: any, i) => {
      arrName.push(v.name)
      arrValue.push(v.value)
      sum = sum + v.value
    })

    // 图表option整理
    chartData.forEach((v: any, i) => {
      pieSeries.push({
        type: 'pie',
        clockWise: false,
        hoverAnimation: false,
        radius: [85 - i * 15 + '%', 77 - i * 15 + '%'],
        center: ['30%', '50%'],
        label: {
          show: false,
        },
        data: [
          {
            value: v.value,
            name: v.name,
          },
          {
            value: sum - v.value,
            name: '',
            itemStyle: {
              color: 'rgba(0,0,0,0)',
            },
          },
        ],
      })
      pieSeries.push({
        name: '',
        type: 'pie',
        silent: true,
        z: 1,
        clockWise: false, //顺时加载
        hoverAnimation: false, //鼠标移入变大
        radius: [85 - i * 15 + '%', 77 - i * 15 + '%'],
        center: ['30%', '50%'],
        label: {
          show: false,
        },
        data: [
          {
            value: 7.5,
            itemStyle: {
              color: 'rgba(255,255,255,0.1)', //圆环颜色
            },
          },
          {
            value: 2.5,
            name: '',
            itemStyle: {
              color: 'rgba(0,0,0,0)',
            },
          },
        ],
      })
      /* eslint-disable @typescript-eslint/ban-ts-ignore */
      //@ts-ignore
      v.percent = (sum ? ((v.value / sum) * 100).toFixed(1) : 0) + '%'
      lineYAxis.push({
        value: i,
        textStyle: {
          rich: {
            circle: {
              color: color[i],
              padding: [0, 5],
            },
          },
        },
      })
    })

    if (this.chart === null || this.chart === undefined) {
      const chartDom = document.getElementById(this.id) as HTMLDivElement
      if (!chartDom) return
      this.chart = echarts.init(chartDom)
    }
    // SO2:1 NO2:1 O3:1 CO:1 PM10:1 PM:2.5
    /* eslint-disable @typescript-eslint/ban-ts-ignore */
    //@ts-ignore
    this.chart.setOption({
      color: color,
      title: [
        {
          text: '任务总数',
          x: '30%',
          y: '39%',
          textAlign: 'center',
          textStyle: {
            fontSize: '12',
            color: 'white',
            textAlign: 'center',
          },
        },
        {
          text: this.propData.total,
          left: '30%',
          top: '49%',
          textAlign: 'center',
          textStyle: {
            fontSize: '18',
            color: '#15FEFE',
            textAlign: 'center',
          },
        },
      ],
      legend: {
        orient: 'vertical', // 设置垂直排列
        textStyle: {
          rich: {
            name: {
              width: 60,
              align: 'left',
            },
            value: {
              width: 60,
              align: 'center',
            },
            percent: {
              width: 40,
              align: 'right',
            },
          },
          color: '#fff',
        },
        top: 40,
        itemGap: 12, // 增加项目间距
        right: 10, // 给右边留一些边距
        itemWidth: 16,
        itemHeight: 10,
        formatter: (params: any, data: any) => {
          let item: any = chartData.filter((items: any) => items.name === params)
          return '{name|' + item[0].name + '}{value|' + item[0].value + '次' + '}{percent|' + item[0].percent + '}'
        },
      },

      yAxis: [
        {
          type: 'category',
          inverse: true,
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            interval: 0,
            inside: true,
            show: false,
          },
          data: lineYAxis,
        },
      ],
      xAxis: [
        {
          show: false,
        },
      ],
      grid: {
        top: 20,
      },
      series: pieSeries,
    } as echarts.EChartsOption)
  }
}
</script>
