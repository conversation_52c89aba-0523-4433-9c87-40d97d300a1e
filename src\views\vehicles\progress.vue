<template>
    <div class="slider" :style="{'pointer-events': locationRecordList.length === 0 ? 'none' : ''}" ref="slider">
        <div class="process" :style="{width}"></div>
        <div class="thunk" ref="trunk" :style="{left}">
            <div class="block"></div>
            <!-- <div class="tips">
              <span>{{scale*100}}</span>
              <i class="fas fa-caret-down"></i>
            </div> -->
        </div>
        <!-- <div class="opacity" style="left:-21px;top:-5px;">0%</div> -->
<!--        <div class="opacity" style=" color:#83C8FE; right:-60px;top:-5px;">{{parseInt(scale*100)}}%</div>-->
    </div>
</template>
<script>
/*
 * min 进度条最小值
 * max 进度条最大值
 * v-model 对当前值进行双向绑定实时显示拖拽进度
 * */
export default {
    props: {
        min: {
            type: Number,
            default: 0
        },
        max: {
            type: Number,
            default: 100
        },
        percent: {
            type: Number,
            default: 100
        },
        list:{
            type:Object,
            default: () => {}
        },
        locationRecordList: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            slider: null, //滚动条DOM元素
            thunk: null, //拖拽DOM元素
            per: this.percent //当前值
        };
    },
    //渲染到页面的时候
    mounted() {
        this.slider = this.$refs.slider;
        this.thunk = this.$refs.trunk;
        var _this = this;
        this.thunk.onmousedown = function(e) {
            var width = parseInt(_this.width);
            var disX = e.clientX;
            document.onmousemove = function(e) {
                // value, left, width
                // 当value变化的时候，会通过计算属性修改left，width

                // 拖拽的时候获取的新width
                var newWidth = e.clientX - disX + width;
                // 拖拽的时候得到新的百分比
                var scale = newWidth / _this.slider.offsetWidth;
                console.log(scale, 'scale')
                _this.per = Math.ceil((_this.max - _this.min) * scale + _this.min);
                _this.per = Math.max(_this.per, _this.min);
                _this.per = Math.min(_this.per, _this.max);
                _this.$emit("SetOpacityConfig",(_this.per - _this.min) / (_this.max - _this.min));
            };
            document.onmouseup = function() {
                document.onmousemove = document.onmouseup = null;
            };
            return false;
        };
        // this.slider.onmousedown = function (e) {
        //     console.log(document.body.clientWidth - 130 - e.clientX,e,'e')
        //     var scale = (e.clientX - 25) / _this.slider.offsetWidth;
        //     _this.per = Math.ceil((_this.max - _this.min) * scale + _this.min);
        //     _this.per = Math.max(_this.per, _this.min);
        //     _this.per = Math.min(_this.per, _this.max);
        //     console.log(_this.per, 'scale')
        // }
    },
    computed: {
        // 设置一个百分比，提供计算slider进度宽度和trunk的left值
        // 对应公式为  当前值-最小值/最大值-最小值 = slider进度width / slider总width
        // trunk left =  slider进度width + trunk宽度/2
        scale() {
            return (this.per - this.min) / (this.max - this.min);
        },
        width() {
            if (this.slider) {
                return this.slider.offsetWidth * this.scale + "px";
            } else {
                return 0 + "px";
            }
        },
        left() {
            if (this.slider) {
                return (
                    this.slider.offsetWidth * this.scale -
                    this.thunk.offsetWidth / 2 +
                    "px"
                );
            } else {
                return 0 + "px";
            }
        }
    },
    watch: {
        scale(val, oldVal) {
            // console.log('per',val, oldVal)
            // this.$emit("SetOpacityConfig",val);
        },
        percent(val) {
            this.per = val
        }
    }
};
</script>
<style scoped>
.clear:after {
    content: "";
    display: block;
    clear: both;
}
.slider {
    position: relative;
    margin: 0.1rem 0 0.05rem 0.25rem;
    width: 3.1rem;
    height: 0.07rem;
    background:rgba(255, 255, 255,0);
    border: 1px solid #83C8FE;
    border-radius: 0.05rem;
    cursor: pointer;
}
.slider .opacity {
    position: absolute;
    font-size: 0.2rem;
    font-family: Agency FB;
    color: rgb(255, 255, 255);
    font-weight: bold;
    line-height: .15rem;
}
.slider .process {
    position: absolute;
    left: 0;
    top: 0;
    width: 1.12rem;
    height: 0.05rem;
    border-radius: 0.05rem;
    background: #83C8FE;
}
.slider .thunk {
    position: absolute;
    left: 1rem;
    top: -4px;
    width: 0.2rem;
    height: 0.2rem;
}
.slider .block {
    margin-top:-3px;
    margin-left:3px;
    width: 8px;
    height: 20px;
    border-radius: 5px;
    border: 1px solid #83C8FE;
    background: #83C8FE;
    transition: 0.2s all;
}
.slider .tips {
    position: absolute;
    left: -2px;
    bottom: 24px;
    min-width: 10px;
    text-align: center;
    padding: 0px 1px 0px 1px;
    background: #83C8FE;
    border-radius: 2px;
    height: 16px;
    line-height: 16px;
    color: #fff;
    font-weight: bold;
    font-size: 14px;
}
.slider .tips i {
    position: absolute;
    margin-left: -5px;
    left: 50%;
    bottom: -9px;
    font-size: 14px;
    color: #83C8FE;
}
.slider .block:hover {
    transform: scale(1.1);
    opacity: 0.9;
}
</style>
