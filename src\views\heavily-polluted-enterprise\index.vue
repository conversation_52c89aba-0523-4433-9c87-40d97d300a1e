<style lang="less" scoped>
.common-main {
  position: relative;
  overflow: hidden;
  width: 100%;
  padding: 0;
  .left-part {
    > div {
      width: 4rem;
    }

    position: absolute;
    top: 0.5rem;
    left: 0.5rem;
    .title {
      font-size: 0.2rem;
      cursor: pointer;
    }
    .flex-number {
      cursor: pointer;
      display: flex;
      margin-top: 0.18rem;
      > div {
        border: 1px solid RGBA(33, 109, 253, 1);
        background-color: RGBA(35, 211, 255, 0.6);
        font-size: 0.4rem;
        margin-right: 0.08rem;
        text-align: center;
        padding: 0rem 0.12rem;
        font-family: "300-CAI978";
      }
    }
    .video-main {
      margin-top: 3rem;
      .sub-title {
        > img {
          width: 50%;
          height: 0.1rem;
        }
      }
      .box-title {
        justify-content: start;
        .title {
          margin-right: 0.5rem;
        }
      }
      .title-video {
        display: flex;
        align-items: center;
      }
      img {
        width: 1.2rem;
      }
    }
  }
  .center-map {
    position: absolute;
    height: calc(1080px - 0.94rem);
    width: 100%;
    // margin-top: 0.15rem;
    > div {
      width: 100%;
      height: 100%;
    }
  }
  .right-part {
    position: absolute;
    top: 0;
    right: 0;
    height: calc(1080px - 1rem);
    width: 4.6rem;
    padding: 0.3rem 0.25rem 0;
    background-image: url(../../assets/air_bg.png) !important;
    background-size: 100% 100% !important;
    > .enterprise {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      // justify-content: space-between;
      > .enterprise-main {
        width: 100%;
        height: 32%;
      }
      > .pollute-main {
        height: 42%;
      }
      > .discharge-main {
        height: 26%;
      }
      > .construction-site {
      }
      > .monitor-main {
      }
    }
    > .construction-plant {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      > div:first-child {
      }
      > div:last-child {
        margin-top: 0.2rem;
      }
    }
    .sub-title {
      margin: 0 0 0.05rem;
    }
    .pollution-main {
      .update-time-pollution {
        font-size: 0.12rem;
        color: white;
        text-align: right;
      }
      .flex-box {
        width: 100%;
        display: flex;
        justify-content: space-between;
        font-size: 0.14rem;
        margin-top: 0.05rem;
        > div {
          .type-card {
            background: url("../../assets/card-top.png") no-repeat center;
            background-size: 100% 100%;
            height: 0.32rem;
            line-height: 0.32rem;
            text-align: center;
            margin-bottom: 0.05rem;
          }
        }
        > div:first-child {
          width: 40%;
          > div {
            margin-top: 0.3rem;
            .name {
              line-height: 0.12rem;
            }
            .number {
              font-size: 0.24rem;
              color: RGBA(0, 229, 255, 1);
              margin-right: 0.06rem;
              font-family: "300-CAI978";
            }
          }
          > div:first-child {
            margin-top: 0;
          }
          > div:nth-child(2) {
            margin-top: 0.08rem;
          }
        }
        > div:last-child {
          width: 60%;
          .change-box {
            width: 115%;
            display: flex;
            box-sizing: border-box;
            > div {
              width: 1.2rem;
              height: 0.2rem;
              -webkit-clip-path: polygon(25% 0%, 100% 0%, 75% 100%, 0% 100%);
              clip-path: polygon(25% 0%, 100% 0%, 75% 100%, 0% 100%);
              background-color: RGBA(6, 58, 114, 1);
              text-align: center;
              height: 0.24rem;
              line-height: 0.24rem;
              cursor: pointer;
            }
            > div:nth-child(2) {
              position: relative;
              right: 0.2rem;
            }
            > div:last-child {
              -webkit-clip-path: polygon(25% 0%, 100% 0%, 100% 100%, 0% 100%);
              clip-path: polygon(25% 0%, 100% 0%, 100% 100%, 0% 100%);
              position: relative;
              right: 0.4rem;
            }
            > .active {
              background-color: RGBA(0, 132, 255, 1);
            }
          }
        }
      }
    }
    .heavy-pollution-table,
    .table-site {
      width: 100%;
      border: 1px solid rgba(54, 218, 234, 0.42);
      .table-name {
        font-size: 0.12rem;
        width: 0.76rem;
        background: rgba(15, 36, 94, 1);
        text-align: center;
      }
      .table-text {
        padding-left: 0.06rem;
      }
      td {
        font-size: 0.12rem;
        height: 0.34rem;
        color: #ffffff;
        font-weight: 500;
      }
    }
    .table-site {
      width: 93%;
      .PM10-detail {
        display: flex;
        align-items: center;
        > span:first-child {
          color: rgb(51, 231, 51);
          font-size: 0.2rem;
        }
        > span:nth-child(2) {
          margin: 0 0.1rem;
        }
        > span:last-child {
          background: rgb(51, 231, 51);
          border-radius: 1px;
          padding: 0 0.05rem;
        }
      }
    }
    .table-data1 {
      cursor: pointer;
      width: 100%;
      .table-data-thead {
        .tr {
          display: flex;
          .th {
            color: #ffffff;
            padding: 0;
            text-align: center;
            border: none;
            font-size: 0.16rem;
          }
          > :nth-of-type(1) {
            width: 20%;
          }
          > :nth-of-type(2) {
            width: 40%;
            text-align: left;
          }
          > :nth-of-type(3) {
            width: 20%;
          }
          > :nth-of-type(4) {
            width: 20%;
          }
        }
      }
      .table-data-tbody {
        height: 1.36rem;
        overflow: hidden;
        .tr {
          display: flex;
          .td {
            color: #ffffff;
            padding: 0;
            font-size: 0.14rem;
            text-align: center;
            height: 0.34rem;
            line-height: 0.34rem;
            border: none;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          > :nth-of-type(1) {
            width: 20%;
            div {
              margin-top: 0.07rem;
            }
          }
          > :nth-of-type(2) {
            width: 40%;
            text-align: left;
          }
          > :nth-of-type(3) {
            width: 20%;
          }
          > :nth-of-type(4) {
            width: 20%;
          }
        }
      }
      .table-data-tbody .tr:nth-child(odd) {
        background: #0f245e;
      }
      .table-data-tbody .tr:nth-child(even) {
        background: transparent;
      }
      .tdBefore {
        //前三
        width: 0.26rem;
        display: block;
        text-align: center;
        height: 0.2rem;
        line-height: 0.2rem;
        background: rgba(255, 133, 9, 1);
        border-radius: 0.06rem;
        margin: 0 auto;
      }
      .tdAfter {
        //前三外
        width: 0.26rem;
        display: block;
        text-align: center;
        height: 0.2rem;
        line-height: 0.2rem;
        background: rgba(18, 136, 226, 1);
        border-radius: 0.06rem;
        margin: 0 auto;
      }
    }
  }
}
.enterprise-search {
  position: absolute;
  top: 0.5rem;
  right: 50%;
  display: flex;
  align-items: center;
  width: 7.5rem;
  height: 0.6rem;
  margin-right: -3.5rem;
  > :nth-of-type(1) {
    display: flex;
    align-items: center;
    width: 6.5rem;
    height: 0.6rem;
    background: rgba(5, 7, 95, 0.7);
    border: 1px solid #034fa8;
    input {
      width: 100%;
      margin-left: 0.25rem;
      // margin-right: 0.25rem;
      background: transparent;
      border: none;
      outline: none;
      font-size: 0.22rem;
    }
    input::-webkit-input-placeholder {
      color: #ffffff;
    }
    input::-moz-placeholder {
      /* Mozilla Firefox 19+ */
      color: #ffffff;
    }
    input:-moz-placeholder {
      /* Mozilla Firefox 4 to 18 */
      color: #ffffff;
    }
    input:-ms-input-placeholder {
      /* Internet Explorer 10-11 */
      color: #ffffff;
    }
  }
  > :nth-of-type(2) {
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(5, 7, 95, 0.7);
    border: 1px solid #034fa8;
    height: 0.6rem;
    width: 1rem;
    img {
      cursor: pointer;
    }
  }
}
.type-marker {
  position: absolute;
  bottom: 0.7rem;
  left: 0.5rem;
  width: 3.25rem;
  display: flex;
  justify-content: center;
  flex-direction: column;
  height: 1.15rem;
  padding: 0.2rem 0.3rem;
  background: rgba(1, 69, 154, 0.5);
  > div {
    display: flex;
    align-items: center;
    > :nth-of-type(1) {
      width: 0.4rem;
      display: flex;
      justify-content: center;
    }
    > :nth-of-type(2) {
      margin-right: 0.25rem;
      margin-left: 0.05rem;
    }
  }
}
.tabs {
  width: 8rem;
  position: absolute;
  bottom: 0.2rem;
  left: 50%;
  right: 50%;
  transform: translateX(-50%) translateY(-50%);
}
.title-flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
  > div {
    display: flex;
    font-size: 0.14rem;
    font-weight: 400;

    > div {
      margin-right: 0.1rem;
      width: 0.8rem;
      height: 0.28rem;
      line-height: 0.3rem;
      color: #0e8bff;
      text-align: center;
      background: url(../../assets/<EMAIL>);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      cursor: pointer;
    }
    div.type-active {
      background: url(../../assets/<EMAIL>);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      color: rgba(0, 234, 255, 1);
    }
    :nth-of-type(2) {
      // margin-left: 0.1rem;
    }
  }
}
.pollutant-table {
  width: 100%;
  text-align: center;
  font-weight: normal;
  border: 1px solid rgba(11, 141, 211, 1);
  thead {
    background: rgba(15, 56, 171, 1);
    > tr {
      height: 0.36rem;
    }
  }
  tbody {
    tr {
      height: 0.29rem;
      td:nth-of-type(odd) {
        background: #0f245e;
      }
    }
  }
}
.gongditable {
  width: 100%;
  .thead,
  .tbody {
    font-size: 0.14rem;
    text-align: left;
    height: 0.43rem;
    line-height: 0.43rem;
  }
  .tbody {
    .td:nth-of-type(1) {
      width: 48%;
      overflow: hidden; //超出的文本隐藏
      text-overflow: ellipsis; //溢出用省略号显示
      white-space: nowrap; //溢出不换行
      display: flex;
      align-items: center;
    }
    .td:nth-of-type(2) {
      width: 19%;
      display: flex;
      align-items: center;
    }
    .td:nth-of-type(3) {
      width: 0.02rem;
      height: 0.3rem;
      margin-top: 0.05rem;
      background: rgba(0, 234, 255, 1);
      margin-right: 0.05rem;
    }
    .td:nth-of-type(4) {
      width: 30%;
      display: flex;
      align-items: center;
    }
  }
  .tbody:nth-child(odd) {
    background: #082f61;
  }
  .tbody:nth-child(even) {
    // background: #041433;
  }
}
</style>
<style lang="less">
.box-title {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  .select-main {
    display: flex;
    justify-content: space-between;
    .ant-select-selection {
      width: 100%;
      border: none;
      outline: none;
      border-radius: unset;
      background: url(../../assets/<EMAIL>);
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
    .ant-select-selection-selected-value {
      color: rgba(0, 234, 255, 1);
    }
    .ant-select-focused .ant-select-selection,
    .ant-select-selection:focus,
    .ant-select-selection:active {
      border: none;
      border-right-width: 0 !important;
      outline: none;
      box-shadow: none;
    }
  }
}
.polluted-video {
  .video-player {
    width: 100%;
    .video-js.vjs-fluid {
      width: 100% !important;
    }
    .video-js .vjs-big-play-button {
      // left: 31% !important;
    }
    .video-js.vjs-fluid {
      height: 2.5rem !important;
    }
  }
}
</style>
<template>
  <section class="common-main">
    <section class="left-part">
      <div>
        <div class="title" style="letter-spacing:2px" v-if="type ==5">企事业单位总数</div>
        <div class="flex-number" v-if="type ==5">
          <div>{{ heavilyPollutionCount[0] }}</div>
          <div>{{ heavilyPollutionCount[1] }}</div>
          <div>{{ heavilyPollutionCount[2] }}</div>
        </div>
        <div class="title" style="margin-top:0.3rem;letter-spacing:7px" v-if="type ==0">
          在建工地总数
        </div>
        <div class="flex-number" v-if="type ==0">
          <div>{{ siteCount[0] }}</div>
          <div>{{ siteCount[1] }}</div>
          <div>{{ siteCount[2] }}</div>
        </div>
      </div>
    </section>
    <section class="center-map">
      <!-- <keep-alive> -->
        <gao-de-map
          :mapStyle="mapStyle"
          :mapZoom="mapViewZoom"
          :enterType="6"
          :buildingMarker="heavyPollutionEnterpriseList"
          :companyList="companyList"
          :viewCenter="mapViewCenter"
          :currentSelectedBuildingMarker="currentSelectedBuildingMarker"
          :currentSelectedBuildingObj="currentSelectedBuildingObj"
          :curMarkerClickMarker="curMarkerClickMarker"
          :curMarkerClickObj="curMarkerClickObj"
          @curSiteChange="getCurSiteFromMap"
          @curCompanyChange="curCompanyChange"
        ></gao-de-map>
      <!-- </keep-alive> -->
    </section>
    <section class="right-part">
      <div class="enterprise" v-if="type ==5">
        <div class="enterprise-main">
          <div class="common-title box-title">
            <div class="title">{{ `企业信息` }}</div>
            <a-select
              style="width:2rem"
              v-model="defaultEnterpriseValue"
              class="select-main"
              @change="heavilyPollutingEnterpriseChage"
            >
              <a-icon
                slot="suffixIcon"
                type="caret-down"
                style="color:rgba(0, 234, 255, 1);width:14px;height:8px;"
              />
              <a-select-option
                :value="item.value"
                v-for="(item, index) in heavyPollutionEnterpriseList"
                :key="index"
              >
                {{ item.name }}</a-select-option
              >
            </a-select>
          </div>
          <div class="sub-title">
            <img src="@/assets/biaoti.png" alt class="title-img" />
          </div>
          <table border="1" class="heavy-pollution-table">
            <tr>
              <td class="table-name">企业名称</td>
              <td colspan="3" class="table-text">
                {{ currentEnterpriseInfor.name }}
              </td>
            </tr>
            <tr>
              <td class="table-name">行政区划</td>
              <td class="table-text">
                {{ currentEnterpriseInfor.district }}
              </td>
              <td class="table-name">企业类型</td>
              <td class="table-text">
                {{ currentEnterpriseInfor.type }}
              </td>
            </tr>
            <tr>
              <td class="table-name">单位地址</td>
              <td colspan="3" class="table-text">
                {{ currentEnterpriseInfor.address }}
              </td>
            </tr>
            <tr>
              <td class="table-name">企业状态</td>
              <td class="table-text">
                {{ currentEnterpriseInfor.status }}
              </td>
              <td class="table-name">行业类别</td>
              <td class="table-text">
                {{ currentEnterpriseInfor.kind }}
              </td>
            </tr>
            <tr>
              <td class="table-name">环保负责人</td>
              <td class="table-text">
                {{ currentEnterpriseInfor.principal }}
              </td>
              <td class="table-name">负责人电话</td>
              <td class="table-text">
                {{ currentEnterpriseInfor.principalTel }}
              </td>
            </tr>
            <tr>
              <td class="table-name">排污许可证</td>
              <td class="table-text">
                {{ currentEnterpriseInfor.licence }}
              </td>
              <td class="table-name">环境应急预案</td>
              <td class="table-text">
                {{ currentEnterpriseInfor.emergencyPlan }}
              </td>
            </tr>
          </table>
        </div>
        <template v-if="enterpriseText !== '水五(七)厂'">
          <div class="pollute-main">
            <div class="common-title box-title">
              <div class="title">{{ `污染源监测` }}</div>
              <div class="update-time-pollution">
                {{ pollutionAllCount.updateTime }}更新
              </div>
            </div>
            <div class="sub-title" style="margin:0;">
              <img src="@/assets/biaoti.png" alt class="title-img" />
            </div>
            <div class="pollution-main">
              <div class="flex-box">
                <div>
                  <div class="type-card">污染源排放年总量</div>
                  <div>
                    <div class="name">废水排放总量：</div>
                    <div>
                      <span class="number">{{
                        pollutionAllCount.countOfWastewater
                      }}</span
                      >千克
                    </div>
                  </div>
                  <div>
                    <div class="name">废气排放总量：</div>
                    <div>
                      <span class="number">{{
                        pollutionAllCount.countOfGas
                      }}</span
                      >m³
                    </div>
                  </div>
                  <div>
                    <div class="name">废料排放总量：</div>
                    <div>
                      <span class="number">{{
                        pollutionAllCount.countOfWaste
                      }}</span
                      >吨
                    </div>
                  </div>
                </div>
                <div>
                  <div class="type-card">污染源排放监测</div>
                  <div class="change-box">
                    <div
                      :class="{ active: curPollutionIndex == 0 }"
                      @click="changePollutionType(0)"
                    >
                      废水监测
                    </div>
                    <div
                      :class="{ active: curPollutionIndex == 1 }"
                      @click="changePollutionType(1)"
                    >
                      废气监测
                    </div>
                    <div
                      :class="{ active: curPollutionIndex == 2 }"
                      @click="changePollutionType(2)"
                    >
                      废料监测
                    </div>
                  </div>
                  <div>
                    <PollutionSource
                      :width="'2.46rem'"
                      :height="'2.2rem'"
                      :id="'PollutionSource'"
                      :propData="pollutionSourceList[curPollutionIndex]"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="discharge-main">
            <div class="common-title box-title title-flex">
              <div class="title">{{ `企业排污排行` }}</div>
              <div>
                <div
                  :class="{ 'type-active': pollutionType === 's' }"
                  @click="pollutionStationChange('s')"
                >
                  废水
                </div>
                <div
                  :class="{ 'type-active': pollutionType === 'e' }"
                  @click="pollutionStationChange('e')"
                >
                  废气
                </div>
                <div
                  :class="{ 'type-active': pollutionType === 'w' }"
                  @click="pollutionStationChange('w')"
                >
                  废料
                </div>
              </div>
              <!-- <a-select
                :defaultValue="pollutionType"
                class="select-main"
                @change="pollutionStationChange"
              >
                <a-icon
                  slot="suffixIcon"
                  type="caret-down"
                  style="color:rgba(0, 234, 255, 1);width:14px;height:8px;"
                />
                <a-select-option value="s" key="s">废水</a-select-option>
                <a-select-option value="e" key="e">废气</a-select-option>
                <a-select-option value="w" key="w">废料</a-select-option>
              </a-select> -->
            </div>
            <div class="sub-title" style="margin:0;">
              <img src="@/assets/biaoti.png" alt class="title-img" />
            </div>
            <div class="table-data1">
              <div class="table-data-thead">
                <div class="tr">
                  <div class="th">排行</div>
                  <div class="th">企事业单位</div>
                  <div class="th">
                    排放量({{ pollutionType == "e" ? "m³" : "吨" }})
                  </div>
                  <div class="th">行业</div>
                </div>
              </div>
              <swiper
                :options="swiperOptionPollution"
                class="table-data-tbody"
                v-if="heavyPollution.length > 5"
              >
                <swiper-slide
                  class="tr"
                  v-for="(item, index) in heavyPollution"
                  :key="index"
                >
                  <div class="td">
                    <div class="tdAfter">{{ index + 1 }}</div>
                  </div>
                  <div class="td">
                    <a-tooltip>
                      <template slot="title">
                        {{ item.heavilyPollutingEnterpriseName }}
                      </template>
                      {{ item.heavilyPollutingEnterpriseNickname }}
                    </a-tooltip>
                  </div>
                  <div class="td" v-if="pollutionType == 's'">
                    {{
                      item.wastewaterDischarge ? item.wastewaterDischarge : "-"
                    }}
                  </div>
                  <div class="td" v-if="pollutionType == 'e'">
                    {{ item.exhaustEmissions ? item.exhaustEmissions : "-" }}
                  </div>
                  <div class="td" v-if="pollutionType == 'w'">
                    {{ item.wasteDischarge ? item.wasteDischarge : "-" }}
                  </div>
                  <div class="td">
                    <a-tooltip>
                      <template slot="title">
                        {{ item.industryCategory }}
                      </template>
                      {{ item.industryCategory }}
                    </a-tooltip>
                  </div>
                </swiper-slide>
              </swiper>
              <div class="table-data-tbody" v-else>
                <div
                  class="tr"
                  v-for="(item, index) in heavyPollution"
                  :key="index"
                >
                  <div class="td">
                    <div class="tdAfter">{{ index + 1 }}</div>
                  </div>
                  <div class="td">
                    <a-tooltip>
                      <template slot="title">
                        {{ item.heavilyPollutingEnterpriseName }}
                      </template>
                      {{ item.heavilyPollutingEnterpriseNickname }}
                    </a-tooltip>
                  </div>
                  <div class="td" v-if="pollutionType == 's'">
                    {{ item.sewageDischarge ? item.sewageDischarge : "-" }}
                  </div>
                  <div class="td" v-if="pollutionType == 'e'">
                    {{ item.exhaustEmissions ? item.exhaustEmissions : "-" }}
                  </div>
                  <div class="td" v-if="pollutionType == 'w'">
                    {{ item.wasteDischarge ? item.wasteDischarge : "-" }}
                  </div>
                  <div class="td">
                    <a-tooltip>
                      <template slot="title">
                        {{ item.industryCategory }}
                      </template>
                      {{ item.industryCategory }}
                    </a-tooltip>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
        <template v-if="enterpriseText === '水五(七)厂'">
          <div>
            <div class="common-title box-title">
              <div class="title">{{ `监测信息` }}</div>
            </div>
            <div class="sub-title" style="margin:0;">
              <img src="@/assets/biaoti.png" alt class="title-img" />
            </div>
            <table border="1" class="pollutant-table">
              <thead>
                <tr>
                  <th>指标项</th>
                  <th>数值</th>
                  <th>单位</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(item, index) in polluteDetails" :key="index">
                  <td>{{ item.codeName }}</td>
                  <td :style="{ color: '#76F573' }">
                    {{
                      item.value && item.value != 0.0 && item.value != 0
                        ? item.value
                        : "-"
                    }}
                  </td>
                  <td>{{ item.unit ? item.unit : "-" }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </template>
      </div>
      <div class="construction-plant" v-else>
        <div>
          <div class="common-title box-title">
            <div class="title">{{ `工地信息` }}</div>
            <a-select
              style="width:2rem"
              v-model="curSite"
              class="select-main"
              @change="curSiteChange"
            >
              <a-icon
                slot="suffixIcon"
                type="caret-down"
                style="color:rgba(0, 234, 255, 1);width:14px;height:8px;"
              />
              <a-select-option
                :value="item.companyId"
                v-for="(item, index) in companyList"
                :key="index"
              >
                {{ item.companyName }}
              </a-select-option>
            </a-select>
          </div>
          <div class="sub-title">
            <img src="@/assets/biaoti.png" alt class="title-img" />
          </div>
          <table border="1" class="table-site">
            <tr>
              <td class="table-name">工地名称</td>
              <td colspan="3" class="table-text">
                {{
                  companyList.length
                    ? companyList[curSiteIndex].companyName
                    : ""
                }}
              </td>
            </tr>
            <tr v-if="curSitePM10">
              <td class="table-name">PM10监测</td>
              <td colspan="3" class="table-text">
                <div class="PM10-detail">
                  <span
                    :style="{
                      color: curSitePM10.exceedStandard
                        ? 'rgba(232, 14, 14, 1)'
                        : ''
                    }"
                    >{{ curSitePM10.inhalableParticles }}</span
                  >
                  <span>ug/m³</span>
                  <span
                    :style="{
                      background: curSitePM10.exceedStandard
                        ? 'rgba(232, 14, 14, 1)'
                        : ''
                    }"
                    >{{ curSitePM10.exceedStandard ? "超标" : "未超标" }}</span
                  >
                </div>
              </td>
            </tr>
            <tr>
              <td class="table-name">工地地址</td>
              <td colspan="3" class="table-text">
                {{
                  companyList.length
                    ? companyList[curSiteIndex].companyAddress
                    : ""
                }}
              </td>
            </tr>
            <tr>
              <td class="table-name">安监备案单位编号</td>
              <td colspan="3" class="table-text">
                {{
                  companyList.length ? companyList[curSiteIndex].ajUnitcode : ""
                }}
              </td>
            </tr>
            <tr>
              <td class="table-name">安监备案号</td>
              <td class="table-text">
                {{ companyList.length ? companyList[curSiteIndex].ajNum : "" }}
              </td>
              <td class="table-name">安监备案单位名称</td>
              <td class="table-text">
                {{
                  companyList.length ? companyList[curSiteIndex].ajUnitname : ""
                }}
              </td>
            </tr>
          </table>
        </div>
        <div>
          <div class="video-main polluted-video">
            <div class="common-title box-title">
              <div class="title">{{ `在建工地PM₁₀监测` }}</div>
            </div>

            <div class="sub-title" style="margin:0;">
              <img src="@/assets/biaoti.png" alt class="title-img" />

              <div class="gongditable">
                <swiper :options="swiperOptionAlert" style="height: 2.15rem;">
                  <swiper-slide
                    class="tbody"
                    v-for="(item, index) in companyAlarmList"
                    :key="index"
                  >
                    <div style="display:flex;" class="gongdi-tr">
                      <div class="td">
                        <div style="width:0.18rem;">
                          <img
                            src="@/assets/<EMAIL>"
                            alt=""
                            style="width:0.15rem;height:0.17rem;margin-bottom: 0.05rem;"
                          />
                        </div>
                        <div
                          style="flex:1;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;padding-right: 0.12rem;"
                        >
                          {{
                            item.alarmContent
                              ? item.alarmContent.replace("PM10", "PM₁₀")
                              : "-"
                          }}
                        </div>
                      </div>
                      <div class="td">
                        PM₁₀：{{ item.alarmValue ? item.alarmValue : "-" }}
                      </div>
                      <div class="td"></div>
                      <div class="td">
                        <div style="flex:1;">
                          {{ item.alarmTime !== "" ? item.alarmTime : "-" }}
                        </div>
                        <div style="width:0.18rem;">
                          <img
                            src="@/assets/<EMAIL>"
                            alt=""
                            style="width:0.15rem;height:0.17rem;margin-bottom: 0.05rem;"
                          />
                        </div>
                      </div>
                    </div>
                  </swiper-slide>
                </swiper>
              </div>
            </div>
          </div>
        </div>
        <div v-if="videoList.length">
          <div class="video-main polluted-video">
            <div class="common-title box-title">
              <div class="title">{{ `工地实况监查` }}</div>
              <a-select
                v-model="curVideo"
                class="select-main"
                @change="videoChange"
                style="width:2rem"
              >
                <a-icon
                  slot="suffixIcon"
                  type="caret-down"
                  style="color:rgba(0, 234, 255, 1);width:14px;height:8px;"
                />
                <a-select-option
                  :value="item.cameraId"
                  v-for="(item, index) in videoList"
                  :key="index"
                >
                  {{ item.cameraName }}</a-select-option
                >
              </a-select>
            </div>
            <div class="sub-title" style="margin:0;">
              <img src="@/assets/biaoti.png" alt class="title-img" />
            </div>
            <video-player
              ref="livePlayer"
              :playsinline="true"
              @statechanged="playerStateChanged($event)"
              :options="playerOptions"
            ></video-player>
          </div>
        </div>
      </div>
    </section>
    <section class="enterprise-search">
      <div>
        <input
          type="text"
          :placeholder="type == 5 ? '请输入：公司名称' : '请输入：(公司/工地)名称'"
          v-model="enterpriseSearch"
          @keyup.enter="enterpriseSearchBtn"
        />
        <a-icon
          type="close-circle"
          style="font-size:0.2rem;cursor: pointer;margin-right: 0.15rem;"
          v-if="enterpriseSearch != ''"
          @click="clearSearch"
        />
      </div>
      <div>
        <img src="@/assets/search.png" alt="" @click="enterpriseSearchBtn" />
      </div>
    </section>
    <section class="type-marker">
      <div v-if="type == 5">
        <div>
          <img src="@/assets/zwqy.png" alt="" />
        </div>
        <div>监测企业</div>
        <div>{{ heavyPollutionEnterpriseStrore.length }}个</div>
      </div>
      <div v-if="type == 0">
        <div><img src="@/assets/siteMarker.png" alt="" /></div>
        <div>在建工地</div>
        <div>{{ companyList.length }}个</div>
      </div>
      <div>
        <div><img src="@/assets/jk.png" alt="" /></div>
        <div>实况监控</div>
        <div>1个</div>
      </div>
    </section>
<!--    <section class="tabs">-->
<!--      <component-tabs :activeIndex="type === 0 ? 0 : 5"></component-tabs>-->
<!--    </section>-->
  </section>
</template>

<script lang="ts">
/* eslint-disable @typescript-eslint/ban-ts-ignore */
//@ts-ignore
import Bus from '@/utils/bus'
import { Component, Vue } from "vue-property-decorator";
import { Swiper, SwiperSlide } from "vue-awesome-swiper";
import GaoDeMap from "@/components/GaoDeMap/index.vue";
import { Table, Radio, Select, Icon, Tooltip } from "ant-design-vue";
import RotateBar from "@/components/Charts/LineAndBarChart.vue";
import tabs from "./tabs.vue";
import PollutionSource from "@/components/Charts/PollutionSource.vue";
import moment from "moment";
import {
  fetchStationWithRecord,
  getTodayMonitorItemRecord,
  fetchRecentAlarm,
  getHeavilyPollutingEnterpriseList
} from "@/api/water";
import { heavilyPollutingEnterpriseList } from "@/api/homeTable";
import {
  getCompanyList,
  getPollutionCount,
  getPollutionEnterprise,
  getSiteVideoList,
  getCameraUrl,
  getCurSitePM10,
  findByKeyWord,
  getFiveWaterList,
  getSevenWaterList,
  getcompanyAlarmList
} from "@/api/headvily-pollution";
import "video.js/dist/video-js.css";
import "videojs-contrib-hls";
import "videojs-flash";
interface MapCenter {
  lng: number;
  lat: number;
}
interface EnterPriseInformation {
  name: string;
  district: string;
  type: string;
  address: string;
  status: string;
  kind: string;
  principal: string;
  principalTel: string;
  licence: string;
  emergencyPlan: string;
}
interface RankList {
  rank: number;
  type: string;
  release: string;
}
@Component({
  name: "HeavilyPollutedEnterprise",
  components: {
    PollutionSource,
    RotateBar,
    GaoDeMap,
    ATable: Table,
    ARadioGroup: Radio.Group,
    ARadioButton: Radio.Button,
    ASelect: Select,
    AIcon: Icon,
    ASelectOption: Select.Option,
    Swiper,
    SwiperSlide,
    ATooltip: Tooltip,
    "component-tabs": tabs
  }
})
export default class extends Vue {
  private enterpriseText = "";
  private currentSelectedBuildingMarker: any = null;
  private currentSelectedBuildingObj: any = {
    target: {
      w: {
        data: {}
      }
    }
  };
  private curMarkerClickMarker: any = "";
  private curMarkerClickObj: any = {
    target: {
      w: {
        data: {}
      }
    }
  };
  private enterpriseSearch: any = "";
  private showType: any = 0;
  // private mapStyle = "amap://styles/0739a6632d849ae1b5e6f1c52f7d12c3";
  private mapStyle = "amap://styles/0739a6632d849ae1b5e6f1c52f7d12c3";
  // 地图视图中心点
  private mapViewCenter: MapCenter = {
    lng: 104.11,
    lat: 30.912
  };
  // 地图缩放大小
  private mapViewZoom = 12.8;
  // 默认选中重污染企业
  private defaultEnterpriseValue = "";
  private swiperOptionAlert = {
    direction: "vertical",
    slidesPerView: 5,
    slidesPerGroup: 1,
    loop: true,
    autoplay: {
      delay: 3500,
      disableOnInteraction: false
    }
  };
  // 当前展示的重污染企业信息
  private currentEnterpriseInfor: EnterPriseInformation = {
    name: "",
    district: "",
    type: "",
    address: "",
    status: "",
    kind: "",
    principal: "",
    principalTel: "",
    licence: "",
    emergencyPlan: ""
  };
  // 重污企业排放量
  private heavyPollution = [];
  private pollutionType = "s"; //当前排污类型
  // 重污染企业轮播timer
  private switchEnterpriseTimerStore: any;
  private playerOptions: any = {
    //playbackRates: [0.7, 1.0, 1.5, 2.0], //播放速度
    autoplay: false, //如果true,浏览器准备好时开始回放。
    muted: true, // 默认情况下将会消除任何音频。
    loop: false, // 导致视频一结束就重新开始。
    preload: "auto", // 建议浏览器在<video>加载元素后是否应该开始下载视频数据。auto浏览器选择最佳行为,立即开始加载视频（如果浏览器支持）
    language: "zh-CN",
    languages: {
      "zh-CN": {
        Play: "播放",
        Pause: "暂停",
        "Current Time": "当前时间",
        Duration: "时长",
        "Remaining Time": "剩余时间",
        "Stream Type": "媒体流类型",
        LIVE: "直播",
        Loaded: "加载完毕",
        Progress: "进度",
        Fullscreen: "全屏",
        "Non-Fullscreen": "退出全屏",
        Mute: "静音",
        Unmute: "取消静音",
        "Playback Rate": "播放速度",
        Subtitles: "字幕",
        "subtitles off": "关闭字幕",
        Captions: "内嵌字幕",
        "captions off": "关闭内嵌字幕",
        Chapters: "节目段落",
        "Close Modal Dialog": "关闭弹窗",
        Descriptions: "描述",
        "descriptions off": "关闭描述",
        "Audio Track": "音轨",
        "You aborted the media playback": "视频播放被终止",
        "A network error caused the media download to fail part-way.":
          "网络错误导致视频下载中途失败。",
        "The media could not be loaded, either because the server or network failed or because the format is not supported.":
          "视频因格式不支持或者服务器或网络的问题无法加载。",
        "The media playback was aborted due to a corruption problem or because the media used features your browser did not support.":
          "由于视频文件损坏或是该视频使用了你的浏览器不支持的功能，播放终止。",
        "No compatible source was found for this media.":
          "无法找到此视频兼容的源。",
        "The media is encrypted and we do not have the keys to decrypt it.":
          "视频已加密，无法解密。",
        "Play Video": "播放视频",
        Close: "关闭",
        "Modal Window": "弹窗",
        "This is a modal window": "这是一个弹窗",
        "This modal can be closed by pressing the Escape key or activating the close button.":
          "可以按ESC按键或启用关闭按钮来关闭此弹窗。",
        ", opens captions settings dialog": ", 开启标题设置弹窗",
        ", opens subtitles settings dialog": ", 开启字幕设置弹窗",
        ", opens descriptions settings dialog": ", 开启描述设置弹窗",
        ", selected": ", 选择",
        "captions settings": "字幕设定",
        "Audio Player": "音频播放器",
        "Video Player": "视频播放器",
        Replay: "重播",
        "Progress Bar": "进度小节",
        "Volume Level": "音量",
        "subtitles settings": "字幕设定",
        "descriptions settings": "描述设定",
        Text: "文字",
        White: "白",
        Black: "黑",
        Red: "红",
        Green: "绿",
        Blue: "蓝",
        Yellow: "黄",
        Magenta: "紫红",
        Cyan: "青",
        Background: "背景",
        Window: "视窗",
        Transparent: "透明",
        "Semi-Transparent": "半透明",
        Opaque: "不透明",
        "Font Size": "字体尺寸",
        "Text Edge Style": "字体边缘样式",
        None: "无",
        Raised: "浮雕",
        Depressed: "压低",
        Uniform: "均匀",
        Dropshadow: "下阴影",
        "Font Family": "字体库",
        "Proportional Sans-Serif": "比例无细体",
        "Monospace Sans-Serif": "单间隔无细体",
        "Proportional Serif": "比例细体",
        "Monospace Serif": "单间隔细体",
        Casual: "舒适",
        Script: "手写体",
        "Small Caps": "小型大写字体",
        Reset: "重启",
        "restore all settings to the default values": "恢复全部设定至预设值",
        Done: "完成",
        "Caption Settings Dialog": "字幕设定视窗",
        "Beginning of dialog window. Escape will cancel and close the window.":
          "开始对话视窗。离开会取消及关闭视窗",
        "End of dialog window.": "结束对话视窗"
      }
    },
    aspectRatio: "448:196", // 将播放器置于流畅模式，并在计算播放器的动态大小时使用该值。值应该代表一个比例 - 用冒号分隔的两个数字（例如"16:9"或"4:3"）
    fluid: true, // 当true时，Video.js player将拥有流体大小。换句话说，它将按比例缩放以适应其容器。
    //application/x-mpegURL-m3u8 video/mp4-mp4 rtmp/mp4-rtmp flv-application/octet-stream-flv
    sources: [
      // {
      //   type: "application/x-mpegURL",
      //   src:
      //     "http://60.255.36.154:10000/sms/34020000002020000001/hls/51010600001320000005_51010600001320000001/51010600001320000005_51010600001320000001_live.m3u8"
      // }
    ],
    // poster:
    //   "http://60.255.36.154:10000/snap/51010600001320000005/51010600001320000001.jpg?t=1586164395847422586", //你的封面地址
    width: document.documentElement.clientWidth,
    notSupportedMessage: "该站点暂未设置监控，请查看其它站点" //允许覆盖Video.js无法播放媒体源时显示的默认信息。
    // controls: false,
    // controlBar: {
    //   timeDivider: false,
    //   durationDisplay: false,
    //   remainingTimeDisplay: false,
    //   fullscreenToggle: false, //全屏按钮
    // },
  };
  // 摄像头列表
  private videoList: any[] = [];
  private curVideo: number | string = ""; //当前的摄像头id
  private fetchTimer: any = null;
  private type:any = 0
  mounted() {
    this.type = this.$route.params.type || 0
    Bus.$on('index', (res:any) => {
      if (res == 0 || res == 5) {
        this.type = res
      }
      this.getHeavilyPollutingEnterpriseList()
      this.fetchcompanyList();
    })
    this.getcompanyAlarmList();
    this.getHeavilyPollutingEnterpriseList();
    this.fetchcompanyList();
    this.fetchHeavyPollution();
    this.fetchTimer = setInterval(() => {
      // 企业
      this.getHeavilyPollutingEnterpriseList();
      // 工地
      this.fetchcompanyList();
    }, 10 * 60 * 1000);
  }

  beforeDestroy() {
    clearInterval(this.switchEnterpriseTimerStore);
    clearInterval(this.fetchTimer);
    Bus.$off('index', () => {})
  }
  // 清空搜索
  private clearSearch() {
    if (this.enterpriseSearch !== "") {
      this.enterpriseSearch = "";
      // 企业
      this.getHeavilyPollutingEnterpriseList();
      // 工地
      this.fetchcompanyList();
    }
  }
  // 搜索
  private enterpriseSearchBtn() {
    if (this.enterpriseSearch !== "") {
      findByKeyWord(this.enterpriseSearch).then((res: any) => {
        // 企业;
        this.heavyPollutionEnterpriseList = this.type ==5 ? res.data.data.heavilyPollutingEnterpriseList.map(
          (enterprise: any) => {
            return {
              name: enterprise.heavilyPollutingEnterpriseNickname,
              value: enterprise.heavilyPollutingEnterpriseId,
              lng: enterprise.lng,
              lat: enterprise.lat
            };
          }
        ) : []
        // 工地;
        this.companyList = this.type ==0 ? res.data.data.companyList : []
        if (res.data.data.heavilyPollutingEnterpriseList.length > 0) {
          // 企业
          this.heavilyPollutingEnterpriseChage(
            this.heavyPollutionEnterpriseList[0].value
          );
          this.showType = 0;
        } else if (res.data.data.companyList.length > 0) {
          // 工地;
          this.curSiteChange(this.companyList[0].companyId);
        }
      });
    }
  }
  // 获取单个摄像头URL
  private fetchCameraUrl(): void {
    getCameraUrl(this.curVideo).then(res => {
      // 清除上一次地址
      this.playerOptions.sources = [
        {
          src: "",
          type: "application/x-mpegURL"
        }
      ];
      if (res.data.data.hlsHttps) {
        this.playerOptions.sources.push({
          type: "application/x-mpegURL",
          src: res.data.data.hlsHttps
        });
      } else if (res.data.data.flvHttps) {
        this.playerOptions.sources.push({
          type: "flv-application/octet-stream",
          src: res.data.data.flvHttps
        });
      } else if (res.data.data.rtmp) {
        this.playerOptions.sources.push({
          type: "rtmp/mp4",
          src: res.data.data.rtmp
        });
      } else {
        this.playerOptions.sources = [
          {
            src: "",
            type: "application/x-mpegURL"
          }
        ];
      }
      if (res.data.data.snapUrl) {
        this.playerOptions.poster = res.data.data.snapUrl;
      } else {
        this.playerOptions.poster = "";
      }
    });
  }
  private videoChange(value: number | string): void {
    this.curVideo = value;
    // this.fetchCameraUrl();
  }
  // 播放状态更改
  private playerStateChanged(playerCurrentState: any) {
    if (playerCurrentState.error) {
      this.videoChange(this.curVideo);
    }
  }
  // 地图标记点和下拉选项列表
  private heavyPollutionEnterpriseList: Array<any> = [];
  // 重污染企业store
  private heavyPollutionEnterpriseStrore: Array<any> = [];
  private heavilyPollutionCount: string | number = 0; //企业数量
  // 获取重污染企业列表
  private getHeavilyPollutingEnterpriseList(): void {
    getHeavilyPollutingEnterpriseList().then(res => {
      const data = res.data.data;
      this.heavilyPollutionCount =
        data.length > 99 ? String(data.length) : "0" + data.length;
      this.heavyPollutionEnterpriseStrore = data;
      //企业信息
      this.currentEnterpriseInfor = {
        name: data[0].heavilyPollutingEnterpriseName,
        district: data[0].administrativeDivision,
        type: data[0].heavilyPollutingEnterpriseType,
        address: data[0].heavilyPollutingEnterpriseAddress,
        status: data[0].heavilyPollutingEnterpriseStatus,
        kind: data[0].industryCategory,
        principal: data[0].principal,
        principalTel: data[0].principalPhoneNumber,
        licence: data[0].haveSewageDischargePermission ? "拥有" : "无",
        emergencyPlan: data[0].haveEnvironmentalEmergencyPlan
      };
      //默认选中的监测点
      this.defaultEnterpriseValue = data[0].heavilyPollutingEnterpriseId;
      this.currentSelectedBuildingMarker = this.type == 5 ? data[0].heavilyPollutingEnterpriseId : ''
      this.currentSelectedBuildingObj = this.type == 5 ? {
        target: {
          w: {
            data: {
              name: data[0].heavilyPollutingEnterpriseNickname,
              value: data[0].heavilyPollutingEnterpriseId,
              lng: data[0].lng,
              lat: data[0].lat
            }
          }
        }
      } : {
        target: {
          w: {
            data: {}
          }
        }
      }
      //地图标记点,下拉选项
      this.heavyPollutionEnterpriseList = this.type == 5 ? data.map((enterprise: any) => {
        return {
          name: enterprise.heavilyPollutingEnterpriseNickname,
          value: enterprise.heavilyPollutingEnterpriseId,
          lng: enterprise.lng,
          lat: enterprise.lat
        };
      }) : []
      this.fetchPollutionCount();
      this.fetchPollutionEnterprise();
      // this.switchEnterpriseSelectTimer();
    });
  }
  private polluteDetails: any = [];
  private selectIndex = 0; //企业切换下标
  // select切换重污染企业项
  private heavilyPollutingEnterpriseChage(value: any): void {
    clearInterval(this.switchEnterpriseTimerStore);
    this.defaultEnterpriseValue = value;
    this.currentSelectedBuildingMarker = this.type == 5 ? value : '';
    const selectIndex = this.heavyPollutionEnterpriseStrore.findIndex(item => {
      return item.heavilyPollutingEnterpriseId == value;
    });
    this.currentSelectedBuildingObj = this.type == 5 ? {
      target: {
        w: {
          data: {
            name: this.heavyPollutionEnterpriseStrore[selectIndex]
              .heavilyPollutingEnterpriseNickname,
            value: this.heavyPollutionEnterpriseStrore[selectIndex]
              .heavilyPollutingEnterpriseId,
            lng: this.heavyPollutionEnterpriseStrore[selectIndex].lng,
            lat: this.heavyPollutionEnterpriseStrore[selectIndex].lat
          }
        }
      }
    } : {
      target: {
        w: {
          data: {}
        }
      }
    }
    this.selectIndex = selectIndex;
    const currentSelect = this.heavyPollutionEnterpriseStrore[selectIndex];
    this.currentEnterpriseInfor = {
      name: currentSelect.heavilyPollutingEnterpriseName,
      district: currentSelect.administrativeDivision,
      type: currentSelect.heavilyPollutingEnterpriseType,
      address: currentSelect.heavilyPollutingEnterpriseAddress,
      status: currentSelect.heavilyPollutingEnterpriseStatus,
      kind: currentSelect.industryCategory,
      principal: currentSelect.principal,
      principalTel: currentSelect.principalPhoneNumber,
      licence: currentSelect.haveSewageDischargePermission ? "拥有" : "无",
      emergencyPlan: currentSelect.haveEnvironmentalEmergencyPlan
    };
    this.fetchPollutionCount();
    this.fetchPollutionEnterprise();
    // this.switchEnterpriseSelectTimer();
    if (value == "510100000004") {
      // 水七厂
      this.enterpriseText = "水五(七)厂";
      this.getSevenWaterList();
    } else if (value == "020") {
      // 水五厂
      this.enterpriseText = "水五(七)厂";
      this.getFiveWaterList();
    } else {
      this.enterpriseText = "";
    }
  }
  // 水七厂
  private getSevenWaterList() {
    getSevenWaterList().then((res: any) => {
      this.polluteDetails = res.data.data;
    });
  }
  // 水五厂
  private getFiveWaterList() {
    getFiveWaterList().then((res: any) => {
      console.log(res.data.data, 'asd')
      this.polluteDetails = res.data.data;
    });
  }
  // 重污染企业Select轮播处理
  private switchEnterpriseSelectTimer(): void {
    const total = this.heavyPollutionEnterpriseStrore.length;
    this.switchEnterpriseTimerStore = setInterval(() => {
      this.selectIndex =
        this.selectIndex + 1 >= this.heavyPollutionEnterpriseStrore.length
          ? 0
          : this.selectIndex + 1;
      this.defaultEnterpriseValue = this.heavyPollutionEnterpriseStrore[
        this.selectIndex
      ].heavilyPollutingEnterpriseId;
      this.heavilyPollutingEnterpriseChage(this.defaultEnterpriseValue);
    }, 10 * 1000);
  }
  private siteCount: number | string = 0; //在建工地数量
  private companyList: any[] = [];
  private curSite = ""; //当前选中工地id
  private curSiteIndex = 0; //当前选中工地索引
  private curSitePM10: any = {}; //当前选中工地的PM10
  // 获取重污染工地列表
  private fetchcompanyList(): void {
    getCompanyList({}).then((res:any) => {
      const data = res.data.data;
      this.siteCount =
        data.length > 99 ? String(data.length) : "0" + data.length;
      this.companyList = this.type == 0 ? data : []
      if (this.curSite == "") {
        this.curSite = this.companyList[0].companyId;
      }
      // this.curMarkerClickMarker = this.companyList[0].companyId;
      // this.curMarkerClickObj.target.w.data = this.companyList[0];
      this.fetchCurSitePM10();
    });
  }
  //获取当前工地PM10相关信息
  private fetchCurSitePM10(): void {
    getCurSitePM10(this.curSite).then(res => {
      const data = res.data.data;
      console.log(*********, data)
      if (data) {
        data.inhalableParticles = Math.round(data.inhalableParticles);
        this.curSitePM10 = data;
      } else {
        this.curSitePM10 = null
      }
    });
  }
  //下拉选择工地
  private curSiteChange(value: any): void {
    this.curSite = value;
    this.curSiteIndex = this.companyList.findIndex((item: any) => {
      return item.companyId == this.curSite;
    });
    this.curMarkerClickMarker = this.companyList[this.curSiteIndex].companyId;
    this.curMarkerClickObj.target.w.data = this.companyList[this.curSiteIndex];
    this.fetchCurSitePM10();
  }
  // 工地地图点击
  private getCurSiteFromMap(value: any): void {
    console.log(value,99999);

    this.curSiteChange(value);
    this.showType = 1;
    if (this.enterpriseSearch !== "") {
      // this.enterpriseSearchBtn();
    } else {
      // 企业
      // this.getHeavilyPollutingEnterpriseList();
      // 工地
      this.fetchcompanyList();
    }
    getSiteVideoList(value).then(res => {
      const data = res.data.data;
      this.videoList = data;
      if (this.videoList.length) {
        this.curVideo = this.videoList[0].cameraId;
        // this.fetchCameraUrl();
      } else {
        this.curVideo = "";
      }
    });
  }
  //污染源类型切换
  private changePollutionType(index: number): void {
    this.curPollutionIndex = index;
  }
  //污染源总数量
  private pollutionAllCount: any = {};
  private fetchPollutionCount(): void {
    getPollutionCount(this.defaultEnterpriseValue).then(res => {
      const data = res.data.data;
      data.countOfWastewater = data.countOfWastewater
        ? Math.round(data.countOfWastewater)
        : 0;
      data.countOfGas = data.countOfGas ? Math.round(data.countOfGas) : 0;
      data.countOfWaste = data.countOfWaste ? Math.round(data.countOfWaste) : 0;
      data.updateTime = moment(data.updateTime).format("YYYY-MM-DD HH:mm");
      this.pollutionAllCount = data;
    });
  }
  //污染源类型下标
  private curPollutionIndex = 0;
  //污染监测物
  private pollutionSourceList: {
    nameList: string[];
    dataList: number[] | string[];
    unit: string;
  }[] = [
    {
      nameList: [],
      dataList: [],
      unit: ""
    },
    {
      nameList: [],
      dataList: [],
      unit: ""
    },
    {
      nameList: [],
      dataList: [],
      unit: ""
    }
  ];
  //污染源分布图
  private fetchPollutionEnterprise(): void {
    getPollutionEnterprise(this.defaultEnterpriseValue).then((res: any) => {
      const data = res.data.data;
      const pollutionArr: any = [];
      const list = [
        data.wastewaterDischargeList,
        data.exhaustGasList,
        data.wasteDischargeList
      ];
      list.forEach((item: any) => {
        const obj: any = { nameList: [], dataList: [], unit: "", show: false };
        item.forEach((child: any) => {
          obj.nameList.push(child.name);
          obj.dataList.push(Number(child.value).toFixed(2));
          obj.unit = child.unit;
        });
        obj.show = item.length ? true : false;
        pollutionArr.push(obj);
      });
      this.pollutionSourceList = pollutionArr;
    });
  }
  // 重污企业swiper配置项
  private swiperOptionPollution = {
    direction: "vertical",
    slidesPerView: 4,
    slidesPerGroup: 1,
    loop: true,
    autoplay: {
      delay: 5000,
      disableOnInteraction: false
    }
  };
  //重污企业
  private fetchHeavyPollution() {
    return new Promise((resolve, reject) => {
      heavilyPollutingEnterpriseList(510106, this.pollutionType).then(
        (res: any) => {
          const data = res.data.data;
          if (data && data.length) {
            this.heavyPollution = data;
          }
          resolve();
        }
      );
    });
  }
  //重污企业下拉改变
  private pollutionStationChange(type: any) {
    this.pollutionType = type;
    this.fetchHeavyPollution();
  }
  // 地图重污染企业选中处理
  private curCompanyChange(BuildingId: string): void {
    // clearInterval(this.switchEnterpriseTimerStore);
    this.heavilyPollutingEnterpriseChage(BuildingId);
    this.showType = 0;
    if (this.enterpriseSearch !== "") {
      // this.enterpriseSearchBtn();
    } else {
      // 企业
      // this.getHeavilyPollutingEnterpriseList();
      // 工地
      this.fetchcompanyList();
    }
    // this.switchEnterpriseSelectTimer();
  }
  private companyAlarmList: any = [];
  private getcompanyAlarmList() {
    getcompanyAlarmList().then((res: any) => {
      for (const item of res.data.data) {
        item.alarmTime = item.alarmTime
          ? moment(item.alarmTime).format("MM月DD日 HH:mm")
          : "";
      }
      console.log(res.data.data);
      this.companyAlarmList = res.data.data;
    });
  }
}
</script>
