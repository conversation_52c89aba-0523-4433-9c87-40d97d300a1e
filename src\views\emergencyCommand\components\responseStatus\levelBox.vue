<template>
  <div class="flex flex-col items-center level-box">
    <span class="level-value">{{ valueChnage[value] }}</span>
    <span class="name">响应等级</span>
  </div>
</template>

<script>
export default {
  name: "levelBox",
  props: {
    value: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      valueChnage: ["—", "Ⅰ", "Ⅱ", "Ⅲ", "Ⅳ", "Ⅴ", "Ⅵ", "Ⅶ", "Ⅷ", "Ⅸ", "Ⅹ"],
    };
  },
};
</script>

<style lang="less" scoped>
.level-box {
  width: 102px;
  height: 59px;
  background: url("../../../../assets/images/<EMAIL>") no-repeat
    center center;
  background-size: 100% 100%;

  .level-value {
    font-size: 25px;
    font-weight: bold;
    color: #f4bb4b;
    line-height: 1;
    margin-bottom: 5px;
    transform: scale(1, 1.4);
  }

  .name {
    color: #dcf0ff;
    font-size: 16px;
  }
}
.items-center {
  align-items: center;
}
.flex-col {
  flex-direction: column;
}
.flex {
  display: flex;
}
</style>
