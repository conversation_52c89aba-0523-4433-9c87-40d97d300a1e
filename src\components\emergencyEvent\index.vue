<template>
  <!-- 应急事件 -->
  <div style="display: flex; flex-wrap: wrap; margin-bottom: 20px">
    <div v-for="(ite, ind) in defaultList" :key="ind + 'ee'" style="width: 50%">
      <item
        :typeText="ite.text"
        :number="ite.num"
        :cflag="ite.selected"
        :type="ite.type"
        @iteclick="iteclick(ite)"
      ></item>
    </div>
  </div>
</template>
<script>
import item from './item.vue'
export default {
  name: '',
  props: {
    data: {
      type: Object,
      default: () => ({
        事故灾害: 0,
        自然灾害: 0,
        公共安全: 0,
        社区安全: 0,
      }),
    },
  },
  components: {
    item,
  },
  data() {
    return {
      defaultList: {
        事故灾害: {
          text: '事故灾害',
          num: 0,
          type: 'sgzh',
          selected: false,
        },
        自然灾害: {
          text: '自然灾害',
          num: 0,
          type: 'zrzh',
          selected: false,
        },
        公共安全: {
          text: '公共安全',
          num: 0,
          type: 'ggaq',
          selected: false,
        },
        社区安全: {
          text: '社区安全',
          num: 0,
          type: 'sqaq',
          selected: false,
        },
      },
    }
  },
  watch: {
    data: {
      handler(newV) {
        for (const key in newV) {
          if (Object.prototype.hasOwnProperty.call(newV, key)) {
            const val = newV[key]
            this.defaultList[key].num = val
            if (this.defaultList[key].text == '事故灾害' && val) {
              this.defaultList[key].selected = true
            } else if (
              this.defaultList[key].text == '自然灾害' &&
              val &&
              !this.defaultList['事故灾害'].selected
            ) {
              this.defaultList[key].selected = true
            } else if (
              this.defaultList[key].text == '公共安全' &&
              val &&
              !(
                this.defaultList['事故灾害'].selected ||
                this.defaultList['自然灾害'].selected
              )
            ) {
              this.defaultList[key].selected = true
            } else if (
              this.defaultList[key].text == '社区安全' &&
              val &&
              !(
                this.defaultList['事故灾害'].selected ||
                this.defaultList['自然灾害'].selected ||
                this.defaultList['自然灾害'].selected
              )
            ) {
              this.defaultList[key].selected = true
            }
          }
        }
        for (const k in this.defaultList) {
          if (Object.prototype.hasOwnProperty.call(this.defaultList, k)) {
            if (this.defaultList[k].selected) {
              this.iteclick(this.defaultList[k], 10)
            }
          }
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    iteclick(item, time = 0) {
      setTimeout(() => {
        for (const key in this.defaultList) {
          if (Object.prototype.hasOwnProperty.call(this.defaultList, key)) {
            const ele = this.defaultList[key]
            // console.log('ele-ele',ele);
            if (key === item.text) {
              ele.selected = true
              this.$emit('change', item.text)
            } else {
              ele.selected = false
            }
          }
        }
      }, time)
    },
  },
}
</script>

<style lang="less" scoped></style>
