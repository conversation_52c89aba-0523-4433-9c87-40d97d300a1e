<template>
  <div class="air-health">
    <div class="left">
      <div class="title">
        <span>街道区域环境大气健康</span>
        <PeriodTimeSelect :useRealTime="true" @getPeriodData="getPeriodData" />
      </div>
      <div class="sub-title">
        <img src="../../../assets/biaoti.png" alt class="title-img" />
      </div>

      <div class="tab-switch-button" @click="showPie = !showPie">
        {{ showPie ? "查看列表" : "查看占比" }}
      </div>

      <transition name="el-fade-in">
        <div v-show="!showPie" class="street_box">
          <!-- <vue-seamless-scroll
            v-if="dataList.length"
            ref="seamlessScroll"
            class="seamlessScroll"
            :data="dataList"
            :class-option="classOption"
          > -->
          <div
            class="street"
            @click="handleClick(item.streetCode, item)"
            :class="{ active: index === activeIndex }"
            v-for="(item, index) in dataList"
            :key="item.streetCode"
            v-loading="loading"
          >
            <div class="db">
              <div
                class="db-num"
                :style="{ color: colorMap[item.levelId] }"
                :class="{ active: index === activeIndex }"
              >
                {{ item.aqhi }}
              </div>
              <div class="db-text">AQHI</div>
            </div>

            <div class="street-info">
              <div class="street-info-top">
                <div class="street-name">{{ item.streetName }}</div>
                <div
                  class="person-count"
                  :class="{ active: index === activeIndex }"
                >
                  人口数：{{ item.personCount }}
                </div>
                <i
                  class="el-icon-location-outline"
                  :class="{ active: index === activeIndex }"
                ></i>
              </div>
              <div class="street-info-bottom">
                <span>
                  健康风险等级：<span
                    class="noise-level"
                    :style="{ color: colorMap[item.levelId] }"
                    >{{ levelMap[item.levelId] }})</span
                  >
                </span>
                <span v-if="periodValue === 0">更新时间：{{ item.createTime || "--" }}</span>
              </div>
            </div>
          </div>
          <!-- </vue-seamless-scroll> -->
          <div v-if="!dataList.length" class="nodata">
            <img src="../../../assets/recheck/<EMAIL>" alt="" />
            <div class="no_data_text">暂无数据</div>
          </div>
        </div>
      </transition>
      <transition name="el-fade-in-linear">
        <div v-show="showPie">
          <HealthRosePie
            :propData="chartData"
            id="air-health-pie"
            unit="个"
            width="410px"
            height="350px"
          />
        </div>
      </transition>
    </div>
    <div class="right">
      <header class="header">
        <aside class="headerLeft tag use-bg" :class="{active: activeTag === 1}" @click="toggleTag">金牛区空气质量健康指数实况</aside>
        <aside class="headerRight tag use-bg" :class="{active: activeTag === 2}" @click="toggleTag">成都体育锻炼适宜指数预报</aside>
      </header>
      <transition name="el-zoom-in-center" mode="out-in">
        <Right2 v-if="activeTag === 2" class="right" />
        <div v-else>
          <header class="update-time"> 更新时间：{{ countyHourData.createTime }}</header>
          <div class="gauge-box">
            <img src="../../../assets/health/<EMAIL>" alt="" class="bg1" />
            <img src="../../../assets/health/<EMAIL>" alt="" class="bg2" />
            <div class="aqhi">{{ countyHourData.aqhi }}</div>
            <HealthGauge
                class="gauge"
                width="150px"
                height="150px"
                id="air-health-gauge"
            />
            <div
                class="level"
                :style="{
              /*background: colorMap[countyHourData.levelId] + '4D',*/
              color: colorMap[countyHourData.levelId],
              borderColor: colorMap[countyHourData.levelId],
            }"
            >
              {{ levelMap[countyHourData.levelId] }}
            </div>

            <div class="dialog-button" @click="visible = true">AQHI评级标准</div>
          </div>

          <div class="legend-box">
            <div>健康风险级别、颜色 AQHI综合指数</div>
            <div class="color-box">
              <div class="color-list">
                <div
                    class="color-item"
                    v-for="item in colorList"
                    :style="{ background: item }"
                ></div>
              </div>
              <div class="num-list">
                <div class="num-item" v-for="item in 10">{{ item }}</div>
                <div class="num-item" style="margin-left: 10px">>10</div>
              </div>
              <div class="level-list">
                <div>一级</div>
                <div>二级</div>
                <div style="margin-left: 12px">三级</div>
                <div>四级</div>
              </div>
            </div>
          </div>

          <div class="health-bar-list">
            <div class="health-bar-item">
              <div class="health-bar">
                <div
                    class="health-bar-value"
                    :style="{
                  background: getErResColor(countyHourData.erRes),
                  width:
                    countyHourData.erRes < 45.45
                      ? (countyHourData.erRes / 45.45) * 100 + '%'
                      : '100%',
                }"
                >
                  <img src="../../../assets/health/<EMAIL>" alt="" />
                </div>
              </div>
              <div class="health-index">
                <span>呼吸系统疾病指数：</span>
                <span
                    class="index-value"
                    :style="{ color: getErResColor(countyHourData.erRes) }"
                >{{ countyHourData.erRes }}</span
                >
              </div>
            </div>
            <div class="health-bar-item">
              <div class="health-bar">
                <div
                    class="health-bar-value"
                    :style="{
                  background: getErCvdColor(countyHourData.erCvd),
                  width:
                    countyHourData.erCvd < 68.43
                      ? (countyHourData.erCvd / 68.43) * 100 + '%'
                      : '100%',
                }"
                >
                  <img src="../../../assets/health/<EMAIL>" alt="" />
                </div>
              </div>
              <div class="health-index">
                <span>心血管系统疾病指数：</span>
                <span
                    class="index-value"
                    :style="{ color: getErCvdColor(countyHourData.erCvd) }"
                >{{ countyHourData.erCvd }}</span
                >
              </div>
            </div>
          </div>

          <div class="title">
            <span>人群健康风险提示</span>
          </div>
          <div class="sub-title">
            <img src="../../../assets/biaoti.png" alt class="title-img" />
          </div>

          <div
              class="hint-level"
              :style="{
            color: colorMap[countyHourData.levelId],
            borderColor: colorMap[countyHourData.levelId],
            /*background: colorMap[countyHourData.levelId] + '4D',*/
            boxShadow: `inset 0px 0px 30px 0px ${
              colorMap[countyHourData.levelId]
            }D3`,
          }"
          >
            <span>健康风险级别：</span>
            <span>{{ levelMap[countyHourData.levelId] }}</span>
          </div>

          <div class="hint-list">
            <div class="hint-item" v-for="(item, index) in tipList" :key="index">
              <p>
                <img
                    src="../../../assets/health/<EMAIL>"
                    alt=""
                    style="margin-right: 6px"
                />{{ item.label }}
              </p>
              <p>{{ item.value }}</p>
            </div>
          </div>
        </div>

      </transition>
    </div>

    <!-- 评级标准弹框 -->
    <a-modal
      id="air-health-modal"
      width="1500px"
      :footer="null"
      :visible="visible"
      @cancel="visible = false"
    >
      <template #closeIcon>
        <img src="../../../assets/health/<EMAIL>" alt="" />
      </template>

      <img src="../../../assets/health/<EMAIL>" alt="" />

      <p class="modal-tip">提示：建议您在不同空气污染水平时采取适当的措施</p>

      <div class="tip-title">
        <div>健康风险级别/AQHI</div>
        <div>心血管系统或者呼吸系统疾病患者</div>
        <div>儿童以及长者</div>
        <div>户外工作者</div>
        <div>一般市民</div>
      </div>

      <div class="tip-table">
        <div
          class="tip-table-row"
          v-for="item in hintLevelInfoList"
          :key="item.levelId"
        >
          <div class="level-item" :style="{ color: colorMap[item.levelId] }">
            <div
              class="tip-level"
              :style="{ borderColor: colorMap[item.levelId] }"
            >
              <img :src="item.icon" alt="" />
              <span>{{ item.level }}</span>
            </div>
            <div>
              {{ item.levelIdRange }}
            </div>
          </div>
          <div
            class="tip-item"
            v-for="(item2, index) in item.tips"
            :key="index"
          >
            {{ item2.value }}
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>
<script>
import PeriodTimeSelect from "@/components/periodTimeSelect/periodTimeSelect.vue";
// import vueSeamlessScroll from "vue-seamless-scroll";
import HealthRosePie from "@/components/Charts/HealthRosePie.vue";
import HealthGauge from "@/components/Charts/HealthGauge.vue";
import { streetFindByTime, countyHour } from "@/api/health";
import jinniuStreet from "@/assets/map-geojson/jinniu_street.json";
import markerTitle from "@/assets/health/title-icon.png";
import yiji from "@/assets/health/<EMAIL>";
import erji from "@/assets/health/<EMAIL>";
import sanji from "@/assets/health/<EMAIL>";
import siji from "@/assets/health/<EMAIL>";
import Right2 from "./right2.vue";

export default {
  name: "NoiseHealth",
  components: {
    PeriodTimeSelect,
    // vueSeamlessScroll,
    HealthRosePie,
    HealthGauge,
    Right2,
  },
  props: {
    map: {
      required: true,
    },
    AMap: {
      required: true,
    },
  },
  data() {
    return {
      showPie: false,
      periodValue: 0,
      startDate: "",
      endDate: "",
      dataList: [],
      chartData: [],
      loading: false,
      activeIndex: undefined,
      activeCode: undefined,
      isDay: true,
      classOption: {
        step: 0.2,
      },
      levelMap: {
        1: "一级 ",
        2: "二级",
        3: "三级",
        4: "四级",
      },
      colorMap: {
        1: "#5AC58B",
        2: "#4E81CC",
        3: "#DD8F57",
        4: "#D13B42",
      },
      polygonList: [],
      markerList: [],
      countyHourData: {},
      colorList: [
        "#80DDA7",
        "#6BD197",
        "#5AC58B",
        "#6DA2ED",
        "#5C90DB",
        "#4E81CC",
        "#ECCA69",
        "#E8B368",
        "#DD8F57",
        "#C96D45",
        "#D13B42",
        "#D13B42",
      ],
      activeTag: 1,

      hintLevelInfoList: [
        {
          levelId: 1,
          level: "一级",
          levelIdRange: "0-3",
          icon: yiji,
          tips: [
            {
              label: "心血管系统或者 呼吸系统疾病患者",
              value: "建议可如常活动",
            },
            {
              label: "儿童以及长者",
              value: "建议可如常活动",
            },
            {
              label: "户外工作者",
              value: "建议可如常活动",
            },
            {
              label: "一般市民",
              value: "建议可如常活动",
            },
          ],
        },
        {
          levelId: 2,
          level: "二级",
          levelIdRange: "4-6",
          icon: erji,
          tips: [
            {
              label: "心血管系统或者 呼吸系统疾病患者",
              value: "应减少户外体力消耗，以及减少在户外逗留的时间。",
            },
            {
              label: "儿童以及长者",
              value: "建议可如常活动",
            },
            {
              label: "户外工作者",
              value: "建议可如常活动",
            },
            {
              label: "一般市民",
              value: "建议可如常活动",
            },
          ],
        },
        {
          levelId: 3,
          level: "三级",
          levelIdRange: "7-10",
          icon: sanji,
          tips: [
            {
              label: "心血管系统或者呼吸系统疾病患者",
              value:
                "应尽量减少户外体力消耗， 以及尽量减少在户外逗留的的时间。尽量紧闭门窗，开启空气净化器。",
            },
            {
              label: "儿童以及长者",
              value:
                "应尽量减少户外体力消耗，以及尽量减少在户外逗留的 的时间。尽量紧闭门窗，开启空气净化器。",
            },
            {
              label: "户外工作者",
              value: "应减少户外体力消耗，以及减少在户外逗留的时间。",
            },
            {
              label: "一般市民",
              value: "应减少户外体力消耗，以及减少在户外逗留的时间。",
            },
          ],
        },
        {
          levelId: 4,
          level: "四级",
          levelIdRange: "10+",
          icon: siji,
          tips: [
            {
              label: "心血管系统或者 呼吸系统疾病患者",
              value:
                "应避免户外体力消耗，以及避免在户外逗留，尽量紧闭门窗，开启空气净化器。",
            },
            {
              label: "儿童以及长者",
              value:
                "应避免户外体力消耗，以及避免在户外逗留，尽量紧闭门窗，开启空气净化器。",
            },
            {
              label: "户外工作者",
              value:
                "应避免户外体力消耗，以及避免在户外逗留，尽量紧闭门窗，开启空气净化器。",
            },
            {
              label: "一般市民",
              value:
                "应尽量减少户外体力消耗， 以及尽量减少在户外逗留的的时间。尽量紧闭门窗，开启空气净化器。",
            },
          ],
        },
      ],
      tipList: [],
      visible: false,

    };
  },
  watch: {
    map(newVal) {
      if (newVal) {
        this.drawStreet();
      }
    },
  },
  beforeDestroy() {
    this.map.clearMap();
  },
  created() {
    this.getCountyHourData();
  },
  methods: {
    getPeriodData(data) {
      this.periodValue = data.periodValue;

      this.startDate =
        data.periodValue === 0 ? data.startDate : data.startDate + " 00:00:00";
      this.endDate =
        data.periodValue === 0 ? data.endDate : data.endDate + " 23:59:59";

      // this.startDate = "2024-12-01 00:00:00";
      // this.endDate = "2024-12-31 23:59:59";
      this.getAirStreetData();
    },
    getAirStreetData() {
      this.loading = true;
      streetFindByTime({
        startTime: this.startDate,
        endTime: this.endDate,
        timeType: this.periodValue,
      })
        .then((res) => {
          this.dataList = res.data.data.list;
          this.chartData = res.data.data.levelList.map((item) => ({
            name: item.name,
            value: item.count,
            rate: item.rate,
          }));
          this.loading = false;

          if (this.map) {
            this.drawStreet();
          }
        })
        .catch(() => {
          this.loading = false;
        });
    },
    getCountyHourData() {
      countyHour().then((res) => {
        this.countyHourData = res.data.data;
        this.tipList = this.hintLevelInfoList[
          this.countyHourData.levelId - 1
        ].tips;
      });
    },

    changeDay() {
      this.isDay = !this.isDay;
      this.chartData = this.getChartData();
      this.map.clearMap();
      this.drawStreet();
    },
    drawStreet() {
      const _this = this;

      for (const item of this.polygonList) {
        this.map.remove(item);
      }

      for (const item of this.markerList) {
        this.map.remove(item);
      }

      new AMap.GeoJSON({
        geoJSON: jinniuStreet,
        // 还可以自定义getMarker和getPolyline
        getPolygon: function(geojson, lnglats) {
          const levelId = _this.dataList.find(
            (item) => item.streetCode === geojson.properties.code
          )?.levelId;
          const polygon = new AMap.Polygon({
            path: lnglats,
            fillColor: levelId ? _this.colorMap[levelId] : "transparent",
            strokeOpacity: 1,
            fillOpacity: 0.5,
            strokeColor: "#0087F9",
            strokeWeight: 2,
            // extrusionHeight: 300,
            strokeStyle: "dashed",
            strokeDasharray: [5, 5],
            extData: {
              streetCode: geojson.properties.code,
            },
          });
          _this.polygonList.push(polygon);
          _this.map.add(polygon);
          polygon.on("mouseover", () => {
            polygon.setOptions({
              fillOpacity: 0.7,
            });
          });
          polygon.on("mouseout", () => {
            polygon.setOptions({
              fillOpacity: 0.5,
            });
          });

          if (geojson.properties.name !== "金牛区") {
            const levelId = _this.dataList.find(
              (item) => item.streetCode === geojson.properties.code
            )?.levelId;
            const healthLevel = _this.levelMap[levelId];
            const marker = new AMap.Marker({
              position: new AMap.LngLat(
                geojson.properties.center.lng,
                geojson.properties.center.lat
              ),
              // icon: icon,
              content: `<div class="noise river-content">
              <div>
                <img src="${markerTitle}" />
                <span id="${geojson.properties.code}">${
                geojson.properties.name
              }</span>
              </div>
              <div class="warter-type">
                <span>健康风险等级：<span style="color: ${
                  _this.colorMap[levelId]
                };"> ${healthLevel || "--"}</span></span>
                <span> </span>
              </div>
            </div>`,
              offset: new AMap.Pixel(-86, -100),
              extData: {
                streetCode: geojson.properties.code,
              },
            });

            _this.map.add(marker);
            _this.markerList.push(marker);

            // 点击联动功能
            marker.on("click", (e) => {
              _this.handleClick(geojson.properties.code)
            });


            marker.on("mouseover", (e) => {
              const streetPolygon = _this.polygonList.find(
                (item) =>
                  item.getExtData().streetCode ===
                  e.target.getExtData().streetCode
              );
              streetPolygon.setOptions({
                fillOpacity: 0.7,
              });
            });
            marker.on("mouseout", (e) => {
              const streetPolygon = _this.polygonList.find(
                (item) =>
                  item.getExtData().streetCode ===
                  e.target.getExtData().streetCode
              );
              streetPolygon.setOptions({
                fillOpacity: 0.5,
              });
            });
          }
        },
      });
    },
    getErCvdColor(value) {
      if (value <= 18.43) return this.colorMap[1];
      if (value <= 28.49) return this.colorMap[2];
      if (value <= 68.43) return this.colorMap[3];
      return this.colorMap[4];
    },
    getErResColor(value) {
      if (value <= 16.43) return this.colorMap[1];
      if (value <= 22.43) return this.colorMap[2];
      if (value <= 45.45) return this.colorMap[3];
      return this.colorMap[4];
    },
    handleClick(code, latLng) {
      const index = this.dataList.findIndex(el => el.streetCode === code)
      document
        .getElementById(this.activeCode)
        ?.classList.remove("marker-street-name");

      this.activeCode = code;
      this.activeIndex = index;

      document
        .getElementById(code)
        .classList.add("marker-street-name");

      latLng && this.map.panTo(new AMap.LngLat(latLng.lng, latLng.lat))

      // // listData length无变化，仅仅是属性变更，手动调用下组件内部的reset方法
      // this.$refs.seamlessScroll.reset();
    },
    toggleTag() {
      this.activeTag = this.activeTag === 1 ? 2 : 1;
    }
  },
};
</script>
<style lang="less" scoped>
.nodata {
  // height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  img {
    width: 100%;
  }
  .no_data_text {
    font-size: 16px;
    font-family: PingFang SC;
    font-weight: 500;
    color: #d8e8fe;
    line-height: 30px;
    text-shadow: 0 0 5px rgb(63, 63, 221), 0 0 5px rgb(13, 13, 218);
  }
}
.air-health {
  width: 100vw !important;
  height: 10px;
}
.right,
.left {
  position: absolute;
  padding: 0 30px;
  width: 480px;
  height: calc(100vh - 100px);
}
.left {
  background: linear-gradient(
    to right,
    #000719,
    #000719d9,
    #000719d9,
    #000719d9,
    #0007190d
  );
}
.right {
  right: 0;
  background: linear-gradient(
    to left,
    #000719,
    #000719d9,
    #000719d9,
    #000719d9,
    #0007190d
  );
  .header {
    display: flex;
    color: #6ba1e4;
    line-height: 52px;
    text-align: center;
    cursor: pointer;
    .tag {
      flex: 1;
      height: 52px;
      &.active {
        color: #ffffff;
        // 文本发光
        text-shadow: 0 0 2px white, 0 0 2px white;
        &.headerLeft {
          background-image: url(~@/assets/health/right/tag-left-active.webp);
        }
        &.headerRight {
          background-image: url(~@/assets/health/right/tag-right-active.webp);
          margin-left: -6px;
        }
      }
    }
    .headerLeft {
      background-image: url(~@/assets/health/right/tag-left.webp);
    }
    .headerRight {
      background-image: url(~@/assets/health/right/tag-right.webp);
      margin-left: -6px;
    }
  }
  .update-time {
    display: flex;
    justify-content: flex-end;
    color: #739fb8;
    font-size: 12px;
    white-space: nowrap;
    margin-top: 10px;
  }
}
.title {
  margin-top: 40px;
  font-family: Source Han Sans CN;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0 0 5px blue, 0 0 5px blue;
  font-size: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 420px;
  .selects {
    display: flex;
    flex-wrap: nowrap;
  }
}
.sub-title {
  margin-bottom: 15px;
}

.street {
  width: 400px;
  height: 80px;
  background: url("~@/assets/health/<EMAIL>") no-repeat center center;
  background-size: 100% 100%;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  padding: 20px 12px;
  cursor: pointer;
  &:hover,
  &.active {
    background: url("~@/assets/health/<EMAIL>") no-repeat center center !important;
    background-size: 100% 100%;
  }
  .db {
    font-size: 12px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 66px;
    .db-num {
      font-size: 20px;
      font-weight: bold;
    }
    .db-text {
      color: #86add5;
    }
    .db-num {
      color: #27bfff;
      &.active {
        color: #fff !important;
      }
    }
  }
  .street-info {
    flex: 1;
    padding-left: 20px;
    .street-info-top {
      width: 100%;
      display: flex;
      align-items: center;
      .street-name {
        font-size: 16px;
        width: 150px;
        color: #c9dcea;
      }
      .el-icon-location-outline {
        font-size: 20px;
        margin-left: auto;
        color: #6796c0;
        &.active {
          color: #01aae9;
        }
      }
      .person-count {
        width: 105px;
        height: 20px;
        background: #0c3972;
        border-radius: 2px;
        font-size: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: auto;
        &.active {
          background: #1a63ae;
        }
      }
    }
    .street-info-bottom {
      margin-top: 3px;
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: #c0d7de;
      .noise-level {
        color: #26b8f7;
      }
    }
  }
}
.street_box {
  height: 800px;
  overflow-y: scroll;
}
// 滚动条样式
// ::-webkit-scrollbar {
//   width: 7px;
//   height: 7px;
// }
::-webkit-scrollbar-thumb {
  background: transparent;
}
::-webkit-scrollbar-track {
  background-color: transparent; /* 轨道背景颜色 */
}

.tab-switch-button,
.dialog-button {
  width: 94px;
  height: 30px;
  background: url("~@/assets/health/<EMAIL>") no-repeat center center;
  background-size: 100% 100%;
  color: #3adbff;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: auto;
  cursor: pointer;
  font-size: 12px;
  &:hover {
    opacity: 0.8;
  }
}
.tab-switch-button {
  margin-bottom: 10px;
}
.dialog-button {
  width: 110px;
  background: url("~@/assets/health/<EMAIL>") no-repeat center center;
  background-size: 100% 100%;
  position: absolute;
  right: -90px;
  top: -10px;
}
.gauge-box {
  width: 260px;
  height: 186px;
  margin: 10px auto 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  .bg1,
  .bg2,
  .level {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
  }
  .bg2 {
    bottom: -10px;
  }
  .gauge {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
  }
  .aqhi {
    margin-bottom: 20px;
    font-size: 40px;
    font-family: "CAI978";
    color: #6bd197;
  }
  .level {
    border: 1px solid;
    bottom: 30px;
    border-radius: 10px;
    padding: 0 15px;
  }
}
.legend-box {
  padding: 16px 0;
  margin-bottom: 25px;
  background: linear-gradient(
    to right,
    transparent,
    #04254db3,
    #04254d,
    #04254db3,
    transparent
  );
  border: 1px solid;
  border-image: linear-gradient(to right, #012c62, #2e7fdb, #012c62) 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  > div:nth-child(1) {
    width: 118px;
    font-size: 13px;
    color: #cde8fd;
  }
  .color-list,
  .num-list,
  .level-list {
    display: flex;
  }
  .color-item,
  .num-item {
    width: 22px;
    height: 16px;
    text-align: center;
    line-height: 18px;
  }
  .num-list {
    margin-top: 3px;
  }
  .color-box {
    position: relative;
  }
  .level-list {
    position: absolute;
    top: 0;
    > div {
      width: 66px;
      text-align: center;
      line-height: 18px;
      color: #fff;
      font-size: 12px;
    }
  }
}
.health-bar-list {
  display: flex;
  justify-content: space-between;
  .health-bar-item {
    .health-bar {
      position: relative;
      margin-bottom: 12px;
      width: 176px;
      height: 6px;
      background: #012c62;
      border-radius: 3px;
      .health-bar-value {
        height: 6px;
        border-radius: 3px;
        position: absolute;
        left: 0;
        top: 0;
        img {
          position: absolute;
          right: 0;
          top: 50%;
          transform: translateY(-50%) translateX(50%);
        }
      }
    }
    .health-index {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 13px;
      color: #cde8fd;
      .index-value {
        color: #26b8f7;
        font-weight: bold;
      }
    }
  }
}
.hint-level {
  width: 416px;
  height: 38px;
  border: 1px solid;
  text-align: center;
  line-height: 38px;
  font-size: 16px;
  margin-bottom: 20px;
}
.hint-list {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  .hint-item {
    width: 210px;
    height: 163px;
    background: url("~@/assets/health/<EMAIL>") no-repeat center center;
    background-size: 100% 100%;
    padding: 15px;
    font-size: 15px;
    > p:nth-child(1) {
      color: #27bfff;
      height: 32px;
    }
    > p:nth-child(2) {
      color: #bde1ed;
    }
  }
}
.modal-tip {
  color: #cae4f2;
  margin: 30px 0 50px;
  font-size: 16px;
}
.tip-title {
  display: flex;
  justify-content: space-between;
  color: #3fbafa;
  font-size: 16px;
  margin-bottom: 6px;
  > div:nth-child(1) {
    text-align: center;
  }
}
.tip-table {
  color: #cddef6;
  font-size: 16px;
  .tip-table-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 110px;
    &:nth-child(even) {
      background: #031933;
    }
    &:nth-child(odd) {
      background: #04254d;
    }
    .level-item {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
    }
    .tip-level {
      border-bottom: 1px solid;
      width: 90px;
      padding-left: 12px;
      > img {
        margin-right: 8px;
      }
    }
  }
}
.tip-item,
.level-item,
.tip-title > div {
  width: 200px;
}
.seamlessScroll {
  width: 100%;
  height: 800px;
  overflow: hidden;
}
</style>
<style lang="less">
#air-health-modal {
  .ant-modal-content {
    background: url("~@/assets/health/modal.png") no-repeat center center;
    background-size: 100% 100%;
    padding: 40px 70px 50px;
  }
  .ant-modal-close {
    position: absolute;
    top: 30px;
    right: 60px;
  }
}

.marker-street-name {
  position: relative;
  animation: selectedAnimation 1s ease-in-out infinite;
}

// 选中动画
@keyframes selectedAnimation {
  0% {
    left: 0;
  }
  50% {
    left: 12px;
  }
  100% {
    left: 0;
  }
}
</style>
