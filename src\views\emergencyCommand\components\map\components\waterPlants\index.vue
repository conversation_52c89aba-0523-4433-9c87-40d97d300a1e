<template>
  <div class="waterPlants">
    <echartsLine
      v-if="chartXdata.length"
      :chartX="chartXdata"
      :echartData="chartYdata"
      :dataType="typeId"
      :chartLegendName="chartLegendName"
    ></echartsLine>
    <div class="chose-box">
      <div
        @click="choseText(ite)"
        class="chose-item"
        :class="ite.flag ? 'choseing' : ''"
        v-for="(ite, ind) in lableArr"
        :key="ind + 'ci'"
      >
        {{ ite.text || '' }}
      </div>
    </div>
  </div>
</template>

<script name="emergencyDetails">
  import echartsLine from './echartsLine.vue'

  // const emit = defineEmits(['detailChange'])
  export default {
    name: 'waterEmergencyDetails',
    components: {
      echartsLine,
    },
    props: {
      detailData: {
        type: Object,
        default: () => ({
          data: [],
        }),
      },
      curStationId: {
        type: String,
        default: '',
      },
      echartData:{
        type: Object,
        default(){
          return {}
        }
      },
      chartX:{
        type: Object,
        default(){
          return {}
        }
      },
      nameArr:{
        type: Object,
        default(){
          return {}
        }
      }
    },
    data() {
      return {
        lableArr: [
          { text: '氨氮', flag: true, id: 'nh3n' },
          { text: '高锰酸盐指数', flag: false, id: 'codmn' },
          { text: '酸碱度', flag: false, id: 'ph' },
          { text: '总磷', flag: false, id: 'total_phosphorus' },
          { text: '溶解氧', flag: false, id: 'dissolved_oxygen' },
        ],
        selectData: [],
        zhanFlag: false,
        stationName: '请选择站点',
        stationId: '',
        typeId: 'nh3n',
      }
    },
    watch: {
      
    },
    computed: {
      chartXdata(){
        // console.log(this.chartX[this.typeId],'------------------84');
        return this.chartX[this.typeId]
      },
      chartYdata(){
        // console.log(this.echartData[this.typeId],'------------------88');
        return this.echartData[this.typeId]
      },
      chartLegendName(){
        return this.nameArr[this.typeId]
      }
    },
    mounted() {
      console.log('------------mounted---------waterPlants');
    },
    methods: {
      /**
       * 监测项选择
       */
      choseText(val) {
        // console.log(val.text)
        this.lableArr.forEach((e) => {
          e.flag = false
        })
        val.flag = true
        this.typeId = val.id
      },
    },
  }
</script>

<style lang="less" scoped>
  .waterPlants {
    .chose-box {
      display: flex;
      // align-items: center;
      justify-content: space-between;
      background-color: rgba(0, 35, 60, 0.7);
      border-top: 1px solid rgba(16, 201, 255, 0.6);
      border-bottom: 1px solid rgba(16, 201, 255, 0.6);
      margin-bottom: 5px;
      .chose-item {
        color: #93a5b4;
        padding: 3px 12px;
        font-size: 14px;
        cursor: pointer;
        transition: 0.3s;
        &:hover {
          background-color: rgba(16, 201, 255, 0.3);
        }
      }
      .choseing {
        background-color: #014c80;
      }
    }
    .select-box {
      // padding: 5px 10px;
      width: 103px;
      height: 29px;
      font-size: 14px;
      background-image: url('../../../../../../assets/images/<EMAIL>');
      background-size: 100% 30px;
      background-repeat: no-repeat;
      position: relative;
      padding: 0 10px;
      .select-text {
        cursor: pointer;
        text-align: center;
        line-height: 29px;
        width: 100%;
        height: 100%;
        overflow: hidden;
        color: #addbfe;
        font-style: oblique;
      }
      .select-list {
        position: absolute;
        top: 30px;
        left: 12px;
        max-height: 110px;
        width: 80px;
        border: 1px solid #42d4dc;
        opacity: 0;
        transition: 0.3s;
        transform: rotateX(90deg);
        transform-origin: 0% 5%;
        // height: 0;
        z-index: 999;
        overflow: scroll;
        &::-webkit-scrollbar {
          display: none;
        }
        .select-list-item {
          height: 25px;
          line-height: 25px;
          padding-left: 3px;
          color: #addbfe;
          background-color: #052136;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          cursor: pointer;
          &:hover {
            background-color: #155281;
          }
          @keyframes siderbar {
            0% {
              transform: translateX(0);
              -webkit-transform: translateX(0);
            }
            100% {
              transform: translateX(-45px);
              -webkit-transform: translateX(-45px);
            }
          }
        }
        .chosetItem {
          background-color: #1f6ca7;
        }
      }
      .showL {
        opacity: 1;
        transform: rotateX(0deg);
        // height:auto
      }
    }
  }
</style>
