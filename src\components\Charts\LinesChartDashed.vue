<template>
    <div
        :id="id"
        :style="{ height: height, width: width }"
        @mousedown = "mousedown()"
        @mouseleave="mouseleave()"
    />
<!--    <div v-else>-->
<!--        <img-->
<!--                style="width:1.7rem;position:relative;left:2rem;top:0.3rem"-->
<!--                src="@/assets/pollution-not.png"-->
<!--                alt=""-->
<!--        />-->
<!--    </div>-->
</template>

<script lang="ts">
import * as echarts from 'echarts'
import { Component, Prop, Watch } from "vue-property-decorator";
import { mixins } from "vue-class-component";
import ResizeMixin from "./mixins/resize";
interface LineData {
    bottomList: string[];
    dataList: string[];
    unit: ''
    isAlarm: []
    name: ''
    dataList1: []
    dataList2: []
}
@Component({
    name: "LineChart"
})
export default class extends mixins(ResizeMixin) {
    @Prop({ default: "chart" }) private id!: string;
    @Prop({ default: "200px" }) private width!: string;
    @Prop({ default: "200px" }) private height!: string;
    @Prop({ required: true }) private propData!: LineData;
    // private chart: any = null;
    private option: any = {};
    @Watch("propData", { immediate: true, deep: true }) public onMsgChanged(
        newValue: LineData,
        oldValue: LineData
    ) {
        this.propData = newValue;
        console.log(newValue, 'newValue')
        if (newValue) {
            if (this.chart) {
                this.chart.clear();
                this.chart.dispose();
                this.chart = null;
            }
            this.$nextTick(() => {
                this.initChart();
            });
        }
    }
    mounted() {
        setTimeout(() => {
            this.initChart();
        }, 2000);
    }
    beforeDestroy() {
        if (!this.chart) {
            return;
        }
        this.chart.clear();
        this.chart.dispose();
        this.chart = null;
    }
    private min:any = 0
    private max:any = 0
    private splitNumber:any = 0
    private interval:any = 10
    private mousedown() {
        this.$emit('changeIndex', this.xIndex)
    }
    private mouseleave() {
        //@ts-ignore
        this.chart.dispatchAction({
            type: 'showTip',
            seriesIndex:0,  // 显示第几个series
            dataIndex: this.propData.dataList.length - 1 // 显示第几个数据
        });
    }
    private xIndex:any=''
    private initChart() {
        if(this.chart === null || this.chart === undefined) {
            this.chart = echarts.init(
                document.getElementById(this.id) as HTMLDivElement
            );
        }
        let that = this

        console.log(this.propData.dataList.length, 'this.propData.dataList.length')
        const option = {
            color: ['#10A2FD', '#F4A61C'],
            tooltip: {
                trigger: "axis",
                axisPointer: {
                    type: "cross",
                    label: {
                        backgroundColor: "#6a7985"
                    },
                },
                triggerOn: 'click',
                formatter:function(params:any){
                    var html = '';
                    if(params.length>0){
                        that.xIndex = params[0].dataIndex;
                        for ( var int = 0; int < params.length; int++) {
                            html+=params[int].seriesName+':'+params[int].data+'<br>';
                        }
                    }
                    return html;

                }
            },
            grid: {
              bottom: '25%',
              left: '8%',
              right: '8%'
            },
            legend: {
                right: '10%',
                textStyle: {
                    color: '#fff',
                    fontSize: 12
                },
                // borderWidth: '1',
                // borderColor: '#2DDCFF',
                data: [this.propData.name, '同比', '环比']
            },
            xAxis: [
                {
                    type: 'category',
                    data: this.propData.bottomList,
                    axisLine: {
                        lineStyle: {
                            color: '#FFFFFF',
                        }
                    },
                    axisTick: {
                        alignWithLabel: true,
                        show: false
                    },
                    axisLabel: {
                        color: '#86C6FF',
                        fontSize: 12,
                        formatter: function (item:any) {
                            let ret = ''
                            const temp1 = item.substr(11, 5) + '\n'
                            const temp2 = item.substr(5, 5)
                            ret = temp1 + temp2 // 凭借最终的字符串
                            return ret
                        }
                    },
                    interval: 50
                },
            ],
            dataZoom: [{
                type: 'slider',
                start: 100 - 1200 / this.propData.dataList.length,
                end: 100,
                show: true,
                height: '7%',
                bottom: '3%',
                borderColor: '#8FDFFE',
                backgroundColor: 'transparent',
                textStyle: {
                  color: '#8FDFFE'
                },
                zoomLock: true,
                handleColor: '#8FDFFE',

                handleSize: 20,

                handleStyle: {
                    borderColor: '#8FDFFE',
                    shadowBlur: 4,
                    shadowOffsetX: 1,
                    shadowOffsetY: 1,
                    shadowColor: '#e5e5e5'
                }
            }],
            yAxis: [
                {
                    type: 'value',
                    name: this.propData.unit,
                    axisTick: {
                        show: false
                    },
                    axisLine: {
                        show: false
                    },
                    splitLine: {
                        lineStyle: {
                            color: 'rgba(204,204,204, .4)',
                            type: 'dashed'
                        }
                    },
                    axisLabel: {
                        color: '#86C6FF',
                        fontSize: 12,
                    },
                    nameTextStyle: {
                        color: '#86C6FF',
                        fontSize: 12,
                    },
                    min: 0,
                    max: function(value:any) {
                        let max = value.max
                        let a = 0
                        if (max % 5 !== 0) {
                            a = Math.floor(max / 5) + 1
                        } else {
                            a = max / 5
                        }
                        that.splitNumber = a
                        return a * 5
                    }
                },
                {
                    type: 'value',
                    name: '(%)',
                    axisTick: {
                        show: false
                    },
                    axisLine: {
                        show: false
                    },
                    splitLine: {
                        show: false,
                        lineStyle: {
                            color: 'rgba(204,204,204, .4)',
                            type: 'dashed'
                        }
                    },
                    axisLabel: {
                        color: '#86C6FF',
                        fontSize: 12
                    },
                    nameTextStyle: {
                        color: '#86C6FF',
                        fontSize: 12
                    }
                }
            ],
            series: [
                {
                    name: this.propData.name,
                    type: 'bar',
                    data: this.propData.dataList,
                    barWidth: '8px',
                    itemStyle: {
                        normal: {
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                                offset: 0,
                                color: '#2DDCFF' // 0% 处的颜色
                            }, {
                                offset: 1,
                                color: '#2DDCFF' // 100% 处的颜色
                            }], false),
                            barBorderRadius: [2, 2, 0, 0]
                        }
                    }
                },
                {
                    name: '同比',
                    type: 'line',
                    smooth: true,
                    lineStyle: {
                        color: '#10A2FD'
                    },
                    yAxisIndex: 1,
                    symbol: "circle",
                    symbolSize: 1,
                    emphasis: {
                        scale: true,
                        itemStyle: {
                            color: '#10A2FD',
                            borderColor: "#fff",
                            borderWidth: 1,
                        },
                    },
                    areaStyle: {
                        color: {
                            type: "linear",
                            x: 0,
                            y: 0,
                            x2: 0,
                            y2: 1,
                            colorStops: [
                                {
                                    offset: 0,
                                    color: '#10A2FD'
                                },
                                {
                                    offset: 1,
                                    color: '#3F89FF' // 100% 处的颜色
                                }
                            ],
                            global: false
                        },
                    },
                    data: this.propData.dataList1
                },
                {
                    name: '环比',
                    type: 'line',
                    smooth: true,
                    lineStyle: {
                        color: '#F4A61C'
                    },
                    yAxisIndex: 1,
                    symbol: "circle",
                    symbolSize: 1,
                    emphasis: {
                        scale: true,
                        itemStyle: {
                            color: '#F4A61C',
                            borderColor: "#fff",
                            borderWidth: 1,
                        },
                    },
                    areaStyle: {
                        color: {
                            type: "linear",
                            x: 0,
                            y: 0,
                            x2: 0,
                            y2: 1,
                            colorStops: [
                                {
                                    offset: 0,
                                    color: 'rgba(244,166,28, .4)'
                                },
                                {
                                    offset: 1,
                                    color: 'rgba(244,166,28, 0)' // 100% 处的颜色
                                }
                            ],
                            global: false
                        },
                    },
                    data: this.propData.dataList2
                },
            ]
        };
        this.chart.on('dataZoom', function (event:any) {
            let start = 0, end = 0
            if(event.batch){
                start =event.batch[0].start;
                end=event.batch[0].end;
            }else{
                start=event.start;
                end=event.end;
            };
            option.dataZoom[0].start=start;
            option.dataZoom[0].end=end
            // option.yAxis[1].interval=that.interval
            // @ts-ignore
            that.chart.setOption(option as EChartOption<EChartOption>)
        })
        // this.chart.on('click', function (event:any) {
        //     console.log(event, 'event')
        //     let index = event.dataIndex
        //     that.$emit('changeIndex', index)
        // })
        // @ts-ignore
        this.chart.setOption(option as EChartOption<EChartOption>);
        setTimeout(() => {
            // @ts-ignore
            this.chart.dispatchAction({
                type: 'showTip',
                seriesIndex:0,  // 显示第几个series
                dataIndex: this.propData.dataList.length - 1 // 显示第几个数据
            });
        })
    }
}
</script>

