@import "./transition.less";
@import "./svgicon.less";
@font-face {
  font-family: "CAI978";
  src: url("../assets/font/DS-DIGII.ttf");
  font-display: swap;
}
@font-face {
  font-family: "YouSheBiaoTiHei";
  src: url("../assets/font/YouSheBiaoTiHei-2.ttf");
  font-display: swap;
}
@font-face {
  font-family: "300-CAI978";
  src: url("../assets/font/300-CAI978-2.ttf");
  font-display: swap;
}
@font-face {
  font-family: "PangMenZhengDao";
  src: url("../assets/font/PangMenZhengDao.ttf");
  font-display: swap;
}
html {
  font-size: 62.5%;
}
body {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  overflow: hidden;
}
::-webkit-scrollbar {
  width: 0.05rem;
  height: 0.05rem;
  /**/
}
::-webkit-scrollbar-track {
  background: #efefef;
  border-radius: 2px;
}
::-webkit-scrollbar-thumb {
  background: #bfbfbf;
  border-radius: 0.05rem;
}
::-webkit-scrollbar-thumb:hover {
  background: #333;
}
::-webkit-scrollbar-corner {
  background: #469fe7;
}
.common-main {
  width: 100%;
  padding: 0 0.4rem;
  display: flex;
  justify-content: space-between;
}
.common-main .left-part {
  z-index: 99;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  align-items: center;
}
.common-main .middle-part {
  z-index: 99;
  position: relative;
  width: 9.5rem;
  height: 2rem;
  margin-top: 0.45rem;
  background: rgba(22, 52, 73, 0.29);
  box-shadow: inset 0px 0px 60px 0px rgba(24, 36, 161, 0.5);
}
.common-main .middle-part .middle-title {
  font-size: 0.2rem;
  font-family: Source Han Sans CN;
  font-weight: 500;
  color: #ffffff;
  text-shadow: 0 0 0.01rem, 0 0 0.03rem;
  position: absolute;
  top: -0.22rem;
  left: 3.5rem;
  width: 2.32rem;
  line-height: 0.43rem;
  text-align: center;
}
.common-main .middle-part .middle-title-bg {
  position: absolute;
  top: -0.22rem;
  left: 3.5rem;
}
.common-main .middle-part .middle-title-bg .title-img {
  width: 2.32rem;
}
.common-main .middle-center {
  position: relative;
}
.common-main .right-part {
  z-index: 99;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  align-items: center;
}
.common-title .title {
  font-size: 0.2rem;
  font-family: Source Han Sans CN;
  font-weight: 500;
  text-shadow: 0 0 5px blue,
      0 0 5px blue;
}
.common-title .sub-title {
  margin: 0 0 0.15rem;
}
.common-title .sub-title .title-img {
  width: 3.54rem;
  height: 0.1rem;
}
.common-content {
  width: 3.5rem;
  height: 2rem;
}
a.amap-logo {
  display: none !important;
}
.ant-select-dropdown {
  margin: 0;
  padding: 0;
  color: #8EB3F6;
  font-variant: tabular-nums;
  line-height: 1.5;
  list-style: none;
  -webkit-font-feature-settings: 'tnum';
  font-feature-settings: 'tnum';
  position: absolute;
  top: -9999px;
  left: -9999px;
  z-index: 1050;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  font-size: 14px;
  font-variant: initial;
  background-color: #012474;
  border-radius: 4px;
  outline: none;
  -webkit-box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}
.ant-select-dropdown-menu-item {
  position: relative;
  font-size: 0.18rem;
  display: block;
  padding: 5px 12px;
  overflow: hidden;
  color: #8EB3F6;
  font-weight: normal;
  line-height: 22px;
  white-space: nowrap;
  text-overflow: ellipsis;
  text-align: center;
  cursor: pointer;
  -webkit-transition: background 0.3s ease;
  transition: background 0.3s ease;
}
.ant-select-dropdown-menu-item-selected,
.ant-select-dropdown-menu-item-selected:hover {
  color: #ffffff !important;
  font-weight: 0 !important;
  background-color: #0061C6 !important;
}
.ant-select-dropdown-menu-item:hover {
  color: #ffffff !important;
  background-color: #0061C6 !important;
}
.ant-message-notice-content {
  display: inline-block;
  padding: 10px 16px;
  background: #e17337;
  border-radius: 4px;
  -webkit-box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  pointer-events: all;
}
.ant-message {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  color: #fff;
  font-size: 14px;
  font-variant: tabular-nums;
  line-height: 1.5;
  list-style: none;
  -webkit-font-feature-settings: 'tnum';
  font-feature-settings: 'tnum';
  position: fixed;
  top: 16px;
  left: 0;
  z-index: 1010;
  width: 100%;
  pointer-events: none;
}
.ant-message-error .anticon {
  color: #fff;
}
.video-js .vjs-big-play-button {
  font-size: 3em;
  line-height: 1.5em;
  height: 1.5em;
  width: 3em;
  display: block;
  position: absolute;
  top: 40% !important;
  left: 40% !important;
  padding: 0;
  cursor: pointer;
  opacity: 1;
  border: 0.06666em solid #fff;
  background-color: #2B333F;
  background-color: rgba(43, 51, 63, 0.7);
  border-radius: 0.3em;
  -webkit-transition: all 0.4s;
  transition: all 0.4s;
}
.video-player {
  position: relative;
}
.video-player .custom-button {
  position: absolute;
  height: 0.45rem;
  width: 0.9rem;
  display: block;
  top: 40% !important;
  left: 40% !important;
  border-radius: 0.09rem;
  border: 0.06666em solid transparent;
  cursor: pointer;
  padding: 0;
}
.vjs-big-play-button {
  margin: 0!important;
}
.video-wrapper {
  width: 100%;
  height: 2rem!important;
}
.water-right-divs .video-wrapper {
  width: 100%;
  height: calc(1.88rem - (10.8rem - 1080px) / 3) !important;
}
.water-right-divs .video-inner {
  height: calc(1.88rem - (10.8rem - 1080px) / 3) !important;
}
.water-right-divs .player-wrapper {
  height: calc(1.88rem - (10.8rem - 1080px) / 3) !important;
}
.drain-area .video-wrapper {
  width: 100%;
  height: calc(2.6rem - (10.8rem - 1080px) / 2) !important;
}
.drain-area .video-inner {
  height: calc(2.6rem - (10.8rem - 1080px) / 2) !important;
}
@keyframes fly-wind {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}
.video-js .vjs-tech {
  object-fit: fill;
}
.ant-empty-description {
  color: #fff;
}

