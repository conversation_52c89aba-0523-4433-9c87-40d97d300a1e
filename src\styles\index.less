@import"./transition.less";
@import"./svgicon.less";
@import './atom.less';

@font-face {
  font-family: "CAI978";
  // src: url("../assets/font/300-CAI978-2.ttf");
  src: url("../assets/font/DS-DIGII.ttf");
  font-display: swap;
}
@font-face {
  font-family: "YouSheBiaoTiHei";
  // src: url("../assets/font/300-CAI978-2.ttf");
  src: url("../assets/font/YouSheBiaoTiHei-2.ttf");
  font-display: swap;
}

@font-face {
  font-family: "300-CAI978";
  src: url("../assets/font/300-CAI978-2.ttf");
  // src: url("../assets/font/DS-DIGII.ttf");
  font-display: swap;
}
@font-face {
  font-family: "PangMenZhengDao";
  src: url("../assets/font/PangMenZhengDao.ttf");
  font-display: swap;
}
html {
  font-size: 62.5%;
}

body {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  overflow: hidden;
  // font-size: 14px;
  // font-size: 1.4rem;
}

::-webkit-scrollbar {
  width: 0.05rem;
  height: 0.05rem;
  /**/
}

::-webkit-scrollbar-track {
  background: rgb(239, 239, 239);
  border-radius: 2px;
}

::-webkit-scrollbar-thumb {
  background: #bfbfbf;
  border-radius: 0.05rem;
}

::-webkit-scrollbar-thumb:hover {
  background: #333;
}

::-webkit-scrollbar-corner {
  background: #469fe7;
}

// 页面公用布局样式

// 主页面
.common-main {
  width: 100%;
  padding: 0 0.4rem;
  display: flex;
  justify-content: space-between;

  .left-part {
    z-index: 9;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    align-items: center;
  }

  .middle-part {
    z-index: 99;
    position: relative;
    width: 9.5rem;
    height: 2rem;
    margin-top: 0.45rem;
    // border: 1px solid rgba(54, 218, 234, 0.42);
    background: rgba(22, 52, 73, 0.29);
    box-shadow: inset 0px 0px 60px 0px rgba(24, 36, 161, 0.5);

    .middle-title {
      font-size: 0.2rem;
      font-family: Source Han Sans CN;
      font-weight: 500;
      color: rgba(255, 255, 255, 1);
      text-shadow: 0 0 0.01rem, 0 0 0.03rem;
      position: absolute;
      top: -0.22rem;
      left: 3.5rem;
      width: 2.32rem;
      line-height: 0.43rem;
      text-align: center;
    }

    .middle-title-bg {
      position: absolute;
      top: -0.22rem;
      left: 3.5rem;

      .title-img {
        width: 2.32rem;
      }
    }
  }

  .middle-center {
    position: relative;

    .middle-map {
      // position: absolute;
      // margin-top: 0.2rem;
      // top: 2.45rem;
      // width: 12rem;
      // margin-left: -1.25rem;
      // z-index: 1;
      // height: calc(1080px - 3.65rem);
    }
  }

  .right-part {
    z-index: 9;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    align-items: center;
  }
}

// 侧边栏标题
.common-title {
  .title {
    font-size: 0.2rem;
    font-family: Source Han Sans CN;
    font-weight: 500;
    // color: rgba(255, 255, 255, 1);
    text-shadow: 0 0 5px blue,
      0 0 5px blue;
  }

  .sub-title {
    margin: 0 0 0.15rem;

    .title-img {
      width: 3.54rem;
      height: 0.1rem;
    }
  }
}

// 侧边栏内容
.common-content {
  width: 3.5rem;
  height: 2rem;
  // border: 1px solid rgba(54, 218, 234, 0.42);
}

a.amap-logo {
  display: none !important;
}

// ant style rewrite
// Select
.ant-select-dropdown {
  margin: 0;
  padding: 0;
  color: #8EB3F6;
  font-variant: tabular-nums;
  line-height: 1.5;
  list-style: none;
  -webkit-font-feature-settings: 'tnum';
  font-feature-settings: 'tnum';
  position: absolute;
  top: -9999px;
  left: -9999px;
  z-index: 1050;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  font-size: 14px;
  font-variant: initial;
  background-color: #012474;
  border-radius: 4px;
  outline: none;
  -webkit-box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.ant-select-dropdown-menu-item {
  position: relative;
  font-size: 0.18rem;
  display: block;
  padding: 5px 12px;
  overflow: hidden;
  color: #8EB3F6;
  font-weight: normal;
  line-height: 22px;
  white-space: nowrap;
  text-overflow: ellipsis;
  text-align: center;
  cursor: pointer;
  -webkit-transition: background 0.3s ease;
  transition: background 0.3s ease;
}

.ant-select-dropdown-menu-item-selected,
.ant-select-dropdown-menu-item-selected:hover {
  color: rgb(255, 255, 255) !important;
  font-weight: 0 !important;
  background-color: #0061C6 !important;
}

.ant-select-dropdown-menu-item:hover {
  color: rgb(255, 255, 255) !important;
  background-color: #0061C6 !important;
}

// Message
.ant-message-notice-content {
  display: inline-block;
  padding: 10px 16px;
  background: #e17337;
  border-radius: 4px;
  -webkit-box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  pointer-events: all;
}

.ant-message {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  color: #fff;
  font-size: 14px;
  font-variant: tabular-nums;
  line-height: 1.5;
  list-style: none;
  -webkit-font-feature-settings: 'tnum';
  font-feature-settings: 'tnum';
  position: fixed;
  top: 16px;
  left: 0;
  z-index: 1010;
  width: 100%;
  pointer-events: none;
}

.ant-message-error .anticon {
  color: #fff;
}

// video js style rewrite
.video-js .vjs-big-play-button {
  font-size: 3em;
  line-height: 1.5em;
  height: 1.5em;
  width: 3em;
  display: block;
  position: absolute;
  top: 40% !important;
  left: 40% !important;
  padding: 0;
  cursor: pointer;
  opacity: 1;
  border: 0.06666em solid #fff;
  background-color: #2B333F;
  background-color: rgba(43, 51, 63, 0.7);
  border-radius: 0.3em;
  -webkit-transition: all 0.4s;
  transition: all 0.4s;
}
.video-player {
  position: relative;
}
.video-player .custom-button {
  position: absolute;
  height: 0.45rem;
  width: 0.9rem;
  display: block;
  top: 40% !important;
  left: 40% !important;
  border-radius: 0.09rem;
  border: 0.06666em solid transparent;
  cursor: pointer;
  padding: 0;
}
.vjs-big-play-button {
  margin: 0!important;
}
.video-wrapper {
  width: 100%;
  height: 2rem!important;
}
.water-right-divs {
  .video-wrapper {
    width: 100%;
    height: calc(1.88rem - (10.8rem - 1080px) / 3)!important;
  }
  .video-inner {
    height: calc(1.88rem - (10.8rem - 1080px) / 3)!important;
  }
  .player-wrapper {
    height: calc(1.88rem - (10.8rem - 1080px) / 3)!important;
  }
}
.drain-area {
  .video-wrapper {
    width: 100%;
    height: calc(2.6rem - (10.8rem - 1080px) / 2)!important;
  }
  .video-inner {
    height: calc(2.6rem - (10.8rem - 1080px) / 2)!important;
  }
}

// animation
@keyframes fly-wind {
  0% {
    transform: rotate(0);
  }

  100% {
    transform: rotate(360deg);
  }
}

.video-js .vjs-tech {
  object-fit: fill;
}
.ant-empty-description{
  color: #fff;
}

.el-loading-mask {
  background-color: transparent;
}

.weather-marker {
  width: 74px;
  height: 100px;
  background: url(~@/assets/map_icon/marker.png) center / 100% 100% no-repeat;
}

.amap-marker-content {
  width: 0;
  height: 0;
}

.weather-marker-active {
  display: flex;
  flex-direction: column;
  align-items: center;
  transform: translate(52%, -57%);
  margin-left: 36px;
  .title {
    //min-width: 245px;
    width: 254px;
    height: 38px;
    //padding: 0 50px;
    line-height: 38px;
    background: url(~@/assets/map_icon/tooltip-bg.png) center / 100% 100% no-repeat;
    color: white;
    text-align: center;
    text-wrap: nowrap;
  }
  .marker {
    width: 74px;
    height: 100px;
    background: url(~@/assets/map_icon/marker-active.png) center / 100% 100% no-repeat;
  }
}

.weather-info {
  width: 371px;
  height: 231px;
  background: url(~@/assets/noise/tanchuang.png) center / 100% 100% no-repeat;
  transform: translate(9%, -34%);
  padding: 50px;
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 30px;
    margin-bottom: 6px;
    .title {
      width: 190px;
      font-family: YouSheBiaoTiHei;
      color: #FFFFFF;
      font-size: 16px;
      background: linear-gradient(0deg, #56D1ED 0%, #CAF6FF 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .close-btn {
      width: 23px;
      height: 23px;
      background: url(~@/assets/noise/<EMAIL>) center / 100% 100% no-repeat;
      cursor: pointer;
    }
  }

  .content {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 11px 10px;

    .item {
      display: grid;
      place-items: center;
      padding: 5px 0;
      width: 80px;
      height: 48px;
      background: rgba(14,139,255,0.2);
      border-radius: 4px;
      border: 1px solid #00FBFF;
      .label {
        font-size: 12px;
        color: #33C3E8;
        font-family: PingFang SC;
      }
      .value {
        font-size: 14px;
        color: #C8DFF0;
      }
    }
  }
}

.map-tooltip {
  //min-width: 245px;
  height: 38px;
  padding: 0 50px;
  line-height: 38px;
  background: url(~@/assets/map_icon/tooltip-bg.png) center / 100% 100% no-repeat;
  color: white;
  text-align: center;
  text-wrap: nowrap;
}


body {

  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  ::-webkit-scrollbar-thumb {
    border-radius: 10px;
    background-color: #0A4889;
    cursor: pointer;
  }

  ::-webkit-scrollbar-track {
    background-color: #0C2849;
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: #0A4889;
  }

}
