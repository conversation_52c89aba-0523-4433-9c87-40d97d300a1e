<template>
  <div id="distribut"></div>
</template>


<script>
import * as echarts from 'echarts'
// import 'echarts-gl'
// import dayjs from 'dayjs'
export default {
  name: 'cardBox',
  props: {
    butionCount: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      iconUrl: require('@/assets/sheng/<EMAIL>'),
    }
  },
  watch: {
    butionCount: {
      handler(newVal) {
        let data = [
          {
            name: '(0,40)',
            value: newVal.one,
          },
          {
            name: '(40,50)',
            value: newVal.two,
          },
          {
            name: '(50,60)',
            value: newVal.three,
          },
          {
            name: '(60,70)',
            value: newVal.four,
          },
          {
            name: '(70,80)',
            value: newVal.five,
          },
          {
            name: '(80,90)',
            value: newVal.six,
          },
          {
            name: '(90,100)',
            value: newVal.seven,
          },
        ]
        this.creatEharts(data)
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {
    let data = [
      {
        name: '(0,40)',
        value: this.butionCount.one,
      },
      {
        name: '(40,50)',
        value: this.butionCount.two,
      },
      {
        name: '(50,60)',
        value: this.butionCount.three,
      },
      {
        name: '(60,70)',
        value: this.butionCount.four,
      },
      {
        name: '(70,80)',
        value: this.butionCount.five,
      },
      {
        name: '(80,90)',
        value: this.butionCount.six,
      },
      {
        name: '(90,100)',
        value: this.butionCount.seven,
      },
    ]
    this.creatEharts(data)
  },
  methods: {
    creatEharts(data) {
      let option = {}
      let myChart = echarts.init(document.getElementById('distribut'))

      function getArrByKey(data, k) {
        let key = k || 'value'
        let res = []
        if (data) {
          data.forEach(function (t) {
            res.push(t[key])
          })
        }
        return res
      }
      function getSymbolData(data) {
        let arr = []
        for (var i = 0; i < data.length; i++) {
          arr.push({
            value: data[i].value,
            symbolPosition: 'end',
          })
        }
        return arr
      }

      // data = data.sort((a, b) => {
      //   return b.value - a.value
      // })
      option = {
        backgroundColor: '',
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
          // formatter: function (params) {
          //   // for (let i = 0; i < params.length; i++) {
          //   //   const v = params[i]
          //   //   return params.name === '占比'
          //   // }
          //   // console.log(params,'paramsparamsparamsparams');
          //   return [params[1]]
          // },
          rich: {
            title: {
              width: 25,
            },
          },
        },
        grid: {
          top: '3%',
          bottom: 3,
          right: 10,
          left: 4,
          containLabel: true,
        },
        xAxis: {
          show: true,
          axisTick: {
            show: false,
          },
          axisLine: {
            show: false,
            lineStyle: {
              color: '#B6D0D8',
            },
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#24a4ff34',
              width: 1,
            },
          },
        },
        yAxis: [
          {
            triggerEvent: true,
            show: true,
            inverse: true,
            data: getArrByKey(data, 'name'),
            axisLine: {
              show: true,
            },
            splitLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              show: true,
              interval: 0,
              color: '#B6D0D8',
              align: 'right',
              margin: 8,
              fontSize: 14,
              inside: false,
              // formatter: function (value, index) {
              //   return '{title|' + value + '}'
              // },
              // rich: {
              //   title: {
              //     width: 25,
              //   },
              // },
            },
          },
          {
            triggerEvent: true,
            show: true,
            inverse: true,
            data: getArrByKey(data, 'name'),
            axisLine: {
              show: false,
            },
            splitLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              show: false,
              interval: 0,
              shadowOffsetX: '-20px',
              color: ['#fff'],
              align: 'right',
              verticalAlign: 'bottom',
              lineHeight: 30,
              fontSize: 13,
              // formatter: function (value, index) {
              //   return (data[index].value / data[index].sum) * 100
              // },
            },
          },
        ],
        series: [
          {
            name: '',
            type: 'pictorialBar',
            symbol: 'image://' + this.iconUrl,
            symbolSize: [25, 25],
            symbolOffset: [10, 1],
            z: 12,
            itemStyle: {
              normal: {
                color: '#14b1eb',
              },
            },
            data: getSymbolData(data),
          },
          {
            name: '数量',
            type: 'bar',
            showBackground: true,
            barBorderRadius: 30,
            yAxisIndex: 0,
            data: data,
            barWidth: 4,
            // align: left,
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  1,
                  0,
                  [
                    {
                      offset: 0,
                      color: '#1E6BE5',
                    },
                    {
                      offset: 1,
                      color: '#00C6FF',
                    },
                  ],
                  false
                ),
                barBorderRadius: 10,
              },
              barBorderRadius: 4,
            },
            label: {
              normal: {
                color: '#fff',
                show: false,
                position: [0, '-20px'],
                textStyle: {
                  fontSize: 16,
                },
                formatter: function (a, b) {
                  return a.name
                },
              },
            },
          },
        ],
      }

      if (option) {
        myChart.setOption(option)
      }
    },
  },
}
</script>

<style lang="less">
#distribut {
  height: 210px;
  width: 100%;
}
</style>
