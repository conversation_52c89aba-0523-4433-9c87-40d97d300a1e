<style lang="less" scoped>
.no-data {
  font-size: 0.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
<template>
  <div
    v-if="propData.list.length > 0"
    :id="id"
    :style="{ height: height, width: width }"
  />
  <div v-else class="no-data" :style="{ height: height, width: width }">
    暂无数据
  </div>
</template>

<script lang="ts">
import * as echarts from 'echarts'
import { Component, Prop, Watch } from 'vue-property-decorator'
import { mixins } from 'vue-class-component'
import ResizeMixin from '@/components/Charts/mixins/resize'
interface AirData {
  list: any[]
  threshold: string | number| null
}
@Component({
  name: 'analysisAirLineChartDashed'
})
export default class extends mixins(ResizeMixin) {
  // @Prop({ default: "chart" }) private className!: string;
  @Prop({ default: 'chart' }) private id!: string
  @Prop({ default: '200px' }) private width!: string
  @Prop({ default: '200px' }) private height!: string
  @Prop({ required: true }) private propData!: AirData
  @Prop({ required: false, default: 'rgba(14,156,255,1)' })
  private bgColor!: string
  @Prop({ required: false, default: false }) private smooth!: boolean
  @Prop({ required: false, default: true }) private bgColorState!: boolean
  @Prop({ required: false, default: '' }) private xText!: string
  @Watch('propData', { immediate: true, deep: true }) public onMsgChanged(
    newValue: AirData,
    oldValue: AirData
  ) {
    if (this.propData) {
      if (this.chart) {
        this.chart.clear()
        this.chart.dispose()
        this.chart = null
      }
      this.$nextTick(() => {
        this.initChart()
      })
    }
  }
  mounted() {
    if (this.propData) {
      this.initChart()
    }
  }
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.clear()
    this.chart.dispose()
    this.chart = null
  }
  private bottomList: string[] = []
  private dataList: number[] = []
  private initChart() {
    if (this.chart === null || this.chart === undefined) {
      const chartDom = document.getElementById(this.id) as HTMLDivElement
      if (!chartDom) return
      this.chart = echarts.init(chartDom)
    }
    if(!this.propData.list) return
    this.bottomList = this.propData.list.map(item => {
        return item.time.substring(5, 16)
    })
    this.dataList = this.propData.list.map(item => {
        return item.value
    })
    this.chart.setOption({
      backgroundColor: 'transparent',
      color:['#40A3F8','#48EBE7'],
      legend: {
        right: 30,
        textStyle: {
          color: '#7BB7ED',
          fontSize: 12
        },
        itemHeight: 12
      },
      grid: {
        // height: '80%',
        left: '5%',
        top: '5%',
        right: '5%',
        bottom: '5%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        name: '',
        nameTextStyle: {
          fontSize: 20,
          color: '#fff',
          lineHeight: -30,
          align: 'center',
          verticalAlign: 'bottom'
        },
        data: this.bottomList,
        axisLabel: {
          textStyle: {
            fontSize: 12,
            color: 'white'
          }
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        }
      },
      yAxis: {
        type: 'value',
        name: '单位：mg/m³',
        min: 0,
        nameTextStyle: {
          color: '#fff',
          shadowOffsetX: 50
          // align: "center"
        },
        axisLabel: {
          textStyle: {
            fontSize: 12,
            color: 'white'
          }
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        splitLine: {
          lineStyle: {
            // type: "dashed"
            color: 'RGBA(2, 39, 75, 1)'
          }
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            backgroundColor: '#6a7985'
          }
        }
      },
      series: [
        // {
        //   name: '告警阈值',
        //   type: 'line',
        //   markLine: {
        //     symbol: 'none',
        //     label: {
        //       show: false
        //     },
        //     data: [
        //       {
        //         silent: false, //鼠标悬停事件  true没有，false有
        //         lineStyle: {
        //           //警戒线的样式  ，虚实  颜色
        //           type: 'dotted',
        //           color: 'red'
        //         },
        //         yAxis: this.propData.standard
        //       }
        //     ]
        //   }
        // },
        {
          // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
          // @ts-ignore
          name: 'VOCs',
          data: this.dataList,
          type: 'line',
          smooth: this.smooth,
          lineStyle: {
            color: '#15B4FE' //改变折线颜色
          },
          symbol: 'circle',
          symbolSize: 5,
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: this.bgColorState
                    ? 'RGBA(21,180,254, 0.5)'
                    : 'transparent' // 0% 处的颜色
                },
                {
                  offset: 1,
                  color: 'RGBA(25,252,255, 0)' // 100% 处的颜色
                }
              ],
              global: false
            },
            shadowColor: 'rgba(0,85,250,0)',
            shadowBlur: 20
          },
          itemStyle: {
            color: (params:any) => {
              return (
                (this.propData.threshold || this.propData.threshold === 0 || this.propData.threshold === '0')
                && params.value > this.propData.threshold) ? 'red' : '#1DCCFF'
            }, //改变折线点的颜色 '#1DCCFF'
            borderColor: '#fff'
          }
        },
        // {
        //   // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
        //   // @ts-ignore
        //   name: `${this.propData.itemName}均值`,
        //   data: this.dataList1,
        //   type: 'line',
        //   smooth: this.smooth,
        //   lineStyle: {
        //     color: '#15FEFE' //改变折线颜色
        //   },
        //   symbol: 'circle',
        //   symbolSize: 5,
        //   areaStyle: {
        //     color: {
        //       type: 'linear',
        //       x: 0,
        //       y: 0,
        //       x2: 0,
        //       y2: 1,
        //       colorStops: [
        //         {
        //           offset: 0,
        //           color: this.bgColorState
        //               ? 'RGBA(21,254,254, 0.5)'
        //               : 'transparent' // 0% 处的颜色
        //         },
        //         {
        //           offset: 1,
        //           color: 'RGBA(1, 208, 254, 0)' // 100% 处的颜色
        //         }
        //       ],
        //       global: false
        //     },
        //     shadowColor: 'rgba(0,85,250,0)',
        //     shadowBlur: 20
        //   },
        //   itemStyle: {
        //     color: '#15FEFE', //改变折线点的颜色
        //     borderColor: '#fff'
        //   }
        // }
      ]
    } as EChartOption<EChartOption>)
  }
}
</script>



