<style lang="less" scoped>
.header-main {
  width: 100%;
  height: 0.94rem;
  background: rgb(3, 18, 51);
  position: relative;
  z-index: 10;
  .head-time {
    position: absolute;
    width: 4rem;
    height: 0.3rem;
    z-index: 99;
    top: 0.05rem;
    left: 0rem;
    font-size: 0.26rem;
    /* text-align: center; */
    text-shadow:
      0 0 0.1rem #2e73d6,
      0 0 0.1rem #2e73d6;
    padding-left: 0.5rem;
    font-family: CAI978;
    display: flex;
    align-items: center;
  }
  .header-weather {
    position: absolute;
    width: 4.2rem;
    height: 0.3rem;
    z-index: 99;
    top: 0.05rem;
    right: 1.2rem;
    display: flex;
    justify-content: space-between;
    > div {
      display: flex;
      align-items: center;
      font-size: 0.2rem;
      img {
        margin-right: 0.04rem;
      }
      span {
        text-shadow:
          0 0 0.1rem #2e73d6,
          0 0 0.1rem #2e73d6;
        font-size: 0.22rem;
        font-family: 'CAI978';
      }
    }
  }
  .head-select {
    position: absolute;
    width: 3.5rem;
    height: 0.46rem;
    padding: 0.06rem 1rem;
  }
  .bg-blue {
    background-image: url('../../assets/headSelectBg-blue.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    padding-left: 0.1rem;
  }
  .bg-yellow {
    background-image: url('../../assets/headSelectBg-yellow.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }
  .header-bg {
    display: block;
    width: 100%;
  }
  .custom-select-current {
    position: absolute;
    top: 0.2rem;
    left: 1rem;
    height: 0.4rem;
    width: 3.78rem;
    line-height: 0.4rem;
    // border: 0.01rem solid #63acf1;
    cursor: pointer;
    border-radius: 0.08rem;
    font-size: 0.25rem;
    text-align: center;
    background-color: transparent;
  }
  .custom-select-placeholder {
    position: absolute;
    // top: 0.3rem;
    right: 1rem;
    height: 0.4rem;
    width: 3.78rem;
    line-height: 0.4rem;
    // border: 0.01rem solid #63acf1;
    cursor: pointer;
    border-radius: 0.08rem;
    font-size: 0.25rem;
    text-align: center;
    transform-origin: 50% 50% -1rem;
    backface-visibility: hidden;
    background-color: transparent;
    transform-style: preserve-3d;
    transition:
      transform 0.3s ease-in,
      opacity 0.3s ease-out;
  }
  .custom-select-options {
    position: absolute;
    // top: 0.3rem;
    right: 0.8rem;
    // border: 0.01rem solid #63acf1;
    z-index: 999;
    cursor: pointer;
    transform-origin: 50% 50% -1rem;
    overflow: visible;
    visibility: visible;
    // transform-style: preserve-3d;
    transition:
      transform 0.3s,
      opacity 0.3s;
    > ul {
      padding: 0;
      margin: 0;
      list-style: none;
      > li {
        height: 0.4rem;
        width: 2.5rem;
        line-height: 0.4rem;
        background-color: #125b9e;
        // border: 0.01rem solid #63acf1;
        font-size: 0.25rem;
        text-align: center;
        transform-origin: 50% 50% -1rem;
        backface-visibility: hidden;
        transform-style: preserve-3d;
        // border-radius: 0.08rem;
        transition:
          transform 0.3s,
          opacity 0.3s,
          backgroundColor 0.3s;
        border-bottom: 0.01rem solid black;
        &:last-child {
          border-bottom: none;
        }
        /*margin-bottom: 0.01rem;*/
        &:hover {
          background-color: #125b9e !important;
        }
      }
    }
  }
  .placeholder-hide {
    opacity: 0;
    transform: rotate3d(0, 1, 0, -90deg);
  }
  .option-hide {
    opacity: 0.85;
    transform: rotate3d(0, 1, 0, 90deg);
    overflow: visible;
    visibility: visible;
    pointer-events: none;
    transform-style: preserve-3d;
  }
  .option-show {
    opacity: 1;
    transform: rotate3d(0, 1, 0, 0deg);
    pointer-events: auto;
    z-index: 99999;
  }
}
.text-active:hover {
  color: #ffc000;
}
</style>
<style lang="less">
.header-main {
  .ant-select {
    width: 2rem;
    color: white;
    // margin: 0.07rem 1rem;
  }
  .ant-select-selection {
    background-color: transparent;
    border: none;
    .ant-select-selection-selected-value {
      width: 100%;
      text-align: center;
      font-size: 0.3rem;
    }
  }
  .ant-select-arrow {
    color: white;
    font-size: 18px;
    top: 42%;
  }
  .ant-select-focused .ant-select-selection,
  .ant-select-selection:focus,
  .ant-select-selection:active {
    border-color: white;
    border-right-width: 0 !important;
    outline: 0;
    box-shadow: none;
  }
}
</style>
<template>
  <section class="header-main">
    <!-- header bg -->
    <!-- v-if="inSpecificPage" -->
    <!-- <img class="header-bg" src="@/assets/<EMAIL>" /> -->
    <img class="header-bg" src="@/assets/header_new.png" />
    <!-- <div class="head-time" v-if="!isCommandMode"> -->
    <div class="head-time">
      <div>
        {{ nowTime }}
      </div>
      <div style="width: 0.02rem; height: 0.28rem; background: rgba(36, 115, 235, 1); margin: 0 0.16rem"></div>
      <div style="font-size: 0.2rem">
        {{ getWeekDate() }}
      </div>
    </div>
    <div class="header-weather">
      <div>
        <img :src="weather.weatherImg" alt="" style="width: 0.27rem; height: 0.27rem" />
      </div>
      <div>
        <img src="@/assets/<EMAIL>" alt="" style="width: 0.1rem; height: 0.2rem" />
        <div>
          <span>{{ weather.minOrMax }}</span
          >℃
        </div>
      </div>
      <div>
        <img src="@/assets/<EMAIL>" alt="" style="width: 0.12rem; height: 0.16rem" />
        <div>
          <span>{{ weather.relativeHumidity }}</span
          >%
        </div>
      </div>
      <div>
        <img src="@/assets/<EMAIL>" alt="" style="width: 0.16rem; height: 0.16rem" />
        <div>
          <span>{{ weather.atmosphericPressure }}</span
          >mb
        </div>
      </div>
      <div style="color: #39d6fe; cursor: pointer; font-size: 16px" @click="logout">退出登录</div>
    </div>
    <!-- <img v-else class="header-bg" src="@/assets/<EMAIL>" /> -->
    <!-- 左侧当前页显示 -->
    <!-- :class="inSpecificPage ? '' : 'bg-yellow'" -->
    <div
      class="custom-select-current bg-yellow"
      :style="{
        top: inSpecificPage ? '0.5rem' : '0.5rem',
      }"
      @click="toRoute"
    >
      <div v-show="!isHomeMap">
        {{ isRadar ? '大气环境质量' : currentValue.name }}
      </div>
      <!-- :class="{ 'text-active': !isCommandMode }" -->
      <div v-show="isHomeMap" @click="toggleMode(false)" class="text-active">巡阅模式</div>
    </div>
    <!-- Select 选择按钮 -->
    <div
      v-show="!isHomeMap"
      ref="placeholderEle"
      class="custom-select-placeholder bg-blue"
      :class="isOption ? 'placeholder-hide' : ''"
      :style="{
        top: inSpecificPage ? '0.5rem' : '0.5rem',
      }"
      @click="isOption = true"
    >
      {{ `窗口切换` }}
      <!-- <svgicon
        name="right"
        color="#fff"
        width="0.2rem"
        height="0.2rem"
      ></svgicon>-->
      <!-- <a-icon type="caret-down" color="#fff" width="0.2rem" height="0.2rem" /> -->
    </div>
    <!-- :class="{ 'text-active': isCommandMode }" -->
    <div v-show="isHomeMap" class="custom-select-placeholder bg-blue text-active" style="top: 0.5rem" @click="toggleMode(true)">指挥模式</div>
    <!-- Select选项 -->
    <div
      ref="optionEle"
      class="custom-select-options"
      :class="isOption ? 'option-show' : 'option-hide'"
      :style="{
        top: inSpecificPage ? '0.4rem' : '0.35rem',
      }"
    >
      <ul>
        <li
          v-for="(item, index) in PagesOptions"
          :key="index"
          :class="isOption ? 'option-show' : 'option-hide'"
          :style="{
            transitionDelay: `${(index + 1) * 0.05}s`,
            backgroundColor: currentValue.name === item.name ? '#0061c6' : '#012474',
            cursor: item.value === 'disable' ? 'not-allowed' : 'pointer',
          }"
          @click="confirmSelect(item)"
        >
          {{ item.name }}
          <svgicon v-if="currentValue.name === item.name" name="check" color="#e17337" width="0.2rem" height="0.2rem"></svgicon>
        </li>
      </ul>
    </div>
  </section>
</template>

<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator'
import { Select, Icon } from 'ant-design-vue'
import { getNowTime } from '@/utils/index'
import Cookies from 'js-cookie'
import { removeToken } from '@/utils/authority'
import { recentWeather } from '@/api/homeTable'
import gif01 from '@/assets/sunlight.gif'
import gif02 from '@/assets/cloudy.gif'
import gif03 from '@/assets/overcast.gif'
import gif04 from '@/assets/rain.gif'
import gif05 from '@/assets/xue.gif'
import gif06 from '@/assets/wu.png'
@Component({
  name: 'Header',
  components: {
    ASelect: Select,
    AIcon: Icon,
    ASelectOption: Select.Option,
  },
})
export default class extends Vue {
  readonly HomeOptions: Array<Record<string, string>> = []
  readonly PagesOptions: Array<Record<string, string>> = [
    {
      name: '指挥沙盘',
      value: '/homeMap',
    },
    {
      name: '大气环境质量',
      value: '/airQuality',
    },
    {
      name: '水环境质量',
      value: '/waterQuality',
    },
    {
      name: '声环境质量',
      value: '/SoundEnvironment',
    },
    {
      name: '污染源监管',
      value: '/otherPages',
    },
    {
      name: '部门联动',
      value: '/taskManagement',
    },
    {
      name: '演练复盘',
      value: '/rehearseAnalyse',
    },
    {
      name: '数据统计',
      value: '/homeTable',
    },
    // {
    //   name: "部门联动",
    //   value: "/vehicleManage"
    // },
    {
      name: '应急指挥',
      value: '/emergencyCommand',
    },
    // {
    //   name: '环境健康',
    //   value: '/env-health',
    // },
    // {
    //   name: "设备工况",
    //   value: "/equipmentCondition"
    // }
  ]
  private isHomeMap = false
  private isOption = false
  private isCommandMode = false
  private currentValue: Record<string, string> = {
    name: '环境健康',
    value: '/homeMap',
  }
  @Watch('$route', { immediate: true, deep: true })
  private onRouteChange(newValue: any, oldValue: any): void {
    this.isRadar = localStorage.getItem('isRadar') || false
    if (newValue) {
      if (newValue.name == 'homeMap') {
        this.isHomeMap = true
      } else {
        this.isHomeMap = false
      }
      for (const route of this.PagesOptions) {
        if (newValue.path === route.value) {
          this.currentValue = route
        }
      }
      // if (localStorage.getItem('isRadar')){
      //   this.currentValue.name = '大气环境质量'
      //   this.currentValue.value = '/airQuality'
      // }
    }
  }
  get inSpecificPage(): boolean {
    if (this.currentValue.value === '/homeTable' || this.currentValue.value === '/homeMap') {
      return false
    }
    return true
  }
  private nowTime = ''
  private timer: any
  //气象监测
  private weather = {}

  isRadar: any = false

  created() {
    const cacheRoute = JSON.parse(localStorage.getItem('currentRoute') as string)
    if (cacheRoute && cacheRoute.value) {
      for (const route of this.PagesOptions) {
        if (cacheRoute.value === route.value) {
          this.currentValue = route
        }
      }
      // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
      // @ts-ignore
      this.$router.push(cacheRoute.value)
    }
  }

  mounted() {
    // // sessionStorage不匹配跳转到登录页
    // if (sessionStorage.getItem("auth-token") !== "loginToken") {
    //   this.$router.push("./login");
    //   sessionStorage.clear();
    //   Cookies.remove("auth-token");
    // }

    //@ts-ignore
    this.$bus.on('showHeaderTime', (arg: any) => {
      this.isCommandMode = false
    })
    this.timer = setInterval(() => {
      this.nowTime = getNowTime()
    }, 1000)

    document.addEventListener('click', this.handleClickOutHide, false)
    this.fetchRecentWeather()
  }
  beforeDestroy() {
    document.removeEventListener('click', this.handleClickOutHide, false)
    clearInterval(this.timer)
    // @ts-ignore
    this.$bus.off('showHeaderTime')
  }
  // 退出登录
  private logout() {
    localStorage.removeItem('currentRoute')
    removeToken()
    this.$router.replace('/login')
  }
  //气象监测
  private fetchRecentWeather() {
    recentWeather().then((res) => {
      const data = res.data.data
      if (data.weather.indexOf('晴') != -1) {
        data.weatherImg = gif01
      } else if (data.weather.indexOf('云') != -1) {
        data.weatherImg = gif02
      } else if (data.weather.indexOf('阴') != -1) {
        data.weatherImg = gif03
      } else if (data.weather.indexOf('雨') != -1) {
        data.weatherImg = gif04
      } else if (data.weather.indexOf('雪') != -1) {
        data.weatherImg = gif05
      } else if (data.weather.indexOf('雾') != -1) {
        data.weatherImg = gif06
      }
      this.weather = data
    })
  }
  private toRoute() {
    // console.log(this.currentValue, 99999999)
    if (this.currentValue.name === '大气环境质量' || this.isRadar) {
      this.$router.push('/airQuality')
    } else if (this.currentValue.name === '水环境质量') {
      this.$router.push('/waterQuality')
    } else if (this.currentValue.name === '部门联动') {
      this.$router.push('/taskManagement')
    } else if (this.currentValue.name === '污染源监管') {
      this.$router.push('/otherPages')
    } else {
      location.reload()
    }
  }
  private getWeekDate() {
    const now = new Date()
    const day = now.getDay()
    const weeks = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
    const week = weeks[day]
    return week
  }
  // 指挥沙盘页面模式切换
  private toggleMode(isCommandMode: boolean): void {
    /* eslint-disable @typescript-eslint/ban-ts-ignore */
    //@ts-ignore
    this.$bus.emit('toggleMode', isCommandMode)
    this.isCommandMode = isCommandMode
  }
  // 点击外部隐藏
  private handleClickOutHide(e: Event): void {
    if (e.target === this.$refs.placeholderEle) {
      return
    }
    if ((e.target as any).parentNode === this.$refs.optionEle) {
      return
    } else {
      this.isOption = false
    }
  }

  // Select选择修改
  private confirmSelect(item: Record<string, string>): void {
    this.isOption = false
    if (item.value === 'disable') {
      return
    }
    this.currentValue = item
    // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
    // @ts-ignore
    localStorage.setItem('currentRoute', JSON.stringify(this.currentValue))
    // window.location.reload()
    if (item.value == '/emergencyCommand') {
      window.location.reload()
      return
    }
    this.$router.push(item.value)
  }
}
</script>
