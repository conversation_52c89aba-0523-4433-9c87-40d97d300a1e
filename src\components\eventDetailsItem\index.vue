<template>
  <div class="event-details-item hvr-grow">
    <div class="left-tag" :class="eventC">
      <div class="left-tag-text">{{ eventT }}</div>
    </div>
    <div class="content">{{ content }}</div>
    <div class="right-status" :class="statusC">
      {{ statusArr[status].text }}
    </div>
  </div>
</template>
<script>
export default {
  name: '',
  props: {
    eventType: {
      type: Number,
      default: 0
    },
    content: {
      type: String,
      default: ''
    },
    status: {
      type: Number,
      default: 0
    },
  },
  data() {
    return {
      eventLevel: [
        { text: '一级响应', color: '' },
        { text: '二级响应', color: 'leftTagBg2' },
        { text: '三级响应', color: 'leftTagBg3' },
        { text: '四级响应', color: 'leftTagBg4' },
      ],
      statusArr: [
        { text: '进行中', color: '' },
        { text: '已完成', color: 'rightStaColor2' },
        { text: '已关闭', color: 'rightStaColor3' },
      ],
    }
  },
  computed: {
    eventT() {
      let text = ''
      try {
        text = this.eventLevel[this.eventType - 1].text
      } catch (error) {}
      return text
    },
    eventC() {
      let bgcolor = ''
      try {
        bgcolor = this.eventLevel[this.eventType - 1].color
      } catch (error) {}
      return bgcolor
    },
    statusC() {
      const color = this.statusArr[this.status].color
      return color
    },
  },
}
</script>

<style lang="less" scoped>
.event-details-item {
  display: flex;
  padding: 15px 20px;
  background-color: rgba(4, 20, 51, 0.3);
  border-bottom: 1px solid #164789;
  font-size: 16px;
  cursor: pointer;
  // margin: 15px 0;
  .left-tag {
    width: 75px;
    height: 25px;
    background-image: linear-gradient(to right top, #dd2c2c, #471010);
    text-align: center;
    color: #000;
    line-height: 25px;
    transform: skewX(-25deg);
    margin-right: 20px;
    .left-tag-text {
      transform: skewX(25deg);
      font-size: 14px;
      font-weight: bold;
    }
  }
  .leftTagBg2 {
    background-image: linear-gradient(to right top, #de5c28, #75392c);
  }
  .leftTagBg3 {
    background-image: linear-gradient(to right top, #d9a607, #705e1b);
  }
  .leftTagBg4 {
    background-image: linear-gradient(to right top, #0ba9b8, #0a6f79);
  }
  .content {
    margin-right: 30px;
    flex: 1;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    color: #fff;
  }
  .right-status {
    font-size: 14px;
    color: #02e6cf;
  }
  .rightStaColor2 {
    color: #d9a607;
  }
  .rightStaColor3 {
    color: #fff;
  }
}
</style>
