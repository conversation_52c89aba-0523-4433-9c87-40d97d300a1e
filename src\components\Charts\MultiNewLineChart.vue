<style lang="less" scoped>
.no-data {
  font-size: 0.2rem;
  position: relative;
  .no-datas {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}
</style>
<template>
  <div
    v-if="airDataProp.speed.length > 0 || airDataProp.TVOC.length > 0"
    :id="id"
    :style="{ height: height, width: width }"
  />
  <div v-else class="no-data" :style="{ height: height, width: width }">
    <div class="no-datas">数据未更新</div>
  </div>
</template>

<script lang="ts">
import * as echarts from 'echarts'
import { Component, Prop, Watch } from "vue-property-decorator";
import { mixins } from "vue-class-component";
import ResizeMixin from "./mixins/resize";
enum Type {
  TREND = 1,
  CONTRAST = 2
}
interface AirDataProp {
  TVOC: string | number[];
  speed: string | number[];
  date: []
  unit: string;
  type: number
}
@Component({
  name: "MultiLineChart"
})
export default class extends mixins(ResizeMixin) {
  @Prop({ default: "chart" }) private className!: string;
  @Prop({ default: "chart" }) private id!: string;
  @Prop({ default: "200px" }) private width!: string;
  @Prop({ default: "200px" }) private height!: string;
  @Prop({ default: 1 }) private type!: number;
  @Prop({ required: true }) private airDataProp!: AirDataProp;
  @Watch("airDataProp", { immediate: true, deep: true })
  public onAirDataProp(newValue: string, oldValue: string) {
    if (this.chart) {
      this.chart.clear();
    }
    this.$nextTick(() => {
      this.initChart();
    });
  }
  private HOURLIST: Array<string> = [
    "1月",
    "2月",
    "3月",
    "4月",
    "5月",
    "6月",
    "7月",
    "8月",
    "9月",
    "10月",
    "11月",
    "12月"
  ];

  mounted() {
    if (this.chart) {
      this.chart.clear();
    }
    this.$nextTick(() => {
      this.initChart();
    });
  }

  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.clear();
    this.chart.dispose();
    this.chart = null;
  }

  private initChart() {

    if(this.chart === null || this.chart === undefined) {
      this.chart = echarts.init(
        document.getElementById(this.id) as HTMLDivElement
      );
    }
    const colors: Array<string> = [
      "#1D9DFF",
      "#FF9F7F",
      "#FB7293",
      "#E7BCF3",
      "#8378EA",
      "#32C5E9",
      "#9FE6B8",
      "#FFDB5C"
    ];

    const TrendSeriesData = [
      {
        name: this.airDataProp.type !== 4 ? "TVOC" : '卸油区油气浓度',
        data: this.airDataProp.TVOC,
        type: "line",
        lineStyle: {
          color: "#EA7638" //改变折线颜色
        },
        symbolSize: 8,
        itemStyle: {
          color: "#EA7638", //改变折线点的颜色
          borderColor: "#EA7638",
          borderWidth: 2
        }
      },
      {
        name: this.airDataProp.type !== 4 ? "排放速率" : '液阻压力',
        data: this.airDataProp.speed,
        type: "line",
        lineStyle: {
          color: "#0492FF" //改变折线颜色
        },
        symbolSize: 8,
        itemStyle: {
          color: "#0492FF", //改变折线点的颜色
          borderColor: "#0492FF",
          borderWidth: 2
        }
      }
    ];
    const TrendSeriesData1 = [
      {
        name: '烟雾浓度',
        data: this.airDataProp.TVOC,
        type: "line",
        lineStyle: {
          color: "#EA7638" //改变折线颜色
        },
        symbolSize: 8,
        itemStyle: {
          color: "#EA7638", //改变折线点的颜色
          borderColor: "#EA7638",
          borderWidth: 2
        }
      }
    ];
    const type =  this.airDataProp.type
    this.chart.setOption({
        backgroundColor: "#022360",
        grid: {
          left: 100,
          bottom: 50,
          right: 20,
          top: 80
        },
        legend: {
          top: 30,
          right: 20,
          textStyle: {
            color: "#fff",
            fontWeight: "400",
            fontFamily: "Source Han Sans CN",
            fontSize: 14
          },
          itemWidth: 40
        },
        xAxis: {
          type: "category",
          data: this.airDataProp.date,
          offset: 10,
          axisLabel: {
            textStyle: {
              fontSize: 20,
              color: "#fff"
            }
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: "#fff"
            }
          },
          axisTick: {
            show: false
          }
        },
        yAxis: {
          type: "value",
          name: this.airDataProp.unit,
          nameTextStyle: {
            padding: [0,0,20,200],
            color: "#fff",
            fontSize: 16
          },
          axisLabel: {
            textStyle: {
              fontSize: 20,
              color: "white"
            }
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          splitLine: {
            lineStyle: {
              type: "dashed"
            }
          }
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross",
            label: {
              backgroundColor: "#6a7985"
            }
          },
          formatter: function(params: Array<any>): string {
            let ret=`${params[0].name}`;
            params.forEach((series: any,index: any) => {
              if(new RegExp(`${series.seriesName}`,"g").test(ret))
                return;
              if(series.value!=="-") {
                ret+=`<br/>${series.seriesName}: ${series.value}${index===0? (type===4? '%/ppm':'mg/m³'):(type===4? 'pa':'kg/h')}`;
              }
            });
            return ret;
          }
        },
        series: type===3? TrendSeriesData1:TrendSeriesData
      } as unknown as EChartOption<EChartOption.SeriesLine>);
  }
}
</script>
