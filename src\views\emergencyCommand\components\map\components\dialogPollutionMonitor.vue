<template>
  <div class="dialogPollutionMonitor-container flex justify-around">
    <div class="item" v-for="(item, i) in list" :key="i">
      <dialogPollutionMonitorItem :data="item"></dialogPollutionMonitorItem>
    </div>
  </div>
</template>

<script>
import dialogPollutionMonitorItem from './dialogPollutionMonitorItem.vue'
export default {
  name: 'dialogPollutionMonitor',
  components: {
    dialogPollutionMonitorItem,
  },
  props: {
    pollutionMonitorData: {
      type: Array,
      default: () => [0, 0, 0], // 废水 - 废气 - 废料
    },
  },
  computed: {
    list() {
      const { pollutionMonitorData } = this
      return [
        {
          type: 'fs', // fs - 废水 fl - 废料 fq - 废气
          value: pollutionMonitorData[0],
          name: '废水排放/kg',
        },
        {
          type: 'fq', // fs - 废水 fl - 废料 fq - 废气
          value: pollutionMonitorData[1],
          name: '废气排放/m³',
        },
        {
          type: 'fl', // fs - 废水 fl - 废料 fq - 废气
          value: pollutionMonitorData[2],
          name: '废料排放/吨',
        },
      ]
    },
  },
}
</script>

<style lang="less" scoped>
.dialogPollutionMonitor-container {
  width: 100%;

  .item {
    width: 100px;
  }
}
.flex{
  display: flex;
}
.justify-around{
  justify-content: space-around;
}
</style>
