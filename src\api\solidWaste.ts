import request from "@/utils/request";
import { AxiosPromise } from "axios";

/**
 * @method functionName
 * @param {type} data 说明
 * @description 获取固废站点数量
 */
export function getSolidWasteSite(params:any): AxiosPromise<any> {
  return request({
    url: `/water/waste-solid-device/stationStatusStatistics`,
    method: "get",
    params
  });
}
/**
 * @method functionName
 * @param {type} data 说明
 * @description 获取固废站点
 */
export function getSolidWasteMapList(params:any): AxiosPromise<any> {
  return request({
    url: `/water/waste-solid-device/listStation`,
    method: "get",
    params
  });
}
/**
 * @method functionName
 * @param {type} data 说明
 * @description 获取固废趋势线型图
 */
export function getSolidWasteListRecord(params:any): AxiosPromise<any> {
  return request({
    url: `/water/waste-solid-record-stock/bigDataListRecord`,
    method: "get",
    params
  });
}
/**
 * @method functionName
 * @param {type} data 说明
 * @description 获取固废趋统计饼图
 */
export function getSolidWasteProportionTotal(params:any): AxiosPromise<any> {
  return request({
    url: `/water/waste-solid-stock/proportionTotal`,
    method: "get",
    params
  });
}

/**
 * @method functionName
 * @param {type} data 说明
 * @description 获取固废监控
 */
export function getSolidWastecamera(params:any): AxiosPromise<any> {
  return request({
    url: `/water/waste-solid-camera/list`,
    method: "get",
    params
  });
} 