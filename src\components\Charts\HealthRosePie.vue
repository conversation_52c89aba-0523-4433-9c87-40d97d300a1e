<template>
  <div
    :id="id"
    :class="className"
    :style="{ height: height, width: width }"
  ></div>
</template>

<script lang="ts">
import * as echarts from 'echarts'
import { Component, Prop, Watch } from "vue-property-decorator";
import { mixins } from "vue-class-component";
import ResizeMixin from "./mixins/resize";
interface TaskData {
  type: number;
  showNumber: number;
  name: string;
  number: number;
}
@Component({
  name: "Dashboard<PERSON>hart",
})
export default class extends mixins(ResizeMixin) {
  @Prop({ default: "chart" }) private className!: string;
  @Prop({ default: "chart" }) private id!: string;
  @Prop({ default: "条" }) private unit!: string;
  @Prop({ default: "200px" }) private width!: string;
  @Prop({ default: "200px" }) private height!: string;
  @Prop({ required: true }) private propData!: TaskData;

  @Watch("propData", { immediate: false, deep: true })
  public onMsgChanged(newValue: InData, oldValue: InData) {
    this.initChart();
  }

  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.clear();
    this.chart.dispose();
    this.chart = null;
  }

  private initChart() {
    const colorList = [
      "#C1E6EB",
      "#2CC3EC",
      "#6EBE44",
      "#EAE84D",
      "#F69331",
      "#FC602A",
    ];
    if (this.chart === null || this.chart === undefined) {
      this.chart = echarts.init(
        document.getElementById(this.id) as HTMLDivElement
      );
    }
    const option = {
      color: colorList,
      legend: {
        show: true,
        bottom: 0,
        left: "center",
        itemWidth: 12,
        itemHeight: 6,
        itemGap: 30,
        formatter: (name) => {
          return `${name} ${
            this.unit === "个"
              ? ""
              : this.propData?.find((item) => item.name === name)?.value +
                this.unit
          } ${this.propData?.find((item) => item.name === name)?.rate}%`;
        },
        textStyle: {
          color: "#789590",
          fontSize: 12,
        },
      },
      series: [
        {
          type: "pie",
          radius: [50, 100],
          roseType: "area",
          center: ["50%", "35%"],
          left: "center",
          label: {
            formatter: `{name|{b}}\n{c}${this.unit} {rate|{d}%}`,
            minMargin: 20,
            lineHeight: 20,
            rich: {
              name: {
                color: "#BFFFF4",
                fontSize: 13,
              },
              rate: {
                fontSize: 13,
              },
            },
          },
          data: this.propData.map((item, index) => ({
            name: item.name,
            value: item.value,
            itemStyle:
              item.value == 0
                ? { orderWidth: 1, borderColor: colorList[index] }
                : {},
          })),
        },
      ],
    };
    this.chart.setOption(option as EChartOption);
  }
}
</script>
