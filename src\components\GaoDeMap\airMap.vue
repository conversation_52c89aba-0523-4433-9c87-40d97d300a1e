<template>
  <div class="container" ref="container" id="container"></div>
</template>

<script lang="ts">
//@ts-ignore
// import AMap from "AMap";
import AMapLoader from "@amap/amap-jsapi-loader";
//@ts-ignore
import jinniuStreet from "@/assets/map-geojson/area_five";
import { Vue, Component, Prop, Watch } from "vue-property-decorator";
import { Divider, Icon, message } from "ant-design-vue";
import qkzHui from "@/assets/qkz_hui.png";
import qkz1 from "@/assets/qkz1.png";
import qkz2 from "@/assets/qkz2.png";
import qkz3 from "@/assets/qkz3.png";
import qkz4 from "@/assets/qkz4.png";
import qkz5 from "@/assets/qkz5.png";
import qkz6 from "@/assets/qkz6.png";
import wkzHui from "@/assets/wkz_hui.png";
import wkz1 from "@/assets/wkz1.png";
import wkz2 from "@/assets/wkz2.png";
import wkz3 from "@/assets/wkz3.png";
import wkz4 from "@/assets/wkz4.png";
import wkz5 from "@/assets/wkz5.png";
import wkz6 from "@/assets/wkz6.png";
import skzHui from "@/assets/skz_hui.png";
import skz1 from "@/assets/skz1.png";
import skz2 from "@/assets/skz2.png";
import skz3 from "@/assets/skz3.png";
import skz4 from "@/assets/skz4.png";
import skz5 from "@/assets/skz5.png";
import skz6 from "@/assets/skz6.png";
import gkzHui from "@/assets/<EMAIL>";
import gkz1 from "@/assets/<EMAIL>";
import gkz2 from "@/assets/<EMAIL>";
import gkz3 from "@/assets/<EMAIL>";
import gkz4 from "@/assets/<EMAIL>";
import gkz5 from "@/assets/<EMAIL>";
import gkz6 from "@/assets/<EMAIL>";
import s1 from "@/assets/s1.gif";
import s2 from "@/assets/s2.gif";
import s3 from "@/assets/s3.gif";
import s4 from "@/assets/s4.gif";
import s5 from "@/assets/s5.gif";
import s6 from "@/assets/s6.gif";
import areaImage from "../../assets/<EMAIL>";
import areaImage1 from "../../assets/<EMAIL>";
import zhzfj from "@/assets/<EMAIL>";
import jainkon from "@/assets/jainkon.png";
import areaalabelImage from "../../assets/<EMAIL>";
import { airStationDetails } from "@/api/air";
import { webglcontextlostHandle } from "@/utils/index";
import mc from "@/assets/<EMAIL>";
let AMap: any;
interface HeatMapData {
  lng: number;
  lat: number;
  count: number;
}
interface ColorList {
  [key: number]: string;
}
interface MapCenter {
  lng: number;
  lat: number;
}

// AQI
const AQIAirColors: Array<any> = [
  {
    color: "rgb(0,255,0)",
    text: "优",
    type: 1,
    min: 0,
    max: 50,
  },
  {
    color: "rgb(255,255,0)",
    text: "良",
    type: 2,
    min: 51,
    max: 100,
  },
  {
    color: "rgb(255,126,0)",
    text: "轻度污染",
    type: 3,
    min: 101,
    max: 150,
  },
  {
    color: "rgb(255,0,0)",
    text: "中度污染",
    type: 4,
    min: 151,
    max: 200,
  },
  {
    color: "rgb(153,0,76)",
    text: "重度污染",
    type: 5,
    min: 201,
    max: 300,
  },
  {
    color: "rgb(126,0,35)",
    text: "严重污染",
    type: 6,
    min: 300,
    max: 999,
  },
];
// SO2
const SO2AirColors: Array<any> = [
  {
    color: "rgb(0,255,0)",
    type: 1,
    min: 0,
    max: 150,
  },
  {
    color: "rgb(255,255,0)",
    type: 2,
    min: 151,
    max: 500,
  },
  {
    color: "rgb(255,126,0)",
    type: 3,
    min: 501,
    max: 650,
  },
  {
    color: "rgb(255,0,0)",
    type: 4,
    min: 651,
    max: 800,
  },
  {
    color: "rgb(153,0,76)",
    type: 5,
    min: 801,
    max: 1600,
  },
  {
    color: "rgb(126,0,35)",
    type: 6,
    min: 1601,
    max: 2620,
  },
];
// NO2
const NO2AirColors: Array<any> = [
  {
    color: "rgb(0,255,0)",
    type: 1,
    min: 0,
    max: 100,
  },
  {
    color: "rgb(255,255,0)",
    type: 2,
    min: 101,
    max: 200,
  },
  {
    color: "rgb(255,126,0)",
    type: 3,
    min: 201,
    max: 700,
  },
  {
    color: "rgb(255,0,0)",
    type: 4,
    min: 701,
    max: 1200,
  },
  {
    color: "rgb(153,0,76)",
    type: 5,
    min: 1201,
    max: 2340,
  },
  {
    color: "rgb(126,0,35)",
    type: 6,
    min: 2341,
    max: 3090,
  },
];
// PM10
const PM10AirColors: Array<any> = [
  {
    color: "rgb(0,255,0)",
    type: 1,
    min: 0,
    max: 50,
  },
  {
    color: "rgb(255,255,0)",
    type: 2,
    min: 51,
    max: 150,
  },
  {
    color: "rgb(255,126,0)",
    type: 3,
    min: 151,
    max: 250,
  },
  {
    color: "rgb(255,0,0)",
    type: 4,
    min: 251,
    max: 350,
  },
  {
    color: "rgb(153,0,76)",
    type: 5,
    min: 351,
    max: 420,
  },
  {
    color: "rgb(126,0,35)",
    type: 6,
    min: 421,
    max: 999,
  },
];
// PM2.5
const PM25AirColors: Array<any> = [
  {
    color: "rgb(0,255,0)",
    type: 1,
    min: 0,
    max: 35,
  },
  {
    color: "rgb(255,255,0)",
    type: 2,
    min: 36,
    max: 75,
  },
  {
    color: "rgb(255,126,0)",
    type: 3,
    min: 76,
    max: 115,
  },
  {
    color: "rgb(255,0,0)",
    type: 4,
    min: 116,
    max: 150,
  },
  {
    color: "rgb(153,0,76)",
    type: 5,
    min: 151,
    max: 250,
  },
  {
    color: "rgb(126,0,35)",
    type: 6,
    min: 251,
    max: 999,
  },
];
// CO
const COAirColors: Array<any> = [
  {
    color: "rgb(0,255,0)",
    type: 1,
    min: 0,
    max: 5,
  },
  {
    color: "rgb(255,255,0)",
    type: 2,
    min: 6,
    max: 10,
  },
  {
    color: "rgb(255,126,0)",
    type: 3,
    min: 11,
    max: 35,
  },
  {
    color: "rgb(255,0,0)",
    type: 4,
    min: 36,
    max: 60,
  },
  {
    color: "rgb(153,0,76)",
    type: 5,
    min: 61,
    max: 90,
  },
  {
    color: "rgb(126,0,35)",
    type: 6,
    min: 91,
    max: 999,
  },
];
// O3
const O3AirColors: Array<any> = [
  {
    color: "rgb(0,255,0, 0.45)",
    type: 1,
    min: 0,
    max: 160,
  },
  {
    color: "rgb(255,255,0, 0.45)",
    type: 2,
    min: 161,
    max: 200,
  },
  {
    color: "rgb(255,126,0, 0.45)",
    type: 3,
    min: 201,
    max: 300,
  },
  {
    color: "rgb(255,0,0, 0.45)",
    type: 4,
    min: 301,
    max: 400,
  },
  {
    color: "rgb(153,0,76, 0.45)",
    type: 5,
    min: 401,
    max: 800,
  },
  {
    color: "rgb(126,0,35, 0.45)",
    type: 6,
    min: 801,
    max: 9999,
  },
];
// AQI
const AQIAirColors1: Array<any> = [
  {
    color: "rgba(0,255,0, 0.45)",
    text: "优",
    type: 1,
    min: 0,
    max: 50,
  },
  {
    color: "rgba(255,255,0, 0.45)",
    text: "良",
    type: 2,
    min: 51,
    max: 100,
  },
  {
    color: "rgba(255,126,0, 0.45)",
    text: "轻度污染",
    type: 3,
    min: 101,
    max: 150,
  },
  {
    color: "rgba(255,0,0, 0.45)",
    text: "中度污染",
    type: 4,
    min: 151,
    max: 200,
  },
  {
    color: "rgba(153,0,76, 0.45)",
    text: "重度污染",
    type: 5,
    min: 201,
    max: 300,
  },
  {
    color: "rgba(126,0,35, 0.45)",
    text: "严重污染",
    type: 6,
    min: 300,
    max: 999,
  },
];
// SO2
const SO2AirColors1: Array<any> = [
  {
    color: "rgba(0,255,0, 0.45)",
    type: 1,
    min: 0,
    max: 150,
  },
  {
    color: "rgba(255,255,0, 0.45)",
    type: 2,
    min: 151,
    max: 500,
  },
  {
    color: "rgba(255,126,0, 0.45)",
    type: 3,
    min: 501,
    max: 650,
  },
  {
    color: "rgba(255,0,0, 0.45)",
    type: 4,
    min: 651,
    max: 800,
  },
  {
    color: "rgba(153,0,76, 0.45)",
    type: 5,
    min: 801,
    max: 1600,
  },
  {
    color: "rgba(126,0,35, 0.45)",
    type: 6,
    min: 1601,
    max: 2620,
  },
];
// NO2
const NO2AirColors1: Array<any> = [
  {
    color: "rgba(0,255,0, 0.45)",
    type: 1,
    min: 0,
    max: 100,
  },
  {
    color: "rgba(255,255,0, 0.45)",
    type: 2,
    min: 101,
    max: 200,
  },
  {
    color: "rgba(255,126,0, 0.45)",
    type: 3,
    min: 201,
    max: 700,
  },
  {
    color: "rgba(255,0,0, 0.45)",
    type: 4,
    min: 701,
    max: 1200,
  },
  {
    color: "rgba(153,0,76, 0.45)",
    type: 5,
    min: 1201,
    max: 2340,
  },
  {
    color: "rgba(126,0,35, 0.45)",
    type: 6,
    min: 2341,
    max: 3090,
  },
];
// PM10
const PM10AirColors1: Array<any> = [
  {
    color: "rgba(0,255,0, 0.45)",
    type: 1,
    min: 0,
    max: 50,
  },
  {
    color: "rgba(255,255,0, 0.45)",
    type: 2,
    min: 51,
    max: 150,
  },
  {
    color: "rgba(255,126,0, 0.45)",
    type: 3,
    min: 151,
    max: 250,
  },
  {
    color: "rgba(255,0,0, 0.45)",
    type: 4,
    min: 251,
    max: 350,
  },
  {
    color: "rgba(153,0,76, 0.45)",
    type: 5,
    min: 351,
    max: 420,
  },
  {
    color: "rgba(126,0,35, 0.45)",
    type: 6,
    min: 421,
    max: 999,
  },
];
// PM2.5
const PM25AirColors1: Array<any> = [
  {
    color: "rgba(0,255,0, 0.45)",
    type: 1,
    min: 0,
    max: 35,
  },
  {
    color: "rgba(255,255,0, 0.45)",
    type: 2,
    min: 36,
    max: 75,
  },
  {
    color: "rgba(255,126,0, 0.45)",
    type: 3,
    min: 76,
    max: 115,
  },
  {
    color: "rgba(255,0,0, 0.45)",
    type: 4,
    min: 116,
    max: 150,
  },
  {
    color: "rgba(153,0,76, 0.45)",
    type: 5,
    min: 151,
    max: 250,
  },
  {
    color: "rgba(126,0,35, 0.45)",
    type: 6,
    min: 251,
    max: 999,
  },
];
// CO
const COAirColors1: Array<any> = [
  {
    color: "rgba(0,255,0, 0.45)",
    type: 1,
    min: 0,
    max: 5,
  },
  {
    color: "rgba(255,255,0, 0.45)",
    type: 2,
    min: 6,
    max: 10,
  },
  {
    color: "rgba(255,126,0, 0.45)",
    type: 3,
    min: 11,
    max: 35,
  },
  {
    color: "rgba(255,0,0, 0.45)",
    type: 4,
    min: 36,
    max: 60,
  },
  {
    color: "rgba(153,0,76, 0.45)",
    type: 5,
    min: 61,
    max: 90,
  },
  {
    color: "rgba(126,0,35, 0.45)",
    type: 6,
    min: 91,
    max: 999,
  },
];
// O3
const O3AirColors1: Array<any> = [
  {
    color: "rgba(0,255,0, 0.45)",
    type: 1,
    min: 0,
    max: 160,
  },
  {
    color: "rgba(255,255,0, 0.45)",
    type: 2,
    min: 161,
    max: 200,
  },
  {
    color: "rgba(255,126,0, 0.45)",
    type: 3,
    min: 201,
    max: 300,
  },
  {
    color: "rgba(255,0,0, 0.45)",
    type: 4,
    min: 301,
    max: 400,
  },
  {
    color: "rgba(153,0,76, 0.45)",
    type: 5,
    min: 401,
    max: 800,
  },
  {
    color: "rgba(126,0,35, 0.45)",
    type: 6,
    min: 801,
    max: 9999,
  },
];
// 地图上各种marker的icon数据
let MapIcon: Object = {};

// 地图上各种类型的站点图标
let MapSiteIcon: Object = {};

// 地图容器
let maps: any = null;

// 地图绘制元素
let canvas: any = null;

@Component({
  name: "AirMap",
  components: {
    AIcon: Icon,
  },
})
export default class extends Vue {
  // private maps: any = "";
  private heatMapDataList: any = [];
  private heathMapMax = 5;
  private textList: any = [];
  private areaList: any[] = [];
  private mapGeoJson: any = null;
  private radarMaker: any = null;
  private videoMarker: any = [];

  @Prop({
    required: false,
    type: Number,
    default: 13.8,
  })
  private mapZoom!: number;
  @Prop({ required: false })
  mapStyle!: string;
  @Prop({ required: false, default: () => {} }) viewCenter!: MapCenter;
  @Prop({ required: false, default: () => [] }) mapMarker!: any[];
  @Prop({ required: false, default: () => [] }) areaData!: any[];
  @Prop({ required: false, default: () => [] }) radarData!: any[];
  @Prop({ required: false, default: () => [] }) CameraData!: any[];
  @Prop({ required: false, default: "" }) mapColor!: string;
  @Prop({ required: false, default: "" }) wind!: string;
  @Prop({ required: false }) typeColor!: string;
  @Prop({ required: false, default: "" }) mapIndex!: string | number;
  @Prop({ required: false, default: "3D" }) viewMode!: string;
  @Prop({ required: false, default: () => [] }) areaCodeList!: string[];

  @Watch("areaCodeList", { immediate: true, deep: true })
  private onareaCodeListChange(newVal: string[]) {
    if (newVal.length > 0) {
      console.log("区域变化", newVal);
      this.handleAreaCodeListChange();
    }
  }

  @Watch("mapZoom", { immediate: true, deep: false })
  private onmapZoomChange(newValue: number, oldValue: number) {
    if (newValue && maps) {
      maps.setZoom(newValue);
    }
  }
  @Watch("viewCenter", { immediate: true, deep: true })
  private onviewCenterChange(newValue: MapCenter, oldValue: MapCenter) {
    if (newValue && maps) {
      const waterCenterPosition = new AMap.LngLat(newValue.lng, newValue.lat);
      maps.setCenter(waterCenterPosition);
    }
  }
  @Watch("areaData", { immediate: true, deep: true })
  private onareaData(newValue: any, oldValue: any) {
    if (newValue) {
      this.areaData = newValue;
      // setTimeout(() => {
      //   if (this.areaData.length) {
      //     this.toDownMap()
      //   }
      //   this.creatAreaMarker()
      // }, 2000)
    }
  }
  @Watch("radarData", { immediate: true, deep: true })
  private onradarData(newValue: any, oldValue: any) {
    if (newValue) {
      this.radarData = newValue;
      setTimeout(() => {
        this.creatLeiDaMarker();
      }, 2000);
    }
  }
  @Watch("CameraData", { immediate: true, deep: true })
  private onCameraData(newValue: any, oldValue: any) {
    if (newValue) {
      this.CameraData = newValue;
      setTimeout(() => {
        this.creatJianKongMarker();
      }, 2000);
    }
  }
  @Watch("mapMarker", { immediate: true, deep: true })
  private onMapMarker(newValue: any, oldValue: any) {
    if (newValue) {
      this.mapMarker = newValue;
      this.checkSiteMarker();
    }
  }
  @Watch("mapColor", { immediate: true, deep: false })
  private onMapColor(newValue: any, oldValue: any) {
    // this.creatGeojson();
  }
  @Watch("typeColor", { immediate: true, deep: false })
  private onTypeColor(newValue: any, oldValue: any) {
    this.typeColor = newValue;
  }

  created() {
    console.log("--created---air---707");
    AMapLoader["reset"]();
  }
  mounted() {
    // // load 加载
    AMapLoader.load({
      key: "777fec7ef3cc29281d60ae900fa33925", // 申请好的Web端开发者Key，首次调用 load 时必填
      version: "1.4.15", // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
      plugins: [
        "AMap.DistrictSearch",
        "AMap.Heatmap",
        "AMap.ControlBar",
        "AMap.Object3DLayer",
        "Map3D",
        "AMap.Geocoder",
        "AMap.CircleMarker",
        "AMap.MouseTool",
      ], // 需要使用的的插件列表，如比例尺'AMap.Scale'等
      AMapUI: {
        // 是否加载 AMapUI，缺省不加载
        version: "1.0", // AMapUI 版本
        // "plugins":['overlay/SimpleMarker'],       // 需要加载的 AMapUI ui插件
      },
      Loca: {
        // 是否加载 Loca， 缺省不加载
        version: "1.3.2", // Loca 版本
      },
    })
      .then((amps) => {
        AMap = amps;
        this.map();
        MapIcon = {
          // 设置空气Marker-Icon(1、区控站)
          markerIcon11: new AMap.Icon({
            size: new AMap.Size(45, 45),
            image: qkz1,
            imageSize: new AMap.Size(45, 45),
            imageOffset: new AMap.Pixel(0, 0),
          }),
          markerIcon12: new AMap.Icon({
            size: new AMap.Size(45, 45),
            image: qkz2,
            imageSize: new AMap.Size(45, 45),
            imageOffset: new AMap.Pixel(0, 0),
          }),
          markerIcon13: new AMap.Icon({
            size: new AMap.Size(45, 45),
            image: qkz3,
            imageSize: new AMap.Size(45, 45),
            imageOffset: new AMap.Pixel(0, 0),
          }),
          markerIcon14: new AMap.Icon({
            size: new AMap.Size(45, 45),
            image: qkz4,
            imageSize: new AMap.Size(45, 45),
            imageOffset: new AMap.Pixel(0, 0),
          }),
          markerIcon15: new AMap.Icon({
            size: new AMap.Size(45, 45),
            image: qkz5,
            imageSize: new AMap.Size(45, 45),
            imageOffset: new AMap.Pixel(0, 0),
          }),
          markerIcon16: new AMap.Icon({
            size: new AMap.Size(45, 45),
            image: qkz6,
            imageSize: new AMap.Size(45, 45),
            imageOffset: new AMap.Pixel(0, 0),
          }),

          // 设置空气Marker-Icon(2、微控站)
          markerIcon21: new AMap.Icon({
            size: new AMap.Size(54, 44),
            image: wkz1,
            imageSize: new AMap.Size(54, 44),
            imageOffset: new AMap.Pixel(0, 0),
          }),
          markerIcon22: new AMap.Icon({
            size: new AMap.Size(54, 44),
            image: wkz2,
            imageSize: new AMap.Size(54, 44),
            imageOffset: new AMap.Pixel(0, 0),
          }),
          markerIcon23: new AMap.Icon({
            size: new AMap.Size(54, 44),
            image: wkz3,
            imageSize: new AMap.Size(54, 44),
            imageOffset: new AMap.Pixel(0, 0),
          }),
          markerIcon24: new AMap.Icon({
            size: new AMap.Size(54, 44),
            image: wkz4,
            imageSize: new AMap.Size(54, 44),
            imageOffset: new AMap.Pixel(0, 0),
          }),
          markerIcon25: new AMap.Icon({
            size: new AMap.Size(54, 44),
            image: wkz5,
            imageSize: new AMap.Size(54, 44),
            imageOffset: new AMap.Pixel(0, 0),
          }),
          markerIcon26: new AMap.Icon({
            size: new AMap.Size(54, 44),
            image: wkz6,
            imageSize: new AMap.Size(54, 44),
            imageOffset: new AMap.Pixel(0, 0),
          }),

          // 设置空气Marker-Icon(3、市控站)
          markerIcon31: new AMap.Icon({
            size: new AMap.Size(51, 37),
            image: skz1,
            imageSize: new AMap.Size(51, 37),
            imageOffset: new AMap.Pixel(0, 0),
          }),
          markerIcon32: new AMap.Icon({
            size: new AMap.Size(51, 37),
            image: skz2,
            imageSize: new AMap.Size(51, 37),
            imageOffset: new AMap.Pixel(0, 0),
          }),
          markerIcon33: new AMap.Icon({
            size: new AMap.Size(51, 37),
            image: skz3,
            imageSize: new AMap.Size(51, 37),
            imageOffset: new AMap.Pixel(0, 0),
          }),
          markerIcon34: new AMap.Icon({
            size: new AMap.Size(51, 37),
            image: skz4,
            imageSize: new AMap.Size(51, 37),
            imageOffset: new AMap.Pixel(0, 0),
          }),
          markerIcon35: new AMap.Icon({
            size: new AMap.Size(51, 37),
            image: skz5,
            imageSize: new AMap.Size(51, 37),
            imageOffset: new AMap.Pixel(0, 0),
          }),
          markerIcon36: new AMap.Icon({
            size: new AMap.Size(51, 37),
            image: skz6,
            imageSize: new AMap.Size(51, 37),
            imageOffset: new AMap.Pixel(0, 0),
          }),

          // 设置空气Marker-Icon(1、区控站)离线
          markerIcon11Hui: new AMap.Icon({
            size: new AMap.Size(45, 45),
            image: qkzHui,
            imageSize: new AMap.Size(45, 45),
            imageOffset: new AMap.Pixel(0, 0),
          }),

          // 设置空气Marker-Icon(2、微控站)离线
          markerIcon21Hui: new AMap.Icon({
            size: new AMap.Size(54, 44),
            image: wkzHui,
            imageSize: new AMap.Size(54, 44),
            imageOffset: new AMap.Pixel(0, 0),
          }),

          // 设置空气Marker-Icon(3、市控站)离线
          markerIcon31Hui: new AMap.Icon({
            size: new AMap.Size(51, 37),
            image: skzHui,
            imageSize: new AMap.Size(51, 37),
            imageOffset: new AMap.Pixel(0, 0),
          }),
          // 设置空气Marker-Icon(3、国控站)离线
          markerIcon41Hui: new AMap.Icon({
            size: new AMap.Size(42, 35),
            image: gkzHui,
            imageSize: new AMap.Size(42, 35),
            imageOffset: new AMap.Pixel(0, 0),
          }),
          // 设置空气Marker-Icon(3、国控站)
          markerIcon41: new AMap.Icon({
            size: new AMap.Size(42, 35),
            image: gkz1,
            imageSize: new AMap.Size(42, 35),
            imageOffset: new AMap.Pixel(0, 0),
          }),
          markerIcon42: new AMap.Icon({
            size: new AMap.Size(42, 35),
            image: gkz2,
            imageSize: new AMap.Size(42, 35),
            imageOffset: new AMap.Pixel(0, 0),
          }),
          markerIcon43: new AMap.Icon({
            size: new AMap.Size(42, 35),
            image: gkz3,
            imageSize: new AMap.Size(42, 35),
            imageOffset: new AMap.Pixel(0, 0),
          }),
          markerIcon44: new AMap.Icon({
            size: new AMap.Size(42, 35),
            image: gkz4,
            imageSize: new AMap.Size(42, 35),
            imageOffset: new AMap.Pixel(0, 0),
          }),
          markerIcon45: new AMap.Icon({
            size: new AMap.Size(42, 35),
            image: gkz5,
            imageSize: new AMap.Size(42, 35),
            imageOffset: new AMap.Pixel(0, 0),
          }),
          markerIcon46: new AMap.Icon({
            size: new AMap.Size(42, 35),
            image: gkz6,
            imageSize: new AMap.Size(42, 35),
            imageOffset: new AMap.Pixel(0, 0),
          }),
        };
        MapSiteIcon = {
          // 区控站
          sq1: new AMap.Icon({
            // 图标尺寸
            size: new AMap.Size(150, 150),
            // 图标的取图地址
            image: s1,
            // 图标所用图片大小
            imageSize: new AMap.Size(150, 150),
            // 图标取图偏移量
            imageOffset: new AMap.Pixel(0, 0),
          }),
          sq2: new AMap.Icon({
            // 图标尺寸
            size: new AMap.Size(150, 150),
            // 图标的取图地址
            image: s2,
            // 图标所用图片大小
            imageSize: new AMap.Size(150, 150),
            // 图标取图偏移量
            imageOffset: new AMap.Pixel(0, 0),
          }),
          sq3: new AMap.Icon({
            // 图标尺寸
            size: new AMap.Size(150, 150),
            // 图标的取图地址
            image: s3,
            // 图标所用图片大小
            imageSize: new AMap.Size(150, 150),
            // 图标取图偏移量
            imageOffset: new AMap.Pixel(0, 0),
          }),
          sq4: new AMap.Icon({
            // 图标尺寸
            size: new AMap.Size(150, 150),
            // 图标的取图地址
            image: s4,
            // 图标所用图片大小
            imageSize: new AMap.Size(150, 150),
            // 图标取图偏移量
            imageOffset: new AMap.Pixel(0, 0),
          }),
          sq5: new AMap.Icon({
            // 图标尺寸
            size: new AMap.Size(150, 150),
            // 图标的取图地址
            image: s5,
            // 图标所用图片大小
            imageSize: new AMap.Size(150, 150),
            // 图标取图偏移量
            imageOffset: new AMap.Pixel(0, 0),
          }),
          sq6: new AMap.Icon({
            // 图标尺寸
            size: new AMap.Size(150, 150),
            // 图标的取图地址
            image: s6,
            // 图标所用图片大小
            imageSize: new AMap.Size(150, 150),
            // 图标取图偏移量
            imageOffset: new AMap.Pixel(0, 0),
          }),

          // 微控站
          sw1: new AMap.Icon({
            // 图标尺寸
            size: new AMap.Size(120, 120),
            // 图标的取图地址
            image: s1,
            // 图标所用图片大小
            imageSize: new AMap.Size(120, 120),
            // 图标取图偏移量
            imageOffset: new AMap.Pixel(0, 0),
          }),
          sw2: new AMap.Icon({
            // 图标尺寸
            size: new AMap.Size(120, 120),
            // 图标的取图地址
            image: s2,
            // 图标所用图片大小
            imageSize: new AMap.Size(120, 120),
            // 图标取图偏移量
            imageOffset: new AMap.Pixel(0, 0),
          }),
          sw3: new AMap.Icon({
            // 图标尺寸
            size: new AMap.Size(120, 120),
            // 图标的取图地址
            image: s3,
            // 图标所用图片大小
            imageSize: new AMap.Size(120, 120),
            // 图标取图偏移量
            imageOffset: new AMap.Pixel(0, 0),
          }),
          sw4: new AMap.Icon({
            // 图标尺寸
            size: new AMap.Size(120, 120),
            // 图标的取图地址
            image: s4,
            // 图标所用图片大小
            imageSize: new AMap.Size(120, 120),
            // 图标取图偏移量
            imageOffset: new AMap.Pixel(0, 0),
          }),
          sw5: new AMap.Icon({
            // 图标尺寸
            size: new AMap.Size(120, 120),
            // 图标的取图地址
            image: s5,
            // 图标所用图片大小
            imageSize: new AMap.Size(120, 120),
            // 图标取图偏移量
            imageOffset: new AMap.Pixel(0, 0),
          }),
          sw6: new AMap.Icon({
            // 图标尺寸
            size: new AMap.Size(120, 120),
            // 图标的取图地址
            image: s6,
            // 图标所用图片大小
            imageSize: new AMap.Size(120, 120),
            // 图标取图偏移量
            imageOffset: new AMap.Pixel(0, 0),
          }),

          // 市控站
          ss1: new AMap.Icon({
            // 图标尺寸
            size: new AMap.Size(180, 180),
            // 图标的取图地址
            image: s1,
            // 图标所用图片大小
            imageSize: new AMap.Size(180, 180),
            // 图标取图偏移量
            imageOffset: new AMap.Pixel(0, 0),
          }),
          ss2: new AMap.Icon({
            // 图标尺寸
            size: new AMap.Size(180, 180),
            // 图标的取图地址
            image: s2,
            // 图标所用图片大小
            imageSize: new AMap.Size(180, 180),
            // 图标取图偏移量
            imageOffset: new AMap.Pixel(0, 0),
          }),
          ss3: new AMap.Icon({
            // 图标尺寸
            size: new AMap.Size(180, 180),
            // 图标的取图地址
            image: s3,
            // 图标所用图片大小
            imageSize: new AMap.Size(180, 180),
            // 图标取图偏移量
            imageOffset: new AMap.Pixel(0, 0),
          }),
          ss4: new AMap.Icon({
            // 图标尺寸
            size: new AMap.Size(180, 180),
            // 图标的取图地址
            image: s4,
            // 图标所用图片大小
            imageSize: new AMap.Size(180, 180),
            // 图标取图偏移量
            imageOffset: new AMap.Pixel(0, 0),
          }),
          ss5: new AMap.Icon({
            // 图标尺寸
            size: new AMap.Size(180, 180),
            // 图标的取图地址
            image: s5,
            // 图标所用图片大小
            imageSize: new AMap.Size(180, 180),
            // 图标取图偏移量
            imageOffset: new AMap.Pixel(0, 0),
          }),
          ss6: new AMap.Icon({
            // 图标尺寸
            size: new AMap.Size(180, 180),
            // 图标的取图地址
            image: s6,
            // 图标所用图片大小
            imageSize: new AMap.Size(180, 180),
            // 图标取图偏移量
            imageOffset: new AMap.Pixel(0, 0),
          }),
        };
        maps.on("complete", () => {
          this.createOverlay();
          this.creatGeojson();
          this.creatControlBar();
          this.creatAreaMarker();
          // this.creatLeiDaMarker();
          this.creatJianKongMarker();
          // setTimeout(() => {
          // }, 2000)
          // this.creatImg();
          // this.windDirection();
        });
      })
      .catch((e) => {
        console.log(e);
      });
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    // 跳转空气详情
    (window as any).toAirDetails = (data: any, type: number) => {
      //@ts-ignore
      // eslint-disable-next-line no-undef
      _this.$router.push({
        //@ts-ignore
        // eslint-disable-next-line no-undef
        path: "/airDetails",
        query: {
          data,
          type,
          //@ts-ignore
          // eslint-disable-next-line no-undef
          stationCode: _this.stationCode,
        },
      });
    };

    // 关闭地图信息弹框
    (window as any).closeInfoWindow = () => {
      //@ts-ignore
      // eslint-disable-next-line no-undef
      if (maps) {
        //@ts-ignore
        // eslint-disable-next-line no-undef
        maps.clearInfoWindow();
      }
    };
    (window as any)._this = this;
  }
  // 切换展示区域
  handleAreaCodeListChange() {
    // maps.clearMap()
    if (!maps) return;
    this.toUpMap();
    if (maps) {
      this.creatGeojson();
    }
    // 包含金牛区 把监控和雷达展示出来
    if (this.areaCodeList.includes("510106") && maps) {
      this.creatLeiDaMarker();
      this.creatJianKongMarker();
    } else if (!this.areaCodeList.includes("510106") && maps) {
      // 否则移除
      if (this.radarMaker) {
        maps.remove(this.radarMaker);
      }
      if (this.videoMarker && this.videoMarker.length) {
        maps.remove(this.videoMarker);
      }
    }
  }
  // 添加街道划分区域地图数据
  private creatGeojson() {
    const map = maps;
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const that = this;
    if (this.mapGeoJson) {
      map.remove(this.mapGeoJson);
    }
    if (this.textList && this.textList.length) {
      map.remove(this.textList);
    }
    if (this.areaList && this.areaList.length) {
      map.remove(this.areaList);
    }
    const textList: any = [];
    const areaList: any[] = [];
    // 添加金牛区地理信息数据 1
    const geojson = new AMap.GeoJSON({
      geoJSON: jinniuStreet,
      getPolygon: function(geojson: any, lnglats: any) {
        // if (!that.areaCodeList || !that.areaCodeList.length) {
        //   const text = new AMap.Text({
        //     text: geojson.properties.name,
        //     anchor: "center", // 设置文本标记锚点
        //     draggable: false,
        //     cursor: "pointer",
        //     zIndex: 10,
        //     angle: 0,
        //     style: {
        //       padding: ".75rem 1.25rem",
        //       "margin-bottom": "1rem",
        //       "border-radius": ".25rem",
        //       "background-color": "transparent",
        //       "border-width": 0,
        //       "text-align": "center",
        //       "font-size": "14px",
        //       color: "#36E9EF",
        //     },
        //     position: [
        //       geojson.properties.center.lng,
        //       geojson.properties.center.lat,
        //     ],
        //   });
        //   textList.push(text);
        //   text.setMap(map);
        //   const polygon = new AMap.Polygon({
        //     path: geojson.geometry.coordinates[0],
        //     // strokeColor: "#0ea9f9",
        //     strokeColor: "#2fbeb5",
        //     strokeWeight: 2,
        //     strokeOpacity: 1,
        //     fillOpacity: 0.5, // 多边形填充透明度
        //     fillColor: geojson.properties.bgColor,
        //     // that.enterType === EnterType.AIR
        //     //   ? that.mapColor
        //     //   : "rgba(0,49,113, 0.15)",
        //     zIndex: 10,
        //   });
        //   areaList.push(polygon);
        //   map.add(polygon);
        // } else

        if (that.areaCodeList.includes(geojson.properties.adcode)) {
          const text = new AMap.Text({
            text: geojson.properties.name,
            anchor: "center", // 设置文本标记锚点
            draggable: false,
            cursor: "pointer",
            zIndex: 10,
            angle: 0,
            style: {
              padding: ".75rem 1.25rem",
              "margin-bottom": "1rem",
              "border-radius": ".25rem",
              "background-color": "transparent",
              "border-width": 0,
              "text-align": "center",
              "font-size": "14px",
              color: "#36E9EF",
            },
            position: [
              geojson.properties.center.lng,
              geojson.properties.center.lat,
            ],
          });
          textList.push(text);
          text.setMap(map);

          const path = geojson.geometry.coordinates[0].map((item) =>
            item[0]
              ? new AMap.LngLat(item[0], item[1])
              : new AMap.LngLat(item.lng, item.lat)
          );
          const polygon = new AMap.Polygon({
            path: path,
            // strokeColor: "#0ea9f9",
            strokeColor: "#2fbeb5",
            strokeWeight: 2,
            strokeOpacity: 1,
            fillOpacity: 0.5, // 多边形填充透明度
            fillColor: geojson.properties.bgColor,
            // that.enterType === EnterType.AIR
            //   ? that.mapColor
            //   : "rgba(0,49,113, 0.15)",
            zIndex: 10,
          });
          areaList.push(polygon);
          map.add(polygon);
        }
        that.textList = textList;
        that.areaList = areaList;
      },
    });

    // 添加金牛区地理信息数据 2
    this.mapGeoJson = geojson;
    geojson.setMap(map);
  }
  beforeDestroy() {
    // 清空地图相关数据和全局挂载事件防止内存泄漏
    if (!maps) return;
    maps.clearMap();
    this.clickMar = [];
    this.markList = [];
    this.controlBar = null;
    maps.destroy();
    maps = null;
    (window as any).closeInfoWindow = null;
    (window as any).toAirDetails = null;
    (window as any)._this = null;
    AMap = null;
  }
  private Object3DLayer: any = null;
  private prisms: any = [];
  private isToDown: any = false;
  private isToUp: any = false;
  // 高德地图
  private map(): void {
    const that = this;
    // 初始化地图
    maps = new AMap.Map(this.$refs.container, {
      center: [104.05, 30.73],
      position: [104.05, 30.73],
      zoom: this.mapZoom,
      viewMode: this.viewMode,
      pitch: 40,
      zoomEnable: true,
      dragEnable: true,
      zooms: [12, 18],
    });
    // 处理webgl上下文丢失事件
    webglcontextlostHandle.call(this);
    // 设置地图样式
    maps.setMapStyle(this.mapStyle);

    // 添加3D图层
    this.Object3DLayer = new AMap.Object3DLayer();
    maps.add(this.Object3DLayer);
    // new AMap.GeoJSON({
    //   geoJSON: jinniuStreet,
    //   getPolygon: function(geojson: any, lnglats: any) {
    //     AMap.convertFrom(geojson.geometry.coordinates[0], "gps", function(
    //       status: any,
    //       result: any
    //     ) {
    //       // const area: any = AMap.GeometryUtil.ringArea(lnglats[0]);
    //       if (geojson.properties.name !== "金牛区") {
    //         const bounds = result.locations;
    //         const height = -5000;
    //         const color = "rgba(47, 110, 238, 0.45)"; // rgba
    //         const prism = new AMap.Object3D.Prism({
    //           path: bounds,
    //           height: height,
    //           color: color
    //         });
    //
    //         prism.transparent = true;
    //         that.Object3DLayer.add(prism);
    //         that.prisms.push(prism);
    //       }
    //     });
    //   }
    // });
    this.toUpMap();
    // maps.on('zoomchange', () => {
    //   if (maps.getZoom() <= 13.5) {
    //     this.isToUp = false
    //     if (!this.isToDown) {
    //       this.$emit('type', 1)
    //     }
    //   }
    //   if (maps.getZoom() > 13.5) {
    //     this.isToDown = false
    //     if (!this.isToUp) {
    //       this.toUpMap()
    //     }
    //   }
    // })
    setTimeout(() => {
      return;
      // 添加雷达图
      // eslint-disable-next-line @typescript-eslint/no-this-alias
      const object3DlayerScan = new AMap.Object3DLayer();
      maps.add(object3DlayerScan);
      let radar: any;

      // 添加雷达扫描2D网格
      // 构造雷达地形
      const buildRadar = function() {
        radar = new AMap.Object3D.Mesh();
        radar.transparent = true;
        radar.backOrFront = "front";

        const geometry = radar.geometry;
        let radius = 9 * 1200; // 半径 * 米
        radius = radius / maps.getResolution(maps.getCenter(), 20);
        const unit = 1; // 单位
        const range = 100; // 扇形角度
        const count = range / unit;

        const getOpacity = function(scale: number): number {
          return 1 - Math.pow(scale, 0.2);
        };

        setTimeout(() => {
          for (let i = 0; i < count; i += 1) {
            const angle1 = (i * unit * Math.PI) / 180;
            const angle2 = ((i + 1) * unit * Math.PI) / 180;
            const p1x = Math.cos(angle1) * radius;
            const p1y = Math.sin(angle1) * radius;
            const p2x = Math.cos(angle2) * radius;
            const p2y = Math.sin(angle2) * radius;

            geometry.vertices.push(0, 0, 0);
            geometry.vertices.push(p1x, p1y, 0);
            geometry.vertices.push(p2x, p2y, 0);

            const opacityStart = getOpacity(i / count);
            const opacityEnd = getOpacity((i + 1) / count);

            geometry.vertexColors.push(0, 0.5, 0.2, opacityStart);
            geometry.vertexColors.push(0, 0.5, 0.2, opacityStart);
            geometry.vertexColors.push(0, 0.5, 0.2, opacityEnd);
          }
          radar.position(maps.getCenter());

          object3DlayerScan.add(radar);
        }, 0);
      };

      // 雷达扫描处理
      const scan = function(): void {
        radar.rotateZ(-1);
        AMap.Util.requestAnimFrame(scan);
      };

      buildRadar();
      scan();
    }, 5000);
  }
  private toDownMap() {
    this.areaData.map((item: any) => {
      if (this.typeColor == "aqi") {
        for (const item1 of AQIAirColors1) {
          if (!item.concentration) {
            item.color = item1.color;
          } else if (
            item.concentration >= item1.min &&
            item.concentration <= item1.max
          ) {
            item.color = item1.color;
          }
        }
      }
      if (this.typeColor == "101") {
        for (const item1 of NO2AirColors1) {
          if (!item.concentration) {
            item.color = item1.color;
          } else if (
            item.concentration >= item1.min &&
            item.concentration <= item1.max
          ) {
            item.color = item1.color;
          }
        }
      }
      if (this.typeColor == "105") {
        for (const item1 of PM25AirColors1) {
          if (!item.concentration) {
            item.color = item1.color;
          } else if (
            item.concentration >= item1.min &&
            item.concentration <= item1.max
          ) {
            item.color = item1.color;
          }
        }
      }
      if (this.typeColor == "102") {
        for (const item1 of O3AirColors1) {
          if (!item.concentration) {
            item.color = item1.color;
          } else if (
            item.concentration >= item1.min &&
            item.concentration <= item1.max
          ) {
            item.color = item1.color;
          }
        }
      }
      if (this.typeColor == "100") {
        for (const item1 of SO2AirColors1) {
          if (!item.concentration) {
            item.color = item1.color;
          } else if (
            item.concentration >= item1.min &&
            item.concentration <= item1.max
          ) {
            item.color = item1.color;
          }
        }
      }
      if (this.typeColor == "103") {
        for (const item1 of COAirColors1) {
          if (!item.concentration) {
            item.color = item1.color;
          } else if (
            item.concentration >= item1.min &&
            item.concentration <= item1.max
          ) {
            item.color = item1.color;
          }
        }
      }
      if (this.typeColor == "104") {
        for (const item1 of PM10AirColors1) {
          if (!item.concentration) {
            item.color = item1.color;
          } else if (
            item.concentration >= item1.min &&
            item.concentration <= item1.max
          ) {
            item.color = item1.color;
          }
        }
      }
      return item;
    });
    const that = this;
    this.isToDown = true;
    this.prisms.forEach((item: any) => {
      this.Object3DLayer.remove(item);
    });
    that.prisms = [];
    new AMap.GeoJSON({
      geoJSON: jinniuStreet,
      getPolygon: function(geojson: any, lnglats: any) {
        // const area: any = AMap.GeometryUtil.ringArea(lnglats[0]);
        if (geojson.properties.name !== "金牛区") {
          const item = that.areaData.find(
            (item) => item.streetName.indexOf(geojson.properties.name) > -1
          );
          const bounds = geojson.geometry.coordinates[0];
          const height = -5000;
          const color = "rgba(47, 110, 238, 0.45)"; // rgba
          // const color = item.color; // rgba
          const prism = new AMap.Object3D.Prism({
            path: bounds,
            height: height,
            color:
              item.concentration !== "--"
                ? item.color || color
                : "rgba(190, 183, 183, 0.45)",
          });

          prism.transparent = true;
          that.prisms.push(prism);
          that.Object3DLayer.add(prism);
          that.Object3DLayer.reDraw();
        }
      },
    });
  }
  private toUpMap() {
    this.$emit("type", 2);
    const that = this;
    this.isToUp = true;
    this.prisms.forEach((item: any) => {
      this.Object3DLayer.remove(item);
    });
    new AMap.GeoJSON({
      geoJSON: jinniuStreet,
      getPolygon: function(geojson: any, lnglats: any) {
        const bounds = geojson.geometry.coordinates[0].map((item) =>
          item[0]
            ? new AMap.LngLat(item[0], item[1])
            : new AMap.LngLat(item.lng, item.lat)
        );

        // if (!that.areaCodeList || !that.areaCodeList.length) {
        //   const height = -5000;
        //   const color = "rgba(47, 110, 238, 0.45)"; // rgba
        //   const prism = new AMap.Object3D.Prism({
        //     path: bounds,
        //     height: height,
        //     color: color,
        //   });

        //   prism.transparent = true;
        //   that.prisms.push(prism);
        //   that.Object3DLayer.add(prism);
        //   that.Object3DLayer.reDraw();
        // } else
        if (that.areaCodeList.includes(geojson.properties.adcode)) {
          const height = -5000;
          const color = "rgba(47, 110, 238, 0.45)"; // rgba
          const prism = new AMap.Object3D.Prism({
            path: bounds,
            height: height,
            color: color,
          });
          prism.transparent = true;
          that.prisms.push(prism);
          that.Object3DLayer.add(prism);
          that.Object3DLayer.reDraw();
        }
      },
    });
  }
  // 添加行政区外的覆盖物
  private createOverlay() {
    const map = maps;
    if (!map) return;
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const that = this;
    // 添加金牛区地理信息数据 3
    new AMap.DistrictSearch({
      extensions: "all",
      subdistrict: 0,
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    }).search("金牛区", function(status: any, result: any) {
      // 外多边形坐标数组和内多边形坐标数组
      const outer = [
        new AMap.LngLat(-360, 90, true),
        new AMap.LngLat(-360, -90, true),
        new AMap.LngLat(360, -90, true),
        new AMap.LngLat(360, 90, true),
      ];
      const holes = result.districtList[0].boundaries;

      const pathArray: any = [outer];
      // eslint-disable-next-line prefer-spread
      pathArray.push.apply(pathArray, holes);
      const polygon = new AMap.Polygon({
        pathL: pathArray,
        //线条颜色，使用16进制颜色代码赋值。默认值为#006600
        strokeColor: "rgb(255,255,255)",
        strokeWeight: 0,
        //轮廓线透明度，取值范围[0,1]，0表示完全透明，1表示不透明。默认为0.9
        strokeOpacity: 0,
        //多边形填充颜色，使用16进制颜色代码赋值，如：#FFAA00
        fillColor: "rgba(3,4,130)",
        // fillColor: "rgba(4,20,50)",
        // fillColor: "#0A1C5F",
        //多边形填充透明度，取值范围[0,1]，0表示完全透明，1表示不透明。默认为0.9
        fillOpacity: 0,
        //轮廓线样式，实线:solid，虚线:dashed
        strokeStyle: "solid",
        strokeDasharray: [10, 2, 10],
      });
      polygon.setPath(pathArray);
      map.add(polygon);
    });
  }

  // 添加 3D 罗盘控制
  private controlBar: any;
  private creatControlBar() {
    AMap.plugin(["AMap.ControlBar"], () => {
      const controlBar = new AMap.ControlBar({
        position: {
          bottom: "-0.65rem",
          left: "3.85rem",
          zIndex: 9,
          transition: "1.5s",
        },
      });
      this.controlBar = controlBar;
      maps.addControl(this.controlBar);
    });
  }

  // 判断站点的颜色并添加空气站点和热力图
  private checkSiteMarker() {
    const map = maps;
    if (!map) return;
    const maplist: any = [];
    if (map.w) {
      map.w.layers.forEach((item: any, index: number) => {
        if (item.w.type !== "heatMap3DLayer") {
          maplist.push(item);
        }
      });
      map.w.layers = maplist;
    }

    /* if (this.clickMar && this.clickMar.length > 0) {
        maps.remove(this.clickMar);
        this.clickMar = [];
        this.markList = [];
      } */
    this.heatMapDataList = [];
    // for (const item of this.mapMarker) {
    //   this.heatMapDataList.push({
    //     lng: item.gcLng,
    //     lat: item.gcLat,
    //     count: item.concentration,
    //   })
    // }
    if (this.typeColor == "aqi") {
      this.heathMapMax = 151;
      // AQI
      for (const item of this.mapMarker) {
        // AQI
        for (const item1 of AQIAirColors) {
          // AQI
          if (item.concentration !== null) {
            if (
              item.concentration >= item1.min &&
              item.concentration <= item1.max
            ) {
              /* eslint-disable @typescript-eslint/ban-ts-ignore */
              //@ts-ignore
              item.type = item1.type;
              item.textColor = item1.color;
            }
          } else {
            item.type = null;
          }
        }
      }
    } else if (this.typeColor == "100") {
      this.heathMapMax = 300;
      // SO2
      for (const item of this.mapMarker) {
        // SO2
        for (const item1 of SO2AirColors) {
          // SO2
          if (item.concentration !== null) {
            if (
              item.concentration >= item1.min &&
              item.concentration <= item1.max
            ) {
              /* eslint-disable @typescript-eslint/ban-ts-ignore */
              //@ts-ignore
              item.type = item1.type;
              item.textColor = item1.color;
            }
          } else {
            item.type = null;
          }
        }
      }
    } else if (this.typeColor == "101") {
      this.heathMapMax = 200;
      // NO2
      for (const item of this.mapMarker) {
        // NO2
        for (const item1 of NO2AirColors) {
          // NO2
          if (item.concentration !== null) {
            if (
              item.concentration >= item1.min &&
              item.concentration <= item1.max
            ) {
              /* eslint-disable @typescript-eslint/ban-ts-ignore */
              //@ts-ignore
              item.type = item1.type;
              item.textColor = item1.color;
            }
          } else {
            item.type = null;
          }
        }
      }
    } else if (this.typeColor == "102") {
      this.heathMapMax = 300;
      // O3
      for (const item of this.mapMarker) {
        // O3
        for (const item1 of O3AirColors) {
          // O3
          if (item.concentration !== null) {
            if (
              item.concentration >= item1.min &&
              item.concentration <= item1.max
            ) {
              /* eslint-disable @typescript-eslint/ban-ts-ignore */
              //@ts-ignore
              item.type = item1.type;
              item.textColor = item1.color;
            }
          } else {
            item.type = null;
          }
        }
      }
    } else if (this.typeColor == "103") {
      this.heathMapMax = 10;
      // CO
      for (const item of this.mapMarker) {
        // CO
        for (const item1 of COAirColors) {
          // CO
          if (item.concentration !== null) {
            if (
              item.concentration >= item1.min &&
              item.concentration <= item1.max
            ) {
              /* eslint-disable @typescript-eslint/ban-ts-ignore */
              //@ts-ignore
              item.type = item1.type;
              item.textColor = item1.color;
            }
          } else {
            item.type = null;
          }
        }
      }
    } else if (this.typeColor == "104") {
      this.heathMapMax = 250;
      // PM10
      for (const item of this.mapMarker) {
        // PM10
        for (const item1 of PM10AirColors) {
          // PM10
          if (item.concentration !== null) {
            if (
              item.concentration >= item1.min &&
              item.concentration <= item1.max
            ) {
              /* eslint-disable @typescript-eslint/ban-ts-ignore */
              //@ts-ignore
              item.type = item1.type;
              item.textColor = item1.color;
            }
          } else {
            item.type = null;
          }
        }
      }
    } else if (this.typeColor == "105") {
      this.heathMapMax = 115;
      // PM2.5
      for (const item of this.mapMarker) {
        // PM2.5
        for (const item1 of PM25AirColors) {
          // PM2.5
          if (item.concentration !== null) {
            if (
              item.concentration >= item1.min &&
              item.concentration <= item1.max
            ) {
              /* eslint-disable @typescript-eslint/ban-ts-ignore */
              //@ts-ignore
              item.type = item1.type;
              item.textColor = item1.color;
            }
          } else {
            item.type = null;
          }
        }
      }
    }
    if (this.mapIndex !== 0) {
      // const maplist: any = [];
      if (map.w) {
        map.w.layers.forEach((item: any, index: number) => {
          if (item.w.type == "heatMap3DLayer") {
            map.w.layers.splice(index, 1);
            // maplist.push(item);
          }
        });
        // map.w.layers = maplist;
      }
      if (map.Ce) {
        map.Ce.layers.forEach((item: any, index: number) => {
          if (item.Ce.type == "heatMap3DLayer") {
            map.Ce.layers.splice(index, 1);
            // maplist.push(item);
          }
        });
        // map.w.layers = maplist;
      }
      if (this.heatMapDataList.length !== 0) {
        this.heathMap();
      } else {
        this.heathMap();
      }
    }
    setTimeout(() => {
      this.creatMarker();
    }, 0);
  }
  private heatmap: any = "";
  // 热力图
  private heathMap(): void {
    const map = maps;
    if (!map) return;
    const maplist: any = [];
    if (map.w) {
      map.w.layers.forEach((item: any, index: number) => {
        if (item.w.type !== "heatMap3DLayer") {
          maplist.push(item);
        }
      });
      map.w.layers = maplist;
    }

    // let heatmap: any;
    const that = this;
    const heathMapMax = this.heathMapMax;
    const heatMapData: any = this.heatMapDataList;
    map.plugin(["AMap.Heatmap"], function() {
      if (that.heatmap) {
        that.heatmap.setMap(null);
      }
      //初始化heatmap对象
      that.heatmap = new AMap.Heatmap(map, {
        radius: 65, //给定半径
        opacity: [0.4, 1],
        gradient: {
          0.1: "rgb(0,255,0)",
          0.2: "rgb(0,255,0)",
          0.3: "rgb(0,255,0)",
          0.4: "rgb(0,255,0)",
          0.6: "rgb(255,255,0)",
          0.8: "rgb(255,126,0)",
          1: "rgb(255,0,0)",
        },
        zIndex: 9999,
      });

      // 进行数据设置及渲染
      that.heatmap.setDataSet({
        data: heatMapData,
        max: heathMapMax,
      });
    });
  }

  private stationCode: any;
  private async showInfo(data: any): Promise<any> {
    let details: any;
    if (data.target) {
      // 鼠标滑过事件
      details = data.target.w.data;
    } else {
      // 定时函数触发
      details = data.w.data;
    }

    this.stationCode = details.stationCode;

    await airStationDetails(this.stationCode).then((res: any) => {
      res.data.data.dataTime = res.data.data.dataTime
        ? res.data.data.dataTime.slice(5, 16)
        : "-";
      details.pollutionDetailsList = res.data.data;
    });

    // SO2
    for (const item of SO2AirColors) {
      // SO2
      if (details.pollutionDetailsList["SO2"] !== null) {
        if (
          details.pollutionDetailsList["SO2"] >= item.min &&
          details.pollutionDetailsList["SO2"] <= item.max
        ) {
          details.pollutionDetailsList["SO2Color"] = item.color;
        }
      } else {
        details.pollutionDetailsList["SO2Color"] = "#fff";
      }
    }
    // NO2
    for (const item of NO2AirColors) {
      // NO2
      if (details.pollutionDetailsList["NO2"] !== null) {
        if (
          details.pollutionDetailsList["NO2"] >= item.min &&
          details.pollutionDetailsList["NO2"] <= item.max
        ) {
          details.pollutionDetailsList["NO2Color"] = item.color;
        }
      } else {
        details.pollutionDetailsList["SO2Color"] = "#fff";
      }
    }
    // PM2.5
    for (const item of PM25AirColors) {
      // PM2.5
      if (details.pollutionDetailsList["PM2.5"] !== null) {
        if (
          details.pollutionDetailsList["PM2.5"] >= item.min &&
          details.pollutionDetailsList["PM2.5"] <= item.max
        ) {
          details.pollutionDetailsList["PM2.5Color"] = item.color;
        }
      } else {
        details.pollutionDetailsList["SO2Color"] = "#fff";
      }
    }
    // PM10
    for (const item of PM10AirColors) {
      // PM10
      if (details.pollutionDetailsList["PM10"] !== null) {
        if (
          details.pollutionDetailsList["PM10"] >= item.min &&
          details.pollutionDetailsList["PM10"] <= item.max
        ) {
          details.pollutionDetailsList["PM10Color"] = item.color;
        }
      } else {
        details.pollutionDetailsList["SO2Color"] = "#fff";
      }
    }
    // O3
    for (const item of O3AirColors) {
      // O3
      if (details.pollutionDetailsList["03"] !== null) {
        if (
          details.pollutionDetailsList["03"] >= item.min &&
          details.pollutionDetailsList["03"] <= item.max
        ) {
          details.pollutionDetailsList["O3Color"] = item.color;
        }
      } else {
        details.pollutionDetailsList["SO2Color"] = "#fff";
      }
    }
    // CO
    for (const item of COAirColors) {
      // CO
      if (details.pollutionDetailsList["CO"] !== null) {
        if (
          details.pollutionDetailsList["CO"] >= item.min &&
          details.pollutionDetailsList["CO"] <= item.max
        ) {
          details.pollutionDetailsList["COColor"] = item.color;
        }
      } else {
        details.pollutionDetailsList["SO2Color"] = "#fff";
      }
    }
    // AQI
    for (const item of AQIAirColors) {
      // AQI
      if (details.pollutionDetailsList["AQI"] !== null) {
        if (
          details.pollutionDetailsList["AQI"] >= item.min &&
          details.pollutionDetailsList["AQI"] <= item.max
        ) {
          details.pollutionDetailsList["AQIColor"] = item.color;
          details.pollutionDetailsList["AQIText"] = item.text;
        }
      } else {
        details.pollutionDetailsList["SO2Color"] = "#fff";
      }
    }

    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const that: any = this;
    let infoWindow: any;
    const map = maps;
    //在指定位置打开信息窗体
    function openInfo() {
      //构建信息窗体中显示的内容
      let html: Array<string> = [];

      // 空气质量监测
      html = [
        `<div class="marker-content">
          <img src="https://oss-chengdu.vankeytech.com:9000/vankeytech-ep/sys/shanchu3.png" alt class="closeInfoWindow" onclick="closeInfoWindow()"/>
          <div class="marker-top">
            <div class="marker-top-top">
              <div class="marker-top-name">
                <div class="marker-top-name1">${details.positionName}</div>
                <div class="marker-top-state" style="background:${
                  details.online ? "#22B331" : "#a8a8a8"
                };">${details.online ? "在线" : "离线"}</div>
              </div>
              <div class="update-time">${
                details.pollutionDetailsList.dataTime
              }</div>
            </div>
            <div class="marker-top-bottom">
              <div class="marker-top-left">
                <div class="dian"></div>
                <div>当前空气质量：<text style="color:${
                  details.pollutionDetailsList["AQIColor"]
                }">${
          details.pollutionDetailsList["AQI"] === undefined
            ? "－"
            : details.pollutionDetailsList["AQI"] || "-"
        }</text></div>
              </div>
              <div class="marker-top-right">
                <div style="color:${
                  details.pollutionDetailsList["AQIColor"]
                }">${
          details.pollutionDetailsList["AQIText"] === undefined
            ? "－"
            : details.pollutionDetailsList["AQIText"]
        }</div>
              </div>
            </div>
          </div>
          <div class="marker-bottom">
            <div>
              <div>
                <p style="color:${
                  details.pollutionDetailsList["SO₂"] === undefined ||
                  details.pollutionDetailsList["SO₂"] === null
                    ? "#A9A9A9"
                    : "#ffffff"
                }">SO₂</p>
                <p style="color:${details.pollutionDetailsList["SO2Color"]}">${
          details.pollutionDetailsList["SO₂"] === undefined ||
          details.pollutionDetailsList["SO₂"] === null
            ? "－"
            : details.pollutionDetailsList["SO₂"]
        }</p>
              </div>
              <div>
                <p style="color:${
                  details.pollutionDetailsList["NO₂"] === undefined ||
                  details.pollutionDetailsList["NO₂"] === null
                    ? "#A9A9A9"
                    : "#ffffff"
                }">NO₂</p>
                <p style="color:${details.pollutionDetailsList["NO2Color"]}">${
          details.pollutionDetailsList["NO₂"] === undefined ||
          details.pollutionDetailsList["NO₂"] === null
            ? "－"
            : details.pollutionDetailsList["NO₂"]
        }</p>
              </div>
              <div>
                <p style="color:${
                  details.pollutionDetailsList["PM₂.₅"] === undefined ||
                  details.pollutionDetailsList["PM₂.₅"] === null
                    ? "#A9A9A9"
                    : "#ffffff"
                }">PM₂.₅</p>
                <p style="color:${
                  details.pollutionDetailsList["PM2.5Color"]
                }">${
          details.pollutionDetailsList["PM₂.₅"] === undefined ||
          details.pollutionDetailsList["PM₂.₅"] === null
            ? "－"
            : details.pollutionDetailsList["PM₂.₅"]
        }</p>
              </div>
              <div>
                <p style="color:${
                  details.pollutionDetailsList["O₃"] === undefined ||
                  details.pollutionDetailsList["O₃"] === null
                    ? "#A9A9A9"
                    : "#ffffff"
                }">O₃</p>
                <p style="color:${details.pollutionDetailsList["O3Color"]}">${
          details.pollutionDetailsList["O₃"] === undefined ||
          details.pollutionDetailsList["O₃"] === null
            ? "－"
            : details.pollutionDetailsList["O₃"]
        }</p>
              </div>
              <div>
                <p style="color:${
                  details.pollutionDetailsList["CO"] === undefined ||
                  details.pollutionDetailsList["CO"] === null
                    ? "#A9A9A9"
                    : "#ffffff"
                }">CO</p>
                <p style="color:${details.pollutionDetailsList["COColor"]}">${
          details.pollutionDetailsList["CO"] === undefined ||
          details.pollutionDetailsList["CO"] === null
            ? "－"
            : details.pollutionDetailsList["CO"]
        }</p>
              </div>
              <div>
                <p style="color:${
                  details.pollutionDetailsList["PM₁₀"] === undefined ||
                  details.pollutionDetailsList["PM₁₀"] === null
                    ? "#A9A9A9"
                    : "#ffffff"
                }">PM₁₀</p>
                <p style="color:${details.pollutionDetailsList["PM10Color"]}">${
          details.pollutionDetailsList["PM₁₀"] === undefined ||
          details.pollutionDetailsList["PM₁₀"] === null
            ? "－"
            : details.pollutionDetailsList["PM₁₀"]
        }</p>
              </div>
            </div>
          <div class="marker-top-detail" onclick="toAirDetails(1, 1)">查看详情</div>
          </div>
          <div class="triangle"></div>
        </div>`,
      ];

      infoWindow = new AMap.InfoWindow({
        // anchor: "bottom-center",
        offset:
          data.target && data.target.w.data.stationTypeId == 1
            ? new AMap.Pixel(0, -20)
            : new AMap.Pixel(0, 0),
        isCustom: true,
        content: html.join(""), //使用默认信息窗体框样式，显示信息内容
      });
      if (data.target) {
        infoWindow.open(map, [
          data.target.w.data.gcLng,
          data.target.w.data.gcLat,
        ]);
      } else {
        infoWindow.open(map, [data.w.data.gcLng, data.w.data.gcLat]);
      }
    }
    openInfo();
    setTimeout(() => {
      infoWindow.close();
      infoWindow = null;
      maps.clearInfoWindow();
    }, 60 * 1000);
  }

  // 图上Marker绘制
  private clickMar: Array<any> = [];
  private markList: Array<any> = [];
  private markerMap: Object = {};
  private airTextList: Array<any> = [];
  private creatMarker(): void {
    if (this.areaMarkerList.length) {
      maps.remove(this.areaMarkerList);
    }
    if (this.areaTextList.length) {
      maps.remove(this.areaTextList);
    }
    const map = maps;

    /* if (this.clickMar && this.clickMar.length > 0) {
        map.remove(this.clickMar);
        this.clickMar = [];
        this.markList = [];
      } */
    /* if (this.airTextList && this.airTextList.length > 0) {
        map.remove(this.airTextList);
        this.airTextList = [];
      } */
    // 图上添加空气marker

    if (maps) {
      maps.clearInfoWindow();
    }

    // 报警
    /* const jingbaoIcon = new AMap.Icon({
          size: new AMap.Size(62.9, 78.2),
          image: jingbao,
          imageSize: new AMap.Size(62.9, 78.2),
          imageOffset: new AMap.Pixel(0, 0)
        }); */
    const ResolvedMakerList = this.mapMarker ? this.mapMarker : [];
    const markers: Array<any> = ResolvedMakerList;

    // 设置marker图标
    for (const item of markers) {
      if (item.stationTypeId == 1) {
        // 微控站
        if (item.online) {
          if (item.type == 1) {
            item.icon = MapIcon["markerIcon21"];
          } else if (item.type == 2) {
            item.icon = MapIcon["markerIcon22"];
          } else if (item.type == 3) {
            item.icon = MapIcon["markerIcon23"];
          } else if (item.type == 4) {
            item.icon = MapIcon["markerIcon24"];
          } else if (item.type == 5) {
            item.icon = MapIcon["markerIcon25"];
          } else if (item.type == 6) {
            item.icon = MapIcon["markerIcon26"];
          } else {
            item.icon = MapIcon["markerIcon21Hui"];
          }
        } else {
          item.icon = MapIcon["markerIcon21Hui"];
        }
        item.zIndex = 99994;
        item.offset = new AMap.Pixel(-27, -42);
        item.textOffset = new AMap.Pixel(0, -12);
      } else if (item.stationTypeId == 2) {
        // 区控站
        if (item.online) {
          if (item.type == 1) {
            item.icon = MapIcon["markerIcon11"];
          } else if (item.type == 2) {
            item.icon = MapIcon["markerIcon12"];
          } else if (item.type == 3) {
            item.icon = MapIcon["markerIcon13"];
          } else if (item.type == 4) {
            item.icon = MapIcon["markerIcon14"];
          } else if (item.type == 5) {
            item.icon = MapIcon["markerIcon15"];
          } else if (item.type == 6) {
            item.icon = MapIcon["markerIcon16"];
          } else {
            item.icon = MapIcon["markerIcon11Hui"];
          }
        } else {
          item.icon = MapIcon["markerIcon11Hui"];
        }
        item.zIndex = 99996;
        item.offset = new AMap.Pixel(-22.5, -22.5);
        item.textOffset = new AMap.Pixel(0, 3);
      } else if (item.stationTypeId == 4) {
        // 国控站
        if (item.online) {
          if (item.type == 1) {
            item.icon = MapIcon["markerIcon41"];
          } else if (item.type == 2) {
            item.icon = MapIcon["markerIcon42"];
          } else if (item.type == 3) {
            item.icon = MapIcon["markerIcon43"];
          } else if (item.type == 4) {
            item.icon = MapIcon["markerIcon44"];
          } else if (item.type == 5) {
            item.icon = MapIcon["markerIcon45"];
          } else if (item.type == 6) {
            item.icon = MapIcon["markerIcon46"];
          } else {
            item.icon = MapIcon["markerIcon41Hui"];
          }
        } else {
          item.icon = MapIcon["markerIcon41Hui"];
        }
        item.zIndex = 99998;
        item.offset = new AMap.Pixel(-25.5, -18.5);
        item.textOffset = new AMap.Pixel(-5, 2);
      } else if (item.stationTypeId == 3) {
        // 市控站
        if (item.online) {
          if (item.type == 1) {
            item.icon = MapIcon["markerIcon31"];
          } else if (item.type == 2) {
            item.icon = MapIcon["markerIcon32"];
          } else if (item.type == 3) {
            item.icon = MapIcon["markerIcon33"];
          } else if (item.type == 4) {
            item.icon = MapIcon["markerIcon34"];
          } else if (item.type == 5) {
            item.icon = MapIcon["markerIcon35"];
          } else if (item.type == 6) {
            item.icon = MapIcon["markerIcon36"];
          } else {
            item.icon = MapIcon["markerIcon31Hui"];
          }
        } else {
          item.icon = MapIcon["markerIcon31Hui"];
        }
        item.zIndex = 99998;
        item.offset = new AMap.Pixel(-25.5, -18.5);
        item.textOffset = new AMap.Pixel(0, 3);
      }
    }

    let visbleMarker: Array<any> = [];
    let newMarker: Array<any> = [];
    // 空气地图添加站点marker
    const alarmMarker = new AMap.Icon({
      size: new AMap.Size(90, 90),
      image: require("@/assets/gj.png"),
      imageSize: new AMap.Size(90, 90),
      imageOffset: new AMap.Pixel(0, 0),
    });
    markers.forEach((marker: any, i: number) => {
      let stationMarker = "";
      stationMarker = new AMap.Marker({
        map: map,
        icon: marker.isAlarm ? alarmMarker : marker.icon,
        zIndex: marker.zIndex,
        position: [marker.gcLng, marker.gcLat],
        // visible: false,
        // offset: new AMap.Pixel(-12.8, -15.2),
        offset: marker.offset,
        data: marker,
        animation: "AMAP_ANIMATION_NONE",
        label: {
          offset: new AMap.Pixel(36, 36), //设置文本标注偏移量
          content: `<div class="${
            marker.isAlarm ? "alarm_marker" : ""
          }"></div>`,
          zIndex: -100,
        },
      });

      this.clickMar.push(stationMarker);

      let lastIndex = this.clickMar.length - 1;
      newMarker.push(lastIndex);
      visbleMarker.push(lastIndex);

      // 显示每项指标的数字
      let textColor: any = "";
      if (marker.type == 1) {
        textColor = "#09960A";
      } else if (marker.type == 2) {
        textColor = "#c59c0a";
      } else if (marker.type == 3) {
        textColor = "#ffffff";
      } else if (marker.type == 4) {
        textColor = "#ffffff";
      } else if (marker.type == 5) {
        textColor = "#ffffff";
      } else if (marker.type == 6) {
        textColor = "#ffffff";
      }
      // if (!marker.isAlarm) {}
      const airText = new AMap.Text({
        text: marker.isAlarm
          ? ""
          : !marker.online
          ? "—"
          : marker.concentration
          ? marker.concentration
          : "—",
        anchor: "center", // 设置文本标记锚点
        cursor: "pointer",
        zIndex:
          marker.stationTypeId == 1
            ? 99995
            : marker.stationTypeId == 2
            ? 99997
            : 99999,
        style: {
          width: "144px",
          height: "27px",
          "background-color": "transparent",
          "border-width": 0,
          "text-align": "center",
          "font-size": "12px",
          color: !marker.online ? "#a8a8a8" : textColor ? textColor : "#a8a8a8",
          "font-weight": "bold",
        },
        position: [marker.gcLng, marker.gcLat],
        offset: marker.textOffset,
        // visible: false,
        data: marker,
      });

      airText.setMap(map);
      this.airTextList.push(airText);
      AMap.event.addListener(airText, "click", this.showInfo);

      const markerMapKey = `${marker.gcLng},${marker.gcLat}`;

      const mapIndex = this.markerMap[markerMapKey];
      if (mapIndex !== undefined) {
        // 当前marker已存在
        const curMarker = this.clickMar[mapIndex];
        const curAirText = this.airTextList[mapIndex];
        const curText = !marker.online
          ? "—"
          : marker.concentration
          ? marker.concentration
          : "—";

        // 显示需要显示的marker和文本
        curMarker.setIcon(marker.isAlarm ? alarmMarker : marker.icon);
        curMarker.show();
        if (!marker.isAlarm) {
          curAirText.setText(curText + "");
        } else {
          curAirText.setText("");
        }
        curAirText.show();

        let textColor: any = "";
        if (marker.type == 1) {
          textColor = "#09960A";
        } else if (marker.type == 2) {
          textColor = "#c59c0a";
        } else if (marker.type == 3) {
          textColor = "#ffffff";
        } else if (marker.type == 4) {
          textColor = "#ffffff";
        } else if (marker.type == 5) {
          textColor = "#ffffff";
        } else if (marker.type == 6) {
          textColor = "#ffffff";
        }
        curAirText.setStyle({
          color: !marker.online ? "#a8a8a8" : textColor ? textColor : "#a8a8a8",
        });

        curAirText.setOffset(marker.textOffset);

        visbleMarker.push(mapIndex);
        // 结束本次forEach循环
        return true;
      }
    });

    // 标记点事件队列
    for (let index = 0; index < this.clickMar.length; index++) {
      const item = this.clickMar[index];
      if (newMarker.includes(index)) {
        this.markList.push({
          lnglat: {
            lng: Number(item.getPosition().lng),
            lat: Number(item.getPosition().lat),
          },
        });

        this.markerMap[`${item.w.data.gcLng},${item.w.data.gcLat}`] = index;

        AMap.event.addListener(item, "click", this.showInfo);
      }

      if (visbleMarker.includes(index)) continue;

      // 隐藏多余的marker
      item.hide();

      const curAirText = this.airTextList[index];
      curAirText.hide();
    }

    /* for (const item of this.clickMar) {
          this.markList.push({
            lnglat: {
              lng: Number(item.getPosition().lng),
              lat: Number(item.getPosition().lat)
            }
          });
          AMap.event.addListener(item, "click", this.showInfo);
        } */
  }

  creatLeiDaMarker(): void {
    if (this.radarMaker) {
      maps.remove(this.radarMaker);
    }
    const marker = this.radarData[0];
    const radarIcon = new AMap.Icon({
      size: new AMap.Size(71, 84),
      image: zhzfj,
      imageSize: new AMap.Size(71, 84),
      imageOffset: new AMap.Pixel(0, 0),
    });
    const radarMaker = new AMap.Marker({
      map: maps,
      icon: radarIcon,
      zIndex: 99999,
      position: new AMap.LngLat(marker?.lng, marker?.lat),
      data: marker,
    });
    // radarMaker.setLabel({
    //   offset: new AMap.Pixel(-10, 50), //设置文本标注偏移量
    //   content: `<div class='area-number'>激光雷达</div>`,
    // })
    this.radarMaker = radarMaker;
    AMap.event.addListener(radarMaker, "click", (e: any) => {
      const isRadar: any = true;
      this.$router.push({ name: "otherPages", params: { isRadar } });
      localStorage.setItem("isRadar", isRadar);
    });
  }

  creatJianKongMarker(): void {
    if (this.videoMarker && this.videoMarker.length) {
      maps.remove(this.videoMarker);
    }
    const radarIcon = new AMap.Icon({
      size: new AMap.Size(65, 70),
      image: jainkon,
      imageSize: new AMap.Size(65, 70),
      imageOffset: new AMap.Pixel(0, 0),
    });
    const stationList: any = [];
    this.CameraData.forEach((v) => {
      stationList.push(...v.stationList);
    });
    const videoMarker: any = [];
    stationList.forEach((marker: any) => {
      const radarMaker = new AMap.Marker({
        map: maps,
        icon: radarIcon,
        zIndex: 99999,
        position: new AMap.LngLat(marker.lng, marker.lat),
        data: marker,
      });
      videoMarker.push(radarMaker);

      AMap.event.addListener(radarMaker, "click", (e: any) => {
        const { stationName } = e.target.De.data;
        const arr: any = this.CameraData;
        this.$router.push({
          name: "AtmosphericMonitor",
          params: {
            stationName,
            CameraData: arr,
          },
        });
      });
    });
    this.videoMarker = videoMarker;
  }

  private areaMarkerList: any = [];
  private areaTextList: any = [];
  private creatAreaMarker(): void {
    // if (this.isToUp) {
    //   return
    // }
    const map = maps;
    if (this.areaMarkerList.length) {
      map.remove(this.areaMarkerList);
    }
    if (this.areaTextList.length) {
      map.remove(this.areaTextList);
    }
    const areaIcon = new AMap.Icon({
      size: new AMap.Size(54, 54),
      image: areaImage,
      imageSize: new AMap.Size(54, 54),
      imageOffset: new AMap.Pixel(0, 0),
    });
    const areaIcon1 = new AMap.Icon({
      size: new AMap.Size(54, 54),
      image: areaImage1,
      imageSize: new AMap.Size(54, 54),
      imageOffset: new AMap.Pixel(0, 0),
    });
    const areaData = this.areaData.filter((item) => item.lng && item.lat);
    areaData.forEach((item: any) => {
      const marker = new AMap.Marker({
        map: map,
        position: new AMap.LngLat(Number(item.lng), Number(item.lat)),
        icon: item.concentration == "--" ? areaIcon1 : areaIcon,
        offset: new AMap.Pixel(-27, -27),
        data: item,
      });
      this.areaMarkerList.push(marker);
      const airText = new AMap.Text({
        text: item.concentration,
        anchor: "center", // 设置文本标记锚点
        cursor: "pointer",
        zIndex: 99999,
        style: {
          width: "144px",
          height: "27px",
          "background-color": "transparent",
          "border-width": 0,
          "text-align": "center",
          "font-size": "14px",
          color: "#FEFEFE",
          "font-weight": "bold",
        },
        position: [item.lng, item.lat],
        offset: new AMap.Pixel(-1, 1),
        data: item,
      });
      airText.setMap(map);
      this.areaTextList.push(airText);
      marker.setLabel({
        offset: new AMap.Pixel(-10, 50), //设置文本标注偏移量
        content: `<div class='area-number'>${item.stationCount}个站点</div>`,
      });
    });
  }
}
</script>

<style lang="less">
@import url(./map.less);
.area-number {
  background-image: url("../../assets/<EMAIL>");
  background-size: 100% 100%;
  width: 0.62rem;
  height: 0.22rem;
  z-index: 9999;
  line-height: 0.22rem;
  text-align: center;
  font-size: 0.12rem;
}
</style>
<style lang="less" scoped>
.container {
  width: 100%;
  height: 100%;
}
.marker-selected {
  width: 0.5rem;
  height: 0.5rem;
  background-image: url("../../assets/car-selected.png");
  background-size: 100% 100%;
}
</style>
<style lang="less">
.alarm_marker {
  position: relative;
  width: 10px;
  height: 10px;
  // left: 35px;
  // top: 35px;
  border-radius: 50%;
  // transform: rotateX(70deg);
  /*-moz-animation-name: ripple;*/
  /*-webkit-animation-name: ripple;*/
  animation-name: depripple_air;
  animation-delay: 0s;
  animation-duration: 1s;
  -moz-animation-iteration-count: infinite;
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
  pointer-events: none;
  background-color: rgba(153, 0, 76, 0.7);
  z-index: -100;
}
@keyframes depripple_air {
  from {
    opacity: 1;
  }
  to {
    // width: 60px;
    // height: 60px;
    // top: 30px;
    // left: 30px;
    transform: scale(10);
    border-radius: 50%;
    opacity: 0;
  }
}
.amap-marker-label {
  position: relative;
  z-index: -10;
}
</style>
