<template>
  <div class="right-first-container">
    <div class="item" v-if="JSON.stringify(detail) !== '{}'">
      <div class="title">{{ detail.title }}</div>
      <div class="content">
        <div class="description" :title="detail.content">描述：{{ detail.content || "--" }}</div>
        <div class="content-box">
          <div class="image-box" v-if="detail.taskAnnexList && detail.taskAnnexList.length">
            <!-- <img src="@/assets/patrolKanban/" alt=""> -->
            <el-image style="width: 100%; height: 100%" fit="fill" :src="detail.taskAnnexList[0].annexUrl" :preview-src-list="detail.taskAnnexList.map(it => it.annexUrl)"></el-image>
          </div>
          <div class="content_text">
            <div class="item_text">
              <span class="label">所属街道</span>
              <span class="value">{{ detail.streetName || "--" }}</span>
            </div>
            <div class="item_text">
              <span class="label">污染类型</span>
              <span class="value">{{ detail.stationTypeName || "--" }}</span>
            </div>
            <div class="item_text">
              <span class="label">上报时间</span>
              <span class="value">{{ detail.createTime || "--" }}</span>
            </div>
            <div class="item_text">
              <span class="label">详细地址</span>
              <span class="value">{{ detail.address || "--" }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="item" style="display: flex; justify-content: center; align-items: center; height: 200px;" v-else>暂无数据</div>
  </div>
</template>

<script>
export default {
  name: "",
  props: {
    detail: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {};
  },
  created() {},
  methods: {},
  computed: {},
  watch: {},
  components: {},
};
</script>

<style lang="less" scoped>
.right-first-container {
  box-sizing: border-box;
  padding-top: 20px;
}
.item {
  .title {
    width: 404px;
    height: 32px;
    background: url("~@/assets/patrolKanban/<EMAIL>") no-repeat center;
    background-size: contain;
    display: flex;
    align-items: center;
    padding-left: 30px;
    font-size: 14px;
    font-family: Source Han Sans CN;
    font-weight: 500;
    color: #daeef9;
  }
  .content {
    width: 400px;
    height: 225px;
    background: url("~@/assets/patrolKanban/<EMAIL>") no-repeat center;
    background-size: contain;
    .description {
      width: 396px;
      height: 63px;
      background: rgba(0, 31, 70, 0.5);
      font-size: 12px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #53c2ff;
      line-height: 18px;
      box-sizing: border-box;
      padding: 18px;
      line-height: 25px;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;

    }
    .content-box {
      display: flex;
      width: 396px;
      height: 128px;
      background: rgba(0, 31, 70, 0.5);
      margin-top: 12px;
      box-sizing: border-box;
      padding: 18px;
      .image-box {
        width: 124px;
        height: 81px;
        background: url("~@/assets/patrolKanban/pic_kuang.png") no-repeat center;
        background-size: contain;
        flex-shrink: 0;
        margin-right: 20px;
      }
      .content_text {
        flex: 1;
        .item_text {
          display: flex;
          .label {
            width: 60px;
            font-size: 12px;
            font-family: PingFang SC;
            font-weight: 400;
            color: #c9dcea;
          }
          .value {
            font-size: 12px;
            font-family: PingFang SC;
            font-weight: 400;
            color: #c9dcea;
            flex: 1;
            // text-overflow: ellipsis;
            // overflow: hidden;
            // white-space: nowrap;
          }
        }
      }
    }
  }
}
</style>
