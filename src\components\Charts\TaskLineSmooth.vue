<template>
  <div :id="id" :style="{ height: height, width: width }" />
</template>

<script lang="ts">
import * as echarts from 'echarts'
import { Component, Prop, Watch } from "vue-property-decorator";
import { mixins } from "vue-class-component";
import ResizeMixin from "./mixins/resize";
@Component({
  name: "TaskLineSmooth"
})
export default class extends mixins(ResizeMixin) {
  @Prop({ default: "chart" }) private id!: string;
  @Prop({ default: "200px" }) private width!: string;
  @Prop({ default: "200px" }) private height!: string;
  @Prop({ required: true }) private propData!: any;
  @Watch("propData", { immediate: true, deep: true })
  public onMsgChanged(newValue: any, oldValue: any) {
    if (this.chart) {
      this.chart.clear();
      this.$nextTick(() => {
        this.initChart();
      });
    }
  }
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
  }
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.clear();
    this.chart.dispose();
    this.chart = null;
  }
  private initChart() {

    if(this.chart === null || this.chart === undefined) {
      this.chart = echarts.init(
        document.getElementById(this.id) as HTMLDivElement
      );
    }
    this.chart.setOption({
      title: {
        text: "当日事件处理数/当日事件生成数",
        textStyle: {
          fontSize: 12,
          color: "#fff"
        },
        right: 10
      },
      backgroundColor: "transparent",
      grid: {
        bottom: 50,
        left: 40,
        right: 20,
        top: 40
      },
      xAxis: {
        type: "category",
        boundaryGap: false,
        nameTextStyle: {
          color: "#fff"
        },
        data: this.propData.bottomList,
        axisLabel: {
          textStyle: {
            fontSize: 12,
            color: "white"
          },
          rotate: 45
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        splitLine: {
          show: false
        }
      },
      yAxis: {
        type: "value",
        min: 0,
        max: 100,
        axisLabel: {
          textStyle: {
            fontSize: 12,
            color: "white"
          },
          formatter: "{value}%" //刻度标签的内容格式器，支持字符串模板和回调函数两种形式，按照自己需求设置
        },
        axisLine: {
          show: true
        },
        axisTick: {
          show: false
        },
        splitLine: {
          show: false,
          lineStyle: {
            color: "#5D6A78"
          }
        }
      },
      tooltip: {
        show: true,
        trigger: "axis",
        formatter: "{b0}: {c0}%"
      },
      series: [
        {
          name: "事件处理率",
          data: this.propData.dataList,
          type: "line",
          lineStyle: {
            color: "#03DCBD" //改变折线颜色
          },
          // symbol: "none",
          // symbolSize: 0,
          smooth: true,
          itemStyle: {
            color: "#03DCBD", //改变折线点的颜色
            borderColor: "#03DCBD"
          },
          label: {
            normal: {
              show: true,
              position: "top",
              formatter: "{c}%",
              textStyle: {
                color: "#03C7B2",
                fontSize: 14
              }
            }
          },
          areaStyle: {
            color: {
              type: "linear",
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: "#05667A"
                },
                {
                  offset: 1,
                  color: "#05667A" // 100% 处的颜色
                }
              ],
              global: false
            }
          }
        }
      ]
    } as any);
  }
}
</script>
