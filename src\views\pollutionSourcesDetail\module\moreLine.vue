<template>
  <div id="Trend"></div>
</template>

<script>
import * as echarts from "echarts";
import "echarts-gl";
import dayjs from "dayjs";
export default {
  name: "cardBox",
  props: {
    AnalyzeNew: {
      type: Array,
      default: () => [],
    },
    isDay: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      // colorList: ['#2AAA90', '#28A1D9','#E7BE30'],
      colorList: ["#1BA2FF", "#22D0DD", "#53EFAF"],
      // colorListT: [
      //   {
      //     one:'#fff',
      //     two:'rgba(42,170,144,0.8)',
      //   },
      //   {
      //     one:'#fff',
      //     two:'rgba(40,161,217,0.1)',
      //   },
      //   {
      //     one:'#fff',
      //     two:'rgba(231,190,48,0.1)',
      //   },
      // ]
    };
  },
  watch: {
    AnalyzeNew: {
      handler(newVal) {
        // this.creatEharts(Array.from(newVal[0].value), Array.from(newVal.vale))
        const list = this.getSeries(newVal);
        setTimeout(() => {
          this.creatEharts(
            Array.from(newVal[0].x),
            Array.from(newVal[0].unit),
            list
          );
        }, 100);
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {
    const list = this.getSeries(this.AnalyzeNew);
    setTimeout(() => {
      this.creatEharts(this.AnalyzeNew[0].x, this.AnalyzeNew[0].unit, list);
    }, 100);
  },
  methods: {
    creatEharts(xLabel, title, series) {
      let option = {};
      let myChart = echarts.init(document.getElementById("Trend"));
      let _this = this;
      option = {
        backgroundColor: "",
        color: this.colorList,
        tooltip: {
          trigger: "axis",
          axisPointer: { type: "cross" },
        }, // 点击显示详情
        //       tooltip: {
        //         trigger: 'axis',
        //         backgroundColor: 'transparent',
        //         axisPointer: {
        //           lineStyle: {
        //             color: {
        //               type: 'linear',
        //               x: 0,
        //               y: 0,
        //               x2: 0,
        //               y2: 1,
        //               colorStops: [
        //                 {
        //                   offset: 0,
        //                   color: 'rgba(126,199,255,0)', // 0% 处的颜色
        //                 },
        //                 {
        //                   offset: 0.5,
        //                   color: 'rgba(126,199,255,1)', // 100% 处的颜色
        //                 },
        //                 {
        //                   offset: 1,
        //                   color: 'rgba(126,199,255,0)', // 100% 处的颜色
        //                 },
        //               ],
        //               global: false, // 缺省为 false
        //             },
        //           },
        //         },
        //         formatter: (p) => {
        //           console.log(p)
        //           let dom = `<div style="width: 79px;
        // height: 50px;color:#fff;position: relative;">
        //       <svg style="position: absolute;top: 50%;
        //   left: 50%;
        //   transform: translateX(-50%) translateY(-50%);" class="svg" xmlns="http://www.w3.org/2000/svg" width="100" height="71" viewBox="0 0 84 55">
        //     <defs>
        //       <style>
        //         .cls-1 {
        //           fill: #07172c;
        //           fill-opacity: 0.8;
        //           stroke: #a7d8ff;
        //           stroke-linejoin: round;
        //           stroke-opacity: 0.2;
        //           stroke-width: 1px;
        //           fill-rule: evenodd;
        //         }

        //       </style>
        //     </defs>
        //     <path id="矩形_419" data-name="矩形 419" class="cls-1" d="M266,595h74v50H266V624.046L261,620l5-3.984V595Z"
        //       transform="translate(-258.5 -592.5)" />
        //   </svg>
        //       <div style="padding: 4px 8px 4px 14px;display: flex;
        //       justify-content: center;
        //       align-items: center;
        //       flex-direction: column;position: relative;z-index: 1;">
        //           <div style="margin-bottom: 4px;width:100%;display:${
        //             p[0] ? 'flex' : 'none'
        //           };justify-content:space-between;align-items:center;">
        //               <span style="font-size:14px;color:#7ec7ff;">${
        //                 p[0] ? p[0].seriesName : ''
        //               }</span>
        //               <span style="font-size:14px;color:#fff;">${
        //                 p[0] ? p[0].data.value : ''
        //               }</span>
        //           </div>
        //           <div style="width:100%;height:100%;display:${
        //             p[1] ? 'flex' : 'none'
        //           };justify-content:space-between;align-items:center;">
        //               <span style="font-size:14px;color:#7ec7ff;">${
        //                 p[1] ? p[1].seriesName : ''
        //               }</span>
        //               <span style="font-size:14px;color:#fff;">${
        //                 p[1] ? p[1].data.value : ''
        //               }</span>
        //           </div>
        //       </div>
        //   </div>`
        //           return dom
        //         },
        //       },
        legend: {
          show: true,
          type: "scroll",
          selectedMode: "single",
          top: "5%",
          right: "5%", //left:"10%"  // // 组件离容器的距离
          textStyle: {
            fontSize: 10,
            color: "#FFFFFF",
          },
        },
        // legend: {
        //   align: 'left',
        //   right: '10%',
        //   top: '10%',
        //   type: 'plain',
        //   textStyle: {
        //     color: '#7ec7ff',
        //     fontSize: 16,
        //   },
        //   // icon:'rect',
        //   itemGap: 25,
        //   itemWidth: 18,
        //   icon: 'path://M0 2a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v0a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2z',

        //   data: [
        //     {
        //       name: '上学',
        //     },
        //     {
        //       name: '放学',
        //     },
        //   ],
        // },
        grid: {
          top: "15%",
          left: "5%",
          right: "5%",
          bottom: "12%",
          // containLabel: true
        },
        xAxis: [
          {
            type: "category",
            boundaryGap: false,
            axisLine: {
              show: true,
              lineStyle: {
                color: "#385982",
                width: 1,
                type: "dashed",
              },
            },
            axisLabel: {
              show: true,
              fontSize: 12,
              color: "#B6D0D8",
              margin: 16,
              textStyle: {
                color: "#a4a7aa",
              },
              formatter: function(data) {
                return data;
              },
            },
            splitLine: {
              show: false,
              lineStyle: {
                color: "#192a44",
              },
            },
            axisTick: {
              show: false,
            },
            data: xLabel,
          },
        ],
        yAxis: [
          {
            name: title,
            nameTextStyle: {
              color: "#A6CBE2",
              fontSize: 12,
            },
            min: function(value) {
              return 0;
            },
            max: function(value) {
              return value.max;
            },
            scale: true,
            splitLine: {
              show: true,
              lineStyle: {
                color: "#385982",
                type: "dashed",
              },
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#233653",
              },
            },
            axisLabel: {
              show: true,
              textStyle: {
                color: "#B6D0D8",
              },
              formatter: function(value) {
                if (value === 0) {
                  return value;
                }
                return value;
              },
            },
            axisTick: {
              show: false,
            },
          },
        ],
        series: series,
        // series: [
        //   {
        //     name: this.isDay ? '昼间： ' : '夜间： ',
        //     type: 'line',
        //     symbol: 'emptyCircle', // 默认是空心圆（中间是白色的），改成实心圆
        //     showAllSymbol: true,
        //     symbolSize: 6,
        //     smooth: true,
        //     lineStyle: {
        //       normal: {
        //         width: 2,
        //         color: 'rgba(10,219,250,1)', // 线条颜色
        //       },
        //       borderColor: 'rgba(0,0,0,.4)',
        //     },
        //     itemStyle: {
        //       color: 'rgba(10,219,250,1)',
        //       borderColor: 'rgba(10,219,250,1)',
        //       borderWidth: 2,
        //     },
        //     tooltip: {
        //       show: true,
        //     },
        //     areaStyle: {
        //       //区域填充样式
        //       normal: {
        //         //线性渐变，前4个参数分别是x0,y0,x2,y2(范围0~1);相当于图形包围盒中的百分比。如果最后一个参数是‘true’，则该四个值是绝对像素位置。
        //         color: new echarts.graphic.LinearGradient(
        //           0,
        //           0,
        //           0,
        //           1,
        //           [
        //             {
        //               offset: 0,
        //               color: 'rgba(10,219,250,.3)',
        //             },
        //             {
        //               offset: 1,
        //               color: 'rgba(10,219,250, 0)',
        //             },
        //           ],
        //           false
        //         ),
        //         shadowColor: 'rgba(10,219,250, 0.5)', //阴影颜色
        //         shadowBlur: 20, //shadowBlur设图形阴影的模糊大小。配合shadowColor,shadowOffsetX/Y, 设置图形的阴影效果。
        //       },
        //     },
        //     data: goOutSchool,
        //   },
        // ],
      };
      if (option) {
        myChart.setOption(option);
      }
      myChart.on("legendselectchanged", function(obj) {
        //  legend点击事件
        _this.AnalyzeNew.forEach((x, i) => {
          if (x.name === obj.name) {
            option.yAxis[0].name = x.unit;
          }
        });
        myChart.setOption(option);
      });
    },
    getSeries(list) {
      let series1 = [];
      const series = list;
      // console.log(series,'1111111111111111111')
      series.forEach((x, index) => {
        let value = [];
        x.value.forEach((y, i) => {
          value.push({
            value: y,
            itemStyle: {
              // borderColor: #3BB66F'
              color: x.isQualified[i] == 1 ? "red" : this.colorList[index],
              // color:'red',
              // borderWidth: 6,
            },
          });
        });
        series1.push({
          name: x.name, // lend
          type: "line",
          // symbol: 'none',
          smooth: true, // 是否平滑
          symbolSize: 6,
          // areaStyle: {
          //   color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          //     {
          //       offset: 0,
          //       color: this.colorListT[index].two
          //     },
          //     {
          //       offset: 1,
          //       color: this.colorListT[index].one
          //     }
          //   ])
          // },
          emphasis: {
            focus: "series",
          },
          // data: [120, 132, 101, 134, 90, 230, 210]
          data: value,
        });
      });
      return series1;
    },
  },
};
</script>

<style lang="less">
#Trend {
  // height: 100%;
  width: 100%;
}
</style>
