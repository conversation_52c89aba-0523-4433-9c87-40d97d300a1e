<template>
  <div class="container" ref="container"></div>
</template>

<script lang="ts">
/* eslint-disable @typescript-eslint/ban-ts-ignore */
//@ts-ignore
// import AMap from "AMap";
import AMapLoader from '@amap/amap-jsapi-loader'
import { Vue, Component, Prop, Watch } from 'vue-property-decorator'
import { Icon } from 'ant-design-vue'

import carSelected from '@/assets/car-selected.png'
import offLine from '@/assets/ws-offline-s.png'
import stationIconPic from '@/assets/<EMAIL>'
import stationIconPicAlarm from '@/assets/-s-jk_gj.png'
import stationIconPicOnline from '@/assets/-s-jk_zx.png'
import districtRiver from '@/assets/jinniu-river4.png'
// import waterSatationNormal from "@/assets/<EMAIL>"; deprecated
import waterSatationNormal from '@/assets/ws-normal.png'
import waterSatationActive from '@/assets/ws-normal-s.png'
// import offlineWaterStation from "@/assets/offline-station.png"; deprecated
import offlineWaterStation from '@/assets/ws-offline.png'
import warningWaterStation from '@/assets/ws-warn.png'
import warningWaterStationSelect from '@/assets/ws-warn-s.png'
import drainlx from '@/assets/outlet/<EMAIL>' // 排口未排水
import drainyc from '@/assets/outlet/<EMAIL>'
import drainzc from '@/assets/outlet/<EMAIL>'
import drainPerson from '@/assets/outlet/<EMAIL>'
import { webglcontextlostHandle } from '@/utils/index'
import wrIcon from '@/assets/<EMAIL>'

interface MapCenter {
  lng: number
  lat: number
}
enum EnterType {
  AIR = 1,
  WATER = 2,
  VEHICLE = 3,
  HOME = 4,
  PAGEHOME = 5,
  POLLUTE = 6,
  TASK = 7,
  EMERGENCY = 8,
  EQUIPMENT = 9,
}

var AMap: any
// 地图容器
let maps: any = null

@Component({
  name: 'WaterMap',
  components: {
    AIcon: Icon,
  },
})
export default class extends Vue {
  // private maps: any = "";

  @Prop({
    required: false,
    type: Number,
    default: 12.5,
  })
  private mapZoom!: number
  @Prop({ required: false })
  mapStyle!: string
  @Prop({ required: false, default: '3D' }) viewMode!: string
  @Prop({ required: false, default: () => {} }) viewCenter!: MapCenter
  @Prop({ required: false, default: () => [] }) mapMarker!: any[]
  @Prop({ required: false, default: () => [] }) mapWaterStationsVideo!: any[]
  @Prop({ required: false, default: () => [] }) waterSourceMonitorDevices!: any[]
  @Prop({ required: false, default: () => [] }) drainMonitorDevices!: any[]
  @Prop({ required: false, default: () => [] }) buildingMarker!: Array<any>
  @Prop({ required: false, default: () => [] }) drainListData!: Array<any>
  @Prop({ required: false, default: () => [] }) drainPersonList!: Array<any>
  @Prop({ required: false, default: null }) currentSelectedStationMarker!: any
  @Prop({ required: false, default: () => {} }) currentSelectedStationObj!: any
  @Prop({ required: false, default: 1 }) enterType!: number | string
  @Prop({ required: false, default: true }) isShowDrain!: boolean
  @Prop({ required: false, default: true }) isShowWater!: boolean
  @Prop({ required: false, default: '' }) defaultDrain!: number | string
  @Prop({ required: false, default: () => {} }) currSelect!: any
  @Prop({ required: false, default: () => [] }) airData!: Array<any>
  @Prop({ required: false, default: true }) isShowMonitor!: boolean
  @Prop({ required: false, default: true }) isShowWaterSourceMonitor!: boolean
  @Prop({ required: false, default: true }) isShowDrainMonitor!: boolean
  @Watch('airData', { immediate: false, deep: true })
  private onAirData(newValue: any, oldValue: any) {
    if (newValue) {
    }
  }
  @Watch('mapWaterStationsVideo', { immediate: false, deep: true })
  private onMapWaterStationsVideo(newValue: any, oldValue: any) {
    if (newValue) {
      this.mapWaterStationsVideo = newValue
      this.createMapWaterStationsVideo()
    }
  }
  @Watch('isShowWaterSourceMonitor', { immediate: false, deep: true })
  private onIsShowWaterSourceMonitor(newValue: any, oldValue: any) {
    if (newValue) {
      this.createMapWaterStationsVideo()
    } else {
      // 清除水源地监控摄像头标记
      if (this.mapWaterStationsVideoList.length) {
        const waterSourceMarkers = this.mapWaterStationsVideoList.filter((marker: any) => {
          const stationData = marker.w && marker.w.data
          if (!stationData) return false
          return stationData.type === 1 || stationData.type === 2
        })
        if (waterSourceMarkers.length) {
          maps.remove(waterSourceMarkers)
          this.mapWaterStationsVideoList = this.mapWaterStationsVideoList.filter((marker: any) => !waterSourceMarkers.includes(marker))
        }
      }
    }
  }
  @Watch('isShowDrainMonitor', { immediate: false, deep: true })
  private onIsShowDrainMonitor(newValue: any, oldValue: any) {
    if (newValue) {
      this.createMapWaterStationsVideo()
    } else {
      // 清除入河排口监控摄像头标记
      if (this.mapWaterStationsVideoList.length) {
        const drainMarkers = this.mapWaterStationsVideoList.filter((marker: any) => {
          const stationData = marker.w && marker.w.data
          if (!stationData) return false
          return stationData.type === 3 || stationData.type === 4 || stationData.type === 5
        })
        if (drainMarkers.length) {
          maps.remove(drainMarkers)
          this.mapWaterStationsVideoList = this.mapWaterStationsVideoList.filter((marker: any) => !drainMarkers.includes(marker))
        }
      }
    }
  }
  @Watch('isShowDrain', { immediate: false, deep: true })
  private onisShowDrain(newValue: any, oldValue: any) {
    if (this.infoWindow && !this.isShowWater) {
      this.infoWindow.close()
      this.infoWindow = null
      maps.clearInfoWindow()
    }
    if (newValue) {
      this.createMapDrainStations(0)
      this.createMapDrainPersons()
    } else {
      if (this.drainMarkerList.length) {
        maps.remove(this.drainMarkerList)
        this.drainMarkerList = []
      }
      if (this.drainPersonMarkerList.length) {
        maps.remove(this.drainPersonMarkerList)
        this.drainPersonMarkerList = []
      }
    }
  }
  @Watch('isShowWater', { immediate: false, deep: true })
  private onisShowWater(newValue: any, oldValue: any) {
    if (this.infoWindow) {
      this.infoWindow.close()
      this.infoWindow = null
      maps.clearInfoWindow()
    }
    if (newValue) {
      this.currentSelectedStation = null
      this.createWaterSationMarker()
    } else {
      if (this.waterStationMarkerList.length) {
        maps.remove(this.waterStationMarkerList)
        this.waterStationMarkerList = []
      }
      if (this.isShowDrain) {
        this.createMapDrainStations(0)
        this.createMapDrainPersons()
      }
    }
  }
  @Watch('isShowMonitor', { immediate: false, deep: true })
  private onisShowMonitor(newValue: any, oldValue: any) {
    if (newValue) {
      this.createMapWaterStationsVideo()
    } else {
      if (this.mapWaterStationsVideoList.length) {
        maps.remove(this.mapWaterStationsVideoList)
        this.mapWaterStationsVideoList = []
      }
      if (this.videoActiveMarker !== null) {
        maps.remove(this.videoActiveMarker)
        this.videoActiveMarker = null
      }
    }
  }
  @Watch('defaultDrain', { immediate: false, deep: true })
  private ondefaultDrain(newValue: any, oldValue: any) {
    this.currDrainId = newValue
    if (this.isShowDrain) {
      setTimeout(() => {
        this.createMapDrainStations(0)
      }, 2000)
    }
  }
  @Watch('currSelect', { immediate: false, deep: true })
  private oncurrSelect(newValue: any, oldValue: any) {
    if (newValue && JSON.stringify(newValue) !== '{}') {
      if (newValue.type == 2) {
        const marker = this.drainMarkerList.find((item: any) => {
          const data = item.w.data
          if (data.waterDrainId == newValue.waterDrainId) {
            return item
          }
        })
        this.handleDrainMarker(marker)
      } else {
        const marker = this.drainPersonMarkerList.find((item: any) => {
          const data = item.w.data
          if (data.merchantId == newValue.id) {
            return item
          }
        })
        this.showPersonData(marker.w.data)
      }
    }
  }
  @Watch('drainListData', { immediate: false, deep: true })
  private onDrainListData(newValue: any, oldValue: any) {
    if (newValue) {
      this.drainListData = newValue
      setTimeout(() => {
        this.createMapDrainStations(0)
      }, 2000)
    }
  }
  @Watch('drainPersonList', { immediate: false, deep: true })
  private ondrainPersonList(newValue: any, oldValue: any) {
    if (newValue) {
      this.drainPersonList = newValue
      setTimeout(() => {
        this.createMapDrainPersons()
      }, 2000)
    }
  }

  // 水质选中
  @Watch('currentSelectedStationMarker', { immediate: false, deep: true })
  private oncurrentSelectedStationMarker(newValue: any, oldValue: any) {
    if (newValue) {
      this.currentSelectedStation = newValue
      this.createWaterSationMarker()
    }
  }

  // 水质弹框
  @Watch('currentSelectedStationObj', { immediate: false, deep: true })
  private oncurrentSelectedStationObj(newValue: any, oldValue: any) {
    if (newValue) {
      this.showInfo(newValue)
    }
  }

  private text: any = null
  private enterpriseMarker: any = null
  // 水质监测MarkerList
  private waterStationMarkerList: any[] = []
  // 当前选中的水质站点
  private currentSelectedStation: any = null

  // 水质监测点Icon
  private iconObj: any = {}
  created() {
    AMapLoader['reset']()
  }
  mounted() {
    // load 加载
    AMapLoader.load({
      key: '777fec7ef3cc29281d60ae900fa33925', // 申请好的Web端开发者Key，首次调用 load 时必填
      version: '1.4.15', // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
      plugins: [
        'AMap.DistrictSearch',
        'AMap.Heatmap',
        'AMap.ControlBar',
        'AMap.Object3DLayer',
        'Map3D',
        'AMap.Geocoder',
        'AMap.CircleMarker',
        'AMap.MouseTool',
      ], // 需要使用的的插件列表，如比例尺'AMap.Scale'等
      AMapUI: {
        // 是否加载 AMapUI，缺省不加载
        version: '1.0', // AMapUI 版本
        // "plugins":['overlay/SimpleMarker'],       // 需要加载的 AMapUI ui插件
      },
      Loca: {
        // 是否加载 Loca， 缺省不加载
        version: '1.3.2', // Loca 版本
      },
    })
      .then((amap) => {
        AMap = amap
        this.map()
        this.dynamicRivderLayer()
        maps.on('complete', () => {
          this.createOverlay()
          const waterCenterPosition = new AMap.LngLat(104.08, 30.725)
          maps.setCenter(waterCenterPosition)
          this.iconObj = {
            building: new AMap.Icon({
              // 图标尺寸
              // size: new AMap.Size(76, 48),
              size: new AMap.Size(90, 72),
              // 图标的取图地址
              image: waterSatationNormal,
              // 图标所用图片大小
              // imageSize: new AMap.Size(28, 35),
              imageSize: new AMap.Size(90, 72),
              // 图标取图偏移量
              imageOffset: new AMap.Pixel(0, 0),
            }),
            stationIconAtive: new AMap.Icon({
              // 图标尺寸
              size: new AMap.Size(90, 72),
              // 图标的取图地址
              image: waterSatationActive,
              // 图标所用图片大小
              imageSize: new AMap.Size(90, 72),
              // 图标取图偏移量
              imageOffset: new AMap.Pixel(0, 0),
            }),
            offlineWaterStationIcon: new AMap.Icon({
              // 图标尺寸
              size: new AMap.Size(90, 72),
              // 图标的取图地址
              image: offlineWaterStation,
              // 图标所用图片大小
              imageSize: new AMap.Size(90, 72),
              // 图标取图偏移量
              imageOffset: new AMap.Pixel(0, 0),
            }),
            warningWaterStationIcon: new AMap.Icon({
              // 图标尺寸
              size: new AMap.Size(90, 72),
              // 图标的取图地址
              image: warningWaterStation,
              // 图标所用图片大小
              imageSize: new AMap.Size(90, 72),
              // 图标取图偏移量
              imageOffset: new AMap.Pixel(0, 0),
            }),
            warningWaterStationSelectIcon: new AMap.Icon({
              // 图标尺寸
              size: new AMap.Size(90, 72),
              // 图标的取图地址
              image: warningWaterStationSelect,
              // 图标所用图片大小
              imageSize: new AMap.Size(90, 72),
              // 图标取图偏移量
              imageOffset: new AMap.Pixel(0, 0),
            }),
            offLineIcon: new AMap.Icon({
              // 图标尺寸
              size: new AMap.Size(90, 72),
              // 图标的取图地址
              image: offLine,
              // 图标所用图片大小
              imageSize: new AMap.Size(90, 72),
              // 图标取图偏移量
              imageOffset: new AMap.Pixel(0, 0),
            }),
            sxt: new AMap.Icon({
              // 图标尺寸
              // size: new AMap.Size(76, 48),
              size: new AMap.Size(28, 28),
              // 图标的取图地址
              image: stationIconPic,
              // 图标所用图片大小
              // imageSize: new AMap.Size(28, 35),
              imageSize: new AMap.Size(28, 28),
              // 图标取图偏移量
              imageOffset: new AMap.Pixel(0, 0),
            }),
          }
          this.createMapWaterStationsVideo()
          this.createAirData()
          this.createWaterSationMarker()
          this.createMapDrainStations(0)
        })
      })
      .catch((e) => {
        console.log(e)
      })
    // 水详情
    ;(window as any).toAirDetails = (data: any, type: number) => {
      //@ts-ignore
      // eslint-disable-next-line no-undef
      _this.$router.push({
        //@ts-ignore
        // eslint-disable-next-line no-undef
        path: '/waterDetails',
        // path: "/details",
        query: {
          data,
          type,
          //@ts-ignore
          // eslint-disable-next-line no-undef
          stationCode: this.stationCode,
        },
      })
    }

    // 排口详情
    ;(window as any).toDrainDetails = () => {
      //@ts-ignore
      // eslint-disable-next-line no-undef
      _this.$router.push({
        //@ts-ignore
        // eslint-disable-next-line no-undef
        path: '/waterOutlet',
        query: {
          //@ts-ignore
          // eslint-disable-next-line no-undef
          currDrainId: this.currDrainId,
        },
      })
    }

    // 排口告警详情
    ;(window as any).toAlarmDetails = () => {
      this.$emit('alarmDetail', this.currDrain)
    }

    // 关闭地图信息弹框
    ;(window as any).closeInfoWindow = () => {
      //@ts-ignore
      // eslint-disable-next-line no-undef
      if (maps) {
        //@ts-ignore
        // eslint-disable-next-line no-undef
        maps.clearInfoWindow()
      }
      //@ts-ignore
      // eslint-disable-next-line no-undef
      if (this.enterType === 2) {
        //@ts-ignore
        if (this.isShowWater) {
          // eslint-disable-next-line no-undef
          this.currentSelectedStation = null
          //@ts-ignore
          // eslint-disable-next-line no-undef
          this.createWaterSationMarker()
        }
        //@ts-ignore
        // eslint-disable-next-line no-undef
        if (this.isShowDrain) {
          // this.currDrainId = ''
          this.currDrainMarker = ''
          this.createMapDrainStations(1)
          this.createMapDrainPersons()
        }
      }
    }
    ;(window as any)._this = this
  }

  beforeDestroy() {
    if (!maps) return
    maps.clearMap()

    // 清除水质环境相关内容
    if (this.waterStationMarkerList && this.waterStationMarkerList.length > 0) {
      maps.remove(this.waterStationMarkerList)
      this.waterStationMarkerList = []
    }

    if (this.text !== null) {
      maps.remove(this.text)
      maps.remove(this.enterpriseMarker)
      this.text = null
      this.enterpriseMarker = null
    }
    // 销毁地图
    maps.destroy()
    maps = null

    // 销毁全局挂载事件
    ;(window as any).toAirDetails = null
    ;(window as any).toDrainDetails = null
    ;(window as any).toAlarmDetails = null
    ;(window as any).closeInfoWindow = null
    ;(window as any)._this = null
    AMap = null
  }

  // 高德地图
  private map(): void {
    // 初始化地图
    maps = new AMap.Map(this.$refs.container, {
      center: [104.05, 30.73],
      position: [104.05, 30.73],
      zoom: this.mapZoom,
      viewMode: this.viewMode,
      pitch: 0,
      zoomEnable: true,
      dragEnable: true,
      zooms: [12, 18],
    })

    // 处理webgl上下文丢失事件
    webglcontextlostHandle.call(this)

    // 设置地图样式
    maps.setMapStyle(this.mapStyle)
    // maps.setMapStyle('amap://styles/0739a6632d849ae1b5e6f1c52f7d12c3')
  }

  // 添加行政区外的覆盖物
  private createOverlay() {
    const map = maps
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const that = this
    // 添加金牛区地理信息数据 3
    new AMap.DistrictSearch({
      extensions: 'all',
      subdistrict: 0,
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    }).search('金牛区', function (status: any, result: any) {
      // 外多边形坐标数组和内多边形坐标数组
      const outer = [new AMap.LngLat(-360, 90, true), new AMap.LngLat(-360, -90, true), new AMap.LngLat(360, -90, true), new AMap.LngLat(360, 90, true)]
      const holes = result.districtList[0].boundaries

      const pathArray: any = [outer]
      // eslint-disable-next-line prefer-spread
      pathArray.push.apply(pathArray, holes)
      const polygon = new AMap.Polygon({
        pathL: pathArray,
        //线条颜色，使用16进制颜色代码赋值。默认值为#006600
        strokeColor: 'rgb(255,255,255)',
        strokeWeight: 0,
        //轮廓线透明度，取值范围[0,1]，0表示完全透明，1表示不透明。默认为0.9
        strokeOpacity: 0,
        //多边形填充颜色，使用16进制颜色代码赋值，如：#FFAA00
        fillColor: 'rgba(3,4,130)',
        // fillColor: "rgba(4,20,50)",
        // fillColor: "#0A1C5F",
        //多边形填充透明度，取值范围[0,1]，0表示完全透明，1表示不透明。默认为0.9
        fillOpacity: 0,
        //轮廓线样式，实线:solid，虚线:dashed
        strokeStyle: 'solid',
        strokeDasharray: [10, 2, 10],
      })
      polygon.setPath(pathArray)
      map.add(polygon)
    })
  }

  // marker的点击事件
  private stationCode: any
  private infoWindow: any
  private async showInfo(data: any): Promise<any> {
    if (this.currDrainMarker && this.isShowDrain) {
      // this.currDrainId = null
      // this.currDrainMarker = null
      this.createMapDrainStations(0)
    }
    let details: any
    if (data.target) {
      // 鼠标滑过事件
      details = data.target.w.data
    } else {
      // 定时函数触发
      details = data.w.data
    }
    if (JSON.stringify(details) === '{}') {
      return
    }
    // if(Number(this.enterType) === EnterType.AIR){
    // }
    this.stationCode = details.stationId
    // if (details.online === 0) {
    //   message.warning("站点处于离线状态！", 3);
    //   this.currentSelectedStation = null;
    //   this.createWaterSationMarker();
    //   return;
    // }
    this.currentSelectedStation = details.stationId
    this.$emit('waterStationSelectChange', details.stationId)
    this.createWaterSationMarker()

    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const that: any = this
    const map = maps
    //在指定位置打开信息窗体
    function openInfo() {
      //构建信息窗体中显示的内容
      let html: Array<string> = []

      // 水质监测
      // <div class="water-top-type">
      //         <div class="dian"></div>
      //         <div>
      //           水质分类：暂无
      //         </div>
      //       </div>

      const waterTime = details.time !== '' ? details.time.slice(5, 16) : '-'
      html = [
        `<div class="water-content">
        <img src="https://oss-chengdu.vankeytech.com:9000/vankeytech-ep/sys/shanchu3.png" alt class="closeInfoWindow" onclick="closeInfoWindow()"/>
        <div class="water-top">
          <div class="water-top-top">
            <div class="water-top-name">
              <div class="water-top-name1">${details.stationName}</div>
              <div class="water-top-state" style="background:${details.online ? '#22B331' : '#a8a8a8'};">${details.online ? '在线' : '离线'}</div>
            </div>
            <div class="update-time">${waterTime}</div>
          </div>
        </div>
        <div class="water-bottom">
          <div>
            <div style="width:15%;">
              <p>PH</p>
              <p style="color: ${details.phAlarm ? 'red' : ''}">${details.ph ? details.ph : ' - '}</p>
            </div>
            <div>
              <p>氨氮(mg/L)</p>
              <p style="color: ${details.nh3nAlarm ? 'red' : ''}">${details.nh3n ? details.nh3n : ' - '}</p>
            </div>
            <div>
              <p>总磷(mg/L)</p>
              <p style="color: ${details.totalPhosphorusAlarm ? 'red' : ''}">${details.totalPhosphorus ? details.totalPhosphorus : ' - '}</p>
            </div>
            <div>
              <p>COD(mg/L)</p>
              <p style="color: ${details.codAlarm ? 'red' : ''}">${details.cod ? details.cod : ' - '}</p>
            </div>
          </div>
          <div class="water-bottom-btn" onclick="toAirDetails(11,2)">查看详情</div>;
        </div>
      </div>`,
      ]

      that.infoWindow = new AMap.InfoWindow({
        // anchor: "bottom-center",
        offset: new AMap.Pixel(0, -60),
        isCustom: true,
        content: html.join(''), //使用默认信息窗体框样式，显示信息内容
      })

      if (data.target) {
        that.infoWindow.open(map, [data.target.w.data.lng, data.target.w.data.lat])
      } else {
        that.infoWindow.open(map, [data.w.data.lng, data.w.data.lat])
      }
    }
    openInfo()

    // setTimeout(() => {
    //   this.infoWindow.close();
    //   this.infoWindow = null;
    //   maps.clearInfoWindow();
    //   this.currentSelectedStation = null;
    //   this.createWaterSationMarker();
    // }, 60 * 1000);
  }

  private mapWaterStationsVideoList: any = []
  private videoActiveMarker: any = null
  // 排口站点
  private drainMarkerList: any = []
  private currDrainId: any = ''
  private currDrain: any = null
  private currDrainMarker: any = ''
  private drainMarkerClick(id: any) {
    const drainzcIcon = new AMap.Icon({
      // 图标尺寸
      size: new AMap.Size(54, 62),
      // 图标的取图地址
      image: drainzc,
      // a.amap.com/jsapi_demos/static/demo-center/icons/dir-marker.png
      // 图标所用图片大小
      imageSize: new AMap.Size(54, 62),
      // 图标取图偏移量
      imageOffset: new AMap.Pixel(0, 0),
    })
    const drainycIcon = new AMap.Icon({
      // 图标尺寸
      size: new AMap.Size(54, 62),
      // 图标的取图地址
      image: drainyc,
      // a.amap.com/jsapi_demos/static/demo-center/icons/dir-marker.png
      // 图标所用图片大小
      imageSize: new AMap.Size(54, 62),
      // 图标取图偏移量
      imageOffset: new AMap.Pixel(0, 0),
    })
    const drainXzcIcon = new AMap.Icon({
      // 图标尺寸
      size: new AMap.Size(64, 76),
      // 图标的取图地址
      image: drainzc,
      // a.amap.com/jsapi_demos/static/demo-center/icons/dir-marker.png
      // 图标所用图片大小
      imageSize: new AMap.Size(64, 76),
      // 图标取图偏移量
      imageOffset: new AMap.Pixel(0, 0),
    })
    const drainXycIcon = new AMap.Icon({
      // 图标尺寸
      size: new AMap.Size(64, 76),
      // 图标的取图地址
      image: drainyc,
      // a.amap.com/jsapi_demos/static/demo-center/icons/dir-marker.png
      // 图标所用图片大小
      imageSize: new AMap.Size(64, 76),
      // 图标取图偏移量
      imageOffset: new AMap.Pixel(0, 0),
    })
    const drainlxIcon = new AMap.Icon({
      // 图标尺寸
      size: new AMap.Size(69.6, 80.4),
      // 图标的取图地址
      image: drainlx,
      // a.amap.com/jsapi_demos/static/demo-center/icons/dir-marker.png
      // 图标所用图片大小
      imageSize: new AMap.Size(47, 70),
      // 图标取图偏移量
      imageOffset: new AMap.Pixel(0, 0),
    })
    let marker: any = ''
    let data: any = ''
    this.drainMarkerList.forEach((item: any) => {
      let datas = item.w.data
      if (datas.waterDrainId === id) {
        marker = item
        data = item.w.data
      }
    })
    marker.setIcon(data.isAlarm ? drainXycIcon : drainXzcIcon)
    if (marker !== this.currDrainMarker) {
      if (this.currDrainMarker) {
        const item = this.drainMarkerList.find((item: any) => item === this.currDrainMarker)
        const datas = item.w.data
        this.currDrainMarker.setIcon(datas.isAlarm ? drainycIcon : drainzcIcon)
      }
      this.currDrainMarker = marker
      this.currDrainId = data.waterDrainId
      this.showDrainInfo(data)
    }
  }
  private drainPersonMarkerList: any = []
  private createMapDrainPersons() {
    const drainPersonIcon = new AMap.Icon({
      // 图标尺寸
      size: new AMap.Size(27, 38),
      // 图标的取图地址
      image: drainPerson,
      // a.amap.com/jsapi_demos/static/demo-center/icons/dir-marker.png
      // 图标所用图片大小
      imageSize: new AMap.Size(27, 38),
      // 图标取图偏移量
      imageOffset: new AMap.Pixel(0, 0),
    })
    if (this.drainPersonMarkerList.length) {
      maps.remove(this.drainPersonMarkerList)
    }
    const map = maps
    this.drainPersonList.forEach((item: any, index: any) => {
      const marker = new AMap.Marker({
        map: map,
        position: new AMap.LngLat(item.merchantLng, item.merchantLat),
        icon: drainPersonIcon,
        offset: new AMap.Pixel(-27, -31),
        data: item,
      })
      this.drainPersonMarkerList.push(marker)
      AMap.event.addListener(marker, 'click', (e: any) => {
        const data = e.target.w.data
        this.showPersonData(data)
      })
    })
  }
  private showPersonData(details: any) {
    if (this.infoWindow) {
      this.infoWindow.close()
      this.infoWindow = null
      maps.clearInfoWindow()
      this.currDrainId = ''
      this.currDrainMarker = ''
      this.createMapDrainStations(0)
      if (this.isShowWater) {
        this.currentSelectedStation = null
        this.createWaterSationMarker()
      }
      if (this.isShowMonitor) {
        this.createMapWaterStationsVideo()
      }
    }
    this.$emit('drainPersonSelect', details.merchantId)
    let that = this
    function openInfo() {
      //构建信息窗体中显示的内容
      let html: Array<string> = []
      html = [
        `<div class="drain-person-content">
        <img src="https://oss-chengdu.vankeytech.com:9000/vankeytech-ep/sys/shanchu3.png" alt class="closeInfoWindow" onclick="closeInfoWindow()"/>
        <div class="name">${details.merchantName ? details.merchantName : '--'}</div>
        <div class="list">
            <span>指纹编号：</span>
            <span>${details.merchantNumber ? details.merchantNumber : '--'}</span>
        </div>
        <div class="list">
            <span>排户类型：</span>
            <span>${details.merchantTypeName ? details.merchantTypeName : '--'}</span>
        </div>
        <div class="list">
            <span>排水类型：</span>
            <span>${details.drainType ? details.drainType : '--'}</span>
        </div>
      </div>`,
      ]

      that.infoWindow = new AMap.InfoWindow({
        // anchor: "bottom-center",
        offset: new AMap.Pixel(-8, -8),
        isCustom: true,
        content: html.join(''), //使用默认信息窗体框样式，显示信息内容
      })

      that.infoWindow.open(maps, [details.merchantLng, details.merchantLat])
    }
    openInfo()
  }
  private createMapDrainStations(params: any) {
    const drainzcIcon = new AMap.Icon({
      // 图标尺寸
      size: new AMap.Size(58, 67),
      // 图标的取图地址
      image: drainzc,
      // a.amap.com/jsapi_demos/static/demo-center/icons/dir-marker.png
      // 图标所用图片大小
      imageSize: new AMap.Size(58, 67),
      // 图标取图偏移量
      imageOffset: new AMap.Pixel(0, 0),
    })
    const drainycIcon = new AMap.Icon({
      // 图标尺寸
      size: new AMap.Size(58, 67),
      // 图标的取图地址
      image: drainyc,
      // a.amap.com/jsapi_demos/static/demo-center/icons/dir-marker.png
      // 图标所用图片大小
      imageSize: new AMap.Size(58, 67),
      // 图标取图偏移量
      imageOffset: new AMap.Pixel(0, 0),
    })
    const drainXzcIcon = new AMap.Icon({
      // 图标尺寸
      size: new AMap.Size(69.6, 80.4),
      // 图标的取图地址
      image: drainzc,
      // a.amap.com/jsapi_demos/static/demo-center/icons/dir-marker.png
      // 图标所用图片大小
      imageSize: new AMap.Size(69.6, 80.4),
      // 图标取图偏移量
      imageOffset: new AMap.Pixel(0, 0),
    })
    const drainXycIcon = new AMap.Icon({
      // 图标尺寸
      size: new AMap.Size(69.6, 80.4),
      // 图标的取图地址
      image: drainyc,
      // a.amap.com/jsapi_demos/static/demo-center/icons/dir-marker.png
      // 图标所用图片大小
      imageSize: new AMap.Size(69.6, 80.4),
      // 图标取图偏移量
      imageOffset: new AMap.Pixel(0, 0),
    })
    const drainlxIcon = new AMap.Icon({
      // 图标尺寸
      size: new AMap.Size(69.6, 80.4),
      // 图标的取图地址
      image: drainlx,
      // a.amap.com/jsapi_demos/static/demo-center/icons/dir-marker.png
      // 图标所用图片大小
      imageSize: new AMap.Size(47, 70),
      // 图标取图偏移量
      imageOffset: new AMap.Pixel(0, 0),
    })
    const map = maps
    if (this.drainMarkerList.length > 0) {
      map.remove(this.drainMarkerList)
      this.drainMarkerList = []
    }
    if (this.currDrainMarker) {
      this.currDrainMarker = null
    }
    this.drainListData.forEach((item: any, index: any) => {
      const marker = new AMap.Marker({
        map: map,
        position: new AMap.LngLat(item.lng, item.lat),
        icon: item.isAlarm ? drainycIcon : drainzcIcon,
        offset: new AMap.Pixel(-29, -33.5),
        data: item,
      })
      if (this.currDrainId == item.waterDrainId && !this.isShowWater && this.isShowDrain && params !== 1) {
        marker.setIcon(item.isAlarm ? drainXycIcon : drainXzcIcon)
        marker.setOffset(new AMap.Pixel(-35, -40))
        this.currDrainMarker = marker
        this.showDrainInfo(item)
      }
      this.drainMarkerList.push(marker)
      AMap.event.addListener(marker, 'click', this.handleDrainMarker)
    })
  }
  private handleDrainMarker(e: any) {
    const drainzcIcon = new AMap.Icon({
      // 图标尺寸
      size: new AMap.Size(58, 67),
      // 图标的取图地址
      image: drainzc,
      // a.amap.com/jsapi_demos/static/demo-center/icons/dir-marker.png
      // 图标所用图片大小
      imageSize: new AMap.Size(58, 67),
      // 图标取图偏移量
      imageOffset: new AMap.Pixel(0, 0),
    })
    const drainycIcon = new AMap.Icon({
      // 图标尺寸
      size: new AMap.Size(58, 67),
      // 图标的取图地址
      image: drainyc,
      // a.amap.com/jsapi_demos/static/demo-center/icons/dir-marker.png
      // 图标所用图片大小
      imageSize: new AMap.Size(58, 67),
      // 图标取图偏移量
      imageOffset: new AMap.Pixel(0, 0),
    })
    const drainXzcIcon = new AMap.Icon({
      // 图标尺寸
      size: new AMap.Size(69.6, 80.4),
      // 图标的取图地址
      image: drainzc,
      // a.amap.com/jsapi_demos/static/demo-center/icons/dir-marker.png
      // 图标所用图片大小
      imageSize: new AMap.Size(69.6, 80.4),
      // 图标取图偏移量
      imageOffset: new AMap.Pixel(0, 0),
    })
    const drainXycIcon = new AMap.Icon({
      // 图标尺寸
      size: new AMap.Size(69.6, 80.4),
      // 图标的取图地址
      image: drainyc,
      // a.amap.com/jsapi_demos/static/demo-center/icons/dir-marker.png
      // 图标所用图片大小
      imageSize: new AMap.Size(69.6, 80.4),
      // 图标取图偏移量
      imageOffset: new AMap.Pixel(0, 0),
    })
    const drainlxIcon = new AMap.Icon({
      // 图标尺寸
      size: new AMap.Size(69.6, 80.4),
      // 图标的取图地址
      image: drainlx,
      // a.amap.com/jsapi_demos/static/demo-center/icons/dir-marker.png
      // 图标所用图片大小
      imageSize: new AMap.Size(47, 70),
      // 图标取图偏移量
      imageOffset: new AMap.Pixel(0, 0),
    })
    let data = null,
      marker = null
    if (e.target) {
      data = e.target.w.data
      marker = e.target
    } else {
      marker = e
      data = e.w.data
    }
    marker.setIcon(data.isAlarm ? drainXycIcon : drainXzcIcon)
    marker.setOffset(new AMap.Pixel(-35, -40))
    if (marker !== this.currDrainMarker) {
      if (this.currDrainMarker) {
        const item = this.drainMarkerList.find((item: any) => item === this.currDrainMarker)
        const datas = item.w.data
        this.currDrainMarker.setIcon(datas.isAlarm ? drainycIcon : drainzcIcon)
      }
      this.currDrainMarker = marker
      this.currDrainId = data.waterDrainId
      this.currDrain = data
      this.showDrainInfo(data)
    }
  }
  private showDrainInfo(details: any) {
    if (this.infoWindow) {
      this.infoWindow.close()
      this.infoWindow = null
      maps.clearInfoWindow()
      if (this.isShowWater) {
        this.currentSelectedStation = null
        this.createWaterSationMarker()
      }
      if (this.isShowMonitor) {
        this.createMapWaterStationsVideo()
      }
    }
    this.$emit('drainSelect', this.currDrain)
    let that = this
    //在指定位置打开信息窗体
    function openInfo() {
      //构建信息窗体中显示的内容
      let html: Array<string> = []
      html = [
        `<div class="drain-content">
        <img src="https://oss-chengdu.vankeytech.com:9000/vankeytech-ep/sys/shanchu3.png" alt class="closeInfoWindow" onclick="closeInfoWindow()"/>
        <div class="drin-name">排口编号：${details.drainName}</div>
        <div class="drain-sub-name">
           <div class="letf ${details.isAlarm ? 'letf-err' : ''}">${
             details.drainMonitorRecord.isDrain === 0 ? '未排水' : details.isAlarm ? '有异变量' : '无异变量'
           }</div>
           <div class="right">${details.updateTime ? details.updateTime.substr(0, details.updateTime.length - 3) : '--'}</div>
        </div>
        <div class="params-area">
            <div class="params-list">
                <span>液位</span>
                <span>${details.drainMonitorRecord && details.drainMonitorRecord.waterDepth ? details.drainMonitorRecord.waterDepth : '--'}
                <span class="unit">${details.drainMonitorRecord && details.drainMonitorRecord.waterDepth ? 'cm' : ''}</span></span>
            </div>
             <div class="params-list">
                <span>电导率</span>
                <span class="${details.drainMonitorRecord && details.drainMonitorRecord.conductivityIsAlarm ? 'err' : '--'}">${
                  details.drainMonitorRecord && details.drainMonitorRecord.conductivity ? details.drainMonitorRecord.conductivity : '--'
                }
                <span class="unit">${details.drainMonitorRecord && details.drainMonitorRecord.conductivity ? 'us/cm' : ''}</span></span>
            </div>
             <div class="params-list">
                <span>氨氮</span>
                <span class="${details.drainMonitorRecord && details.drainMonitorRecord.ammoniaNitrogenIsAlarm ? 'err' : '--'}">${
                  details.drainMonitorRecord && details.drainMonitorRecord.ammoniaNitrogen ? details.drainMonitorRecord.ammoniaNitrogen : '--'
                }
                <span class="unit">${details.drainMonitorRecord && details.drainMonitorRecord.ammoniaNitrogen ? 'mg/L' : ''}</span></span>
            </div>
             <div class="params-list">
                <span>降雨强度</span>
                <span>${details.drainMonitorRecord && details.drainMonitorRecord.rainfall ? details.drainMonitorRecord.rainfall : 0}
                <span class="unit">mm/min</span></span>
            </div>
             <div class="params-list">
                <span>温度</span>
                <span>${details.drainMonitorRecord && details.drainMonitorRecord.temperature ? details.drainMonitorRecord.temperature : '--'}
                <span class="unit">${details.drainMonitorRecord && details.drainMonitorRecord.temperature ? '℃' : ''}</span></span>
            </div>
             <div class="params-list">
                <span>TVOC</span>
                <span>${details.drainMonitorRecord && details.drainMonitorRecord.tvoc ? details.drainMonitorRecord.tvoc : '--'}
                <span class="unit">${details.drainMonitorRecord && details.drainMonitorRecord.tvoc ? 'ppm' : ''}</span></span>
            </div>
        </div>
        <footer style="display: flex; gap: 20px;justify-content: center;">
          <div class="btn" onclick="toDrainDetails()">查看详情</div>
         <div class="btn" onclick="toAlarmDetails()">告警信息</div>
        </footer>
      </div>`,
      ]

      that.infoWindow = new AMap.InfoWindow({
        // anchor: "bottom-center",
        offset: new AMap.Pixel(0, -12),
        isCustom: true,
        content: html.join(''), //使用默认信息窗体框样式，显示信息内容
      })

      that.infoWindow.open(maps, [details.lng, details.lat])
    }
    openInfo()
  }
  private airMarker: any = []
  // 无人机监控
  private createAirData() {
    const map = maps
    if (this.airMarker && this.airMarker.length > 0) {
      map.remove(this.airMarker)
      this.airData = []
    }
    const icon = new AMap.Icon({
      // 图标尺寸
      size: new AMap.Size(58, 58),
      // 图标的取图地址
      image: wrIcon,
      // 图标所用图片大小
      imageSize: new AMap.Size(58, 58),
      // 图标取图偏移量
      imageOffset: new AMap.Pixel(0, 0),
    })
    const marker = new AMap.Marker({
      position: new AMap.LngLat(this.airData[0].lng, this.airData[0].lat),
      icon: icon,
      offset: new AMap.Pixel(-11.9, -14.7),
    })
    this.airMarker.push(marker)
    marker.setMap(map)
    AMap.event.addListener(marker, 'click', (data: any) => {
      this.$emit('airVideo')
    })
  }
  // 水质监控
  private createMapWaterStationsVideo() {
    const map = maps
    if (this.mapWaterStationsVideoList && this.mapWaterStationsVideoList.length > 0) {
      map.remove(this.mapWaterStationsVideoList)
      this.mapWaterStationsVideoList = []
    }
    if (this.videoActiveMarker !== null) {
      map.remove(this.videoActiveMarker)
    }

    // 如果不显示监控，直接返回
    if (!this.isShowMonitor) {
      return
    }

    // 水质监测点Icon
    if (!this.mapWaterStationsVideo) return
    this.mapWaterStationsVideo.forEach((waterSation: any) => {
      // 根据监控类型控制显示
      const isWaterSource = waterSation.type === 1 || waterSation.type === 2
      const isDrain = waterSation.type === 3 || waterSation.type === 4 || waterSation.type === 5

      // 如果是水源地监控设备但水源地监控未开启，则跳过
      if (isWaterSource && !this.isShowWaterSourceMonitor) {
        return
      }

      // 如果是入河排口监控设备但入河排口监控未开启，则跳过
      if (isDrain && !this.isShowDrainMonitor) {
        return
      }

      const isAlarm = waterSation.isAlarm

      // 动态控制是否添加告警效果类名
      const alarmClass = isAlarm ? 'amap-marker-alarm-pulse' : ''
      const pic = isAlarm ? stationIconPicAlarm : waterSation.online ? stationIconPicOnline : stationIconPic

      const m: any = new AMap.Marker({
        map: map,
        position: [waterSation.lng, waterSation.lat],
        data: waterSation,
        zIndex: 99999,
        content: `
        <div class="${alarmClass}" style="width: fit-content;height: fit-content;display: flex;transform: translateY(-100%)">
          <img src="${pic}" style="width: 28px; height: 28px;" />
        </div>`,
        offset: new AMap.Pixel(-14, -46),
        // anchor: 'bottom-center',
      })

      this.mapWaterStationsVideoList.push(m)
      const carActive = new AMap.Icon({
        // 图标尺寸
        size: new AMap.Size(36, 36),
        // 图标的取图地址
        image: carSelected,
        // a.amap.com/jsapi_demos/static/demo-center/icons/dir-marker.png
        // 图标所用图片大小
        imageSize: new AMap.Size(36, 36),
        // 图标取图偏移量
        imageOffset: new AMap.Pixel(0, 0),
      })

      AMap.event.addListener(m, 'click', (data: any) => {
        if (this.videoActiveMarker !== null) {
          map.remove(this.videoActiveMarker)
        }
        this.videoActiveMarker = new AMap.Marker({
          map: map,
          position: [m.getPosition().lng, m.getPosition().lat],
          icon: carActive,
          offset: new AMap.Pixel(-18, -36),
          autoRotation: true,
          zIndex: 99998,
        })

        if (data.target) {
          this.$emit('cameraChange', data.target.w.data)
        }
      })
    })
  }

  /**
   * 供外部组件调用: 传入监控id定位到当前marker
   * */
  private focusOnMarkerById(id: string): void {
    if (!id) return
    const marker = this.mapWaterStationsVideoList.find((item: any) => item.w.data.id === id)
    if (marker) {
      AMap.event.trigger(marker, 'click')
      maps.setFitView([marker], false)
    }
  }

  // 水质监测站Marker
  private createWaterSationMarker(): void {
    const map = maps
    if (this.waterStationMarkerList && this.waterStationMarkerList.length > 0) {
      map.remove(this.waterStationMarkerList)
      this.waterStationMarkerList = []
    }
    let waterSationMarker: any = null
    this.mapMarker.forEach((waterSation: any, i: number) => {
      let offsetMarker = new AMap.Pixel(-36, -72)
      // 连心桥 stationId: "20171207000002"
      // 高桥 stationId: "60681008"
      // 洞子口 stationId: "60681004"
      // 沙河大桥 stationId: "60681006"
      if (waterSation.stationId == '60681008') {
        // 高桥
        offsetMarker = new AMap.Pixel(-36, -82)
        console.log('高桥 ', waterSation)
        // waterSation.lat = "30.71883199";
      } else if (waterSation.stationId == '60681004') {
        // 洞子口
        offsetMarker = new AMap.Pixel(-36, -82)
        // waterSation.lng = "104.0569359";
        console.log('洞子口 ', waterSation)
      } else if (waterSation.stationId == '60681006') {
        // 沙河大桥
        offsetMarker = new AMap.Pixel(-36, -82)
        // waterSation.lat = "30.71908199";
        console.log('沙河大桥 ', waterSation)
      }
      const warnState = waterSation.codAlarm || waterSation.nh3nAlarm || waterSation.totalPhosphorusAlarm || waterSation.phAlarm
      if (this.currentSelectedStation && this.currentSelectedStation === waterSation.stationId && !warnState) {
        if (waterSation.online == 0) {
          waterSationMarker = new AMap.Marker({
            // map: map,
            icon: this.iconObj.offLineIcon,
            position: [waterSation.lng, waterSation.lat],
            offset: new AMap.Pixel(-36, -72),
            data: waterSation,
          })
        } else {
          waterSationMarker = new AMap.Marker({
            // map: map,
            icon: this.iconObj.stationIconAtive,
            position: [waterSation.lng, waterSation.lat],
            offset: new AMap.Pixel(-36, -72),
            data: waterSation,
          })
        }
      } else if (this.currentSelectedStation && this.currentSelectedStation === waterSation.stationId && warnState) {
        if (waterSation.online) {
          waterSationMarker = new AMap.Marker({
            // map: map,
            icon: this.iconObj.warningWaterStationSelectIcon,
            position: [waterSation.lng, waterSation.lat],
            offset: new AMap.Pixel(-36, -72),
            data: waterSation,
          })
        } else {
          waterSationMarker = new AMap.Marker({
            // map: map,
            icon: this.iconObj.offLineIcon,
            position: [waterSation.lng, waterSation.lat],
            offset: new AMap.Pixel(-36, -72),
            data: waterSation,
          })
        }
      } else if (warnState && waterSation.online) {
        waterSationMarker = new AMap.Marker({
          // map: map,
          icon: this.iconObj.warningWaterStationIcon,
          position: [waterSation.lng, waterSation.lat],
          offset: new AMap.Pixel(-36, -72),
          data: waterSation,
        })
      } else if (waterSation.online === 0) {
        waterSationMarker = new AMap.Marker({
          // map: map,
          icon: this.iconObj.offlineWaterStationIcon,
          position: [waterSation.lng, waterSation.lat],
          offset: new AMap.Pixel(-45, -72),
          data: waterSation,
        })
      } else {
        waterSationMarker = new AMap.Marker({
          // map: map,
          icon: this.iconObj.building,
          position: [waterSation.lng, waterSation.lat],
          offset: new AMap.Pixel(-45, -72),
          data: waterSation,
        })
      }
      waterSationMarker.setTitle(`${waterSation.stationName}`)
      if (this.currentSelectedStation && this.currentSelectedStation === waterSation.stationId) {
        waterSationMarker.setLabel({
          offset: new AMap.Pixel(0, 29), //设置文本标注偏移量
          content: `<div>${waterSation.stationName}</div>`, //设置文本标注内容
          direction: 'top', //设置文本标注方位
        })
      } else {
        waterSationMarker.setLabel({
          offset: new AMap.Pixel(0, 25), //设置文本标注偏移量
          content: `<div>${waterSation.stationName}</div>`, //设置文本标注内容
          direction: 'top', //设置文本标注方位
        })
      }
      this.waterStationMarkerList.push(waterSationMarker)
      waterSationMarker.setMap(map)
      AMap.event.addListener(waterSationMarker, 'click', this.showInfo)
    })
  }

  // 添加河流水系图层
  private dynamicRivderLayer() {
    // const bounds = new AMap.Bounds([103.9, 30.647], [104.1635, 30.823]);
    // const bounds = new AMap.Bounds([103.933, 30.647], [104.15, 30.805]);
    // const bounds = new AMap.Bounds([103.95, 30.65], [104.148, 30.808]);
    // const bounds = new AMap.Bounds([103.937, 30.661], [104.160, 30.811]); // 地图3
    const bounds = new AMap.Bounds([103.9363, 30.662957], [104.160597, 30.807361])
    const imageLayer = new AMap.ImageLayer({
      url: districtRiver, // districtRiver
      bounds: bounds,
      zooms: [3, 18],
      opacity: 1,
    })
    imageLayer.setMap(maps)
  }
}
</script>

<style lang="less">
@import url(./map.less);

/* 告警效果 */
@keyframes pulse {
  0% {
    transform: scale(0.5);
    opacity: 1;
  }
  70% {
    transform: scale(2);
    opacity: 0.3;
  }
  100% {
    transform: scale(2.5);
    opacity: 0;
  }
}

.amap-marker-alarm-pulse {
  position: relative;
}

.amap-marker-alarm-pulse::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 14px;
  width: 16px;
  height: 10px;
  margin-left: -8px;
  background-color: rgba(255, 0, 0, 0.6);
  border-radius: 50%;
  z-index: -1;
  animation: pulse 1.5s infinite;
}

/* 水质监测 */
.water-content {
  // background: rgba(2, 34, 98, 1);
  // border: 1px solid rgba(76, 143, 222, 1);
  opacity: 0.89;
  box-sizing: border-box;
  color: #00eaff;
  overflow: -Scroll;
  overflow-x: hidden;
  overflow-y: hidden;
  position: relative;
  // clip-path: polygon(4% 0, 96% 0, 100% 7%, 100% 100%, 0 100%, 0 7%);
  width: 3.2rem;
  min-height: 1.6rem;
  background: url(../../assets/<EMAIL>);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  .closeInfoWindow {
    position: absolute;
    top: 0.15rem;
    right: 0.15rem;
    width: 0.2rem !important;
    cursor: pointer;
  }
}
.drain-content {
  opacity: 0.89;
  box-sizing: border-box;
  color: #00eaff;
  overflow: -Scroll;
  overflow-x: hidden;
  overflow-y: hidden;
  position: relative;
  // clip-path: polygon(4% 0, 96% 0, 100% 7%, 100% 100%, 0 100%, 0 7%);
  width: 3.6rem;
  height: 2.3rem;
  background: url(../../assets/outlet/<EMAIL>);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  .closeInfoWindow {
    position: absolute;
    top: 0.2rem;
    right: 0.25rem;
    width: 0.25rem !important;
    cursor: pointer;
  }
  .drin-name {
    font-size: 0.18rem;
    padding: 0.2rem 0 0.01rem 0.27rem;
    font-family: PingFang SC;
    font-weight: 500;
    color: #00eaff;
  }
  .drain-sub-name {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-left: 0.27rem;
    padding-right: 0.74rem;
    .letf {
      padding-left: 0.15rem;
      position: relative;
      font-size: 0.16rem;
      font-weight: 500;
      color: #11f7da;
      &::before {
        position: absolute;
        width: 0.06rem;
        height: 0.06rem;
        background: #11f7da;
        border-radius: 50%;
        content: '';
        left: 0;
        top: 0.08rem;
      }
    }
    .letf-err {
      padding-left: 0.15rem;
      position: relative;
      font-size: 0.16rem;
      font-weight: 500;
      color: #ff4343;
      &::before {
        position: absolute;
        width: 0.06rem;
        height: 0.06rem;
        background: #ff4343;
        border-radius: 50%;
        content: '';
        left: 0;
        top: 0.08rem;
      }
    }
    .right {
      font-size: 0.14rem;
      font-weight: 500;
      color: #ededed;
    }
  }
  .params-area {
    padding: 0.08rem 0.27rem 0;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    .params-list {
      display: flex;
      flex-direction: column;
      justify-content: center;
      width: 33.3%;
      span {
        text-align: center;
        &:nth-child(1) {
          font-size: 0.14rem;
          font-weight: 400;
          color: #5abefe;
        }
        &:nth-child(2) {
          font-size: 0.18rem;
          font-weight: 400;
          color: #ffffff;
          .unit {
            font-size: 0.12rem;
            color: #ffffff;
          }
        }
      }
      .err {
        color: #ff4343 !important;
        span {
          color: #ff4343 !important;
        }
      }
    }
  }
  .btn {
    width: 1.1rem;
    height: 0.3rem;
    background: rgba(9, 72, 171, 0.6);
    border: 1px solid #00eaff;
    border-radius: 0.05rem;
    text-align: center;
    line-height: 0.3rem;
    font-size: 0.16rem;
    font-weight: 500;
    color: #00eaff;
    cursor: pointer;
  }
}
.drain-person-content {
  opacity: 0.89;
  box-sizing: border-box;
  color: #00eaff;
  overflow: -Scroll;
  overflow-x: hidden;
  overflow-y: hidden;
  position: relative;
  // clip-path: polygon(4% 0, 96% 0, 100% 7%, 100% 100%, 0 100%, 0 7%);
  width: 3.9rem;
  height: 2.3rem;
  background: url(../../assets/outlet/<EMAIL>);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  .closeInfoWindow {
    position: absolute;
    top: 0.28rem;
    right: 0.5rem;
    width: 0.25rem !important;
    cursor: pointer;
  }
  .name {
    font-size: 0.18rem;
    font-weight: 500;
    color: #00eaff;
    padding: 0.32rem 0.7rem 0.32rem 0.32rem;
    line-height: 0.18rem;
  }
  .list {
    display: flex;
    margin-top: 0.13rem;
    padding: 0 0.32rem;
    &:first-child {
      margin-top: 0;
    }
    span {
      display: inline-block;
      font-size: 0.14rem;
      font-weight: 400;
      line-height: 0.14rem;
      &:nth-child(1) {
        width: 0.7rem;
        color: #5abefe;
      }
      &:nth-child(2) {
        width: calc(100% - 0.7rem);
        color: #ffffff;
      }
    }
  }
}
// .water-content::before {
//   position: absolute;
//   top: 0;
//   left: -0.15rem;
//   content: "";
//   width: 0.6rem;
//   transform: rotate(141deg);
//   height: 1px;
//   background: #4c8fde;
// }
// .water-content::after {
//   position: absolute;
//   top: 0;
//   right: -0.15rem;
//   content: "";
//   width: 0.6rem;
//   transform: rotate(-141deg);
//   height: 1px;
//   background: #4c8fde;
// }
.water-top {
  width: 2.8rem;
  height: 0.36rem;
  // margin-top: 0.2rem;
  margin-left: 0.1rem;
  box-sizing: border-box;
  padding: 0 0.1rem;
}
.water-top-top {
  display: flex;
  justify-content: space-between;
  box-sizing: border-box;
  margin-top: 0.15rem;
  // padding: 0.1rem 0.17rem 0.11rem 0.27rem;
}
.water-top-name1 {
  font-size: 0.16rem;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
.water-top-state {
  width: 0.35rem;
  height: 0.16rem;
  background: #22b331;
  border-radius: 0.05rem;
  line-height: 0.16rem;
  color: #fff;
  font-size: 0.12rem;
  text-align: center;
  margin-left: 0.05rem;
  flex: none;
}
.water-top-name {
  font-size: 0.15rem;
  font-weight: 500;
  width: 1.5rem;
  display: flex;
  align-items: center;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
.water-top-type {
  font-size: 0.13rem;
  font-weight: 500;
  color: rgba(0, 234, 255, 1);
  display: flex;
  align-items: center;
}
.dian {
  width: 0.06rem;
  height: 0.06rem;
  background: rgb(0, 255, 0);
  border-radius: 50%;
  margin-right: 0.06rem;
}
.water-bottom {
  width: 3rem;
  margin-left: 0.12rem;
  p {
    margin-bottom: 0;
  }
}
.water-bottom > div:nth-of-type(1) {
  width: 100%;
  display: flex;
  margin-bottom: 0.1rem;
}
.water-bottom > div:nth-of-type(1) > div {
  width: 28.33%;
  text-align: center;
}
.water-bottom > div p:nth-of-type(1) {
  font-size: 0.14rem;
  color: #ffffff;
}
.water-bottom > div p:nth-of-type(2) {
  font-size: 0.16rem;
  color: #4adb4e;
}
.water-bottom-btn {
  width: 1rem;
  height: 0.25rem;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(9, 72, 171, 0.6);
  border: 1px solid rgba(0, 234, 255, 1);
  font-weight: 500;
  font-size: 0.14rem;
  color: rgba(0, 234, 255, 1);
  margin: 0 auto;
  cursor: pointer;
}
</style>
<style lang="less" scoped>
.container {
  width: 100%;
  height: 100%;
}
.marker-selected {
  width: 0.5rem;
  height: 0.5rem;
  background-image: url('../../assets/car-selected.png');
  background-size: 100% 100%;
}
</style>
