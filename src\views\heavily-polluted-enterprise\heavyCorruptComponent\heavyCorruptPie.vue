<template>
  <div class="HCPtitle">
    <div :id="Pid" style="height:1.28rem"></div>
    <div class="HCPright">
      <div class="title">{{ chartData.type }}</div>
      <div class="num">
        历史累计： <span :class="chartData.isAlarm ? 'isalarm' : ''">{{chartData.value}}</span>{{ chartData.unit }}
      </div>
      <div :id="Bid" style="height: 0.2rem;width: 100%;margin-top: 0.05rem;"></div>
    </div>
  </div>
</template>

<script>
import * as echarts from "echarts";
export default {
  props: {
    Pid: {
      type: String,
      default: "HCPie",
    },
    Bid: {
      type: String,
      default: "HCBar",
    },
    chartData: {
      type: Object,
      default: () => ({
        type: "废水监测",
        value: 800,
        rate: 70,
        unit: "吨",
        isAlarm: true,
      }),
    },
  },
  watch: {
    id(id) {
      if (id) {
        this.initChartP(id, 30);
      }
    },
    Bid(id){
      if(id){
        this.initBar()
      }
    }
  },
  mounted() {
    this.initChartP(this.Pid, this.chartData.rate);
    this.initBar()
  },
  methods: {
    initChartP(id, val, clr = "#00B4F7") {
      let value = val;
      let chart = echarts.init(document.getElementById(id));
      let option = {
        title: {
          text: "{b|" + val + "%}",
          x: "9%",
          y: "center",
          textStyle: {
            lineHeight: 260,
            //height:20,
            rich: {
              a: {
                fontSize: 45,
                color: "#fff",
                fontWeight: "500",
                fontStyle: "oblique",
              },
              b: {
                fontSize: 18,
                color: "#fff",
                fontStyle: "bold",
                fontFamily: "DIN",
              },
              c: {
                fontSize: 25,
                fontWeight: "600",
                color: "#fff",
                padding: [5, 0],
              },
            },
          },
        },
        series: [
          {
            name: "内环",
            type: "gauge",
            radius: "72%",
            center: ["15%", "50%"],
            clockwise: false,
            startAngle: "90",
            endAngle: "-269.9999",
            splitNumber: "50",
            detail: {
              show: false,
              offsetCenter: [0, 0],
              formatter: `{fline|32}{tline|次}`,
              color: "#7BCEF6",
              rich: {
                fline: {
                  padding: [0, 8],
                  fontSize: 24,
                  color: "#7BCEF6",
                },
                tline: {
                  fontSize: 12,
                  color: "#7BCEF6",
                },
              },
            },
            pointer: {
              show: true,
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: [
                  [0, "#2cfafc"],
                  [
                    (100 - value) / 100,
                    new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                      {
                        offset: 0,
                        color: "#7ED0F6",
                      },
                      {
                        offset: 0.3,
                        color: "#7ED0F6",
                      },
                      {
                        offset: 1,
                        color: "#173c5e",
                      },
                    ]),
                  ],
                  [1, "#0f232e"],
                ],
                width: 5,
              },
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              show: true,
              length: 5,
              lineStyle: {
                color: "#020f18",
                width: 3,
              },
            },
            axisLabel: {
              show: false,
            },
          },
          {
            name: "外环",
            type: "pie",
            center: ["15%", "50%"],
            radius: ["84%", "85%"],
            hoverAnimation: false,
            clockWise: false,
            itemStyle: {
              normal: {
                color: "#223040",
              },
            },
            label: {
              show: false,
            },
            data: [100],
          },
          {
            name: "",
            type: "gauge",
            radius: "56%",
            center: ["15%", "50%"],
            startAngle: "90",
            endAngle: "-269.9999",
            splitNumber: 50,
            hoverAnimation: true,
            axisTick: {
              show: false,
            },
            splitLine: {
              length: 3,
              lineStyle: {
                color: "#223040",
                width: 3,
              },
            },
            axisLabel: {
              show: false,
            },
            pointer: {
              show: false,
            },
            axisLine: {
              lineStyle: {
                opacity: 0,
              },
            },
            detail: {
              show: false,
            },
            data: [
              {
                value: 0,
                name: "",
              },
            ],
          },
        ],
      };
      chart.setOption(option);
    },
    initBar() {
      let chart = echarts.init(document.getElementById(this.Bid));
      let option = {
        grid: {
          top: "0%",
          left: "0%",
          right: "0%",
          bottom: "0%",
        },
        yAxis: {
          show: false,
          data: ["1"],
          axisLine: {
            lineStyle: {
              color: "#0a4980",
            },
          },
          axisLabel: {
            textStyle: {
              color: "#ADD6FF",
              fontSize: 12,
            },
          },
        },
        xAxis: [
          {
            show: false,
            type: "value",
            splitLine: {
              show: false,
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#0a4980",
              },
            },
            axisLabel: {
              show: true,
              fontSize: 12,
              textStyle: {
                color: "#ADD6FF",
              },
            },
          },
        ],
        series: [
          {
            name: "机器故障率",
            type: "bar",
            barWidth: 8,
            itemStyle: {
              normal: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 1,
                  y2: 0,
                  colorStops: [
                    {
                      offset: 0,
                      color: "#0089CE", // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: this.chartData.isAlarm ? "#D15252" : "#00B4F7", // 100% 处的颜色
                    },
                  ],
                  global: false, // 缺省为 false
                },
              },
            },
            data: [this.chartData.rate],
            z: 3,
          },
          {
            name: "机器故障率",
            type: "bar",
            barGap: "-100%",
            data: [100],
            barWidth: 8,
            label: {
              show: false,
              position: ["0%", "-100%"],
              formatter: "机具故障率",
            },
            itemStyle: {
              normal: {
                color: "#051C2F",
              },
            },
            z: 2,
          },
        ],
      }
      chart.setOption(option);
    },
  },
};
</script>

<style lang="less" scoped>
.HCPtitle {
  position: relative;
  .HCPright {
    position: absolute;
    left: 1.3rem;
    top: 0.3rem;
    right: 0.2rem;
    .title {
      font-size: 0.16rem;
      font-family: Source Han Sans CN;
      font-weight: 500;
      color: #dbebf7;
    }
    .num {
      font-size: 0.14rem;
      font-family: PingFang SC;
      font-weight: 400;
      color: #a8b2b8;
      margin-top: 0.05rem;
      .isalarm {
        color: #ff4444;
      }
    }
  }
}
</style>
