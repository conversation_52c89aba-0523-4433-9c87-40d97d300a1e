<template>
  <div class="people-area">
    <peopleRow :list="title" rowTitle="row-title" :height="40"></peopleRow>

    <!-- <swiper
      :mousewheel="true"
      :autoplay="swiper_options.autoplay"
      :loop="swiper_options.loop"
      :speed="swiper_options.speed"
      :direction="swiper_options.direction"
      :slidesPerView="swiper_options.slidesPerView"
      :spaceBetween="swiper_options.spaceBetween"
    >
      <swiper-slide
        v-for="(item, index) in data1"
        :key="index + 'pers'"
        style="height: 36px !important"
      >
        <peopleRow :list="item" :index="index" />
      </swiper-slide>
    </swiper> -->
    <div class="people_row">
      <div
        v-for="(item, index) in data1"
        :key="index + 'pers'"
        style="height: 36px !important"
      >
        <peopleRow :list="item" :index="index" />
      </div>
    </div>
  </div>
</template>

<script>
import { Swiper, SwiperSlide } from 'vue-awesome-swiper'
import peopleRow from './peopleRow.vue'
export default {
  name: 'peopleList',
  props: {
    persiondata: {
      type: Array,
      default: () => [],
    },
  },
  components: {
    peopleRow,
    Swiper,
    SwiperSlide
  },
  data() {
    return {
      title: {
        departmentName: '部门',
        name: '姓名',
        tel: '电话',
        state: '状态',
      },
      swiper_options: {
        autoplay: {
          delay: 3000,
          disableOnInteraction: false,
        },
        loop: false,
        slidesPerView: 3,
        spaceBetween: 0,
        direction: 'vertical',
      },
      data1: [
        // {
        //   departmentName: '监测站',
        //   name: '周鹏林0',
        //   tel: '18352482642',
        //   state: '进行中',
        // },
      ],
    }
  },
  watch: {
    persiondata: {
      handler(nval, oval) {
        // console.log(oval,nval,'人员的数据人员列表');
        if (nval) {
          this.data1 = nval.map((e) => {
            return {
              departmentName: e.departmentName,
              name: e.userName,
              tel: e.phone,
              state: e.completeStatus == 0 ? '进行中' : '已完成',
            }
          })
          // console.log('人员的列表', this.data1)
        } else {
          this.data1 = []
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    moreClick() {
      this.isMore = !this.isMore
      // this.$emit('getclick', this.thind)
    },
  },
}
</script>
<style lang="less" scoped>
.people_row{
  height: 110px;
  overflow-y: auto;
  &::-webkit-scrollbar-track {
    border-radius: 10px;
    background: rgba(57, 177, 255, 0);
  }
  &::-webkit-scrollbar-thumb {
    border-radius: 10px;
    width: 1px;
    background: rgba(21, 62, 105, 0.5);
  }
}
</style>