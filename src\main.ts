import Vue from "vue";
import App from "./App.vue";
import router from "./router";
// @ts-ignore
const originalPush = router.push
// @ts-ignore
router.push = function push(location: any, onResolve: any, onReject: any) {
  if (onResolve || onReject) return originalPush.call(this, location, onResolve, onReject)
  // @ts-ignore
  return originalPush.call(this, location).catch((err: any) => err)
}
import store from "./store";
import vueBus from "./vueBus";
import "@/icons/components";
import SvgIcon from "vue-svgicon";
import { Modal } from "ant-design-vue";
import Antd from 'ant-design-vue'
/* eslint-disable @typescript-eslint/ban-ts-ignore */
//@ts-ignore
import preview from "vue-photo-preview";
import 'vue-photo-preview/dist/skin.css'
import VideoPlayer from "vue-video-player";
import ElementUI from 'element-ui';
import 'element-ui/lib/theme-chalk/index.css';
import '@/assets/css/element.css'
import DvFullScreenContainer from './components/fullScreenContainer1/index.js'
import Vue2OrgTree from 'vue2-org-tree'
import VideoTimeline from '@wanglin1994/video-timeline'

Vue.use(VideoTimeline)

import 'animate.css'
import anime from 'animejs/lib/anime.es.js'

// 全局使用swiper
import swiper from 'swiper'

Vue.use(Vue2OrgTree)

Vue.use(Antd)
Vue.use(ElementUI);
Vue.use(preview)


Vue.use(VideoPlayer);
// http://necolas.github.io/normalize.css/ a popular css reset lib
import "normalize.css/normalize.css";

// antd css lib
import "ant-design-vue/dist/antd.css";

// swiper css
import "swiper/css/swiper.css";

// global less style
import "@/styles/index.less";
// ignore
import { rewirteLog } from '@/utils/ignore'
// moment
import moment from "moment";
moment.locale("zh-cn");

import {timeoutFunc} from '@/utils/index'

function reloadPage() {
  location.reload()
}

// 每日0点自动刷新页面
timeoutFunc({time: '00:00:00'}, reloadPage)
timeoutFunc({time: '05:00:00'}, reloadPage)
timeoutFunc({time: '12:10:00'}, reloadPage)
timeoutFunc({time: '19:00:00'}, reloadPage)
// document.documentElement.style.fontSize = `192px`

// svg icon
Vue.use(SvgIcon);
Vue.use(DvFullScreenContainer)

//vue bus
Vue.use(vueBus);

// permission control
import "@/permission";


Vue.config.productionTip = false;

new Vue({
  router,
  store,
  render: h => h(App)
}).$mount("#app");
rewirteLog()
