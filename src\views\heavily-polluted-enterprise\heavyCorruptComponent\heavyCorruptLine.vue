<style lang="less" scoped>
.no-data {
  font-size: 0.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
<template>
  <div
    v-if="propData&&propData.length > 0&&(this.propData[0].x?true:this.propData[1]&&this.propData[1].x?true:false)"
    :id="id"
    :style="{ height: height, width: width }"
  />
  <div v-else class="no-data" :style="{ height: height, width: width }">
    暂无数据
  </div>
</template>

<script lang="ts">
import * as echarts from 'echarts'
import { Component, Prop, Watch } from 'vue-property-decorator'
import { mixins } from 'vue-class-component'
import ResizeMixin from '@/components/Charts/mixins/resize'
import moment from 'moment'
interface AirData {
  bottomList: string[]
  dataList: string[]
  standard: number | string
  max: number | string
  unit: string
  name: string
}
@Component({
  name: 'LineChartDashed'
})
export default class extends mixins(ResizeMixin) {
  // @Prop({ default: "chart" }) private className!: string;
  @Prop({ default: 'Leq' }) private subTabName!: string
  @Prop({ default: 0 }) private standard!: Number|string
  @Prop({ default: 'chart' }) private id!: string
  @Prop({ default: '200px' }) private width!: string
  @Prop({ default: '200px' }) private height!: string
  @Prop({ required: true }) private propData!: any
  @Prop({ required: false, default: 'rgba(14,156,255,1)' })
  private bgColor!: string
  @Prop({ required: false, default: false }) private smooth!: boolean
  @Prop({ required: false, default: true }) private bgColorState!: boolean
  @Prop({ required: false, default: '' }) private xText!: string
  @Watch('propData', { immediate: true, deep: true }) public onMsgChanged(
    newValue: any,
    oldValue: any
  ) {
    if (this.propData) {
      if (this.chart) {
        this.chart.clear()
        this.chart.dispose()
        this.chart = null
      }
      if(!this.propData.length)return
      this.$nextTick(() => {
        this.initChart()
      })
    }
  }
  mounted() {
    if (this.propData) {
      this.initChart()
    }
  }
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.clear()
    this.chart.dispose()
    this.chart = null
  }

  private initChart() {
    if (this.chart === null || this.chart === undefined) {
      const chartDom = document.getElementById(this.id) as HTMLDivElement
      if (!chartDom) return
      this.chart = echarts.init(chartDom)
    }
    // SO2:1 NO2:1 O3:1 CO:1 PM10:1 PM:2.5
    const option:any ={
      backgroundColor: 'transparent',
      color:['#1DCCFF','#15FEFE'],
      legend: {
        right: 30,
        textStyle: {
          color: '#7BB7ED',
          fontSize: 12
        },
        itemHeight: 12
      },
      grid: {
        // height: '80%',
        left: '3%',
        right: '3%',
        top: '18%',
        bottom:'3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        name: '',
        nameTextStyle: {
          color: '#fff',
          lineHeight: -30,
          align: 'center',
          verticalAlign: 'bottom'
        },
        // data: this.propData&&this.propData[0].x?this.propData[0].x.map((v:any)=>v.slice(6,11)):this.propData[0]?[]:this.propData[1]?[]:this.propData[1].x.map((v:any)=>v.slice(6,11)),
        data: this.propData[0]&&this.propData[0].x?this.propData[0].x.map((v:any)=>v.slice(6,11)):this.propData[1]?this.propData[1].x.map((v:any)=>v.slice(6,11)):[],
        axisLabel: {
          textStyle: {
            fontSize: 12,
            color: 'white'
          }
        },
        axisLine: {
          show: false,
          lineStyle:{
            type: 'dotted'
          }
        },
        axisTick: {
          show: false
        }
      },
      yAxis: {
        type: 'value',
        name: '单位：'+(this.subTabName=='pH'?'无':this.subTabName=='流量'?'m³':this.subTabName=='水温'?'℃':'mg/L'),
        min: 0,
        max:  (value:any)=> {
          let max = value.max !== -Infinity ?value.max:10
          return max;
        },
        nameTextStyle: {
          color: '#fff',
          shadowOffsetX: 50
          // align: "center"
        },
        axisLabel: {
          textStyle: {
            fontSize: 12,
            color: 'white'
          }
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        splitNumber: 5,
        splitLine: {
          lineStyle: {
            // type: "dashed"
            color: 'RGBA(2, 39, 75, 1)'
          }
        }
      },
      tooltip: {
        trigger: 'axis',
        confine: true,
        axisPointer: {
          type: 'cross',
          label: {
            show:false,
            backgroundColor: '#6a7985'
            // formatter:(params:any)=>{
            //   if(params.axisDimension=='x'){
            //     return params.value
            //   }else{
            //     return ''
            //   }
            // },
          }
        },
        formatter:(params:any)=>{
          // console.log(params);
          let str = ''
          const time:string = this.propData[params[0].seriesIndex].x[params[0].dataIndex]
          const curDay = moment().format('YYYY')
          const showTime = time.includes('-')?time:curDay+'-'+time
          params.forEach((p:any)=>{
            const dayData = this.propData[p.seriesIndex].value[p.dataIndex]
            str += `
              <div style="font-size: 0.14rem;margin-top: 0.12rem;">
                ${p.marker}  ${p.seriesName+this.subTabName}： <span>${dayData} ${this.subTabName=='pH'?'':this.subTabName=='流量'?'m³':this.subTabName=='水温'?'℃':'mg/L'}</span>
              </div>
            `
          })
          return `
            <div style="border-radius: 0.06rem;padding: 0.1rem 0.13rem; background-color: rgba(11, 11, 11, .4);color: #fff;">
              <div style="font-size: 0.14rem;"><i class="el-icon-time"></i>  监测时间:  ${showTime}</div>
              ${str}
            </div>
          `
        }
      },
      series: []
    }
    option.series=this.propData.map((v:any,i:any)=>{
      return {
          name: v.name,
          data: v.value || [],
          type: 'line',
          smooth: this.smooth,
          lineStyle: {
            color: '#15B4FE' //改变折线颜色
          },
          symbol: 'circle',
          symbolSize: 5,
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: this.bgColorState
                    ? 'RGBA(21,180,254, 0.7)'
                    : 'transparent' // 0% 处的颜色
                },
                {
                  offset: 1,
                  color: 'RGBA(25,252,255, 0)' // 100% 处的颜色
                }
              ],
              global: false
            },
            shadowColor: 'rgba(0,85,250,0)',
            shadowBlur: 20
          },
          itemStyle: {
            color: (params:any) => {
              if(v.valueType==1&&v.alarmMaxValue&&v.alarmMinValue){
                if(params.value>v.alarmMaxValue||params.value<v.alarmMinValue) return 'red'
              }else if(v.valueType==0&&v.alarmValue){
                if(params.value>v.alarmValue) return 'red'
              }else{
                return '#1DCCFF'
              }
              // return params.value <= v.standard ? '#1DCCFF'  : 'red'
            }, //改变折线点的颜色 '#1DCCFF'
            borderColor: '#fff'
          }
        }
    })
    this.chart.setOption(option as EChartOption<EChartOption>)
  }
}
</script>



