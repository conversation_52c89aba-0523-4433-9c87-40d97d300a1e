<style lang="less" scoped>
    p {
        margin-bottom: 0 !important;
    }
    .emergency-command {
        width: 100%;
        height: 100%;
        background-size: 100%;
        position: relative;
        overflow: hidden;
        .sub-title {
            margin-bottom: 0.1rem;
        }
        .emergency-command-content {
            position: relative;
            .center-map {
                width: 100%;
                height: calc(1080px - 0.94rem);
            }
            .title {
                font-size: 0.2rem;
                font-family: Source <PERSON>;
                font-weight: 500;
                text-shadow: 0 0 5px blue, 0 0 5px blue;
            }
            .emergency-command-bottom {
                overflow: hidden;
                transition: 1.5s;
                position: absolute;
                bottom: 0;
                // height: 5rem;
                height: 6.4rem;
                width: 14.5rem;
                // width: 100%;
                // background-image: url("../../assets/bottomBg.png");
                // background-size: 100% 100%;
                background: linear-gradient(rgba(5, 23, 54, 0.1), rgba(5, 23, 54, 1));
                padding-left: 0.71rem;
                .content {
                    width: 13.5rem;
                    height: 1.3rem;
                    .line-five {
                        height: 5.5rem;
                        background-color: #ffd200;
                        width: 0.02rem;
                        position: absolute;
                        top: 0rem;
                        z-index: 9999;
                        right: 0.33rem;
                    }
                    .taskAlert {
                        position: absolute;
                        width: 1.86rem;
                        height: auto;
                        background: rgba(8, 45, 120, 0.8);
                        top: 0.5rem;
                        z-index: 999999;
                        padding: 0.1rem;
                        > div:nth-of-type(2) {
                            display: flex;
                            span {
                                display: block;
                                width: 0.1rem;
                                height: 0.1rem;
                                background: rgba(255, 210, 0, 1);
                                border-radius: 50%;
                                margin-top: 0.05rem;
                                margin-right: 0.05rem;
                            }
                            div {
                                flex: 1;
                                // overflow: hidden;
                                // text-overflow: ellipsis;
                                // display: -webkit-box;
                                // -webkit-box-orient: vertical;
                                // -webkit-line-clamp: 2;
                                font-size: 0.16rem;
                            }
                        }
                        span {
                            display: block;
                            width: 0.1rem;
                            height: 0.1rem;
                            background: rgba(255, 210, 0, 1);
                            border-radius: 50%;
                            margin-top: 0.05rem;
                            margin-right: 0.05rem;
                        }
                        div {
                            flex: 1;
                            // overflow: hidden;
                            // text-overflow: ellipsis;
                            // display: -webkit-box;
                            // -webkit-box-orient: vertical;
                            // -webkit-line-clamp: 2;
                            font-size: 0.16rem;
                        }
                    }
                    // .taskAlert:after {
                    //   position: absolute;
                    //   content: "";
                    //   right: 0;
                    //   width: 0;
                    //   height: 0;
                    //   border-style: solid;
                    //   border-width: 0.075rem 0 0.075rem 0.13rem;
                    //   border-color: transparent transparent transparent #007bff;
                    // }
                    .timeLine {
                        display: flex;
                        justify-content: space-between;
                        position: relative;
                        .timeLine-text {
                            font-size: 0.13rem;
                            color: rgba(255, 255, 255, 1);
                            width: 1.5rem;
                            text-align: right;
                            padding-top: 0.45rem;
                            position: relative;
                            .timeLine-text-btn {
                                position: absolute;
                                left: 0;
                                top: 0;
                                background-image: url("../../assets/emergency/anniubg.png");
                                background-size: 100% 100%;
                                width: 1.04rem;
                                height: 0.46rem;
                                display: flex;
                                justify-content: center;
                                align-items: center;
                                font-size: 0.16rem;
                                font-family: Source Han Sans SC;
                                font-weight: 400;
                                color: rgba(165, 248, 255, 1);
                            }
                            > div {
                                line-height: 0.2rem;
                            }
                        }
                        .timeLine-text-line {
                            width: calc(100% - 1.5rem);
                            overflow: hidden;
                            height: 1.2rem;
                            .line-one {
                                height: 0.02rem;
                                background-color: #ffffff;
                                width: 100%;
                                position: absolute;
                                top: -0.57rem;
                            }
                            .line-two {
                                height: 0.02rem;
                                background-color: #ffffff;
                                width: 100%;
                                position: absolute;
                                top: -0.37rem;
                            }
                            .line-three {
                                height: 0.02rem;
                                background-color: #ffffff;
                                width: 100%;
                                position: absolute;
                                top: -0.17rem;
                            }
                            .line-four {
                                height: 0.02rem;
                                background-color: #ffffff;
                                width: 100%;
                                position: absolute;
                                top: 0.03rem;
                            }

                            .line-scrool {
                                height: 1.5rem;
                                background: transparent;
                                width: 100%;
                                position: absolute;
                                top: -1.35rem;
                            }
                        }
                    }
                }
                .charts-div {
                    overflow: hidden;
                    display: flex;
                    justify-content: space-between;
                    width: 13.5rem;
                    .charts-img {
                        width: 0.72rem;
                        height: 0.72rem;
                    }
                    .charts-btn {
                        background-image: url("../../assets/emergency/anniubg.png");
                        background-size: 100% 100%;
                        width: 1.04rem;
                        height: 0.46rem;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        font-size: 0.16rem;
                        font-family: Source Han Sans SC;
                        font-weight: 400;
                        color: rgba(165, 248, 255, 1);
                    }
                    .img-one {
                        margin-top: 0.15rem;
                    }
                    .img-two {
                        margin-top: 0.65rem;
                        margin-bottom: 0.5rem;
                    }
                    .charts-left {
                        > div {
                            text-align: center;
                        }
                    }
                    .charts-right {
                        flex: 1;
                        .charts-title {
                            width: 3.1rem;
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            border-bottom: 1px solid rgba(62, 193, 239, 1);
                            padding-bottom: 0.05rem;
                            font-size: 0.16rem;
                            font-family: Source Han Sans SC;
                            font-weight: bold;
                            color: rgba(12, 235, 255, 1);
                            margin-left: 0.5rem;
                        }
                        .charts-content {
                            height: 1.15rem;
                        }
                    }
                }
                .showImg {
                    width: 0.52rem;
                    height: 100%;
                    position: absolute;
                    right: 0;
                    top: 0;
                }
            }
            .emergency-command-left {
                transition: 1.5s;
                position: absolute;
                left: 0.71rem;
                top: 0.2rem;
                width: 4.68rem;
                box-sizing: border-box;
                .left-content {
                    background: rgba(7, 24, 102, 0.6);
                    .table {
                        width: 100%;
                        .tr {
                            height: 0.32rem;
                            font-size: 0.12rem;
                            font-family: Source Han Sans SC;
                            font-weight: 400;
                            color: rgba(255, 255, 255, 1);
                            display: flex;
                            align-items: center;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                            .table-name {
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                background: #0f245e;
                                width: 0.8rem;
                            }
                            .td {
                                border: 1px solid rgba(22, 179, 255, 1);
                                height: 0.32rem;
                                display: flex;
                                box-sizing: border-box;
                                align-items: center;
                            }
                            > .td:nth-of-type(2) {
                                padding: 0 0.18rem;
                                font-size: 0.14rem;
                                overflow: hidden;
                                text-overflow: ellipsis;
                                white-space: nowrap;
                                flex: 1;
                            }
                            .table-text {
                                font-size: 0.14rem;
                                color: #f82929;
                                padding: 0 0.18rem;
                                font-weight: bolder;
                                overflow: hidden;
                                text-overflow: ellipsis;
                                white-space: nowrap;
                            }
                        }
                    }
                }
            }
            .emergency-command-right {
                transition: 1.5s;
                position: absolute;
                right: 0.71rem;
                top: 0.2rem;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                height: calc(100% - 0.6rem);
                .right-one-content,
                .right-two-content,
                .right-three-content {
                    width: 4.1rem;
                    height: 2.38rem;
                    padding: 0.09rem 0.1rem 0 0.11rem;
                    background-image: url("../../assets/emergency/videobg.png");
                    background-size: 100% 100%;
                }
                .right-one-content {
                }
                .right-two-content {
                    position: relative;
                    .right-two-content-one {
                        z-index: 99;
                        position: absolute;
                        padding-left: 0.07rem;
                        left: 0.1rem;
                        top: 0.5rem;
                        width: 2.1rem;
                        height: 0.54rem;
                        background-image: url("../../assets/emergency/bgs1.png");
                        background-size: 100% 100%;
                        font-style: italic;
                        font-size: 0.14rem;
                        display: flex;
                        align-items: center;
                        justify-content: flex-start;
                        span {
                            font-size: 0.23rem;
                        }
                    }
                    .right-two-content-two {
                        z-index: 99;
                        position: absolute;
                        padding-right: 0.07rem;
                        right: 0.1rem;
                        top: 0.5rem;
                        width: 2.1rem;
                        height: 0.54rem;
                        background-image: url("../../assets/emergency/bgs.png");
                        background-size: 100% 100%;
                        font-style: italic;
                        font-size: 0.14rem;
                        display: flex;
                        align-items: center;
                        justify-content: flex-end;
                        span {
                            font-size: 0.23rem;
                        }
                    }
                }
                .right-three-content {
                    position: relative;
                    .video-right {
                        position: absolute;
                        right: 0.25rem;
                        top: 0.15rem;
                        z-index: 9999;
                        .video-right-top {
                            width: 0.95rem;
                            height: 0.54rem;
                            border-radius: 2px;
                            background: rgba(255, 255, 255, 1);
                            border: 1px solid rgba(0, 234, 255, 1);
                        }
                        .swiper-video {
                            height: 1.2rem;
                        }
                    }
                }
            }
            .transform-left {
                transform: scale(0); // translateX(-4rem)
                transition: 1.5s;
                opacity: 0;
                visibility: hidden;
            }
            .transform-right {
                transform: scale(0); // translateX(4rem)
                transition: 1.5s;
                opacity: 0;
                visibility: hidden;
            }
            .transform-bottom {
                transform: scale(0); // translateY(4rem)
                transition: 1.5s;
                opacity: 0;
                visibility: hidden;
            }
            .transform-div {
                width: 5.8rem !important;
                transition: 1.5s;
            }
            .transform-div1 {
                width: 4.8rem !important;
                transition: 1.5s;
            }
        }
        .content-text {
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 3;
        }
        .content-one {
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            line-height: 0.28rem;
        }
        .content-two {
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            line-height: 0.28rem;
        }
        .title-top {
            display: flex;
            align-items: center;
            justify-content: space-between;
            > div {
                display: flex;
                align-items: center;
                > :nth-of-type(2) {
                    margin-left: 0.1rem;
                }
            }
        }
    }
    .quanpin {
        position: absolute;
        top: 0.05rem;
        right: 0.5rem;
        z-index: 99;
    }
    .change-camera {
        position: absolute;
        top: 0.1rem;
        right: 0.1rem;
        width: 0.8rem;
        height: 0.24rem;
        background: rgba(13, 29, 74, 0.7);
        border: 1px solid rgba(32, 103, 224, 1);
        border-radius: 0.04rem;
        display: flex;
        align-items: center;
        color: #ccc;
        justify-content: space-around;
        cursor: pointer;
    }
    .change-camera-selected {
        text-shadow: 0 0 5px blue, 0 0 5px blue;
        color: #fff;
    }
</style>
<style lang="less">
    .emergency-command {
        .right-arrow,
        .left-arrow {
            text-align: center;
            outline: none;
            cursor: pointer;
            .icons {
                font-size: 0.16rem;
                color: #ffffff;
            }
        }
        .timeLine {
            // overflow: hidden;
            .tttt {
                // background: #0b3684;
                // height: 0.5rem;
                height: 0rem;
                cursor: move;
                position: relative;
            }

            #times {
                width: 100%;
                // height: 0.5rem;
                height: 0rem;
                // background-color: #0b3684;
                font-size: 0.1rem;
                color: #6aa1cb;
                margin-top: 1.1rem;
                -moz-user-select: none;
                /*火狐*/
                -webkit-user-select: none;
                /*webkit浏览器*/
                -ms-user-select: none;
                /*IE10*/
                -khtml-user-select: none;
                /*早期浏览器*/
                user-select: none;
                padding-left: 0.1rem;
            }

            #time {
                height: 0.02rem;
                // background: #b4f7fd;
                position: relative;
                cursor: pointer;
                top: -1rem;
                // left: -0.8rem;
            }

            #time li {
                float: left;
                background: #b4f7fd;
                height: 0.06rem;
                width: 0.01rem;
                position: absolute;
                list-style: none;
            }

            #time > .time1 {
                width: 0.12rem;
                height: 0.12rem;
                background: linear-gradient(
                        0deg,
                        rgba(0, 150, 81, 1),
                        rgba(16, 162, 95, 1),
                        rgba(0, 208, 112, 1)
                );
                border-radius: 50%;
                z-index: 10;
                position: absolute;
                z-index: 99999;
            }
            #time > .time1-active {
                width: 0.16rem;
                height: 0.16rem;
                background: linear-gradient(
                        0deg,
                        rgba(242, 142, 38, 1),
                        rgba(253, 100, 79, 1)
                );
                border-radius: 50%;
                z-index: 10;
                position: absolute;
            }
        }
        .content {
        }
        .video-js.vjs-fluid {
            height: 100% !important;
        }
        .vjs-poster {
            background-size: 100% 100%;
        }
        .vjs-big-play-button {
            border: none !important;
            background-color: transparent !important;
            border-radius: 0.3em;
            outline: none;
        }
        .title-select-water {
            display: flex;
            justify-content: space-between;
            width: 1.08rem;
            .ant-select-selection {
                display: flex;
                justify-content: center;
                width: 1.08rem;
                height: 0.25rem;
                font-size: 0.14rem;
                border: none;
                border-radius: unset;
                background: url(../../assets/<EMAIL>);
                background-size: 100% 100%;
                background-repeat: no-repeat;
            }
            .ant-select-selection-selected-value {
                color: rgba(0, 234, 255, 1);
                font-size: 0.14rem;
            }

            .ant-select-focused .ant-select-selection,
            .ant-select-selection:focus,
            .ant-select-selection:active {
                border-color: #40a9ff;
                border-right-width: 0 !important;
                outline: 0;
                box-shadow: none;
                font-size: 0.14rem;
            }
        }
        .title-select-range {
            width: 1.5rem;
            .ant-select-selection {
                width: 1.5rem;
            }
        }
        .water-monitor-tab {
            height: 0.24rem;
            font-size: 0.12rem;
            color: rgba(255, 255, 255, 1);
            background: #0c275c;
            border-radius: 0;
            padding: 0 0.1rem;
            border: none;
        }
        .ant-radio-group-solid
        .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
            background: #0c275c;
            color: rgba(146, 145, 255, 1);
            border: none;
        }
        .ant-radio-button-wrapper:not(:first-child)::before {
            background: transparent;
        }
        .ant-radio-button-wrapper-checked::before {
            background: transparent !important;
        }
        .ant-radio-button-wrapper-checked {
            z-index: 1;
            border-color: #0c275c !important;
            box-shadow: -1px 0 0 0 #0c275c;
            color: rgba(146, 145, 255, 1);
        }
        .ant-radio-group {
            display: flex;
            justify-content: space-around;
            background: #0c275c;
        }
        .ant-radio-group-small .ant-radio-button-wrapper {
            padding: 0;
        }
        .ant-select-selection__rendered {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
        .ant-select-dropdown {
            margin: 0;
            padding: 0;
            color: #8eb3f6;
            font-variant: tabular-nums;
            line-height: 1.5;
            list-style: none;
            -webkit-font-feature-settings: "tnum";
            font-feature-settings: "tnum";
            position: absolute;
            top: -9999px;
            left: -9999px;
            z-index: 1050;
            -webkit-box-sizing: border-box;
            box-sizing: border-box;
            font-size: 0.12rem !important;
            font-variant: initial;
            background-color: #012474;
            border-radius: 4px;
            outline: none;
            -webkit-box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }
        .ant-select-dropdown-menu-item {
            position: relative;
            font-size: 0.12rem !important;
            display: block;
            padding: 5px 12px;
            overflow: hidden;
            color: #8eb3f6;
            font-weight: normal;
            line-height: 22px;
            white-space: nowrap;
            text-overflow: ellipsis;
            text-align: center;
            cursor: pointer;
            -webkit-transition: background 0.3s ease;
            transition: background 0.3s ease;
        }
        .select-main {
            width: 1.2rem;
            display: flex;
            justify-content: space-between;
            .ant-select-selection {
                height: 0.3rem;
                width: 100%;
                border: none;
                outline: none;
                border-radius: unset;
                background: url(../../assets/<EMAIL>);
                background-size: 100% 100%;
                background-repeat: no-repeat;
            }
            .ant-select-selection-selected-value {
                color: rgba(0, 234, 255, 1);
            }
            .ant-select-focused .ant-select-selection,
            .ant-select-selection:focus,
            .ant-select-selection:active {
                border: none;
                border-right-width: 0 !important;
                outline: none;
                box-shadow: none;
            }
        }
    }
    .ant-select-dropdown-menu-item {
        font-size: 0.14rem !important;
    }
</style>
<template>
    <section>
        <section class="emergency-command">
            <section class="emergency-command-content">
                <section class="center-map">
                    <!-- <keep-alive> -->
                        <emergency-map
                        :map-style="mapStyle"
                        :enter-type="8"
                        :map-zoom="13.3"
                        :view-mode="'2D'"
                        :viewCenter="mapViewCenter"
                        :emergency-list="emergencyList"
                        :emergency-station-list="emergencyStationList"
                        :report-details="reportDetails"
                        :rangeObj="rangeObj"
                        />
                    <!-- </keep-alive> -->
                </section>
                <!-- 下面（时间轴、图表） -->
                <section
                        class="emergency-command-bottom"
                        :class="{
            'transform-bottom': displayState,
            'transform-div': showDiv
          }"
                >
                    <div class="title">{{ `协同反应` }}</div>
                    <div class="sub-title">
                        <img
                                src="@/assets/biaoti.png"
                                class="title-img"
                                style="width: 4.5rem;"
                        />
                    </div>
                    <section class="content" :class="{ 'transform-div1': showDiv }">
                        <div class="timeLine">
                            <div class="line-five"></div>
                            <div class="taskAlert" v-show="taskDetailState">
                                <div>{{ creatTime }}</div>
                                <div>
                                    <span></span>
                                    <div :title="replyContent ? replyContent : ''">
                                        {{ replyContent ? replyContent : "" }}
                                    </div>
                                </div>
                            </div>
                            <div class="timeLine-text">
                                <div class="timeLine-text-btn">协同进度</div>
                                <div v-for="(item, index) in taskRemark" :key="index">{{ item.departmentName }}</div>
<!--                                <div>综合行政执法局</div>-->
<!--                                <div>农业和水务局</div>-->
<!--                                <div>住房建设和交通运输局</div>-->
                            </div>
                            <div class="timeLine-text-line">
                                <div id="times">
                                    <div class="tttt">
                                        <ul id="time"></ul>
                                        <div class="line-one"></div>
                                        <div class="line-two"></div>
                                        <div class="line-three"></div>
                                        <div class="line-four"></div>
                                        <div class="line-scrool"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>
                    <section class="charts-div" :class="{ 'transform-div1': showDiv }">
                        <div class="charts-left">
                            <div class="charts-btn">实时监测</div>
                            <div class="img-one">
                                <img class="charts-img" src="@/assets/emergency/shui.png" />
                            </div>
                            <div class="img-two">
                                <img class="charts-img" src="@/assets/emergency/kongqi.png" />
                            </div>
                            <div class="img-three">
                                <img class="charts-img" src="@/assets/emergency/zhongwu.png" />
                            </div>
                        </div>
                        <div class="charts-right">
                            <div>
                                <div class="charts-title">
                                    <div>水质监测</div>
                                    <div>
                                        <a-select
                                                v-model="defaultWater"
                                                class="title-select-water"
                                                @change="waterChange"
                                        >
                                            <a-icon
                                                    slot="suffixIcon"
                                                    type="caret-down"
                                                    style="color:rgba(0, 234, 255, 1);width:14px;height:8px;"
                                            />
                                            <a-select-option
                                                    :value="String(index)"
                                                    v-for="(item, index) in waterStationList"
                                                    :key="index"
                                            >
                                                {{ item.stationName }}</a-select-option
                                            >
                                        </a-select>
                                    </div>
                                </div>
                                <div class="charts-content">
                                    <LineEmergencyCommand
                                            :pageX="pageX"
                                            :id="'Command-emergency123'"
                                            :width="'100%'"
                                            :height="'0.8rem'"
                                            :propData="waterMonitor"
                                            :smooth="true"
                                    />
                                    <a-radio-group
                                            v-model="defaultwaterType"
                                            size="small"
                                            buttonStyle="solid"
                                            style="width:100%;"
                                            @change="waterTypeChange"
                                    >
                                        <a-radio-button
                                                v-for="item in waterTypeList"
                                                :key="item.itemCode"
                                                :value="item.itemCode"
                                                class="water-monitor-tab"
                                        >{{ item.name }}</a-radio-button
                                        >
                                    </a-radio-group>
                                </div>
                            </div>
                            <div>
                                <div class="charts-title">
                                    <div>空气监测</div>
                                    <div>
                                        <a-select
                                                v-model="defaultAir"
                                                class="title-select-water"
                                                @change="airChange"
                                        >
                                            <a-icon
                                                    slot="suffixIcon"
                                                    type="caret-down"
                                                    style="color:rgba(0, 234, 255, 1);width:14px;height:8px;"
                                            />
                                            <a-select-option
                                                    :value="String(index)"
                                                    v-for="(item, index) in airStationList"
                                                    :key="index"
                                            >
                                                {{ item.positionName }}</a-select-option
                                            >
                                        </a-select>
                                    </div>
                                </div>
                                <div class="charts-content">
                                    <LineEmergencyCommand
                                            :pageX="pageX"
                                            :id="'Command-emergency456'"
                                            :width="'100%'"
                                            :height="'1rem'"
                                            :propData="airData"
                                            :smooth="true"
                                    />
                                    <a-radio-group
                                            v-model="airType"
                                            size="small"
                                            buttonStyle="solid"
                                            style="width:100%;"
                                            @change="airTypeChange"
                                    >
                                        <a-radio-button
                                                v-for="item in airTypeLIst"
                                                :key="item.code"
                                                :value="item.code"
                                                class="water-monitor-tab"
                                        >{{ item.name }}</a-radio-button
                                        >
                                    </a-radio-group>
                                </div>
                            </div>
                            <div>
                                <div class="charts-title">
                                    <div>重污监测</div>
                                    <div>
                                        <a-select
                                                v-model="defaultPollution"
                                                class="title-select-water"
                                                @change="pollutionChange"
                                        >
                                            <a-icon
                                                    slot="suffixIcon"
                                                    type="caret-down"
                                                    style="color:rgba(0, 234, 255, 1);width:14px;height:8px;"
                                            />
                                            <a-select-option
                                                    :value="String(index)"
                                                    v-for="(item, index) in pollutionList"
                                                    :key="index"
                                            >
                                                {{ item.companyName }}</a-select-option
                                            >
                                        </a-select>
                                    </div>
                                </div>
                                <div class="charts-content">
                                    <LineEmergencyCommand
                                            :pageX="pageX"
                                            :id="'Command-emergency789'"
                                            :width="'100%'"
                                            :height="'0.8rem'"
                                            :propData="pollutionData"
                                            :smooth="true"
                                    />
                                    <a-radio-group
                                            default-value="'PM₁₀'"
                                            size="small"
                                            buttonStyle="solid"
                                            style="width:100%;"
                                            @change="airTypeChange"
                                    >
                                        <a-radio-button value="'PM₁₀'" class="water-monitor-tab"
                                        >PM₁₀</a-radio-button
                                        >
                                    </a-radio-group>
                                </div>
                            </div>
                        </div>
                    </section>
                    <div>
                        <img
                                src="@/assets/emergency/zhankai.png"
                                class="showImg"
                                v-if="showDiv"
                                @click="showDiv = false"
                                :style="{ right: showDiv ? '-0.2rem' : '0' }"
                        />
                        <img
                                src="@/assets/emergency/shousuo.png"
                                class="showImg"
                                v-if="!showDiv"
                                @click="showDiv = true"
                        />
                    </div>
                </section>
                <!-- 左侧（事件概况） -->
                <section
                        class="emergency-command-left"
                        :class="{ 'transform-left': displayState }"
                >
                    <div class="title  title-top">
                        <div>{{ `事件概况` }}</div>
                        <div>
                            <a-select
                                    v-model="defaultRange"
                                    class="title-select-water title-select-range"
                                    @change="rangeChange"
                            >
                                <a-icon
                                        slot="suffixIcon"
                                        type="caret-down"
                                        style="color:rgba(0, 234, 255, 1);width:14px;height:8px;"
                                />
                                <a-select-option
                                        :value="item.value"
                                        v-for="(item, index) in rangeList"
                                        :key="index"
                                >
                                    {{ item.name }}</a-select-option
                                >
                            </a-select>
                        </div>
                    </div>
                    <div class="sub-title">
                        <img
                                src="@/assets/biaoti.png"
                                class="title-img"
                                style="width: 4.5rem;"
                        />
                    </div>
                    <div class="left-content">
                        <div class="table">
                            <div class="tr">
                                <div class="td table-name">事件类型</div>
                                <div class="td" style="width:1.3rem;">
                                    初报
                                </div>
                                <div class="td table-name">事件等级</div>
                                <div class="td table-text">
                                    {{
                                    reportDetails.levelId ? reportDetails.levelId : ""
                                    }}级突发环境污染事件
                                </div>
                            </div>
                            <div class="tr">
                                <div class="td table-name">上报内容</div>
                                <div
                                        class="td"
                                        :title="reportDetails.content ? reportDetails.content : '--'"
                                >
                                    {{ reportDetails.content ? reportDetails.content : "--" }}
                                </div>
                            </div>
                            <div class="tr">
                                <div class="td table-name">上报单位</div>
                                <div
                                        class="td"
                                        :title="
                    reportDetails.reportingDepartment ? reportDetails.reportingDepartment : ''
                  "
                                >
                                    {{ reportDetails.reportingDepartment ? reportDetails.reportingDepartment : "--" }}
                                </div>
                            </div>
                            <div class="tr">
                                <div class="td table-name">协同单位</div>
                                <div
                                        class="td"
                                        :title="
                    reportDetails.assistDepartment
                      ? reportDetails.assistDepartment
                      : ''
                  "
                                >
                                    {{
                                    reportDetails.assistDepartment
                                    ? reportDetails.assistDepartment
                                    : "--"
                                    }}
                                </div>
                            </div>
                            <div class="tr">
                                <div class="td table-name">指挥小组</div>
                                <div
                                        class="td"
                                        :title="
                    reportDetails.commandDepartment ? reportDetails.commandDepartment : ''
                  "
                                >
                                    {{
                                    reportDetails.commandDepartment ? reportDetails.commandDepartment : "--"
                                    }}
                                </div>
                            </div>
                            <div class="tr">
                                <div class="td table-name">综合协调小组</div>
                                <div
                                        class="td"
                                        :title="
                    reportDetails.coordinationDepartment
                      ? reportDetails.coordinationDepartment
                      : ''
                  "
                                >
                                    {{
                                    reportDetails.coordinationDepartment
                                    ? reportDetails.coordinationDepartment
                                    : "--"
                                    }}
                                </div>
                            </div>
                            <div class="tr">
                                <div class="td table-name">初报时间</div>
                                <div class="td">
                                    {{ reportDetails.createTime ? reportDetails.createTime : "--" }}
                                </div>
                            </div>
                            <div class="tr">
                                <div class="td table-name">事件位置</div>
                                <div
                                        class="td"
                                        :title="reportDetails.address ? reportDetails.address : ''"
                                >
                                    {{ reportDetails.address ? reportDetails.address : "" }}
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
                <!-- 右侧（视频监控） -->
                <div
                        class="emergency-command-right"
                        :class="{ 'transform-right': displayState }"
                >
                    <!-- 站点监控 -->
                    <div class="right-one">
                        <div class="title title-top">
                            <div @click="toRouter('/waterVideo')" style="cursor: pointer;">
                                {{ `站点监控>>` }}
                            </div>
                            <div>
                                <a-select
                                        v-model="defaultWaterStationValue"
                                        class="title-select-water"
                                        @change="waterStationChange"
                                >
                                    <a-icon
                                            slot="suffixIcon"
                                            type="caret-down"
                                            style="color:rgba(0, 234, 255, 1);width:14px;height:8px;"
                                    />
                                    <a-select-option
                                            :value="item.stationId"
                                            v-for="(item, index) in waterStationVideoList"
                                            :key="index"
                                    >
                                        {{ item.stationName }}</a-select-option
                                    >
                                </a-select>
                                <a-select
                                        v-model="defaultCameraValue"
                                        class="title-select-water"
                                        @change="waterCameraChange"
                                >
                                    <a-icon
                                            slot="suffixIcon"
                                            type="caret-down"
                                            style="color:rgba(0, 234, 255, 1);width:14px;height:8px;"
                                    />
                                    <a-select-option
                                            :value="item.monitorId"
                                            v-for="(item, index) in currentCameraList"
                                            :key="index"
                                    >
                                        {{ item.monitorName }}</a-select-option
                                    >
                                </a-select>
                            </div>
                        </div>
                        <div class="sub-title">
                            <img
                                    src="@/assets/biaoti.png"
                                    class="title-img"
                                    style="width: 4rem;"
                            />
                        </div>
                        <div class="right-one-content">
                            <video-player
                                    class="vjs-custom-skin"
                                    ref="livePlayer"
                                    :playsinline="true"
                                    @statechanged="playerStateChanged($event)"
                                    :options="playerOptions1"
                            ></video-player>
                        </div>
                    </div>
                    <!-- 流动监控 -->
                    <div class="right-two">
                        <div class="title title-top">
                            <div @click="toRouter('/vehiclesVideo')" style="cursor: pointer;">
                                {{ `流动监控>>` }}
                            </div>
                            <div>
                                <a-select
                                        v-model="carStreet"
                                        class="select-main"
                                        @change="changeStreet"
                                        style="width: 1.05rem;"
                                >
                                    <a-icon
                                            slot="suffixIcon"
                                            type="caret-down"
                                            style="color:rgba(0, 234, 255, 1);width:14px;height:8px;"
                                    />
                                    <a-select-option
                                            :value="item.deptId"
                                            v-for="item in deptStreetList"
                                            :key="item.deptId"
                                    >
                                        {{ item.deptName }}</a-select-option
                                    >
                                </a-select>
                                <a-select
                                        v-model="carId"
                                        @change="getCarCamera"
                                        class="select-main"
                                        style="margin-left:0.2rem;width:1.05rem"
                                >
                                    <a-icon
                                            slot="suffixIcon"
                                            type="caret-down"
                                            style="color:rgba(0, 234, 255, 1);width:14px;height:8px;"
                                    />
                                    <a-select-option
                                            :value="item.carId"
                                            v-for="item in deptCarList"
                                            :key="item.carId"
                                    >
                                        {{ item.license }}</a-select-option
                                    >
                                </a-select>
                            </div>
                        </div>
                        <div class="sub-title">
                            <img
                                    src="@/assets/biaoti.png"
                                    class="title-img"
                                    style="width: 4rem;"
                            />
                        </div>
                        <div class="right-two-content">
                            <div
                                    v-show="flvPlayer === null"
                                    style="background:rgba(8,45,120,0.5);width: 3.9rem;height: 2.2rem;font-size:0.2rem;display: flex;flex-direction: column;align-items: center;justify-content: center;"
                            >
                                <img
                                        src="@/assets/shipin.png"
                                        alt=""
                                        style="width: 1.11rem;height: 1.04rem;margin-bottom:0.3rem;"
                                />
                                该摄像头不在线
                            </div>
                            <video
                                    v-show="flvPlayer !== null"
                                    id="videoElement"
                                    style="width: 3.9rem;height: 2.2rem;background: #000;"
                                    controls="true"
                            ></video>
                            <div class="change-camera" v-show="flvPlayer !== null">
                <span
                        :class="cameraDirection == 0 ? 'change-camera-selected' : ''"
                        @click="changeCameraDirection(0)"
                >前</span
                >
                                <img src="@/assets/<EMAIL>" width="2" height="25" />
                                <span
                                        :class="cameraDirection == 1 ? 'change-camera-selected' : ''"
                                        @click="changeCameraDirection(1)"
                                >后</span
                                >
                            </div>
                            <div class="right-two-content-one" v-show="flvPlayer !== null">
                                距离事发地：<span>{{melliageData.distance}}</span>{{melliageData.type ? 'KM' : 'M'}}
                            </div>
                            <div class="right-two-content-two" v-show="flvPlayer !== null">
                                预计<span>{{melliageData.time}}</span>分钟后到达
                            </div>
                        </div>
                    </div>
                    <!-- 在线指挥 -->
                    <div class="right-three">
                        <div class="title">{{ `在线指挥` }}</div>
                        <div class="sub-title">
                            <img
                                    src="@/assets/biaoti.png"
                                    class="title-img"
                                    style="width: 4rem;"
                            />
                        </div>
                        <div class="right-three-content">
                            <!-- <video-player
                              class="vjs-custom-skin"
                              ref="livePlayer"
                              :playsinline="true"
                              @statechanged="playerStateChanged($event)"
                              :options="playerOptions"
                            ></video-player> -->
                            <img :src="emActive" style="width:100%;height:100%;" />
                            <div class="video-right">
                                <img class="video-right-top" :src="emActive" />
                                <div>
                                    <div class="right-arrow">
                                        <a-icon type="up" class="icons" />
                                    </div>
                                    <swiper :options="swiperVideoOption" class="swiper-video">
                                        <swiper-slide
                                                v-for="(item, index) in emList"
                                                :key="index"
                                                @click="checkEmImg(index)"
                                        >
                                            <img
                                                    class="video-right-top"
                                                    :src="item"
                                                    style="cursor: pointer;"
                                                    @click="checkEmImg(index)"
                                            />
                                        </swiper-slide>
                                    </swiper>
                                    <div class="left-arrow">
                                        <a-icon type="down" class="icons" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </section>
        <section class="quanpin" @click="fullScreen">
            <img src="@/assets/quanping.png" alt />
        </section>
    </section>
</template>

<script lang="ts">
/* eslint-disable @typescript-eslint/ban-ts-ignore */
//@ts-ignore
import AMap from "AMap";
import flvjs from "flv.js";
import { Component, Vue, Watch } from "vue-property-decorator";
import EmergencyMap from "@/components/GaoDeMap/emergencyMap.vue";
import {
    Table,
    Icon,
    Empty,
    Avatar,
    Radio,
    Select,
    message,
    Tooltip
} from "ant-design-vue";
import "video.js/dist/video-js.css";
import "videojs-contrib-hls";
import "videojs-flash";
import jq from "jquery";
import { aqiQualityTrend, getAllAqiInfo } from "@/api/air";
import { getCompanyList } from "@/api/headvily-pollution";
import { getStationList, fetchCameraUrl, fetchCameraList } from "@/api/water";
import moment from "moment";
import {
    getReportDetails,
    getTaskOverview,
    getTaskRemark,
    getEmergencyStationList,
    getAqiQuality,
    getMonitorItemDetailRecord,
    getTwentyFourHour
} from "@/api/emergency";
import { getEnforcement, getEnforcementTaskId } from "@/api/task-management";
import { keepAlive } from "@/api/vehicles";
import LineEmergencyCommand from "@/components/Charts/LineEmergencyCommand.vue";
import { Swiper, SwiperSlide } from "vue-awesome-swiper";
import { clearInterval, setInterval } from "timers";
import em1 from "@/assets/emergency/em-1.png";
import em11 from "@/assets/emergency/em-1-1.png";
import em2 from "@/assets/emergency/em-2.png";
import em21 from "@/assets/emergency/em-2-1.png";
import em3 from "@/assets/emergency/em-3.png";
import em31 from "@/assets/emergency/em-3-1.png";
import em4 from "@/assets/emergency/em-4.png";
import em41 from "@/assets/emergency/em-4-1.png";
import { socketUrl, socketUrl2 } from "@/utils/index";
@Component({
    name: "EmergencyCommand",
    components: {
        EmergencyMap,
        ATable: Table,
        AIcon: Icon,
        AEmpty: Empty,
        AAvatar: Avatar,
        LineEmergencyCommand,
        ARadioGroup: Radio.Group,
        ARadioButton: Radio.Button,
        Swiper,
        SwiperSlide,
        ASelect: Select,
        ASelectOption: Select.Option,
        ATooltip: Tooltip
    }
})
export default class extends Vue {
    @Watch("swiperIndex", { immediate: true, deep: true })
    public onSwiperIndex(newValue: any, oldValue: any) {
        if (newValue && this.emList.length - 1 == newValue) {
            // this.autoSwiperTimer();
        }
    }
    private defaultRange: any = "3";
    private rangeList: any = [
        {
            name: "监测范围:0.5公里",
            value: "0.5"
        },
        {
            name: "监测范围:1公里",
            value: "1"
        },
        {
            name: "监测范围:2公里",
            value: "2"
        },
        {
            name: "监测范围:3公里",
            value: "3"
        },
        {
            name: "监测范围:4公里",
            value: "4"
        }
    ];
    private rangeObj: any = {
        lng: "",
        lat: "",
        number: 0.5
    };
    private rangeChange(val: any) {
        this.defaultRange = val;
        if (val == 0.5) {
            this.rangeObj = {
                lng: this.reportDetails.longitude,
                lat: this.reportDetails.latitude,
                number: 500
            };
        } else if (val == 1) {
            this.rangeObj = {
                lng: this.reportDetails.longitude,
                lat: this.reportDetails.latitude,
                number: 1000
            };
        } else if (val == 2) {
            this.rangeObj = {
                lng: this.reportDetails.longitude,
                lat: this.reportDetails.latitude,
                number: 2000
            };
        } else if (val == 3) {
            this.rangeObj = {
                lng: this.reportDetails.longitude,
                lat: this.reportDetails.latitude,
                number: 3000
            };
        } else if (val == 4) {
            this.rangeObj = {
                lng: this.reportDetails.longitude,
                lat: this.reportDetails.latitude,
                number: 4000
            };
        }
        this.socket1.send(JSON.stringify({ code: 7, lng: this.reportDetails.lng, lat: this.reportDetails.lat, distance: this.defaultRange  }));
        // this.getEmergencyStationList();
    }
    private showDiv: any = false;
    // 水质站点列表
    private waterStationList: Array<any> = [];
    // 水质类型列表
    private waterTypeList: any = [
        {
            concentrationUnit: "mg/L",
            emissionsUnit: "千克",
            itemCode: "002",
            itemId: "002",
            name: "氨氮"
        },
        {
            concentrationUnit: "mg/L",
            emissionsUnit: "千克",
            itemCode: "003",
            itemId: "003",
            name: "COD"
        },
        {
            concentrationUnit: "mg/L",
            emissionsUnit: "千克",
            itemCode: "004",
            itemId: "004",
            name: "总磷"
        },
        {
            concentrationUnit: "mg/L",
            emissionsUnit: "千克",
            itemCode: "005",
            itemId: "005",
            name: "挥发酚"
        }
    ];
    // 默认水质类型
    private defaultwaterType: any = "002";
    // 水质站点id
    private waterStationId: any = "";
    // 水质下拉框
    private defaultWater: any = "0";
    // 获取站点列表
    private getStationList(): void {
        getStationList('').then((res:any) => {
            if (res.data.data) {
                // this.waterStationList = res.data.data;
                this.waterStationVideoList = res.data.data;
                this.defaultWaterStationValue = res.data.data[0].stationId;
                this.fetchCameraList(this.defaultWaterStationValue);
                // this.defaultwaterType = this.waterTypeList[0].itemCode;
                // this.waterStationId = res.data.data[0].stationId;
                // this.getMonitorItemDetailRecord();
            }
        });
    }
    // 水质radio切换
    private waterTypeChange(val: any) {
        console.log(val.target.value);
        this.defaultwaterType = val.target.value;
        this.getMonitorItemDetailRecord();
    }
    // 水质下拉框
    private waterChange(val: any) {
        console.log(val);
        this.defaultWater = val;
        this.waterStationId = this.waterStationList[val].stationId;
        this.getMonitorItemDetailRecord();
    }
    // 空气站点列表
    private airStationList: any = [];
    // 空气站点id
    private airStationId: any = "";
    // 空气下拉框默认
    private defaultAir: any = "0";
    private getAllAqiInfo() {
        // getAllAqiInfo({ areaCode: "510106" }).then((res: any) => {
        //   console.log("air", res.data.data);
        //   this.airStationList = res.data.data;
        //   this.airStationId = res.data.data[0].stationCode;
        //   this.getAqiTrend();
        // });
    }
    // 空气下拉框
    private airChange(val: any) {
        this.defaultAir = val;
        this.airStationId = this.airStationList[val].stationCode;
        this.getAqiTrend();
    }
    // 重污列表
    private pollutionList: any = [];
    // 重污下拉框默认
    private defaultPollution: any = "0";
    // 重污id
    private pollutionStationId: any = "";
    private getCompanyList() {
        // getCompanyList().then((res: any) => {
        //   console.log("CompanyList", res.data.data);
        //   this.pollutionList = res.data.data;
        //   this.pollutionStationId = res.data.data[0].companyId;
        //   this.getTwentyFourHour();
        // });
    }
    private pollutionChange(val: any) {
        console.log(val);
        this.defaultPollution = val;
        this.pollutionStationId = this.pollutionList[val].companyId;
        this.getTwentyFourHour();
    }
    private swiperVideoOption: any = {
        direction: "vertical",
        slidesPerView: 2,
        slidesPerGroup: 1,
        loop: true,
        autoplay: {
            delay: 3500,
            disableOnInteraction: false
        },
        navigation: {
            nextEl: ".right-arrow",
            prevEl: ".left-arrow"
        }
    };
    private swiperTimer: any = null;
    private emList: any = [em1, em2, em3, em4];
    private emListActive: any = [em11, em21, em31, em41];
    private emActive: any = em11;
    private checkEmImg(index: any) {
        this.emActive = this.emListActive[index];
        clearInterval(this.swiperTimer);
        this.swiperIndex = index;
        this.autoSwiperTimer();
    }
    private swiperIndex: any = 0;
    autoSwiperTimer() {
        this.swiperTimer = setInterval(() => {
            if (this.swiperIndex > this.emList.length - 1) {
                clearInterval(this.swiperTimer);
                this.swiperIndex = 0;
                this.checkEmImg(this.swiperIndex);
            }
            this.checkEmImg(this.swiperIndex);
            this.swiperIndex++;
        }, 3500);
    }
    private swiperOption: any = {
        loop: true,
        loopFillGroupWithBlank: true,
        pagination: {
            el: ".swiper-pagination",
            clickable: true
        },
        autoplay: {
            delay: 3000,
            disableOnInteraction: false
        },
        navigation: {
            nextEl: ".right-arrow",
            prevEl: ".left-arrow"
        }
    };
    private playerOptions: any = {
        autoplay: false, //如果true,浏览器准备好时开始回放。
        muted: false, // 默认情况下将会消除任何音频。
        loop: false, // 导致视频一结束就重新开始。
        preload: "auto", // 建议浏览器在<video>加载元素后是否应该开始下载视频数据。auto浏览器选择最佳行为,立即开始加载视频（如果浏览器支持）
        language: "zh-CN",
        languages: {
            "zh-CN": {
                Play: "播放",
                Pause: "暂停",
                "Current Time": "当前时间",
                Duration: "时长",
                "Remaining Time": "剩余时间",
                "Stream Type": "媒体流类型",
                LIVE: "直播",
                Loaded: "加载完毕",
                Progress: "进度",
                Fullscreen: "全屏",
                "Non-Fullscreen": "退出全屏",
                Mute: "静音",
                Unmute: "取消静音",
                "Playback Rate": "播放速度",
                Subtitles: "字幕",
                "subtitles off": "关闭字幕",
                Captions: "内嵌字幕",
                "captions off": "关闭内嵌字幕",
                Chapters: "节目段落",
                "Close Modal Dialog": "关闭弹窗",
                Descriptions: "描述",
                "descriptions off": "关闭描述",
                "Audio Track": "音轨",
                "You aborted the media playback": "视频播放被终止",
                "A network error caused the media download to fail part-way.":
                    "网络错误导致视频下载中途失败。",
                "The media could not be loaded, either because the server or network failed or because the format is not supported.":
                    "视频因格式不支持或者服务器或网络的问题无法加载。",
                "The media playback was aborted due to a corruption problem or because the media used features your browser did not support.":
                    "由于视频文件损坏或是该视频使用了你的浏览器不支持的功能，播放终止。",
                "No compatible source was found for this media.":
                    "无法找到此视频兼容的源。",
                "The media is encrypted and we do not have the keys to decrypt it.":
                    "视频已加密，无法解密。",
                // "Play Video": "播放视频",
                Close: "关闭",
                "Modal Window": "弹窗",
                "This is a modal window": "这是一个弹窗",
                "This modal can be closed by pressing the Escape key or activating the close button.":
                    "可以按ESC按键或启用关闭按钮来关闭此弹窗。",
                ", opens captions settings dialog": ", 开启标题设置弹窗",
                ", opens subtitles settings dialog": ", 开启字幕设置弹窗",
                ", opens descriptions settings dialog": ", 开启描述设置弹窗",
                ", selected": ", 选择",
                "captions settings": "字幕设定",
                "Audio Player": "音频播放器",
                "Video Player": "视频播放器",
                Replay: "重播",
                "Progress Bar": "进度小节",
                "Volume Level": "音量",
                "subtitles settings": "字幕设定",
                "descriptions settings": "描述设定",
                Text: "文字",
                White: "白",
                Black: "黑",
                Red: "红",
                Green: "绿",
                Blue: "蓝",
                Yellow: "黄",
                Magenta: "紫红",
                Cyan: "青",
                Background: "背景",
                Window: "视窗",
                Transparent: "透明",
                "Semi-Transparent": "半透明",
                Opaque: "不透明",
                "Font Size": "字体尺寸",
                "Text Edge Style": "字体边缘样式",
                None: "无",
                Raised: "浮雕",
                Depressed: "压低",
                Uniform: "均匀",
                Dropshadow: "下阴影",
                "Font Family": "字体库",
                "Proportional Sans-Serif": "比例无细体",
                "Monospace Sans-Serif": "单间隔无细体",
                "Proportional Serif": "比例细体",
                "Monospace Serif": "单间隔细体",
                Casual: "舒适",
                Script: "手写体",
                "Small Caps": "小型大写字体",
                Reset: "重启",
                "restore all settings to the default values": "恢复全部设定至预设值",
                Done: "完成",
                "Caption Settings Dialog": "字幕设定视窗",
                "Beginning of dialog window. Escape will cancel and close the window.":
                    "开始对话视窗。离开会取消及关闭视窗",
                "End of dialog window.": "结束对话视窗"
            }
        },
        aspectRatio: "16:9", // 将播放器置于流畅模式，并在计算播放器的动态大小时使用该值。值应该代表一个比例 - 用冒号分隔的两个数字（例如"16:9"或"4:3"）
        fluid: true, // 当true时，Video.js player将拥有流体大小。换句话说，它将按比例缩放以适应其容器。
        //application/x-mpegURL-m3u8 video/mp4-mp4 rtmp/mp4-rtmp flv-application/octet-stream-flv
        sources: [
            {
                type: "flv-application/octet-stream",
                src: ""
            },
            {
                type: "rtmp/mp4",
                src: ""
            },
            {
                type: "application/x-mpegURL-m3u8",
                src: ""
            }
        ],
        poster: this.emActive,
        width: document.documentElement.clientWidth,
        // notSupportedMessage: "该站点暂未设置监控，请查看其它站点" //允许覆盖Video.js无法播放媒体源时显示的默认信息。
        notSupportedMessage: " " //允许覆盖Video.js无法播放媒体源时显示的默认信息。
    };
    private playerOptions1: any = {
        autoplay: true, //如果true,浏览器准备好时开始回放。
        muted: true, // 默认情况下将会消除任何音频。
        loop: false, // 导致视频一结束就重新开始。
        preload: "auto", // 建议浏览器在<video>加载元素后是否应该开始下载视频数据。auto浏览器选择最佳行为,立即开始加载视频（如果浏览器支持）
        language: "zh-CN",
        languages: {
            "zh-CN": {
                Play: "播放",
                Pause: "暂停",
                "Current Time": "当前时间",
                Duration: "时长",
                "Remaining Time": "剩余时间",
                "Stream Type": "媒体流类型",
                LIVE: "直播",
                Loaded: "加载完毕",
                Progress: "进度",
                Fullscreen: "全屏",
                "Non-Fullscreen": "退出全屏",
                Mute: "静音",
                Unmute: "取消静音",
                "Playback Rate": "播放速度",
                Subtitles: "字幕",
                "subtitles off": "关闭字幕",
                Captions: "内嵌字幕",
                "captions off": "关闭内嵌字幕",
                Chapters: "节目段落",
                "Close Modal Dialog": "关闭弹窗",
                Descriptions: "描述",
                "descriptions off": "关闭描述",
                "Audio Track": "音轨",
                "You aborted the media playback": "视频播放被终止",
                "A network error caused the media download to fail part-way.":
                    "网络错误导致视频下载中途失败。",
                "The media could not be loaded, either because the server or network failed or because the format is not supported.":
                    "视频因格式不支持或者服务器或网络的问题无法加载。",
                "The media playback was aborted due to a corruption problem or because the media used features your browser did not support.":
                    "由于视频文件损坏或是该视频使用了你的浏览器不支持的功能，播放终止。",
                "No compatible source was found for this media.":
                    "无法找到此视频兼容的源。",
                "The media is encrypted and we do not have the keys to decrypt it.":
                    "视频已加密，无法解密。",
                // "Play Video": "播放视频",
                Close: "关闭",
                "Modal Window": "弹窗",
                "This is a modal window": "这是一个弹窗",
                "This modal can be closed by pressing the Escape key or activating the close button.":
                    "可以按ESC按键或启用关闭按钮来关闭此弹窗。",
                ", opens captions settings dialog": ", 开启标题设置弹窗",
                ", opens subtitles settings dialog": ", 开启字幕设置弹窗",
                ", opens descriptions settings dialog": ", 开启描述设置弹窗",
                ", selected": ", 选择",
                "captions settings": "字幕设定",
                "Audio Player": "音频播放器",
                "Video Player": "视频播放器",
                Replay: "重播",
                "Progress Bar": "进度小节",
                "Volume Level": "音量",
                "subtitles settings": "字幕设定",
                "descriptions settings": "描述设定",
                Text: "文字",
                White: "白",
                Black: "黑",
                Red: "红",
                Green: "绿",
                Blue: "蓝",
                Yellow: "黄",
                Magenta: "紫红",
                Cyan: "青",
                Background: "背景",
                Window: "视窗",
                Transparent: "透明",
                "Semi-Transparent": "半透明",
                Opaque: "不透明",
                "Font Size": "字体尺寸",
                "Text Edge Style": "字体边缘样式",
                None: "无",
                Raised: "浮雕",
                Depressed: "压低",
                Uniform: "均匀",
                Dropshadow: "下阴影",
                "Font Family": "字体库",
                "Proportional Sans-Serif": "比例无细体",
                "Monospace Sans-Serif": "单间隔无细体",
                "Proportional Serif": "比例细体",
                "Monospace Serif": "单间隔细体",
                Casual: "舒适",
                Script: "手写体",
                "Small Caps": "小型大写字体",
                Reset: "重启",
                "restore all settings to the default values": "恢复全部设定至预设值",
                Done: "完成",
                "Caption Settings Dialog": "字幕设定视窗",
                "Beginning of dialog window. Escape will cancel and close the window.":
                    "开始对话视窗。离开会取消及关闭视窗",
                "End of dialog window.": "结束对话视窗"
            }
        },
        aspectRatio: "16:9", // 将播放器置于流畅模式，并在计算播放器的动态大小时使用该值。值应该代表一个比例 - 用冒号分隔的两个数字（例如"16:9"或"4:3"）
        fluid: true, // 当true时，Video.js player将拥有流体大小。换句话说，它将按比例缩放以适应其容器。
        //application/x-mpegURL-m3u8 video/mp4-mp4 rtmp/mp4-rtmp flv-application/octet-stream-flv
        sources: [
            {
                type: "flv-application/octet-stream",
                src: ""
            },
            {
                type: "rtmp/mp4",
                src: ""
            },
            {
                type: "application/x-mpegURL-m3u8",
                src: ""
            }
        ],
        poster: "",
        width: document.documentElement.clientWidth,
        notSupportedMessage: "该站点暂未设置监控，请查看其它站点" //允许覆盖Video.js无法播放媒体源时显示的默认信息。
    };
    private mapStyle = "amap://styles/0739a6632d849ae1b5e6f1c52f7d12c3";
    private emergencyList: any = [];
    private taskList: any = [
        {
            timec: 1564503166555,
            time: 1564533166555,
            top: "-1.235rem"
        },
        {
            timec: 1564503166555,
            time: 1564538366555,
            top: "-0.985rem"
        },
        {
            timec: 1564503166555,
            time: 1564536166555,
            top: "-0.48rem"
        },
        {
            timec: 1564503166555,
            time: 1564535166555,
            top: "-0.735rem"
        }
    ];
    created() {
        // this.autoSwiperTimer();
    }
    mounted() {
        // this.getReportDetails();
        // this.getTaskRemark();
        this.connect();
        this.connect1();
        this.getStationList();
        this.getAllAqiInfo();
        this.getCompanyList();
        // this.getEmergencyStationList();
        (window as any)._that = this;
        // this.getTaskOverview();
        // this.timer = setInterval(() => {
        //     this.getReportDetails();
        //     this.getTaskOverview();
        //     this.getTaskRemark();
        // }, 15 * 60 * 1000);
        //@ts-ignore
        // eslint-disable-next-line no-undef
        this.$bus.on("stationType", (res: any) => {
            console.log(res[0]);
            // this.displayState = false;
            // this.mapViewCenter = this.displayState
            //   ? {
            //       lng: 104.04,
            //       lat: 30.725
            //     }
            //   : {
            //       lng: 104.04,
            //       lat: 30.685
            //     };
            if (res[0].type == 1) {
                // 空气
                this.airStationId = res[0].stationId;
                this.getAqiTrend();
                this.defaultAir = String(
                    this.airStationList.findIndex((item: any, index: any) => {
                        return res[0].stationId == item.stationCode;
                    })
                );
            } else if (res[0].type == 2) {
                // 水
                this.waterStationId = res[0].stationId;
                this.getMonitorItemDetailRecord();
                this.defaultWater = String(
                    this.waterStationList.findIndex((item: any, index: any) => {
                        return res[0].stationId == item.stationId;
                    })
                );
            } else if (res[0].type == 3) {
                // 重污
                this.pollutionStationId = res[0].stationId;
                this.getTwentyFourHour();
                this.defaultPollution = String(
                    this.pollutionList.findIndex((item: any, index: any) => {
                        return res[0].stationId == item.companyId;
                    })
                );
                console.log(this.defaultPollution);
            }
        });
        // eslint-disable-next-line @typescript-eslint/no-this-alias
        const _this = this;
        jq(function() {
            jq("#times").mousemove((e: any) => {
                _this.pageX = e.pageX;
                jq(".line-five").css({
                    left: e.pageX - 60
                });
            });
            // jq(".charts-content").mousemove((e: any) => {
            //   _this.pageX = e.pageX;
            //   jq(".line-five").css({
            //     left: e.pageX - 60
            //   });
            // });
        });
    }
    beforeDestroy() {
        window.clearInterval(this.timer);
        window.clearInterval(this.requestTime);
        window.clearInterval(this.swiperTimer);
        this.socket.close();

        if(this.$refs.livePlayer) {
            // @ts-ignore
            this.$refs.livePlayer.dispose()
        }
        if (this.flvPlayer) {
            this.flvPlayer.pause();
            this.flvPlayer.unload();
            this.flvPlayer.detachMediaElement();
            this.flvPlayer.destroy();
            this.flvPlayer = null;
        }
        // @ts-ignore
        this.$bus.off("stationType")
        (window as any)._that = null;
    }
    private timer: any = null;
    private siteType: any = 1;
    private taskDetailState: any = false;
    private pageX = 2000;
    private timeLineDataTime: any = {
        startTime: "",
        endTime: ""
    };
    private timeLineTimes: any = moment(
        new Date(new Date().valueOf() - 86400000)
    ).format("YYYY-MM-DD HH:00:00");
    // private timeLineTimes: any = "2020-07-07 22:00:00";
    private htmlLoad(startTime: any, endTime: any) {
        // eslint-disable-next-line @typescript-eslint/no-this-alias
        const _this = this;
        let _move = false;
        let _x: any;
        let dataTime: any = "";
        jq(".tttt")
            .click(function() {
                // 1;
            })
            .mousedown(function(e: any) {
                _move = true;
                _x = e.pageX - parseInt(jq("#time").css("left"));
                jq("#time").fadeTo(20, 1);
            });
        jq(".tttt")
            .mousemove(function(e: any) {
                // _this.pageX = e.pageX;
                if (_move) {
                    const x = e.pageX - _x;
                    jq("#time").css({
                        left: x
                    });
                    if (x > 0) {
                        jq("#time").css({
                            left: 0
                        });
                        dataTime = moment(
                            new Date(Number(_this.timeLineDataTime.startTime) + 86400000)
                        ).format("YYYY-MM-DD HH:00:00");
                        _this.timeLineTimes = dataTime;
                    } else if (jq("#time").width() - jq("#times").width() + x < 0) {
                        jq("#time").css({
                            left: -(jq("#time").width() - jq("#times").width())
                        });
                    } else if (x < 0) {
                        const number =
                            (Math.ceil(Math.abs(x) / (jq("#times").width() / 24)) + 24 - 1) *
                            3600000;
                        dataTime = moment(
                            new Date(Number(_this.timeLineDataTime.startTime) + number)
                        ).format("YYYY-MM-DD HH:00:00");
                        _this.timeLineTimes = dataTime;
                    }
                }
            })
            .mouseup(function() {
                // 水质
                _this.getMonitorItemDetailRecord();
                // 空气
                _this.getAqiTrend();
                // 重污
                _this.getTwentyFourHour();
                _move = false;
                jq(".tttt").fadeTo("fast", 1);
            });
        this.time(startTime, endTime);
    }
    private time(hour: any, endTime: any) {
        const date = endTime;
        const timess = (date - hour) / 1000 / 60;
        console.log("timess", timess);
        for (let i = 0; i < Math.floor(timess); i++) {
            let time1: any = "";
            if (i % 5 == 0) {
                time1 = `<li style='left:${i * 0.0083}rem;'></li>`;
            }
            const house: any =
                new Date(hour + i * 60000).getHours() >= 10
                    ? new Date(hour + i * 60000).getHours()
                    : "0" + new Date(hour + i * 60000).getHours();
            const minutes: any =
                new Date(hour + i * 60000).getMinutes() >= 10
                    ? new Date(hour + i * 60000).getMinutes()
                    : "0" + new Date(hour + i * 60000).getMinutes();
            const sxsx = house + ":" + minutes;
            if (minutes == "00") {
                time1 = `<li style='left:${i *
                0.0083}rem;top:-0.04rem;height:0.2rem;line-height:0.5rem;text-indent:-0.04rem;'>${sxsx}</li>`;
            }
            jq("#time").append(time1);
        }
        jq("#time").css({
            width: Math.floor(timess) * 0.0083 + "rem"
        });
    }
    private addtimes(data: any, endTime: any, startTime: any) {
        // data.conetnt = '我是刘磊 已完成'
        // data.replyContent = '我是徐玲  我已经完成'
        const replyContent = data.replyContent.replace(/\s/g, "");
        const content = data.content;
        const str: any = `'${data.departmentName}---${data.time}---${content}---${replyContent}'`;
        console.log(str, 'ppp')
        // console.log(str);
        const timess: any = Math.floor((data.time - data.timec) / 1000 / 60);
        const time1: any = `<li class='time1' style='left:${timess *
        0.0083}rem;top:${data.top}' onclick=taskDetail(${str})></li>`;
        jq("#time").append(time1);
        if (endTime - startTime > 90000000) {
            jq("#time").css({
                left: "-" + (timess * 0.0083 - 11.5) + "rem"
            });
        }
    }
    // 空气质量趋势
    private airData: any = {
        bottomList: [],
        dataList: [],
        unit: "",
        colorType: ""
    };
    // 空气质量趋势type
    private airTypeLIst: any[] = [
        {
            code: "104",
            name: "PM₁₀"
        },
        {
            code: "102",
            name: "O₃"
        },
        {
            code: "101",
            name: "NO₂"
        },
        {
            code: "105",
            name: "PM₂.₅"
        },
        {
            code: "100",
            name: "SO₂"
        },
        {
            code: "103",
            name: "CO"
        }
    ];
    private airType = "104";
    // 24小时空气质趋势
    private getAqiTrend() {
        getAqiQuality(this.airStationId, this.airType, this.timeLineTimes).then(
            (res: any) => {
                const airData: any = {
                    bottomList: [],
                    dataList: [],
                    unit: "",
                    colorType: ""
                };
                if (res.data.data) {
                    for (const item of res.data.data) {
                        airData.bottomList.push(item.time);
                        airData.dataList.push(item.value);
                        if (this.airType == "103") {
                            airData.unit = "mg/m³";
                        } else {
                            airData.unit = "μg/m³";
                        }
                        if (this.airType == "100") {
                            airData.colorType = "SO2";
                        } else if (this.airType == "101") {
                            airData.colorType = "NO2";
                        } else if (this.airType == "102") {
                            airData.colorType = "O3";
                        } else if (this.airType == "103") {
                            airData.colorType = "CO";
                        } else if (this.airType == "104") {
                            airData.colorType = "PM10";
                        } else if (this.airType == "105") {
                            airData.colorType = "PM25";
                        }
                    }
                }
                this.airData = airData;
            }
        );
    }
    // 切换标签
    private airTypeChange(e: any) {
        this.airType = e.target.value;
        this.getAqiTrend();
    }
    private displayState = false;
    // 地图视图中心点
    private mapViewCenter: any = {
        lng: 104.04,
        lat: 30.685
    };
    private fullScreen() {
        this.displayState = !this.displayState;
        this.mapViewCenter = this.displayState
            ? {
                lng: 104.04,
                lat: 30.725
            }
            : {
                lng: 104.04,
                lat: 30.685
            };
    }
    private taskId: any = "";
    // 上报详情
    private reportDetails: any = {};
    private getReportDetails() {
        getReportDetails().then((res: any) => {
            if (res.data.data.levelId == 1) {
                res.data.data.levelId = "一";
            } else if (res.data.data.levelId == 2) {
                res.data.data.levelId = "二";
            } else if (res.data.data.levelId == 3) {
                res.data.data.levelId = "三";
            } else if (res.data.data.levelId == 4) {
                res.data.data.levelId = "四";
            }
            this.taskId = res.data.data.id;
            const geocoder = new AMap.Geocoder({
                city: "全国"
            });
            geocoder.getAddress(
                [res.data.data.longitude, res.data.data.latitude],
                (status: any, result: any) => {
                    if (status === "complete" && result.regeocode) {
                        const address = result.regeocode.formattedAddress;
                        res.data.data.address = address;
                        this.reportDetails = res.data.data;
                        this.rangeObj = {
                            lng: this.reportDetails.longitude,
                            lat: this.reportDetails.latitude,
                            number: 3000
                        };
                    }
                }
            );
        });
    }
    // 任务概览
    private taskOverview: any = {};
    private getTaskOverview() {
        getTaskOverview().then((res: any) => {
            this.taskOverview = res.data.data;
        });
    }
    // 任务反馈时间轴显示
    private taskRemark: any = {};
    private getTaskRemark() {
        getTaskRemark().then((res: any) => {
            this.taskRemark = res.data.data;
            const taskList: any[] = [];
            for (const item of res.data.data) {
                if (
                    !item.departmentName.indexOf("生态") ||
                    !item.departmentName.indexOf("环境")
                ) {
                    // top: "0.38rem;"  生态环境局
                    for (const task of item.remarkList) {
                        task.time = new Date(task.createTime).getTime();
                        task.top = "0.38rem;";
                        task.departmentName = item.departmentName;
                        taskList.push(task);
                    }
                } else if (
                    !item.departmentName.indexOf("综合") ||
                    !item.departmentName.indexOf("执法")
                ) {
                    // top: "0.58rem"   综合行政执法局
                    for (const task of item.remarkList) {
                        task.time = new Date(task.createTime).getTime();
                        task.top = "0.58rem";
                        task.departmentName = item.departmentName;
                        taskList.push(task);
                    }
                } else if (
                    !item.departmentName.indexOf("农业") ||
                    !item.departmentName.indexOf("水务")
                ) {
                    // top: "0.78rem"  农业和水务局
                    for (const task of item.remarkList) {
                        task.time = new Date(task.createTime).getTime();
                        task.top = "0.78rem";
                        task.departmentName = item.departmentName;
                        taskList.push(task);
                    }
                } else if (
                    !item.departmentName.indexOf("住房") ||
                    !item.departmentName.indexOf("交通")
                ) {
                    // top: "0.98rem"  住房建筑和交通运输局
                    for (const task of item.remarkList) {
                        task.time = new Date(task.createTime).getTime();
                        task.top = "0.98rem";
                        task.departmentName = item.departmentName;
                        taskList.push(task);
                    }
                }
            }
            if (taskList.length !== 0) {
                const sort: any = taskList.sort((a, b) => {
                    return a.time - b.time;
                });
                // 时间轴开始时间
                let startTime: any = "";
                // 时间轴结束时间
                let endTime: any = "";
                if (sort.slice(-1)[0].time - sort[0].time > 90000000) {
                    // 开始时间大于结束时间24小时
                    startTime = new Date(
                        moment(new Date(Number(sort[0].time))).format("YYYY-MM-DD HH:00:00")
                    ).valueOf();
                    endTime =
                        new Date(
                            moment(new Date(Number(sort.slice(-1)[0].time))).format(
                                "YYYY-MM-DD HH:00:00"
                            )
                        ).valueOf() + 8000000;
                } else {
                    // 开始时间小于结束时间24小时
                    startTime = new Date(
                        moment(new Date(Number(sort[0].time))).format("YYYY-MM-DD HH:00:00")
                    ).valueOf();
                    endTime =
                        new Date(
                            moment(new Date(Number(sort[0].time))).format(
                                "YYYY-MM-DD HH:00:00"
                            )
                        ).valueOf() + 90000000;
                }
                this.timeLineDataTime = {
                    startTime,
                    endTime
                };
                this.timeLineTimes = moment(
                    new Date(Number(this.timeLineDataTime.startTime) + 86400000)
                ).format("YYYY-MM-DD HH:00:00");
                for (const item of taskList) {
                    item.timec = startTime;
                }
                this.taskList = taskList;
                jq(".time1").remove();
                this.$nextTick(() => {
                    this.htmlLoad(startTime, endTime);
                    for (const item of this.taskList) {
                        this.addtimes(item, sort.slice(-1)[0].time, sort[0].time);
                    }
                });
            }
            this.getEmergencyStationList();
        });
    }
    private creatTime: any = "";
    private departmentName: any = "";
    private content: any = "";
    private replyContent: any = "";
    // 水质监测数据
    private waterMonitor: any = {
        bottomList: [],
        dataList: [],
        unit: "mg/L"
    };
    // 水质实时监测数据btn选择
    private waterMonitorSelectChange(e: any): void {
        this.airType = e.target.value;
    }
    // 获取水质监测项详情记录
    private getMonitorItemDetailRecord(): void {
        getMonitorItemDetailRecord(
            this.waterStationId,
            this.defaultwaterType,
            this.timeLineTimes
        ).then(res => {
            if (res.data.data) {
                const data = res.data.data;
                this.waterMonitor.bottomList = [];
                this.waterMonitor.dataList = [];
                for (const item of data) {
                    item.time = moment(new Date(item.time)).format("YYYY-MM-DD HH:00:00");
                    this.waterMonitor.bottomList.push(item.time);
                    this.waterMonitor.dataList.push(item.value);
                }
            }
        });
    }
    // 空气质量趋势
    private pollutionData: any = {
        bottomList: [],
        dataList: [],
        unit: "",
        colorType: ""
    };
    // 获取重点污染源
    private getTwentyFourHour() {
        getTwentyFourHour(this.pollutionStationId, this.timeLineTimes).then(
            (res: any) => {
                const pollutionData: any = {
                    bottomList: [],
                    dataList: [],
                    unit: "",
                    colorType: ""
                };
                if (res.data.data) {
                    for (const item of res.data.data) {
                        pollutionData.bottomList.push(item.time);
                        pollutionData.dataList.push(item.value);
                        if (this.airType == "103") {
                            pollutionData.unit = "mg/m³";
                        } else {
                            pollutionData.unit = "μg/m³";
                        }
                        if (this.airType == "100") {
                            pollutionData.colorType = "SO2";
                        } else if (this.airType == "101") {
                            pollutionData.colorType = "NO2";
                        } else if (this.airType == "102") {
                            pollutionData.colorType = "O3";
                        } else if (this.airType == "103") {
                            pollutionData.colorType = "CO";
                        } else if (this.airType == "104") {
                            pollutionData.colorType = "PM10";
                        } else if (this.airType == "105") {
                            pollutionData.colorType = "PM25";
                        }
                    }
                }
                this.pollutionData = pollutionData;
            }
        );
    }
    // 获取地图的站点信息
    private emergencyStationList: any[] = [];
    private getEmergencyStationList() {
        getEmergencyStationList(this.taskId, this.defaultRange).then((res: any) => {
            this.emergencyStationList = res.data.data;
            console.log("getEmergencyStationList", res.data.data);
            console.log("getEmergencyStationList", res.data.data);
            this.waterStationList = [];
            this.airStationList = [];
            this.pollutionList = [];
            for (const item of res.data.data) {
                if (item.stationType == 1) {
                    // 空气
                    this.airStationList.push({
                        stationCode: item.stationCode,
                        positionName: item.stationName
                    });
                } else if (item.stationType == 2) {
                    // 水
                    this.waterStationList.push({
                        stationId: item.stationCode,
                        stationName: item.stationName
                    });
                } else if (item.stationType == 3) {
                    // 重污
                    this.pollutionList.push({
                        companyId: item.stationCode,
                        companyName: item.stationName
                    });
                }
            }
            if (this.waterStationList.length > 0) {
                this.waterStationId = this.waterStationList[0].stationId;
                this.getMonitorItemDetailRecord();
            }
            if (this.airStationList.length > 0) {
                this.airStationId = this.airStationList[0].stationCode;
                this.getAqiTrend();
            }
            if (this.pollutionList.length > 0) {
                this.pollutionStationId = this.pollutionList[0].companyId;
                this.getTwentyFourHour();
            }

            console.log("水", this.waterStationList);
            console.log("空气", this.airStationList);
            console.log("重污", this.pollutionList);
        });
    }
    // 水站点列表(摄像头)
    private waterStationVideoList: any = [];
    // 水站点摄像头列表
    private currentCameraList: Array<any> = [];
    // 默认选中水摄像头
    private defaultCameraValue = "";
    private defaultWaterStationValue: any = "";
    // 水质监控站点切换
    private waterStationChange(value: string): void {
        this.fetchCameraList(value);
        this.defaultWaterStationValue = value + "";
        // 重置一次摄像头数据
        this.currentCameraList = [];
        this.defaultCameraValue = "";
        this.playerOptions1.sources = [
            {
                src: null,
                type: null
            }
        ];
    }
    // 获取摄像头列表
    private fetchCameraList(station: string): void {
        fetchCameraList(station).then(res => {
            if (res.data.data && res.data.data.length > 0) {
                this.currentCameraList = res.data.data;
                this.defaultCameraValue = this.currentCameraList[0].monitorId;
                this.fetchCameraUrl(
                    this.defaultCameraValue,
                    this.currentCameraList[0].snapUrl
                );
            } else {
                this.currentCameraList = [];
                this.defaultCameraValue = "";
                this.playerOptions1.sources = [
                    {
                        src: "",
                        type: "application/x-mpegURL"
                    }
                ];
                this.playerOptions1.poster = "";
            }
        });
    }
    // 水质监控站点摄像头切换
    private waterCameraChange(value: any): void {
        const camera = this.currentCameraList.find((item: any, index: any) => {
            if (item.monitorId == value) {
                return item;
            }
        });
        this.fetchCameraUrl(value, camera.snapUrl);
    }
    // 获取摄像头播放地址
    private fetchCameraUrl(channelId: string, snapUrl: any): void {
        fetchCameraUrl(channelId).then(res => {
            // 清除上一次地址
            this.playerOptions1.sources = [
                {
                    src: "",
                    type: "application/x-mpegURL"
                }
            ];
            if (res.data.data.hlsHttps) {
                this.playerOptions1.sources.push({
                    type: "application/x-mpegURL",
                    src: res.data.data.hlsHttps
                });
            } else if (res.data.data.flvHttps) {
                this.playerOptions1.sources.push({
                    type: "flv-application/octet-stream",
                    src: res.data.data.flvHttps
                });
            } else if (res.data.data.rtmp) {
                this.playerOptions1.sources.push({
                    type: "rtmp/mp4",
                    src: res.data.data.rtmp
                });
            } else {
                this.playerOptions1.sources = [
                    {
                        src: "",
                        type: "application/x-mpegURL"
                    }
                ];
            }
            if (snapUrl) {
                this.playerOptions1.poster = snapUrl;
            } else {
                this.playerOptions1.poster = "";
            }
        });
    }
    // 更多视频
    toRouter(router: any) {
        this.$router.push(router);
    }
    private playerStateChanged(playerCurrentState: any) {
        // if (playerCurrentState.error) {
        // }
    }
    private socket: any = null;
    private socket1: any = null;
    // 建立连接
    private connect() {
        // this.socket = new WebSocket("ws://192.168.0.135:22000/ws");
        // this.socket = new WebSocket("ws://ep.vankeytech.com:8835/ws");
        // this.socket = new WebSocket("ws://www.jinnq.com:8834/ws");
        this.socket = new WebSocket(socketUrl());
        // 监听socket连接
        this.socket.onopen = this.open;
        // 监听socket错误信息
        this.socket.onerror = this.error;
        // 监听socket消息
        this.socket.onmessage = this.getMessage;
        window.onbeforeunload = () => {
            this.socket.onopen = () => {}
            this.socket.onerror = () => {}
            this.socket.onmessage = () => {}
            this.socket.close();
        };
    }
    private open() {
        console.log("socket连接成功");
        this.send();
    }
    private error() {
        if(location.hash.includes('emergencyCommand')){
            message.error("系统连接错误");
        }
    }
    private send() {
        this.socket.send(JSON.stringify({ code: 1, token: "hello1234" }));
        this.socket.send(JSON.stringify({ code: 11 }));
    }
    // 建立连接
    private connect1() {
        // this.socket1 = new WebSocket("ws://192.168.0.135:22000/ws");
        // this.socket1 = new WebSocket("ws://ep.vankeytech.com:8835/ws");
        // this.socket1 = new WebSocket("ws://www.jinnq.com:8834/ws");
        this.socket1 = new WebSocket(socketUrl2());
        // 监听socket连接
        this.socket1.onopen = this.open1;
        // 监听socket错误信息
        this.socket1.onerror = this.error1;
        // 监听socket消息
        this.socket1.onmessage = this.getMessage1;
        window.onbeforeunload = () => {
            this.socket1.onopen = () => {}
            this.socket1.onerror = () => {}
            this.socket1.onmessage = () => {}
            this.socket1.close();
        };
    }
    private open1() {
        this.send1();
    }
    private error1() {
        if(location.hash.includes('emergencyCommand')){
            message.error("系统连接错误");
        }
    }
    private send1() {
        this.socket1.send(JSON.stringify({ code: 5 }));
    }
    private melliageData:any= {}
    private getMessage1(msg: any) {
        const res: any = JSON.parse(msg.data);
        if (res.code === -8) {
            if (res.distance > 1000) {
                res.distance = (res.distance / 1000).toFixed(2)
                res.type = 1
            }
            this.melliageData = res
        }
        if (res.code === -5) {
            if (this.carId) {
                this.socket1.send(JSON.stringify({ code: 8, taskId: res.emergencyTask.taskId, carId: this.carId }));
            }
            res.emergencyTask.levelId = res.emergencyTask.emergencyLevel
            this.socket1.send(JSON.stringify({ code: 7, lng: res.emergencyTask.lng, lat: res.emergencyTask.lat, distance: this.defaultRange  }));
            if (res.emergencyTask.levelId == 1) {
                res.emergencyTask.levelId = "一";
            } else if (res.emergencyTask.levelId == 2) {
                res.emergencyTask.levelId = "二";
            } else if (res.emergencyTask.levelId == 3) {
                res.emergencyTask.levelId = "三";
            } else if (res.emergencyTask.levelId == 4) {
                res.emergencyTask.levelId = "四";
            }
            this.taskId = res.emergencyTask.taskId;
            res.emergencyTask.longitude =  res.emergencyTask.lng
            res.emergencyTask.latitude =  res.emergencyTask.lat
            this.reportDetails = res.emergencyTask
            this.rangeObj = {
                lng: this.reportDetails.lng,
                lat: this.reportDetails.lat,
                number: 3000
            };
            this.taskRemark = res.executiveDepartmentList;
            const taskList: any[] = [];
            (res.executiveDepartmentList || []).forEach((item:any, index:any) => {
                for (const task of item.feedbackList) {
                    task.time = new Date(task.time).getTime();
                    task.top = index == 0 ? "0.38rem;" : index == 1 ? "0.58rem;" : index == 2 ? "0.78rem;" : "0.98rem;";
                    task.departmentName = item.departmentName;
                    task.replyContent = task.remark
                    taskList.push(task);
                }
            })
            console.log(taskList, 'taskList')
            // for (const item of res.executiveDepartmentList) {
            //     if (item.departmentName.indexOf("生态") > -1 && item.departmentName.indexOf("环境") > -1) {
            //         // top: "0.38rem;"  生态环境局
            //         for (const task of item.feedbackList) {
            //             task.time = new Date(task.time).getTime();
            //             task.top = "0.38rem;";
            //             task.departmentName = item.departmentName;
            //             task.replyContent = task.remark
            //             taskList.push(task);
            //         }
            //     } else if (item.departmentName.indexOf("综合") > -1 && item.departmentName.indexOf("执法") > -1) {
            //         // top: "0.58rem"   综合行政执法局
            //         for (const task of item.feedbackList) {
            //             task.time = new Date(task.time).getTime();
            //             task.top = "0.58rem";
            //             task.departmentName = item.departmentName;
            //             task.replyContent = task.remark
            //             taskList.push(task);
            //         }
            //     } else if (item.departmentName.indexOf("农业") > -1 && item.departmentName.indexOf("水务") > -1) {
            //         // top: "0.78rem"  农业和水务局
            //         for (const task of item.feedbackList) {
            //             task.time = new Date(task.time).getTime();
            //             task.top = "0.78rem";
            //             task.departmentName = item.departmentName;
            //             task.replyContent = task.remark
            //             taskList.push(task);
            //         }
            //     } else if (item.departmentName.indexOf("住建") > -1) {
            //         // top: "0.98rem"  住房建筑和交通运输局
            //         for (const task of item.feedbackList) {
            //             task.time = new Date(task.time).getTime();
            //             task.top = "0.98rem";
            //             task.departmentName = item.departmentName;
            //             task.replyContent = task.remark
            //             taskList.push(task);
            //         }
            //     }
            // }
            if (taskList.length !== 0) {
                const sort: any = taskList.sort((a, b) => {
                    return a.time - b.time;
                });
                // 时间轴开始时间
                let startTime: any = "";
                // 时间轴结束时间
                let endTime: any = "";
                if (sort.slice(-1)[0].time - sort[0].time > 90000000) {
                    // 开始时间大于结束时间24小时
                    startTime = new Date(
                        moment(new Date(Number(sort[0].time))).format("YYYY-MM-DD HH:00:00")
                    ).valueOf();
                    endTime =
                        new Date(
                            moment(new Date(Number(sort.slice(-1)[0].time))).format(
                                "YYYY-MM-DD HH:00:00"
                            )
                        ).valueOf() + 8000000;
                } else {
                    // 开始时间小于结束时间24小时
                    startTime = new Date(
                        moment(new Date(Number(sort[0].time))).format("YYYY-MM-DD HH:00:00")
                    ).valueOf();
                    endTime =
                        new Date(
                            moment(new Date(Number(sort[0].time))).format(
                                "YYYY-MM-DD HH:00:00"
                            )
                        ).valueOf() + 90000000;
                }
                this.timeLineDataTime = {
                    startTime,
                    endTime
                };
                this.timeLineTimes = moment(
                    new Date(Number(this.timeLineDataTime.startTime) + 86400000)
                ).format("YYYY-MM-DD HH:00:00");
                for (const item of taskList) {
                    item.timec = startTime;
                }
                this.taskList = taskList;
                jq(".time1").remove();
                this.$nextTick(() => {
                    this.htmlLoad(startTime, endTime);
                    for (const item of this.taskList) {
                        this.addtimes(item, sort.slice(-1)[0].time, sort[0].time);
                    }
                });
            }
        }
        if (res.code === -7) {
            const data = res.stationList
            data.airStationList = data.airStationList.map((item:any) => {
                item.stationType = 1
                item.aqi = item.concentration
                item.id = item.stationCode
                item.stationName = item.positionName
                return item
            })
            data.waterStationList = data.waterStationList.map((item:any) => {
                item.longitude = item.lng
                item.latitude = item.lat
                item.stationType = 2
                item.id = item.stationId
                item.stationCode = item.stationId
                return item
            })
            data.pollutionStationList = data.pollutionStationList.map((item:any) => {
                item.longitude = item.gcLng
                item.latitude = item.gcLat
                item.id = item.companyId
                item.stationCode = item.companyId
                item.stationType = 3
                return item
            })
            this.emergencyStationList = data.airStationList.concat(data.pollutionStationList).concat(data.waterStationList);
            this.waterStationList = res.stationList.waterStationList
            if (!this.waterStationList.length) {
                this.defaultWater = ''
            }
            this.airStationList = res.stationList.airStationList
            if (!this.airStationList.length) {
                this.defaultAir = ''
            }
            this.pollutionList = res.stationList.pollutionStationList
            if (!this.pollutionList.length) {
                this.defaultPollution = ''
            }
            console.log("getEmergencyStationList", this.emergencyStationList);
            if (this.waterStationList.length > 0) {
                this.waterStationId = this.waterStationList[0].stationId;
                console.log(this.waterStationId, 'waterStationId')
                this.getMonitorItemDetailRecord();
            }
            if (this.airStationList.length > 0) {
                this.airStationId = this.airStationList[0].stationCode;
                console.log(this.airStationId, this.defaultAir, 'defaultAir')
                this.getAqiTrend();
            }
            if (this.pollutionList.length > 0) {
                this.pollutionStationId = this.pollutionList[0].companyId;
                this.getTwentyFourHour();
            }
        }
    }
    private cameraSerial: any = "";
    private requestTime: any = "";
    private videoCameraSerial: any = "";
    // 获得数据
    private getMessage(msg: any) {
        const res: any = JSON.parse(msg.data);
        if (res.code == -4) {
            if (this.flvPlayer) {
                this.flvPlayer.pause();
                this.flvPlayer.unload();
                this.flvPlayer.detachMediaElement();
                this.flvPlayer.destroy();
                this.flvPlayer = null;
            }
            if (flvjs.isSupported() && res.liveAddress.online) {
                const videoElement = document.getElementById("videoElement");
                this.flvPlayer = flvjs.createPlayer({
                    type: "flv",
                    isLive: true,
                    hasVideo: true,
                    // hasAudio: true,
                    url: res.liveAddress.flv
                });
                this.flvPlayer.attachMediaElement(videoElement);
                this.flvPlayer.load();
                this.flvPlayer.play();
            }
            clearInterval(this.requestTime);
            this.requestTime = setInterval(() => {
                keepAlive(this.videoCameraSerial);
            }, 10 * 1000);
        }
        if (res.code === -11) {
            this.cameraSerial = [];
            // 车辆监控列表
            this.deptStreetList = res.cameraMap.carList;
            this.cameraList = res.cameraMap.cameraList;
            for (const i in this.deptStreetList) {
                for (const j in this.deptStreetList[i].carList) {
                    if (this.deptStreetList[i].carList[j].license == "川AAH355") {
                        this.carStreet = this.deptStreetList[i].deptId;
                        this.deptCarList = this.deptStreetList[i].carList;
                        this.carId = this.deptCarList[j].carId;
                        console.log(this.cameraSerial);
                        this.cameraSerial.push(this.deptStreetList[0].cameraSerial);
                    }
                }
            }
            for (const item of this.cameraList) {
                console.log(item.carId, this.carId, 66666);
                if (item.carId === this.carId && item.positionType === 0) {
                    this.cameraSerial[0] = item.cameraSerial;
                }
                if (item.carId === this.carId && item.positionType === 1) {
                    this.cameraSerial[1] = item.cameraSerial;
                }
            }
            if (this.taskId) {
                this.socket1.send(JSON.stringify({ code: 8, taskId: this.taskId, carId: this.carId }));
            }
            console.log(this.cameraSerial);
            this.socket.send(
                JSON.stringify({
                    code: 4,
                    cameraSerial: this.cameraSerial[this.cameraDirection]
                })
            );
            this.videoCameraSerial = this.cameraSerial[this.cameraDirection];
        }
    }
    private cameraDirection = 0;
    private flvPlayer: any = null;
    private deptStreetList: any = [];
    private deptCarList: any = [];
    private cameraList: any = [];
    private carId: any = "";
    private carStreet: any = "";
    private carLicense: any = "";
    // 切换车辆视频的街道
    private changeStreet() {
        this.deptCarList = this.deptStreetList.filter((item: any) => {
            return item.deptId === this.carStreet;
        })[0].carList;
        this.carLicense = this.deptCarList[0].carId;
        this.carId = this.deptCarList[0].carId;
        this.getCarCamera();
    }
    // 获取车辆视频地址
    private getCarCamera() {
        this.cameraSerial = [];
        for (const item of this.cameraList) {
            if (item.carId === this.carId && item.positionType === 0) {
                this.cameraSerial[0] = item.cameraSerial;
            }
            if (item.carId === this.carId && item.positionType === 1) {
                this.cameraSerial[1] = item.cameraSerial;
            }
        }
        if (this.cameraSerial.length > 0) {
            this.socket.send(
                JSON.stringify({
                    code: 4,
                    cameraSerial: this.cameraSerial[this.cameraDirection]
                })
            );
            this.videoCameraSerial = this.cameraSerial[this.cameraDirection];
        } else {
            if (this.flvPlayer) {
                this.flvPlayer.pause();
                this.flvPlayer.unload();
                this.flvPlayer.detachMediaElement();
                this.flvPlayer.destroy();
                this.flvPlayer = null;
            }
        }
    }
    // 切换摄像头前后方向
    changeCameraDirection(direction: number) {
        if (this.cameraSerial[direction]) {
            this.cameraDirection = direction;
            this.socket.send(
                JSON.stringify({
                    code: 4,
                    // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
                    // @ts-ignore
                    cameraSerial: this.cameraSerial[direction]
                })
            );
            this.videoCameraSerial = this.cameraSerial[direction];
        } else {
            message.error("暂无该方向摄像头");
        }
    }
}
(window as any).taskDetail = (data: any) => {
    for (const item of document.querySelectorAll(".time1-active")) {
        item.className = "time1";
    }
    // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
    //@ts-ignore
    // eslint-disable-next-line no-undef
    window.clearTimeout(_that.alertTime);
    // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
    //@ts-ignore
    // eslint-disable-next-line no-undef
    _that.departmentName = data.split("---")[0];
    // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
    //@ts-ignore
    // eslint-disable-next-line no-undef
    _that.creatTime = moment(new Date(Number(data.split("---")[1]))).format(
        "YYYY-MM-DD HH:mm"
    );
    // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
    //@ts-ignore
    // eslint-disable-next-line no-undef
    _that.content = data.split("---")[2];
    // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
    //@ts-ignore
    // eslint-disable-next-line no-undef
    _that.replyContent = data.split("---")[3];
    // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
    //@ts-ignore
    event.target.className = "time1-active";
    // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
    //@ts-ignore
    document.querySelector(".taskAlert").style.left = `${event.x - 280}px`;
    // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
    //@ts-ignore
    // eslint-disable-next-line no-undef
    _that.taskDetailState = true;
    jq(document).one("click", function() {
        // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
        //@ts-ignore
        // eslint-disable-next-line no-undef
        _that.taskDetailState = false;
        for (const item of document.querySelectorAll(".time1-active")) {
            item.className = "time1";
        }
    });
    // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
    //@ts-ignore
    // eslint-disable-next-line no-undef
    event.stopPropagation();
    // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
    //@ts-ignore
    // eslint-disable-next-line no-undef
    _that.alertTime = setTimeout(() => {
        // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
        //@ts-ignore
        // eslint-disable-next-line no-undef
        _that.taskDetailState = false;
        for (const item of document.querySelectorAll(".time1-active")) {
            item.className = "time1";
        }
    }, 15 * 1000);
};
</script>

