// date.js
export function formatDate(date, fmt) {
  if (/(y+)/.test(fmt)) {
    fmt = fmt.replace(RegExp.$1, (`${date.getFullYear()}`).substr(4 - RegExp.$1.length))
  }
  const o = {
    'M+': date.getMonth() + 1,
    'd+': date.getDate(),
    'h+': date.getHours(),
    'm+': date.getMinutes(),
    's+': date.getSeconds()
  }
  for (const k in o) {
    if (new RegExp(`(${k})`).test(fmt)) {
      const str = `${o[k]}`
      fmt = fmt.replace(RegExp.$1, (RegExp.$1.length === 1) ? str : padLeftZero(str))
    }
  }
  return fmt
}

function padLeftZero(str) {
  return (`00${str}`).substr(str.length)
}

export function getyyyyMMdd(date) {
  const d = new Date(date)
  let curr_date = d.getDate()
  let curr_month = d.getMonth() + 1
  const curr_year = d.getFullYear()
  String(curr_month).length < 2 ? (curr_month = `0${curr_month}`) : curr_month
  String(curr_date).length < 2 ? (curr_date = `0${curr_date}`) : curr_date
  const yyyyMMdd = `${curr_year}-${curr_month}-${curr_date}`
  return yyyyMMdd
}

export function str2Date(dateStr, separator) {
  if (!separator) {
    separator = '-'
  }
  const dateArr = dateStr.split(separator)
  const year = parseInt(dateArr[0])
  let month
  // 处理月份为04这样的情况
  if (dateArr[1].indexOf('0') === 0) {
    month = parseInt(dateArr[1].substring(1))
  } else {
    month = parseInt(dateArr[1])
  }
  const day = parseInt(dateArr[2])
  const date = new Date(year, month - 1, day)
  return date
}
