import request from "@/utils/request";
import { AxiosPromise } from "axios";

/**
 * @method functionName
 * @param {type} data 说明
 * @description 获取雷达数据
 */
export function getRadarData(): AxiosPromise<any> {
  return request({
    url: `/iot/lidar/dataRed`,
    method: "get",
  });
}
/**
 * @method functionName
 * @param {type} data 说明
 * @description 获取雷达数据
 */
export function getRadarDataList(): AxiosPromise<any> {
  return request({
    url: `/iot/lidar/dataRedList`,
    method: "get",
  });
}