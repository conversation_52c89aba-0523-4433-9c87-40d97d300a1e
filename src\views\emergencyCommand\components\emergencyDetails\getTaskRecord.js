

import { taskRecord } from '@/api/emergencyDetails/emergencyEvent'

import { onMounted, watch, shallowRef, onActivated } from 'vue'

export default function getTaskRecord() {

  // 应急事件统计数据
  const emergencyEventList = shallowRef([])

  let pageNum = shallowRef(1)
  let pageSize = shallowRef(10)
  let totalPages = shallowRef(Infinity)
  let type = shallowRef(4)

  // 数据加载中
  let recordLoading = shallowRef(false)

  /**
   * 获取应急任务列表
   * @param {Object} 请求参数 
   * {
   * pageNum, pageSize, type: 1: '社区安全',
    2: '公共安全',
    3: '自然灾害',
    4: '事故灾害',
   * }
    @example
    getRecord({pageNum: 1, pageSize: 10, type: 1})
   */
  function getRecord() {
    if (pageNum.value >= totalPages.value) return

    recordLoading.value = true
    taskRecord({
      pageNum: pageNum.value || 1,
      pageSize: pageSize.value || 10,
      query: {
        emergencyEventType: type.value
      }
    })
      .then((res) => {
        if (!res) throw new Error('请求失败')

        const { code, data } = res
        // console.log('获取数据成功', data);
        if (code === 200) {
          emergencyEventList.value = emergencyEventList.value.concat(data.records)
          totalPages = data.pages
        }
      })
      .catch((err) => {
        console.log('获取数据失败', err)
      })
      .finally(() => {
        console.log('获取数据请求完成')
        recordLoading.value = false
      })
  }

  // 类型改变
  watch(type, (newType) => {
    if (recordLoading.value) return
    emergencyEventList.value = []
    pageNum.value = 1
    getRecord()
  })

  // 页码变化
  watch(pageNum, (newPageNum) => {
    if (recordLoading.value) return
    getRecord()
  })

  onActivated(() => {
    emergencyEventList.value = []
    pageNum.value = 1
    getRecord()
  })

  return {
    emergencyEventList,
    type,
    pageNum,
    recordLoading,
  }
}