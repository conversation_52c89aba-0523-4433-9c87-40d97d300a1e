<style lang="less" scoped>
    .no-data {
        font-size: 0.2rem;
        position: relative;
        .no-datas {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
    }
</style>
<template>
    <div
            v-if="(AirDataProp.type === 1 && AirDataProp.stationY1Data.length) || (AirDataProp.type === 2 && AirDataProp.stationY2Data.length)"
            :id="id"
            :style="{ height: height, width: width }"
    />
    <div v-else class="no-data" :style="{ height: height, width: width }">
        <div class="no-datas">数据未更新</div>
    </div>
</template>

<script lang="ts">
import * as echarts from 'echarts'
import { Component, Prop, Watch } from "vue-property-decorator";
import { mixins } from "vue-class-component";
import ResizeMixin from "./mixins/resize";
interface AirDataProp {
    stationXData: string | number[];
    stationY1Data: string | number [];
    stationY2Data: string | number[];
    type: number,
    isShow: number
}
enum Type {
    TREND = 1,
    CONTRAST = 2
}
@Component({
    name: "stationEcharts"
})
export default class extends mixins(ResizeMixin) {
    @Prop({ default: "chart" }) private className!: string;
    @Prop({ default: "chart" }) private id!: string;
    @Prop({ default: "200px" }) private width!: string;
    @Prop({ default: "200px" }) private height!: string;
    @Prop({ default: 1 }) private type!: number;
    @Prop({ required: true }) private AirDataProp!: AirDataProp;
    @Watch("AirDataProp", { immediate: true, deep: true })
    public onAirDataProp(newValue: string, oldValue: string) {
        if (this.chart) {
            this.chart.clear();
        }
        this.$nextTick(() => {
            this.initChart();
        });
    }

    mounted() {
        console.log(this.AirDataProp, 'AirDataProp')
        if (this.chart) {
            this.chart.clear();
        }
        this.$nextTick(() => {
            this.initChart();
        });
    }

    beforeDestroy() {
        if (!this.chart) {
            return;
        }
        this.chart.clear();
        this.chart.dispose();
        this.chart = null;
    }

    private initChart() {

        if(this.chart === null || this.chart === undefined) {
        this.chart = echarts.init(
            document.getElementById(this.id) as HTMLDivElement
        );
        }
        const type = this.AirDataProp.type
        const isShow = this.AirDataProp.isShow
        const option = {
            tooltip: {
                trigger: "axis",
                axisPointer: {
                    type: "cross",
                    label: {
                        backgroundColor: "#6a7985"
                    }
                },
                formatter: function(params: Array<any>): string {
                    console.log(params, 123)
                    let ret = `${params[0].name}`;
                    params.forEach((series: any, index:any) => {
                        if (new RegExp(`${series.seriesName}`, "g").test(ret)) return;
                        if (series.value !== "-") {
                            ret += `<br/>${isShow === 2 ? '烟雾浓度' : (type === 1 ? '液阻压力' : '卸油区油气浓度')}: ${series.value}${isShow === 2 ? 'mg/m³' : (type === 1 ? '(pa)' : '(%/ppm)')}`;
                        }
                    });
                    return ret;
                }
            },
            grid:{
                top: 40,
                left: '10%',
                bottom: 20,
                right: 20
            },

            xAxis: {//代表x轴，这里type类型代表字符
                type: 'category',
                boundaryGap: false,//这里表示是否补齐空白
                axisTick: {
                    show:false
                },
                axisLine: {
                    show: false,//不显示坐标轴线
                },
                data: this.AirDataProp.stationXData,
                //网格属性
                splitLine: {
                    show: false,
                    lineStyle:{
                        width: 1,
                        type: 'solid'
                    }
                },
                axisLabel: {
                    interval:2,
                    color: 'white'
                }
            },
            yAxis: [{
                type: 'value',
                name: Number(this.AirDataProp.isShow) === 2 ? 'mg/m³' : (this.AirDataProp.type === 1 ? 'pa' : '（%/ppm）'),
                nameTextStyle: {
                    color: "#fff",
                    fontSize: 14,
                },
                axisTick: {
                    show:false
                },
                axisLine: {
                    show: false,//不显示坐标轴线
                },
                //网格样式
                splitLine: {
                    show: true,
                    lineStyle:{
                        width: 1,
                        type: 'solid',
                        color: 'rgba(204,204,204, .1)'
                    }
                },
                axisLabel: {
                    color: 'white'
                }
            }],
            series: [{
                data: this.AirDataProp.isShow === 2 ? this.AirDataProp.stationY1Data : (this.AirDataProp.type === 1 ? this.AirDataProp.stationY1Data : this.AirDataProp.stationY2Data),
                type: 'line',
                smooth: true,//如果想折线图平滑可以加上这个属性
                showSymbol: true,
                symbol: 'circle',     //设定为实心点
                symbolSize: 3,
                hoverAnimation: false,
                itemStyle: {
                    normal: {
                        areaStyle: {
                            color: {
                                type: "linear",
                                x: 0,
                                y: 0,
                                x2: 0,
                                y2: 1,
                                colorStops: [
                                    {
                                        offset: 0,
                                        color: '#03adff'
                                    },
                                    {
                                        offset: 1,
                                        color: 'transparent' // 100% 处的颜色
                                    }
                                ],
                                global: false
                            },
                        },
                        color: '#03adff' //改变区域颜色
                    }
                },
            },
            ]
        }
        this.chart.setOption(option as EChartOption<EChartOption.SeriesLine>);
    }
}
</script>
