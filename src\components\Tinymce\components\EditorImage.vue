<template>
  <div class="upload-container">
    <el-button
      size="mini"
      type="info"
      plain
      @click="dialogVisible=true"
    >
      上传图片附件
    </el-button>
    <el-dialog
      :visible.sync="dialogVisible"
      :modal="false"
      width="45%"
    >
      <el-upload
        ref="upload"
        :auto-upload="false"
        :http-request="noop"
        :file-list="fileList"
        :on-change="uploadFile"
        :before-remove="fileBeforeRemove"
        :on-remove="handleFileRemove"
        drag
        multiple
        action="customize"
      >
        <i class="el-icon-upload" />
        <div class="el-upload__text"><em>点击上传</em></div>
      </el-upload>
      <span
        slot="footer"
        class="footer-button-style"
      >
        <el-button @click="cancleUpload">
          取消
        </el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
        >
          确认
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
// import { getToken } from 'api/qiniu'
const _ = require('lodash')

const FILE_TYPE_DIC = {
  png: 0,
  jpg: 0,
  jpeg: 0,
  bmp: 0,
  docx: 1,
  doc: 1,
  xlsx: 2,
  xls: 2,
  pdf: 3,
  pptx: 4,
  ppt: 4,
  rar: 5,
  zip: 5
}
export default {
  name: 'EditorSlideUpload',
  props: {
    color: {
      type: String,
      default: '#1890ff'
    }
  },
  data() {
    return {
      dialogVisible: false,
      listObj: {},
      fileList: [],
      upLoadFileList: []
    }
  },
  methods: {
    noop() {
      // el-update http-request 不处理 noop 方法
    },
    uploadFile(file, fileList) {
      // 利用fileReader对象获取file
      const that = this
      that.upLoadFileList = []
      fileList.forEach((item) => {
        const rawFile = item.raw
        const filename = rawFile.name
        const filetype = rawFile.name.split('.').pop()
        // if (filesize / 1024 / 1024 > 3) {
        //   // 文件大于3MB
        //   this.$message.error('上传文件不能大于3M')
        //   return
        // }
        if (FILE_TYPE_DIC[filetype] === undefined) {
          this.$message.error('只能上传docx/doc/xlsx/xls/pdf文件')
          return
        }
        const reader = new FileReader()
        reader.readAsDataURL(rawFile)
        reader.onload = function(e) {
          // 读取到的图片base64 数据编码 将此编码字符串传给后台即可
          that.upLoadFileList.push({
            baseStr: e.target.result, // .split(',')[1]
            type: FILE_TYPE_DIC[filetype],
            fileName: filename
          })
        }
      })
    },
    fileBeforeRemove(file, fileList) {
      return this.$confirm(`确定移除 ${file.name}？`)
    },
    handleFileRemove(file, fileList) {
      this.uploadFile(file, fileList)
    },
    checkAllSuccess() {
      return Object.keys(this.listObj).every((item) => this.listObj[item].hasSuccess)
    },
    handleSubmit() {
      // const arr = Object.keys(this.listObj).map(v => this.listObj[v])
      // if (!this.checkAllSuccess()) {
      //   this.$message('Please wait for all images to be uploaded successfully. If there is a network problem, please refresh the page and upload again!')
      //   return
      // }
      // this.$emit('successCBK', arr)
      // this.listObj = {}
      this.upLoadFileList = _.uniqBy(this.upLoadFileList, 'fileName')
      this.$emit('successCBK', this.upLoadFileList)
      this.fileList = []
      this.dialogVisible = false
    },
    cancleUpload() {
      this.fileList = []
      this.dialogVisible = false
    },
    handleSuccess(response, file) {
      const { uid } = file
      const objKeyArr = Object.keys(this.listObj)
      for (let i = 0, len = objKeyArr.length; i < len; i++) {
        if (this.listObj[objKeyArr[i]].uid === uid) {
          this.listObj[objKeyArr[i]].url = response.files.file
          this.listObj[objKeyArr[i]].hasSuccess = true
          return
        }
      }
    },
    handleRemove(file) {
      const { uid } = file
      const objKeyArr = Object.keys(this.listObj)
      for (let i = 0, len = objKeyArr.length; i < len; i++) {
        if (this.listObj[objKeyArr[i]].uid === uid) {
          delete this.listObj[objKeyArr[i]]
          return
        }
      }
    },
    beforeUpload(file) {
      const _self = this
      const _URL = window.URL || window.webkitURL
      const fileName = file.uid
      this.listObj[fileName] = {}
      return new Promise((resolve, reject) => {
        const img = new Image()
        img.src = _URL.createObjectURL(file)
        img.onload = function() {
          _self.listObj[fileName] = {
            hasSuccess: false, uid: file.uid, width: this.width, height: this.height
          }
        }
        resolve(true)
      })
    }
  }
}
</script>

<style lang="less" scoped>
.editor-slide-upload {
  margin-bottom: 20px;
  ::v-deep  .el-upload--picture-card {
    width: 100%;
  }
  .footer-button-style{
    display: flex;
    flex-direction: row;
    justify-content: start;
  }
}
</style>
