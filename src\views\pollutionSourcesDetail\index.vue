<template>
  <div class="pollutionSourcesDetail">
    <div class="title">
      <img style="width:18px;height:20px" src="@/assets/<EMAIL>">
      {{detailData.name}}</div>
    <div class="baseInfo">
      <div class="info">
        <span class="title">统一社会信用代码：</span>
        <span>{{detailData.code || '--'}}</span>
      </div>
      <div class="info">
        <span class="title">所属区县：</span>
        <span>{{detailData.districtName || '--'}}{{detailData.streetName || '--'}}</span>
      </div>
      <div class="info">
        <span class="title">企业联系人：</span>
        <span> {{detailData.contact || '--'}}</span>
      </div>
      <div class="info">
        <span class="title">经纬度：</span>
        <span>{{detailData.lng || '--'}} , {{detailData.lat || '--'}} </span>
      </div>
      <div class="info">
        <span class="title">联系人电话：</span>
        <span>{{detailData.phone || '--'}}</span>
      </div>
      <div class="info">
        <span class="title">所在位置：</span>
        <span> {{detailData.address || '--'}}</span>
      </div>
    </div>
    <div class="line" style="margin-top:26px"></div>
    <div class="tableTitle">
      <div v-for="(item,index) in tableTitleList" :key="index" class="tableTitleBox" @click="tabChange(item)" :class="activeTab===item.id?'activeTab':''">{{item.name}}</div>
    </div>
    <div v-if="activeTab===0 && detailData.pollutionType!==4">
      <div v-if="detailData.pollutionType===0"  class="tabOne">
        <div class="tabOneBox">
          <div class="tableName">是否为绿色钣喷</div>
          <div class="tableInfo">{{productMsg.isSprayName || '--'}}</div>
        </div>
        <div class="tabOneBox">
          <div class="tableName">所属行业（小类）</div>
          <div class="tableInfo">{{detailData.industryName || '--'}}</div>
        </div>
        <!-- <div class="tabOneBox">
          <div class="tableName">主要原辅料</div>
          <div class="tableInfo">{{productMsg.mainMaterials || '--'}}</div>
        </div> -->
        <div class="tabOneBox">
          <div class="tableName">主要产品</div>
          <div class="tableInfo">{{productMsg.mainProduct || '--'}}</div>
        </div>
        <!-- <div class="tabOneBox">
          <div class="tableName">涉VOCs原辅料名称</div>
          <div class="tableInfo">{{productMsg.VOCsMaterials || '--'}}</div>
        </div> -->
        <div class="tabOneBox">
          <div class="tableName">含VOCs废气末端处理设施类型</div>
          <div class="tableInfo">{{productMsg.VOCsDevice || '--'}}</div>
        </div>
      </div>
      <div v-if="detailData.pollutionType===0"  class="tabOne" style="display:flex">
        <div class="tabOneLine"></div>
        <div style="width:100%;display:flex;justify-content: space-between;">
          <div style="width:48%">
            <div class="tabOneTitle">主要原辅料及用量</div>
            <div class="tabOneTop">
              <div>序号</div>
              <div>主要原辅料</div>
              <div>用量</div>
            </div>
            <div class="tabOneContentHeight" v-if="productMsg && productMsg.mainMaterialsList && productMsg.mainMaterialsList.length>0">
              <div v-for="(item,index) in productMsg.mainMaterialsList" :key="index"  class="tabOneTop tabOneContent">
                <div>{{index + 1}}</div>
                <div :title="item.name">{{item.name}}</div>
                <div :title="item.num">{{item.num}}</div>
              </div>
            </div>
            <div v-else class="tabOneTop tabOneTopNotData" style="">
              暂无数据
            </div>
          </div>
          <div style="width:48%">
            <div class="tabOneTitle">涉VOCs原辅料名称及用量</div>
            <div class="tabOneTop">
              <div>序号</div>
              <div>主要原辅料</div>
              <div>用量</div>
            </div>
            <div class="tabOneContentHeight" v-if="productMsg && productMsg.VOCsMaterialsList && productMsg.VOCsMaterialsList.length>0">
              <div v-for="(item,index) in productMsg.VOCsMaterialsList" :key="index" class="tabOneTop tabOneContent">
                <div>{{index + 1}}</div>
                <div :title="item.name">{{item.name}}</div>
                <div :title="item.num">{{item.num}}</div>
              </div>
            </div>
            <div v-else class="tabOneTop tabOneTopNotData" style="">
              暂无数据
            </div>
          </div>
        </div>
      </div>
      <div v-if="detailData.pollutionType===1"  class="tabOne">
        <div class="tabOneBox">
          <div class="tableName">建设单位名称</div>
          <!-- <el-popover
            placement="top-start"
            title="标题"
            width="200"
            trigger="hover"
            content="这是一段内容,这是一段内容,这是一段内容,这是一段内容。">
            <el-button slot="reference">hover 激活</el-button>
          </el-popover> -->
          <div class="tableInfo">{{detailData.constructionName  || '--'}}</div>
        </div>
        <div class="tabOneBox">
          <div class="tableName">施工单位</div>
          <div class="tableInfo">{{detailData.construction  || '--'}}</div>
        </div>
        <div class="tabOneBox">
          <div class="tableName">工地类型</div>
          <div class="tableInfo">{{detailData.siteTypeName  || '--'}}</div>
        </div>
        <div class="tabOneBox">
          <div class="tableName">施工阶段</div>
          <div class="tableInfo">{{ detailData.constructionTypeName  || '--'}}</div>
        </div>
        <div class="tabOneBox">
          <div class="tableName">施工面积（㎡）</div>
          <div class="tableInfo">{{detailData.constructionArea  || '--'}}</div>
        </div>
        <div class="tabOneBox">
          <div class="tableName">已获环保标识码机械数量（台）</div>
          <div class="tableInfo">{{detailData.constructionEocDeviceNum  || '--'}}</div>
        </div>
          <div class="tabOneBox">
          <div class="tableName">施工时间</div>
          <div class="tableInfo">{{detailData.startConstructionTime  || '--'}} -- {{detailData.endConstructionTime ||  ''}}</div>
        </div>
          <div class="tabOneBox">
          <div class="tableName">施工机械类型、数量（台）</div>
          <div class="tableInfo">{{detailData.constructionDevice  || '--'}}</div>
        </div>
          <div class="tabOneBox">
          <div class="tableName">各排放标准下的机械数量（台）</div>
          <div class="tableInfo">{{ detailData.constructionStanderDevice  || '--'}}</div>
        </div>
      </div>
      <div v-if="detailData.pollutionType===2"  class="tabOne">
        <div class="tabOneBox">
          <div class="tableName">排污许可证号</div>
          <div class="tableInfo">{{productMsg.pollutantDischargePermitId || '--'}}</div>
        </div>
        <div class="tabOneBox">
          <div class="tableName">所属行业（小类）</div>
          <div class="tableInfo">{{productMsg.industryName || '--'}}</div>
        </div>
        <div class="tabOneBox">
          <div class="tableName">主要产品及年产量</div>
          <div class="tableInfo">{{productMsg.mainProductAndAnnualOutput || '--'}}</div>
        </div>
        <!-- <div class="tabOneBox">
          <div class="tableName">主要原辅料及用量</div>
          <div class="tableInfo">{{productMsg.mainMaterialsAndDosage || '--'}}</div>
        </div> -->
      </div>
      <div v-if="detailData.pollutionType===2"  class="tabOne">
        <div class="tabOneLine"></div>
        <div style="width:100%;display:flex;justify-content: space-between;">
          <div style="width:48%">
            <div class="tabOneTitle">主要原辅料及用量</div>
            <div class="tabOneTop">
              <div>序号</div>
              <div>主要原辅料</div>
              <div>用量</div>
            </div>
            <div class="tabOneContentHeight" v-if="productMsg && productMsg.mainMaterialsAndDosageList && productMsg.mainMaterialsAndDosageList.length>0">
              <div v-for="(item,index) in productMsg.mainMaterialsAndDosageList" :key="index"  class="tabOneTop tabOneContent">
                <div>{{index + 1}}</div>
                <div :title="item.name">{{item.name}}</div>
                <div :title="item.num">{{item.num}}</div>
              </div>
            </div>
            <div v-else class="tabOneTop tabOneTopNotData" style="">
              暂无数据
            </div>
          </div>
        </div>
      </div>
      <div v-if="detailData.pollutionType===3"  class="tabOne">
        <div class="tabOneBox">
          <div class="tableName">汽油储罐个数（个）</div>
          <div class="tableInfo">{{productMsg.gasolineStorageTanks || '--'}}</div>
        </div>
        <div class="tabOneBox">
          <div class="tableName">汽油储罐总容积（m³）</div>
          <div class="tableInfo">{{productMsg.gasolineStorageTankCapacity || '--'}}</div>
        </div>
        <div class="tabOneBox">
          <div class="tableName">汽油（包括含醇汽油）年均销售量（吨，近三年）</div>
          <div class="tableInfo">{{productMsg.averageAnnualSalesVolume || '--'}}</div>
        </div>
      </div>
    </div>
   <div v-if="activeTab===1" class="tabTwo">
      <div class="tabTwoLeft">
         <div v-for="(item,index) in tabTwoLeftList" :key="index" @click="tabTwoChange(index,item)" :class="activeTabTwo===index?'activeLeft':''">{{item.name}}</div>
      </div>
      <div class="tabTwoRight">
        <div v-if="activeTabTwoName==='airPollution' && detailData.pollutionType!=4">
          <div class="rowOne">是否涉大气污染物排放：
            {{judgeNull({pollutionMsg:pollutionMsg},'pollutionMsg.airPollution.isAirPollutionObj.name')}}
          </div>
          <div class="rowOne">大气主要污染物：
            {{(judgeNull({pollutionMsg:pollutionMsg},'pollutionMsg.airPollution.mainAirPollution')).toString()}}
          </div>
          <div class="rowOne">大气环保治理设施类型:
            {{judgeNull({pollutionMsg:pollutionMsg},'pollutionMsg.airPollution.atmoSphericEnvironmentalProtection')}}
          </div>
          <div class="rowOne">废气收集系统类型: 
            {{judgeNull({pollutionMsg:pollutionMsg},'pollutionMsg.airPollution.gasCollectionSystem')}}
          </div>
        </div>
        <div v-if="activeTabTwoName==='airPollution' && detailData.pollutionType==4">
          <div class="rowOne">是否有硬化：
            {{judgeNull({pollutionMsg:pollutionMsg},'pollutionMsg.airPollution.isHardenObj.name')}}
          </div>
          <div class="rowOne">是何种硬化：
            {{judgeNull({pollutionMsg:pollutionMsg},'pollutionMsg.airPollution.hardenWay')}}
          </div>
          <div class="rowOne">大气环保治理设施类型:
            {{judgeNull({pollutionMsg:pollutionMsg},'pollutionMsg.airPollution.isDustProtectionMeasuresObj.name')}}
          </div>
        </div>
        <div v-if="activeTabTwoName==='sewagePollution'">
          <!-- rowTwo -->
          <div class="rowOne">是否涉污水排放：
             {{judgeNull({pollutionMsg:pollutionMsg},'pollutionMsg.sewagePollution.isSewagePollutionObj.name')}}
          </div>
          <div class="rowOne">污水主要污染物：
             {{(judgeNull({pollutionMsg:pollutionMsg},'pollutionMsg.sewagePollution.mainSewagePollution')).toString()}}
          </div>
          <div class="rowOne">污水收集方式：
             {{judgeNull({pollutionMsg:pollutionMsg},'pollutionMsg.sewagePollution.sewageCollection')}}
          </div>
          <div class="rowOne">污水排放方式：
             {{judgeNull({pollutionMsg:pollutionMsg},'pollutionMsg.sewagePollution.sewageDischargeMethod')}}
          </div>
          <div class="rowOne">是否有污水处理设施：
             {{judgeNull({pollutionMsg:pollutionMsg},'pollutionMsg.sewagePollution.isSewageTreatmentFacilityObj.name')}}
          </div>
          <div class="rowOne">主要处理工艺：
             {{judgeNull({pollutionMsg:pollutionMsg},'pollutionMsg.sewagePollution.mainTreatmentProcess')}}
          </div>
          <div class="rowOne">污水产生来源：
             {{judgeNull({pollutionMsg:pollutionMsg},'pollutionMsg.sewagePollution.sewagePollutionSource')}}
          </div>
          <div class="rowOne">污水排放量（吨/月）：
             {{judgeNull({pollutionMsg:pollutionMsg},'pollutionMsg.sewagePollution.sewageEmissions')}}
          </div>
        </div>
        <div v-if="activeTabTwoName==='solidPollution'">
          <div class="rowOne">是否涉危险废物：
            {{judgeNull({pollutionMsg:pollutionMsg},'pollutionMsg.solidPollution.isHazardousWasteObj.name')}}
          </div>
          <div class="rowOne">危险废弃物类别（代码）： 
            {{judgeNull({pollutionMsg:pollutionMsg},'pollutionMsg.solidPollution.hazardousWasteTypeCode')}}
          </div>
          <div class="rowOne">危险废弃物转运方式及去向：
            {{judgeNull({pollutionMsg:pollutionMsg},'pollutionMsg.solidPollution.hazardousWasteTransport')}}
          </div>
          <div class="rowOne">危险废弃物产生来源：
            {{judgeNull({pollutionMsg:pollutionMsg},'pollutionMsg.solidPollution.hazardousWasteSource')}}
          </div>
        </div>
        <div v-if="activeTabTwoName==='noisePollution'">
          <div class="rowOne">是否存在噪声污染：
            {{judgeNull({pollutionMsg:pollutionMsg},'pollutionMsg.noisePollution.isNoisePollutionObj.name')}}
          </div>
          <div class="rowOne">噪声产生来源： 
              {{judgeNull({pollutionMsg:pollutionMsg},'pollutionMsg.noisePollution.noiseSource')}}
          </div>
          <div class="rowOne">是否有降噪设施： 
              {{judgeNull({pollutionMsg:pollutionMsg},'pollutionMsg.noisePollution.isNoiseReductionFacilitiesObj.name')}}
          </div>
          <div class="rowOne">噪声产生时段： 
              {{judgeNull({pollutionMsg:pollutionMsg},'pollutionMsg.noisePollution.startNoiseTimePeriod')}}--{{judgeNull({pollutionMsg:pollutionMsg},'pollutionMsg.noisePollution.endNoiseTimePeriod')}}
          </div>
        </div>
      </div>
    </div>
    <div v-if="activeTab===2" class="tabThree">
      <div class="tabTitle">
        <div v-for="(item,index) in tabThreeTitleList" @click="tabThreeChange(item)" :class="activeTabThree==item.id?'activeThree':''" :key="index">{{item.name}}</div>
      </div>
      <div v-if="activeTabThree!==0">
      <div class="chooseBox">
      <div>
      <el-select v-model="threeName" @change="searchThree" size="small" :popper-append-to-body="false" placeholder="请选择点位名称">
        <el-option
          v-for="(item,index) in pointList"
          :key="index"
          :label="item.name"
          :value="item.name">
        </el-option>
      </el-select>
      <el-select style="margin:0 10px" v-model="type" size="small" :popper-append-to-body="false" placeholder="请选择数据类型" @change="changeType">
        <el-option
          v-for="item in timeTypeList"
          :key="item.id"
          :label="item.name"
          :value="item.id">
        </el-option>
      </el-select>
      <!-- <div class="time"> -->
        <el-date-picker
          :key="type + '0'"
          v-model="airSearch.time1"
          class="custom-date"
          size="small"
          :clearable="true"
          :type="type == 3 ? 'date' : 'datetime'"
          :placeholder="type == 3 ? '开始日期' : '开始时间'"
          :picker-options="type == 1 ? pickerOptions1 : type == 2
            ? pickerOptions2 : pickerOptions5"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm"
          style="margin-right:10px"
          @change="changeTime"
        />
        <el-date-picker
          :key="type + '1'"
          size="small"
          v-model="airSearch.time2"
          class="custom-date"
          :clearable="true"
          :disabled="!airSearch.time1"
          :type="type == 3 ? 'date' : 'datetime'"
          :placeholder="type == 3 ? '结束日期' : '结束时间'"
          :picker-options="airSearch.time1 ?
            (type == 3 ? pickerOptions6 : type == 2 ?
              pickerOptions4 : pickerOptions3) : pickerOptions1"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm"
          @change="changeTime"
        />
      <!-- </div> -->
      <!-- <el-date-picker
        v-model="value1"
        size="small"
        type="datetimerange"
        range-separator="-"
        start-placeholder="开始日期"
        end-placeholder="结束日期">
      </el-date-picker> -->
      </div>
      <div class="changeView">
        <div :class="activeView==='list'?'activeView':''" @click="changeView('list')">列表</div>
        <div style="margin:0 12px">|</div>
        <div :class="activeView==='view'?'activeView':''" @click="changeView('view')">视图</div>
      </div>
      </div>
      <!-- 列表 -->
      <div v-if="pointList && pointList.length>0 && pointList[threeIndex].itemList &&  pointList[threeIndex].itemList.length && activeView==='list'" class="list">
          <div class="thead">
            <div>序号</div>
            <div>点位名称</div>
            <div>监测时间</div>
            <div>达标情况</div>
            <!-- {{seriesData}} -->
            <div v-for="(item,index) in seriesData" :key="index">
              {{item.name || ''}}{{item.unit || ''}}
            </div>
            <!-- <div>PM2.5 (μg/m³)</div>
            <div>PM10 (μg/m³)</div>
            <div>臭氧 (μg/m³)</div>
            <div>二氧化硫 (μg/m³)</div>
            <div>二氧化氮 (μg/m³)</div>
            <div>一氧化碳 (μg/m³)</div> -->
          </div>
          <div style="height:200px;overflow:auto">
          <swiper
            class="t-body"
            :options="pointList[threeIndex].itemList.length > 4 ? swiperOption : swiperOption1"
            ref="mySwiper"
          >
            <swiper-slide
              class="task-list"
              v-for="(item, index) in pointList[threeIndex].itemList"
              :key="index"
              :data-href="JSON.stringify(item)"
              :data-index="index"
            >
              <div :data-href="JSON.stringify(item)" :data-index="index">
                {{ index + 1 }}
              </div>
              <div :data-href="JSON.stringify(item)" :data-index="index">
                {{ pointList[threeIndex].name }}
              </div>
              <div :data-href="JSON.stringify(item)" :data-index="index">
                <!-- {{ item.eventTypeId ? '自动监测' : '巡岗上报' }} -->
                {{item.time}}
              </div>
              <div :data-href="JSON.stringify(item)" :data-index="index" :style="item.isQualified!=0?'color:#EF6043':''">
                <!-- {{ item.eventTypeId ? 'd' : '巡岗上报' }} -->
                {{ item.isQualified==0?'达标':'未达标' }}
              </div>
              <div v-for="(i,n) in seriesData" :key="n">
                <!-- {{item.value}} -->
                <!-- {{i.name}}{{item.monitorDataItems[n].name}}{{item}} -->
                <!-- <span v-if="i.name==item.monitorDataItems[n].name" :style="item.monitorDataItems[n].isQualified!=0?'color:#EF6043':''"> {{item.monitorDataItems[n].value}}</span> -->
                <span :style="i.isQualified[index]!=0?'color:#EF6043':''">{{i.value[index]}}</span>
                  <!-- {{i.unit || ''}} -->
              </div>
              <!-- <div :data-href="JSON.stringify(item)" :data-index="index">
                {{ item.createTime }}
              </div>
              <div :data-href="JSON.stringify(item)" :data-index="index">
                {{ item.createTime }}
              </div>
              <div :data-href="JSON.stringify(item)" :data-index="index">
                {{ item.createTime }}
              </div>
              <div
                :title="item.content"
                :data-href="JSON.stringify(item)"
                :data-index="index"
              >
                {{ item.content }}
              </div>
              <div :data-href="JSON.stringify(item)" :data-index="index">
                {{ item.stationName ? item.stationName : '--' }}
              </div>
              <div :data-href="JSON.stringify(item)" :data-index="index">
                {{ item.address ? item.address : '--' }}
              </div> -->
            </swiper-slide>
          </swiper>
          </div>
      </div>
      <div  v-else >
        <div v-if="activeView==='list'" class="noData" style="margin-top:40px">
          暂无数据
        </div>
      </div>
      <!-- 视图 -->
      <div v-if="pointList && pointList.length>0 && pointList[threeIndex].itemList &&  pointList[threeIndex].itemList.length && activeView==='view'" style="width:100%;height:300px;color:#fff">
        <MoreLine :AnalyzeNew="seriesData" />
      </div>
      <div  v-else >
        <div v-if="activeView==='view'" class="noData" style="margin-top:40px">
          暂无数据
        </div>
      </div>

      </div>
      <!-- 监控 -->
      <div v-else>
        <el-select v-model="channelName" @change="searchThreeMonitor" clearable size="small" placeholder="请选择点位名称">
          <el-option
            v-for="(item,index) in monitorListAll"
            :key="index"
            :label="item.name"
            :value="item.channelId">
          </el-option>
        </el-select>
        <el-select style="margin:0 10px" v-model="state" size="small" placeholder="请选择设备状态" @change="stateChange">
          <el-option
            v-for="item in stateList"
            :key="item.id"
            :label="item.name"
            :value="item.id">
          </el-option>
        </el-select>
        <div v-if="monitorList.length>0" style="margin-top:15px;display:flex;flex-wrap: wrap;">
          <div v-for="(item,index) in monitorList" :key="index" class="monitor">
            <div class="titleTop">
              <div class="title">
                <img style="width:12px;height:14px" src="@/assets/<EMAIL>">
                {{item.name}}
              </div>
              <div :style="item.isOnline===true?'':'color:#EF5205'" style="display: flex;align-items: center;">
                <span class="cirleState" :style="item.isOnline===true?'':'background:#EF5205'" ></span>
                {{item.isOnline===true?'在线':'离线'}}</div>
            </div>
            <!-- <img :src="item.coverUrl?item.coverUrl:'@/assets/<EMAIL>'"/> -->
            <div v-if="!item.hls"   class="not-online" :style="{ backgroundImage: 'url(' + item.snapUrl + ')' }">暂无监控视频</div>
            <div v-else class="drain-video">
                <LivePlayer v-if="item.hls && item.open" :video-url="item.hls" fluent autoplay  live :stretch="true" />
                <img   v-else @click="openVideo(index)" :src="item.coverUrl?item.coverUrl:'@/assets/<EMAIL>'"/>
            </div>
          </div>
        </div>
        <div v-else class="noData">
            暂无数据
        </div>
      </div>
    
    </div>
    <div  v-if="activeTab===3" class="tabFour">
      <el-select v-model="isNotification" size="small" clearable @change="searchFour" placeholder="请选择是否通报">
        <el-option
          v-for="item in isNotificationList"
          :key="item.id"
          :label="item.name"
          :value="item.id">
        </el-option>
      </el-select>
      <el-select style="margin:0 10px" v-model="isRectification" clearable @change="searchFour" size="small" placeholder="请选择整改状态">
        <el-option
          v-for="item in isRectificationList"
          :key="item.id"
          :label="item.name"
          :value="item.id">
        </el-option>
      </el-select>
      <el-date-picker
        v-model="dateFour"
        size="small"
        clearable
        type="daterange"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        range-separator="-"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        @change="timeFourChange">
      </el-date-picker>
     <div class="list">
          <div class="thead">
            <div>序号</div>
            <div>巡检情况</div>
            <div>整改时限</div>
            <div>上报人</div>
            <div>上报时间</div>
            <div>问题类型</div>
            <div>是否通报</div>
            <div>是否整改</div>
            <!-- <div>操作</div> -->
          </div>
          <div style="height:160px;overflow:auto" v-if="situationList && situationList.length>0">
          <swiper
            class="t-body"
            :options="situationList.length > 4 ? swiperOption : swiperOption1"
            ref="mySwiper"
          >
            <swiper-slide
              class="task-list"
              v-for="(item, index) in situationList"
              :key="index"
              :data-href="JSON.stringify(item)"
              :data-index="index"
            >
              <div :data-href="JSON.stringify(item)" :data-index="index">
                {{ index + 1 }}
              </div>
              <div :data-href="JSON.stringify(item)" :data-index="index">
                {{ item.patrolRemark }}
              </div>
              <div :data-href="JSON.stringify(item)" :data-index="index">
                {{ item.rectificationTimeLimit }}
              </div>
              <div :data-href="JSON.stringify(item)" :data-index="index">
                {{ item.uploadPerson }}
              </div>
              <div :data-href="JSON.stringify(item)" :data-index="index">
                {{ item.uploadTime }}
              </div>
              <div :data-href="JSON.stringify(item)" :data-index="index">
                {{ item.patrolIssuesManifestTypeName || '--' }}
              </div>
              <!-- <div
                :title="item.content"
                :data-href="JSON.stringify(item)"
                :data-index="index"
              >
                {{ item.content }}
              </div> -->
              <div :data-href="JSON.stringify(item)" :data-index="index">
                {{ item.isNotification==true ? '是' :item.isNotification==false ? '否' : '--' }}
              </div>
              <div :data-href="JSON.stringify(item)" :data-index="index">
                {{ item.isRectification==true ? '已整改' :item.isRectification==false ? '未整改' : '--' }}
              </div>
              <!-- <div
                :data-href="JSON.stringify(item)"
                :data-index="index"
                class="stateDetail"
              >
                详情
              </div> -->
            </swiper-slide>
          </swiper>
          </div>
          <div v-else class="noData">
            暂无数据
          </div>
          <!-- 分页 -->
          <div class="suggestPagetion">
            <el-pagination
              class="billPagenation"
              @current-change="handleCurrentChange"
              :current-page="pageNum"
              :page-size="pageSize"
              background
              prev-text="上一页"
              next-text="下一页"
              layout="prev, pager, next"
              :total="total"
            >
            </el-pagination>
          </div>
        </div>
    </div>
    <div v-if="activeTab===4" class="tabFive">
      <div v-for="(item,index) in enterpriseNameList" :key="index" style="width:100%;display: flex;justify-content: space-between;flex-wrap: wrap;padding-right:20px">
      <div class="tabFiveBox" >
        <div class="tabFiveBoxLeft">
          <div class="leftTitle"><img style="width:12px;height:16px;margin-right:6px" src="../../assets/<EMAIL>" />{{item.deviceName || '--'}}</div>
          <div style="margin-top:15px">
            <div class="leftContent">
              <span class="name">活性炭类型： </span>
              <span class="text">{{item.name || '--'}}</span>
            </div>
            <div class="leftContent">
              <span class="name">碘吸附值： </span>
              <span class="text">{{item.adsorptionValue || '--'}}</span>
            </div>
            <div class="leftContent">
              <span class="name">填装量（公斤）：</span>
              <span class="text">{{item.fillingVolume || '--'}}</span>
            </div>
            <div class="leftContent">
              <span class="name">更换周期（天）：</span>
              <span class="text">{{item.replacementCycle|| '--'}}</span>
            </div>
            <div class="leftContent">
              <span class="name">上次更换距今（天）：</span>
              <span class="text">{{item.upReplaceDay || '--'}}</span>
            </div>
            <div class="leftContent">
              <span class="name">下次更换日期：</span>
              <span class="text">{{item.nextTimeReplace || '--'}}</span>
            </div>
          </div>
        </div>
        <!-- adsorptionEfficiency 活性炭吸附效率 大于50% 正常显示  50%-20%  红色  >20% 显示已失效  ，为null  不显示 -->
        <div v-if="item.adsorptionEfficiency>=50" class="tabFiveBoxRight">
          <el-progress type="circle" class="blue" :percentage="item.adsorptionEfficiency" :show-text="false" :stroke-width="12" width="180"></el-progress>
          <div class="progressText">
            <div>{{item.adsorptionEfficiency}}%</div>
            <div class="text">活性炭吸附值</div> 
          </div>
        </div>
        <div v-else-if="(item.adsorptionEfficiency<50 && item.adsorptionEfficiency>=20)" class="tabFiveBoxRight">
          <el-progress type="circle" class="red" :percentage="item.adsorptionEfficiency" :show-text="false" :stroke-width="12" width="180"></el-progress>
          <div class="progressText">
            <div>{{item.adsorptionEfficiency}}%</div>
            <div class="text">活性炭吸附值</div> 
          </div>
        </div>
        <div v-else-if="item.adsorptionEfficiency<20" class="tabFiveBoxRight">
          <el-progress type="circle" class="blue" :percentage="0" :show-text="false" :stroke-width="12" width="180"></el-progress>
          <div class="progressText">
            <div>已失效</div>
            <div class="text">活性炭吸附值</div> 
          </div>
        </div>
      </div>
      <div  class="tabFiveBox" style="width:52%;display: block" >
      <div  style="width:100%;display:flex;justify-content: flex-start;">
        <div v-if="item.replaceImage&&item.replaceImage.length>0">
          <el-image
            v-for="(val,i) in item.replaceImage" 
            :key="i"
            style="width: 110px; height: 96px;;margin-right:10px;"
            :src="val" 
            fit="cover"
            :preview-src-list="item.replaceImage">
          </el-image>
        </div>
        <div v-if="item.replaceVideo&&item.replaceVideo.length>0" style="display: flex;">
          <div style="width: 110px; height: 96px;margin-right:10px; position:relative"
          v-for="(val,i) in item.replaceVideo"
          :key="i">
           <video
            style="height:100%; width:100%; object-fit: cover;"
            :src="val"
            controls
            width="118px"
            height="100px"
          /> 
          </div>
        </div>
       </div>
      <div  style="width:100%;display:flex;justify-content: flex-start;">
        <div v-if="item.replaceBeforeImage&&item.replaceBeforeImage.length>0">
          <el-image
            v-for="(val,i) in item.replaceBeforeImage" 
            :key="i"
            style="width: 110px; height: 96px;margin-right:10px;"
            :src="val" 
            fit="cover"
            :preview-src-list="item.replaceBeforeImage">
          </el-image>
        </div>
        <div v-if="item.replaceBeforeVideo&&item.replaceBeforeVideo.length>0" style="display: flex;">
          <div style="width: 110px; height: 96px;margin-right:10px; position:relative"
          v-for="(val,i) in item.replaceBeforeVideo"
          :key="i">
           <video
            style="height:100%; width:100%; object-fit: cover;"
            :src="val"
            controls
            width="118px"
            height="100px"
          /> 
          </div>
        </div>
       </div>
      <div v-if="!item.replaceBeforeImage&&!item.replaceVreplaceBeforeVideoideo&&!item.replaceImage&&!item.replaceVideo" class="noData">
        暂无数据
      </div>
      </div>
      </div>
      <div v-if="enterpriseNameList.length<1" class="noData">
        暂无数据
      </div>
    </div>
  </div>
</template>
<script>
import { Swiper, SwiperSlide } from "vue-awesome-swiper"
import Trend from './module/trend.vue'
import MoreLine from './module/moreLine.vue'
import LivePlayer from '@liveqing/liveplayer'
import { pollutionSourcesDetail,pollutionMonitorItem,pollutionMonitorData,pollutionCameraList,supervisionSituationList,getByEnterpriseName,pollutionCameraLive } from '@/api/pollutionSources'

export default {
  name: 'pollutionSourcesDetail',
  props: {
    listData: {
      type: Array,
      default: () => [],
    },
    id: {
      type: Number,
      default: -1
    }
  },
  components: {
    Trend,
    MoreLine,
    LivePlayer,
  },
  data() {
    return {
      dialogVisible:true,
      tableTitleList:[{name:'生产基本信息',id:0},{name:'污染状况调查信息',id:1},{name:'监控点信息',id:2},{name:'督办情况',id:3},{name:'活性碳信息',id:4},],
      activeTab:0,
      activeTabTwo:0,
      activeTabTwoName:'airPollution',
      activeTabThree:0,
      tabThreeTitleList:[],
      activeView:'list',
      taskData:[{},{},{}],
      tabTwoLeftList:[{name:'大气污染状况调查',text:'airPollution'},{name:'废水污染状况调查',text:'sewagePollution'},{name:'固体废物污染状况调查',text:'solidPollution'},{name:'噪声污染状况调查',text:'noisePollution'},],
      swiperOption: {
        direction: 'vertical',
        slidesPerView: 4,
        slidesPerGroup: 1,
        loop: true,
        autoplay: {
          delay: 2800,
          disableOnInteraction: false
        },
        on: {
          click: function (e) {
            _this.currObj = JSON.parse(e.target.getAttribute('data-href'))
          }
        }
      },
      swiperOption1: {
        direction: 'vertical',
        slidesPerView: 4,
        slidesPerGroup: 1,
        loop: false,
        autoplay: {
          delay: 2800,
          disableOnInteraction: false
        },
        on: {
          click: function (e) {
            _this.currObj = JSON.parse(e.target.getAttribute('data-href'))
          }
        }
      },
      options:[{value:'11111',label:'555555555'}],
      // AnalyzeNew:{x:[1,2,1,6,7,8],vale:[1,2,1,6,7,8]},
      AnalyzeNew:[],
      format(percentage) {
        // return percentage === 100 ? '满' : `${percentage}%`;
        return '80% /br活性炭吸附率';
      },
      detailData:{},
      productMsg:{}, // 生产基本信息
      pollutionMsg:{}, // 污染状况调查
      pointList:[], // 监控点列表
      threeTableData:[] , // 监控点数据列表
      seriesData:[],
      timeTypeList:[
        {id:1,name:'分钟'},{id:2,name:'小时'},{id:3,name:'天'}
      ],
      airSearch:{
        time1: undefined,
        time2: undefined
      },
      type:2,
      pickerOptions1: {
        selectableRange: (() => {
          const data = new Date()
          const hour = data.getHours() - 2
          const minute = data.getMinutes()
          const second = data.getSeconds()
          return [`00:00:00 - ${hour}:${minute}:${second}`]
        })(),
        disabledDate(time) {
          return new Date().getHours() < 2 ? time.getTime() >
               Date.now() - 8.64e7 : time.getTime() > Date.now()
        }
      },
      pickerOptions2: {
        disabledDate(time) {
          return new Date().getHours() < 1 ? time.getTime() >
               Date.now() - 8.64e7 : time.getTime() > Date.now()
        }
      },
      pickerOptions3: {},
      pickerOptions4: {},
      pickerOptions5: {
        disabledDate(time) {
          return time.getTime() > Date.now() - 8.64e7
        }
      },
      pickerOptions6: {},
      threeIndex:0,
      threeName:'',
      isNotificationList:[
        {id:false,name:'否'}, {id:true,name:'是'}
      ],
      isNotification:'',
      isRectificationList:[
        {id:false,name:'未整改'}, {id:true,name:'已整改'}
      ],
      isRectification:'',
      stateList:[
        {id:1,name:'在线'},{id:0,name:'离线'}
      ],
      state:'',
      pageNum:  1,
      pageSize:  5,
      total: 0,
      dateFour:[],
      situationList:[], // 督办情况列表
      enterpriseNameList:[] ,// 活性炭数据列表
      monitorList:[],// 监控列表
      monitorListAll:[],
      channelName:'',
      videoUrl:'', // 监控地址
    }
  },
 watch: {
    'airSearch.time1': {
      handler(val, oldVal) {
        if (val) {
          if (this.type === 1) {
            this.pickerOptions1 = {
              selectableRange: (() => {
                const isEqual = new Date(new Date(val).getTime()).toDateString() ===
               new Date().toDateString()
                const data = new Date()
                const hour = data.getHours() - 2 < 0
                  ? 24 - Math.abs(data.getHours() - 2) : data.getHours() - 2
                const minute = data.getMinutes()
                const second = data.getSeconds()
                return isEqual ? [`00:00:00 - ${hour}:${minute}:${second}`] : ['00:00:00-23:59:59']
              })(),
              disabledDate(time) {
                return new Date().getHours() < 2 ? time.getTime() >
               Date.now() - 8.64e7 : time.getTime() > Date.now()
              }
            }
            this.airSearch.time2 = undefined
            this.pickerOptions3 = {
              selectableRange: (() => {
                const data = new Date(this.airSearch.time1)
                const hour = data.getHours() < 10 ? `0${data.getHours()}` : data.getHours()
                const minute = data.getMinutes() < 10 ? `0${data.getMinutes()}` : data.getMinutes()
                const second = data.getSeconds() < 10 ? `0${data.getSeconds()}` : data.getSeconds()
                // eslint-disable-next-line no-nested-ternary
                const hours = data.getHours() > 22 ? 24 - data.getHours() : (data.getHours() + 2) < 10 ? `0${(data.getHours() + 2)}` : data.getHours() + 2
                return data.getHours() > 22 ? `00:00:00 - ${hours}:${minute}:${second}` : `${hour}:${minute}:${second} - ${hours}:${minute}:${second}`
              }
              )(),
              disabledDate: (time) => {
                const isEqual = new Date(new Date(val).getTime()).toDateString() ===
               new Date().toDateString()
                const date = new Date(new Date(this.airSearch.time1).getTime())
                const hours = date.getHours()
                date.setHours(0)
                date.setMinutes(0)
                date.setSeconds(0)
                const timeOptionRange = new Date(date)
                return (hours > 21 && !isEqual) ? (time.getTime() < timeOptionRange.getTime() ||
                 time.getTime() > timeOptionRange.getTime() + 8.64e7)
                  : (time.getTime() < timeOptionRange.getTime() ||
                 time.getTime() > timeOptionRange.getTime())
              }
            }
          }
          else if (this.type == 2) {
            this.pickerOptions2 = {
              selectableRange: (() => {
                const isEqual = new Date(new Date(val).getTime()).toDateString() ===
               new Date().toDateString()
                const data = new Date()
                const hour = data.getHours() - 1 < 0
                  ? 24 - Math.abs(data.getHours() - 1) : data.getHours() - 1
                const minute = data.getMinutes()
                const second = data.getSeconds()
                return isEqual ? [`00:00:00 - ${hour}:${minute}:${second}`] : ['00:00:00-23:59:59']
              })(),
              disabledDate(time) {
                return new Date().getHours() < 1 ? time.getTime() >
               Date.now() - 8.64e7 : time.getTime() > Date.now()
              }
            }
            this.airSearch.time2 = undefined
            this.pickerOptions4 = {
              selectableRange: (() => {
                const isEqual = new Date(new Date(val).getTime()).toDateString() ===
               new Date().toDateString()
                const data = new Date(this.airSearch.time1)
                const hour = data.getHours() < 10 ? `0${data.getHours()}` : data.getHours()
                const minute = data.getMinutes() < 10 ? `0${data.getMinutes()}` : data.getMinutes()
                const second = data.getSeconds() < 10 ? `0${data.getSeconds()}` : data.getSeconds()
                const data1 = new Date()
                const hour1 = data1.getHours() < 10 ? `0${data1.getHours()}` : data1.getHours()
                const minute1 = data1.getMinutes() < 10 ? `0${data1.getMinutes()}` : data1.getMinutes()
                const second1 = data1.getSeconds() < 10 ? `0${data1.getSeconds()}` : data1.getSeconds()
                return isEqual ? `${hour}:${minute}:${second} - ${hour1}:${minute1}:${second1}` : `00:00:00 - ${hour}:${minute}:${second}`
              }
              )(),
              disabledDate: (time) => {
                const isEqual = new Date(new Date(val).getTime()).toDateString() ===
               new Date().toDateString()
                const date = new Date(new Date(this.airSearch.time1).getTime())
                date.setHours(0)
                date.setMinutes(0)
                date.setSeconds(0)
                if (isEqual) {
                  return time.getTime() > date.getTime() || time.getTime() < date.getTime()
                }
                return time.getTime() < date.getTime() || time.getTime() > date.getTime() + 8.64e7
              }
            }
          } else {
            this.pickerOptions6 = {
              disabledDate: (time) => {
                const timeOptionRange = new Date(this.airSearch.time1)
                // const secondNum = 1000 * 60 * 60 * 24 * 30
                // if (!timeOptionRange) {
                //   return time.getTime() > Date.now() - 8.64e6
                // }
                // if ((timeOptionRange.getTime() + secondNum) > (Date.now() - 8.64e6)) {
                //   return time.getTime() > Date.now() - 8.64e6 ||
                //   time.getTime() < timeOptionRange.getTime() - secondNum
                // }
                return time.getTime() > Date.now() || time.getTime() < timeOptionRange.getTime() + 1000 * 60 * 60 * 24 || time.getTime() > timeOptionRange.getTime() + 1000 * 60 * 60 * 24 * 30
                // return time.getTime() > timeOptionRange.getTime() + secondNum ||
                // time.getTime() < timeOptionRange.getTime() - secondNum
              }
            }
          }
        }
      },
      deep: true // true 深度监听
    },
    'airSearch.time2': {
      handler(val, oldVal) {
        if (val) {
          if (this.type === 2) {
            this.pickerOptions4 = {
              selectableRange: (() => {
                const isEqual = new Date(new Date(this.airSearch.time1).getTime())
                  .toDateString() === new Date().toDateString()
                const isEqual2 = new Date(new Date(this.airSearch.time2).getTime())
                  .toDateString() === new Date(new Date(this.airSearch.time1)
                  .getTime()).toDateString()
                const isEqual3 = new Date(new Date(this.airSearch.time2).getTime())
                  .toDateString() === new Date().toDateString()
                const data = new Date(this.airSearch.time1)
                const hour = data.getHours() < 10 ? `0${data.getHours()}` : data.getHours()
                const minute = data.getMinutes() < 10 ? `0${data.getMinutes()}` : data.getMinutes()
                const second = data.getSeconds() < 10 ? `0${data.getSeconds()}` : data.getSeconds()
                const data1 = new Date()
                const hour1 = data1.getHours() < 10 ? `0${data1.getHours()}` : data1.getHours()
                const minute1 = data1.getMinutes() < 10 ? `0${data1.getMinutes()}` : data1.getMinutes()
                const second1 = data1.getSeconds() < 10 ? `0${data1.getSeconds()}` : data1.getSeconds()
                // eslint-disable-next-line no-nested-ternary
                return (isEqual3 && !isEqual &&  data1.getHours() < data.getHours()) ?  `00:00:00 - ${hour1}:${minute1}:${second1}` : (isEqual2 && !isEqual) ? `${hour}:${minute}:${second} - 23:59:59` : (isEqual ? `${hour}:${minute}:${second} - ${hour1}:${minute1}:${second1}` : `00:00:00 - ${hour}:${minute}:${second}`)
              }
              )(),
              disabledDate: (time) => {
                const isEqual = new Date(new Date(this.airSearch.time1).getTime())
                  .toDateString() === new Date().toDateString()
                const date = new Date(new Date(this.airSearch.time1).getTime())
                date.setHours(0)
                date.setMinutes(0)
                date.setSeconds(0)
                if (isEqual) {
                  return time.getTime() > date.getTime() || time.getTime() < date.getTime()
                }
                return time.getTime() < date.getTime() || time.getTime() > date.getTime() + 8.64e7
              }
            }
           } else if (this.type == 1) {
            this.pickerOptions3 = {
              selectableRange: (() => {
                const isEqual = new Date(new Date(val).getTime()).toDateString() ===
               new Date(new Date(this.airSearch.time1).getTime()).toDateString()
                const data = new Date(this.airSearch.time1)
                const hour = data.getHours() < 10 ? `0${data.getHours()}` : data.getHours()
                const minute = data.getMinutes() < 10 ? `0${data.getMinutes()}` : data.getMinutes()
                const second = data.getSeconds() < 10 ? `0${data.getSeconds()}` : data.getSeconds()
                // eslint-disable-next-line no-nested-ternary
                const hours = data.getHours() > 22 ? (isEqual ? data.getHours() : 24 - data.getHours()) : (data.getHours() + 2) < 10 ? `0${(data.getHours() + 2)}` : data.getHours() + 2
                // eslint-disable-next-line no-nested-ternary
                return data.getHours() > 22 ? (isEqual ? `${hours}:${minute}:${second} - 23:59:59` : `00:00:00 - ${hours}:${minute}:${second}`) : `${hour}:${minute}:${second} - ${hours}:${minute}:${second}`
              }
              )(),
              disabledDate: (time) => {
                const isEqual = new Date(new Date(this.airSearch.time1).getTime())
                  .toDateString() === new Date().toDateString()
                const date = new Date(new Date(this.airSearch.time1).getTime())
                const hours = date.getHours()
                date.setHours(0)
                date.setMinutes(0)
                date.setSeconds(0)
                const timeOptionRange = new Date(date)
                return (hours > 21 && !isEqual) ? (time.getTime() < timeOptionRange.getTime() ||
                 time.getTime() > timeOptionRange.getTime() + 8.64e7)
                  : (time.getTime() < timeOptionRange.getTime() ||
                 time.getTime() > timeOptionRange.getTime())
              }
            }
          }
        }
      },
      deep: true // true 深度监听
    }
  },
  mounted(){
    this.getPollutionSourcesDetail()

    setTimeout(()=>{
    this.getByEnterpriseName()
    },1000)
  },
  methods: {
    // 获取详情数据
    // id 汽修源11、工地源5、工业源2、加油站3、停车场15
    getPollutionSourcesDetail(){
      pollutionSourcesDetail({id:this.id}).then((res)=>{
        this.detailData=res.data.data
        const detailData=res.data.data
        if(res.data.data){
          this.productMsg=JSON.parse(res.data.data.productMsg) || {} 
          this.pollutionMsg=JSON.parse(res.data.data.pollutionMsg) || {}
          // console.log(res.data.data.pollutionType)
          if(res.data.data.pollutionType==0 || res.data.data.pollutionType==2 || res.data.data.pollutionType==3){
            this.tableTitleList=[{name:'生产基本信息',id:0},{name:'污染状况调查信息',id:1},{name:'监控点信息',id:2},{name:'督办情况',id:3},{name:'活性碳信息',id:4},]
          }else if(res.data.data.pollutionType==1){
             this.tableTitleList=[{name:'生产基本信息',id:0},{name:'监控点信息',id:2},{name:'督办情况',id:3},{name:'活性碳信息',id:4},]
          }else if(res.data.data.pollutionType==4){
             this.tableTitleList=[{name:'污染状况调查信息',id:1},{name:'监控点信息',id:2},{name:'督办情况',id:3},{name:'活性碳信息',id:4},]
          }
            
        if(detailData.pollutionType==0){
          this.tabTwoLeftList= [{name:'大气污染状况调查',text:'airPollution'},{name:'废水污染状况调查',text:'sewagePollution'},{name:'固体废物污染状况调查',text:'solidPollution'},{name:'噪声污染状况调查',text:'noisePollution'},]
        }else if(detailData.pollutionType==1){
          this.tabTwoLeftList= []
        }else if(detailData.pollutionType==2){
          this.tabTwoLeftList= [{name:'大气污染状况调查',text:'airPollution'},{name:'废水污染状况调查',text:'sewagePollution'},{name:'固体废物污染状况调查',text:'solidPollution'},{name:'噪声污染状况调查',text:'noisePollution'},]
        }else if(detailData.pollutionType==3){
          this.tabTwoLeftList= [{name:'废水污染状况调查',text:'sewagePollution'},{name:'固体废物污染状况调查',text:'solidPollution'},{name:'噪声污染状况调查',text:'noisePollution'},]
        }else if(detailData.pollutionType==4){
          this.tabTwoLeftList= [{name:'大气污染状况调查',text:'airPollution'}]
        }
        this.activeTab=this.tableTitleList[0].id
        if(detailData.pollutionType!==1){
          this.activeTabTwo=0
          this.activeTabTwoName=this.tabTwoLeftList[0].text
        }

   
        }
      
        // this.detailData=res
      })
    },
    // 获取监控点信息列表
    getPollutionMonitorItem(){
      const data={
        pollutionType:this.detailData.pollutionType
      }
      pollutionMonitorItem(data).then((res)=>{
        this.tabThreeTitleList=res.data.data
      //   console.log(this.tabThreeTitleList,'this.tabThreeTitleList')
        if(res.data.data && res.data.data.length>0){
          // console.log(list,list[0].id)
          this.activeTabThree=res.data.data[0].id
          if(res.data.data[0].id===0){
            this.getPollutionCameraList()
          }else{
            this.getPollutionMonitorData()
          }
        }
      })
    },
    // tab切换
    tabChange(item){
      this.activeTab=item.id
      const index=item.id
      if(index===2){ // 监控点信息
        this.getPollutionMonitorItem()
      }
      if(index==3){
        this.getSupervisionSituationList()
      }
    },
    // 左侧切换
    tabTwoChange(index,item){
      this.activeTabTwo=index
      this.activeTabTwoName=item.text
    },
    // 监控点tab切换
    tabThreeChange(item){
      this.activeTabThree=item.id
      if(item.id===0){
        this.getPollutionCameraList()
      }else{
       this.getPollutionMonitorData()
      }
    },
    // 获取监控点数据
    getPollutionMonitorData(){
      const data={
        startTime: this.airSearch.time1?this.airSearch.time1:"",
        endTime: this.airSearch.time2?this.airSearch.time2:"",
        pollutionType: this.detailData.pollutionType,
        type: this.activeTabThree, // this.activeTabThree
        keywords: this.detailData.name , // this.detailData.name
        timeType:this.type
      }
      pollutionMonitorData(data).then((res)=>{
        console.log(res.data.data,'ppppppppppppppppppppppppppppppp')
        let list=[]
        if(res.data.data && res.data.data.pointList && res.data.data.pointList.length>0){
          res.data.data.pointList.forEach((x,i)=>{
            if(x.itemList && x.itemList.length>0){
              x.itemList.forEach((y,z)=>{
                if(y.monitorDataItems && y.monitorDataItems.length>0){
                  y.isQualified=0
                  y.monitorDataItems.forEach((q,w)=>{
                    if(q.isQualified==1){
                      y.isQualified=1
                    }
                  })
                }
              })
            }
          })
          this.pointList=res.data.data.pointList
          this.threeName=res.data.data.pointList[0].name?res.data.data.pointList[0].name: ''
          this.threeIndex=0
          setTimeout(()=>{
           this.getSeriesData()
          },100)
        }
        console.log(list,this.pointList,this.threeName)
        // this.threeTableData=list
      })
    },
    // 列表、视图切换
    changeView(type){
      this.activeView=type
    },
    judgeNull(obj,val){
      let result = '';
      val.split(".").forEach(item => {
          obj[item]? obj=obj[item]: result='--'
          if(result !== '--')
           {
            result = obj
          }
      });
      return result
    },
     // 获取整体图表列表
    getSeriesData(){
      let series=[]
      let series1=[]
      const list=this.pointList

      list.forEach((v,i)=>{   
         if(this.threeIndex == i){
          Object.keys(v).forEach(v=>{
              console.log(v)//取到了key
              // console.log(list[i][v])//取到了值
              if(v!='itemList'&&v!='stationId'&&v!='name'){
                series.push({
                  name:v,
                  isQualified:list[i][v].isQualified,
                  value:list[i][v].value,
                  unit:list[i][v].unit,
                  x:list[i][v].x,
                })
              }
          })
         }              
      })
      console.log(series)
      this.seriesData=series
      // this.setData({
      //   seriesData:series
      // })
    },
    // 监控点信息监控点筛选
    searchThree(val){
      this.threeName=val
      this.activeView='list'

      // let list=[]
      // list=this.pointList.filter((x)=>x.name==val)
      // this.pointList.filter((x)=>x.name==val)
      if(this.pointList.length>0){
        this.pointList.forEach((x,i)=>{
          if(x.name==val){
            this.threeIndex=i
          }
        })
      }
      this.getSeriesData()
      // this.activeView='list'
    },
    changeType(params) {
      this.type = params
      this.airSearch.time1 = undefined
      this.airSearch.time2 = undefined
      // this.threeIndex=0
      // this.threeName=''
      this.activeView='list'
      this.getPollutionMonitorData()
    },
    changeTime() {
     if (!this.airSearch.time2 && this.airSearch.time1) {
       return
     }
     if (this.airSearch.time1 && !this.airSearch.time2) {
       return
     }
     if (!this.airSearch.time1) {
       this.airSearch.time2 = undefined
     }
     this.activeView='list'
     this.getPollutionMonitorData()
    },
    searchThreeMonitor(val){
      // console.log(val,this.channelName,'55555555555555555')
        this.fuzhiVideo(this.channelName) 
    },
    // 监控筛选
    stateChange(){
      this.getPollutionCameraList()
    },
    // 监控列表
    getPollutionCameraList(){
      const data={
        pageNum:1,
        pageSize:100,
        pollutionType:this.detailData.pollutionType,
        // keywords:'四川一汽贸易有限公司',
        keywords:this.detailData.name,
        state:this.state
      }
      const that=this
      pollutionCameraList(data).then((res)=>{
        let monitorList=res.data.data.records
        let monitorListAll=res.data.data.records
    
        if(res.data.data && res.data.data.records && res.data.data.records.length>0){
          let promiseArr = res.data.data.records.map((item) => {
            return that.getVideoUrl(item)
          })
          Promise.all(promiseArr).then((value)=>{
            value.forEach((x,i)=>{
              monitorList[i].coverUrl=x.coverUrl
              monitorList[i].hls=x.hls
              monitorList[i].open=false
              monitorListAll[i].coverUrl=x.coverUrl
              monitorListAll[i].hls=x.hls
              monitorListAll[i].open=false
            })
            this.monitorListAll=monitorListAll
            this.monitorList=monitorList
          })
        }else{
          this.monitorListAll=[]
          this.monitorList=[]
        }
        
      })
    },
      // 赋值显示的监控列表
    fuzhiVideo(channelId){
      if(channelId){
        const monitorList=this.monitorListAll.filter((x)=>x.channelId===channelId)
        this.monitorList=monitorList
      }else{
        this.monitorList=this.monitorListAll
      }
    },
    // 获取监控地址
    getVideoUrl(item){
      const channelId=item.channelId
      const data={
        channelId:item.channelId,
        pollutionType:this.detailData.pollutionType
      }
      // const _this = this
      return new Promise((resolve,reject) => {
          pollutionCameraLive(data).then((res)=>{
            if(res.data.data){
            resolve(res.data.data)
            }else{
              resolve({
                coverUrl:'',hls:''
              })
            }
          })
      })
    },
    // 点击播放
    openVideo(index){
      this.monitorList[index].open=true
      // this.monitorList[index].open=true
    },
     // 督办情况
    searchFour(val){
      console.log(val)
      // this.searchFour()
      this.pageNum=1
      this.getSupervisionSituationList()
    },
    // 督办情况时间change
    timeFourChange(val){
      console.log(val)
      if(val==null){
        this.dateFour=[]
      }
      this.pageNum=1
      this.getSupervisionSituationList()
    },
    // 获取督办情况列表
    getSupervisionSituationList(){
      const data={
        pageNum:this.pageNum,
        pageSize:this.pageSize,
        isNotification:this.isNotification, // true 通报 false未通报
        isRectification: this.isRectification , // 已整改 false未整改
        startTime:(this.dateFour&&this.dateFour.length>0)?this.dateFour[0]:'', // 开始时间 yyyy-MM-dd HH:mm:ss
        endTime:(this.dateFour&&this.dateFour.length>0)?this.dateFour[1]:'', // 结束时间 yyyy-MM-dd HH:mm:ss
        pollutionName: this.detailData.name, // this.data.pollutionName 污染源名称
        // pollutionName: '四川一汽贸易有限公司', // this.data.pollutionName 污染源名称
        pollutionType:this.detailData.pollutionType, // 污染源类型
      }
      supervisionSituationList(data).then((res)=>{
        this.situationList=res.data.data.records
      })
    },
    handleCurrentChange(val){
      this.pageNum=val
      this.getSupervisionSituationList()
    },
    // 获取活性炭列表
    getByEnterpriseName(){
      getByEnterpriseName({enterpriseName:this.detailData.name}).then((res)=>{
      // getByEnterpriseName({enterpriseName:'测试'}).then((res)=>{
        this.enterpriseNameList=res.data.data
      })  
    },


    //  设置默认时间七天前
    // getTimeFn() {
    //   const end = new Date();
    //   const start = new Date();
    //   start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
    //   this.value1[0] = (start);
    //   this.value1[1] = (end);
    // },
  }
}
</script>
<style lang="less" scoped>
.pollutionSourcesDetail{
  // width: 1000px;
  // height: 500px;
  // background-image: url('../../assets/tcbjNew.png');
  // background-position: 100% 100%;
  // position: relative;
  // top: 10%;
  // left: 50%;
  // margin-left: -500px;
  // z-index: 9999;
  // background-color: aliceblue;
  // border-color: #0092fe;
 
  width: 90%;
  .noData{
    width: 100%;
    text-align: center;
    font-size: 15px;
    height: 100px;
    line-height: 100px;
    color: #DCF0FF;
  }
  //列表样式
  .no-text {
      height: 2.1rem;
      line-height: 2.1rem;
      text-align: center;
      font-size: 0.18rem;
      color: #ffffff;
      margin-bottom: 0.63rem;
      padding: 0 0.4rem 0 0.82rem;
      box-sizing: border-box;
  }
  .list {
    height: 2.6rem;
    margin-bottom: 0.23rem;
    // padding: 0 0.4rem 0 0.82rem;
    box-sizing: border-box;
    margin-top: 20px;
    font-family: PingFang SC;
    font-weight: 550;
    font-size: 14px;
    color: #3076AC;
    line-height: 18px;
    border: 1px solid #063259;
    padding: 10px 20px 20px 20px;
  }
  .thead {
    display: flex;
    align-items: center;
    // background-color: #0f245e;
    opacity: 0.8;
    div {
      display: inline-block;
      width: 12.5%;
      text-align: center;
      line-height: 0.4rem;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  ::v-deep .t-body {
    // height: 1.6rem;
    // height: 100px !important;
    height: 100%;
    // overflow: auto;
  }
  .task-list {
    display: flex;
    align-items: center;
    cursor: pointer;
    // color: #97b6e4;
    font-size: 0.14rem;
    font-family: PingFang SC;
    font-weight: 400;
    font-size: 13px;
    color: #D2EAF6;
    &:nth-child(2n) {
      // background: linear-gradient(90deg, rgba(0,59,109,0) 0%);
      // background-color: #aa25a3;
      opacity: 0.8;
    }
    &:nth-child(2n + 1) {
      // background-color: #04173d;
      background: linear-gradient(90deg, rgba(0,59,109,0) 0%);
      background: linear-gradient(90deg, #09182D 0%, #082B51 100%);
      // opacity: 0.8;
    }
    div {
      width: 12.5%;
      text-align: center;
      line-height: 0.4rem;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      padding: 0 0.1rem;
      box-sizing: border-box;
    }
    .state {
      display: flex;
      justify-content: center;
      .image {
        width: 0.7rem;
        height: 0.3rem;
        background-size: 100% 100% !important;
        line-height: 0.3rem !important;
        color: #ffe27f;
      }
      .active {
        color: #4bfbff;
      }
    }
    .stateDetail{
      font-family: PingFang SC;
      font-weight: 400;
      font-size: 13px;
      color: #15B0F8;
      text-decoration: underline;
    }
  }
  .thead {
    // color: #40deff;
    font-size: 0.14rem;
  }
}
.title{
  font-family: HuXiaoBo-NanShen;
  font-weight: 400;
  font-size: 16px;
  color: #C2E7FE;
  line-height: 36px;
  text-shadow: 0px 0px 10px rgba(0,131,255,0.51);
  font-style: italic;
}
.baseInfo>div{
  width: 100%
}
.baseInfo .info{
  width: 50%;
  display: inline-block;
  font-family: PingFang SC;
  font-weight: 400;
  font-size: 13px;
  color: #DCF0FF;
  line-height: 22px;
  // margin: 16px 0;
  margin-top: 12px;
}
.baseInfo .info .title{
  font-family: PingFang SC;
  font-weight: 400;
  font-size: 13px;
  color: #5ABEFE;
  line-height: 22px;
}
.line{
  width: 100%;
  height: 1px;
  background: #093C68;
  // border: 1px solid #093C68;
}

.tableBox{

}
.table{
  display: flex;
}
.table .title{
  width: 150px;
  height: 148px;
  background: rgba(7,37,72,0.8);
  border: 1px solid #093C68;
}
.table .content{
  // width: 758px;
  height: 50px;
  border: 1px solid #093C68;
}
.tableTitle{
  display: flex;
  width: 100%;
  margin-top: 20px;
  margin-bottom: 30px;
}
.tableTitleBox{
  // width: 153px;
  flex: 1;
  height: 32px;
  background: linear-gradient(0deg, #032D57 0%, #09182D 100%);
  border: 1px solid #09294E;
  font-family: PingFang SC;
  font-weight: 400;
  font-size: 13px;
  color: #497FA7;
  line-height: 36px;
  text-align: center;
  line-height: 32px;
}
.activeTab{
  background: linear-gradient(0deg, #07498B 0%, #09182D 100%);
  border: 1px solid #09294E;
  font-family: PingFang SC;
  font-weight: 400;
  font-size: 13px;
  color: #D3E7F6;
  line-height: 36px;
  text-shadow: 0px 0px 6px rgba(0,131,255,0.51);
}
// 生产基本信息
.tabOne{
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  .tabOneBox{
    width: 50%;
    // display: inline-block;
    height: 50px;
    // line-height: 50px;
    text-align: center;
    display: flex;
    align-items: center;
    .tableName{
      height: 100%;
      width: 30%;
      border: 1px solid #093C68;
      font-family: PingFang SC;
      font-weight: 400;
      font-size: 13px;
      color: #C4D5E1;
      // line-height: 50px;
      background: rgba(7,37,72,0.8);
      border-bottom: none;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .tableName:nth-last-child(1){
      border-bottom: 1px solid #093C68;
    }
    .tableName:nth-last-child(2){
      border-bottom: 1px solid #093C68;
    }
    // .tableName:not(:nth-last-child(1)){
    //   border-bottom: none;
    // }
    // .tableName:not(:nth-last-child(2)){
    //   border-bottom: none;
    // }
    .tableInfo{
      width: calc(100% - 30%);
      height: 100%;
      border-top: 1px solid #093C68;
      border-bottom: 1px solid #093C68;
      font-family: PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #DCF0FF;
      // line-height: 50px;
      border-bottom: none;
      // overflow: hidden;
      // white-space: nowrap;
      // text-overflow: ellipsis;
      // word-break: break-all;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .tableInfo:nth-last-child(1){
      border-bottom: 1px solid #093C68;
    }
    .tableInfo:nth-last-child(2){
      border-bottom: 1px solid #093C68;
    }
    .tableInfo:nth-child(2n){
      border-right: 1px solid #093C68;
    }
  }
  .tabOneLine{
    width: 100%;
    background: rgba(7,37,72,0.5);
    border: 1px solid #093C68;
    margin: 15px 0 10px;
  }
  .tabOneTitle{
    font-family: PingFang SC;
    font-weight: 400;
    font-size: 13px;
    color: #53AFEB;
    margin-bottom: 15px;
  }
  .tabOneTop{
    width: 100%;
    display: flex;
    align-items: center;
    height: 42px;
    background: rgba(7,37,72,0.8);
    border: 1px solid #093C68;
    font-family: PingFang SC;
    font-weight: 400;
    font-size: 13px;
    color: #C4D5E1;
    >div{
     width: 33%;
     text-align: center;
     white-space: nowrap;
     text-overflow: ellipsis;
     overflow: hidden;
    }
    >div:nth-child(2){ 
      height: 42px;
      line-height: 42px;
      border-left:1px solid #093C68;
      border-right:1px solid #093C68;
    }
  }
  .tabOneContentHeight{
    height: 126px;
    overflow-y: auto;
  }
  .tabOneContent{
    font-family: PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #DCF0FF;
    border-top: 0px solid #000000;
    background: none;
    >div:nth-child(2){ 
      height: 42px;
      line-height: 42px;
      border-left:1px solid #093C68;
      border-right:1px solid #093C68;
    }
  }
  .tabOneTopNotData{
    width: 100%;
    display: flex;
    justify-content: center;
    border-top: 0px solid #000000;
    background: none;
    text-align: center;
  }
}
.tabTwo{
  display: flex;
  width: 100%;
  height: 300px;
  background: rgba(7,37,72,0);
  border: 1px solid #093C68;
  font-family: PingFang SC;
  font-weight: 400;
  font-size: 13px;
  color: #C4D5E1;
  line-height: 44px;
  .tabTwoLeft{
    width: 180px;
    height: 300px;
    background: rgba(5,33,65,0.5);
    border-right: 1px solid #093C68;
    overflow-y: auto;
    >div{
      // width: 178px;
      width: 100%;
      height: 44px;
      line-height: 44px;
      text-align: center;
      color: #C4D5E1;
      line-height: 44px;
    }
    .activeLeft{
      background: #00457A;
    }
  }
  .tabTwoRight{
    width: calc(100% - 180px);
    padding: 16px 20px;
    height: 100%;
    box-sizing: border-box;
    overflow-y: auto;
    .rowOne{
      width: 100%;
      // height: 34px;
      border: 1px solid #00457A;
      padding: 10px 20px;
      line-height: 22px;
      border-bottom: none;
    }
    .rowOne:nth-last-child(1){
      border-bottom: 1px solid #00457A;
    }
    .rowTwo{
      width: 48%;
      display: inline-block;
      height: 34px;
      border: 1px solid #00457A;
      padding: 0 20px;
      line-height: 34px;
    }
    .rowTwo:nth-child(2n-1){
      border-right: none;
    }
  }
}
.tabThree{
  .tabTitle{
    display: flex;
    font-family: PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #696f74;
    margin-bottom: 15px;
    >div{
      padding: 6px 20px;
      background: url('../../assets/<EMAIL>');
      background-size: 100% 100%;
      margin-right: 10px;
    }
    .activeThree{
      background: url('../../assets/<EMAIL>');
      background-size: 100% 100%;
      color: #DCF0FF;
    }
  }
  .chooseBox{
    display: flex;
    justify-content: space-between;
    align-items: center;
    .changeView{
      display: flex;
      align-items: center;
      font-family: PingFang SC;
      font-weight: 400;
      font-size: 13px;
      color: #497FA7;
      .activeView{
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 13px;
        color: #D4EFFF;
      }
    }
  }
  .monitor{
    width: 31%;
    margin-right: 2%;
    // display: inline-block;
    border: 1px solid #00457A;
    padding: 6px 15px 15px 15px;
    margin-bottom: 10px;
    .titleTop{
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 13px;
      color: #05D9DA;
      .title{
        font-size: 13px;
      }
      .cirleState{
        display: inline-block;
        width: 5px;
        height: 5px;
        background: #05D9DA;
        border-radius: 50%;
        margin: 0 5px;
      }
    }
    .monitorImage{
      width: 100%;
      height: 100%;
      // margin-top: 10px;
      img{
        width: 100%;
        height: 100%;
      }
    }
    .not-online {
      width: 100%;
      height: calc(2.2rem - (10.8rem - 1080px) / 3);
      margin-top: 10px;
      background-image: url("../../assets/heavily/<EMAIL>");
      background-repeat: no-repeat;
      background-size: 100% 100%;
      text-align: center;
      line-height: calc(1.88rem - (10.8rem - 1080px) / 3);
      font-size: 0.16rem;
      color: #000000;
    }
    .drain-video {
      width: 100%;
      height: calc(2.2rem - (10.8rem - 1080px) / 3);
      margin-top: 10px;
      .player-wrapper{
         width: 100%;
      height: 100%;
      }
      img{
      width: 100%;
      height: 100%;
    }
    // height: calc(2.2rem - (10.8rem - 1080px) / 3);
    // background-image: url("../../assets/heavily/<EMAIL>");
    // background-repeat: no-repeat;
    // background-size: 100% 100%;
    // padding: 0.16rem 0.2rem;
    // box-sizing: border-box;
  }
  }
}

  .tabFour{
    .region-box {
      // padding: 0.05rem 0.25rem;
      // box-sizing: border-box;
      // background: rgba(6, 30, 101, 1);
      // border: 1px solid rgba(2, 174, 210, 1);
      display: flex;
      justify-content: space-between;
      align-items: center;
      .street {
        width: 2.6rem;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .site-type {
        width: 2.6rem;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .site-name {
        width: 3rem;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .select-time {
        width: 3.8rem;
        font-family: fontnameRegular;
        font-size: 0.36rem;
        margin-left: 0.35rem;
      }
      .line {
        width: 0.02rem;
        height: 0.7rem;
        background-image: url("../../assets/xgx.png");
        background-repeat: no-repeat;
        background-size: 100% 100%;
      }
      .time {
        width: 4rem;
        font-size: 0.32rem;
        font-family: "DS-DIGII";
        font-weight: 400;
        text-align: center;
        // display: none;
      }
      .name,
      .type {
        width: 3rem;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .type {
        > div {
          width: 1.6rem;
          height: 0.44rem;
          background: #0084ff;
          border-radius: 0.44rem;
          line-height: 0.44rem;
          font-size: 0.24rem;
          display: flex;
          justify-content: center;
        }
      }
      .name {
        // padding-left: 0.3rem;
        // width: 3.5rem;
      }
      .address {
        width: 6rem;
        font-size: 0.2rem;
        display: flex;
        align-items: center;
        // padding-left: 0.5rem;
        .address-text {
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          flex: 1;
        }
        .icon {
          width: 0.3rem;
          height: 0.32rem;
          margin-right: 0.28rem;
          background-image: url("../../assets/<EMAIL>");
          background-size: 100% 100%;
        }
      }
    }
      // position: absolute;
      // pointer-events: auto;
      // z-index: 2;
      // bottom: 0;
      // height: 2.48rem;
      // width: calc(100% - 4.5rem);
     
    }
// 下拉框样式修改//////////
// 筛选框样式 
::v-deep  .el-input__inner{
  background: linear-gradient(90deg, #09182D 0%, #082B51 100%);
  border: 1px solid #063259;
  font-family: PingFang SC;
  font-weight: 400;
  font-size: 13px;
  color: #DCF0FF;
  // color: ;
}
::v-deep .el-range-input{
  background: linear-gradient(90deg, #09182D 0%, #082B51 100%);
  font-family: PingFang SC;
  font-weight: 400;
  font-size: 13px;
  color: #DCF0FF;
}
// placeholder样式
::v-deep .el-input__inner::placeholder,
::v-deep .el-textarea__inner::placeholder {
  font-family: PingFang SC;
  font-weight: 400;
  font-size: 13px;
  color: #366695 !important;
  line-height: 40px;
}

::v-deep .el-input.is-disabled .el-input__inner{
  border: 1px solid #063259;
}
////////////////////////////////////////////
::v-deep .el-select .el-select-dropdown{
  background: linear-gradient(90deg, #09182D 0%, #082B51 100%) !important;
  // font-family: PingFang SC;
  // font-weight: 400;
  // font-size: 13px;
  // // color: #366695;
  // color: rebeccapurple !important;
  border: none;
  // // color: $white;
  // span{
  //   // color: $white;
  // }
}
// 下拉框hover背景
::v-deep .el-select-dropdown__item.hover, .el-select-dropdown__item:hover{
  background-color: #767983 !important;
  color: #fff;
  //  background: linear-gradient(90deg, #09182D 0%, #082B51 100%);
}
::v-deep .el-popper, .popper__arrow{
    // top: -6px;
    // left: 50%;
    // margin-right: 3px;
    // border-top-width: 0;
    // border: none; 
        // border-width:0px !important;
}

////////////////////////////////////////////

// 活性炭
.tabFive{
    display: flex;
    justify-content: space-between;
    // align-items: center;
    flex-wrap: wrap;
    height: 3.1rem;
    overflow: auto;
  .tabFiveBox{
    width:45%;
    height: 236px;
    background: rgba(5,33,65,0.36);
    border: 1px solid #093C68;
    padding: 20px 20px 10px 20px;
    margin-bottom: 15px;
    display: flex;
    justify-content: space-between;
    // align-items: center;
    .tabFiveBoxLeft{
      .leftTitle{
        width: 239px;
        height: 28px;
        background: linear-gradient(90deg, rgba(22,110,199,0.5) 0%, rgba(9,16,14,0.2) 100%);
        opacity: 0.8;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 14px;
        color: #DCF1EF;
        padding-left: 10px;
        line-height: 28px;
      }
      .leftContent{
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 13px;
        color: #D3E7F6;
        margin-bottom: 10px;
        .name{
          color: #5ABEFE;
        }
      }
    }
    .tabFiveBoxRight{
      position: relative;
      color: #fff;
      width: 180px;
      height: 180px;
      .progressText{
        position: absolute;
        top: 46px;
        right: 0;
        z-index: 9999;
        font-family: DIN;
        font-weight: 500;
        font-size: 36px;
        color: #A5B2C8;
        width: 180px;
        text-align: center;
        .text{
          font-family: Microsoft YaHei;
          font-weight: 400;
          font-size: 12px;
          color: #A5B2C8;
        }
      }
      // 进度条背景色
      ::v-deep .el-progress-circle__track{
         stroke:  rgba(5,33,65,1) !important;
        //  background: rgba(5,33,65,0.36);
      }
      .blue{
         ::v-deep .el-progress-circle__path{
           stroke: linear-gradient(90deg, #166EC7 0%, rgba(9,16,14,0.5) 100%) !important;
        }
        //  ::v-deep  .el-progress-bar__outer{
        //   background:linear-gradient(to right#8075fd , #b87bfe)  
        // }
        // ::v-deep .el-progress-bar__inner{
        //   background: linear-gradient(90deg, #FEC015 0%, #FE8B25 100%) !important;
        // }
      }
      .red{
        ::v-deep .el-progress-circle__path{
           stroke:  rgb(207, 55, 55) !important;
        }
      }
      // width: 122px;
      // height: 122px;
    }
  }
}
</style>
<style lang="less">
  .suggestPagetion {
    display: flex;
    justify-content: flex-end;
    .firstPage,
    .lastPage {
      span {
        cursor: pointer;
        display: block;
        text-align: center;
        line-height: 26px;
        width: 50px;
        height: 26px;
        // background: rgba(255, 255, 255, 0);
        // border: 1px solid #1a2d3d;
        // border-radius: 4px;
        margin-top: 10px;
        color: #fff;

        background: #09182D;
        border-radius: 4px;
        border: 1px solid #063259;
        font-family: PingFang;
        font-weight: 400;
        font-size: 14px;
        color: #6F7F8A;
      }
    }
    .billPagenation {
      text-align: right;
      margin-top: 10px;
      margin-right: 85px;
      .btn-prev,
      .btn-next {
        // background: rgba(255, 255, 255, 0);
        // border: 1px solid #1a2d3d;
        // border-radius: 4px;
        padding: 0 6px;
        // font-family: PingFang SC;
        // color: #fff !important;
        background: #09182D;
        border-radius: 4px;
        border: 1px solid #063259;
        font-family: PingFang;
        font-weight: 400;
        font-size: 14px;
        color: #6F7F8A!important;
      }
      .number,
      .more {
        background: rgba(255, 255, 255, 0) !important;
        border: 1px solid #1a2d3d;
        border-radius: 4px;
        font-family: PingFang SC;
        font-weight: 400;
        color: #fff !important;
      }
      .active {
        background: #0a3c6c !important;
        border: 1px solid #0a3c6c;
        border-radius: 4px;
      }
    }
  }

 /* 自定义滚动条轨道 */
::-webkit-scrollbar-track {
  background: rgba(116, 182, 224, 0.1);
}
/* 自定义滚动条的滑块 */
::-webkit-scrollbar-thumb {
  background: rgba(116, 182, 224, 0.5);
}
/* 当滑块悬停或被点击时的颜色 */
::-webkit-scrollbar-thumb:hover {
  background: rgba(116, 182, 224, 0.5);
}

</style>