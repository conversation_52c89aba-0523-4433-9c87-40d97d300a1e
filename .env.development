# current runtime environment
ENV = "development"
VUE_APP_ENV = "development"
# base api
  VUE_APP_BASE_API = http://ep.vankeytech.com:9876/api/v1/
# 谢梁
# VUE_APP_BASE_API = http://192.168.0.61:10001/api/v1/
#    VUE_APP_BASE_API = http://www.jinnq.com/api/v1/


# vue-cli uses the VUE_CLI_BABEL_TRANSPILE_MODULES environment variable,
# to control whether the babel-plugin-dynamic-import-node plugin is enabled.
# It only does one thing by converting all import() to require().
# This configuration can significantly increase the speed of hot updates,
# when you have a large number of pages.
# Detail:  https://github.com/vuejs/vue-cli/blob/dev/packages/@vue/babel-preset-app/index.js

VUE_CLI_BABEL_TRANSPILE_MODULES = true

#VUE_WS_API = ws://ep.vankeytech.com:8835/ws
#VUE_WS_API_1 = ws://ep.vankeytech.com:23001/ws
#VUE_WS_API_2 = ws://111.9.60.160:29999/ws
#VUE_WS_API_3 = ws://ep.vankeytech.com:23001/ws

VUE_APP_WS_API = ws://221.237.107.77:8834/ws
VUE_APP_WS_API_1 = wss://www.jinnq.com:27712/ws
VUE_APP_WS_API_2 = ws://www.jinnq.com:9880/ws
VUE_APP_WS_API_3 = wss://www.jinnq.com:27712/ws
