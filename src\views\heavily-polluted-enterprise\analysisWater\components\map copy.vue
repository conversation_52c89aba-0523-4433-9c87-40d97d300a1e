<template>
  <div class="analysisMap-container">
    <div ref="analysisMapContainer" class="analysisMapContainer"></div>
  </div>
</template>

<script>
import AMap from 'AMap'
import jinniuArea from '@/assets/map-geojson/jinniu_area.json'
import noiseClose from '@/assets/noise/<EMAIL>'; // 噪声弹窗关闭
import QYCB from '@/assets/heavily-polluted-enterprise/<EMAIL>'  // 企业超标
import QYZC from '@/assets/heavily-polluted-enterprise/<EMAIL>'  // 企业超标
import electricityBg from '@/assets/noise/<EMAIL>'; // 用电弹窗背景
import districtRiver from "@/assets/jinniu-river4.png";
const TEXT_COLOR = {
  offLine: {
    text: '离线',
    color: '#ACACAC'
  },
  0: {
    text: '优',
    color: '#01FF01'
  },
  50: {
    text: '良',
    color: '#E8D505'
  },
  100: {
    text: '轻度污染',
    color: '#FD8200'
  },
  150: {
    text: '中度污染',
    color: '#FD0001'
  },
  200: {
    text: '重度污染',
    color: '#95014B'
  },
  300: {
    text: '严重污染',
    color: '#7E0226'
  },
}
const AQI_LEVEL = [ 0, 50, 100, 150, 200, 300, 500 ]
export default {
  name: '',
  data() {
    return {
      // 地图配置
      mapConfig: {
        zoom: 13.8,
        zooms: [12, 20],
        center: [104.061111, 30.714222],
        mapStyle: 'amap://styles/0739a6632d849ae1b5e6f1c52f7d12c3',
        viewMode: '3D',
        zoomEnable: true,
        dragEnable: true,
        // rotation: 45,
        // pitch: 30, // 地图倾斜
      },
      mapIcon: {
        1: require('@/assets/recheck/sz_0.png'),
        2: require('@/assets/recheck/cg_1.png'),
        3: require('@/assets/recheck/cg_2.png'),
        4: require('@/assets/recheck/cg_3.png'),
        5: require('@/assets/recheck/cg_4.png'),
        6: require('@/assets/recheck/cg_5.png'),
        7: require('@/assets/recheck/cg_6.png')
      },
      currentMarker: {},
      stationMarkerList: [],
      infoWindow: null,
      AMap: null,
      circleMap: null,
      companyMarkerList: [],
      myCanvas: null,
      myContext: null,
      canvasRadius: 0,
      CanvasLayer: null,
      animFrameId: 0,
      lineLayer: null,
      rectLayer: null
    }
  },
  created() {
    window.closeInfoWindow2 = this.closeInfoWindow2
  },
  mounted() {
    this.initMap()
  },
  props: {
    markerList: {
        type: Array,
        default: () => []
    },
    distance: {
      type: Number | String,
      default: 500
    },
    stationList: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    // 初始化地图
    initMap() {
       this.$nextTick(() => {
            const map = new AMap.Map(this.$refs.analysisMapContainer, this.mapConfig);
            this.AMap = map;
            map.on('click', (e) => {
                console.log('点击地图', e)
            })
            // this.creatGeojson(map);
            this.dynamicRivderLayer()
            if(this.markerList.length) {
                this.$nextTick(() => {
                    this.setMarkers()
                })
            }
       })
    },
    dynamicRivderLayer() {
    // const bounds = new AMap.Bounds([103.9, 30.647], [104.1635, 30.823]);
    // const bounds = new AMap.Bounds([103.933, 30.647], [104.15, 30.805]);
    // const bounds = new AMap.Bounds([103.95, 30.65], [104.148, 30.808]);
    // const bounds = new AMap.Bounds([103.937, 30.661], [104.160, 30.811]); // 地图3
    const bounds = new AMap.Bounds([103.9363,30.662957], [104.160597,30.807361]);
    const imageLayer = new AMap.ImageLayer({
      url: districtRiver, // districtRiver
      bounds: bounds,
      zooms: [3, 18],
      opacity: 1
    });
    imageLayer.setMap(this.AMap);
    imageLayer.on('click', (e)=> {
      console.log(e)
    })
  },
    // 区域覆盖物点击事件
    handleClick(e){
        console.log('区域点击事件')
    },
    //  空气等级过滤
    waterLevelFilter(waterLevel){
      if(!waterLevel) return this.mapIcon[1]
      let icon = this.mapIcon[1]
      if(waterLevel === 'I') icon = this.mapIcon[1]
      else if(waterLevel === 'II') icon = this.mapIcon[1]
      else if(waterLevel === 'III') icon = this.mapIcon[2]
      else if(waterLevel === 'IV') icon = this.mapIcon[3]
      else if(waterLevel === 'V') icon = this.mapIcon[4]
      else icon = this.mapIcon[5]
      return icon
    },
    // 添加站点icon
    setMarkers() {
        if(this.stationMarkerList.length) {
            this.AMap.remove(this.stationMarkerList)
            this.stationMarkerList = []
        }
        this.stationMarkerList = []
        this.markerList.forEach((item, index) => {
            this.addMaker(item)
        })
        if(this.markerList.length) {
            this.currentMarker = this.markerList[0]
            this.setINfoWindow(this.markerList[0])
            // this.setCircle(this.markerList[0])
            // this.setCanvas(this.markerList[0])
            this.setRect(this.markerList[0])
            this.AMap.setCenter([this.markerList[0].lng, this.markerList[0].lat])
            this.$emit('handleChangeStation', this.markerList[0].stationId)
        }
    },
    addMaker(item) {
      console.log('item', item)
        let image = this.waterLevelFilter(item.waterType)
        if(item.online !== 1) image = this.mapIcon[1]
        const icon = new AMap.Icon({
          // 图标尺寸
          size: new AMap.Size(38, 27),
          // 图标的取图地址
          image: image,
          // 图标所用图片大小
          imageSize: new AMap.Size(38, 27),
          // 图标取图偏移量
          imageOffset: new AMap.Pixel(0, 0)
        })
        const markerOptions = {
          map: this.AMap,
          position: new AMap.LngLat(item.lng, item.lat),
          offset: new AMap.Pixel(-19, -13.5),
          icon: icon,
          zIndex: 9,
          data: item
        }
        let markerIcon = new AMap.Marker(markerOptions)
        // AMap.event.addListener(markerIcon, ()=> {
        //     this.handleClickMarker
        // })
        markerIcon.on('click', this.handleClickMarker)
        this.stationMarkerList.push(markerIcon)

    },
    closeInfoWindow2() {
      if (this.AMap) {
        this.AMap.clearInfoWindow()
      }
    },
    // 计算颜色 污染程度
    computedTextColor(data) {
        let pollutionType = `${data.waterType}类`
        let currentColor = '#01FF01'
        currentColor = data.waterType == 'Ⅰ' || data.waterType == 'Ⅱ'
          ? 'rgb(22, 145, 249)'
          : data.waterType == 'Ⅲ'
          ? 'rgb(0, 228, 0)'
          : data.waterType == 'Ⅳ'
          ? 'rgb(255, 255, 0)'
          : data.waterType == 'Ⅴ'
          ? 'rgb(255, 126, 0)'
          : 'rgb(255, 0, 0)'
        return { pollutionType, currentColor }
    },
    // 添加弹窗
    setINfoWindow(data) {
      const { pollutionType, currentColor } = this.computedTextColor(data)
      let infoWindowContent = `
          <div class="infoWindow" style="background: url('${electricityBg}') no-repeat;">
            <span class="title" style="">站点信息</span>
            <img class="close" src="${noiseClose}" style="" onclick="closeInfoWindow2()"/>
            <div class="content">
                <div class="time">监测时间：${data.updateTime ? data.updateTime.substring(0, 16) : "--"}</div>
                <div class="item">
                    <span class="label">站点名称：</span>
                    <span class="value">${data.stationName || '--'}</span>
                </div>
                <div class="item">
                    <span class="label">WQI：</span>
                    <span class="value" style="color:#ffffff"><b class="number" style="color: ${currentColor}">${(data.wpi || data.wpi === 0 || data.wpi === '0') ? data.wpi : '--'}</b><span style="background: #0E5EAF; display: inline-block;padding: 0 10px;border-radius 5px;">${pollutionType}</span></span>
                </div>
                <div class="item">
                    <span class="label">所属河流：</span>
                    <span class="value">${data.riverName || '--'}</span>
                </div>
                <div class="item">
                    <span class="label">详细地址：</span>
                    <span class="value">${data.stationAddress || '--'}</span>
                </div>
            </div>
          </div>
          `
        if (this.infoWindow) {
        this.infoWindow.setContent(infoWindowContent)
        this.infoWindow.setPosition([data.lng, data.lat]) // 更新点标记位置
        this.infoWindow.open(this.AMap)
      } else {
        // 创建一个自定义内容的 infowindow 实例
        this.infoWindow = new AMap.InfoWindow({
          position: [data.lng, data.lat],
          offset: new AMap.Pixel(-1, 5),
          content: infoWindowContent,
          zIndex: 10
        })
        this.infoWindow.open(this.AMap)
        // this.infoWindow.on('close', this.handlecloseInfoWindow2)
      }
    },
    // 添加圆圈
    setCircle(data) {
      return
      if(this.circleMap) {
        this.circleMap.setCenter([data.lng, data.lat])
      } else {
        //  添加圆圈
        let circle = new AMap.Circle({
            map: this.AMap,
            center: [data.lng, data.lat],         //设置线覆盖物路径
            radius: this.distance,
            strokeColor: "#fff", //边框线颜色
            strokeStyle: 'dashed',
            strokeOpacity: 0.5,       //边框线透明度
            strokeWeight: 1,        //边框线宽
            fillColor: "#fff", //填充色
            fillOpacity: 0//填充透明度
        });
        this.circleMap = circle
      }
    },
    // 波纹动画
    setCanvas(item) {
      return
      if(this.animFrameId) {
        AMap.Util.cancelAnimFrame(this.animFrameId);
      }
      if(!this.myContext) {
        this.myCanvas = document.createElement('canvas');
        this.myCanvas.setAttribute('width', 200)
        this.myCanvas.setAttribute('height', 200)
        this.myContext = this.myCanvas.getContext('2d')
        this.myContext.fillStyle = 'rgba(255,30,30, 0.2)';
        this.myContext.strokeStyle = 'rgba(255,30,30, 0.2)';
        this.myContext.globalAlpha = 1;
        this.myContext.lineWidth = 2;
        const centerPoint = new AMap.LngLat(item.lng, item.lat)
        const pointEN = centerPoint.offset(this.distance,this.distance) //向东1000m，向北1000m的位置的经纬度
        const pointWS = centerPoint.offset(-this.distance,-this.distance) //向西1000m，向南1000m的位置的经纬度
        this.CanvasLayer = new AMap.CanvasLayer({
          canvas: this.myCanvas,
          bounds: new AMap.Bounds(pointWS, pointEN),
          // bounds: new AMap.Bounds([104.08876,30.726911], [104.07831,30.717928]),
          zooms: [12, 18],
          zIndex: 9999
        });
         this.AMap.add(this.CanvasLayer)
         this.drawCanvas()
      } else {
        const centerPoint = new AMap.LngLat(item.lng, item.lat)
        const pointEN = centerPoint.offset(this.distance,this.distance) //向东1000m，向北1000m的位置的经纬度
        const pointWS = centerPoint.offset(-this.distance,-this.distance) //向西1000m，向南1000m的位置的经纬度
        this.CanvasLayer.setOptions({
          bounds: new AMap.Bounds(pointWS, pointEN),
        })
        this.drawCanvas()
      }

    },
    drawCanvas() {
      this.myContext.clearRect(0, 0, 200, 200)
      this.myContext.globalAlpha = (this.myContext.globalAlpha - 0.01 + 1) % 1;
      this.canvasRadius = (this.canvasRadius + 1) % 100;
      this.myContext.beginPath();
      this.myContext.arc(100, 100, this.canvasRadius, 0, 2 * Math.PI);
      this.myContext.fill();
      this.myContext.stroke();
      this.CanvasLayer.reFresh();
      this.animFrameId = AMap.Util.requestAnimFrame(this.drawCanvas);
    },
    setRect(data) {
      const stationData = {
        '园林大队站': [104.010793, 30.754641],
        '付家站': [104.017312, 30.746561],
        '踏水桥站': [104.034126, 30.744566],
        '连心桥站': [104.050769, 30.725786],
        '洞子口老街站': [104.052406, 30.722922],
        '沙河大桥站': [104.055375, 30.719191],
        '高桥站': [104.047469, 30.721744],
        '西北桥站': [104.060483, 30.694354],
        '泰宏桥站': [104.091629, 30.697706],
        '大湾桥站': [104.102485, 30.743909]
      }
      // 第一步 拿到上游坐标点 当前站点坐标
      // 上游站点坐标
      const [lng0, lat0] = stationData[data.stationName]
      const upstreamCoordinates = new AMap.LngLat( lng0, lat0)
      // 当前站点坐标
      const CurrentSiteCoordinates = new AMap.LngLat(data.lng, data.lat)

      // 第二步 根据距离，计算四个点经纬度
        const point0 = upstreamCoordinates.offset(this.distance / 1.414, this.distance / 1.414) // 向东 向北偏移
        const point1 = upstreamCoordinates.offset(-this.distance / 1.414, -this.distance / 1.414) //向西 向南偏移
        const point3 = CurrentSiteCoordinates.offset(this.distance / 1.414, this.distance / 1.414) // 向东 向北偏移
        const point2 = CurrentSiteCoordinates.offset(-this.distance / 1.414, -this.distance / 1.414) // 向东 向北偏移
      // 第三步 绘制矩形
      if(!this.rectLayer) {
        this.rectLayer = new AMap.Polygon({
          map: this.AMap,
          path: [point0, point1, point2, point3],
          strokeColor: 'rgb(255,255,255)',
          strokeOpacity: 0.2,
          strokeWeight: 2,
          fillColor:  'rgb(255,255,255)',
          fillOpacity: 0,
          strokeStyle: 'dashed'
        })
      } else {
        this.rectLayer.setOptions({
          path: [point0, point1, point2, point3],
        })
      }
    },
    // 点击站点
    handleClickMarker(e) {
      const { data } = e.target.w
      this.currentMarker = data
      this.setINfoWindow(data)
      this.setRect(data)
      // this.setCircle(data)
      // this.setCanvas(data)
      this.AMap.setCenter([data.lng, data.lat])
      this.$emit('handleChangeStation', data.stationId)
    },
    // 添加站点列表marker
    setStationMarkers() {
      if(this.companyMarkerList.length) {
          this.AMap.remove(this.companyMarkerList)
          this.companyMarkerList = []
      }
      this.stationList.forEach((item) => {
        let image = QYZC
        if(item.isAlarm) {
          image = QYCB
        } else {
          image = QYZC
        }
        const icon = new AMap.Icon({
          // 图标尺寸
          size: new AMap.Size(41, 51),
          // 图标的取图地址
          image: image,
          // 图标所用图片大小
          imageSize: new AMap.Size(41, 51),
          // 图标取图偏移量
          imageOffset: new AMap.Pixel(0, 0)
        })
        const markerOptions = {
          map: this.AMap,
          position: new AMap.LngLat(+item.lng, +item.lat),
          offset: new AMap.Pixel(-20.5, -43.5),
          icon: icon,
          zIndex: 9,
          data: item
        }
        let markerIcon = new AMap.Marker(markerOptions)
        this.companyMarkerList.push(markerIcon)
        markerIcon.on('click', (e) => {
          const { data } = e.target.w
          this.$emit('handleClickCompanyMarker', data)
          console.log('站点被点击了',e)
        })
      })

    },
    // 创建飞线
    addFlyLine() {
      if(!this.AMap) return
      const data = this.stationList.map(item => {
        console.log('item', item)
        return {
          lnglat: [
            [this.currentMarker.lng, this.currentMarker.lat],
            [item.lng, item.lat]
          ]
        }
      })
      if(!this.lineLayer) {
        // 创建飞线
        this.lineLayer = new Loca.LinkLayer({
            map: this.AMap,
            fitView: false,
          });
        this.lineLayer.setData(data, {
          lnglat: 'lnglat'
        });
        this.lineLayer.setOptions({
            blendMode: 'lighter',
              style: {
                  // 曲率 [-1, 1] 区间
                  curveness: 0.005,
                  // curveness: function(data) {
                  //   console.log('item.distance',item.distance)
                  //   if(item.distance < 0.5){
                  //     return 0.07;
                  //   } else if(item.distance < 1){
                  //     return 0.05;
                  //   } else if(item.distance < 1.5){
                  //     return 0.02;
                  //   }  else if(item.distance < 2){
                  //     return 0.01;
                  //   } else {
                  //     return 0.005;
                  //   }
                  // },
                  opacity: 0.8,
                  color: '#5DFBF9'
          }
        });
      } else {
        this.lineLayer.setData(data, {
          lnglat: 'lnglat'
        });
      }

      this.lineLayer.setzIndex(1000)
      this.lineLayer.render();
    }
  },
  computed: {},
  watch: {
    markerList: {
        handler(val) {
            this.setMarkers()
        },
        deep: true,
        immediate: true
    },
    stationList: {
        handler(val) {
            this.setStationMarkers()
            this.addFlyLine()
        },
        deep: true,
        immediate: true
    },
    distance(val) {
      if(this.circleMap) {
        this.circleMap.setRadius(this.distance)
      }
      // this.setCanvas(this.currentMarker)
      this.setRect(this.currentMarker)
      this.addFlyLine()
    }
  },
  components: {},
  beforeDestroy() {
    this.stationMarkerList.length && this.AMap.remove(this.stationMarkerList)
    this.infoWindow && this.AMap.remove(this.infoWindow)
    this.circleMap && this.AMap.remove(this.circleMap)
    this.companyMarkerList.length && this.AMap.remove(this.companyMarkerList)
    this.lineLayer && this.AMap.remove(this.lineLayer)
    document.querySelector(`canvas.amap-layer`)?.getContext('webgl')?.getExtension('WEBGL_lose_context')?.loseContext()
    this.AMap.remove(this.AMap.getLayers(), this.AMap.getAllOverlays())
    this.AMap && this.AMap.destroy()
    this.AMap = null
    window.closeInfoWindow2 = null
  }
}
</script>

<style lang="less" scoped>
.analysisMap-container {
  width: 100%;
  height: 100%;
  //   position: relative;
  .analysisMapContainer {
    width: 100%;
    height: 100%;
  }
}
</style>
<style lang="less">
.analysisMap-container {
  .amap-info-close {
    display: none;
  }
  .infoWindow {
    pointer-events: auto;
    width: 330px;
    // height: 240px;
    background-repeat: no-repeat;
    background-size: 100% 100% !important;
    padding: 15px 25px;
    position: relative;
    box-sizing: border-box;
    .title {
      position: absolute;
      top: 35px;
      left: 38px;
      font-size: 18px;
      font-family: YouSheBiaoTiHei;
      font-weight: 400;
      color: #ffffff;
      background: linear-gradient(0deg, #56d1ed 0%, #caf6ff 100%);
      background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .close {
      position: absolute;
      width: 22px;
      height: 22px;
      top: 47px;
      right: 25px;
      cursor: pointer;
    }
    .content {
      margin-top: 50px;
      padding: 0 20px 20px;
      .time {
        font-size: 12px;
        font-family: PingFang SC;
        font-weight: 400;
        color: #7798b9;
      }
      .item {
        display: flex;
        .label {
          font-size: 12px;
          font-family: PingFang SC;
          font-weight: 400;
          color: #dcf0ff;
          line-height: 20px;
        }
        .value {
          font-size: 12px;
          font-family: PingFang SC;
          font-weight: 400;
          color: #56a7f1;
          line-height: 20px;
          flex: 1;
        }
        .number {
          font-family: YouSheBiaoTiHei;
          font-size: 18px;
          font-weight: bold;
          margin-right: 10px;
        }
      }
    }
  }
}
</style>
