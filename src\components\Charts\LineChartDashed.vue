<style lang="less" scoped>
.no-data {
  position: relative;
}
.text {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  font-size: 0.2rem;
}
</style>
<template>
  <div>
    <div
      v-if="propData.dataList.length > 0"
      :id="id"
      :style="{ height: height, width: width }"
    />
    <div v-else class="no-data" :style="{ height: height, width: width }">
      <!--      <div class="text">设备离线</div>-->
      <div class="text">暂无数据</div>
    </div>
  </div>
</template>

<script lang="ts">
import * as echarts from 'echarts'

import { Component, Prop, Watch } from "vue-property-decorator";
import { mixins } from "vue-class-component";
import ResizeMixin from "./mixins/resize";
interface AirData {
  bottomList: string[];
  dataList: string[];
  unit: string;
  name?: string;
  colorType?: string;
  warnValue?: number;
  miniValue?: number;
  maxValue?: number;
  valueType?: null | number;
}
@Component({
  name: "LineChartDashed",
})
export default class extends mixins(ResizeMixin) {
  // @Prop({ default: "chart" }) private className!: string;
  @Prop({ default: "chart" }) private id!: string;
  @Prop({ default: "200px" }) private width!: string;
  @Prop({ default: "200px" }) private height!: string;
  @Prop({ required: true }) private propData!: AirData;
  @Prop({ required: false, default: "rgba(14,156,255,1)" })
  private bgColor!: string;
  @Prop({ required: false, default: false }) private smooth!: boolean;
  @Prop({ required: false, default: true }) private bgColorState!: boolean;
  @Prop({ required: false, default: "" }) private xText!: string;
  @Prop({ required: false, default: null }) private valueType!: null | number;
  @Watch("propData", { immediate: true, deep: true }) public onMsgChanged(
    newValue: AirData,
    oldValue: AirData
  ) {
    if (this.propData) {
      if (this.chart) {
        // this.chart.clear();
        // this.chart.dispose();
        // this.chart = null;
        this.$nextTick(() => {
          this.updateChart();
        });
        return;
      }
      this.$nextTick(() => {
        this.initChart();
      });
    }
  }

  // SO2:1 NO2:1 O3:1 CO:1 PM10:1 PM:2.5
  private itemStyleColor = {
    SO2: {
      color: (params: any) => {
        if (params.data >= 0 && params.data <= 150) {
          return "rgb(0,255,0)";
        } else if (params.data > 150 && params.data <= 500) {
          return "rgb(255,255,0)";
        } else if (params.data > 500 && params.data <= 650) {
          return "rgb(255,126,0)";
        } else if (params.data > 650 && params.data <= 800) {
          return "rgb(255,0,0)";
        } else if (params.data > 800 && params.data <= 1600) {
          return "rgb(153,0,76)";
        } else {
          return "rgb(126,0,35)";
        }
      },
    },
    NO2: {
      color: (params: any) => {
        if (params.data >= 0 && params.data <= 100) {
          return "rgb(0,255,0)";
        } else if (params.data > 100 && params.data <= 80) {
          return "rgb(255,255,0)";
        } else if (params.data > 80 && params.data <= 200) {
          return "rgb(255,126,0)";
        } else if (params.data > 200 && params.data <= 700) {
          return "rgb(255,0,0)";
        } else if (params.data > 700 && params.data <= 1200) {
          return "rgb(153,0,76)";
        } else {
          return "rgb(126,0,35)";
        }
      },
    },
    O3: {
      color: (params: any) => {
        if (params.data >= 0 && params.data <= 160) {
          return "rgb(0,255,0)";
        } else if (params.data > 160 && params.data <= 200) {
          return "rgb(255,255,0)";
        } else if (params.data > 200 && params.data <= 300) {
          return "rgb(255,126,0)";
        } else if (params.data > 300 && params.data <= 400) {
          return "rgb(255,0,0)";
        } else if (params.data > 400 && params.data <= 800) {
          return "rgb(153,0,76)";
        } else {
          return "rgb(126,0,35)";
        }
      },
    },
    CO: {
      color: (params: any) => {
        if (params.data >= 0 && params.data <= 5) {
          return "rgb(0,255,0)";
        } else if (params.data > 5 && params.data <= 10) {
          return "rgb(255,255,0)";
        } else if (params.data > 10 && params.data <= 35) {
          return "rgb(255,126,0)";
        } else if (params.data > 35 && params.data <= 60) {
          return "rgb(255,0,0)";
        } else if (params.data > 60 && params.data <= 90) {
          return "rgb(153,0,76)";
        } else {
          return "rgb(126,0,35)";
        }
      },
    },
    PM25: {
      color: (params: any) => {
        if (params.data >= 0 && params.data <= 35) {
          return "rgb(0,255,0)";
        } else if (params.data > 35 && params.data <= 75) {
          return "rgb(255,255,0)";
        } else if (params.data > 75 && params.data <= 115) {
          return "rgb(255,126,0)";
        } else if (params.data > 115 && params.data <= 150) {
          return "rgb(255,0,0)";
        } else if (params.data > 150 && params.data <= 250) {
          return "rgb(153,0,76)";
        } else {
          return "rgb(126,0,35)";
        }
      },
    },
    PM10: {
      color: (params: any) => {
        if (params.data >= 0 && params.data <= 50) {
          return "rgb(0,255,0)";
        } else if (params.data > 50 && params.data <= 150) {
          return "rgb(255,255,0)";
        } else if (params.data > 150 && params.data <= 250) {
          return "rgb(255,126,0)";
        } else if (params.data > 250 && params.data <= 350) {
          return "rgb(255,0,0)";
        } else if (params.data > 350 && params.data <= 420) {
          return "rgb(153,0,76)";
        } else {
          return "rgb(126,0,35)";
        }
      },
    },
    n: {
      color: "white", //改变折线点的颜色
      borderColor: this.bgColor,
      borderWidth: 2,
    },
  };

  mounted() {
    if (this.propData) {
      if (this.chart) {
        this.updateChart();
        return;
      }

      this.initChart();
    }
  }
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.clear();
    this.chart.dispose();
    this.chart = null;
  }

  private initChart() {
    if (this.chart === null || this.chart === undefined) {
      const chartDom = document.getElementById(this.id) as HTMLDivElement;
      if (!chartDom) return;
      this.chart = echarts.init(chartDom);
    }
    // @ts-ignore
    this.chart._dom.style.display = "none";
    // @ts-ignore
    this.chart.setOption({
      backgroundColor: "transparent",
      grid: {
        height: "80%",
        bottom: 20,
        // right: 0,
        top: 35,
        left: 20,
        containLabel: true,
      },
      xAxis: {
        type: "category",
        name: this.xText,
        nameTextStyle: {
          color: "#fff",
          lineHeight: -30,
          align: "center",
          verticalAlign: "bottom",
        },
        data: this.propData.bottomList,
        axisLabel: {
          textStyle: {
            fontSize: 12,
            color: "white",
          },
        },
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
      },
      yAxis: {
        type: "value",
        name: this.propData.dataList.length > 0 ? this.propData.unit : "",
        nameTextStyle: {
          color: "#fff",
          // align: "center"
        },
        axisLabel: {
          textStyle: {
            fontSize: 12,
            color: "white",
          },
        },
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          lineStyle: {
            // type: "dashed"
            color: "RGBA(2, 39, 75, 1)",
          },
        },
      },
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "cross",
          label: {
            backgroundColor: "#6a7985",
          },
        },
      },
      series: [
        {
          // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
          // @ts-ignore
          name: "数值",
          data: this.propData.dataList,
          type: "line",
          smooth: this.smooth,
          // areaStyle: {
          //   color: this.bgColorState ? "#052968" : "transparent" //改变区域颜色
          // },
          lineStyle: {
            color: this.bgColor, //改变折线颜色
          },
          symbol: "circle",
          symbolSize: 6,
          itemStyle: this.propData.colorType
            ? this.itemStyleColor[this.propData.colorType as string]
            : {
                color: (params: any) => {
                  // 0 大于固定值报警, 1 范围内正常 2 范围外正常 3 小于固定值报警
                  const {
                    valueType,
                    miniValue,
                    maxValue,
                    warnValue,
                  } = this.propData;
                  if (valueType === 0) {
                    if (Number(params.data) > Number(warnValue)) {
                      return "#f00";
                    } else {
                      return "#0f0";
                    }
                  } else if (valueType === 1) {
                    if (
                      Number(params.data) < Number(miniValue) ||
                      Number(params.data) > Number(maxValue)
                    ) {
                      return "#f00";
                    } else {
                      return "#0f0";
                    }
                  } else if (valueType === 2) {
                    if (
                      Number(params.data) > Number(miniValue) &&
                      Number(params.data) < Number(maxValue)
                    ) {
                      return "#f00";
                    } else {
                      return "#0f0";
                    }
                  } else if (valueType === 3) {
                    if (Number(params.data) < Number(warnValue)) {
                      return "#f00";
                    } else {
                      return "#0f0";
                    }
                  } else {
                    return this.bgColor;
                  }
                }, //改变折线点的颜色
                borderColor: this.bgColor,
                // borderWidth: 2
              },
          areaStyle: {
            color: {
              type: "linear",
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: this.bgColorState
                    ? "RGBA(1, 208, 254, 1)"
                    : "transparent", // 0% 处的颜色
                },
                {
                  offset: 1,
                  color: "RGBA(1, 208, 254, 0)", // 100% 处的颜色
                },
              ],
              global: false,
            },
            shadowColor: "rgba(0,85,250,0)",
            shadowBlur: 20,
          },
          // itemStyle: {
          //   color: "white", //改变折线点的颜色
          //   borderColor: this.bgColor,
          //   borderWidth: 2
          // }
        },
      ],
    } as EChartOption<EChartOption>);

    this.chart.on("finished", () => {
      // @ts-ignore
      this.chart._dom.style.display = "block";
      // console.log('图表事件', this.chart);
    });
  }

  private updateChart() {
    if (!this.chart) return this.initChart();

    // @ts-ignore

    this.chart.clear();
    this.chart.setOption({
      backgroundColor: "transparent",
      grid: {
        height: "80%",
        bottom: 20,
        // right: 0,
        top: 35,
        left: 20,
        containLabel: true,
      },
      xAxis: {
        type: "category",
        name: this.xText,
        nameTextStyle: {
          color: "#fff",
          lineHeight: -30,
          align: "center",
          verticalAlign: "bottom",
        },
        data: this.propData.bottomList,
        axisLabel: {
          textStyle: {
            fontSize: 12,
            color: "white",
          },
        },
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
      },
      yAxis: {
        type: "value",
        name: this.propData.dataList.length > 0 ? this.propData.unit : "",
        nameTextStyle: {
          color: "#fff",
          // align: "center"
        },
        axisLabel: {
          textStyle: {
            fontSize: 12,
            color: "white",
          },
        },
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          lineStyle: {
            // type: "dashed"
            color: "RGBA(2, 39, 75, 1)",
          },
        },
      },
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "cross",
          label: {
            backgroundColor: "#6a7985",
          },
        },
      },
      series: [
        {
          // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
          // @ts-ignore
          name: "数值",
          data: this.propData.dataList,
          type: "line",
          smooth: this.smooth,
          // areaStyle: {
          //   color: this.bgColorState ? "#052968" : "transparent" //改变区域颜色
          // },
          lineStyle: {
            color: this.bgColor, //改变折线颜色
          },
          symbol: "circle",
          symbolSize: 6,
          itemStyle: this.propData.colorType
            ? this.itemStyleColor[this.propData.colorType as string]
            : {
                color: (params: any) => {
                  // 0 大于固定值报警, 1 范围内正常 2 范围外正常 3 小于固定值报警
                  const {
                    valueType,
                    miniValue,
                    maxValue,
                    warnValue,
                  } = this.propData;
                  if (valueType === 0) {
                    if (Number(params.data) > Number(warnValue)) {
                      return "#f00";
                    } else {
                      return "#0f0";
                    }
                  } else if (valueType === 1) {
                    if (
                      Number(params.data) < Number(miniValue) ||
                      Number(params.data) > Number(maxValue)
                    ) {
                      return "#f00";
                    } else {
                      return "#0f0";
                    }
                  } else if (valueType === 2) {
                    if (
                      Number(params.data) > Number(miniValue) &&
                      Number(params.data) < Number(maxValue)
                    ) {
                      return "#f00";
                    } else {
                      return "#0f0";
                    }
                  } else if (valueType === 3) {
                    if (Number(params.data) < Number(warnValue)) {
                      return "#f00";
                    } else {
                      return "#0f0";
                    }
                  } else {
                    return this.bgColor;
                  }
                }, //改变折线点的颜色
                borderColor: this.bgColor,
                // borderWidth: 2
              },
          areaStyle: {
            color: {
              type: "linear",
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: this.bgColorState
                    ? "RGBA(1, 208, 254, 1)"
                    : "transparent", // 0% 处的颜色
                },
                {
                  offset: 1,
                  color: "RGBA(1, 208, 254, 0)", // 100% 处的颜色
                },
              ],
              global: false,
            },
            shadowColor: "rgba(0,85,250,0)",
            shadowBlur: 20,
          },
          // itemStyle: {
          //   color: "white", //改变折线点的颜色
          //   borderColor: this.bgColor,
          //   borderWidth: 2
          // }
        },
      ],
    } as EChartOption<EChartOption>);
  }
}
</script>
