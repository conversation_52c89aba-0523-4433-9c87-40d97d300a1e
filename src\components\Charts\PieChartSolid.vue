<template>
  <div :id="id" :style="{ height: height, width: width }"></div>
</template>

<script lang="ts">
import * as echarts from 'echarts'
import { Component, Prop, Watch } from "vue-property-decorator";
import { mixins } from "vue-class-component";
import ResizeMixin from "./mixins/resize";
interface InData {
  name: string;
  dataList: any[];
  colorList: string[];
}
@Component({
  name: "PieChartSolid"
})
export default class extends mixins(ResizeMixin) {
  // @Prop({ default: "chart" }) private className!: string;
  @Prop({ default: "charts" }) private id!: string;
  @Prop({ default: "200px" }) private width!: string;
  @Prop({ default: "200px" }) private height!: string;
  @Prop({ required: true }) private PieChartData!: InData;
  // private chart: any;
  private timer: any;
  private chartCurrentIndex = 0;
  private colors: string[] = [];
  private option: any;
  mounted() {
    this.initChart();
  }
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.clear();
    this.chart.dispose();
    this.chart = null;
    clearInterval(this.timer);
  }
  @Watch("PieChartData", { immediate: false, deep: true })
  public onMsgChanged(newValue: InData, oldValue: InData) {
    this.initChart();
  }
  private initChart() {
    this.colors = this.PieChartData.colorList;
    this.option = {
      color: this.colors,
      title: [
        {
          text: this.PieChartData.name,
          textStyle: {
            color: "white",
            fontSize: 14
          },
          left: "39%",
          top: '44%'
        }
      ],
      tooltip: {
        show: true
        // formatter: "污染类型:{b}<br>污染天数:{c}"
      },
      series: [
        {
          // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
          // @ts-ignore
          name: this.PieChartData.name,
          type: "pie",
          radius: ["45%", "60%"],
          center: ["50%", "50%"],
          data: this.PieChartData.dataList,
          label: {
            show: true,
            formatter: "{a|{c}天}\n{b|{b}} ",
            rich: {
              a: {
                fontSize: 14
              },
              b: {
                fontSize: 14,
                lineHeight: 33,
                align: "center",
                color: "white"
              }
            }
          }
        }
      ]
    };

    if(this.chart === null || this.chart === undefined) {
      this.chart = echarts.init(
        document.getElementById(this.id) as HTMLDivElement
      );
    }
    this.chart.setOption(this.option as EChartOption<EChartOption>);
    clearInterval(this.timer);
    this.timer = setInterval(() => {
      this.autoPlayTool();
    }, 2000);

    //鼠标移动上去的时候的高亮动画
    this.chart.on("mouseover", (param: any) => {
      clearInterval(this.timer);
      // 取消之前高亮的图形
      (this.chart as any).dispatchAction({
        type: "downplay",
        seriesIndex: 0
        // dataIndex: this.chartCurrentIndex
      });
      this.chartCurrentIndex = param.dataIndex;
      // 高亮当前图形
      (this.chart as any).dispatchAction({
        type: "highlight",
        seriesIndex: 0,
        dataIndex: this.chartCurrentIndex
      });
    });
    // 鼠标移出之后，恢复自动高亮
    this.chart.on("mouseout", (param: any) => {
      clearInterval(this.timer);
      this.timer = setInterval(() => {
        this.autoPlayTool();
      }, 2000);
    });
  }
  /**
   * @param {chartIns} echarts实例
   * @param {chartDate} echarts数据
   * @param {curIndex} 当前要展示的数据index
   */
  private autoPlayTool(): void {
    const dataLen = this.option.series[0].data.length;
    // 取消之前高亮的图形
    (this.chart as any).dispatchAction({
      type: "downplay",
      seriesIndex: 0,
      dataIndex: this.chartCurrentIndex
    });
    this.chartCurrentIndex =
      this.chartCurrentIndex + 1 >= dataLen ? 0 : this.chartCurrentIndex + 1;
    // 高亮当前图形
    (this.chart as any).dispatchAction({
      type: "highlight",
      seriesIndex: 0,
      dataIndex: this.chartCurrentIndex
    });
    // }
    // private graceRequestAnimationFrame() {
    //   if (!Date.now) {
    //     Date.now = function() {
    //       return new Date().getTime();
    //     };
    //   }

    //   (function() {
    //     "use strict";

    //     const vendors = ["webkit", "moz"];
    //     for (
    //       let i = 0;
    //       i < vendors.length && !window.requestAnimationFrame;
    //       ++i
    //     ) {
    //       const vp = vendors[i];
    //       window.requestAnimationFrame = window[vp + "RequestAnimationFrame"];
    //       window.cancelAnimationFrame =
    //         window[vp + "CancelAnimationFrame"] ||
    //         window[vp + "CancelRequestAnimationFrame"];
    //     }
    //     if (
    //       /iP(ad|hone|od).*OS 6/.test(window.navigator.userAgent) || // iOS6 is buggy
    //       !window.requestAnimationFrame ||
    //       !window.cancelAnimationFrame
    //     ) {
    //       let lastTime = 0;
    //       (window as any).requestAnimationFrame = function(callback: Function) {
    //         const now = Date.now();
    //         const nextTime = Math.max(lastTime + 16, now);
    //         return setTimeout(function() {
    //           callback((lastTime = nextTime));
    //         }, nextTime - now);
    //       };
    //       window.cancelAnimationFrame = clearTimeout;
    //     }
    //   })();
    // }
  }
}
</script>
