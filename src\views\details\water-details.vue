<style lang="less" scoped>
@font-face {
  font-family: "DS-DIGII";
  src: url("../../assets/font/DS-DIGII.ttf");
  font-display: swap;
}

.details-main-warp {

  padding: 0.2rem 0.2rem 0.2rem;
  position: relative;
  .details-main {
    height: 100%;
    background-image: url("../../assets/kqbk.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    padding: 0.65rem 0.5rem 0.35rem;
    display: flex;
    justify-content: space-around;
    flex-direction: column;
    .details-region {
      padding: 0 0.7rem;
      margin-bottom: 0.15rem;
      .region-box {
        padding: 0.05rem 0.25rem;
        box-sizing: border-box;
        background: rgba(6, 30, 101, 1);
        border: 1px solid rgba(2, 174, 210, 1);
        display: flex;
        justify-content: space-between;
        align-items: center;
        .street {
          width: 3rem;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .site-type {
          width: 3rem;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .site-name {
          width: 3rem;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .select-time {
          width: 3.8rem;
          font-family: fontnameRegular;
          font-size: 0.36rem;
          margin-left: 0.35rem;
        }
        .line {
          width: 0.02rem;
          height: 0.7rem;
          background-image: url("../../assets/xgx.png");
          background-repeat: no-repeat;
          background-size: 100% 100%;
        }
        .time {
          width: 3rem;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 0 0.25rem;
          box-sizing: content-box;
        }
        .name,
        .type {
          width: 3rem;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .type {
          > div {
            width: 1.6rem;
            height: 0.44rem;
            background: #0084ff;
            border-radius: 0.44rem;
            line-height: 0.44rem;
            font-size: 0.24rem;
            display: flex;
            justify-content: center;
          }
        }
        .name {
          padding: 0 0.25rem;
          box-sizing: content-box;
        }
        .address {
          padding: 0 0.25rem;
          box-sizing: content-box;
          width: 6rem;
          font-size: 0.24rem;
          display: flex;
          align-items: center;
          flex: 1;
          // padding-left: 0.5rem;
          .address-text {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            flex: 1;
          }
          .icon {
            width: 0.3rem;
            height: 0.3rem;
            margin-right: 0.28rem;
            background-image: url("../../assets/<EMAIL>");
            background-size: 100% 100%;
          }
        }
      }
    }
    .details-top {
      // height: 2.8rem;
      margin-bottom: 0.2rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .left-arrow {
        cursor: pointer;
        width: 0.4rem;
        height: 0.4rem;
        background: rgba(7, 56, 141, 0.6);
        border: 1px solid rgba(54, 218, 234, 0.42);
      }
      .middle-wrap {
        // flex: 1;
        // height: 100%;
        min-height: 2.8rem;
        width: 17rem;
        // width: 100%;
        padding: 0 0.3rem;
        .middle-carousel1 {
          display: flex;
          // justify-content: space-between;
          align-items: center;
          height: calc(100% - 0.5rem);
          > div {
            width: 2rem;
            height: 2.65rem;
          }
        }
      }
      .right-arrow {
        cursor: pointer;
        width: 0.4rem;
        height: 0.4rem;
        background: rgba(7, 56, 141, 0.6);
        border: 1px solid rgba(54, 218, 234, 0.42);
      }
    }
    .details-bottom {
      height: 3.2rem;
      padding: 0 0.7rem;
      .details-bottom-chart {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .bottom-chart {
          height: 2.2rem;
        }
      }
      .bottom-btn {
        height: 0.8rem;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
    .middle-carousel1 {
      .middle-carousel-title {
        height: 0.3rem;
        text-align: center;
        font-size: 0.18rem;
        font-family: PingFang SC;
        font-weight: bold;
        color: rgba(255, 255, 255, 1);
      }
      .middle-carousel-content {
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        height: calc(100% - 0.3rem);
        overflow-y: auto;
        background: rgba(1, 41, 104, 1);
        padding: 0.1rem 0.2rem;
        scrollbar-width: thin;
        scrollbar-color: rgba(14, 139, 255, 0.5) rgba(12, 39, 92, 0.3);

        > div {
          font-size: 16px;
          font-family: Source Han Sans CN;
          font-weight: 400;
          color: rgba(255, 255, 255, 1);
        }
      }
    }
    .common-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 0.2rem;
      font-family: Source Han Sans CN;
      font-weight: 500;
      color: #fff;
      text-shadow: 0 0 0.1rem #2e73d6, 0 0 0.1rem #2e73d6;
      margin-bottom: 0.1rem;
      > div {
        display: flex;
        align-items: center;
        > span:nth-child(1) {
          width: 0.05rem;
          display: inline-block;
          height: 0.25rem;
          background: #fff;
          margin-right: 0.2rem;
        }
      }
    }
    .svg-arrow {
      width: 0.35rem;
      height: 0.35rem;
    }
  }
  label {
    font-size: 0.16rem;
  }
  .ant-radio-group-solid
    .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
    color: #fff;
    background: #0084ff;
    border-color: #0084ff;
  }
  .ant-radio-button-wrapper-checked {
    z-index: 1;
    color: #0084ff;
    background: #fff;
    border-color: #0084ff;
    -webkit-box-shadow: -1px 0 0 0 #0084ff;
    box-shadow: -1px 0 0 0 #0084ff;
  }
  .ant-radio-button-wrapper:hover {
    position: relative;
    color: #0084ff;
  }
  .ant-radio-button-wrapper {
    position: relative;
    display: inline-block;
    height: 32px;
    margin: 0;
    padding: 0 30px;
    color: rgb(255, 255, 255);
    line-height: 30px;
    background: rgba(10, 45, 107, 1);
    margin-right: 0.1rem;
    border: 1px solid rgba(255, 255, 255, 0);
    border-top-width: 1.02px;
    border-left: 0;
    cursor: pointer;
    -webkit-transition: color 0.3s, background 0.3s, border-color 0.3s;
    transition: color 0.3s, background 0.3s, border-color 0.3s;
  }
  .ant-radio-button-wrapper:not(:first-child)::before {
    position: absolute;
    top: 0;
    left: -1px;
    display: block;
    width: 0px;
    height: 100%;
    background-color: #d9d9d9;
    content: "";
  }
}
// 设备已掉线
.drop-line {
  transform: scale(0);
  transition: 1.5s;
  position: absolute;
  top: 50%;
  left: 50%;
  margin-left: -2.985rem;
  margin-top: -1.71rem;
  height: 3.42rem;
  width: 5.97rem;
  background-image: url(../../assets/tc-bg.png);
  background-size: 100% 100%;
  .drop-line-content {
    position: relative;
    height: 3.42rem;
    width: 5.97rem;
    .drop-line-btn {
      width: 2.55rem;
      height: 0.57rem;
      line-height: 0.57rem;
      text-align: center;
      bottom: 0.5rem;
      font-size: 0.18rem;
      left: 50%;
      margin-left: -1.275rem;
      position: absolute;
      background-image: url(../../assets/tc-btn.png);
      background-size: 100% 100%;
      cursor: pointer;
    }
    .drop-line-des {
      position: absolute;
      width: 2.56rem;
      height: 0.78rem;
      left: 2.8rem;
      top: 1.2rem;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      text-align: center;
      p {
        margin-bottom: 0;
      }
      > :nth-of-type(1) {
        font-size: 0.32rem;
        color: rgba(167, 206, 255, 1);
      }
      > :nth-of-type(2) {
        font-size: 0.17rem;
        color: rgba(167, 206, 255, 1);
      }
    }
    .drop-line-title {
      position: absolute;
      font-size: 0.16rem;
      left: 0.75rem;
      top: 0.42rem;
    }
  }
}
.drop-line-state {
  transform: scale(1);
  transition: 1.5s;
}
// 设备参数异常
.params-abnormal {
  transform: scale(0);
  transition: 1.5s;
  position: absolute;
  top: 50%;
  left: 50%;
  margin-left: -2.985rem;
  margin-top: -1.71rem;
  height: 3.42rem;
  width: 5.97rem;
  background-image: url(../../assets/tc-bg.png);
  background-size: 100% 100%;
  .drop-line-content {
    position: relative;
    height: 3.42rem;
    width: 5.97rem;
    .drop-line-btn {
      width: 2.55rem;
      height: 0.57rem;
      line-height: 0.57rem;
      text-align: center;
      bottom: 0.5rem;
      font-size: 0.18rem;
      left: 50%;
      margin-left: -1.275rem;
      position: absolute;
      background-image: url(../../assets/tc-btn.png);
      background-size: 100% 100%;
      cursor: pointer;
    }
    .drop-line-des {
      position: absolute;
      width: 2.56rem;
      height: 0.78rem;
      left: 2.8rem;
      top: 1.2rem;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      text-align: center;
      p {
        margin-bottom: 0;
      }
      > :nth-of-type(1) {
        font-size: 0.32rem;
        color: rgba(167, 206, 255, 1);
      }
      > :nth-of-type(2) {
        font-size: 0.17rem;
        color: rgba(167, 206, 255, 1);
      }
    }
    .drop-line-title {
      position: absolute;
      font-size: 0.16rem;
      left: 0.75rem;
      top: 0.42rem;
    }
  }
}
.params-abnormal-state {
  transform: scale(1);
  transition: 1.5s;
}
// 数据请求失败
.network-fail {
  transform: scale(0);
  transition: 1.5s;
  position: absolute;
  top: 50%;
  left: 50%;
  margin-left: -2.985rem;
  margin-top: -1.71rem;
  height: 3.42rem;
  width: 5.97rem;
  background-image: url(../../assets/tc-bg.png);
  background-size: 100% 100%;
  .drop-line-content {
    position: relative;
    height: 3.42rem;
    width: 5.97rem;
    .drop-line-btn {
      width: 2.55rem;
      height: 0.57rem;
      line-height: 0.57rem;
      text-align: center;
      bottom: 0.5rem;
      font-size: 0.18rem;
      left: 50%;
      margin-left: -1.275rem;
      position: absolute;
      background-image: url(../../assets/tc-btn.png);
      background-size: 100% 100%;
      cursor: pointer;
    }
    .drop-line-des {
      position: absolute;
      width: 2.56rem;
      height: 0.78rem;
      left: 2.8rem;
      top: 1.2rem;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      text-align: center;
      p {
        margin-bottom: 0;
      }
      > :nth-of-type(1) {
        font-size: 0.32rem;
        color: rgba(167, 206, 255, 1);
      }
      > :nth-of-type(2) {
        font-size: 0.17rem;
        color: rgba(167, 206, 255, 1);
      }
    }
    .drop-line-title {
      position: absolute;
      font-size: 0.16rem;
      left: 0.75rem;
      top: 0.42rem;
    }
  }
}
.network-fail-state {
  transform: scale(1);
  transition: 1.5s;
}
.title-flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
  > div {
    display: flex;
    font-size: 0.14rem;
    font-weight: 400;

    > div {
      width: 0.8rem;
      height: 0.28rem;
      line-height: 0.3rem;
      color: #0e8bff;
      // background: rgba(14, 139, 255, 0.32);
      // border: 1px solid rgba(14, 139, 255, 1);
      text-align: center;
      // clip-path: polygon(100% 0, 100% 100%, 12% 100%, 0% 70%, 0 0);
      background: url(../../assets/<EMAIL>);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      cursor: pointer;
    }
    div.type-active {
      background: url(../../assets/<EMAIL>);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      color: rgba(0, 234, 255, 1);
    }
    :nth-of-type(2) {
      margin-left: 0.1rem;
    }
  }
}
.site-type-name {
  font-size: 0.24rem;
  color: #ffffff;
}
</style>

<style lang="less">
.title-select2-water {
  align-items: flex-end;
  .ant-select-selection {
    width: 1.8rem;
    height: 0.3rem;
    font-size: 0.24rem;
    // background: rgba(14, 139, 255, 0.32);
    border: none;
    border-radius: unset;
    // clip-path: polygon(100% 0, 100% 100%, 12% 100%, 0% 70%, 0 0);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-color: transparent;
  }
  .ant-select-selection__placeholder,
  .ant-select-search__field__placeholder {
    height: 0.3rem;
    text-align: center;
    line-height: 0.3rem;
  }
  .ant-select-selection-selected-value {
    color: #00eaff;
  }
  .ant-select-focused .ant-select-selection,
  .ant-select-selection:focus,
  .ant-select-selection:active {
    border-color: transparent !important;
    border-right-width: 0 !important;
    outline: 0 !important;
    box-shadow: none !important;
    border: none !important;
  }
  .ant-select-selection__rendered {
    display: flex;
    // justify-content: center;
  }
}
.middle-carousel1 {
  .swiper-slide {
    height: 2.65rem !important;
  }
}
</style>

<template>
  <section class="details-main-warp">
    <div class="details-main">
      <!-- 头部区域选择 -->
      <div class="details-region">
        <div class="region-box">
          <div class="time">
            <div class="site-type-name">河流筛选：</div>
            <!-- 河流 -->
            <a-select
              v-model="riverIndex"
              class="title-select2-water"
              @change="riverChange"
            >
              <a-icon
                slot="suffixIcon"
                type="caret-down"
                style="color:#1FFFFC;font-size:0.2rem"
              />
              <a-select-option
                :value="String(index)"
                v-for="(item, index) in stationList"
                :key="index"
              >
                {{ item.riverName }}
              </a-select-option>
            </a-select>
          </div>
          <div class="line"></div>
          <div class="name">
            <div class="site-type-name">站点名称：</div>
            <!-- 站点 -->
            <a-select
              v-model="stationIndex"
              class="title-select2-water"
              @change="stationChange"
            >
              <a-icon
                slot="suffixIcon"
                type="caret-down"
                style="color:#1FFFFC;font-size:0.2rem"
              />
              <a-select-option
                :value="String(index)"
                v-for="(item, index) in stationList[riverIndex].stationList"
                :key="index"
              >
                {{ item.stationName ? item.stationName : "" }}
              </a-select-option>
            </a-select>
          </div>
          <!-- <div class="line"></div>
          <div class="type">
            <div>监测站</div>
          </div> -->
          <div class="line"></div>
          <div class="address">
            <!-- <div class="icon"></div> -->
            <div class="address-text">
              站点位置：{{ stationdDetails.address }}
            </div>
          </div>
        </div>
      </div>
      <!-- 上半边部分 -->
      <div class="details-top">
        <!-- 左侧箭头 -->
        <div class="left-arrow">
          <svgicon name="left" color="#00D9D5" class="svg-arrow"></svgicon>
        </div>
        <!-- 中间部分 -->
        <div class="middle-wrap">
          <!-- 通用标题 -->
          <div class="common-title">
            <div>
              <span></span>
              <span>24小时水质监测</span>
            </div>
          </div>
          <!-- 内容滚动部分 -->
          <swiper
            class="middle-carousel1"
            :options="swiperOption"
            v-if="trendDataList.length != 0 && trendDataList.length > 8"
          >
            <swiper-slide v-for="(item, i) in trendDataList" :key="i">
              <div
                  class="middle-carousel-title"
                  :style="{ backgroundColor: '#0072ff' }"
              >
                {{ `${item.monitorTimes}` }}
              </div>

              <!-- 水质污染 -->
              <div
                  class="middle-carousel-content"
                  style="padding:0.1rem 0.15rem;"
              >
                <div
                    v-for="(detail, index) in item.monitorItemDetails"
                    :key="index"
                    :style="{color: detail.isAlarm ? 'red' : ''}"
                >
                  <!-- {{ airTypeName[detail.pollutantName] }}: {{ detail.monValue }} -->
                  <span style="font-size:0.14rem;">{{ detail.itemName }}</span
                  >:
                  <span style="font-size:0.18rem;">{{
                      detail.monitorValue
                    }}</span
                  ><span style="font-size:0.12rem;"
                >({{ detail.itemUnit ? detail.itemUnit : " - " }})</span
                >
                </div>
              </div>


            </swiper-slide>
            <!-- <div class="swiper-pagination" slot="pagination"></div> -->
          </swiper>
          <!-- 内容滚动部分 -->
          <div
            class="middle-carousel1"
            v-if="trendDataList.length != 0 && trendDataList.length <= 8"
          >
            <div
              v-for="(item, i) in trendDataList"
              :key="i"
              style="margin-right: 0.15rem;"
            >
              <div
                class="middle-carousel-title"
                :style="{ backgroundColor: '#0072ff' }"
              >
                {{ `${item.monitorTimes}` }}
              </div>
              <div
                class="middle-carousel-content"
                style="padding:0.1rem 0.15rem;"
              >
                <div
                  v-for="(detail, index) in item.monitorItemDetails"
                  :key="index"
                >
                  <!-- {{ airTypeName[detail.pollutantName] }}: {{ detail.monValue }} -->
                  <span style="font-size:0.14rem;">{{ detail.itemName }}</span
                  >:
                  <span style="font-size:0.18rem;">{{
                    detail.monitorValue
                  }}</span
                  ><span style="font-size:0.12rem;"
                    >({{ detail.itemUnit ? detail.itemUnit : " - " }})</span
                  >
                </div>
              </div>
            </div>
          </div>
          <div
            v-if="trendDataList.length == 0"
            class="middle-carousel"
            style="display: flex;justify-content: center;align-items: center;font-size: 0.2rem;"
          >
            设备离线
          </div>
        </div>
        <!-- 右侧箭头 -->
        <div class="right-arrow">
          <svgicon name="right" color="#00D9D5" class="svg-arrow"></svgicon>
        </div>
      </div>
      <!-- 下半边部分 -->
      <div class="details-bottom">
        <div class="details-bottom-chart">
          <div>
            <!-- 通用标题 -->
            <div class="common-title">
              <div>
                <span></span>
                <span>24小时水质变化趋势</span>
              </div>
            </div>
            <div class="bottom-chart">
              <LineChartDashed
                :id="'waterMonitor'+new Date().getTime()"
                :width="'8.2rem'"
                :height="'2.2rem'"
                :propData="waterMonitorData"
                :smooth="false"
              />
            </div>
          </div>
          <div>
            <!-- 通用标题 -->
            <div class="common-title">
              <div style="width: 100%;justify-content: space-between;">
                <div style="display: flex;align-items: center;">
                  <span
                    style="width: 0.05rem;display: inline-block;height: 0.25rem;background: #fff;margin-right: 0.2rem;"
                  ></span>
                  <span>水质质量对比</span>
                </div>
                <div class="title-flex">
                  <div>
                    <div
                      :class="{ 'type-active': airQualityType === '同比' }"
                      @click="airQualityType = '同比'"
                    >
                      同比
                    </div>
                    <div
                      :class="{ 'type-active': airQualityType === '环比' }"
                      @click="airQualityType = '环比'"
                    >
                      环比
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="bottom-chart">
              <LineAndBarChart
                v-if="airQualityType == '同比'"
                :id="'detail-water-change-3'"
                :width="'8.2rem'"
                :height="'2.2rem'"
                :LineAndBarData="waterChangeChartData"
              />
              <LineAndBarChartOne
                v-if="airQualityType == '环比'"
                :id="'LineAndBarChartOne--5'"
                :width="'8.2rem'"
                :height="'2.2rem'"
                :propData="chainRatio"
              />
            </div>
          </div>
        </div>
        <div class="bottom-btn">
          <a-radio-group
            v-model="elementType"
            buttonStyle="solid"
            size="large"
            @change="elementTypeChange"
          >
            <a-radio-button
              v-for="btn in ButtonGroup"
              :key="btn.value"
              :value="btn.value"
              >{{ btn.name }}
            </a-radio-button>
          </a-radio-group>
        </div>
      </div>
    </div>
    <!-- 设备已掉线 -->
    <section class="drop-line" :class="{ 'drop-line-state': dropLineState }">
      <div class="drop-line-content">
        <div class="drop-line-title">设备异常</div>
        <div class="drop-line-des">
          <p>设备已掉线</p>
          <p>请联系管理人员检查设备运行状态</p>
        </div>
        <div class="drop-line-btn" @click="dropLineState = false">确认</div>
      </div>
    </section>
    <!-- 设备参数异常 -->
    <section
      class="params-abnormal"
      :class="{ 'params-abnormal-state': paramsSbnormalState }"
    >
      <div class="drop-line-content">
        <div class="drop-line-title">参数异常</div>
        <div class="drop-line-des">
          <p>设备参数异常</p>
          <p>请联系管理员检查设备工况</p>
        </div>
        <div class="drop-line-btn" @click="paramsSbnormalState = false">
          确认
        </div>
      </div>
    </section>
    <!-- 数据请求失败 -->
    <section
      class="network-fail"
      :class="{ 'network-fail-state': networkSailState }"
    >
      <div class="drop-line-content">
        <div class="drop-line-title">参数异常</div>
        <div class="drop-line-des">
          <p>数据请求失败</p>
          <p>请检查网络连接</p>
        </div>
        <div class="drop-line-btn" @click="networkSailState = false">确认</div>
      </div>
    </section>
  </section>
</template>

<script lang="ts">
/* eslint-disable @typescript-eslint/ban-ts-ignore */
import { Component, Vue, Watch } from "vue-property-decorator";
import MultiLineChart from "@/components/Charts/MultiLineChart.vue";
import LineChartDashed from "@/components/Charts/LineChartDashed.vue";
import LineAndBarChart from "@/components/Charts/LineAndBarChart.vue";
import LineAndBarChartOne from "@/components/Charts/LineAndBarChartOne.vue";
import { Radio, Select, Icon, message } from "ant-design-vue";
import { Swiper, SwiperSlide } from "vue-awesome-swiper";
import { getNowTime } from "@/utils/index";
import {
  getHourMonitorRecordList,
  getStationList,
  getMonitorItemDetailRecord,
  riverList
} from "@/api/water";
import { getAirTypes } from "@/api/homeTable";
interface DataList {
  name: string;
  value: number;
}

interface ButtonProperty {
  name: string | number;
  value: string | number;
}

interface AirDataProp {
  airTrendYesterday: string | number[];
  airToday1: string | number[];
  airToday2: string | number[];
  airAverage: string | number[];
  unit: string;
}

interface AirContrast {
  monthList: string | number[];
  thisYear: string | number[];
  lastYear: string | number[];
  thisYearName?: string;
  lastYearName?: string;
}

interface LineChartData {
  bottomList: string[];
  dataList: string[];
  unit?: string;
}

interface WaterChageData {
  bottomList: string[];
  currentYearName: string;
  currentYearDataList: string[];
  beforeOneYearName: string;
  beforeOneYearDataList: string[];
  unit?: string;
}

enum EnterType {
  AIR = 1,
  WATER = 2,
  VEHICLE = 3,
  HOME = 4
}

@Component({
  name: "WaterDetails",
  components: {
    MultiLineChart,
    LineChartDashed,
    LineAndBarChart,
    ARadioGroup: Radio.Group,
    ARadioButton: Radio.Button,
    AIcon: Icon,
    ASelect: Select,
    ASelectOption: Select.Option,
    Swiper,
    SwiperSlide,
    LineAndBarChartOne
  }
})
export default class extends Vue {
  @Watch("stationCode")
  public async onStationCode(newValue: string, oldValue: string) {
    // @ts-ignore
    await this.getWaterStationList();
    await this.getWaterHourMonitorRecordList();
  }
  private nowTime = "";
  private timer: any;
  private dropLineState = false;
  private paramsSbnormalState = false;
  private networkSailState = false;
  private airQualityType: any = "同比";
  readonly colors: Array<string> = [
    "#1D9DFF",
    "#FF9F7F",
    "#FB7293",
    "#E7BCF3",
    "#8378EA",
    "#32C5E9",
    "#9FE6B8",
    "#FFDB5C"
  ];
  private airTypeName = {
    NO2: "μg/m³",
    O3: "μg/m³",
    PM10: "μg/m³",
    "PM2.5": "μg/m³",
    NOx: "μg/m³",
    NO: "μg/m³"
  };
  private airTypeNamePub = {
    NO2: "NO₂",
    O3: "O₃",
    PM10: "PM₁₀",
    "PM2.5": "PM₂.₅",
    NOx: "NOx",
    NO: "NO"
  };
  private dataList!: Array<any>;

  private displayDataList: Array<DataList> = [
    {
      name: "二氧化硫",
      value: 0.13
    },
    {
      name: "二氧化氮",
      value: 0.4
    },
    {
      name: "PM2.5",
      value: 0.139
    },
    {
      name: "PM10",
      value: 0.246
    },
    {
      name: "一氧化硫",
      value: 0.145
    },
    {
      name: "臭氧",
      value: 0.317
    }
  ];

  private ButtonGroup: Array<ButtonProperty> = [
    // {
    //   name: "AQI",
    //   value: "aqi"
    // },
    {
      name: "NO₂",
      value: "101"
    },
    {
      name: "PM₂.₅",
      value: "105"
    },
    {
      name: "PM₁₀",
      value: "104"
    },
    {
      name: "O₃",
      value: "102"
    },
    {
      name: "SO₂",
      value: "100"
    },
    {
      name: "CO",
      value: "103"
    }
  ];

  private swiperOption: any = {
    slidesPerView: 8,
    spaceBetween: 15,
    slidesPerGroup: 1,
    loop: false,
    loopFillGroupWithBlank: true,
    pagination: {
      el: ".swiper-pagination",
      clickable: true
    },
    autoplay: {
      delay: 30000,
      disableOnInteraction: false
    },
    navigation: {
      nextEl: ".right-arrow",
      prevEl: ".left-arrow"
    }
  };

  readonly AirColors: Array<any> = [
    {
      color: "rgb(0,255,0)",
      min: 0,
      max: 50
    },
    {
      color: "rgb(255,255,0)",
      min: 51,
      max: 100
    },
    {
      color: "rgb(255,126,0)",
      min: 101,
      max: 150
    },
    {
      color: "rgb(255,0,0)",
      min: 151,
      max: 200
    },
    {
      color: "rgb(153,0,76)",
      min: 201,
      max: 300
    },
    {
      color: "rgb(126,0,35)",
      min: 300,
      max: 999
    }
  ];
  readonly NO2Colors: number = 700;
  readonly O3Colors: number = 300;
  readonly PM10Colors: number = 250;
  readonly PM25Colors: number = 115;
  private stationList = [];
  private stationCode = "";
  private elementType = "";
  private trendDataList = [];
  private airDataProp: AirDataProp = {
    airTrendYesterday: [],
    airToday1: [],
    airToday2: [],
    airAverage: [],
    unit: ""
  };
  private airContrast: AirContrast = {
    thisYear: [], // 今年
    lastYear: [], // 上年
    monthList: [],
    thisYearName: "",
    lastYearName: ""
  };
  // 下方按钮组自动轮播count
  private switchButtonGroupCount = 0;
  // 下方按钮组自动轮播timer
  private switchButtonGroupTimer: any;
  private selectTimer: any;
  private airTimer: any;
  // 水质变化Chart
  private waterChangeChartData: any = {
    bottomList: ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12"],
    currentYearName: "",
    currentYearDataList: [],
    beforeOneYearName: "",
    beforeOneYearDataList: [],
    unit: "",
    sameRatioName: "同比率",
    sameRatio: [],
    unit1: "比率(%)"
  };
  //水质监测详情数据
  private waterMonitorData: LineChartData = {
    bottomList: [],
    dataList: [],
    unit: ""
  };
  private stationdDetails: any = {};
  private siteList: any[] = [];
  private streetName = "0";
  private siteTypeName: any = "qk"; //  = "sk"
  private sitetName: any = "请选择站点"; // = "0"
  created() {
    // @ts-ignore
    // this.stationCode = this.$route.query.stationCode;
    this.stationCode = this.getQueryString("stationCode");
    // @ts-ignore
  }
  mounted() {
    this.nowTime;
    this.timer = setInterval(() => {
      this.nowTime = getNowTime();
    }, 1000);
    // @ts-ignore
    // 水质监测
    this.getWaterStationList();
    this.getWaterHourMonitorRecordList();
    // @ts-ignore
  }
  beforeDestroy() {
    // clearInterval(this.selectTimer);
    clearInterval(this.switchButtonGroupTimer);
  }
  private getQueryString(name: string) {
    const reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
    const r = location.href
      .slice(location.href.indexOf("?"))
      .substr(1)
      .match(reg);
    if (r != null) {
      return decodeURIComponent(r[2]);
    }
    return "";
  }
  // 河流
  private riverIndex: any = "0";
  private riverChange(val: any) {
    this.paramsSbnormalState = false;
    this.riverIndex = val;
    this.stationIndex =
      // @ts-ignore
      this.stationList[val].stationList.length !== 0 ? "0" : "";
    // @ts-ignore
    this.stationCode = this.stationList[val].stationList[
      this.stationIndex
    ].stationId;
    this.switchButtonGroupCount = 0;
    this.stationdDetails.address = this.stationList[
      this.riverIndex
      // @ts-ignore
    ].stationList[val].stationAddress;
  }
  private stationIndex: any = "0";
  // 站点下拉框
  private stationChange(val: any) {
    this.paramsSbnormalState = false;
    this.stationIndex = val;
    // @ts-ignore
    this.stationCode = this.stationList[this.riverIndex].stationList[
      val
    ].stationId;
    this.switchButtonGroupCount = 0;
    this.stationdDetails.address = this.stationList[
      this.riverIndex
      // @ts-ignore
    ].stationList[val].stationAddress;
  }

  // 切换按钮组标签
  private elementTypeChange(e: any) {
    this.elementType = e.target.value;
    this.ButtonGroup.forEach((item: any, index: number) => {
      if (item.value === e.target.value) {
        if (!e.target.mechanical) {
          this.switchButtonGroupCount = index;
          this.waterMonitorData.unit = item.unit;
          this.waterChangeChartData.unit = item.unit;
        }
      }
    });
    this.getMonitorItemDetailRecord(this.stationCode, this.elementType);
  }

  // 获取水质监测站点列表
  private getWaterStationList(): void {
    riverList().then(res => {
      this.stationList = res.data.data;
      res.data.data.forEach((river: any, index: number) => {
        river.stationList.forEach((station: any, index1: number) => {
          if (station.stationId === this.stationCode + "") {
            this.riverIndex = String(index);
            this.stationIndex = String(index1);
            this.stationdDetails.address = this.stationList[
              this.riverIndex
              // @ts-ignore
            ].stationList[this.stationIndex].stationAddress;
          }
        });
      });
      this.generateCurrentBtn();
    });
    // getStationList().then(res => {
    //   if (res.data.data && res.data.data.length > 0) {
    //     this.stationList = res.data.data;
    //     this.stationList.find((item: any) => {
    //       if (item.stationId == this.stationCode) {
    //         this.stationdDetails.address = item.stationAddress;
    //       }
    //     });
    //     this.generateCurrentBtn();
    //   }
    // });
  }
  // 获取水质监测24小时监测数据
  private getWaterHourMonitorRecordList(): void {
    this.paramsSbnormalState = false;
    getHourMonitorRecordList(this.stationCode).then(res => {
      if (res.data.data && res.data.data.length > 0) {
        this.trendDataList = (res.data.data || []).map((item:any)=> {
          item.monitorTimes = item.monitorTime.substr(5,11)
          console.log(item.monitorTimes, 'item.monitorTimes')
          return item
        });
        console.log(this.trendDataList, 'this.trendDataList')
        this.swiperOption.initialSlide = res.data.data.length - 7
      } else {
        this.trendDataList = [];
        // message.warning("24小时水质质量监测数据为空", 3);
        // this.paramsSbnormalState = true;
      }
    });
  }
  // 生成下方只针对此时站点的项目按钮
  private generateCurrentBtn(): void {
    for (const river of this.stationList as Array<any>) {
      for (const station of river.stationList) {
        if (station.stationId === this.stationCode + "") {
          this.ButtonGroup = station.monitorItems.map((item: any) => {
            return {
              name: item.name,
              value: item.itemCode,
              unit: item.concentrationUnit
            };
          });
          this.waterMonitorData.unit = (this.ButtonGroup[0] as any).unit + "";
          this.waterChangeChartData.unit =
            (this.ButtonGroup[0] as any).unit + "";
          this.getMonitorItemDetailRecord(
            this.stationCode,
            this.ButtonGroup[0].value + ""
          );
          this.switchButtonGroup(this.ButtonGroup, true);
        }
      }
      // if (station.stationId === this.stationCode + "") {
      //   this.ButtonGroup = station.monitorItems.map((item: any) => {
      //     return {
      //       name: item.name,
      //       value: item.itemCode,
      //       unit: item.concentrationUnit
      //     };
      //   });
      //   this.waterMonitorData.unit = (this.ButtonGroup[0] as any).unit + "";
      //   this.waterChangeChartData.unit = (this.ButtonGroup[0] as any).unit + "";
      //   this.getMonitorItemDetailRecord(
      //     this.stationCode,
      //     this.ButtonGroup[0].value + ""
      //   );
      //   this.switchButtonGroup(this.ButtonGroup, true);
      // }
    }
  }
  // 自动切换下方按钮组
  private switchButtonGroup(buttonList: Array<any>, immediate?: boolean): void {
    // eslint-disable-next-line
    const _this = this;
    const total = buttonList.length;
    // 如果有中间操作，重置timer
    if (this.switchButtonGroupTimer) {
      clearInterval(this.switchButtonGroupTimer);
    }
    if (immediate && buttonList.length && buttonList.length > 0) {
      // 立即执行一次
      this.elementType = buttonList[0].value + "";
      this.getMonitorItemDetailRecord(this.stationCode, this.elementType);
      this.switchButtonGroupCount++;
    }
    this.switchButtonGroupTimer = setInterval(() => {
      _this.elementType = buttonList[_this.switchButtonGroupCount].value + "";
      _this.switchButtonGroupCount < total - 1
        ? _this.switchButtonGroupCount++
        : (_this.switchButtonGroupCount = 0);
      _this.elementTypeChange({
        target: {
          value: _this.elementType,
          mechanical: true
        }
      });
    }, 30 * 1000);
  }
  private chainRatio: any = {};
  // 获取监测项详情记录
  private getMonitorItemDetailRecord(stationId: string, itemCode: string) {
    getMonitorItemDetailRecord(stationId, itemCode).then(res => {
      if (res.data.data) {
        const chainRatio: any = {
          bottomList: [],
          monthName: "",
          monthList: [],
          ringRatioName: "环比率",
          ringRatioList: [],
          unit: "",
          unit1: "环比率(%)"
        };
        const { hourMonitorItems, lastYear, thisYear } = res.data.data;
        // 24小时水质质量趋势
        this.waterMonitorData.bottomList = [];
        this.waterMonitorData.dataList = [];
        if (hourMonitorItems.length > 0) {
          // const sortedHourMonitorItems = hourMonitorItems.sort(
          //   (a: any, b: any) => {
          //     return a.hour - b.hour;
          //   }
          // );
          hourMonitorItems.forEach((record: any) => {
            this.waterMonitorData.bottomList.push(record.hour + "时");
            this.waterMonitorData.dataList.push(record.monitorValue || "-");
          });
        }1

        this.waterChangeChartData.sameRatio = [];
        this.waterChangeChartData.currentYearDataList = [];
        this.waterChangeChartData.beforeOneYearDataList = [];
        // 水质质量对比 lastYear
        if (lastYear.length > 0) {
          const sortedLastYear = lastYear.sort((a: any, b: any) => {
            return a.month - b.month;
          });
          const sortedLastYears = ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12"].map((item:any) => {
            const curr = sortedLastYear.findIndex((items:any) => items.month == item)
            return curr > - 1 ? sortedLastYear[curr] : {month: item,value: 0 }
          })
          this.waterChangeChartData.beforeOneYearName =
            sortedLastYear[0].year + "";
          chainRatio.unit = this.waterChangeChartData.unit;
          this.waterChangeChartData.bottomList = [];
          sortedLastYears.forEach((yearLucy: any) => {
            this.waterChangeChartData.bottomList.push(`${yearLucy.month}`);
            this.waterChangeChartData.beforeOneYearDataList.push(
              yearLucy.value || "-"
            );
          });
        }

        // 水质质量对比 thisYear
        if (thisYear.length > 0) {
          const sortedThisYear = thisYear.sort((a: any, b: any) => {
            return a.month - b.month;
          });
          const sortedThisYears = ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12"].map((item:any) => {
            const curr = sortedThisYear.findIndex((items:any) => items.month == item)
            return curr > - 1 ? sortedThisYear[curr] : {month: item,value: 0 }
          })
          this.waterChangeChartData.currentYearName =
            sortedThisYear[0].year + "";
          sortedThisYears.forEach((yearLucky: any) => {
            this.waterChangeChartData.currentYearDataList.push(
              yearLucky.value || "-"
            );
          });
        }
        for (const item of res.data.data.yoyList) {
          (this.waterChangeChartData.sameRatio as any).push(item.over);
        }
        console.log(res.data.data);
        // 环比
        if (res.data.data.momList.length !== res.data.data.beforeYear.length) {
          const leng =
            res.data.data.beforeYear.length - res.data.data.momList.length;
          for (let index = 0; index < leng; index++) {
            chainRatio.ringRatioList.push("-");
          }
        }
        for (const item of res.data.data.momList) {
          chainRatio.ringRatioList.push(item.over);
        }
        for (const item of res.data.data.beforeYear) {
          chainRatio.monthList.push(item.value);
          chainRatio.bottomList.push(item.year + "-" + item.month);
        }
        this.chainRatio = chainRatio;
      } else {
        this.waterMonitorData = {
          bottomList: [],
          dataList: [],
          unit: ""
        }
        this.waterChangeChartData = {
          bottomList: ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12"],
          currentYearName: "",
          currentYearDataList: [],
          beforeOneYearName: "",
          beforeOneYearDataList: [],
          unit: "",
          sameRatioName: "同比率",
          sameRatio: [],
          unit1: "比率(%)"
        };
        this.chainRatio = {}
      }
    });
  }
}
</script>
