<template>
  <div class="marker-control-box">
    <section class="air-station-main">
      <div v-for="item in iconList" :key="item.name" class="list-air" @click="checkChange(item)">
        <img
            v-if="!item.selected"
            src="@/assets/heavily/<EMAIL>"
            alt=""
            class="gou"
        />
        <img
            v-else
            src="@/assets/gou_active.png"
            alt=""
            class="gou"
        />
        <div style="display: inline-block">{{item.name}}</div>
      </div>
    </section>
  </div>
</template>

<script>
import {Checkbox} from 'ant-design-vue'
import ldicon from '@/assets/images/<EMAIL>' //雷达
import jkicon from '@/assets/images/<EMAIL>' //监控
import syicon from '@/assets/images/<EMAIL>' //事业
import gdicon from '@/assets/images/<EMAIL>' //工地
import dlicon from '@/assets/images/<EMAIL>' //电量
export default {
  components: {
    ACheckbox: Checkbox,
  },
  props: {
    typeNum: {
      type: Number,
      default: 0,
    },
    close: {
      type: Number,
      default: 1,
    },
  },
  watch: {
    close:{
      handler(newVal) {
        this.showIconList=false
      },
      deep: true,
      immediate: true,
    }
  },
  data() {
    return {
      iconList: [
        {
          name: "汽修源",
          icon: gdicon,
          index: 0,
          selected: true,
        },
        {
          name: "工地源",
          icon: ldicon,
          index: 1,
          selected: true,
        },
        {
          name: "工业源",
          icon: syicon,
          index: 2,
          selected: true,
        },
        {
          name: "加油站源",
          icon: dlicon,
          index: 3,
          selected: true,
        },
        {
          name: "重型停车场",
          icon: jkicon,
          index: 4,
          selected: true,
        },
      ],
      allSelectItem: [],
      activeIndex: 0,
    };
  },
  methods: {
    selectAllChange() {},
    checkChange(raw) {
      raw.selected = !raw.selected
      this.allSelectItem = this.iconList.filter(item => item.selected).map(it => it.index)
      this.$emit('handleGetSelectItem', this.allSelectItem)
    },
  },
};
</script>

<style lang="less" scoped>
.flex {
  display: flex;
}
.items-center {
  justify-items: center;
  align-items: center;
  vertical-align: middle;
}
.marker-control-box {
  position: fixed;
  right: 1.33rem;
  bottom: 1px;
  padding: 11px 32px;
  z-index: 2999;
}


.air-station-main {
  transition: 1.5s;
  width: 1.21rem;
  height: 1.3rem;
  // background: rgba(23, 56, 92, 0.5);
  position: absolute;
  bottom: 0.8rem;
  right: 4.5rem;
  z-index: 100;
  .gou {
    width: 0.16rem;
    margin-right: 0.1rem;
    height: 0.16rem;
  }
  .list-air {
    display: flex;
    width: fit-content;
    cursor: pointer;
  }
  > div {
    display: inline-block;
    align-items: center;
    margin-top: 0.08rem;
    > input {
      margin-right: 0.1rem;
      width: 0.16rem;
      height: 0.16rem;
    }
    > :nth-of-type(2) {
      width: 0.5rem;
      padding-left: 8px;
    }
  }
  padding: 0.05rem 0 0 0.25rem;
}

</style>
