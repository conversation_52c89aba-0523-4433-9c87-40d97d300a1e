import request from "@/utils/request";
import { AxiosPromise } from "axios";
// 获取所有空气站点
export function getAllAirStationList(params: any): AxiosPromise<any> {
  return request({
    url:
      // "/air/air_station/air_home/data_show?stationTypeId=1,2,3,4&pollutionCode=aqi",
      "/air/air_station/air_home/data_show?pollutionCode=aqi",
    method: "get",
    params
  });
}
// 获取所有空气站点
export function listStationItem(params: any): AxiosPromise<any> {
  return request({
    url: "/air/record/listStationItem",
    method: "get",
    params,
  });
}
// 获取站点24小时站点监测列表
export function stationItemMonitorTrendHour(params: any): AxiosPromise<any> {
  return request({
    url: "/air/record/stationItemMonitorTrendHour",
    method: "get",
    params,
  });
}
//  获取站点 参考站点监测值列表微控 周围三公里 区控 全部区控，市控，国控，全部市控国控
export function consultStationItemMonitorTrendHour(
  params: any
): AxiosPromise<any> {
  return request({
    url: "/air/record/consultStationItemMonitorTrendHour",
    method: "get",
    params,
  });
}
// 获取指定站点附近污染企业站点列表
export function listStationRangeEnterprise(params: any): AxiosPromise<any> {
  return request({
    url: "/air/record/listStationRangeEnterprise",
    method: "get",
    params,
  });
}
// 空气站点成因分析
export function originAnalyse(params: any): AxiosPromise<any> {
  return request({
    url: "/air/record/originAnalyse",
    method: "get",
    params,
  });
}
// 获取污染企业的监测值列表 并返回告警阈值
export function listSewageMonitorTrendHour(params: any): AxiosPromise<any> {
  return request({
    url: "/air/record/listSewageMonitorTrendHour",
    method: "get",
    params,
  });
}
// 获取区域code
export function districtList(): AxiosPromise<any> {
  return request({
    url: "/air/area/district",
    method: "get",
  });
}
