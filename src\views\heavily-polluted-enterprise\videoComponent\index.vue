<style lang="less" scoped>
.pollution-video {
  padding: 0.2rem 0.2rem 0.2rem;
  position: relative;
  .pollution-video-conetnt {
    box-sizing: border-box;
    padding: 0.5rem 1rem;
    height: 100%;
    background-image: url(../../../assets/water-video.png) !important;
    background-size: 100% 100% !important;
    .video-select {
      display: flex;
      position: relative;
      .back{
        width: 130px;
        height: 0.64rem;
        line-height: 0.64rem;
        font-size: 0.2rem;
        text-align: center;
        // border: solid 1px #0A48A5;
        background-image: url('../../../assets/video/<EMAIL>');
        background-size: 100% 100%;
        margin-right: 0.08rem;
        cursor: pointer;
        color: #32CDF9;
      }
      .fullCr{
        position: absolute;
        right: 0px;
        top: 15px;
        background-image: url('../../../assets/video/<EMAIL>');
        background-size: 100% 100%;
        width: 1.1rem;
        height: 0.46rem;
        text-align: center;
        line-height: 0.44rem;
        color: #32CDF9;
        padding-left: 0.2rem;
        font-size: 16px;
        cursor: pointer;
      }
    }
    .video-list {
      margin-top: 0.2rem;
      height: calc(100% - 1.1rem);
      display: flex;
      flex-wrap: wrap;
      overflow-y: scroll;
      align-content: flex-start;
      ::-webkit-scrollbar {
        width: 0 !important;
      }
      .video-list-content {
        box-sizing: border-box;
        position: relative;
        width: 25%;
        border: 1px solid #2A76D0;
        margin-top: 0;
        border-radius: 5px;
        overflow: hidden;
        // margin: 0 0.08rem;
        // margin-bottom: 0.3rem;
        > :nth-of-type(2) {
          // margin-top: 0.15rem;
          p {
            margin-bottom: 0;
            font-size: 0.18rem;
          }
        }
        .video_name_style{
          position: absolute;
          left: 0px;
          top: 0px;
          z-index: 9;
          width: 100%;
          height: 0.46rem;
          line-height: 0.46rem;
          font-size: 0.18rem;
          color: #cedbee;
          padding-left: 0.1rem;
          background: linear-gradient(180deg, rgba(4, 13, 31,0.8) 0%, rgba(4, 13, 31,0) 100%);;
        }
        .video_style {
          position: absolute;
          right: 0;
          top: 0px;
          z-index: 222;
          width: 1.2rem;
          height: 0.36rem;
          font-size: 0.18rem;
          color: #cedbee;
          cursor: pointer;
          background-color: rgba(0, 0, 0, 0.5);
          text-align: center;
          border-left: 1px solid #226da2;
          border-bottom: 1px solid #226da2;
          line-height: 0.36rem;
        }
        .btn_class{
          right: 0;
          top: 0px;
        }
        // .video_style:hover {
        //   color: #759ce4;
        // }
      }
    }
  }
  .bottom-image {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 0.3rem;
    margin-top: 0.1rem;
    img {
      cursor: pointer;
      width: 0.27rem;
      height: 100%;
    }
  }
  .false-camera {
    width: 100%;
    height: 100%;
    background-repeat: no-repeat;
    display: flex;
    justify-content: center;
    background-size: 100% 100%;
    align-items: center;
    .play {
      width: 0.8rem;
      height: 0.44rem;
      background: #2b333f;
      cursor: pointer;
      border-radius: 0.3em;
      border: 0.06666em solid #fff;
      display: flex;
      justify-content: center;
      align-content: center;
      > span {
        display: block;
        line-height: 0.45rem;
      }
      .el-icon-caret-right {
        position: relative;
        top: 3px;
        left: 2px;
        font-size: 0.36rem;
        color: white;
      }
      .el-icon-caret-right:hover {
        color: white;
      }
    }
    .play:hover {
      background-color: rgba(43, 51, 63, 0.7);
    }
  }
  ::-webkit-scrollbar {
    width: 0 !important;
  }
}
</style>

<style lang="less">
.video-select {
  .hc-title-video-select {
    display: flex;
    justify-content: space-between;
    // margin-right: 0.1rem;
    .ant-select-selection {
      width: 1.5rem;
      height: 0.6rem;
      padding-top: 0.2rem;
      // background: rgba(14, 139, 255, 0.32);
      border: none;
      border-radius: unset;
      font-size: 0.2rem;
      // clip-path: polygon(100% 0, 100% 100%, 12% 100%, 0% 70%, 0 0);
      // background: url(../../../assets/<EMAIL>);
      background: url(../../../assets/video/<EMAIL>) !important;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      display: flex;
      justify-content: center;
    }
    .ant-select-selection-selected-value {
      // color: rgba(0, 234, 255, 1);
      color: #A6BFDC;
    }
    .ant-select-focused .ant-select-selection,
    .ant-select-selection:focus,
    .ant-select-selection:active {
      border-color: #40a9ff;
      border-right-width: 0 !important;
      outline: 0;
      box-shadow: none;
    }
  }
  .right {
    position: relative;
    display: flex;
    align-items: center;
    width: 4.06rem;
    height: 0.64rem;
    background-image: url('../../../assets/video/<EMAIL>');
    background-size: 100% 100%;
    /*background: rgba(5, 7, 95, 0.7);*/
    /*border: 1px solid #034fa8;*/
    margin-left: 0.08rem;
    input {
      width: 100%;
      margin-left: 0.15rem;
      // margin-right: 0.25rem;
      background: transparent;
      border: none;
      outline: none;
      font-size: 0.2rem;
      padding-top: 0.05rem;
      color: #A6BFDC;
    }
    input::-webkit-input-placeholder {
      color: #A6BFDC;
    }
    input::-moz-placeholder {
      /* Mozilla Firefox 19+ */
      color: #A6BFDC;
    }
    input:-moz-placeholder {
      /* Mozilla Firefox 4 to 18 */
      color: #A6BFDC;
    }
    input:-ms-input-placeholder {
      /* Internet Explorer 10-11 */
      color: #A6BFDC;
    }
  }
  .icon {
    position: absolute;
    right: 5px;
    z-index: 5;
    img {
      cursor: pointer;
      height: 23px;
      width: 23px;
    }
  }
}

.video-list-content {
  .video-list-content {
    .vjs-custom-skin {
      height: 100% !important;
    }
  }
  .player-wrapper,.vjs-tech,.video-wrapper{
    height: 100% !important;
  }
  .video-js.vjs-fluid {
    height: 100% !important;
  }
  .vjs-poster {
    background-size: 100% 100%;
  }
}
</style>

<template>
  <section class="pollution-video">
    <section class="pollution-video-conetnt">
      <section class="video-select">
      <div class="back" @click="back">
        <img src="@/assets/video/<EMAIL>" alt="">
        返回
      </div>
        <!-- <a-select
          v-model="pollutionType"
          class="hc-title-video-select"
          @change="pollutionTypeChange"
        >
          <a-icon
            slot="suffixIcon"
            type="caret-down"
            style="color: rgba(0, 234, 255, 1); width: 14px; height: 8px"
          />
          <a-select-option
            :value="item.id"
            v-for="(item, index) in pollutionTypeList"
            :key="index"
            >{{ item.label }}</a-select-option
          >
        </a-select> -->
        <div class="right">
          <input
            type="text"
            placeholder="请输入监控名称"
            v-model="keywords"
            @keyup.enter="search"
          />
          <!-- <a-icon
            type="close-circle"
            style="font-size: 0.2rem; cursor: pointer; margin-right: 0.35rem"
            v-if="keywords != ''"
            @click="clearSearch"
          /> -->
          <div class="icon">
            <img src="@/assets/video/<EMAIL>" alt="" @click="search" />
          </div>
        </div>
        <div @click="fullscreenSwich" class="fullCr">
          全屏
        </div>
      </section>
      <section v-if="cameraList.length" class="video-list" id="pollution_video">
        <section
          v-for="(item, index) in cameraList"
          :key="index"
          class="video-list-content"
          :style="{height:'33.33%'}"
          @mouseenter="repalyIndex=index"
          @mouseleave="repalyIndex=null"
        >
          <!-- <div class="video_style" @click="CheckVideo(item,index)" v-show="repalyIndex == index">回放录像</div> -->
          <div class="video_style" @click="CheckVideo(item,index)" v-show="false">回放录像</div>
          <div class="video_name_style">{{cameraList[index].monitorName||cameraList[index].cameraName}}</div>
          <div
            v-if="!item.videoUrl"
            class="false-camera"
            :style="{ backgroundImage: 'url(' + item.snapUrl + ')' }"
            style="position: relative"
          >
            <span
              v-if="!item.online"
              style="
                position: absolute;
                bottom: 0.03rem;
                left: 0.05rem;
                font-size: 0.1rem;
                color: red;
              "
              >离线</span
            >
            <div class="play" @click="play(index)">
              <a-icon
                v-if="!item.loading"
                class="el-icon-caret-right"
                type="caret-right"
              />
              <span v-else style="color: #fff">加载中...</span>
            </div>
          </div>
          <div v-else style="position: relative; height: 100%; width: 100%">
            <LivePlayer :video-url="item.videoUrl" fluent autoplay live :stretch="true" />
            <!-- <player
              v-if="item.videoUrl.length > 0"
              :video-url="item.videoUrl"
              :has-audio="false"
              height="200px"
              style="width: 320px"
              :isFullResize="true"
              :autoplay="true"
              :index="index"
              live
              muted
            /> -->
          </div>
        </section>
      </section>
      <section v-else class="video-list">
        <div
          style="
            text-align: center;
            line-height: 6.6rem;
            width: 100%;
            font-size: 22px;
          "
        >
          暂无数据
        </div>
      </section>
      <section class="bottom-image">
        <img
          :src="index === pageNum - 1 ? src1 : src2"
          alt=""
          v-for="(item, index) in pages"
          :key="index"
          @click="changePage(index)"
        />
      </section>
    </section>
  </section>
</template>

<script lang="ts">
/* eslint-disable @typescript-eslint/ban-ts-ignore */
// import store from '@/store';
import { Component, Vue, Watch } from 'vue-property-decorator'
import { Table, Radio, Select, Icon, message } from 'ant-design-vue'
import 'video.js/dist/video-js.css'
import 'videojs-contrib-hls'
import 'videojs-flash'
import {
  fetchCameraUrl,
  riverList,
} from '@/api/water'
import {
  getPollutionVideo,
  pollutionCameraUrl,
  getRandaVideo,
  randaCameraUrl
} from '@/api/pollutionVideo';
/* eslint-disable @typescript-eslint/ban-ts-ignore */
//@ts-ignore
import LivePlayer from '@liveqing/liveplayer'
import player from '@/components/jessibucaPlayer/jessibuca.vue'
@Component({
  name: 'waterVideo',
  components: {
    ATable: Table,
    ARadioGroup: Radio.Group,
    ARadioButton: Radio.Button,
    ASelect: Select,
    AIcon: Icon,
    ASelectOption: Select.Option,
    LivePlayer,
    player,
  },
})
export default class extends Vue {
  private keywords = ''
  private defaultValue: any = '连心桥站'
  private fullScreen = false
  private repalyIndex:any = null
  mounted() {
    // this.riverList()
    this.getVideo()
    document.addEventListener('wheel',this.debounce(
      (e:any)=>{
        if(e.deltaY>0){
          // if(this.pageNum === this.pages)return
          if(this.pageNum<this.pages){
            this.pageNum += 1
            this.$store.dispatch('user/setPageUnm',this.pageNum)
            this.getVideo()
          }
        }else{
          // if(this.pageNum===1)return
          if(this.pageNum>1){
            this.pageNum -= 1
            this.$store.dispatch('user/setPageUnm',this.pageNum)
            this.getVideo()
          }
        }
      }
    ))
  }
  private debounce(func:any, delay = 500) {
    let timer:any = null;
    return function (args:any) {
      clearTimeout(timer);
      timer = setTimeout(function () {
        func(args);
      }, delay);
    }
  }
  private back(){
    this.$store.dispatch('user/setPageUnm',1)
    const isSolid: any = true
    this.$router.push({ name: 'otherPages', params: { isSolid } })
  }
  // 清空搜索框
  private clearSearch() {
    this.$store.dispatch('user/setPageUnm',1)
    this.keywords = ''
    this.search()
  }
  // 搜索
  private search(){
    this.$store.dispatch('user/setPageUnm',1)
    this.getVideo()
  }
  private firstIndex: any = -1
  private play(index: any) {
    this.btnClass=true
    // if (this.firstIndex != -1) {
    //   this.$set(this.cameraList[this.firstIndex], 'videoUrl', '')
    // }
    // this.firstIndex = index
    this.$set(this.cameraList[index], 'loading', true)
    let fn = this.pollutionType==1?pollutionCameraUrl:randaCameraUrl
    fn(this.cameraList[index].monitorId||this.cameraList[index].cameraSerial)
      .then((res) => {
        // this.$set(this.cameraList[index], 'videoUrl', res.data.data.flvHttps)
        this.$set(this.cameraList[index], 'videoUrl', (res.data.data.wsFlvHttps||res.data.data.hdFlvAddress))
        this.$set(this.cameraList[index], 'loading', false)
      })
      .catch((err) => {
        message.error('视频不在线或已离线')
        this.$set(this.cameraList[index], 'loading', false)
      })
  }
  private videoError(e: any) {
    console.log(`播放器错误：${JSON.stringify(e)}`)
  }
  private changePage(index: any) {
    this.pageNum = index + 1
    this.$store.dispatch('user/setPageUnm',index + 1)
    this.getVideo()
  }
  // 水质站点列表
  private waterStationList: Array<any> = []
  // 默认选中水质站点
  private btnClass = false
  private defaultStationValue = ''
  private pageNum: number = 1
  private pageSize: number = 12
  private src1 = require('../../../assets/react.png')
  private src2 = require('../../../assets/react2x.png')
  private pages = 1
  private cameraList: any[] = []
  // 获取所有摄像头列表
  private getVideo() {
    /* eslint-disable @typescript-eslint/ban-ts-ignore */
    //@ts-ignore
    this.pageNum = this.$store.state.user.pageUnm
    let fn = this.pollutionType==1?getPollutionVideo:getRandaVideo
    fn({
      pageNum: this.pageNum,
      pageSize: this.pageSize,
      keywords: this.keywords
    }).then((res: any) => {
      const data: any = res.data.data.records
      this.pages = res.data.data.pages
      this.cameraList = data || []
    })
  }
  // private pollutionTypeList:any = [{id:1,label:'固废监测'},{id:2,label:'雷达监测'}]
  private pollutionTypeList:any = [{id:1,label:'固废监测'}]
  private pollutionType: any = 1
  // 河流切换
  private pollutionTypeChange(value: string): void {
    this.pageNum = 1
    // if (typeof value === 'number') {
    //   this.waterStationList = JSON.parse(
    //     JSON.stringify(this.pollutionTypeList[value].stationList)
    //   )
    //   this.waterStationList.unshift({
    //     stationName: '全部',
    //     stationId: '全部',
    //   })
    //   this.defaultStationValue = this.waterStationList[0].stationId
    // } else {
    //   this.waterStationList = []
    //   this.defaultStationValue = ''
    // }
    this.$store.dispatch('user/setPageUnm',1)
    this.keywords = ''
    this.getVideo()
  }
  /*回放录像 */
  CheckVideo(item:any,index:any) {
    this.btnClass = false
    // console.log(item);
    this.$router.push(`/videoList?id=${item.monitorId}&pageNum=${this.pageNum}&type=pollution`)
  }
  /**
   * 切换全屏状态
   */
  fullscreenSwich() {
    const docElm:any = document.documentElement
    const ele:any = document.querySelector('#pollution_video')
    // ele.style.height = '10.8rem'
    // ele.style.width = '19.2rem'
    if (!this.fullScreen) {
      if (ele.requestFullscreen) {
        ele.requestFullscreen()
      } else if (ele.msRequestFullscreen) {
        ele.msRequestFullscreen()
      } else if (ele.mozRequestFullScreen) {
        ele.mozRequestFullScreen()
      } else if (ele.webkitRequestFullScreen) {
        ele.webkitRequestFullScreen()
      }
    } else {
      this.exitFullscreen()
    }
    this.fullScreen = !this.fullScreen
  }
  /**
   * 退出全屏
   */
  exitFullscreen() {
    const docu:any = document
    const ele:any = document.querySelector('#pollution_video')
    // ele.style.height = '7.64rem'
    // ele.style.width = '16.78rem'
    if (ele.exitFullscreen) {
      ele.exitFullscreen()
    } else if (ele.msExitFullscreen) {
      ele.msExitFullscreen()
    } else if (ele.mozCancelFullScreen) {
      ele.mozCancelFullScreen()
    } else if (ele.webkitCancelFullScreen) {
      ele.webkitCancelFullScreen()
    }
  }
}
</script>
