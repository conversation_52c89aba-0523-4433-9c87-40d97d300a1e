<style lang="less" scoped>
.no-data {
  font-size: 0.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
<template>
  <div>
    <div
      v-if="propData.bottomList.length"
      :id="id"
      :style="{ height: height, width: width }"
    />
    <div class="no-data" v-else>暂无数据</div>
  </div>
</template>

<script lang="ts">
import * as echarts from 'echarts'
import { Component, Prop, Watch } from "vue-property-decorator";
import { mixins } from "vue-class-component";
import ResizeMixin from "./mixins/resize";
interface AirData {
  bottomList: string[];
  dataList: string[];
  dataList1: string[];
  dataList2: string[];
  dataList3: string[];
}
@Component({
  name: "LineChartDashed",
})
export default class extends mixins(ResizeMixin) {
  // @Prop({ default: "chart" }) private className!: string;
  @Prop({ default: "chart" }) private id!: string;
  @Prop({ default: "200px" }) private width!: string;
  @Prop({ default: "200px" }) private height!: string;
  @Prop({ required: true }) private propData!: AirData;
  @Prop({ required: false, default: "rgba(14,156,255,1)" })
  private bgColor!: string;
  @Prop({ required: false, default: false }) private smooth!: boolean;
  @Prop({ required: false, default: true }) private bgColorState!: boolean;
  @Prop({ required: false, default: "" }) private xText!: string;

  @Watch("propData", { immediate: true, deep: true }) public onMsgChanged(
    newValue: AirData,
    oldValue: AirData
  ) {
    if (this.propData) {
      if (this.chart) {
        this.chart.clear();
        this.chart.dispose();
        this.chart = null;
      }
      this.$nextTick(() => {
        this.initChart();
      });
    }
  }
  mounted() {
    // if (this.propData) {
    //   this.initChart()
    // }
    if (this.propData) {
      if (this.chart) {
        this.chart.clear();
        this.chart.dispose();
        this.chart = null;
      }
      this.$nextTick(() => {
        this.initChart();
      });
    }
  }
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.clear();
    this.chart.dispose();
    this.chart = null;
  }

  private initChart() {
    if (this.chart === null || this.chart === undefined) {
      const chartDom = document.getElementById(this.id) as HTMLDivElement;
      if (!chartDom) return;
      this.chart = echarts.init(chartDom);
    }
    // SO2:1 NO2:1 O3:1 CO:1 PM10:1 PM:2.5
    this.chart.setOption({
      // title: {
      //   text: "七日累积任务趋势",
      //   textStyle: {
      //     align: "right",
      //     color: "#A3C0DF",
      //     fontSize: 14,
      //   },
      //   right: "10%",
      //   top: "20%",
      // },
      backgroundColor: "transparent",
      animation: true,
      grid: {
        top: "25%",
        bottom: "10%", //也可设置left和right设置距离来控制图表的大小
      },
      xAxis: {
        data: ["10.21", "10.20", "10.19", "10.18", "10.17", "10.16", "10.15"],
        axisLine: {
          show: true, //隐藏X轴轴线
          lineStyle: {
            color: "#2D455A",
          },
        },
        axisTick: {
          show: false, //隐藏X轴刻度
        },
        axisLabel: {
          show: true,
          margin: 14,
          fontSize: 14,
          textStyle: {
            color: "#A3C0DF", //X轴文字颜色
          },
        },
      },
      yAxis: [
        {
          type: "value",
          gridIndex: 0,
          min: 0,
          max: 100,
          interval: 25,
          // splitNumber: 4,
          splitLine:false,
          // splitLine: {
          //   show: true,
          //   lineStyle: {
          //     show:false,
          //     type: "dashed",
          //     color: "#2B4359",
          //     width: 2,
          //   },
          // },
          axisTick: {
            show: false,
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: "#2D455A",
            },
          },
          axisLabel: {
            show: true,
            margin: 14,
            fontSize: 14,
            textStyle: {
              color: "#A3C0DF", //X轴文字颜色
            },
          },
        },
      ],
      series: [
        { //内
            type: 'bar',
            barWidth:18,
            legendHoverLink: false,
            symbolRepeat: true,
            silent: true,
            itemStyle: {
                color: {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 1,
                    y2: 1,
                    colorStops: [{
                        offset: 0,
                        color: '#2DDCFF' // 0% 处的颜色
                    }, {
                        offset: 1,
                        color: '#061547' // 100% 处的颜色
                    }]
                }
            },
            data: [23,32,32,68,42,32],
            z: 1,
            animationEasing: 'elasticOut'
        },
        { // 背景
            type: 'pictorialBar',
            animationDuration: 0,
            symbolRepeat: true,
            symbolMargin: '20%',
            symbol: 'roundRect',
            symbolSize: [18, 2],
            itemStyle: {
                normal: {
                    color: '#376BAE',

                }
            },
            data: [100, 100, 100, 100, 100, 100, 100],
            z: 0,
            animationEasing: 'elasticOut'
        },
        { //分隔
            type: 'pictorialBar',
            itemStyle: {
                color: '#000'
            },
            symbolRepeat: 'fixed',
            symbolMargin: 4,
            symbol: 'roundRect',
            symbolClip: true,
            symbolSize: [18,2],
            symbolPosition: 'start',
            symbolOffset: [0, 0],
            data: [100, 100, 100, 100, 100, 100],
            z: 2,
            animationEasing: 'elasticOut'
        },
        {
           // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
          // @ts-ignore
          name: "销售水量",
          type: "line",
          smooth: true, //平滑曲线显示
          showAllSymbol: false, //显示所有图形。
          symbolSize: 0,
          lineStyle: {
            color: "#182733",
            width: 0,
          },
          areaStyle: {
            color: "rgba(5,140,255, 0.2)",
          },
          data: [20, 80, 100, 40, 34, 90, 60],
          z: 5,
        },
      ],
    } as EChartOption<EChartOption>);
  }
}
</script>



