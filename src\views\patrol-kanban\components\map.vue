<template>
  <div class="map-container">
    <div class="map-box" ref="mapContainer"></div>
  </div>
</template>

<script>
import AMapLoader from "@amap/amap-jsapi-loader";
import { webglcontextlostHandle } from "@/utils/index";
import jinniuStreet from "@/assets/map-geojson/jinniu_street";
import process from "@/assets/patrolKanban/process.png";
import finish from "@/assets/patrolKanban/finish.png";

let AMap;
export default {
  name: "",
  props: {
    patrolList: {
      type: Array,
      default: () => {
        return [];
      },
    },
    currentTaskId: {
      type: String,
      default: "",
    }
  },
  data() {
    return {
      maps: null,
      mapStyle: "amap://styles/0739a6632d849ae1b5e6f1c52f7d12c3",
      markerList: [],
      styleObj: {
        0: {
          className: 'process',
          bg_url: require("@/assets/patrolKanban/process.png")
        },
        1: {
          className: 'finish',
          bg_url: require("@/assets/patrolKanban/finish.png")
        },
        2: {
          className: 'complete',
          bg_url: require("@/assets/patrolKanban/complete.png")
        },
      }
    };
  },
  created() {
    AMapLoader["reset"]();
  },
  mounted() {
    this.loadMap();
  },
  methods: {
    // 加载地图
    loadMap() {
      AMapLoader.load({
        key: "777fec7ef3cc29281d60ae900fa33925", // 申请好的Web端开发者Key，首次调用 load 时必填
        version: "1.4.15", // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
        plugins: [
          "AMap.DistrictSearch",
          "AMap.Heatmap",
          "AMap.ControlBar",
          "AMap.Object3DLayer",
          "Map3D",
          "AMap.Geocoder",
          "AMap.CircleMarker",
          "AMap.MouseTool",
        ], // 需要使用的的插件列表，如比例尺'AMap.Scale'等
        AMapUI: {
          // 是否加载 AMapUI，缺省不加载
          version: "1.0", // AMapUI 版本
          // "plugins":['overlay/SimpleMarker'],       // 需要加载的 AMapUI ui插件
        },
        Loca: {
          // 是否加载 Loca， 缺省不加载
          version: "1.3.2", // Loca 版本
        },
      })
        .then((amaps) => {
          AMap = amaps;
          this.initMap();
          //   this.addMarkers();
        })
        .catch((e) => {
          console.log(e);
        });
    },
    // 初始化地图
    initMap() {
      const map = new AMap.Map(this.$refs.mapContainer, {
        center: [104.05, 30.73],
        position: [104.05, 30.73],
        zoom: 18,
        zooms: [12, 18],
        viewMode: "2D",
        zoomEnable: true,
        dragEnable: true,
        pitch: 40,
      });
      // 设置地图样式
      map.setMapStyle(this.mapStyle);
      //   AMap.event.addListener(map, "click", this.handleBackDepartment);
      this.maps = map;
      // 处理webgl上下文丢失事件
      webglcontextlostHandle.call(this);
      // 添加街道划分区域地图数据
      this.creatGeojson(map);
      this.addMarkers();
    },
    // 添加街道划分区域地图数据
    creatGeojson(map) {
      // eslint-disable-next-line @typescript-eslint/no-this-alias
      const that = this;
      // 添加金牛区地理信息数据 1
      const geojson = new AMap.GeoJSON({
        geoJSON: jinniuStreet,
        getPolygon: function(geojson, lnglats) {
          AMap.convertFrom(geojson.geometry.coordinates[0], "gps", function(
            status,
            result
          ) {
            // const area: any = AMap.GeometryUtil.ringArea(lnglats[0]);
            if (geojson.properties.name !== "金牛区") {
              const text = new AMap.Text({
                text: geojson.properties.name,
                anchor: "center", // 设置文本标记锚点
                draggable: false,
                cursor: "pointer",
                angle: 0,
                style: {
                  padding: ".75rem 1.25rem",
                  "margin-bottom": "1rem",
                  "border-radius": ".25rem",
                  "background-color": "transparent",
                  "border-width": 0,
                  "text-align": "center",
                  "font-size": "14px",
                  "pointer-events": "none",
                  color: "#36E9EF",
                },
                position: [
                  geojson.properties.center.lng,
                  geojson.properties.center.lat,
                ],
              });
              text.setMap(map);
            }

            if (result.info === "ok") {
              const polygon = new AMap.Polygon({
                path: result.locations,
                // strokeColor: "#0ea9f9",
                strokeColor: "#2fbeb5",
                strokeWeight: 2,
                strokeOpacity: 1,
                fillOpacity: 0.2, // 多边形填充透明度
                fillColor: "rgba(0,49,113, 0.15)",
                // that.enterType === EnterType.AIR
                //   ? that.mapColor
                //   : "rgba(0,49,113, 0.15)",
                zIndex: 10,
              });
              if (that.currMarker) {
                AMap.event.addListener(
                  polygon,
                  "click",
                  that.handleBackDepartment
                );
              }
              map.add(polygon);
            }
          });
        },
      });

      // 添加金牛区地理信息数据 2
      geojson.setMap(map);
    },
    // 添加marker
    addMarkers() {
      const processIcon = new AMap.Icon({
        size: new AMap.Size(74, 103), // 图标尺寸
        image: process,
        imageSize: new AMap.Size(74, 103),
      });
      const finishIcon = new AMap.Icon({
        size: new AMap.Size(74, 103), // 图标尺寸
        image: finish,
        imageSize: new AMap.Size(74, 103),
      });
      this.patrolList.forEach((item, index) => {
        const marker = new AMap.Marker({
          position: [ +item.lng, +item.lat],
          icon: item.completeStatus === 0 ? processIcon : finishIcon,
          offset: new AMap.Pixel(-37, -103),
          data: item
        });
        const markerContent = `<div class="marker_content_box">
            <div class="maker_label ${this.styleObj[item.completeStatus].className}">${item.address}</div>
            <img src="${this.styleObj[item.completeStatus].bg_url}" />
          </div>`;

        marker.setContent(markerContent); //更新点标记内容
        marker.setMap(this.maps);
        if(index === 0) {
          this.maps.setCenter(marker.getPosition());
        }
        marker.on("click", (e) => {
          this.$emit('clickMarker', e.target.w.data.taskId)
          this.maps.setCenter(marker.getPosition())
        })
        this.markerList.push(marker);
      });
    },
  },
  beforeDestroy() {
    if (this.maps) {
      this.maps.clearMap();
      this.maps.destroy();
      this.maps = null;
    }
    this.markerList = [];
    AMap = null;
  },
  computed: {},
  watch: {
    patrolList: {
      handler(val) {
        this.markerList.forEach((item) => {
          item.setMap(null);
        });
        this.markerList = [];
        const dater = setTimeout(() => {
          this.addMarkers();
          clearTimeout(dater);
        }, 1000);
      },
      deep: true,
    },
    currentTaskId: {
      handler(val) {
        if(val) {
          const currentMarker = this.markerList.find(item => item.w.data.taskId === val);
          if(currentMarker) {
            this.maps.setCenter(currentMarker.getPosition());
            this.markerList.forEach(item => {
              if(item.w.data.taskId === val) {
                item.setzIndex(11)
              } else {
                item.setzIndex(10)
              }
            })
          }
        }
      },
    }
  },
  components: {},
};
</script>

<style lang="less" scoped>
.map-container {
  width: 100%;
  height: 100%;
  .map-box {
    width: 100%;
    height: 100%;
  }
}
</style>
<style lang="less">
.marker_content_box {
  position: relative;
  .maker_label {
    display: none;
  }
  &:hover {
    .maker_label {
      display: flex;
    }
  }
}
.maker_label {
  width: 245px;
  height: 38px;

  background-size: contain;
  font-size: 15px;
  font-family: PingFang SC;
  font-weight: 500;
  color: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  left: 50%;
  top: -40px;
  transform: translateX(-50%);
  &.process {
    background: url("~@/assets/patrolKanban/process_maker_bg.png") no-repeat center;
  }
  &.finish {
    background: url("~@/assets/patrolKanban/finish_maker_bg.png") no-repeat center;
  }
  &.complete {
    background: url("~@/assets/patrolKanban/complete_maker_bg.png") no-repeat center;
  }
}
</style>
