import request from "@/utils/request";
import { AxiosPromise } from "axios";

/**
 * @method functionName
 * @param {type} data 说明
 * @description 获取噪音站点数量
 */
export function getNoiseSite(params:any): AxiosPromise<any> {
  return request({
    url: `/water/noise/stationStatusStatistics`,
    method: "get",
    params
  });
}
/**
 * @method functionName
 * @param {type} data 说明
 * @description 获取噪音站点数量
 */
export function getNoiseMapList(params:any): AxiosPromise<any> {
  return request({
    url: `/water/noise/bigDataListStation`,
    method: "get",
    params
  });
}
/**
 * @method functionName
 * @param {type} data 说明
 * @description 昼夜达标率
 */
export function getDataComplianceRate(params:any): AxiosPromise<any> {
  return request({
    url: `/water/noise-record/bigDataComplianceRate`,
    method: "get",
    params
  });
}
/**
 * @method functionName
 * @param {type} data 说明
 * @description 小时监测
 */
export function getHourAnalyze(params:any): AxiosPromise<any> {
  return request({
    url: `/water/noise-record/bigDataHourAnalyze`,
    method: "get",
    params
  });
}
/**
 * @method functionName
 * @param {type} data 说明
 * @description 天监测
 */
export function getDaysAnalyze(params:any): AxiosPromise<any> {
  return request({
    url: `/water/noise-record/bigDataDaysAnalyze`,
    method: "get",
    params
  });
}