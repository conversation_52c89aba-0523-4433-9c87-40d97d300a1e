<style lang="less" scoped>
    .no-data {
        font-size: 0.2rem;
        display: flex;
        align-items: center;
        justify-content: center;
    }
</style>
<template>
    <div
        v-if="propData.dataList.length > 0"
        :id="id"
        :style="{ height: height, width: width }"
    />
    <div v-else class="no-data" :style="{ height: height, width: width }">
        数据未更新
    </div>
</template>

<script lang="ts">
import * as echarts from 'echarts'
import { Component, Prop, Watch } from "vue-property-decorator";
import { mixins } from "vue-class-component";
import ResizeMixin from "./mixins/resize";
interface AirData {
    bottomList: string[];
    dataList: string[];
}
@Component({
    name: "LineChartDashed"
})
export default class extends mixins(ResizeMixin) {
    // @Prop({ default: "chart" }) private className!: string;
    @Prop({ default: "chart" }) private id!: string;
    @Prop({ default: "200px" }) private width!: string;
    @Prop({ default: "200px" }) private height!: string;
    @Prop({ required: true }) private propData!: AirData;
    @Prop({ required: false, default: "rgba(14,156,255,1)" })
    private bgColor!: string;
    @Prop({ required: false, default: false }) private smooth!: boolean;
    @Prop({ required: false, default: true }) private bgColorState!: boolean;
    @Prop({ required: false, default: "" }) private xText!: string;
    @Watch("propData", { immediate: true, deep: true }) public onMsgChanged(
        newValue: AirData,
        oldValue: AirData
    ) {
        if (this.propData) {
            if (this.chart) {
                this.chart.clear();
                this.chart.dispose();
                this.chart = null;
            }
            this.$nextTick(() => {
                this.initChart();
            });
        }
    }
    mounted() {
        if (this.propData) {
            this.$nextTick(() => {
                this.initChart();
            })
        }
    }
    beforeDestroy() {
        if (!this.chart) {
            return;
        }
        this.chart.clear();
        this.chart.dispose();
        this.chart = null;
    }

    private initChart() {

        if(this.chart === null || this.chart === undefined) {
            const chartDom = document.getElementById(this.id) as HTMLDivElement
            if(!chartDom) return;
            this.chart = echarts.init(chartDom);
        }
        // SO2:1 NO2:1 O3:1 CO:1 PM10:1 PM:2.5
        this.chart.setOption({
            backgroundColor: "transparent",
            grid: {
                height: "80%",
                bottom: 20,
                // right: 0,
                top: 20,
                left: "left",
                containLabel: true
            },
            xAxis: {
                type: "category",
                name: '',
                // nameTextStyle: {
                //     color: "#fff",
                //     lineHeight: -30,
                //     align: "center",
                //     verticalAlign: "bottom"
                // },
                data: this.propData.bottomList,
                axisLabel: {
                    textStyle: {
                        fontSize: 12,
                        color: "white"
                    }
                },
                offset: 12,
                axisLine: {
                    show: false
                },
                axisTick: {
                    show: false
                }
            },
            yAxis: {
                type: "value",
                name: '',
                nameTextStyle: {
                    color: "#fff"
                    // align: "center"
                },
                axisLabel: {
                    textStyle: {
                        fontSize: 12,
                        color: "white"
                    }
                },
                axisLine: {
                    show: false
                },
                axisTick: {
                    show: false
                },
                splitLine: {
                    lineStyle: {
                        // type: "dashed"
                        color: "RGBA(2, 39, 75, 1)"
                    }
                }
            },
            tooltip: {
                trigger: "axis",
                axisPointer: {
                    type: "cross",
                    label: {
                        backgroundColor: "#6a7985"
                    }
                }
            },
            series: [
                {
                    // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
                    // @ts-ignore
                    name: "数值",
                    data: this.propData.dataList,
                    type: 'bar',
                    barWidth: '12px',
                    borderRadius: '6px',
                    label: {
                        normal: {
                            show: true,
                            color: 'white',
                            position: ['-2', '-16'],
                        }
                    },
                    itemStyle: {
                        normal: {
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                                offset: 0,
                                color: 'rgba(0,198,255,1)' // 0% 处的颜色
                            }, {
                                offset: 1,
                                color: 'rgba(30,107,229,1)' // 100% 处的颜色
                            }], false),
                            barBorderRadius: [30, 30, 30, 30]
                        }
                    }
                }
            ]
        } as EChartOption<EChartOption>);
    }
}
</script>
