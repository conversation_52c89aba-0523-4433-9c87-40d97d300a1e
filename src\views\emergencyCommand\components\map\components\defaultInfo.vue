<template>
  <div class="default-info-box">
    <div class="header">
      <nameBox
        v-if="defaultInfoData.type"
        :type="defaultInfoData.type"
        :top="defaultInfoData.name"
        :bottom="defaultInfoData.address"
      ></nameBox>
    </div>
    <div class="body flex flex-wrap">
      <div class="item" v-for="(item, i) in defaultInfoData.infoList" :key="i">
        <infoItem
          :name="`${item.name}`"
          :value="item.value"
          :warnValue="item.warnValue"
        ></infoItem>
      </div>
    </div>
  </div>
</template>

<script>
import nameBox from './nameBox.vue'
import infoItem from './infoItem.vue'
export default {
  name: 'defaultInfo',
  components: {
    nameBox,
    infoItem,
  },
  props: {
    defaultInfoData: {
      type: Object,
      default: () => ({}),
    },
  },
}
</script>

<style lang="less" scoped>
.default-info-box {
  width: 100%;
  .header {
    width: 100%;
    margin-bottom: 20px;
  }
  .flex{
    display: flex;
  }
  .flex-wrap{
    flex-wrap: wrap;
  }
  .body {
    width: 100%;
    .item {
      flex: 1;
      min-width: 50%;
      margin-bottom: 8px;
    }
  }
}
</style>
