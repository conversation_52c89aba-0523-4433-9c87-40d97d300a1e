<template>
  <div style="height: 100%">
    <a-select v-model="currentMonitorId" class="title-select-water" @change="changeMonitor">
      <a-icon slot="suffixIcon" type="caret-down" style="color: rgba(0, 234, 255, 1); width: 14px; height: 8px" />
      <a-select-option :value="item.monitorId" v-for="item in cameraList" :key="item.monitorId"> {{ item.monitorName }}</a-select-option>
    </a-select>
    <div v-loading="listLoading || monitorLoading" style="height: calc(100% - 0.6rem); position: relative; margin-top: 0.2rem" class="monitor-container">
      <LivePlayer v-if="url" :video-url="url" autoplay live aspect="fullscreen" />
      <div v-else class="empty">暂无数据</div>
    </div>
  </div>
</template>

<script>
import LivePlayer from '@liveqing/liveplayer'
import { drainCameraList, fetchCameraUrl } from '@/api/water'

export default {
  name: 'MonitorPane',
  props: {
    data: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      cameraList: [],
      currentMonitorId: null,
      url: null,
      listLoading: false,
      monitorLoading: false,
    }
  },
  created() {
    this.getCameraList()
  },
  computed: {},
  watch: {
    // currentStation:{
    //
    // },
  },
  components: {
    LivePlayer,
  },

  methods: {
    getCameraList() {
      this.listLoading = true
      drainCameraList(this.data.waterDrainId)
        .then((res) => {
          this.cameraList = res.data.data
          if (res.data.data && res.data.data.length) {
            this.currentMonitorId = res.data.data[0].monitorId
            this.getMonitorUrl()
          }
        })
        .finally(() => {
          this.listLoading = false
        })
    },

    changeMonitor(val) {
      this.url = null
      this.getMonitorUrl()
    },

    getMonitorUrl() {
      this.monitorLoading = true
      fetchCameraUrl(this.currentMonitorId)
        .then((res) => {
          this.url = res.data.data.wsFlvHttps
        })
        .finally(() => {
          this.monitorLoading = false
        })
    },
  },
}
</script>

<style lang="less" scoped>
::v-deep .title-select-water {
  display: flex;
  justify-content: space-between;
  .ant-select-selection {
    display: flex;
    justify-content: center;
    width: 3rem;
    height: 0.4rem;
    border: none;
    border-radius: unset;
    background: url(~@/assets/<EMAIL>) center / 100% 100% no-repeat;
    overflow: hidden; //超出的文本隐藏
    text-overflow: ellipsis; //溢出用省略号显示
    white-space: nowrap; //溢出不换行
  }

  .ant-select-selection-selected-value {
    color: rgba(0, 234, 255, 1);
    font-size: 0.17rem;
  }

  .ant-select-selection__rendered {
    line-height: 0.4rem;
    overflow: hidden;
  }

  .ant-select-focused .ant-select-selection,
  .ant-select-selection:focus,
  .ant-select-selection:active {
    border-color: #40a9ff;
    border-right-width: 0 !important;
    outline: 0;
    box-shadow: none;
  }
}

.monitor-container {
  display: grid;
  place-items: center;
}

.empty {
  letter-spacing: 1.5px;
  user-select: none;
}
</style>
