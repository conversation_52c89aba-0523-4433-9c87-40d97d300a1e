<template>
  <div class="name-box-container flex items-center">
    <div class="left">
      <div class="icon-box base-bg-img" :class="[type]"></div>
    </div>
    <div class="right">
      <div class="top">{{ top || '—' }}</div>
      <div class="bottom">{{ bottom || '—' }}</div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'nameBox',
  props: {
    type: {
      type: String,
      default: 'qiye', // qiye gongdi yiyuan yinpin cangku
    },
    top: {
      type: String,
      default: '',
    },
    bottom: {
      type: String,
      default: '',
    },
  },
}
</script>

<style lang="less" scoped>
.name-box-container {
  width: 100%;
  white-space: normal;

  .left {
    margin-right: 10px;
  }

  .icon-box {
    width: 60px;
    height: 71px;
    border-radius: 1px;

    &.qiye {
      background-image: url('../../../../../assets/images/<EMAIL>');
      background-repeat: no-repeat;
    }
    &.gongdi {
      background-image: url('../../../../../assets/images/<EMAIL>');
      background-repeat: no-repeat;
    }
    &.yiyuan {
      background-image: url('../../../../../assets/images/<EMAIL>');
      background-repeat: no-repeat;
    }
    &.yinpin {
      background-image: url('../../../../../assets/images/<EMAIL>');
      background-repeat: no-repeat;
    }
    &.cangku {
      background-image: url('../../../../../assets/images/<EMAIL>');
      background-repeat: no-repeat;
    }
    &.shengguang {
      background-image: url('../../../../../assets/images/<EMAIL>');
      background-repeat: no-repeat;
    }
  }

  .right {
    .top {
      font-size: 16px;
      color: #dcf0ff;
      margin-bottom: 10px;
    }

    .bottom {
      font-size: 14px;
      color: #4c7da9;
    }
  }
}
</style>
