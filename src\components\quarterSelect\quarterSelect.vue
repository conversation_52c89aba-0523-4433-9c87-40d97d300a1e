<script>
import { hasClass } from "element-ui/lib/utils/dom";
import ClickOutside from "element-ui/lib/utils/clickoutside";
import dayjs from "dayjs";

export default {
  name: "QuarterPicker",
  directives: {
    ClickOutside,
  },
  props: {
    format: {
      type: String,
      default: "",
    },
    modelValue: {
      type: String,
      default: "",
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    valueFormat: {
      type: String,
      default: "",
    },
    width: {
      type: String,
      default: "324px",
    },
    clearable: {
      type: Boolean,
      default: true,
    },
    placeholder: {
      type: String,
      default: "请选择季度",
    },
    disabledDate: {
      type: Function,
      default: () => () => false,
    },
  },
  data() {
    return {
      pickerVisible: false,
      date: new Date(),
      quarter: null,
      quarterText: ["一", "二", "三", "四"],
    };
  },
  computed: {
    year() {
      return this.date.getFullYear();
    },
    parsedValue() {
      if (!this.modelValue) {
        return new Date();
      }
      return new Date(this.modelValue);
    },
    displayValue() {
      if (!this.modelValue) return null;
      const quarter = Math.trunc(this.parsedValue.getMonth() / 3);
      let fDate = this.formatDate(this.parsedValue, this.format);
      fDate = fDate
        .replace(/Q/, `${quarter + 1}`)
        .replace(/q/, this.quarterText[quarter]);
      return fDate;
    },
  },
  watch: {
    modelValue: {
      handler(val) {
        this.date = val ? this.parsedValue : new Date();
      },
      immediate: true,
    },
  },
  methods: {
    formatDate(date, format) {
      if (!date) return "";
      return dayjs(date).format(format);
    },
    clearModelValue() {
      this.$emit("input", "");
    },
    handleTableClick(event) {
      let target = event.target;
      if (target.tagName === "A") {
        target = target.parentNode;
      }

      if (target.tagName !== "TD" || hasClass(target, "disabled")) return;

      const column = target.cellIndex;
      const row = target.parentNode.rowIndex;
      const currentQuarter = row * 2 + column;
      const month = currentQuarter * 3;
      let newDate = new Date(this.year, month, 1);
      if (this.valueFormat) {
        newDate = this.formatDate(newDate, this.valueFormat);
      }

      this.quarter = currentQuarter + 1;
      this.$emit("input", newDate.toString());
      this.pickerVisible = false;
    },
    getPrevYear() {
      this.date.setFullYear(this.date.getFullYear() - 1);
    },
    getNextYear() {
      this.date.setFullYear(this.date.getFullYear() + 1);
    },
    getCellStyle(quarter) {
      const today = new Date();
      const date = this.parsedValue;
      return {
        disabled:
          typeof this.disabledDate === "function"
            ? this.datesInYearAndQuarter(this.year, quarter).every(
                this.disabledDate
              )
            : false,
        current:
          date.getFullYear() === this.year &&
          Math.trunc(date.getMonth() / 3) === quarter,
        quarter:
          today.getFullYear() === this.year &&
          Math.trunc(today.getMonth() / 3) === quarter,
      };
    },
    datesInYearAndQuarter(year, quarter) {
      const numOfDays = this.getDayCountOfQuarter(year, quarter);
      const firstDay = new Date(year, quarter * 3, 1);
      return Array.from({ length: numOfDays }).map((_, n) =>
        this.nextDate(firstDay, n)
      );
    },
    getDayCountOfQuarter(year, quarter) {
      switch (quarter) {
        case 0:
          if ((year % 4 === 0 && year % 100 !== 0) || year % 400 === 0) {
            return 91;
          } else {
            return 90;
          }
        case 1:
          return 91;
        default:
          return 92;
      }
    },
    nextDate(date, amount = 1) {
      return new Date(
        date.getFullYear(),
        date.getMonth(),
        date.getDate() + amount
      );
    },
  },
};
</script>

<template>
  <el-popover
    ref="quarterPopover"
    v-model="pickerVisible"
    popper-class="quarter-popover el-date-picker"
    width="width"
  >
    <template slot="reference">
      <el-input
        :value="displayValue"
        class="el-date-editor"
        icon="el-icon-date"
        :clearable="clearable"
        :placeholder="placeholder"
        @focus="pickerVisible = true"
        @clear="clearModelValue"
      ></el-input>
    </template>
    <div v-click-outside="() => (pickerVisible = false)" class="el-date-picker">
      <div class="el-date-picker__header">
        <button @click="getPrevYear"><i class="el-icon-arrow-left"></i></button>
        <span>{{ year }}年</span>
        <button @click="getNextYear">
          <i class="el-icon-arrow-right"></i>
        </button>
      </div>
      <div class="el-picker-panel__content">
        <table class="quarter-table" @click="handleTableClick">
          <tbody>
            <tr>
              <td :class="getCellStyle(0)"><a>第一季度</a></td>
              <td :class="getCellStyle(1)"><a>第二季度</a></td>
            </tr>
            <tr>
              <td :class="getCellStyle(2)"><a>第三季度</a></td>
              <td :class="getCellStyle(3)"><a>第四季度</a></td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </el-popover>
</template>

<style>
.quarter-popover {
  padding: 0 !important;
}
.quarter-table {
  border-collapse: collapse;
  width: 100%;
  margin: -1px;
  font-size: 12px;
}
.quarter-table td {
  padding: 20px 3px;
  text-align: center;
  cursor: pointer;
}
.quarter-table td .cell {
  display: block;
  height: 32px;
  margin: 0 auto;
  color: #606266;
  line-height: 32px;
}
.quarter-table td .cell:hover {
  color: #1890ff;
}

.quarter-table td.current:not(.disabled) .cell {
  color: #409eff;
}

.quarter-table td.quarter .cell {
  color: #409eff;
  font-weight: 700;
}

.quarter-table td.disabled .cell {
  color: #c0c4cc;
  background-color: #f5f7fa;
  cursor: not-allowed;
}
</style>
