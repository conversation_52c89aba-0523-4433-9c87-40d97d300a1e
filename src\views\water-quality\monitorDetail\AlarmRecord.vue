<template>
  <a-spin :spinning="loading" class="alarm-record">
    <a-table :dataSource="tableList" :rowKey="(record) => record.waterDrainAlarmId" :pagination="false" :scroll="{ y: '385px'}">
      <a-table-column key="alarmTime" dataIndex="alarmTime" title="告警时间" align="center" />
      <a-table-column key="alarmTypeName" dataIndex="alarmTypeName" title="告警类型" align="center" width="100px" />
      <a-table-column key="analysisContent" dataIndex="analysisContent" title="告警信息" align="center" :ellipsis="true" />
      <a-table-column key="action" title="操作" align="center">
        <template slot-scope="data">
          <a-button type="primary" ghost size="small" @click="showDetails(data)">详情</a-button>
        </template>
      </a-table-column>
    </a-table>
    <a-pagination
        :total="total"
        v-model:pageSize="queryParams.pageSize"
        v-model:current="queryParams.pageNum"
        @change="handleQuery"
        size="small"
        style="margin-top: 15px; text-align: right"
    />

    <a-modal v-if="dialogVisible" v-model="dialogVisible" class="water-detail-dialog" :footer="null" centered destroy-on-close>
      <header class="header">
        <span class="title">详情</span>
        <a-icon type="close" style="color: #1598cf; font-size: 18px; cursor: pointer; margin-top: 10px" @click="dialogVisible = false" />
      </header>
      <section class="detail-content">
        <header class="content-header">
          <div style="margin-bottom: 10px">
            <span class="label">告警时间：</span>
            <span class="value">{{ formData.alarmTime }}</span>
          </div>
          <div style="margin-bottom: 10px">
            <span class="label">告警类型：</span>
            <span class="value">{{ formData.alarmTypeName }}</span>
          </div>
          <div>
            <span class="label">告警地址：</span>
            <span class="value">{{ formData.address || '--' }}</span>
          </div>
        </header>

        <div class="tab-content">
          <header class="main-header use-bg">告警描述及分析图像</header>
          <div class="tab-pane">
            <div>
              <span style="color: #95aabe">置信度：</span>
              <span style="font-size: 16px; color: #3ee1ff; font-weight: bold">{{ formData.confidence || '--' }}</span>
            </div>

            <section style="margin-top: 10px">{{ formData.analysisContent }}</section>

            <footer style="margin-top: 18px; display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px; grid-template-rows: repeat(2, 188px)">
              <el-image v-for="item in formData.alarmImage" :Key="item.url" :src="item.url" :preview-src-list="formData.alarmImage.map(el => el.url)" fit="cover" />
            </footer>
          </div>
        </div>
      </section>
    </a-modal>
  </a-spin>
</template>

<script>
import { pageAlarm } from '@/api/water'

export default {
  name: 'AlarmRecord',
  props: {
    data: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      loading: false,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      tableList: [],
      total: 0,
      formData: null,
      dialogVisible: false,
    }
  },
  created() {
    this.handleQuery()
  },
  computed: {},
  watch: {
    // currentStation:{
    //
    // },
  },
  components: {},

  methods: {
    handleQuery() {
      this.loading = true
      this.queryParams.cameraId = this.data.monitorId
      pageAlarm(this.queryParams)
          .then((res) => {
            const { data } = res.data
            this.tableList = data.records
            this.total = data.total
          })
          .finally(() => {
            this.loading = false
          })
    },

    showDetails(row) {
      this.formData = { ...row }
      this.dialogVisible = true
    },
  },
}
</script>

<style lang="less" scoped>
::v-deep .ant-pagination {
  a {
    color: #1890ff;
  }
  .ant-pagination-jump-prev .ant-pagination-item-container .ant-pagination-item-ellipsis,
  .ant-pagination-jump-next .ant-pagination-item-container .ant-pagination-item-ellipsis {
    color: #1890ff;
  }
}

::v-deep .ant-table {
  width: 783px;
  height: 445px;
  color: #d2eaf6;
  border: none;
  background: url(~@/assets/waterPng/<EMAIL>) center / 100% 100% no-repeat;

  .ant-table-placeholder {
    background-color: transparent;
    border: none;
  }

  .ant-table-header,
  .ant-table-body {
    background: transparent!important;
  }

  table {
    border: none;
  }

  th {
    background: transparent;
    color: #3076ac;
  }

  th,
  td {
    border: none;
    border-right: none !important;
  }

  .ant-table-row:nth-child(odd) {
    background: linear-gradient(90deg, rgba(0, 59, 109, 0) 0%, rgba(0, 59, 109, 0.5) 50%, rgba(0, 59, 109, 0) 100%);
  }

  .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
    background: transparent;
  }

  td {
    &:empty {
      &::before {
        content: '--';
      }
    }
  }
}

::v-deep .ant-pagination {
  .ant-pagination-item {
    background: transparent;
  }
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-left: 30px;
  padding-top: 4px;
  padding-right: 8px;
  .title {
    font-size: 16px;
    color: #dcf0ff;
  }
}

.detail-content {
  margin-top: 35px;
  padding-left: 27px;
  padding-right: 20px;
  .label {
    color: #95aabe;
  }
  .value {
    color: #f4faff;
  }

  .content-header {
    padding-left: 13px;
    padding-bottom: 31px;
    border-bottom: 1px solid #093c68;
  }

  .tab-content {
    padding: 20px 0;

    .main-header {
      width: 163px;
      height: 30px;
      line-height: 30px;
      background-image: url(~@/assets/waterPng/<EMAIL>);
      color: #f4faff;
      padding-left: 13px;
    }

    .tab-pane {
      height: 454px;
      background: linear-gradient(0deg, rgba(9, 21, 42, 0.6) 0%, rgba(22, 49, 95, 0.6) 100%);
      padding: 17px;
    }
  }

  .tab-pane {
    color: #f4faff;
  }
}
</style>
