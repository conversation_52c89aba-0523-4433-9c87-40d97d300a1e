<template>
  <div
    class="row-list"
    const
    props="defineProps({"
    :class="{
      'row-title': rowTitle,
      'row-double': data.index % 2 == 0 ? 'row-double' : '',
    }"
    :style="{ height: height + 'px' }"
  >
    <a-tooltip v-if="data.list.departmentName" placement="top">
      <template slot="title">
        <span>{{ data.list.departmentName }}</span>
      </template>
      <span>{{ data.list.departmentName }}</span>
    </a-tooltip>
    <a-tooltip placement="top">
      <template slot="title">
        <span :title="data.list.name">{{ data.list.name }}</span>
      </template>
      <span :title="data.list.name">{{ data.list.name }}</span>
    </a-tooltip>
    <a-tooltip placement="top">
      <template slot="title">
        <span :title="data.list.tel">{{ data.list.tel }}</span>
      </template>
      <span :title="data.list.tel">{{ data.list.tel }}</span>
    </a-tooltip>
    <a-tooltip placement="top">
      <template slot="title">
        <span :title="data.list.state" :class="data.list.className">{{
          data.list.state
        }}</span>
      </template>
      <span :title="data.list.state" :class="data.list.className">{{
        data.list.state
      }}</span>
    </a-tooltip>
  </div>
</template>

<script>
export default {
  name: "peopleRow",
  props: {
    height: {
      type: Number,
      default: 36,
    },
    list: {
      type: Object,
      default: () => {},
    },
    rowTitle: {
      type: String,
      default: "",
    },
    index: {
      type: Number,
      default: -1,
    },
  },
  data() {
    return {
      data: {
        list: {},
        index: this.index,
      },
    };
  },
  watch: {
    list: {
      handler() {
        this.setData();
      },
      immediate: true,
      deep: true,
    },
  },
  mounted() {
    this.setData();
  },
  methods: {
    setData() {
      const className =
        this.list.state === "进行中"
          ? "going"
          : this.list.state === "已完成"
          ? "completed"
          : "";
      this.data.list = { ...this.list, className };
    },
  },
};
</script>

<style lang="less" scoped>
.row-list {
  display: flex;
  align-items: center;
  // height: 36px;
  font-size: 14px;
  font-family: PingFang SC;
  font-weight: 400;
  color: #ffffff;
  background: linear-gradient(
    to right,
    rgba(0, 0, 0, 0) 0%,
    rgba(14, 133, 255, 0.1) 30%,
    rgba(14, 133, 255, 0.3) 50%,
    rgba(14, 133, 255, 0.1) 70%,
    rgba(0, 0, 0, 0) 100%
  );
  span {
    flex: 1;
    text-align: center;
    font-size: 14px;
    font-family: PingFang SC;
    font-weight: 400;
    padding: 0 2px;
    color: #caf6ff;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  &:nth-child(2n) {
  }
  .going {
    color: #edc621;
  }
  .completed {
    color: #02e6cf;
  }
}
.row-double {
  background: transparent;
}
.row-title {
  margin-top: 16px;
  background: transparent;
  span {
    color: #fff;
  }
}
</style>
