<template>
  <div class="card-box-container" :class="[$attrs.class]">
    <!-- 头部区域 S -->
    <div class="card-box-head">
      <!-- 左侧 -->
      <div class="flex items-center card-title">
        <div class="title">{{ title }}</div>
      </div>
      <div class="sub-title">
        <img src="@/assets/biaoti.png" alt class="title-img" />
      </div>
      <div class="flex items-center justify-end flex-grow card-head-slot">
        <slot name="right"></slot>
      </div>
      <!-- 右侧 -->
    </div>
    <!-- 头部区域 E -->
    <!-- 内容区域 S -->
    <div class="card-box-content">
      <slot></slot>
    </div>
    <!-- 内容区域 E -->
  </div>
</template>
<script>
export default {
  name: 'cardBox',
  props: {
    title: {
      type: String,
      default: '盒子容器',
    },
  },
}
</script>

<style lang="less">
.card-box-container {
  width: 100%;

  .card-box-head,
  .card-box-content {
    width: 100%;
  }

  .card-box-head {
    height: 32px;
    // background: url('../../assets/images/<EMAIL>') no-repeat center
    //   center;
    background-size: 100% auto;
    // padding-left: 60px;
    padding-right: 20px;
    font-size: 18px;
    color: #ffffff;
    position: relative;
    .sub-title {
      height: 15px;
      line-height: 15px;
      > img {
        width: 100%;
        height: 0.1rem;
      }
    }
    .title{
      font-size: 0.2rem;
      font-family: Source Han Sans CN;
      font-weight: 500;
      text-shadow: 0 0 5px blue, 0 0 5px blue;
    }
    .card-head-slot{
      position: absolute;
      top: 15px;
      right: 20px;
    }
  }

  .card-box-content {
    margin-top: 30px;
  }
  .justify-end {
    justify-content: flex-end;
  }
  .items-center {
    align-items: center;
  }
  .flex-grow {
    flex-grow: 1;
  }
  .flex {
    display: flex;
  }
}
</style>
