import request from "@/utils/request";
import { AxiosPromise } from "axios";

// 站点，区县排名
export function weatherSort(params: WeatherSortQuery): AxiosPromise<{data: WeatherData[]}> {
    return request({
        url: "/air/weather/sort",
        method: "get",
		params
    });
}

// 获取气象站点列表
export function weatherStationList(): AxiosPromise<{data: WeatherStation[]}> {
	return request({
		url: "/air/weather_station/list",
		method: "get"
	});
}

// 获取气象站点监测趋势
export function weatherStationTrend(params: StationQuery): AxiosPromise<{data: WeatherStationTrend}> {
	return request({
		url: "/air/weather/monitoringTrends",
		method: "get",
		params
	});
}

export interface WeatherSortQuery {
	// 1 站点 2 区域
	type: number;
	// 1 小时 2 天
	timeType: number;
	findTime: string;
	// 排序 temperature ：温度  rainfall：降雨量
	sortItem: string;
	// desc  asc
	sortType: string;
}
export interface WeatherData {
	name?: any;
	code: string;
	monitorTime: string;
	type: number;
	timeType: number;
	windDirection: number;
	windDirectionName: string;
	windPower: number;
	windLevel: number;
	windLevelName?: any;
	temperature: number;
	humidity: number;
	pressure: number;
	rainfall?: any;
}

export interface WeatherStation {
	stationId: string;
	province: string;
	city: string;
	county: string;
	stationName: string;
	lat: string;
	lng: string;
	altitude?: any;
	address: string;
	monitorTime: string;
	dayMonitorDate: string;
	windDirection: number;
	windPower: number;
	temperature: number;
	humidity: number;
	pressure: number;
	dataSource: number;
	rainfall1h: number;
	rainfall3h: number;
	rainfall6h: number;
	rainfall12h: number;
	rainfall24h: number;
	monitorCode: string[];
	online: boolean;
}

export interface StationQuery {
	// 1 小时 2 天
	type: number
	stationId: string
	startTime: string
	endTime: string
}
export interface WeatherStationTrend {
	itemList: TrendItem[]
	[property: string]: Item
}
export interface TrendItem {
	id: number;
	code: string;
	name: string;
	chineseName: string;
	bindingName?: any;
	unit: string;
	typeId?: any;
	description?: any;
	dataRound?: any;
}
export interface Item {
	unit: string;
	x: string[];
	y: string[];
}
