<?xml version="1.0" encoding="utf-8"?>
<ckplayer>
<title>{font color="#FFDD00" face="Microsoft YaHei,微软雅黑" size="14"}精彩视频推荐{/font}</title>
	<!--左上角的标题说明文字，支持html-->
	<area>600,400,0xFFDD00,20</area>
	<!--
		该插件在播放中的区域控制
		固定尺寸，精彩视频区域会固定大小并且保持剧中
		1：宽
		2：高
		3：边框色
		4：边框透明度
	-->
	<image>120,90</image>
	<!--图片的宽，高-->
	<distance>150,175,100</distance>
	<!--
		1：横排图片之间的距离，前一个图片的左边到后一个图片的左边的距离
		2：竖排图片之间距离，上一个图片的上边到下一个图片的上边距离，这里需要特别注意，因为图片显示区域是总区域-40的高度，默认的即<area>的高400-40=360，所以比如你的图片想一列显示二个，则该参数需要设置成360/2=180
		3：文字距离图片上方的距离
		只要设置好这三个参数，图片的位置就可以固定住了
	-->
	<rep_title>{a href="[url]" target="_blank"}{font color="#FFFFFF" face="新宋体"}[title]{/font}{/a}</rep_title>
	<!--标题替换规则-->
	<style>a{color:#FFFFFF;}a:hover{color:#FF0000;}</style>
	<!--文本样式-->
	<size>120,42,3</size>
	<!--文本的宽度，高度，行和行之间距离-->
	<relatedlist>
		<related>
			<img>temp/1.jpg</img>
			<url>http://www.ckplayer.com/</url>
			<title>1：这里调用的文件配置在ckplayer/related.xml里</title>
		</related>
		<related>
			<img>temp/2.jpg</img>
			<url>http://www.ckplayer.com/</url>
			<title>2：支持多页调用</title>
		</related>
		<related>
			<img>temp/3.jpg</img>
			<url>http://www.ckplayer.com/</url>
			<title>3：文本请保持在二行，不能多于三行，多于三行将不显示</title>
		</related>
		<related>
			<img>temp/4.jpg</img>
			<url>http://www.ckplayer.com/</url>
			<title>4：支持多行调用，多页调用</title>
		</related>
		<related>
			<img>temp/5.jpg</img>
			<url>http://www.ckplayer.com/</url>
			<title>5：感谢对ckplayer的支持</title>
		</related>
		<related>
			<img>temp/6.jpg</img>
			<url>http://www.ckplayer.com/</url>
			<title>6：最多两行,请不要超过二行</title>
		</related>
		<related>
			<img>temp/3.jpg</img>
			<url>http://www.ckplayer.com/</url>
			<title>7：这里调用的文件配置在ckplayer/related.xml里</title>
		</related>
		<related>
			<img>temp/5.jpg</img>
			<url>http://www.ckplayer.com/</url>
			<title>8：支持多页调用</title>
		</related>
		<related>
			<img>temp/4.jpg</img>
			<url>http://www.ckplayer.com/</url>
			<title>9：文本请保持在二行，不能多于三行，多于三行将不显示</title>
		</related>
		<related>
			<img>temp/1.jpg</img>
			<url>http://www.ckplayer.com/</url>
			<title>10：支持多行调用，多页调用</title>
		</related>
		<related>
			<img>temp/2.jpg</img>
			<url>http://www.ckplayer.com/</url>
			<title>11：感谢对ckplayer的支持</title>
		</related>
		<related>
			<img>temp/6.jpg</img>
			<url>http://www.ckplayer.com/</url>
			<title>12：最多两行,请不要超过二行</title>
		</related>
		<related>
			<img>temp/5.jpg</img>
			<url>http://www.ckplayer.com/</url>
			<title>13：这里调用的文件配置在ckplayer/related.xml里</title>
		</related>
		<related>
			<img>temp/4.jpg</img>
			<url>http://www.ckplayer.com/</url>
			<title>14：支持多页调用</title>
		</related>
		<related>
			<img>temp/6.jpg</img>
			<url>http://www.ckplayer.com/</url>
			<title>15：文本请保持在二行，不能多于三行，多于三行将不显示</title>
		</related>
		<related>
			<img>temp/3.jpg</img>
			<url>http://www.ckplayer.com/</url>
			<title>16：支持多行调用，多页调用</title>
		</related>
		<related>
			<img>temp/1.jpg</img>
			<url>http://www.ckplayer.com/</url>
			<title>17：感谢对ckplayer的支持</title>
		</related>
		<related>
			<img>temp/2.jpg</img>
			<url>http://www.ckplayer.com/</url>
			<title>18：最多两行,请不要超过二行</title>
		</related>
	</relatedlist>
</ckplayer>