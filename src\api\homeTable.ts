import request from "@/utils/request";
import { AxiosPromise } from "axios";

/**
 * @method pollutionCount
 * @param {type} data 说明
 * @description 空气污染源统计
 */
export function pollutionCount(): AxiosPromise<any> {
  return request({
    url: "/air/pollution-count",
    method: "get"
  });
}

/**
 * @method recentWeather
 * @param {type} data 说明
 * @description 气象在线监测
 */
export function recentWeather(): AxiosPromise<any> {
  return request({
    url: "/air/weather/recent",
    method: "get"
  });
}

/**
 * @method heavyPollutionList
 * @param {type}
 * @description 重污企业
 */
export function heavyPollutionList(districtCode = 510106): AxiosPromise<any> {
  return request({
    url: "/water/monitor/getHeavilyPollutingEnterpriseList",
    method: "get",
    params: { districtCode }
  });
}

/**
 * @method earlyWarning
 * @param {type} data 说明
 * @description  实时监测预警
 */
export function earlyWarning(): AxiosPromise<any> {
  return request({
    url: "home/alarm",
    method: "get"
  });
}
/**
 * @method airTrend
 * @param {type} data 说明
 * @description 空气监测类型
 */
export function getAirTypes(params: any): AxiosPromise<any> {
  return request({
    url: "/air/air_station/pollution_codes",
    method: "get",
    params
  });
}
/**
 * @method airTrend
 * @param {type} data 说明
 * @description 空气监测趋势
 */
export function airTrend(params: any): AxiosPromise<any> {
  return request({
    // url: "air_station/aqi_quality/trend",
    url: "/air/air_station/air_detection_trends",
    method: "get",
    params
  });
}
/**
 * @method airStation
 * @param {type} data 说明
 * @description  空气监测站点记录
 */
export function airStation(params: any): AxiosPromise<any> {
  return request({
    url: "/air/air_station/home/<USER>",
    method: "get",
    params
  });
}
/**
 * @method waterStationList
 * @param {type} data 说明
 * @description 水质监测站点
 */
export function waterStationList(params: any): AxiosPromise<any> {
  return request({
    url: "/water/monitor/getStationList",
    method: "get",
    params
  });
}
/**
 * @method waterStationRecord
 * @param {type} data 说明
 * @description 水质监测记录
 */
export function waterStationRecord(stationId: any): AxiosPromise<any> {
  return request({
    url: "/water/monitor/getRealtimeMonitorRecordList",
    method: "get",
    params: { stationId }
  });
}
/**
 * @method waterTrend
 * @param {type} data 说明
 * @description 水质监测趋势
 */
export function waterTrend(districtCode: any, type: any): AxiosPromise<any> {
  const url: string =
    type == 1
      ? "/water/monitor/getTodayMonitorItemRecord"
      : "/water/monitor/getMonthMonitorItemRecord";
  return request({
    url,
    method: "get",
    params: { districtCode }
  });
}

/**
 * @method waterTrend
 * @param {type} data 说明
 * @description 水质污染
 */
export function waterQuality(): AxiosPromise<any> {
  return request({
    url: "/water/water-quality/getWaterQuality",
    method: "post"
  });
}

/**
 * @method waterTrend
 * @param {type} data 说明
 * @description 水质监测趋势
 */
export function heavilyPollutingEnterpriseList(districtCode: any, type:any): AxiosPromise<any> {
  return request({
    url: "/water/monitor/getHeavilyPollutingEnterpriseList",
    method: "get",
    params:{
      districtCode,
      type
    }
  });
}
/**
 * @method getCarList
 * @param {type} data 车辆类型统计
 * @description
 */
export function getCarList(): AxiosPromise<any> {
  return request({
    url: "/car/getCarTypeCountList",
    method: "get"
  });
}
/**
 * @method getCarList
 * @param {type} data 车辆类型统计
 * @description
 */
export function getOnlineCount(): AxiosPromise<any> {
  return request({
    url: "/car/device/online/count",
    method: "get"
  });
}
/**
 * @method getCarList
 * @param {type} data 车辆类型统计
 * @description
 */
export function getOffline(): AxiosPromise<any> {
  return request({
    url: "/car/device/offline/list",
    method: "get"
  });
}
/**
 * @method getCarList
 * @param {type} data 车辆类型统计
 * @description
 */
export function typeStationInfoList(): AxiosPromise<any> {
  return request({
    url: "/air/air_station/typeStationInfoList",
    method: "get"
  });
}
/**
 * @method functionName
 * @param {type} data 本年街道污染物
 * @description
 */
export function yearStreet(): AxiosPromise<any> {
  return request({
    url: "/air/air-station-day-aqi/airStreetYearCount",
    method: "get"
  });
}
/**
 * @method functionName
 * @param {type} data 本月街道污染物
 * @description
 */
export function monthStreet(): AxiosPromise<any> {
  return request({
    url: "/air/air-station-day-aqi/airStreetMonthCount",
    method: "get"
  });
}
/**
 * @method functionName
 * @param {type} data 本年站点污染物
 * @description
 */
export function yearSite(): AxiosPromise<any> {
  return request({
    url: "/air/air-station-day-aqi/airYearCount",
    method: "get"
  });
}
/**
 * @method functionName
 * @param {type} data 本月站点污染物
 * @description
 */
export function monthSite(): AxiosPromise<any> {
  return request({
    url: "/air/air-station-day-aqi/airYearCount",
    method: "get"
  });
}
/**
 * @method functionName
 * @param {type} data 说明
 * @description
 */
export function getMonitorReport(type: any): AxiosPromise<any> {
  return request({
    url: "/water/monitor_report",
    method: "get",
    params: {
      type
    }
  });
}
/**
 * @method functionName
 * @param {type} data 说明
 * @description
 */
export function functionName(data: any): AxiosPromise<any> {
  return request({
    url: "url",
    method: "post",
    data: data
  });
}
/**
 * @method functionName
 * @param {type} data 说明
 * @description
 */
export function getStatistics(params: any): AxiosPromise<any> {
  return request({
    url: "/air/air_station/statistics",
    method: "get",
    params
  });
}
/**
 * @method functionName
 * @param {type} data 任务执行率统计
 * @description
 */
 export function selectMonthTaskCompletion(): AxiosPromise<any> {
  return request({
    url: "/task/selectMonthTaskCompletion",
    method: "get"
  });
}
/**
 * @method functionName
 * @param {type} data 任务类型完成情况
 * @description
 */
 export function selectTaskTypeCompletion(type: number|string): AxiosPromise<any> {
  return request({
    url: `/task/selectTaskTypeCompletion?timeType=${type}`,
    method: "get"
  });
}
/**
 * @method functionName
 * @param {type} data 任务类型完成情况
 * @description
 */
 export function selectTypeStatistic(type: number|string): AxiosPromise<any> {
  return request({
    url: `/task/selectTypeStatistic?timeType=${type}`,
    method: "get"
  });
}
/**
 * @method functionName
 * @param {type} data 任务类型完成情况
 * @description
 */
export function listStationPollutantMonitorAnalysis(params: any): AxiosPromise<any> {
  return request({
    url: `/air/air-station-hour-aqi/listStationPollutantMonitorAnalysis`,
    method: "get",
    params
  });
}

