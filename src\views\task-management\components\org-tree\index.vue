<template>
  <div class="org-boxs">
    <vue2-org-tree
      v-drag
      name="test"
      :data="treeData"
      :horizontal="horizontal"
      :label-class-name="labelClassName"
      :render-content="renderContent"
      @on-expand="onExpand"
      @on-node-click="onNodeClick"
    />
  </div>
</template>

<script>
export default {
  name: '',
  props: {
    orgTreeList: {
        type: Array,
        default: () => { return []}
    },
    typeId: {
      type: Number,
      default: 0
    }
  },
  // 开启拖拽的指令
directives: {
    drag: {
      // 指令的定义
      bind: function (el) {
        const odiv = el // 获取当前元素
        odiv.onmousedown = (e) => {
          // 算出鼠标相对元素的位置
          const disX = e.clientX - odiv.offsetLeft
          const disY = e.clientY - odiv.offsetTop

          document.onmousemove = (e) => {
            // 用鼠标的位置减去鼠标相对元素的位置，得到元素的位置
            const left = e.clientX - disX
            const top = e.clientY - disY
            // 移动当前元素
            odiv.style.left = left + 'px'
            odiv.style.top = top + 'px'
          }
          document.onmouseup = (e) => {
            document.onmousemove = null
            document.onmouseup = null
          }
        }
      }
    }
  },
  data () {
      return {
        horizontal: false,
        collapsable: false,
        expandAll: true,
        labelClassName: "bg-none",
        manUrl:require('@/assets/department/<EMAIL>'),
        womanUrl:require('@/assets/department/<EMAIL>'),
        shemaleUrl:require('@/assets/department/<EMAIL>'),
        bumen: require('@/assets/department/orgTree/<EMAIL>'), // 部门
        dqgj: require('@/assets/department/orgTree/<EMAIL>'), // 大气告警
        renyuan: require('@/assets/department/orgTree/<EMAIL>'), // 人员
        rwdd: require('@/assets/department/orgTree/<EMAIL>'), // 巡岗报警
        shjgj: require('@/assets/department/orgTree/<EMAIL>'), // 水环境告警
        whuifu: require('@/assets/department/orgTree/<EMAIL>'), // 未回复
        zwgj: require('@/assets/department/orgTree/<EMAIL>'), // 污染源告警
        chaosong: require('@/assets/department/orgTree/<EMAIL>'), // 污染源告警
        treeData:{}
      }
    },
  created() {},
  mounted(){
    this.treeData = this.arrayToTree5(this.orgTreeList)
    this.initOrg()
  },
  methods: {
    onNodeClick(){},
    onExpand(){},
    initOrg(){
        this.$set(this.treeData,'expand',true);
        if(this.treeData.children){
          this.treeData.children.forEach((item,index)=>{
            this.$set(item,'expand',true);
          })
        }
      },
      renderContent(h, data) {
        return (
        <div style="background:transparent;padding;20px;">
            <div style="background:transparent;padding;20px;"><img src={data.icon} /></div>
            <div style="background:transparent;padding;20px;">{data.taskNodeName}</div>
        </div>
        )
      },
      arrayToTree5(items) {
      const result = [];   // 存放结果集
      const itemMap = {};  // 
      for (const item of items) {
        const id = item.taskNodeId;
        const pid = item.parentId;
 
        if (!itemMap[id]) {
          itemMap[id] = {
            children: [],
          }
        }
        let icon = '' // 图标
        let title = '' // 标题
        // 告警类型
        if(item.taskNodeId && !item.departmentId && !item.userId) {
          if(this.typeId == 1) {
            icon = this.shjgj
          }
          if(this.typeId == 2) {
            icon = this.dqgj
          }
          if(this.typeId == 3) {
            icon = this.zwgj
          }
          if(this.typeId == 4) {
            icon = this.rwdd
          }
            
        }
        // 部门
        if(item.taskNodeId && item.departmentId && !item.userId) {
            icon = this.bumen
        }
        // 人员
        if(item.taskNodeId && item.departmentId && item.userId) {
          // 1 抄送者  2 执行者
          if(item.permission === 1) {
            icon = this.chaosong
          }else{
            // 完成状态(0 进行中, 1 已完成, 2 已关闭)
            if(item.completeStatus === 0){
              icon = this.whuifu
            }else{
              icon = this.renyuan
            }
            
          }
        }
 
        itemMap[id] = {
          ...item,
          icon,
          id,
          children: itemMap[id]['children']
        }
 
        const treeItem = itemMap[id];
 
        if (!pid) {
          result.push(treeItem);
        } else {
          if (!itemMap[pid]) {
            itemMap[pid] = {
              children: [],
            }
          }
          itemMap[pid].children.push(treeItem)
        }
 
      }
      return result[0];
    }
  },
  computed: {},
  watch: {},
  components: {}
}
</script>
<style scoped>
* {
  -webkit-touch-callout: none; /*系统默认菜单被禁用*/
  -webkit-user-select: none; /*webkit浏览器*/
  -khtml-user-select: none; /*早期浏览器*/
  -moz-user-select: none; /*火狐*/
  -ms-user-select: none; /*IE10*/
  user-select: none;
}
*::-webkit-scrollbar {
  display: none; /* Chrome Safari */
}
</style>
<style>
.org-tree-container {
  position: relative; /*定位*/
  top: 0;
  left: 0;
}


img {
  -webkit-user-drag: none;
}
</style>
<style lang="less">
@import "./index.less";
.org-boxs {
  width: 100%;
  height: 100%;
  text-align: center;
  cursor: pointer;
  position: relative;
  overflow: auto;
}
.org-tree-container {
  background: none !important;
}
.org-tree-node-label {
  white-space: nowrap;
  -webkit-user-drag: none;
}
.bg-none {
  background-color: #030c24;
  color: #ffffed;
}
.bg-white {
  background-color: #ecf5ff;
}
.org-tree-node-label .org-tree-node-label-inner {
  min-width: 6vw;
  padding: 0px 0px;
  text-align: center;
  border-radius: 3px;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.15);
  /* border: 1px solid @colors;*/
  overflow: hidden;
  box-sizing: border-box;
  background: transparent;
}
.bg-tomato {
  background-color: #9e4a1c;
}
.bg-gold {
  background-color: #eca150;
}
.bg-gray {
  background-color: #deceaa;
}
.bg-lightpink {
  background-color: lightpink;
}
.bg-blue {
  background-color: #057d9f;
}
.bg-green {
  background-color: #50cb90;
}
</style>
