<style lang="less" scoped>
.topTab {
  position: fixed;
  top: 1.5rem;
  left: 0.93rem;
  display: flex;
  z-index: 9999;
  .air_btn,
  .water_btn {
    width: 1.57rem;
    height: 0.54rem;
    font-size: 0.14rem;
    font-family: PingFang SC;
    font-weight: 400;
    color: #0d93f0;
    line-height: 0.554rem;
    text-align: center;
    background: url('~@/assets/recheck/<EMAIL>') no-repeat;
    background-size: 100%;
    margin-right: 0.1rem;
  }
  .btn_active {
    font-size: 0.14rem;
    color: #e7f6fb;
    background: url('~@/assets/recheck/<EMAIL>') no-repeat;
    background-size: 100%;
  }
}
.sequence_active {
  background: rgba(53, 138, 243, 0.884) !important;
}
.title_select {
  position: absolute;
  right: 0;
  top: 0;
  display: flex;
  .title_select_item {
    width: 0.85rem;
    height: 0.32rem;
    background: url('../../assets/noise/shaixuanc <EMAIL>') no-repeat;
    background-size: 100%;
    text-align: center;
    line-height: 0.32rem;
    font-size: 0.14rem;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #0c6dc3;
    cursor: pointer;
  }
  .title_select_item_select {
    background: url('../../assets/noise/<EMAIL>') no-repeat;
    color: #3adbff;
  }
}
.cater_rank_box {
  .cater_rank_th,
  .cater_rank_tr {
    display: flex;
    div {
      padding: 3px;
      text-align: center;
      &:nth-child(1) {
        width: 40px;
      }
      &:nth-child(3) {
        width: 64px;
      }
      &:nth-child(2) {
        flex: 1;
        overflow: hidden; // 超出边框外隐藏
        text-overflow: ellipsis; // 属性规定当文本溢出包含元素时发生的事情。
        white-space: nowrap; // 规定段落中的文本不进行换行
      }
    }
  }
  .cater_rank_th {
    padding: 0 15px;
  }
  .cater_rank_tb {
    padding: 0 15px;
    height: 198px;
    overflow-y: auto;
    // &::-webkit-scrollbar {
    //   width: 3px !important;
    //   height: 3px !important;
    // }
    &::-webkit-scrollbar-thumb {
      border-radius: 10px;
      width: 1px;
      background: #08336c;
    }
    &::-webkit-scrollbar-track {
      border-radius: 10px;
      background: rgba(57, 177, 255, 0);
    }
    .cater_rank_tr {
      padding: 3px 0;
      &:nth-child(2n + 1) {
        background-color: #052f61;
      }
      div {
        .index {
          display: inline-block;
          width: 30px;
          height: 100%;
          background-color: #1288e2;
          color: #fff;
          border-radius: 3px;
        }
      }
    }
  }
}
// 右到左
.pul-randa-r2l {
  animation: pu-randa-r2l 1.5s linear;
}
@keyframes pu-randa-r2l {
  0% {
    transform: translate(120%, 0);
  }
  50% {
    transform: translate(120%, 0);
  }
  100% {
    transform: translate(0, 0);
  }
}
// 左到右
.pul-randa-l2r {
  animation: pu-randa-l2r 1.5s linear;
}
@keyframes pu-randa-l2r {
  0% {
    transform: translate(-120%, 0);
  }
  50% {
    transform: translate(-120%, 0);
  }
  100% {
    transform: translate(0, 0);
  }
}
.common-main {
  position: relative;
  overflow: hidden;
  width: 100%;
  padding: 0;
  .small_map {
    position: absolute;
    right: 40px;
    bottom: 40px;
    width: 179px;
    height: 130px;
    border-radius: 2px;
    background: #000;
    z-index: 1000000;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .RHSCC {
    position: absolute;
    left: 300px;
    bottom: 40px;
    width: 240px;
    height: 74px;
    background: rgba(0, 4, 18, 0.5);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    // padding: 20px 7px 0 0;
    // padding: ;
    .RHSCC1 {
      width: 100%;
      display: flex;
      // flex-direction: column;
      justify-content: space-between;
      font-size: 12px;
      font-family: PingFang SC;
      font-weight: 500;
      color: #ffffff;
      height: 30px;
      text-align: center;
      // flex: 1;
      > span {
        flex: 1;
        position: relative;
      }
      > span::after {
        content: '';
        display: block;
        position: absolute;
        width: 2px;
        height: 22px;
        left: 25px;
        // transform: translateX(-50%);
        color: #fff;
        top: -13px;
        border-top: 12px solid #fff;
        z-index: 10;
      }
      // > span:nth-child(1)::after {
      //   position: absolute;
      //   top: 0;
      //   right: -10px;
      // }
      // > span:nth-child(5)::after {
      //   position: absolute;
      //   top: 16px !important;
      //   right: -10px;
      // }
    }
    .RHSCC2 {
      width: 200px;
      height: 23px;
      background: linear-gradient(90deg, #0045b8 0%, #05fffb 25%, #92ff42 50%, #ffaa00 75%, #f90b00 100%);
    }
  }

  .controls {
    position: absolute;
    right: 40px;
    top: 30px;
    width: 670px;
    height: 66px;
    background: rgba(0, 4, 18, 0.5);
    border-radius: 4px;
    display: flex;
    padding: 10px;
    justify-content: space-between;
    > div {
      font-size: 22px;
      font-family: PingFang SC;
      font-weight: 500;
      color: #ffffff;
      line-height: 46px;
      text-align: center;
      flex: 1;
      img {
        margin-right: 5px;
      }
    }
    > div:nth-child(1) {
      flex: 1.6;
      border-right: 2px solid #808080;
      position: relative;
      .icon-selecte-box {
        position: absolute;
        padding: 5px 5px 5px 15px;
        margin-top: 20px;
        left: -9px;
        width: 190px;
        background-color: rgba(0, 4, 18, 0.56);
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        border-top: 2px solid #3883df;
        border-bottom: 2px solid #3883df;
        .randaSel_item {
          height: 0.34rem;
          width: 100%;
          text-align: center;
          line-height: 0.32rem;
          color: #a6c0d5;
          font-size: 0.2rem;
          box-sizing: border-box;
          font-family: PingFang SC;
          font-weight: 500;
          cursor: pointer;
          background: url('../../assets/randa/<EMAIL>') no-repeat;
          background-size: 100%;
          margin-bottom: 10px;
          margin-top: 10px;
        }
        .randaSel_item:hover {
          width: 100%;
          height: 0.53rem;
          font-size: 0.2rem;
          color: #fefefe;
          line-height: 0.33rem;
          background: url('../../assets/randa/<EMAIL>') no-repeat !important;
          background-size: 95% !important;
        }

        .randaSel_item_active {
          width: 100%;
          height: 0.53rem;
          font-size: 0.2rem;
          color: #fefefe;
          line-height: 0.33rem;
          background: url('../../assets/randa/<EMAIL>') no-repeat;
          background-size: 95%;
        }
      }
    }
    .controls1::after {
      content: '';
      display: block;
      // width: 1px;
      position: absolute;
      right: 15px;
      top: 20px;
      border: 10px solid rgba(255, 255, 255, 0);
      border-top: 10px solid #fff;
    }
    .controls2 {
      color: #00eaff !important;
    }
  }

  .sequence {
    position: absolute;
    left: 30px;
    top: 30px;
    width: 220px;
    min-height: 400px;
    max-height: 920px;
    background: rgba(0, 4, 18, 0.5);
    border: 1px solid;
    border-image: linear-gradient(0deg, #3589f3, #3589f3) 10 10;
    display: flex;
    flex-direction: column;
    .sequence_title {
      height: 44px;
      font-size: 18px;
      font-family: PingFang SC;
      font-weight: 500;
      color: #ffffff;
      line-height: 44px;
      border-bottom: 1px solid #3589f3;
      position: relative;
      padding-left: 30px;
      &::after {
        content: '';
        display: block;
        width: 4px;
        height: 16px;
        background: #3589f3;
        border-radius: 2px;
        position: absolute;
        top: 14px;
        left: 16px;
      }
    }
    .sequence_cont {
      flex: 1;
      margin-top: 20px;
      padding: 0 8px;
      overflow: auto;
      div {
        font-size: 14px;
        font-family: DIN;
        font-weight: 500;
        color: #ffffff;
        text-align: center;
        height: 38px;
        line-height: 38px;
        border-radius: 4px;
        margin: 10px 0;
      }
      &::-webkit-scrollbar {
        width: 0px !important;
        height: 0px !important;
      }
    }

    .choose_date {
      position: relative;
      height: 46px;
      background-image: url('../../assets/<EMAIL>');
      background-size: 100% 100%;
      margin-bottom: 20px;
      margin-top: 16px;
      text-align: center;
      font-size: 17px;
      line-height: 46px;
      font-family: DIN;
      font-weight: 500;
      color: #ffffff;
      padding-left: 10px;
      cursor: pointer;
      img {
        position: absolute;
        width: 17px;
        height: 17px;
        left: 37px;
        top: 13px;
      }
    }
  }
  .container-bg {
    position: absolute;
    pointer-events: none;
    z-index: 2;
    width: 100%;
    height: 100%;
    background-size: 100% 100%;
    opacity: 0.8;
    background-image: url('../../assets/department/<EMAIL>');
  }
  .container-bg1 {
    position: absolute;
    pointer-events: none;
    z-index: 2;
    width: 100%;
    height: 100%;
    background-size: 100% 100%;
    opacity: 0.8;
    background-image: url('../../assets/recheck/ylfp_bg.png');
  }
  .left-part {
    pointer-events: auto;
    > div {
      width: 4rem;
    }

    position: absolute;
    top: 0.5rem;
    left: 0.5rem;
    .title {
      font-size: 0.2rem;
      cursor: pointer;
    }
    .flex-number {
      cursor: pointer;
      display: flex;
      margin-top: 0.18rem;
      > div {
        background-image: url('../../assets/<EMAIL>');
        background-size: 100% 100%;
        /*border: 1px solid RGBA(33, 109, 253, 1);*/
        /*background-color: RGBA(35, 211, 255, 0.6);*/
        font-size: 0.67rem;
        text-align: center;
        width: 0.73rem;
        height: 0.88rem;
        line-height: 0.88rem;
        font-family: '300-CAI978';
      }
    }
    .video-main {
      margin-top: 3rem;
      .sub-title {
        > img {
          width: 50%;
          height: 0.1rem;
        }
      }
      .box-title {
        justify-content: start;
        .title {
          margin-right: 0.5rem;
        }
      }
      .title-video {
        display: flex;
        align-items: center;
      }
      img {
        width: 1.2rem;
      }
    }
  }
  .center-map {
    position: absolute;
    height: calc(1080px - 0.94rem);
    // margin-top: 0.15rem;
    > div {
      width: 100%;
      height: 100%;
    }
  }
  .opstion {
    top: 1rem;
  }
  .right-part {
    pointer-events: auto;
    position: absolute;
    top: 0;
    right: 0;
    height: calc(1080px - 1rem);
    width: 4.6rem;
    padding: 0.3rem 0.25rem 0;
    /*background-image: url(../../assets/air_bg.png) !important;*/
    background-size: 100% 100% !important;
    > .enterprise {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      // justify-content: space-between;
      > .enterprise-main {
        width: 100%;
        height: 32%;
      }
      > .pollute-main {
        height: 42%;
      }
      > .discharge-main {
        height: 26%;
      }
      // > .construction-site {
      // }
      // > .monitor-main {
      // }
    }
    > .construction-plant {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      // > div:first-child {
      // }
      > div:last-child {
        margin-top: 0.2rem;
      }
    }
    .sub-title {
      margin: 0 0 0.05rem;
    }
    .pollution-main {
      .update-time-pollution {
        font-size: 0.12rem;
        color: white;
        text-align: right;
      }
      .flex-box {
        width: 100%;
        display: flex;
        justify-content: space-between;
        font-size: 0.14rem;
        margin-top: 0.05rem;
        > div {
          .type-card {
            background: url('../../assets/card-top.png') no-repeat center;
            background-size: 100% 100%;
            height: 0.32rem;
            line-height: 0.32rem;
            text-align: center;
            margin-bottom: 0.05rem;
          }
        }
        > div:first-child {
          width: 40%;
          > div {
            margin-top: 0.3rem;
            .name {
              line-height: 0.12rem;
            }
            .number {
              font-size: 0.24rem;
              color: RGBA(0, 229, 255, 1);
              margin-right: 0.06rem;
              font-family: '300-CAI978';
            }
          }
          > div:first-child {
            margin-top: 0;
          }
          > div:nth-child(2) {
            margin-top: 0.08rem;
          }
        }
        > div:last-child {
          width: 60%;
          .change-box {
            width: 115%;
            display: flex;
            box-sizing: border-box;
            > div {
              width: 1.2rem;
              height: 0.2rem;
              -webkit-clip-path: polygon(25% 0%, 100% 0%, 75% 100%, 0% 100%);
              clip-path: polygon(25% 0%, 100% 0%, 75% 100%, 0% 100%);
              background-color: RGBA(6, 58, 114, 1);
              text-align: center;
              height: 0.24rem;
              line-height: 0.24rem;
              cursor: pointer;
            }
            > div:nth-child(2) {
              position: relative;
              right: 0.2rem;
            }
            > div:last-child {
              -webkit-clip-path: polygon(25% 0%, 100% 0%, 100% 100%, 0% 100%);
              clip-path: polygon(25% 0%, 100% 0%, 100% 100%, 0% 100%);
              position: relative;
              right: 0.4rem;
            }
            > .active {
              background-color: RGBA(0, 132, 255, 1);
            }
          }
        }
      }
    }
    .heavy-pollution-table,
    .table-site {
      width: 100%;
      border: 1px solid rgba(54, 218, 234, 0.42);
      .table-name {
        font-size: 0.12rem;
        width: 0.76rem;
        background: rgba(15, 36, 94, 1);
        text-align: center;
      }
      .table-text {
        padding-left: 0.06rem;
      }
      td {
        font-size: 0.12rem;
        height: 0.34rem;
        color: #ffffff;
        font-weight: 500;
      }
    }
    .table-site {
      width: 93%;
      .PM10-detail {
        display: flex;
        align-items: center;
        > span:first-child {
          color: rgb(51, 231, 51);
          font-size: 0.2rem;
        }
        > span:nth-child(2) {
          margin: 0 0.1rem;
        }
        > span:last-child {
          background: rgb(51, 231, 51);
          border-radius: 1px;
          padding: 0 0.05rem;
        }
      }
    }
    .table-data1 {
      cursor: pointer;
      width: 100%;
      .table-data-thead {
        .tr {
          display: flex;
          .th {
            color: #ffffff;
            padding: 0;
            text-align: center;
            border: none;
            font-size: 0.16rem;
          }
          > :nth-of-type(1) {
            width: 20%;
          }
          > :nth-of-type(2) {
            width: 40%;
            text-align: left;
          }
          > :nth-of-type(3) {
            width: 20%;
          }
          > :nth-of-type(4) {
            width: 20%;
          }
        }
      }
      .table-data-tbody {
        height: 1.36rem;
        overflow: hidden;
        .tr {
          display: flex;
          .td {
            color: #ffffff;
            padding: 0;
            font-size: 0.14rem;
            text-align: center;
            height: 0.34rem;
            line-height: 0.34rem;
            border: none;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          > :nth-of-type(1) {
            width: 20%;
            div {
              margin-top: 0.07rem;
            }
          }
          > :nth-of-type(2) {
            width: 40%;
            text-align: left;
          }
          > :nth-of-type(3) {
            width: 20%;
          }
          > :nth-of-type(4) {
            width: 20%;
          }
        }
      }
      .table-data-tbody .tr:nth-child(odd) {
        background: #0f245e;
      }
      .table-data-tbody .tr:nth-child(even) {
        background: transparent;
      }
      .tdBefore {
        //前三
        width: 0.26rem;
        display: block;
        text-align: center;
        height: 0.2rem;
        line-height: 0.2rem;
        background: rgba(255, 133, 9, 1);
        border-radius: 0.06rem;
        margin: 0 auto;
      }
      .tdAfter {
        //前三外
        width: 0.26rem;
        display: block;
        text-align: center;
        height: 0.2rem;
        line-height: 0.2rem;
        background: rgba(18, 136, 226, 1);
        border-radius: 0.06rem;
        margin: 0 auto;
      }
    }
  }
  .air-video {
    width: 3.67rem;
    height: 2.8rem;
    position: absolute;
    bottom: 2.25rem;
    left: 0.8rem;
    background-image: url('../../assets/<EMAIL>');
    background-size: 100% 100%;
    padding: 0.2rem;
    box-sizing: border-box;
    z-index: 99999;
    animation: left-entry 0.5s linear;
    .video-title {
      display: flex;
      align-items: center;
      position: relative;
      img {
        width: 0.17rem;
        margin-left: 0.18rem;
        height: 0.21rem;
      }
      span {
        font-size: 0.18rem;
        font-family: PingFang SC;
        font-weight: bold;
        color: #dbf5ff;
        margin-left: 0.16rem;
      }
      .close {
        position: absolute;
        right: 0;
        font-size: 18px;
        font-family: PingFang SC;
        font-weight: 400;
        color: #3db4ff;
      }
    }
    .air-video-area {
      width: 100%;
      height: 1.86rem;
      margin-top: 0.15rem;
    }
  }

  @keyframes left-entry {
    0% {
      transform: translate(-100%, 0);
      opacity: 0;
    }
    66% {
      transform: translate(-100%, 0);
      opacity: 0;
    }
    100% {
      transform: translate(0, 0);
      opacity: 1;
    }
  }
}
.right-side {
  position: absolute;
  pointer-events: auto;
  /*top: 0.6rem;*/
  /*right: 0.67rem;*/
  /*width: 3.9rem;*/
  overflow: hidden;
  z-index: 2;
  top: 0;
  right: 0;
  height: calc(1080px - 0.94rem);
  width: 4.6rem;
  padding: 0.3rem 0.3rem 0;
  background-image: url(../../assets/air_bg.png) !important;
  background-size: 100% 100% !important;
  .content {
    table {
      td {
        &:nth-child(1) {
          width: 25%;
        }
        &:nth-child(2) {
          width: 75%;
          max-width: 1rem;
        }
        height: 0.2rem;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        padding: 4px 8px !important;
      }
    }
  }
  /*height: calc(100vh - 2rem);*/
  .title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    > .see-more {
      font-size: 0.16rem;
      color: rgba(58, 219, 255, 1);
      text-shadow: none;
      cursor: pointer;
    }
  }
  .common-station-echarts {
    margin-top: 0.39rem;
    align-items: center;
    font-size: 0.2rem;
    font-weight: 500;
    color: #fff;
    text-shadow:
      0 0 0.1rem #2e73d6,
      0 0 0.1rem #2e73d6;
    margin-bottom: 0.1rem;
    > .title {
      display: flex;
      align-items: center;
      > span:nth-child(1) {
        display: inline-block;
        height: 0.25rem;
        margin-right: 0.2rem;
      }
      .more {
        font-size: 0.16rem;
        color: #3adbff;
        font-weight: 400;
        cursor: pointer;
      }
    }
    .button {
      display: flex;
      align-items: center;
      span {
        display: inline-block;
        padding: 0.08rem;
        text-shadow: none;
        font-size: 0.14rem;
        border-bottom: 1px solid #ffffff;
        margin-right: 0.1rem;
        opacity: 0.6;
        cursor: pointer;
      }
      .active {
        opacity: 1;
        background-color: rgba(14, 139, 255, 0.3);
        border-color: #00eaff;
      }
    }
    .station-echarts {
      height: 1.6rem;
      margin-top: 0.1rem;
    }
    .real-table {
      td {
        padding: 0.05rem 0.01rem;
        text-align: center;
        height: 0.3rem;
        &:nth-child(1) {
          min-width: 1.2rem !important;
        }
      }
      thead {
        td {
          color: #3be6ff;
        }
      }
    }
  }

  .motor-info {
    margin-top: 0.25rem;
    height: 3.26rem;
    align-items: center;
    font-size: 0.2rem;
    font-weight: 500;
    color: #fff;
    text-shadow:
      0 0 0.1rem #2e73d6,
      0 0 0.1rem #2e73d6;
    margin-bottom: 0.1rem;
    > title {
      display: flex;
      align-items: center;
      > span:nth-child(1) {
        display: inline-block;
        height: 0.25rem;
        margin-right: 0.2rem;
      }
      .more {
        font-size: 0.16rem;
        color: #3adbff;
        font-weight: 400;
        cursor: pointer;
      }
    }
    .motor-list {
      /*height: 2.3rem;*/
      overflow-y: hidden;
      .motor-content {
        background-image: url('../../assets/<EMAIL>');
        background-repeat: no-repeat;
        background-size: 100% 100%;
        height: 0.68rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
        /*margin-bottom: 0.1rem;*/
        padding: 0 0.26rem;
        .left {
          display: flex;
          align-items: center;
          span {
            &:nth-child(1) {
              display: inline-block;
              width: 0.5rem;
              height: 0.5rem;
              background-image: url('../../assets/<EMAIL>');
              background-size: 100% 100%;
              background-repeat: no-repeat;
              /*background: rgba(0, 234, 255, 0);*/
              /*box-shadow: 0px 0px 0.18rem 0px rgba(0, 167, 221, 0.7) inset;*/
              /*border-radius: 50%;*/
            }
            &:nth-child(2) {
              margin-left: 0.2rem;
              font-size: 0.18rem;
              font-weight: bold;
              color: white;
              text-shadow: none;
            }
          }
        }
        .right {
          width: 0.94rem;
          height: 0.34rem;
          background: rgba(37, 111, 17, 0);
          box-shadow: 0px 0px 0.18rem 0px rgba(0, 0, 0, 0.3) inset;
          border-radius: 17px;
          font-size: 0.18rem;
          line-height: 0.36rem;
          color: #333333;
          text-align: center;
        }
        .right1 {
          box-shadow: 0px 0px 0.18rem 0px rgba(246, 19, 25, 0.7) inset;
        }
        .right2 {
          box-shadow: 0px 0px 0.18rem 0px rgba(37, 111, 17, 0.7) inset;
        }
        .right3 {
          box-shadow: 0px 0px 0.18rem 0px rgba(255, 202, 20, 0.7) inset;
        }
      }
      .bg {
        margin-top: 0.2rem;
        height: 0.78rem;
        background-repeat: no-repeat;
        background-size: 100% 100%;
        background-image: url('../../assets/heavily-polluted-enterprise/<EMAIL>');
        position: relative;
        display: flex;
        align-items: center;
        > div {
          width: 50%;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          span {
            &:nth-child(1) {
              font-family: PangMenZhengDao;
              font-size: 0.26rem;
            }
            &:nth-child(2) {
              font-size: 16px;
              color: #3adbff;
            }
          }
        }
        img {
          position: absolute;
          width: 0.15rem;
          top: 0.04rem;
          left: 48%;
        }
      }
      .bottom {
        margin-top: 0.15rem;
        display: flex;
        align-items: center;
        justify-content: center;
        > img {
          width: 1.3rem;
          height: 1.73rem;
        }
        .right {
          .single {
            width: 2rem;
            height: 0.54rem;
            background-size: 100% 100%;
            background-repeat: no-repeat;
            position: relative;
            display: flex;
            align-items: center;
            padding-left: 0.35rem;
            &:first-child {
              margin-left: 0.4rem;
              margin-bottom: 0.26rem;
            }
            &:last-child {
              margin-left: 0.1rem;
            }
            img {
              position: absolute;
              left: 0.17rem;
              top: -0.07rem;
              width: 0.68rem;
              height: 0.68rem;
            }
            span {
              font-size: 0.16rem;
            }
            .warn {
              color: #ff173d;
              margin-right: 0.32rem;
            }
            .normal {
              color: #17f7ff;
              margin-right: 0.32rem;
            }
            .police {
              color: #ffb422;
              margin-right: 0.32rem;
            }
            .state {
              font-size: 0.14rem;
              color: white;
            }
            .text {
              font-size: 0.12rem;
              color: #c4dbfb;
              margin-right: 0.05rem;
            }
            .number {
              font-size: 0.2rem;
              color: #ffffff;
            }
          }
          .normal {
            background-image: url('../../assets/heavily-polluted-enterprise/<EMAIL>');
          }
          .warn {
            background-image: url('../../assets/heavily-polluted-enterprise/<EMAIL>');
          }
          .police {
            background-image: url('../../assets/heavily-polluted-enterprise/<EMAIL>');
          }
        }
      }
    }
    .electric-area {
      margin-top: 0.36rem;
      height: 2.62rem;
      overflow: hidden;
      .electric-list {
        margin-bottom: 0.15rem;
        height: 1.16rem;
        width: 100%;
        background-image: url('../../assets/heavily/dqbg.png');
        background-size: 100% 100%;
        padding: 0.16rem 0.35rem 0.18rem 0.28rem;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .electric-left {
          width: 0.74rem;
          height: 0.69rem;
          background-image: url('../../assets/heavily/dqbh.png');
          background-size: 100% 100%;
          display: flex;
          align-items: center;
          flex-direction: column;
          span {
            &:nth-child(1) {
              font-size: 0.24rem;
              font-weight: bold;
              color: #ffffff;
            }
            &:nth-child(2) {
              font-size: 14px;
              font-weight: 500;
              color: #ffffff;
            }
          }
        }
        .electric-middle {
          display: flex;
          flex-direction: column;
          div {
            display: flex;
            align-items: center;
            span {
              &:nth-child(1) {
                font-size: 0.14rem;
                font-weight: 400;
                color: #c4dbfb;
              }
              &:nth-child(2) {
                font-size: 24px;
                font-family: PangMenZhengDao;
                margin-left: 0.25rem;
                font-weight: 400;
                color: #6fdcff;
              }
            }
          }
        }
        @keyframes rotation {
          from {
            -webkit-transform: rotate(0deg);
          }
          to {
            -webkit-transform: rotate(360deg);
          }
        }
        // @-webkit-keyframes rotation {
        //   from {
        //     -webkit-transform: rotate(0deg);
        //   }
        //   to {
        //     -webkit-transform: rotate(360deg);
        //   }
        // }
        .Rotation {
          transform: rotate(360deg);
          -webkit-transform: rotate(360deg);
          animation: rotation 2s linear infinite;
          -moz-animation: rotation 2s linear infinite;
          -webkit-animation: rotation 2s linear infinite;
          -o-animation: rotation 2s linear infinite;
        }
        .electric-right {
          display: flex;
          flex-direction: column;
          align-items: center;
          width: 0.82rem;
          height: 0.82rem;
          justify-content: center;
          position: relative;
          .electric-right-bg {
            position: absolute;
            width: 100%;
            height: 100%;
          }
          span {
            &:nth-child(1) {
              font-size: 0.2rem;
              font-weight: 400;
              color: #6fdcff;
            }
            &:nth-child(2) {
              font-size: 0.12rem;
              font-weight: 400;
              color: #ffffff;
            }
          }
        }
        .electric-right-error {
          background-image: url('../../assets/heavily/yc.png');
        }
      }
    }
  }
  .motor-alarm-info {
    margin-top: 0.6rem;
    height: 3.06rem;
    align-items: center;
    font-size: 0.2rem;
    font-weight: 500;
    color: #fff;
    text-shadow:
      0 0 0.1rem #2e73d6,
      0 0 0.1rem #2e73d6;
    margin-bottom: 0.1rem;
    > title {
      display: flex;
      align-items: center;
      > span:nth-child(1) {
        display: inline-block;
        height: 0.25rem;
        margin-right: 0.2rem;
      }
      .more {
        font-size: 0.16rem;
        color: #3adbff;
        font-weight: 400;
        cursor: pointer;
      }
    }
    .motor-list {
      margin-top: 0.1rem;
      .motor-area {
        color: #3be6ff;
        font-size: 0.16rem;
        .motor-title {
          display: flex;
          width: 100%;
          align-items: center;
          justify-content: space-between;
          padding: 0 0.25rem;
          line-height: 0.38rem;
          span {
            display: inline-block;
            width: 0.8rem;
            &:nth-child(1) {
              width: 25%;
            }
            &:nth-child(3) {
              text-align: center;
              width: 25%;
            }
            &:nth-child(2) {
              width: 50%;
              text-align: center;
            }
          }
        }
        .content-list {
          height: 1.9rem;
          overflow-y: hidden;
          margin-bottom: 0.1rem;
          .single {
            display: flex;
            padding: 0 0.25rem;
            height: 0.38rem;
            line-height: 0.38rem;
            width: 100%;
            justify-content: space-between;
            &:nth-child(2n + 1) {
              background-color: #0f245e;
            }
            &:nth-child(2n) {
              background-color: rgba(4, 20, 50, 0.2);
            }
            span {
              color: white;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              img {
                width: 0.27rem;
                height: 0.27rem;
              }
              &:nth-child(1) {
                width: 25%;
              }
              &:nth-child(3) {
                width: 25%;
                text-align: center;
              }
              &:nth-child(2) {
                text-align: center;
                width: 50%;
              }
            }
          }
        }
      }
    }
  }
}
.bottom-tabs {
  width: 9rem;
  pointer-events: auto;
  position: absolute;
  bottom: 0.2rem;
  left: 45%;
  right: 50%;
  transform: translateX(-50%) translateY(-50%);
  z-index: 2;
}
.center-map {
  position: absolute;
  height: calc(1080px - 0.94rem);
  width: 100%;
  > div {
    width: 100%;
    height: 100%;
  }
}
.mapPotion {
  top: 1rem;
  right: 5.5rem;
}
.statistics {
  z-index: 20000;
  pointer-events: auto;
  position: absolute;
  top: 0.46rem;
  left: 0.8rem;
  width: 1.43rem;
  height: 4.15rem;
  display: flex;
  .statistics_data {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 4.1rem;
    padding-left: 14px;
    > div {
      div:nth-child(1) {
        font-size: 22px;
        font-family: DIN;
        font-weight: 500;
        color: #a9c8db;
        text-shadow: 0px 0px 7px rgba(0, 134, 227, 0.6);
      }
      div:nth-child(2) {
        font-size: 12px;
        font-family: Adobe Heiti Std;
        font-weight: normal;
        color: #7a8d9f;
      }
    }
  }
  > img {
    width: 0.5rem;
    height: 4.15rem;
  }
}
.left-side {
  z-index: 2;
  pointer-events: auto;
  font-size: 0.28rem;
  position: absolute;
  top: 0.46rem;
  left: 1rem;
  .title {
    line-height: 0.27rem;
    text-align-last: justify;
    font-size: 0.28rem;
    font-weight: bold;
    position: relative;
    &::after {
      position: absolute;
      content: '';
      width: 100%;
      height: 0.01rem;
      left: 0;
      bottom: -0.16rem;
      background-image: url('../../assets/heavily/<EMAIL>');
      background-size: 100% 100%;
    }
  }
  .flex-number {
    cursor: pointer;
    display: flex;
    margin-top: 0.18rem;
    > div {
      background-image: url('../../assets/<EMAIL>');
      background-size: 100% 100%;
      /*border: 1px solid RGBA(33, 109, 253, 1);*/
      /*background-color: RGBA(35, 211, 255, 0.6);*/
      font-size: 0.67rem;
      width: 0.62rem;
      height: 0.77rem;
      line-height: 0.77rem;
      text-align: center;
      font-family: '300-CAI978';
    }
  }
  .title-flex {
    display: flex;
    align-items: center;
    justify-content: space-between;
    > div {
      display: flex;
      font-size: 0.14rem;
      font-weight: 400;

      > div {
        width: 0.8rem;
        height: 0.28rem;
        line-height: 0.3rem;
        color: #0e8bff;
        // background: rgba(14, 139, 255, 0.32);
        // border: 1px solid rgba(14, 139, 255, 1);
        text-align: center;
        // clip-path: polygon(100% 0, 100% 100%, 12% 100%, 0% 70%, 0 0);
        background: url(../../assets/<EMAIL>);
        background-size: 100% 100%;
        background-repeat: no-repeat;
        cursor: pointer;
      }
      div.type-active {
        background: url(../../assets/<EMAIL>);
        background-size: 100% 100%;
        background-repeat: no-repeat;
        color: rgba(0, 234, 255, 1);
      }
      :nth-of-type(2) {
        margin-left: 0.1rem;
      }
    }
  }
}
.left-side-one {
  pointer-events: auto;
  z-index: 2;
  font-size: 0.28rem;
  position: absolute;
  padding: 0.1rem 0.2rem;
  top: 2.6rem;
  left: 1rem;
  width: 2.8rem;
  height: 2rem;
  background-color: rgba(3, 31, 93, 0.3);
  .title {
    display: flex;
    position: relative;
    justify-content: space-between;
    font-size: 0.14rem;
    padding: 0.1rem 0;
    cursor: pointer;
    span {
      &:first-child {
        display: inline-block;
        width: 70%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      &:last-child {
        display: inline-block;
        width: 30%;
      }
    }
    img {
      width: 0.2rem;
      height: 0.2rem;
      cursor: pointer;
      position: absolute;
      top: -0.1rem;
      right: -0.2rem;
    }
  }
  /*background-image: url('../../assets/kqbt.png');*/
  /*background-repeat: no-repeat;*/
  /*background-size: 100% 100%;*/
}
.left-side-two {
  pointer-events: auto;
  position: absolute;
  z-index: 2;
  bottom: 0.67rem;
  left: 0.67rem;
  width: 3.89rem;
  height: 2.6rem;
  .title {
    font-size: 0.2rem;
    font-family: Source Han Sans CN;
    font-weight: 500;
    text-shadow:
      0 0 5px blue,
      0 0 5px blue;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-right: 0.2rem;
    > .see-more {
      font-size: 0.16rem;
      color: rgba(58, 219, 255, 1);
      text-shadow: none;
      cursor: pointer;
    }
  }
  .vidoe-area {
    width: 3.8rem;
    height: 2.12rem;
    position: relative;
    span {
      position: absolute;
      top: 0.1rem;
      right: 0.1rem;
    }
    .title1 {
      z-index: 2;
      left: 0.1rem;
    }
    .no-more {
      width: 3.8rem;
      height: 2.12rem;
      text-align: center;
      line-height: 2.12rem;
      font-size: 0.2rem;
      background-image: url('../../assets/heavily-polluted-enterprise/<EMAIL>');
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
  }
}
.right-side-video {
  margin-top: 0.2rem;
  pointer-events: auto;
  width: 3.89rem;
  height: 2.6rem;
  .title {
    font-size: 0.2rem;
    font-family: Source Han Sans CN;
    font-weight: 500;
    text-shadow:
      0 0 5px blue,
      0 0 5px blue;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-right: 0.2rem;
    > .see-more {
      font-size: 0.16rem;
      color: rgba(58, 219, 255, 1);
      text-shadow: none;
      cursor: pointer;
    }
  }
  .video-area-text {
    width: 3.8rem;
    height: 2.12rem;
    line-height: 2.12rem;
    text-align: center;
    font-size: 0.18rem;
  }
  .vidoe-area {
    width: 3.8rem;
    height: 2.56rem;
    box-sizing: border-box;
    padding: 0.19rem 0.17rem 0.16rem 0.19rem;
    background-image: url('../../assets/heavily/<EMAIL>');
    background-size: 100% 100%;
    position: relative;
    .tilte {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-bottom: 0.05rem;
      border-bottom: 0.01rem solid rgba(11, 121, 180, 0.6);
      margin-bottom: 0.1rem;
      > div {
        display: flex;
        align-items: center;
        .circle {
          display: inline-block;
          width: 5px;
          height: 5px;
          background: #11f7da;
          border-radius: 50%;
          margin-right: 0.05rem;
        }
        span {
          color: #11f7da;
          font-size: 0.12rem;
        }
      }
    }
    .title1 {
      z-index: 2;
      left: 0.1rem;
    }
    .no-more {
      width: 3.49rem;
      height: 1.86rem;
      text-align: center;
      line-height: 1.86rem;
      font-size: 0.2rem;
      background-image: url('../../assets/heavily-polluted-enterprise/<EMAIL>');
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
  }
}
.left-side-three {
  pointer-events: auto;
  position: absolute;
  z-index: 2;
  width: 2rem;
  height: 1.24rem;
  bottom: 0.86rem;
  left: 0.84rem;
  box-sizing: border-box;
  padding: 0.35rem;
  background-image: url('../../assets/heavily/<EMAIL>');
  background-size: 100% 100%;
  .list {
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    &:last-child {
      margin-top: 0.24rem;
    }
    .circle {
      width: 0.2rem;
      height: 0.2rem;
      display: inline-flex;
      border: 1px solid #97b6e4;
      justify-content: center;
      border-radius: 50%;
      position: relative;
      align-items: center;
      .active {
        width: 0.1rem;
        height: 0.1rem;
        display: inline-block;
        background: #2afff8;
        border-radius: 50%;
      }
    }
  }
}
.left-side-four {
  pointer-events: auto;
  position: absolute;
  z-index: 2;
  width: 2rem;
  height: 1.24rem;
  bottom: 0.86rem;
  left: 2.45rem;
  box-sizing: border-box;
  padding: 0.25rem 0.2rem;
  background-image: url('../../assets/heavily/<EMAIL>');
  background-size: 100% 100%;
  .list {
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    &:not(:first-child) {
      margin-top: 0.06rem;
    }
    .circle {
      width: 0.2rem;
      height: 0.2rem;
      display: inline-flex;
      border: 1px solid #97b6e4;
      justify-content: center;
      border-radius: 50%;
      position: relative;
      align-items: center;
      .active {
        width: 0.1rem;
        height: 0.1rem;
        display: inline-block;
        background: #2afff8;
        border-radius: 50%;
      }
    }
  }
}
.center-searchs {
  z-index: 2;
  position: absolute;
  pointer-events: auto;
  top: 0.8rem;
  right: 50%;
  display: flex;
  align-items: center;
  width: 7.5rem;
  height: 0.6rem;
}
.center-search {
  z-index: 2;
  position: absolute;
  pointer-events: auto;
  top: 0.5rem;
  right: 50%;
  display: flex;
  align-items: center;
  width: 7.5rem;
  height: 0.6rem;
  margin-right: -3.5rem;
  .left {
    width: 1.8rem;
    height: 0.48rem;
    background-image: url('../../assets/heavily/<EMAIL>');
    background-size: 100% 100%;
    margin-right: 0.1rem;
  }
  .left1 {
    width: 1.8rem;
    height: 0.48rem;
    background-image: url('../../assets/heavily/<EMAIL>');
    background-size: 100% 100%;
    margin-right: 0.1rem;
  }
  .right {
    display: flex;
    align-items: center;
    width: 4.06rem;
    height: 0.48rem;
    background-image: url('../../assets/heavily/<EMAIL>');
    background-size: 100% 100%;
    /*background: rgba(5, 7, 95, 0.7);*/
    /*border: 1px solid #034fa8;*/
    input {
      width: 100%;
      margin-left: 0.25rem;
      // margin-right: 0.25rem;
      background: transparent;
      border: none;
      outline: none;
      font-size: 0.2rem;
    }
    input::-webkit-input-placeholder {
      color: #ffffff;
    }
    input::-moz-placeholder {
      /* Mozilla Firefox 19+ */
      color: #ffffff;
    }
    input:-moz-placeholder {
      /* Mozilla Firefox 4 to 18 */
      color: #ffffff;
    }
    input:-ms-input-placeholder {
      /* Internet Explorer 10-11 */
      color: #ffffff;
    }
  }
  .icon {
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(5, 7, 95, 0.7);
    border: 1px solid #034fa8;
    border-left: none;
    height: 0.48rem;
    width: 0.8rem;
    img {
      cursor: pointer;
    }
  }
}
table {
  width: 100%;
  font-size: 0.14rem;
  border: 1px solid #0b8dd3;
  border-collapse: collapse;
  td {
    padding: 0.07rem 0.1rem;
    /*text-align: center;*/
  }
  td:nth-child(odd) {
    width: 1rem;
    background: #0f245e;
    text-align: center;
  }
}
.online {
  padding: 0.02rem 0.1rem;
  border-radius: 5px;
  background: #1d8502;
}
.notOnline {
  padding: 0.02rem 0.1rem;
  border-radius: 5px;
  background: #e80e0e;
}
.warning {
  padding: 0.02rem 0.1rem;
  border-radius: 5px;
  background: #c88002;
}
.green {
  display: inline-block;
  width: 6px;
  height: 6px;
  background: #4ee01b;
  border-radius: 50%;
  margin-right: 0.05rem;
}
.red {
  display: inline-block;
  width: 6px;
  height: 6px;
  background: #e80e0e;
  border-radius: 50%;
  margin-right: 0.05rem;
}
.common-select {
  display: flex;
  justify-content: space-between;
  .ant-select-selection {
    display: flex;
    justify-content: center;
    width: 1.5rem;
    height: 0.3rem;
    // background: rgba(14, 139, 255, 0.32);
    border: none;
    border-radius: unset;
    // clip-path: polygon(100% 0, 100% 100%, 12% 100%, 0% 70%, 0 0);
    background: url(../../assets/<EMAIL>);
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }
  .ant-select-selection-selected-value {
    color: rgba(0, 234, 255, 1);
    font-size: 0.17rem;
  }

  .ant-select-focused .ant-select-selection,
  .ant-select-selection:focus,
  .ant-select-selection:active {
    border-color: #40a9ff;
    border-right-width: 0 !important;
    outline: 0;
    box-shadow: none;
  }
}
.common-title {
  width: 3.9rem;
  position: relative;
  .type-list {
    display: flex;
    > div {
      width: 0.88rem;
      height: 0.3rem;
      border-bottom: 1px solid #9aa1b1;
      color: #9aa1b1;
      margin-right: 0.17rem;
      cursor: pointer;
      text-align: center;
      line-height: 0.3rem;
    }
    > .active {
      background: #083881;
      color: #fff;
      border-bottom-color: #00eaff;
    }
  }
  > .table-thead {
    > .tr {
      display: flex;
      color: rgba(59, 230, 255, 1);
      font-size: 0.16rem;
      > .th {
        text-align: center;
      }
      > .th:first-child {
        width: 30%;
      }
      > .th:nth-child(2) {
        width: 40%;
      }
      > .th:last-child {
        width: 30%;
      }
    }
  }
  > .table-tbody {
    height: 1.9rem;
    .tr:nth-child(odd) {
      background: rgba(15, 36, 94, 1);
    }
    .tr {
      display: flex;
      height: 0.38rem;
      align-items: center;
      padding: 0 0.1rem;
      > div {
        text-align: center;
      }
      > div:first-child {
        width: 30%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      > div:nth-child(2) {
        width: 40%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      > div:last-child {
        width: 30%;
      }
    }
  }
  .real-table {
    td {
      padding: 0.05rem 0.01rem;
      text-align: center;
      height: 0.3rem;
      &:nth-child(1) {
        min-width: 1.2rem !important;
      }
    }
    thead {
      td {
        color: #3be6ff;
      }
    }
  }
}
.pollutant-table {
  width: 100%;
  text-align: center;
  font-weight: normal;
  border: 1px solid rgba(11, 141, 211, 1);
  thead {
    background: rgba(15, 56, 171, 1);
    > tr {
      height: 0.36rem;
    }
  }
  tbody {
    tr {
      height: 0.2rem;
      td:nth-of-type(odd) {
        background: #0f245e;
      }
      td {
        padding: 4px 8px !important;
      }
    }
  }
}
</style>
<style lang="less">
.ant-calendar {
  background-color: #012474;
  border: none;
}
.ant-calendar-picker-container,
.ant-calendar-header .ant-calendar-year-select,
.ant-calendar-date,
.ant-calendar-header .ant-calendar-month-select {
  color: #fff !important;
}

.ant-calendar-header {
  border-bottom: none;
}
.ant-calendar-footer {
  border-top: none;
}
.ant-calendar-input-wrap,
.ant-calendar-input {
  border-bottom: none;
  background-color: transparent;
  color: #fff;
}
.ant-calendar-disabled-cell .ant-calendar-date {
  background-color: transparent;
}
.ant-calendar-disabled-cell .ant-calendar-date:hover {
  background-color: #bae7ff;
}
.ant-calendar-today .ant-calendar-date,
.ant-calendar-date:hover {
  color: #1890ff !important;
}
.hasDataClass {
  position: relative;
  &::after {
    content: '';
    height: 5px;
    width: 5px;
    border-radius: 50%;
    background-color: #fc5531;
    position: absolute;
    bottom: -2px;
    left: 9px;
  }
}

.motor-info,
.merchants-message {
  .select-main {
    width: 1.96rem;
    height: 0.32rem;
    display: flex;
    justify-content: space-between;
    .ant-select-selection,
    .ant-select-focused,
    .ant-calendar-picker-input {
      width: 100%;
      border: none !important;
      outline: none !important;
      border-radius: unset;
      background: url(../../assets/<EMAIL>);
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
    .ant-select-selection-selected-value {
      color: rgba(0, 234, 255, 1);
    }
    .ant-calendar-picker-input,
    .ant-calendar-picker-clear {
      color: rgba(0, 234, 255, 1) !important;
    }
    .ant-select-focused .ant-select-selection,
    .ant-calendar-picker-input,
    .ant-select-selection:focus,
    .ant-select-selection:active {
      border-color: transparent !important;
      border-right-width: 0 !important;
      outline: 0 !important;
      box-shadow: none !important;
      border: none !important;
    }
    .ant-select-selection__rendered {
      outline: none;
    }
  }
  .select-main1 {
    width: 1.96rem;
    height: 0.32rem;
    display: flex;
    justify-content: space-between;
    .ant-select-selection,
    .ant-select-focused,
    .ant-calendar-picker-input {
      width: 100%;
      border: none !important;
      outline: none !important;
      border-radius: unset;
      background: url(../../assets/<EMAIL>);
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
    .ant-select-selection-selected-value {
      color: rgba(0, 234, 255, 1);
    }
    .ant-calendar-picker-input,
    .ant-calendar-picker-clear {
      color: rgba(0, 234, 255, 1) !important;
    }
    .datePickerClass {
      .ant-calendar {
        background-color: #012474 !important;
        border: none !important;
      }
      .ant-calendar-picker-clear,
      .ant-calendar-picker-icon {
        color: #00eaff !important;
        background-color: transparent !important;
      }
    }
    .ant-select-focused .ant-select-selection,
    .ant-calendar-picker-input,
    .ant-select-selection:focus,
    .ant-select-selection:active {
      border-color: transparent !important;
      border-right-width: 0 !important;
      outline: 0 !important;
      box-shadow: none !important;
      border: none !important;
    }
    .ant-select-selection__rendered {
      outline: none;
    }
  }
}
.center-search .left,
.center-search .left1,
.center-searchNew .left,
.center-searchNew .left1 {
  .ant-select-selection__rendered {
    height: 0.48rem !important;
    line-height: 0.48rem !important;
    color: #ffffff !important;
    font-size: 0.2rem !important;
  }
  .ant-select-selection,
  .ant-select-selection--single {
    height: 0.48rem !important;
  }
  .select-main1 {
    height: 0.48rem !important;
    width: 4.5rem !important;
    display: flex;
    justify-content: space-between;
    .ant-select-selection,
    .ant-select-focused,
    .ant-calendar-picker-input {
      width: 100%;
      border: none !important;
      outline: none !important;
      border-radius: unset;
      background: url(../../assets/heavily/<EMAIL>);
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
    .ant-select-selection-selected-value {
      color: #ffffff;
    }
    .ant-select-focused .ant-select-selection,
    .ant-calendar-picker-input,
    .ant-select-selection:focus,
    .ant-select-selection:active {
      border-color: transparent !important;
      border-right-width: 0 !important;
      outline: 0 !important;
      box-shadow: none !important;
      border: none !important;
      height: 0.48rem !important;
    }
    .ant-select-selection__rendered {
      outline: none;
    }
  }
  .select-main {
    height: 0.48rem !important;
    width: 1.8rem !important;
    display: flex;
    justify-content: space-between;
    .ant-select-selection,
    .ant-select-focused,
    .ant-calendar-picker-input {
      width: 100%;
      border: none !important;
      outline: none !important;
      border-radius: unset;
      background: url(../../assets/heavily/<EMAIL>);
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
    .ant-select-selection-selected-value {
      color: #ffffff;
    }
    .ant-select-focused .ant-select-selection,
    .ant-calendar-picker-input,
    .ant-select-selection:focus,
    .ant-select-selection:active {
      border-color: transparent !important;
      border-right-width: 0 !important;
      outline: 0 !important;
      box-shadow: none !important;
      border: none !important;
      height: 0.48rem !important;
    }
    .ant-select-selection__rendered {
      outline: none;
    }
  }
  .select-main2 {
    height: 0.48rem !important;
    width: 1.8rem !important;
    display: flex;
    justify-content: space-between;
    .ant-calendar-picker-clear,
    .ant-calendar-picker-icon {
      color: #00eaff !important;
      background-color: transparent !important;
    }
    .ant-calendar-selected-day {
      background-color: #bae7ff !important;
    }
    .ant-select-selection,
    .ant-select-focused,
    .ant-calendar-picker-input {
      width: 100%;
      border: none !important;
      outline: none !important;
      border-radius: unset;
      background: url(../../assets/heavily/<EMAIL>);
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
    .ant-select-selection-selected-value {
      color: #ffffff;
    }
    .ant-input {
      color: #fff;
      font-size: 0.2rem;
    }
    .ant-select-focused .ant-select-selection,
    .ant-calendar-picker-input,
    .ant-select-selection:focus,
    .ant-select-selection:active {
      border-color: transparent !important;
      border-right-width: 0 !important;
      outline: 0 !important;
      box-shadow: none !important;
      border: none !important;
      height: 0.48rem !important;
    }
    .ant-select-selection__rendered {
      outline: none;
    }
  }
}
.center-search .left1,
.center-searchNew .left1 {
  .ant-select-selection__rendered {
    height: 0.48rem !important;
    line-height: 0.48rem !important;
    color: #ffffff !important;
    font-size: 0.2rem !important;
  }
  .ant-select-selection,
  .ant-select-selection--single {
    height: 0.48rem !important;
  }
}
.center-searchNew {
  // z-index: 2;
  // position: absolute;
  pointer-events: auto;
  // top: 0rem !important;
  // right: 50% !important;
  display: flex;
  align-items: center;
  // width: 7rem;
  height: 0.5rem;
  margin-left: 0.1rem;
  // margin-right: -3.5rem;
  .left {
    width: 1.7rem;
    height: 0.48rem;
    background-image: url('../../assets/heavily/<EMAIL>');
    background-size: 100% 100%;
    margin-right: 0.1rem;
  }
  .left1 {
    width: 1.7rem;
    height: 0.48rem;
    background-image: url('../../assets/heavily/<EMAIL>');
    background-size: 100% 100%;
    margin-right: 0.1rem;
  }
  .right {
    display: flex;
    align-items: center;
    // width: 4.06rem;
    width: 3.76rem;
    height: 0.43rem;
    background-image: url('../../assets/heavily/<EMAIL>');
    background-size: 100% 100%;
    /*background: rgba(5, 7, 95, 0.7);*/
    /*border: 1px solid #034fa8;*/
    input {
      width: 100%;
      margin-left: 0.25rem;
      // margin-right: 0.25rem;
      background: transparent;
      border: none;
      outline: none;
      font-size: 0.17rem;
      margin-top: 0.04rem;
    }
    input::-webkit-input-placeholder {
      color: #ffffff;
      // font-size: 0.17rem;
    }
    input::-moz-placeholder {
      /* Mozilla Firefox 19+ */
      color: #ffffff;
    }
    input:-moz-placeholder {
      /* Mozilla Firefox 4 to 18 */
      color: #ffffff;
    }
    input:-ms-input-placeholder {
      /* Internet Explorer 10-11 */
      color: #ffffff;
      font-size: 0.17rem;
    }
  }
  .icon {
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(5, 7, 95, 0.7);
    border: 1px solid #034fa8;
    border-left: none;
    height: 0.43rem;
    // width: 0.8rem;
    width: 0.6rem;
    img {
      cursor: pointer;
    }
  }
}

.map-control {
  .amap-marker-label {
    position: absolute;
    z-index: 2;
    // width: 2rem;
    border: 1px solid transparent;
    // background-image: url("../../assets/szjcd.png");
    // background-repeat: no-repeat;
    // background-size: 100% 100%;
    background-color: transparent;
    white-space: nowrap;
    cursor: default;
    padding: 0.03rem;
    font-size: 0.12rem;
    line-height: 0.14rem;
    .info {
      font-size: 0.12rem;
      font-family: Source Han Sans CN;
      font-weight: 500;
      color: rgba(255, 255, 255, 1);
      text-align: center;
    }
  }
  .amap-controlbar-zoom {
    display: none;
  }
  .amap-luopan,
  .amap-luopan-bg {
    background: url(../../assets/luopan.png) -44px -60px no-repeat;
  }
  .amap-compass {
    background: url(../../assets/luopan.png) -462px -52px no-repeat;
  }
  .amap-pointers {
    background: url(../../assets/luopan.png) -562px -52px no-repeat;
  }
  .amap-pitchDown,
  .amap-pitchUp {
    background: url(../../assets/luopan.png) -605px -98px no-repeat;
  }
  .amap-rotateLeft,
  .amap-rotateRight {
    background: url(../../assets/luopan.png) -603px -154px no-repeat;
  }
  .amap-rotateLeft,
  .amap-rotateRight {
    background: url(../../assets/luopan.png) -603px -154px no-repeat;
  }
}

.el-date-table th {
  border: none !important;
  color: #bfd6e4 !important;
}
.el-picker-panel,
.el-picker-panel__footer {
  background: rgba(0, 59, 109, 1) !important;
  border-color: rgba(0, 59, 109, 1) !important;
  color: #bfd6e4 !important;
}

.el-picker-panel__link-btn {
  border-color: rgb(24, 105, 226) !important;
  background: rgb(91, 147, 231) !important;
  color: #cfe2ec !important;
}

.el-picker-panel__icon-btn {
  color: #fff !important;
}
// .el-popper .popper__arrow::after {
//   border-color: rgba(0, 59, 109, 0) !important;
// }
// .el-popper .popper__arrow{
//   border-color: rgba(0, 59, 109, 1) !important;
// }
</style>
<template>
  <div class="common-main">
    <template v-if="typeNum === 11">
      <div class="topTab" v-if="typeNum === 11">
        <div
          class="air_btn"
          :class="analysisType === 'air' ? 'btn_active' : ''"
          style="cursor: pointer"
          @click="
            analysisType = 'air'
            searchWords = ''
            keywords1 = ''
            stationTypeKey = ''
          "
        >
          大气环境综合分析
        </div>
        <div class="water_btn" style="cursor: not-allowed">
          <div class="water_btn" :class="analysisType === 'water' ? 'btn_active' : ''" style="cursor: pointer" @click="analysisType = 'water'">
            水环境综合分析
          </div>
        </div>
        <section class="center-searchNew" v-if="typeNum === 11 && analysisType === 'air'">
          <div class="right">
            <input type="text" :placeholder="'请输入：(公司/工地)名称'" v-model="searchWords" @keyup.enter="search" />
            <a-icon type="close-circle" style="font-size: 0.2rem; cursor: pointer; margin-right: 0.15rem" v-if="searchWords != ''" @click="clearSearch" />
          </div>
          <div class="icon">
            <img src="@/assets/search.png" alt="" @click="search" style="width: 0.25rem; height: 0.26rem" />
          </div>
        </section>
      </div>

      <analysisAir
        v-if="typeNum === 11 && analysisType === 'air'"
        :keywords="keywords1"
        :currIndex="currIndex"
        @clean="cleanKeyword"
        :stationTypeKey="stationTypeKey"
      ></analysisAir>
      <analysisWater v-if="typeNum === 11 && analysisType === 'water'"></analysisWater>
    </template>
    <template v-else>
      <template v-if="[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 13, 99].includes(currIndex)">
        <div
          :class="{
            'container-bg': currIndex != 10,
            'container-bg1': currIndex == 10,
          }"
        >
          <section class="left-side" v-if="![10, 11, 8].includes(currIndex)">
            <div class="title">
              {{ titleList[currIndex] }}
            </div>
            <div class="flex-number">
              <div>{{ currCompanyListLength[0] || 0 }}</div>
              <div>{{ currCompanyListLength[1] || 0 }}</div>
              <div>{{ currCompanyListLength[2] || 0 }}</div>
              <div v-if="currCompanyListLength.length > 3">{{ currCompanyListLength[3] || 0 }}</div>
            </div>
          </section>
          <section class="statistics" v-if="[8].includes(currIndex)">
            <img src="@/assets/noise/<EMAIL>" alt="" />
            <div class="statistics_data">
              <div v-for="(item, index) in statData" :key="index">
                <div>{{ item.num }}</div>
                <div>{{ item.name }}</div>
              </div>
            </div>
          </section>
          <section class="left-side-two" v-if="false">
            <div class="title">
              <span>实时监控</span>
              <span class="see-more" @click="jumpMoreVideo">查看更多>></span>
            </div>
            <div class="sub-title">
              <img src="@/assets/biaoti.png" style="width: 100%; margin-bottom: 0.2rem" />
            </div>
            <div v-if="currIndex === 1" class="vidoe-area">
              <div class="tilte">
                <span>{{ title }}</span>
                <div>
                  <span class="circle"></span>
                  <span>在线</span>
                </div>
              </div>
              <template v-for="(item, index) in currCompanyList">
                <EZUIKitJs
                  v-if="ezopen && item.printFactoryId === currId"
                  :key="index"
                  :id="currId"
                  :width="width1"
                  :monitorDeviceState="monitorDeviceState"
                  :height="height1"
                  :accessToken="accessToken"
                  :url="ezopen"
                />
              </template>
            </div>
            <div v-if="currIndex === 2" class="vidoe-area">
              <span class="title1">{{ title }}</span>
              <template v-for="(item, index) in videoList">
                <livePlayer
                  v-if="item.garageId === currId"
                  :type="currIndex"
                  :key="index"
                  :playerId="index"
                  :vidoeUrl="item.liveUrl"
                  :snapUrl="item.snapUrl"
                  :online="item.online"
                  :outPage="outPage"
                  style="width: 380px; height: 2.12rem"
                />
              </template>

              <div v-if="!ezopen" class="no-more" />
            </div>
          </section>
          <section v-if="![5, 6, 7, 8, 9, 10, 11, 13].includes(currIndex)" class="left-side-three">
            <div class="list" @click="handleOnline(2)">
              <span>在线</span>
              <span>{{ currSite.online }}个</span>
              <span class="circle"><span :class="{ active: online.indexOf(2) > -1 ? 'active' : '' }"></span></span>
            </div>
            <div class="list" @click="handleOnline(3)">
              <span>离线</span>
              <span>{{ currSite.Offline }}个</span>
              <span class="circle"><span :class="{ active: online.indexOf(3) > -1 ? 'active' : '' }"></span></span>
            </div>
          </section>
          <section v-if="[6, 7, 8, 9, 13].includes(currIndex)" class="left-side-four">
            <div class="list" @click="handleOnlineF(1)">
              <span> 正常 </span>
              <span>{{ currSite.onlineCount }}个</span>
              <span class="circle"><span :class="{ active: online.indexOf(1) > -1 ? 'active' : '' }"></span></span>
            </div>
            <div class="list" @click="handleOnlineF(2)">
              <span> 预警 </span>
              <span>{{ currSite.alarmCount }}个</span>
              <span class="circle"><span :class="{ active: online.indexOf(2) > -1 ? 'active' : '' }"></span></span>
            </div>
            <div class="list" @click="handleOnlineF(3)">
              <span> 离线 </span>
              <span style="font-family: PingFangSC-Regular">{{ currSite.offlineCount }}个</span>
              <span class="circle"><span :class="{ active: online.indexOf(3) > -1 ? 'active' : '' }"></span></span>
            </div>
          </section>
          <section v-if="[1, 2, 3, 4].includes(currIndex)" class="right-side">
            <div class="common-title merchants-message pul-randa-r2l">
              <div class="title">
                <span>商户信息</span>
                <a-select v-if="currIndex === 1" v-model="currCompanyId" @change="changeCompany" class="select-main">
                  <a-icon slot="suffixIcon" type="caret-down" style="color: rgba(0, 234, 255, 1); width: 0.23rem; height: 0.11rem" />
                  <a-select-option :value="item.printFactoryId" v-for="(item, index) in currCompanyList" :key="index">
                    {{ item.printFactoryName }}</a-select-option
                  >
                </a-select>
                <a-select v-if="currIndex === 2" v-model="currCompanyId" @change="changeCompany" class="select-main">
                  <a-icon slot="suffixIcon" type="caret-down" style="color: rgba(0, 234, 255, 1); width: 0.23rem; height: 0.11rem" />
                  <a-select-option :value="item.garageId" v-for="(item, index) in currCompanyList" :key="index"> {{ item.garageName }}</a-select-option>
                </a-select>
                <a-select v-if="currIndex === 3" v-model="currCompanyId" @change="changeCompany" class="select-main">
                  <a-icon slot="suffixIcon" type="caret-down" style="color: rgba(0, 234, 255, 1); width: 0.23rem; height: 0.11rem" />
                  <a-select-option :value="item.id" v-for="(item, index) in currCompanyList" :key="index"> {{ item.restaurantName }}</a-select-option>
                </a-select>
                <a-select v-if="currIndex === 4" v-model="currCompanyId" @change="changeCompany" class="select-main">
                  <a-icon slot="suffixIcon" type="caret-down" style="color: rgba(0, 234, 255, 1); width: 0.23rem; height: 0.11rem" />
                  <a-select-option :value="item.gasId" v-for="(item, index) in currCompanyList" :key="index"> {{ item.gasName }}</a-select-option>
                </a-select>
              </div>
              <div class="sub-title">
                <img src="@/assets/biaoti.png" alt />
              </div>
              <div class="content">
                <table border>
                  <tbody>
                    <tr>
                      <td>商户名称</td>
                      <td :colspan="currIndex === 4 ? 0 : 3" style="height: 0.2rem">
                        {{
                          currIndex === 1
                            ? currCompany.printFactoryName
                            : currIndex === 2
                              ? currCompany.garageName
                              : currIndex === 4
                                ? currCompany.gasName
                                : currCompany.restaurantName
                        }}
                      </td>
                      <td v-if="currIndex === 4">归属单位</td>
                      <td v-if="currIndex === 4">中石油</td>
                    </tr>
                    <tr>
                      <td>企业编码</td>
                      <td :colspan="3">{{ currCompany.enterpriseCode }}</td>
                    </tr>
                    <tr>
                      <td>所在街道</td>
                      <td :colspan="3">{{ currCompany.streetName }}</td>
                    </tr>
                    <tr>
                      <td>详细地址</td>
                      <td
                        :colspan="3"
                        v-if="
                          currIndex === 1
                            ? currCompany && currCompany.printFactoryAddress && currCompany.printFactoryAddress.length < 21
                            : currIndex === 2
                              ? currCompany && currCompany.garageAddress && currCompany.garageAddress.length < 21
                              : currIndex === 4
                                ? (currCompany && currCompany.address && currCompany.address.length) < 21
                                : currCompany && currCompany.restaurantAddress && currCompany.restaurantAddress.length < 21
                        "
                      >
                        {{
                          currIndex === 1
                            ? currCompany.printFactoryAddress
                            : currIndex === 2
                              ? currCompany.garageAddress
                              : currIndex === 4
                                ? currCompany.address
                                : currCompany.restaurantAddress
                        }}
                      </td>
                      <el-tooltip
                        v-else
                        class="item"
                        effect="dark"
                        :content="
                          currIndex === 1
                            ? currCompany.printFactoryAddress
                            : currIndex === 2
                              ? currCompany.garageAddress
                              : currIndex === 4
                                ? currCompany.address
                                : currCompany.restaurantAddress
                        "
                        placement="top"
                      >
                        <td :colspan="3">
                          {{
                            currIndex === 1
                              ? currCompany.printFactoryAddress
                              : currIndex === 2
                                ? currCompany.garageAddress
                                : currIndex === 4
                                  ? currCompany.address
                                  : currCompany.restaurantAddress
                          }}
                        </td>
                      </el-tooltip>
                    </tr>
                    <tr>
                      <td>联系人</td>
                      <td>
                        {{ currIndex !== 4 ? currCompany.contactPerson : currCompany.contactUser }}
                      </td>
                      <td>联系电话</td>
                      <td>
                        {{ currIndex === 4 ? currCompany.mobile : currCompany.phoneNumber }}
                      </td>
                    </tr>
                    <tr v-if="currIndex === 4">
                      <td>设备状态</td>
                      <td colspan="3">
                        <span
                          :class="{
                            green: currCompany.online ? 'green' : '',
                            red: currCompany.online ? '' : 'red',
                          }"
                        ></span>
                        <span
                          :style="{
                            color: currCompany.online ? 'white' : 'red',
                          }"
                          >{{ currCompany.online ? '在线' : '离线' }}</span
                        >
                      </td>
                    </tr>
                    <tr v-if="currIndex === 4">
                      <td>油罐状态</td>
                      <td>
                        <span
                          :class="{
                            online: currCompany.tankState === 0 ? 'online' : '',
                            notOnline: currCompany.tankState === 2 ? 'notOnline' : '',
                            warning: currCompany.tankState === 1 ? 'warning' : '',
                          }"
                          >{{ currCompany.tankState === 0 ? '正常' : currCompany.tankState === 1 ? '预警' : '报警' }}</span
                        >
                      </td>
                      <td>油气浓度状态</td>
                      <td>
                        <span
                          :class="{
                            online: currCompany.gasConcentrationState === 0 ? 'online' : '',
                            notOnline: currCompany.gasConcentrationState === 1 ? 'notOnline' : '',
                          }"
                          >{{ currCompany.gasConcentrationState === 0 ? '正常' : '报警' }}</span
                        >
                      </td>
                    </tr>
                    <tr v-if="currIndex === 3">
                      <td>监测设备</td>
                      <td colspan="3">
                        <span
                          :class="{
                            online: currCompany.monitorDeviceState ? 'online' : '',
                            notOnline: !currCompany.monitorDeviceState ? 'notOnline' : '',
                          }"
                          >{{ currCompany.monitorDeviceState ? '在线' : '离线' }}</span
                        >
                      </td>
                      <!-- <td>净化设备</td>
                  <td>
                    <span
                      :class="{
                        online: currCompany.purifyDeviceState ? 'online' : '',
                        notOnline: !currCompany.purifyDeviceState
                          ? 'notOnline'
                          : ''
                      }"
                      >{{
                        currCompany.purifyDeviceState ? '在线' : '离线'
                      }}</span
                    >
                  </td> -->
                    </tr>
                    <tr v-if="currIndex === 1 || currIndex === 2">
                      <td>{{ currIndex === 3 ? '风机状态' : '监测设备' }}</td>
                      <td>
                        <span
                          :class="{
                            online: (currIndex === 3 ? currCompany.fanState : currCompany.monitorDeviceState) ? 'online' : '',
                            notOnline: (currIndex === 3 ? !currCompany.fanState : !currCompany.monitorDeviceState) ? 'notOnline' : '',
                          }"
                          >{{ currCompany.fanState || currCompany.monitorDeviceState ? '在线' : '离线' }}</span
                        >
                      </td>
                      <td>{{ currIndex === 3 ? '净化器状态' : '净化设备' }}</td>
                      <td>
                        <span
                          :class="{
                            online: currCompany.purifyDeviceState ? 'online' : '',
                            notOnline: !currCompany.purifyDeviceState ? 'notOnline' : '',
                          }"
                          >{{ currCompany.purifyDeviceState ? '在线' : '离线' }}</span
                        >
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
            <div v-if="currIndex === 1 || currIndex === 2" class="common-title" :style="{ 'margin-top': currIndex === 1 ? '0.1rem' : '0.2rem' }">
              <div class="title">
                <span>实时监测</span>
                <span class="see-more" style="display: block" @click="jumpDetail">查看更多>></span>
              </div>
              <div class="sub-title">
                <img src="@/assets/biaoti.png" alt />
              </div>
              <table border class="real-table">
                <thead>
                  <tr>
                    <td>名称</td>
                    <td>数值</td>
                    <td>单位</td>
                    <td>在线状态</td>
                  </tr>
                </thead>
                <tbody v-if="currIndex === 1">
                  <tr>
                    <td>voc</td>
                    <td>{{ currCompany.vocConcentration }}</td>
                    <td>mg/m³</td>
                    <td rowspan="2">
                      <span
                        :class="{
                          online: currCompany.monitorDeviceState ? 'online' : '',
                          notOnline: currCompany.monitorDeviceState ? '' : 'notOnline',
                        }"
                        >{{ currCompany.monitorDeviceState ? '在线' : '离线' }}</span
                      >
                    </td>
                  </tr>
                  <tr>
                    <td>排放速率</td>
                    <td>{{ currCompany.emissionRate }}</td>
                    <td>kg/h</td>
                  </tr>
                  <tr>
                    <td>电压</td>
                    <td>{{ currCompany.voltage }}</td>
                    <td>v</td>
                    <td rowspan="7">
                      <span
                        :class="{
                          online: currCompany.purifyDeviceState ? 'online' : '',
                          notOnline: currCompany.purifyDeviceState ? '' : 'notOnline',
                        }"
                        >{{ currCompany.purifyDeviceState ? '在线' : '离线' }}</span
                      >
                    </td>
                  </tr>
                  <tr>
                    <td>电流</td>
                    <td>{{ currCompany.electricCurrent }}</td>
                    <td>A</td>
                  </tr>
                  <tr>
                    <td>有用功率</td>
                    <td>{{ currCompany.activePower }}</td>
                    <td>KW</td>
                  </tr>
                  <tr>
                    <td>无用功率</td>
                    <td>{{ currCompany.reactivePower }}</td>
                    <td>kvar</td>
                  </tr>
                  <tr>
                    <td>视在功率</td>
                    <td>{{ currCompany.inspectingPower }}</td>
                    <td>KVA</td>
                  </tr>
                  <tr>
                    <td>正向有功电能</td>
                    <td>{{ currCompany.positiveActiveEnergy }}</td>
                    <td>KWH</td>
                  </tr>
                  <tr v-if="false">
                    <td>反向有功电能</td>
                    <td>{{ currCompany.reverseActiveEnergy }}</td>
                    <td>KWH</td>
                  </tr>
                </tbody>
                <tbody v-if="currIndex === 2">
                  <tr>
                    <td>voc</td>
                    <td>{{ currCompany.vocConcentration }}</td>
                    <td>mg/m³</td>
                    <td rowspan="2">
                      <span
                        :class="{
                          online: currCompany.monitorDeviceState ? 'online' : '',
                          notOnline: currCompany.monitorDeviceState ? '' : 'notOnline',
                        }"
                        >{{ currCompany.monitorDeviceState ? '在线' : '离线' }}</span
                      >
                    </td>
                  </tr>
                  <tr>
                    <td>排放速率</td>
                    <td>
                      {{ currCompany.emissionRate ? currCompany.emissionRate : '' }}
                    </td>
                    <td>kg/h</td>
                  </tr>
                  <tr>
                    <td>总电流</td>
                    <td>
                      {{ currCompany.lastData ? currCompany.lastData.electronCurrent : '' }}
                    </td>
                    <td>A</td>
                    <td rowspan="4">
                      <span
                        :class="{
                          online: currCompany.purifyDeviceState ? 'online' : '',
                          notOnline: currCompany.purifyDeviceState ? '' : 'notOnline',
                        }"
                        >{{ currCompany.purifyDeviceState ? '在线' : '离线' }}</span
                      >
                    </td>
                  </tr>
                  <tr>
                    <td>总电压</td>
                    <td>
                      {{ currCompany.lastData ? currCompany.lastData.voltage : '' }}
                    </td>
                    <td>v</td>
                  </tr>
                  <tr>
                    <td>总有功功率</td>
                    <td>
                      {{ currCompany.lastData ? currCompany.lastData.pt : '' }}
                    </td>
                    <td>KW</td>
                  </tr>
                  <tr>
                    <td>总正向有功电能</td>
                    <td>
                      {{ currCompany.lastData ? currCompany.lastData.ept : '' }}
                    </td>
                    <td>KWH</td>
                  </tr>
                </tbody>
              </table>
              <!--        <div v-if="(type === 1 && VocData.vocyData.length) || (type === 2 && VocData.emissionyData.length)" class="type-list">-->
              <!--          <div :class="{'active': type === 1 ? 'active' : ''}" @click="changeType(1)">TVOCs</div>-->
              <!--          <div :class="{'active': type === 2 ? 'active' : ''}"  @click="changeType(2)">排放速率</div>-->
              <!--        </div>-->
              <!--        <VocLineChart v-if="(type === 1 && VocData.vocyData.length) || (type === 2 && VocData.emissionyData.length)" :VocData="VocData" width="100%" height="2rem" />-->
              <!--        <div v-else style="width: 100%;height: 2rem; font-size: 0.2rem;text-align: center;line-height: 2rem">暂无数据</div>-->
            </div>
            <div v-if="true" class="common-title" style="margin-top: 0.2rem; margin-bottom: 0.1rem">
              <div class="title">
                <span>超标告警</span>
              </div>
              <div class="sub-title">
                <img src="@/assets/biaoti.png" alt />
              </div>
              <div v-if="alarmList.length && (currIndex === 1 || currIndex === 2)" class="table-thead">
                <div class="tr">
                  <div class="th">商户名称</div>
                  <div class="th">告警内容</div>
                  <div class="th">发生时间</div>
                </div>
              </div>
              <swiper v-if="alarmList.length" :options="swiperOption" class="table-tbody">
                <swiper-slide class="tr" v-for="(item, index) in alarmList" :key="index">
                  <div :title="currIndex === 1 ? item.printFactoryName : item.garageName">
                    {{ currIndex === 1 ? item.printFactoryName : item.garageName }}
                  </div>
                  <div :title="currIndex === 1 ? item.printAlarmContent : item.garageAlarmContent">
                    {{ currIndex === 1 ? item.printAlarmContent : item.garageAlarmContent }}
                  </div>
                  <div>
                    {{ currIndex === 1 ? item.printAlarmTime : item.garageAlarmTime }}
                  </div>
                </swiper-slide>
              </swiper>
              <div v-else class="table-tbody" style="line-height: 1.9rem; font-size: 0.16rem; text-align: center">暂无告警信息</div>
            </div>
            <section
              style="display: none"
              class="right-side-video"
              v-if="currIndex === 1 || currIndex === 2"
              :style="{ 'margin-top': currIndex === 1 ? '0.1rem' : '0.2rem' }"
            >
              <div class="title">
                <span>实时监控</span>
                <span class="see-more" @click="jumpMoreVideo">查看更多>></span>
              </div>
              <div class="sub-title">
                <img src="@/assets/biaoti.png" alt style="width: 100%; margin-bottom: 0.2rem" />
              </div>
              <div v-if="currIndex === 1 && currCompanyList && currCompanyList.length" class="vidoe-area">
                <div class="tilte">
                  <span>{{ title }}</span>
                </div>
                <template v-for="(item, index) in currCompanyList">
                  <EZUIKitJs
                    v-if="ezopen && item.printFactoryId === currId"
                    :key="index"
                    :id="currId"
                    :width="width1"
                    :monitorDeviceState="monitorDeviceState"
                    :height="height1"
                    :accessToken="accessToken"
                    :url="ezopen"
                  />
                </template>
              </div>
              <div v-if="currIndex === 2 && currCompanyList && currCompanyList.length" class="vidoe-area">
                <div class="tilte">
                  <span>{{ title }}</span>
                  <div>
                    <span class="circle"></span>
                    <span>在线</span>
                  </div>
                </div>
                <template v-for="(item, index) in videoList">
                  <livePlayer
                    v-if="item.garageId === currId"
                    :type="currIndex"
                    :key="index"
                    :playerId="index"
                    :vidoeUrl="item.liveUrl"
                    :snapUrl="item.snapUrl"
                    :online="item.online"
                    :outPage="outPage"
                    style="width: 3.49rem; height: 1.86rem"
                  />
                </template>
                <div v-if="!ezopen" class="no-more" />
              </div>
              <div v-if="currCompanyList && !currCompanyList.length" class="video-area-text">暂无数据</div>
            </section>
            <div v-if="currIndex === 4" class="common-station-echarts">
              <div class="title">
                <span>实时监测</span>
                <span class="more" style="display: none" @click="jumpDetail">查看更多 >></span>
              </div>
              <div class="sub-title">
                <img src="@/assets/biaoti.png" alt />
              </div>
              <!-- <table
            v-if="currIndex === 3"
            border
            class="real-table"
            style="margin: 0.2rem 0"
          >
            <thead>
              <tr>
                <td>名称</td>
                <td>数值</td>
                <td>单位</td>
                <td>状态</td>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>油烟浓度</td>
                <td>{{ currCompany.concentration }}</td>
                <td>mg/m³</td>
                <td>
                  <span
                    :class="{
                      online: !currCompany.isAlarm ? 'online' : '',
                      notOnline: currCompany.isAlarm ? 'notOnline' : ''
                    }"
                    >{{ currCompany.isAlarm ? '超标' : '正常' }}</span
                  >
                </td>
              </tr>
            </tbody>
          </table>
          <table
            v-if="currIndex === 3"
            border
            class="real-table"
            style="margin: 0.2rem 0"
          >
            <thead>
              <tr>
                <td>名称</td>
                <td>电流</td>
                <td>单位</td>
                <td>状态</td>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>风机</td>
                <td>{{ currCompany.fanCurrent }}</td>
                <td>A</td>
                <td>
                  <span
                    :class="{
                      online: currCompany.fanState ? 'online' : '',
                      notOnline: currCompany.fanState ? '' : 'notOnline'
                    }"
                    >{{ currCompany.fanState ? '在线' : '离线' }}</span
                  >
                </td>
              </tr>
              <tr>
                <td>净化器</td>
                <td>{{ currCompany.purifierCurrent }}</td>
                <td>A</td>
                <td>
                  <span
                    :class="{
                      online: currCompany.purifyDeviceState ? 'online' : '',
                      notOnline: currCompany.purifyDeviceState
                        ? ''
                        : 'notOnline'
                    }"
                    >{{ currCompany.purifyDeviceState ? '在线' : '离线' }}</span
                  >
                </td>
              </tr>
            </tbody>
          </table> -->
              <table v-if="currIndex === 4" border class="real-table" style="margin: 0.2rem 0">
                <thead>
                  <tr>
                    <td>名称</td>
                    <td>数值</td>
                    <td>单位</td>
                    <td>状态</td>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>液阻压力</td>
                    <td>{{ currCompany.hydraulicPressure }}</td>
                    <td>Pa</td>
                    <td>-</td>
                  </tr>
                  <tr>
                    <td>密闭性</td>
                    <td>0</td>
                    <td>-</td>
                    <td>
                      <span
                        :class="{
                          online: currCompany.tightness == 0 ? 'online' : '',
                          notOnline: currCompany.tightness == 1 ? 'notOnline' : '',
                          warning: currCompany.tightness == 'N' ? 'warning' : '',
                        }"
                        >{{ currCompany.tightness == 0 ? '正常' : currCompany.tightness == 1 ? '报警' : '无效' }}</span
                      >
                    </td>
                  </tr>
                  <tr>
                    <td>卸油区油气浓度</td>
                    <td>{{ currCompany.oilConcentration }}</td>
                    <td>%/ppm</td>
                    <td>-</td>
                  </tr>
                </tbody>
              </table>
              <!--        <div v-if="currIndex === 4" class="button">-->
              <!--          <span :class="{'active': stationType === 1 ? 'active' : ''}" @click="changeStation(1)">液阻压力</span>-->
              <!--          <span :class="{'active': stationType === 2 ? 'active' : ''}" @click="changeStation(2)">卸油区油气浓度</span>-->
              <!--        </div>-->
              <!--        <div v-if="currIndex === 4" class="station-echarts">-->
              <!--          <station-echarts-->
              <!--            :id="stationId"-->
              <!--            :width="width"-->
              <!--            :height="height"-->
              <!--            :AirDataProp="AirDataProp"-->
              <!--          />-->
              <!--        </div>-->
            </div>
            <div v-if="currIndex === 3" class="motor-info pul-randa-l2r">
              <div class="common-title box-title">
                <div class="title">{{ `监测信息` }}</div>
              </div>
              <div class="sub-title" style="margin: 0">
                <img src="@/assets/biaoti.png" alt class="title-img" />
              </div>
              <table border="1" class="pollutant-table" style="margin: 0.1rem 0 0 0">
                <thead>
                  <tr>
                    <th>指标项</th>
                    <th>数值</th>
                    <th>单位</th>
                  </tr>
                </thead>
                <tbody>
                  <!-- 油烟浓度 -->
                  <tr>
                    <td>油烟浓度</td>
                    <td
                      :style="{
                        color: currCompany.concentration && currCompany.concentration - currCompany.concentrationThreshold > 0 ? '#F26E70' : '#76F573',
                      }"
                    >
                      {{ currCompany.concentration || currCompany.concentration == 0.0 || currCompany.concentration == 0 ? currCompany.concentration : '-' }}
                    </td>
                    <td>mg/m³</td>
                  </tr>
                  <!-- 风机电流 -->
                  <tr>
                    <td>风机电流</td>
                    <td :style="{ color: '#76F573' }">
                      {{ currCompany.fanCurrent || currCompany.fanCurrent == 0.0 || currCompany.fanCurrent == 0 ? currCompany.fanCurrent : '-' }}
                    </td>
                    <td>A</td>
                  </tr>
                  <!-- 净化器电流 -->
                  <tr>
                    <td>净化器电流</td>
                    <td :style="{ color: '#76F573' }">
                      {{
                        currCompany.purifierCurrent || currCompany.purifierCurrent == 0.0 || currCompany.purifierCurrent == 0
                          ? currCompany.purifierCurrent
                          : '-'
                      }}
                    </td>
                    <td>A</td>
                  </tr>
                  <!-- 非甲烷总烃 -->
                  <tr>
                    <td>非甲烷总烃</td>
                    <td :style="{ color: '#76F573' }">
                      {{ currCompany.nmhc || currCompany.nmhc == 0.0 || currCompany.nmhc == 0 ? currCompany.nmhc : '-' }}
                    </td>
                    <td>mg/m³</td>
                  </tr>
                  <!-- 温度 -->
                  <tr>
                    <td>温度</td>
                    <td :style="{ color: '#76F573' }">
                      {{ currCompany.temperature || currCompany.temperature == 0.0 || currCompany.temperature == 0 ? currCompany.temperature : '-' }}
                    </td>
                    <td>℃</td>
                  </tr>
                  <!-- 湿度 -->
                  <tr>
                    <td>湿度</td>
                    <td :style="{ color: '#76F573' }">
                      {{ currCompany.humidity || currCompany.humidity == 0.0 || currCompany.humidity == 0 ? currCompany.humidity : '-' }}
                    </td>
                    <td>%RH</td>
                  </tr>
                  <!-- 颗粒物浓度 -->
                  <tr>
                    <td>颗粒物浓度</td>
                    <td :style="{ color: '#76F573' }">
                      {{
                        currCompany.particulateMatterConcentration ||
                        currCompany.particulateMatterConcentration == 0.0 ||
                        currCompany.particulateMatterConcentration == 0
                          ? currCompany.particulateMatterConcentration
                          : '-'
                      }}
                    </td>
                    <td>mg/m³</td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div v-if="currIndex === 3" style="margin-top: 20px">
              <div class="common-title box-title">
                <div class="title">{{ `企业污染排名` }}</div>
                <div class="title_select">
                  <div
                    class="title_select_item"
                    :class="rankType === '1' ? 'title_select_item_select' : ''"
                    style="margin-right: 0.05rem"
                    @click="rankType = '1'"
                  >
                    按站点
                  </div>
                  <div class="title_select_item" :class="rankType === '2' ? 'title_select_item_select' : ''" @click="rankType = '2'">按街道</div>
                </div>
              </div>
              <div class="sub-title" style="margin: 0">
                <img src="@/assets/biaoti.png" alt class="title-img" />
              </div>
              <div class="cater_rank_box">
                <div class="cater_rank_th">
                  <div>排行</div>
                  <div>{{ rankType === '1' ? '站点' : '街道' }}</div>
                  <div>油烟浓度</div>
                </div>
                <div class="cater_rank_tb">
                  <div class="cater_rank_tr" v-for="(item, i) in caterRankList" :key="i">
                    <div>
                      <span class="index">{{ i + 1 }}</span>
                    </div>
                    <div :title="rankType === '1' ? item.restaurantName : item.streetName">
                      {{ rankType === '1' ? item.restaurantName : item.streetName }}
                    </div>
                    <div>{{ item.particulateMatterConcentration }}</div>
                  </div>
                </div>
              </div>
            </div>
            <div v-if="currIndex === 4" class="motor-info">
              <div class="title">
                <span>电枪监控</span>
                <div style="display: flex; align-items: center">
                  <a-select v-model="fuelDispenserId" style="width: 1rem" class="select-main" @change="changefuelDispenser">
                    <a-icon slot="suffixIcon" type="caret-down" style="color: rgba(0, 234, 255, 1); width: 0.23rem; height: 0.11rem" />
                    <a-select-option :value="item.fuelDispenserId" v-for="(item, index) in dispenserList" :key="index" class="select-option">
                      {{ item.fuelDispenserName }}</a-select-option
                    >
                  </a-select>
                  <!--              <a-select v-model="fuelGunId" style="width: 1rem;margin-left: 0.2rem" class="select-main" @change="changeFuelGun">-->
                  <!--                <a-icon-->
                  <!--                        slot="suffixIcon"-->
                  <!--                        type="caret-down"-->
                  <!--                        style="color:rgba(0, 234, 255, 1);width:0.23rem;height:0.11rem"-->
                  <!--                />-->
                  <!--                <a-select-option-->
                  <!--                        :value="item.fuelGunId"-->
                  <!--                        v-for="(item, index) in monitorInfo"-->
                  <!--                        :key="index"-->
                  <!--                        class="select-option"-->
                  <!--                >-->
                  <!--                  {{ item.fuelGunName }}</a-select-option-->
                  <!--                >-->
                  <!--              </a-select>-->
                </div>
              </div>
              <div class="sub-title">
                <img src="@/assets/biaoti.png" alt />
              </div>
              <swiper :options="swiperOptionOilElectric" class="electric-area">
                <swiper-slide v-for="(item, index) in monitorInfo" :key="index">
                  <div class="electric-list">
                    <div class="electric-left">
                      <span>{{ item.fuelGunName }}</span>
                      <span>电枪编号</span>
                    </div>
                    <div class="electric-middle">
                      <div>
                        <span>加油油品</span><span>{{ item.oils }}</span>
                      </div>
                      <div>
                        <span>油品标号</span><span>{{ item.oilGrade }}</span>
                      </div>
                    </div>
                    <div class="electric-right">
                      <img :src="item.alarmState ? abnoImage : noImage" alt="" class="electric-right-bg Rotation" />
                      <span>{{ item.gasLiquidRatio }}{{ item.gasLiquidRatio ? '%' : '--' }}</span>
                      <span>气液比</span>
                    </div>
                  </div>
                </swiper-slide>
              </swiper>
              <!--          <div class="motor-list">-->
              <!--            <div class="bg">-->
              <!--              <div>-->
              <!--                <span>{{ fuelGunSingle.oils }}</span>-->
              <!--                <span>加油油品</span>-->
              <!--              </div>-->
              <!--              <div>-->
              <!--                <span>{{ fuelGunSingle.oilGrade }}</span>-->
              <!--                <span>加油标号</span>-->
              <!--              </div>-->
              <!--              <img src="../../assets/heavily-polluted-enterprise/<EMAIL>" alt="">-->
              <!--            </div>-->
              <!--            <div class="bottom">-->
              <!--              <img src="../../assets/heavily-polluted-enterprise/<EMAIL>" alt="">-->
              <!--              <div class="right">-->
              <!--                <div class="single" :class="{'normal': fuelGunSingle.runState === 0 ? 'normal' : '', 'police': fuelGunSingle.runState === 1 ? 'police' : '', 'warn': fuelGunSingle.runState === 2 ? 'warn' : ''}">-->
              <!--                  <img :src="fuelGunSingle.runState === 0 ? nomImage : fuelGunSingle.runState === 1 ? yjImage : bjImage" alt="" class="Rotation">-->
              <!--                  <span :class="{'normal': fuelGunSingle.runState === 0 ? 'normal' : '', 'police': fuelGunSingle.runState === 1 ? 'police' : '', 'warn': fuelGunSingle.runState === 2 ? 'warn' : ''}">{{ fuelGunSingle.runState === 0 ? '正常' : fuelGunSingle.runState === 1 ? '预警 ' : '报警' }}</span>-->
              <!--                  <span class="state">运行状态</span>-->
              <!--                </div>-->
              <!--                <div class="single" :class="{'normal': fuelGunSingle.alarmState === 0 ? 'normal' : '', 'police': fuelGunSingle.alarmState === 1 ? 'police' : '', 'warn': fuelGunSingle.alarmState === 2 ? 'warn' : ''}">-->
              <!--                  <img :src="fuelGunSingle.alarmState === 0 ? nomImage : fuelGunSingle.alarmState === 1 ? yjImage : bjImage" alt="" class="Rotation">-->
              <!--                  <span :class="{'normal': fuelGunSingle.alarmState === 0 ? 'normal' : '', 'police': fuelGunSingle.alarmState === 1 ? 'police' : '', 'warn': fuelGunSingle.alarmState === 2 ? 'warn' : ''}">{{ fuelGunSingle.alarmState === 0 ? '正常' : fuelGunSingle.alarmState === 1 ? '预警 ' : '报警' }}</span>-->
              <!--                  <span class="text">气液比</span>-->
              <!--                  <span class="number">{{ fuelGunSingle.gasLiquidRatio }}{{ fuelGunSingle.gasLiquidRatio ? '%' : '-' }}</span>-->
              <!--                </div>-->
              <!--              </div>-->
              <!--            </div>-->
              <!--          </div>-->
            </div>
            <!-- <div v-if="currIndex === 3" class="motor-alarm-info">
          <div class="title">
            <span>油烟超标告警</span>
          </div>
          <div class="sub-title">
            <img src="@/assets/biaoti.png" alt />
          </div>
          <div class="motor-list">
            <div class="motor-area" v-if="monitorOilInfo.length">
              <div class="motor-title">
                <span>商户名称</span>
                <span>报警内容</span>
                <span>报警时间</span>
              </div>
              <div class="content-list">
                <swiper
                  :options="swiperOptionOilMonitor"
                  style="width: 100%; height: 100%"
                >
                  <swiper-slide
                    class="single"
                    v-for="(item, index) in monitorOilInfo"
                    :key="index"
                  >
                    <span :title="item.restaurantName">{{
                      item.restaurantName
                    }}</span>
                    <span :title="item.restaurantAlarmContent">{{
                      item.restaurantAlarmContent
                    }}</span>
                    <span :title="item.restaurantAlarmTime">{{
                      item.restaurantAlarmTime
                    }}</span>
                  </swiper-slide>
                </swiper>
              </div>
            </div>
            <div
              class="motor-area"
              v-else
              style="text-align: center; line-height: 2rem"
            >
              暂无告警信息
            </div>
          </div>
        </div> -->
          </section>
          <construction-site
            v-if="[0, 5, 6, 7, 8, 9, 13].includes(currIndex)"
            :index="currIndex"
            :searchWords="searchWord"
            :online="online"
            :currStreet="currStreet"
            :randaLocation="randaLocation"
            :randaItem="randaItem"
            @constrSite="handleConstrSite"
            @heavyPollutionEnterpriseList="handleHeavyPollutionEnterpriseList"
            @heaaviycount="handleCount"
            @sitecount="handleSitecount"
            @noiseSitecount="noiseSitecount"
            @change="changeWater"
            @changeSite="changeSite"
            @changeRandaType="changeRandaType"
            @setVideo="setVideo"
          />
          <section class="center-search">
            <div class="left" v-if="![10, 11].includes(currIndex)">
              <a-select v-model="currStreet" @change="changeStreet" class="select-main">
                <a-icon slot="suffixIcon" type="caret-down" style="color: rgba(0, 234, 255, 1); width: 0.23rem; height: 0.11rem" />
                <a-select-option :value="item.streetCode" v-for="(item, index) in streetList" :key="index"> {{ item.streetName }}</a-select-option>
              </a-select>
              <!-- <div v-if="currIndex==10" style="position: absolute;color:#fff;top:18px;left:15px; font-size:18px;">{{chooseDate}}</div> -->
            </div>
            <div class="left1" v-if="false">
              <a-date-picker
                v-if="false"
                v-model="chooseDate"
                format="YYYY-MM-DD"
                valueFormat="YYYY-MM-DD"
                class="select-main2"
                :showToday="false"
                :disabled-date="disabledDate"
                @openChange="datePanelChange"
              >
                <template slot="dateRender" slot-scope="current">
                  <div :class="'ant-calendar-date' + getHasDataStyle(current)">
                    {{ current.date() }}
                  </div>
                </template>
              </a-date-picker>
            </div>
            <div class="left1" v-if="false" style="width: 4.5rem">
              <a-select v-model="randaItem" @change="changeRandaItem" class="select-main1">
                <a-icon slot="suffixIcon" type="caret-down" style="color: rgba(0, 234, 255, 1); width: 0.23rem; height: 0.11rem" />
                <a-select-option :value="item.id" v-for="(item, index) in randaItemList" :key="index"> {{ item.name }}</a-select-option>
              </a-select>
            </div>
            <div class="right" v-if="![10, 11].includes(currIndex)">
              <input
                type="text"
                :placeholder="
                  currIndex == 5
                    ? '请输入：公司名称'
                    : currIndex == 0
                      ? '请输入：(公司/工地)名称'
                      : currIndex == 3
                        ? '请输入想要查看的商户名称'
                        : currIndex == 6
                          ? '请输入：站点名称'
                          : '请输入：企业名称'
                "
                v-model="searchWords"
                @keyup.enter="search"
              />
              <a-icon type="close-circle" style="font-size: 0.2rem; cursor: pointer; margin-right: 0.15rem" v-if="searchWords != ''" @click="clearSearch" />
            </div>
            <div class="icon" v-if="![10, 11].includes(currIndex)">
              <img src="@/assets/search.png" alt="" @click="search" style="width: 0.25rem; height: 0.26rem" />
            </div>
          </section>
        </div>
        <section class="center-map map-control" style="width: '100%'">
          <!-- <keep-alive> -->
          <center-map
            :markerList="currCompanyList"
            :currCompanyId="currCompanyId.toString()"
            :currentSelectedBuilding="currentSelectedBuilding"
            :currentSelectedCompanyId="currentSelectedCompanyId.toString()"
            :isClose="isClose"
            :activeIndex="currIndex"
            :accessToken="accessToken"
            :randaType="randaType"
            :isWx="isWx"
            :isRadar="isRadar"
            :lukuangIndex="lukuangIndex"
            :cotrolsIndex="cotrolsIndex"
            :radarItem="radarItem"
            @getGroupFileName="getGroupFileName"
            @clickMaker="handleClickMaker"
            @close="close"
            @curSiteChange="getCurSiteFromMap"
            @curCompanyChange="curCompanyChange"
            @randaClick="randaClick"
            @randaExistDate="randaExistDateC"
          ></center-map>
          <!-- </keep-alive> -->
        </section>
      </template>
      <MapControlBox v-if="!isRadar && typeNum !== 11" :typeNum="typeNum" @postActiveIndex="getActiveIndex"></MapControlBox>

      <!-- 雷达小地图 -->
      <div class="small_map" v-if="currIndex === 10" @click="changeSmallMap">
        <img :src="isWx ? wxdt : dzdt" alt="" />
      </div>

      <!-- 比色卡 -->
      <div class="RHSCC" v-if="currIndex === 10">
        <div class="RHSCC2"></div>
        <div class="RHSCC1">
          <span>0.00</span>
          <span>0.125</span>
          <span>0.250</span>
          <span>0.375</span>
          <span>0.500</span>
        </div>
      </div>

      <!-- 雷达控件 -->
      <div v-if="currIndex === 10" class="controls">
        <div class="controls1" @click="controls(1)">
          {{ randaType }}
          <!--消光系数下拉框 -->
          <transition enter-active-class="animate__animated animate__fadeInRight" leave-active-class="animate__animated animate__fadeOutRight">
            <div v-show="showselect" class="icon-selecte-box">
              <div
                v-for="item in RDSubNameList"
                :key="item"
                class="randaSel_item"
                :class="randaType == item ? 'randaSel_item_active' : ''"
                @click="changeRandaType(item)"
              >
                {{ item }}
              </div>
            </div>
          </transition>
        </div>
        <div :class="{ controls2: lukuangIndex == 2 }" @click="controls(2)"><img :src="lukuang" alt="路况" /> 路况</div>
        <div :class="{ controls2: cotrolsIndex == 3 }" @click="controls(3)"><img :src="ceju" alt="测距" /> 测距</div>
        <div style="user-select: none; cursor: not-allowed" :class="{ controls2: cotrolsIndex == 4 }" @click="controls(4)">
          <img :src="biaoji" alt="标记" /> 标记
        </div>
        <div style="user-select: none; cursor: not-allowed" :class="{ controls2: cotrolsIndex == 5 }" @click="controls(5)">
          <img :src="jietu" alt="截图" /> 截图
        </div>
      </div>

      <!-- 固废小视频 -->
      <div v-if="currIndex === 8 && videoUrl" class="air-video">
        <div class="video-title">
          <img src="../../assets/<EMAIL>" alt="icon" />
          <span>危废监控</span>
          <span class="close" @click="goVideo">更多视频>></span>
        </div>
        <div class="air-video-area">
          <LivePlayers :video-url="videoUrl" :aspect="'327:186'" fluent live :stretch="true" />
        </div>
      </div>

      <!-- 扫描时序 -->
      <div v-if="currIndex === 10" class="sequence">
        <div class="sequence_title">扫描时序</div>
        <div class="sequence_cont">
          <div v-for="(item, index) in chooseDateList" :class="radarIndex == index ? 'sequence_active' : ''" @click="ChDate(item, index)" :key="index">
            {{ item.startDate }}-{{ item.endDate }}
          </div>
        </div>
        <div class="choose_date">
          <img src="../../assets/<EMAIL>" alt="" />
          {{ chooseDate }}
          <el-date-picker
            style="opacity: 0; position: absolute; left: 0; top: 0"
            v-model="chooseDate"
            type="date"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            @change="chooseDateChange"
            placeholder="选择日期"
          >
          </el-date-picker>
        </div>
      </div>
    </template>
    <!-- 底部分类 -->
    <section class="bottom-tabs" v-if="!isRadar">
      <bottom-tabs @postActiveIndex="getActiveIndexP" />
    </section>
  </div>
</template>

<script lang="ts">
/* eslint-disable @typescript-eslint/ban-ts-ignore */
//@ts-ignore
import LivePlayers from '@liveqing/liveplayer'
//@ts-ignore
import Bus from '@/utils/bus'
import moment from 'moment'
import EZUIKitJs from '../details/EZUIKitJs.vue'
import { Vue, Component, Watch } from 'vue-property-decorator'
import tabs from './tabs.vue'
import CenterMap from './map.vue'
import { socketUrl1 } from '@/utils/index'
import { getToken } from '@/utils/authority'
import { Icon, Select, DatePicker } from 'ant-design-vue'
import { Swiper, SwiperSlide } from 'vue-awesome-swiper'
import VocLineChart from '@/components/Charts/VocLineChart1.vue'
import stationEcharts from '@/components/Charts/stationEcharts.vue'
import { getNowTime } from '@/utils/index'
import livePlayer from '../details/livePlayer.vue'
import constructionSite from './constructionSite.vue'
import { getStreet } from '@/api/headvily-pollution'
import MapControlBox from './mapControlBox.vue'
import { dataFileList, randaExistDate } from '@/api/randa'
import analysisAir from './analysisAir/index.vue'
import analysisWater from './analysisWater/index.vue'
import { getCaterRank } from '@/api/heavyCorrupt'

import dayjs from 'dayjs'
interface VocData {
  vocxData: any[]
  vocyData: any[]
  voczData: any[]
  emissionyData: any[]
  emissionzData: any[]
  type: number
}
interface AirDataProp {
  stationXData: string | number[]
  stationY1Data: string | number[]
  stationY2Data: string | number[]
  type: number
  isShow: number
}
@Component({
  name: 'otherPages',
  components: {
    BottomTabs: tabs,
    CenterMap,
    ADatePicker: DatePicker,
    AIcon: Icon,
    ASelect: Select,
    ASelectOption: Select.Option,
    VocLineChart,
    Swiper,
    SwiperSlide,
    stationEcharts,
    EZUIKitJs,
    livePlayer,
    constructionSite,
    MapControlBox,
    analysisAir,
    analysisWater,
    LivePlayers,
  },
})
export default class extends Vue {
  private titleList = [
    '在建工地总数',
    '印刷行业总数',
    '汽修行业总数',
    '餐饮油烟监测总数',
    '加油站总数',
    '企事业单位',
    '噪声监测总数',
    '电量监测总数',
    '危废在线监管总数',
    '重点污染企业总数',
    '',
    '',
    '',
    '尾气监测总数',
  ]
  private activeIndex: any = 0
  // created() {
  //   this.activeIndex = this.$route.query.activeIndex;
  // }
  // 餐饮油烟污染企业排名类型    站点/街道
  private caterRankList: any = []
  private rankType: any = '1'
  private analysisType: string = 'air'
  statData: any = [
    {
      name: '全部',
      num: 618,
    },
    {
      name: '医疗机构',
      num: 147,
    },
    {
      name: '印刷企业',
      num: 19,
    },
    {
      name: '制药行业',
      num: 23,
    },
    {
      name: '加油站',
      num: 32,
    },
    {
      name: '检测机构',
      num: 7,
    },
    {
      name: '汽修行业',
      num: 390,
    },
  ]
  typeNum: any = 0
  @Watch('rankType')
  private rankTypeChange(type: any) {
    this.getCaterRankList()
  }
  private getCaterRankList() {
    getCaterRank({ type: this.rankType }).then((res) => {
      this.caterRankList = res.data.data || []
    })
  }
  private randaItem: any = ''
  private randaItemList: any = []
  private stationId = 'stationId'
  private dispenserList = [] // 电机
  private fuelDispenserId = '' // 电机id
  private fuelGunId = '' // 电枪id
  videoUrl: any = ''
  private fuelGunSingle = {} // 当前电枪
  private AirDataProp: AirDataProp = {
    stationXData: [],
    stationY1Data: [],
    stationY2Data: [],
    type: 1,
    isShow: 1,
  }
  private streetList: any = []
  private currStreet = '全部街道'
  private bjImage = require('../../assets/heavily-polluted-enterprise/<EMAIL>')
  private yjImage = require('../../assets/heavily-polluted-enterprise/<EMAIL>')
  private nomImage = require('../../assets/heavily-polluted-enterprise/<EMAIL>')
  private noImage = require('../../assets/heavily/zc.png')
  private abnoImage = require('../../assets/heavily/yc.png')
  wxdt: any = require('../../assets/<EMAIL>')
  dzdt: any = require('../../assets/<EMAIL>')
  biaoji: any = require('../../assets/<EMAIL>')
  ceju: any = require('../../assets/<EMAIL>')
  jietu: any = require('../../assets/<EMAIL>')
  lukuang: any = require('../../assets/<EMAIL>')
  private accessToken = 'at.3oba0x332snvccwh2tqs0qjf33f6feop-4g2wr4z07g-023nzpf-kvh5jz7gw'
  private monitorDeviceState = 1
  private currId = ''
  private outPage = true
  private width1 = 349
  private height1 = 186
  private title = ''
  private ezopen = ''
  private isClose = false
  private isRadar: any = false
  private stationType = 1
  private img1 = require('../../assets/gou.png')
  private img2 = require('../../assets/cha.png')
  private width = 100 + '%'
  private height = 1.7 + 'rem'
  private printFactoryList = []
  private monitorInfo = []
  private monitorOilInfo = []
  private swiperOptionMonitor = {
    direction: 'vertical',
    slidesPerView: 3,
    slidesPerGroup: 1,
    loop: true,
    autoplay: {
      delay: 5000,
      disableOnInteraction: false,
    },
  }
  private swiperOptionOilMonitor = {
    direction: 'vertical',
    slidesPerView: 5,
    slidesPerGroup: 1,
    loop: true,
    autoplay: {
      delay: 5000,
      disableOnInteraction: false,
    },
  }
  private swiperOptionOilElectric = {
    direction: 'vertical',
    slidesPerView: 2,
    slidesPerGroup: 1,
    loop: true,
    autoplay: {
      delay: 5000,
      disableOnInteraction: false,
    },
  }
  private VocData: VocData = {
    vocxData: [],
    vocyData: [],
    voczData: [],
    emissionyData: [],
    emissionzData: [],
    type: 1,
  }
  private type = 1
  private keywords1 = ''

  private defaultValue = ''
  private swiperOption = {
    direction: 'vertical',
    slidesPerView: 5,
    slidesPerGroup: 1,
    loop: true,
    autoplay: {
      delay: 2800,
      disableOnInteraction: false,
    },
  }

  // 当前商户对象
  private currCompany = {}
  // 商户列表
  private currCompanyList: Array<any> = []
  private currentSelectedBuilding = ''
  private currentSelectedCompanyId = ''
  // 当前商户id
  private currCompanyId = ''
  // 商户列表长度
  private currCompanyListLength: any = []
  private currSite: any = {}
  private online: any = [2, 3]
  // 告警信息列表
  private alarmList = []
  // 视频列标
  private videoList = []
  // 档当前id集合
  private currVideoID: any = []
  // 地图marker点集合
  private markerList = []
  // 搜索关键词
  private searchWords = ''
  private searchWord = ''
  // 默认商户ID
  private merchantsId = ''
  private isfirst = true
  // // 默认
  // private VocData = {}
  // 当前socket返回数据
  private socketData: any
  // 获得tabs组件传来的activeIndex
  private currIndex = 0
  // 在建工地
  private companyList: any[] = []

  // 判断是否为大气环境综合分析点击站点
  private stationTypeKey = ''

  isWx: boolean = false

  showselect: boolean = false

  cotrolsIndex: number = 0

  lukuangIndex: number = 0

  RDSubNameList: any = ['消光系数', 'PM₁₀', 'PM₂.₅', '退偏振比']

  chooseDate: string = ''

  radarItem: any = {}

  radarIndex: number = -1
  private openPicker: any = false
  private randaType: any = '消光系数'
  private randaLocation: any = {}
  chooseDateList: any = []
  private rdMonth: any = moment().format('M')
  private rdYear: any = moment().format('YYYY')
  private groupFileName: any = ''
  @Watch('chooseDate', { deep: true, immediate: false })
  private chooseRaderDate(nVal: any) {
    this.randaExistDate(nVal)
  }
  // @Watch('rdMonth', { deep: true })
  // private rdMonthChange(nVal: any) {
  //   nVal = nVal > 9 ? nVal : '0' + nVal
  //   const findDate = this.chooseDate.slice(0, 4) + '-' + nVal + '-01'
  //   this.randaExistDate(findDate)
  // }
  // @Watch('rdYear', { deep: true })
  // private rdYearChange(nVal: any) {
  //   this.rdMonth = this.rdMonth > 9 ? this.rdMonth : '0' + this.rdMonth
  //   const findDate = nVal + '-' + this.rdMonth + '-01'
  //   this.randaExistDate(findDate)
  // }

  private getGroupFileName(groupFileName: any) {
    this.randaItem = groupFileName
  }
  // 雷达数据类型 消光系数、PM10、PM2.5
  changeRandaType(randaType: any) {
    this.randaType = randaType
    // this.showselect = false
    this.randaLocation = {}
  }
  // 雷达地图点击获取的经纬度
  private randaClick(location: any) {
    this.randaLocation = location
  }
  // 日期选择器样式
  private getHasDataStyle(current: any) {
    let date = moment(current._d).format('YYYY-MM-DD')
    if (this.chooseDateList.includes(date)) {
      return ' hasDataClass'
    } else {
      return ''
    }
  }
  chooseDateChange(e: any) {
    // this.randaExistDate(this.chooseDate)
    // this.getRandaFileList()
  }
  // datePanelChange方法为openChange事件的执行函数
  private datePanelChange(isShow: any) {
    if (isShow) {
      setTimeout(() => {
        const dateDom: any = document.querySelector('.ant-calendar-ym-select')
        dateDom.addEventListener('DOMCharacterDataModified', () => {
          let yearDom: any = document.querySelector('.ant-calendar-year-select')
          let monthDom: any = document.querySelector('.ant-calendar-month-select')
          const year = yearDom.innerText.replace('年', '')
          const month = monthDom.innerText.replace('月', '')
          this.rdMonth = month
          this.rdYear = year
        })
      }, 0)
    }
  }
  private changeRandaItem() {}
  private disabledDate(current: any) {
    // Can not select days before today and today
    return current && current > moment().endOf('day')
  }
  private getRandaFileList() {
    dataFileList({ findDate: this.chooseDate }).then((res) => {
      this.randaItemList = res.data.data
        ? res.data.data.map((v: any) => {
            return {
              id: v.fileName,
              name: v.startDate.slice(11, 16) + ' ~ ' + v.endDate.slice(11, 16),
              // name: '1234~5678'
            }
          })
        : []
      this.randaItem = this.randaItemList.length ? this.randaItemList[0].id : ''
    })
  }
  randaExistDateC(findDate: any) {
    this.chooseDate = dayjs(findDate).format('YYYY-MM-DD')
    randaExistDate({ findDate: this.chooseDate }).then((res) => {
      this.chooseDateList = res.data.data || []
      this.radarIndex = this.chooseDateList.length - 1
    })
  }
  private randaExistDate(findDate: any) {
    randaExistDate({ findDate }).then((res) => {
      this.chooseDateList = res.data.data || []
    })
  }
  getActiveIndex(index: number) {
    this.currIndex = index
    this.searchWord = ''
    this.chooseDate = moment().format('YYYY-MM-DD')
    this.ezopen = ''
    this.currId = ''
    this.currCompanyId = ''
    if (index == 3) {
      this.getCaterRankList()
    }
    if (index == 0 || index == 3) {
      this.online = [2, 3]
    } else {
      this.online = []
    }
    this.currStreet = '全部街道'
    this.searchWords = ''
    this.isfirst = true
    // 空气站点
    if (index === 11) {
      console.log('大气环境综合分析')
      return
    }
    console.log(index, 'index')
    this.connect()
    if (index == 10) {
      // this.randaExistDate(this.chooseDate)
      // this.getRandaFileList()
    }
  }
  getActiveIndexP(index: number) {
    // this.getActiveIndex(index)
    this.typeNum = index
  }
  private handleConstrSite(args: any) {
    this.currCompanyList = (args || []).map((item: any) => {
      item.show = false
      return item
    })
    this.currentSelectedCompanyId = this.currCompanyList && this.currCompanyList.length ? this.currCompanyList[0].companyId : ''
  }
  private handleHeavyPollutionEnterpriseList(args: any) {
    this.currCompanyList = (args || []).map((item: any) => {
      item.show = false
      return item
    })
    this.currentSelectedBuilding = this.currCompanyList && this.currCompanyList.length ? this.currCompanyList[0].value : ''
    // Bus.$emit('curCompanyChange', this.currentSelectedBuilding)
  }
  private changeWater(args: any) {
    const data = args.target.w.data
    if (data) {
      this.currentSelectedBuilding = data.value || ''
    }
  }
  private handleCount(args: any) {
    this.currCompanyListLength = (args < 1000 ? (args > 99 ? String(args) : '0' + args) : String(args)).split('')
  }
  private handleSitecount(args: any) {
    this.currCompanyListLength = (args.count < 1000 ? (args.count > 99 ? String(args.count) : '0' + args.count) : String(args.count)).split('')
    this.currSite = args
  }
  private noiseSitecount(args: any) {
    this.currCompanyListLength = (
      args.total < 1000 ? (args.total > 99 ? String(args.total) : args.total < 10 ? '00' + args.total : '0' + args.total) : String(args.total)
    ).split('')
    this.currSite = args
    let num = this.currSite.onlineCount ? 1 : this.currSite.alarmCount ? 2 : 0
    if (num) {
      this.online = [1, 2]
    } else {
      this.online = [1, 2, 3]
    }
  }
  private changeSite(args: any) {
    this.currentSelectedCompanyId = args
  }
  private socket: any
  // 建立连接
  private connect() {
    this.socket = new WebSocket(socketUrl1())
    // 监听socket连接
    this.socket.onopen = this.open
    // 监听socket错误信息
    this.socket.onerror = this.error
    // 监听socket消息
    this.socket.onmessage = this.getMessage
    window.onbeforeunload = () => {
      this.socket.onopen = () => {}
      this.socket.onerror = () => {}
      this.socket.onmessage = () => {}
      this.socket.close()
    }
  }
  private open() {
    this.send()
  }
  private send() {
    this.socket.send(JSON.stringify({ code: 1, token: 'token' }))
  }
  private error() {
    console.error('系统连接错误')
  }
  private getMessage(msg: any) {
    const data: any = JSON.parse(msg.data)
    // console.log(data,'拿到数据')
    // console.log(this.currIndex, '当前下标')
    if (data.code === -1 && data.success) {
      let streetCode = this.currStreet == '全部街道' ? undefined : this.currStreet
      let online = this.online.indexOf(2) > -1 && this.online.length == 1 ? 1 : this.online.indexOf(3) > -1 && this.online.length == 1 ? 0 : undefined
      switch (this.currIndex) {
        case 1: // 印刷厂数据
          this.socket.send(
            JSON.stringify({
              code: 2,
              token: getToken(),
              keywords: this.searchWords,
              streetCode: streetCode,
              online: online,
            })
          )
          break
        case 2: // 汽修数据
          this.socket.send(
            JSON.stringify({
              code: 15,
              token: getToken(),
              keywords: this.searchWords,
              pageNum: 1,
              pageSize: 36,
            })
          )
          this.socket.send(
            JSON.stringify({
              code: 10,
              token: getToken(),
              keywords: this.searchWords,
              streetCode: streetCode,
              online: online,
            })
          )
          break
        case 3: // 餐饮油烟数据
          console.log('查询餐饮油烟数据')
          this.socket.send(
            JSON.stringify({
              code: 17,
              token: getToken(),
              keywords: this.searchWords,
              streetCode: streetCode,
              online: online,
            })
          )
          break
        case 4: // 加油站数据
          this.socket.send(
            JSON.stringify({
              code: 8,
              token: getToken(),
              keywords: this.searchWords,
              streetCode: streetCode,
              online: online,
            })
          )
          break
        default:
          break
      }
    }
    // 获得印刷企业列表
    if (data.code === -2 && data.success) {
      this.socketData = data
      const count = Number(data.count)
      this.currSite.online = data.onlineCount
      this.currSite.Offline = data.offlineCount
      if (this.isfirst) {
        this.currCompanyListLength = (
          count === 0 ? '000' : count < 10 ? '00' + count : count < 100 ? '0' + count : count < 1000 ? count.toString() : count.toString()
        ).split('')
        this.isfirst = false
      }
      if (data.printFactoryList.length) {
        this.markerList = data.printFactoryList || []
        this.merchantsId = data.printFactoryList[0].printFactoryId
        this.currCompanyList = (data.printFactoryList || []).map((item: any) => {
          return item
        })
        this.accessToken = data.accessToken
        this.currCompany = data.printFactoryList[0] || {}
        this.currCompanyId = data.printFactoryList[0].printFactoryId
      } else {
        this.markerList = []
        this.merchantsId = ''
        this.currCompanyList = []
        this.accessToken = ''
        this.currCompany = {}
        this.currCompanyId = ''
        this.ezopen = ''
      }
      console.log(this.markerList, 'markerList')
      this.socket.send(
        JSON.stringify({
          code: 3,
          printFactoryId: this.currCompanyId,
          date: getNowTime().substr(0, 10),
          token: getToken(),
        })
      )
      this.socket.send(JSON.stringify({ code: 4, token: getToken(), pageNum: 1, pageSize: 10 }))
    }
    if (data.code === -3 && data.success) {
      if (data.printRecordHourList.length) {
        const array1: any = []
        const array2: any = []
        const array3: any = []
        const array4: any = []
        const array5: any = []
        data.printRecordHourList.forEach((item: any) => {
          array1.push(item.hour + '时')
          array2.push(data.vocAvgConcentrationDaily || 0)
          array3.push(item.vocAvgConcentration || 0)
          array4.push(item.emissionAvgRate || 0)
          array5.push(data.emissionAvgRateDaily || 0)
        })
        this.VocData.vocxData = array1
        this.VocData.voczData = array2
        this.VocData.vocyData = array3
        this.VocData.emissionyData = array4
        this.VocData.emissionzData = array5
        this.VocData.type = this.type
      } else {
        this.VocData = {
          vocxData: [],
          vocyData: [],
          voczData: [],
          emissionyData: [],
          emissionzData: [],
          type: this.type,
        }
      }
    }
    if (data.code === -4 && data.success) {
      this.alarmList = (data.printAlarmList || []).map((item: any) => {
        item.printAlarmTime = item.printAlarmTime.substr(5, 11)
        return item
      })
    }
    // 汽修
    if (data.code === -10 && data.success) {
      this.socketData = data
      this.type = 1
      this.VocData.type = 1
      const count = Number(data.count)
      this.currSite.online = data.onlineCount
      this.currSite.Offline = data.offlineCount
      if (this.isfirst) {
        this.currCompanyListLength = (
          count === 0 ? '000' : count < 10 ? '00' + count : count < 100 ? '0' + count : count < 1000 ? count.toString() : count.toString()
        ).split('')
        this.isfirst = false
      }
      if (data.garageList.length) {
        this.currCompanyList = (data.garageList || []).map((item: any) => {
          item.show = false
          return item
        })
        this.currCompany = data.garageList[0] || {}
        this.currCompanyId = data.garageList[0].garageId
      } else {
        this.currCompanyList = []
        this.currCompany = {}
        this.currCompanyId = ''
        this.ezopen = ''
      }
      // this.socket.send(JSON.stringify({ code: 15,  token: getToken(), keywords: this.searchWords, pageNum: 1, pageSize: 36, garageId: this.currCompanyId }));
      this.socket.send(
        JSON.stringify({
          code: 11,
          garageId: this.currCompanyId,
          token: getToken(),
        })
      )
      this.socket.send(
        JSON.stringify({
          code: 13,
          token: getToken(),
          pageNum: 1,
          pageSize: 10,
        })
      )
    }
    if (data.code === -11 && data.success) {
      if (data.garageRecordHourList.length) {
        const array1: any = []
        const array2: any = []
        const array3: any = []
        const array4: any = []
        const array5: any = []
        data.garageRecordHourList.forEach((item: any) => {
          array1.push(item.hour + '时')
          array2.push(data.vocAvgConcentrationDaily || 0)
          array3.push(item.vocAvgConcentration || 0)
          array4.push(item.emissionAvgRate || 0)
          array5.push(data.emissionAvgRateDaily || 0)
        })
        this.VocData.vocxData = array1
        this.VocData.voczData = array2
        this.VocData.vocyData = array3
        this.VocData.emissionyData = array4
        this.VocData.emissionzData = array5
        this.VocData.type = this.type
      } else {
        this.VocData = {
          vocxData: [],
          vocyData: [],
          voczData: [],
          emissionyData: [],
          emissionzData: [],
          type: this.type,
        }
      }
    }
    if (data.code === -13 && data.success) {
      this.alarmList = (data.alarmList || []).map((item: any) => {
        item.garageAlarmTime = item.garageAlarmTime.substr(5, 11)
        return item
      })
    }
    if (data.code === -15 && data.success) {
      const array: any = []
      const records = data.cameraList ? data.cameraList.records || [] : []
      records.forEach((item: any) => {
        const curr = array.filter((items: any) => items.garageId === item.garageId)
        if (curr.length === 0) {
          array.push(item)
        }
      })
      this.videoList = array
      // this.handleClickMaker({ data: this.currCompany, type: 2 })
    }
    // 加油站
    if (data.code === -8 && data.success) {
      const count = Number(data.gasCount)
      this.currSite.online = data.onlineCount
      this.currSite.Offline = data.offlineCount
      if (this.isfirst) {
        this.currCompanyListLength = (
          count === 0 ? '000' : count < 10 ? '00' + count : count < 100 ? '0' + count : count < 1000 ? count.toString() : count.toString()
        ).split('')
        this.isfirst = false
      }
      if (data.gasList.length) {
        this.socketData = data
        this.currCompanyList = (data.gasList || []).map((item: any) => {
          item.show = false
          return item
        })
        this.currCompany = data.gasList[0] || {}
        this.currCompanyId = data.gasList[0].gasId
      } else {
        this.currCompanyList = []
        this.currCompany = {}
        this.currCompanyId = ''
      }
      this.socket.send(
        JSON.stringify({
          code: 7,
          gasId: this.currCompanyId,
          token: getToken(),
        })
      )
    }
    if (data.code === -7 && data.success) {
      const gasPojo = data.gasPojo
      if (gasPojo.dispenserList.length) {
        this.dispenserList = gasPojo.dispenserList || []
        this.fuelDispenserId = gasPojo.dispenserList[0].fuelDispenserId
        this.monitorInfo = (gasPojo.dispenserList[0].fuelGunList || []).map((item: any) => {
          item.fuelGunName = item.fuelGunName
          return item
        })
        /* eslint-disable @typescript-eslint/ban-ts-ignore */
        //@ts-ignore
        this.fuelGunId = this.monitorInfo.length ? this.monitorInfo[0]['fuelGunId'] : ''
        this.fuelGunSingle = this.monitorInfo[0]
      } else {
        this.fuelDispenserId = ''
        this.fuelGunId = ''
        this.fuelGunSingle = {}
        this.monitorInfo = []
      }
      if (gasPojo.environmentList && gasPojo.environmentList.length) {
        const array1: any = []
        const array2: any = []
        const array3: any = []
        gasPojo.environmentList.forEach((item: any) => {
          array1.push(item.hours)
          array2.push(item.hydraulicPressure)
          array3.push(item.oilConcentration)
        })
        this.AirDataProp.stationXData = array1
        this.AirDataProp.stationY1Data = array2
        this.AirDataProp.stationY2Data = array3
        this.AirDataProp.type = this.stationType
        this.AirDataProp.isShow = 1
      } else {
        this.AirDataProp = {
          stationXData: [],
          stationY1Data: [],
          stationY2Data: [],
          type: this.stationType,
          isShow: 1,
        }
      }
    }
    // 餐饮油烟
    if (data.code === -17 && data.success) {
      this.socketData = data
      console.log(data, '餐饮油烟数据')
      const count = Number(data.count)
      this.currSite.online = data.onlineCount
      this.currSite.Offline = data.offlineCount
      if (this.isfirst) {
        this.currCompanyListLength = (
          count === 0 ? '000' : count < 10 ? '00' + count : count < 100 ? '0' + count : count < 1000 ? count.toString() : count.toString()
        ).split('')
        this.isfirst = false
      }
      if (data.restaurantList && data.restaurantList.length !== 0) {
        console.log('qwe')
        this.currCompanyList = (data.restaurantList || []).map((item: any) => {
          item.show = false
          return item
        })
        this.currCompany = data.restaurantList[0] || {}
        this.currCompanyId = data.restaurantList[0].id
      } else {
        console.log('asd')
        this.currCompanyList = []
        this.currCompany = {}
        this.currCompanyId = ''
      }
      this.socket.send(
        JSON.stringify({
          code: 19,
          token: getToken(),
          pageNum: 1,
          pageSize: 10,
        })
      )
      this.socket.send(
        JSON.stringify({
          code: 21,
          token: getToken(),
          restaurantId: this.currCompanyId,
          date: getNowTime().substr(0, 10),
        })
      )
    }
    if (data.code === -19 && data.success) {
      // restaurantAlarmList
      this.monitorOilInfo = (data.restaurantAlarmList || []).map((item: any) => {
        item.restaurantAlarmTime = item.restaurantAlarmTime.substr(5, 11)
        return item
      })
    }
    if (data.code === -21 && data.success) {
      if (data.restaurantRecordHourList.length) {
        const array1: any = []
        const array2: any = []
        const array3: any = []
        data.restaurantRecordHourList.forEach((item: any) => {
          array1.push(item.hour + '时')
          array2.push(item.concentration || 0)
          array3.push(0)
        })
        this.AirDataProp.stationXData = array1
        this.AirDataProp.stationY1Data = array2
        this.AirDataProp.stationY2Data = array3
        this.AirDataProp.type = this.stationType
        this.AirDataProp.isShow = 2
      } else {
        this.AirDataProp = {
          stationXData: [],
          stationY1Data: [],
          stationY2Data: [],
          type: this.stationType,
          isShow: 2,
        }
      }
    }
    // 印刷实时数据
    if (data.code === 26) {
      if (this.currIndex === 1) {
        const curr = data.printFactory
        /* eslint-disable @typescript-eslint/ban-ts-ignore */
        //@ts-ignore
        if (curr.printFactoryId === this.currCompany.printFactoryId) {
          this.currCompany = curr
        }
      }
    }
    // 汽修实时数据
    if (data.code === 23) {
      if (this.currIndex === 2) {
        const curr = data.garage
        /* eslint-disable @typescript-eslint/ban-ts-ignore */
        //@ts-ignore
        if (curr.garageId === this.currCompany.garageId) {
          this.currCompany = curr
        }
      }
    }
    // 餐饮油烟实时数据
    if (data.code === 25) {
      if (this.currIndex === 3) {
        console.log('餐饮油烟实时数据', data.restaurantList)
        const curr = data.restaurantList
        /* eslint-disable @typescript-eslint/ban-ts-ignore */
        //@ts-ignore
        if (curr.id === this.currCompany.id) {
          this.currCompany = curr
        }
      }
    }
    // 加油站油烟实时数据
    if (data.code === 27) {
      if (this.currIndex === 4) {
        const curr = data.gasPojo
        /* eslint-disable @typescript-eslint/ban-ts-ignore */
        //@ts-ignore
        if (curr.gasId === this.currCompany.gasId) {
          this.currCompany = curr
        }
      }
    }
  }
  // 液压交换
  changeStation(params: any): void {
    this.stationType = params
    this.AirDataProp.type = params
  }
  // 电机切换
  private changefuelDispenser(val: any): void {
    const curr: any = this.dispenserList.filter((item: any) => item.fuelDispenserId === val)[0]
    this.monitorInfo = curr.fuelGunList || []
    /* eslint-disable @typescript-eslint/ban-ts-ignore */
    //@ts-ignore
    this.fuelGunId = this.monitorInfo.length ? this.monitorInfo[0]['fuelGunId'] : ''
    this.fuelGunSingle = this.monitorInfo.length ? this.monitorInfo[0] : {}
  }
  private changeFuelGun(val: any): void {
    this.fuelGunSingle = this.monitorInfo.filter((item: any) => item.fuelGunId === val)[0]
  }
  // 搜索功能
  private search() {
    if (this.typeNum == 11) {
      this.keywords1 = this.searchWords
      this.stationTypeKey = ''
    }
    let streetCode = this.currStreet == '全部街道' ? undefined : this.currStreet
    let online = this.online.indexOf(2) > -1 && this.online.length == 1 ? 1 : this.online.indexOf(3) > -1 && this.online.length == 1 ? 0 : undefined
    switch (this.currIndex) {
      case 1: // 印刷厂数据
        this.socket.send(
          JSON.stringify({
            code: 2,
            token: getToken(),
            keywords: this.searchWords,
            streetCode: streetCode,
            online: online,
          })
        )
        break
      case 2: // 汽修数据
        this.socket.send(
          JSON.stringify({
            code: 10,
            token: getToken(),
            keywords: this.searchWords,
            streetCode: streetCode,
            online: online,
          })
        )
        break
      case 3: // 餐饮油烟数据
        this.socket.send(
          JSON.stringify({
            code: 17,
            token: getToken(),
            keywords: this.searchWords,
            streetCode: streetCode,
            online: online,
          })
        )
        break
      case 4: // 加油站数据
        this.socket.send(
          JSON.stringify({
            code: 8,
            token: getToken(),
            keywords: this.searchWords,
            streetCode: streetCode,
            online: online,
          })
        )
        break
      default:
        this.searchWord = this.searchWords
        break
    }
  }
  // 清空搜索框
  private clearSearch() {
    this.searchWords = ''
    this.search()
  }
  private jumpDetail() {
    this.$router.push({
      name: 'monitorDetails',
      params: {
        socketData: this.socketData,
        socket: this.socket,
        type: this.currIndex.toString(),
      },
    })
  }
  private jumpMoreVideo() {
    this.$router.push({
      name: 'moreVideo',
      params: {
        socketData: this.socketData,
        socket: this.socket,
        type: this.currIndex.toString(),
      },
    })
  }
  private changeCompany(val: any): void {
    if (this.currIndex === 1) {
      this.currCompany = this.currCompanyList.filter((item) => val === item.printFactoryId)[0]
      this.socket.send(
        JSON.stringify({
          code: 3,
          printFactoryId: this.currCompanyId,
          date: getNowTime().substr(0, 10),
          token: getToken(),
        })
      )
    } else if (this.currIndex === 2) {
      this.currCompany = this.currCompanyList.filter((item) => val === item.garageId)[0]
      this.socket.send(
        JSON.stringify({
          code: 11,
          garageId: this.currCompanyId,
          token: getToken(),
        })
      )
    } else if (this.currIndex === 4) {
      this.currCompany = this.currCompanyList.filter((item) => val === item.gasId)[0]
      this.socket.send(
        JSON.stringify({
          code: 7,
          gasId: this.currCompanyId,
          token: getToken(),
        })
      )
    } else if (this.currIndex === 3) {
      this.currCompany = this.currCompanyList.filter((item) => val === item.id)[0]
      this.socket.send(
        JSON.stringify({
          code: 21,
          token: getToken(),
          restaurantId: this.currCompanyId,
          date: getNowTime().substr(0, 10),
        })
      )
    }
  }

  private changeType(params: number) {
    this.type = params
    this.VocData.type = params
  }
  private clear() {
    this.currId = ''
    this.ezopen = ''
    this.isClose = true
  }
  private close() {
    this.isClose = false
  }
  private getCurSiteFromMap(value: any, id: any, projectId: any, alias: any) {
    Bus.$emit('curSiteChange', value, id, projectId, alias)
  }
  private curCompanyChange(value: any) {
    Bus.$emit('curCompanyChange', value)
  }
  private handleClickMaker(params: any) {
    console.log(params, '餐饮油烟父组件')
    if (params.type === 1) {
      this.ezopen = ''
      this.currId = params.data.printFactoryCamera.printFactoryId
      this.ezopen = params.data.printFactoryCamera.ezopen
      this.title = params.data.printFactoryName
      this.currCompanyId = params.data.printFactoryId
      this.currCompany = this.currCompanyList.filter((item) => params.data.printFactoryId === item.printFactoryId)[0]
      this.socket.send(
        JSON.stringify({
          code: 3,
          printFactoryId: this.currCompanyId,
          date: getNowTime().substr(0, 10),
          token: getToken(),
        })
      )
    } else if (params.type === 2) {
      this.currId = params.data.garageId
      /* eslint-disable @typescript-eslint/ban-ts-ignore */
      const curr = this.videoList.filter(
        //@ts-ignore
        (item) => item.garageId === params.data.garageId
      )
      /* eslint-disable @typescript-eslint/ban-ts-ignore */
      //@ts-ignore
      this.ezopen = curr.length ? curr[0].liveUrl : ''
      this.title = params.data.garageName
      this.currCompanyId = params.data.garageId
      this.currCompany = this.currCompanyList.filter((item) => params.data.garageId === item.garageId)[0]
      this.socket.send(
        JSON.stringify({
          code: 11,
          garageId: this.currCompanyId,
          token: getToken(),
        })
      )
    } else if (params.type === 4) {
      this.currCompanyId = params.data.gasId
      this.currCompany = this.currCompanyList.filter((item) => params.data.gasId === item.gasId)[0]
      this.socket.send(
        JSON.stringify({
          code: 7,
          gasId: this.currCompanyId,
          token: getToken(),
        })
      )
    } else if (params.type === 3) {
      this.currCompanyId = params.data.id
      this.currCompany = this.currCompanyList.filter((item) => params.data.id === item.id)[0]
      console.log('this.currCompanyId', this.currCompanyId)
      console.log('this.currCompany', this.currCompany)
      this.socket.send(
        JSON.stringify({
          code: 21,
          token: getToken(),
          restaurantId: this.currCompanyId,
          date: getNowTime().substr(0, 10),
        })
      )
    }
  }
  changeSmallMap() {
    this.isWx = !this.isWx
    this.cotrolsIndex = 0
  }

  /* 雷达控件选择 */
  controls(index: any) {
    const { cotrolsIndex } = this
    switch (index) {
      case 1:
        this.cotrolsIndex = index
        this.showselect = !this.showselect
        break
      case 2:
        this.lukuangIndex = this.lukuangIndex === 0 ? 2 : 0
        this.radarIndex = -1
        this.randaType = '消光系数'
        this.cotrolsIndex = 0
        break
      case 3:
        if (cotrolsIndex === index) {
          this.cotrolsIndex = 0
        } else {
          this.cotrolsIndex = index
        }
        break
      case 4:
        // if (cotrolsIndex === index) {
        //   this.cotrolsIndex = 0
        // }else{
        // this.cotrolsIndex = index
        // }

        break

      default:
        // if (cotrolsIndex === index) {
        //   this.cotrolsIndex = 0
        // }else{
        // this.cotrolsIndex = index
        // }

        break
    }
  }
  beforeDestroy() {
    localStorage.removeItem('isRadar')
  }
  created() {}
  mounted() {
    const { isRadar } = this.$route.params
    const { isSolid } = this.$route.params
    this.isRadar = isRadar
    if (isRadar) {
      this.getActiveIndex(10)
    }
    if (isSolid) {
      this.getActiveIndex(8)
      this.videoUrl = localStorage.getItem('videoUrl')
    }
    this.getStreet()
  }
  private getStreet() {
    getStreet().then((res) => {
      this.streetList = res.data.data || []
      this.streetList.unshift({
        streetCode: '全部街道',
        streetName: '全部街道',
      })
    })
  }
  private changeStreet(value: any): void {
    this.currStreet = value
    this.searchWords = ''
    this.search()
  }
  private handleOnline(params: any) {
    switch (params) {
      case 2:
        if (this.online.indexOf(2) > -1) {
          if (this.online.length == 2) {
            this.online.splice(this.online.indexOf(2), 1)
          }
        } else {
          this.online.push(2)
        }
        break
      case 3:
        if (this.online.indexOf(3) > -1) {
          if (this.online.length == 2) {
            this.online.splice(this.online.indexOf(3), 1)
          }
        } else {
          this.online.push(3)
        }
        break
    }
    /* eslint-disable @typescript-eslint/ban-ts-ignore */
    //@ts-ignore
    if (this.currIndex !== 5 || this.currIndex !== 0) {
      this.search()
    }
  }
  private handleOnlineF(params: any) {
    switch (params) {
      case 1:
        if (this.online.indexOf(1) > -1) {
          if (this.online.length >= 2) {
            this.online.splice(this.online.indexOf(1), 1)
          }
        } else {
          this.online.push(1)
        }
        break
      case 2:
        if (this.online.indexOf(2) > -1) {
          if (this.online.length >= 2) {
            this.online.splice(this.online.indexOf(2), 1)
          }
        } else {
          this.online.push(2)
        }
        break
      case 3:
        if (this.online.indexOf(3) > -1) {
          if (this.online.length >= 2) {
            this.online.splice(this.online.indexOf(3), 1)
          }
        } else {
          this.online.push(3)
        }
        break
    }

    /* eslint-disable @typescript-eslint/ban-ts-ignore */
    //@ts-ignore
    if (this.currIndex !== 5 || this.currIndex !== 0) {
      this.search()
    }
  }
  ChDate(item: any, index: number) {
    this.radarItem = item
    this.radarIndex = index
    this.randaType = '消光系数'
  }
  goVideo() {
    this.$router.push('/pollutionVideo')
    localStorage.setItem('videoUrl', this.videoUrl)
  }
  //获取小视频推流路径
  setVideo(value: any) {
    this.videoUrl = value || ''
  }

  cleanKeyword(type: any) {
    this.searchWords = ''
    this.keywords1 = ''
    this.stationTypeKey = type
  }
}
</script>
