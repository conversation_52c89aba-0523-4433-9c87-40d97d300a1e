<template>
  <div class="analysisAir-container">
    <div class="container-bg"></div>
    <analysisMap
      class="center-map"
      :markerList="markerList"
      :distance="distance"
      :stationList="stationList"
      @handleClickCompanyMarker="handleClickMore"
      @handleChangeStation="handleChangeStation"
    ></analysisMap>
    <div class="right-container-box">
      <!-- 监测趋势 -->
      <div style="height: 36%" class="right-item-box right-to-left">
        <div class="video-main polluted-video">
          <div class="common-title box-title" style="position: relative">
            <div class="title">{{ `监测趋势` }}</div>
          </div>
          <div class="sub-title" style="margin: 0">
            <img src="@/assets/biaoti.png" alt class="title-img" />
            <div
              class="sub_tabs1"
              v-if="subTabs && subTabs.length"
              style="margin-top: 0.1rem"
            >
              <div
                v-for="tab in subTabs"
                :key="tab.value"
                class="tab"
                @click="itemCode = tab.value"
                :class="{
                  active: tab.value === itemCode,
                }"
                :title="tab.name"
              >
                {{ tab.name }}
              </div>
            </div>
            <chart
              :propData="propData"
              :id="'HCLineC'"
              :width="'4rem'"
              :height="'2.45rem'"
              :smooth="true"
            />
          </div>
        </div>
      </div>
      <!-- 成因分析 -->
      <div style="height: 30%" class="right-item-box left-to-right">
        <div class="video-main polluted-video">
          <div class="common-title box-title" style="position: relative">
            <div class="title">{{ `成因分析` }}</div>
          </div>
          <div class="sub-title" style="margin: 0">
            <img src="@/assets/biaoti.png" alt class="title-img" />
          </div>
          <div class="cause-analysis-container">
            <div
              class="cause-analysis-box"
              v-if="analyseInfo.analysisBasis || analyseInfo.analysisConclusion"
            >
              <p class="cause-analysis-title">分析依据</p>
              <div
                class="cause-analysis-content"
                v-html="analyseInfo.analysisBasis || '--'"
              ></div>
              <p class="cause-analysis-title">分析结论</p>
              <p class="cause-analysis-content">
                {{ analyseInfo.analysisConclusion || "--" }}
              </p>
            </div>
            <div style="width: 100%; text-align: center" v-else>
              <img src="@/assets/<EMAIL>" alt="" />
              <p
                style="
                  height: 15px;
                  font-size: 14px;
                  font-family: PingFang SC;
                  font-weight: 500;
                  color: #d8e8fe;
                "
              >
                暂无数据
              </p>
            </div>
          </div>
        </div>
      </div>
      <!-- 涉污企业指纹库 -->
      <div
        style="height: 36%"
        class="right-item-box right-to-left"
        v-if="gcLat && gcLng"
      >
        <div class="video-main polluted-video">
          <div class="common-title box-title" style="position: relative">
            <div class="title">涉污企业指纹库</div>
            <a-select
              style="width: 1rem"
              @change="changeDistance"
              v-model="distance"
              class="select-main"
            >
              <a-icon
                slot="suffixIcon"
                type="caret-down"
                style="color: rgba(0, 234, 255, 1); width: 14px; height: 8px"
              />
              <a-select-option
                v-for="item in distanceList"
                :key="item.name"
                :value="item.value"
              >
                {{ item.name }}
              </a-select-option>
            </a-select>
          </div>
          <div class="sub-title" style="margin: 0">
            <img src="@/assets/biaoti.png" alt class="title-img" />
          </div>
          <div class="fingerprint-container" ref="fingerprintContainer">
            <template v-if="stationList && stationList.length">
              <div
                class="fingerprint-item"
                v-for="(item, index) in stationList"
                :key="item.stationCode"
                @click="handleClickMore(item)"
              >
                <div class="fingerprint-title">
                  <div class="serial-number">
                    <span>{{
                      index >= 9 ? index + 1 : "0" + (index + 1)
                    }}</span>
                  </div>
                  <div class="title" :title="item.stationName || item.name">
                    {{
                      item.stationName
                        ? item.stationName
                        : item.name
                        ? item.name
                        : "--"
                    }}
                  </div>
                  <div class="more">更多>></div>
                </div>
                <div class="fingerprint-content">
                  <div class="title">
                    <span
                      >污染源类型：{{
                        pollutionTypeObj[item.pollutionType] || "--"
                      }}</span
                    >
                    <!-- <span
                      >设备状态：{{
                        item.devieList &&
                        item.devieList.length &&
                        item.devieList[0].isOnline
                          ? "在线"
                          : "离线"
                      }}</span
                    > -->
                    <!-- <span>{{ item.manItem.name || "--" }}：</span>
                    <template v-if="item.online">
                      <span
                        :style="{
                          color: !item.online
                            ? ''
                            : item.isAlarm
                            ? '#F5542F'
                            : '#26FFED',
                          fontFamily: 'YouSheBiaoTiHei',
                        }"
                        >{{
                          item.manItem.value ||
                          item.manItem.value === 0 ||
                          item.manItem.value === "0"
                            ? item.manItem.value
                            : "--"
                        }}</span
                      >
                      <span>{{ item.manItem.unit }}</span>
                    </template>
                    <span v-else>设备离线</span> -->
                  </div>
                  <div class="position">
                    <img src="@/assets/position.png" alt="" />
                    <span style="margin: 0 5px">距此</span>
                    <span>{{ item.distance }}</span>
                    <span>km</span>
                  </div>
                </div>
              </div>
            </template>
            <template v-else>
              <div
                style="
                  width: 100%;
                  height: 100%;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                "
              >
                <div style="width: 100%; text-align: center">
                  <img src="@/assets/<EMAIL>" alt="" />
                  <p
                    style="
                      height: 15px;
                      font-size: 14px;
                      font-family: PingFang SC;
                      font-weight: 500;
                      color: #d8e8fe;
                    "
                  >
                    周边暂无涉污企业
                  </p>
                </div>
              </div>
            </template>
          </div>
        </div>
      </div>
    </div>
    <mapControlBox @handleGetSelectItem="handleGetSelectItem" @closeDialogVisible="closeDialogVisible" :close="mapBoxShow1"></mapControlBox>
    <mapControlBoxArea @handleGetSelectArea="handleGetSelectArea" @closeDialogVisible="closeDialogVisible" :close="mapBoxShow2"></mapControlBoxArea>
    <chartDialog
      :visibles="visibles"
      :currentStation="currentStation"
      @handleClose="handleClose"
    ></chartDialog>
    <!-- 污染源详情 -->
    <div ref="modal" style="width:100%;height:100%">
      <a-modal
        v-if="dialogVisible"
        :visible.sync="dialogVisible"
        class="ant-modal-recheck"
        :footer="null"
        :maskClosable="true"
        :centered="true"
        @cancel="handleCancel"
        style="top: 0.2rem !important;"
        :getContainer="() => $refs.modal"
      >
        <div class="content">
          <pollutionSourcesDetail :id="companyId" />
        </div>
      </a-modal>
    </div>

      <section
        :class="{
          'air-station-main': true,
          'div-transform-right': displayState,
        }"
      >
        <div class="list-air">
          <!-- <input type="checkbox" v-model="stationType" value="3" /> -->
          <img
            src="@/assets/heavily/<EMAIL>"
            alt=""
            class="gou"
            @click="siteTypeChange(4)"
            v-if="!stationTypeState[3]"
          />
          <img
            src="@/assets/gou_active.png"
            alt=""
            class="gou"
            @click="siteTypeChange(4)"
            v-if="stationTypeState[3]"
          />
          <div style="display: inline-block">国控</div>
          <div>{{ stationData.gk }}个</div>
          <div style="width: 0.27rem; display: flex; justify-content: center">
            <img src="@/assets/<EMAIL>" alt style="width: 0.27rem" />
          </div>
        </div>
        <div class="list-air">
          <!-- <input type="checkbox" v-model="stationType" value="3" /> -->
          <img
            src="@/assets/heavily/<EMAIL>"
            alt=""
            class="gou"
            @click="siteTypeChange(3)"
            v-if="!stationTypeState[2]"
          />
          <img
            src="@/assets/gou_active.png"
            alt=""
            class="gou"
            @click="siteTypeChange(3)"
            v-if="stationTypeState[2]"
          />
          <div style="display: inline-block">市控</div>
          <div>{{ stationData.sk }}个</div>
          <div style="width: 0.27rem; display: flex; justify-content: center">
            <img src="@/assets/skz1.png" alt style="width: 0.27rem" />
          </div>
        </div>
        <div class="list-air">
          <!-- <input type="checkbox" v-model="stationType" value="1" /> -->
          <img
            src="@/assets/heavily/<EMAIL>"
            alt=""
            class="gou"
            @click="siteTypeChange(2)"
            v-if="!stationTypeState[1]"
          />
          <img
            src="@/assets/gou_active.png"
            alt=""
            class="gou"
            @click="siteTypeChange(2)"
            v-if="stationTypeState[1]"
          />
          <div style="display: inline-block">区控</div>
          <div>{{ stationData.qk }}个</div>
          <div style="width: 0.27rem; display: flex; justify-content: center">
            <img src="@/assets/qkz1.png" alt style="width: 0.27rem" />
          </div>
        </div>
        <div class="list-air">
          <!-- <input type="checkbox" v-model="stationType" value="2" /> -->
          <img
            src="@/assets/heavily/<EMAIL>"
            alt=""
            class="gou"
            @click="siteTypeChange(1)"
            v-if="!stationTypeState[0]"
          />
          <img
            src="@/assets/gou_active.png"
            alt=""
            class="gou"
            @click="siteTypeChange(1)"
            v-if="stationTypeState[0]"
          />
          <div style="display: inline-block">微站</div>
          <div>{{ stationData.wk }}个</div>
          <div style="width: 0.27rem; display: flex; justify-content: center">
            <img
              src="@/assets/wkz1.png"
              alt
              style="width: 0.27rem; height: 0.22rem"
            />
          </div>
        </div>
      </section>
  </div>
</template>

<script>
import pollutionSourcesDetail from "../../pollutionSourcesDetail/index";
import analysisMap from "./components/map.vue";
import chart from "./components/chart.vue";
import chartDialog from "./components/dialog.vue";
import { Select, Icon } from "ant-design-vue";
import { rangeByPollutionSource } from "@/api/practiceCheck";
import mapControlBox from "./components/mapControlBox";
import mapControlBoxArea from "./components/mapControlBoxArea";
import {
  getAllAirStationList,
  listStationItem,
  stationItemMonitorTrendHour,
  consultStationItemMonitorTrendHour,
  listStationRangeEnterprise,
  originAnalyse,
} from "@/api/analysisAir";
import { getStatistics } from "@/api/homeTable";
export default {
  name: "",
  props: {
    currIndex: {
      type: Number,
      default: 100,
    },
    keywords: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      subTabs: [],
      itemCode: "",
      HCLineType: null,
      markerList: [],
      stationCode: null,
      propData: {
        currentData: [],
        averagegData: [],
        itemName: "AQI",
        unit: "",
      },
      stationList: [],
      distanceList: [
        { name: "0.5公里", value: "500" },
        { name: "1公里", value: "1000" },
        { name: "1.5公里", value: "1500" },
        { name: "2公里", value: "2000" },
      ],
      distance: "500",
      analyseInfo: {},
      visibles: false,
      currentStation: null,
      dialogVisible: false,
      companyId: -1,
      gcLat: null,
      gcLng: null,
      typeList: [0,1,2,3,4],
      // 污染源类型 0 汽修 1 工地源 2 工业源 3加油站 4 停车场
      pollutionTypeObj: {
        0: "汽修源",
        1: "工地源",
        2: "工业源",
        3: "加油站",
        4: "停车场",
      },
      stationTypeState: [true, true, true, true],
      stationType : [1, 2, 3, 4],
      stationData:{},
      displayState :false,
      districtCode:[],
      mapBoxShow1:1,
      mapBoxShow2:1
    };
  },
  created() {
    // this.getList();
    this.handleGetRangeByPollutionSourceList(1);

    this.getStatistics()
  },
  methods: {
    // 国控，微控等数据
    getStatistics() {
      const params = {
        districtCode: this.districtCode.join(",")
      }
      getStatistics(params).then((res) => {
        this.stationData = res.data.data;
      });
    },
    handleCancel() {
      this.dialogVisible = false;
    },
    // 地图图层数据改变
    handleGetSelectItem(list) {
      this.typeList = list;
    },
    // 地图图层（地图图层关闭）展示
    closeDialogVisible(){
      this.handleCancel()
    },
    // 地图区域改变
    handleGetSelectArea(list) {
      this.districtCode=list
      const districtCode = list.join(",")
      this.getStatistics()
      this.getList(districtCode)
      // 污染企业
      // this.handleGetRangeByPollutionSourceList(1)
    },
    // 获取所有站点列表（空气站点）
    getList(districtCode = "") {
      let stationType=this.stationType.join(",")
      getAllAirStationList({districtCode,stationTypeId:stationType})
        .then((res) => {
          if (res.data.code === 200) {
            this.markerList = res.data.data || [];
          } else {
            this.markerList = [];
          }
        })
        .catch((_) => {
          this.markerList = [];
        });
    },
    // 切换站点
    handleChangeStation({ stationCode, gcLng, gcLat,type }) {
      console.log('2222222222222222255555555555555555555555',type)
      this.stationCode = stationCode;
      this.gcLat = gcLat;
      this.gcLng = gcLng;
      this.getOriginAnalyse();
      // 点击站点改变时请求污染企业
      // if(type){
        this.handleGetRangeByPollutionSourceList();
        // this.keywords=''
        // 置空keywords
      // }
      const params = {
        stationId: stationCode,
      };
      listStationItem(params)
        .then((res) => {
          if (res.data.code === 200) {
            this.subTabs = res.data.data || [];
            if (this.subTabs.length) {
              this.itemCode = this.subTabs[0].value;
              this.getStationItemMonitorTrendHour();
              this.getConsultStationItemMonitorTrendHour();
            }
          } else {
            this.subTabs = [];
          }
        })
        .catch((_) => {
          this.subTabs = [];
        });
    },
    // 获取站点24小时站点监测列表
    getStationItemMonitorTrendHour() {
      const params = {
        stationId: this.stationCode,
        itemCode: this.itemCode,
      };
      stationItemMonitorTrendHour(params)
        .then((res) => {
          if (res.data.code === 200) {
            this.propData.currentData = res.data.data.dataList || [];
          } else {
            this.propData.currentData = [];
          }
        })
        .catch((_) => {
          this.propData.currentData = [];
        });
    },
    // 获取站点均值 参考站点监测值列表微控 周围三公里 区控 全部区控，市控，国控，全部市控国控
    getConsultStationItemMonitorTrendHour() {
      const params = {
        stationId: this.stationCode,
        itemCode: this.itemCode,
      };
      consultStationItemMonitorTrendHour(params)
        .then((res) => {
          if (res.data.code === 200) {
            this.propData.averagegData = res.data.data || [];
          } else {
            this.propData.averagegData = [];
          }
        })
        .catch((_) => {
          this.propData.averagegData = [];
        });
    },
    // 获取指定站点附近污染企业站点列表
    getListStationRangeEnterprise() {
      const params = {
        stationId: this.stationCode,
        distance: this.distance,
        enterpriseType: "5,6,7",
      };
      listStationRangeEnterprise(params)
        .then((res) => {
          if (res.data.code === 200) {
            this.stationList = res.data.data || [];
          } else {
            this.stationList = [];
          }
        })
        .catch((_) => {
          this.stationList = [];
        })
        .finally((_) => {
          this.scrollTOTop();
        });
    },
    // 切换距离
    changeDistance() {
      this.handleGetRangeByPollutionSourceList();
    },
    // 空气站点成因分析
    getOriginAnalyse() {
      const params = {
        stationId: this.stationCode,
      };
      originAnalyse(params)
        .then((res) => {
          if (res.data.code === 200) {
            this.analyseInfo = res.data.data || {};
            if (this.analyseInfo.analysisBasis) {
              this.analyseInfo.analysisBasis = this.analyseInfo.analysisBasis
                .replace(/\n\n/gi, "\n")
                .replace(/\n/gi, "<br/>");
            }
          } else {
            this.analyseInfo = {};
          }
        })
        .catch((_) => {
          this.analyseInfo = [];
        });
    },
    // 获取站点列表
    handleGetRangeByPollutionSourceList(type) {
      let params={}
      const districtCode =  this.districtCode.join(",")
      if(type){
        params = {
          distance: this.distance,
          keywords:this.keywords,
          districtCode
        };
      }else{
       params = {
        distance: this.distance,
        lng: this.gcLng,
        lat: this.gcLat,
        keywords:this.keywords,
        districtCode
      };
      }

      rangeByPollutionSource(params)
        .then((res) => {
          if (res.data.code == 200) {
            this.baseStationList = res.data.data || [];
            if(res.data.data && res.data.data.length>0){
              this.gcLat = res.data.data[0].lat;
              this.gcLng = res.data.data[0].lng;
              this.filterData();
            }else{
              this.stationList = [];
            }
            // this.filterData();
          } else {
            this.stationList = [];
            this.baseStationList = [];
          }
        })
        .catch((_) => {
          this.stationList = [];
          this.baseStationList = [];
        })
        .finally((_) => {
          this.scrollTOTop();
        });
    },
    // 滚动到顶部
    scrollTOTop() {
      const fingerprintContainer = this.$refs.fingerprintContainer;
      if (!fingerprintContainer) return;
      fingerprintContainer.scrollTop = 0;
    },
    // 污染源详情打开
    handleClickMore(item) {
      this.dialogVisible = true;
      this.companyId = item.id;
      this.mapBoxShow1++
      this.mapBoxShow2++
      // this.currentStation = item
      // this.visibles = true
    },
    handleClose() {
      this.visibles = false;
    },

    // 地图图层值改变
    filterData() {
      if (!this.baseStationList || this.baseStationList.length === 0) return;
      if (!this.typeList || this.typeList.length === 0) {
        this.stationList = [];
        return;
      }
      this.stationList = [];
      this.baseStationList.forEach((item) => {
        if (this.typeList.includes(item.pollutionType)) {
          this.stationList.push(item);
        }
      });
    },


  // 站点类型选择
   siteTypeChange(index) {
    if (index == 1) {
      this.stationTypeState[0] = !this.stationTypeState[0];
    } else if (index == 2) {
      this.stationTypeState[1] = !this.stationTypeState[1];
    } else if (index == 3) {
      this.stationTypeState[2] = !this.stationTypeState[2];
    } else if (index == 4) {
      this.stationTypeState[3] = !this.stationTypeState[3];
    }
    const stationType = [];
    this.stationTypeState.forEach((item, index) => {
      if (item) {
        stationType.push(index + 1);
      }
    });
    this.stationType = stationType;

    const districtCode = this.districtCode.join(",")
    this.getList(districtCode)
  }
  },
  computed: {},
  watch: {
    itemCode() {
      this.getStationItemMonitorTrendHour();
      this.getConsultStationItemMonitorTrendHour();
      this.propData.itemName = this.subTabs.find(
        (item) => item.value === this.itemCode
      ).name;
      this.propData.unit = this.subTabs.find(
        (item) => item.value === this.itemCode
      ).unit;
    },
    typeList: {
      handler(newVal) {
        this.filterData();
      },
      deep: true,
      immediate: true,
    },
   keywords: {
      handler(newVal, oldVal) {
        console.log(newVal,'--------------------------newVal',oldVal,'oldVal');
        if (newVal || newVal!==oldVal) {
          this.$nextTick(() => {
           this.handleGetRangeByPollutionSourceList(1);
          })
        }
      },
    },
  },
  components: {
    analysisMap,
    chart,
    ASelect: Select,
    AIcon: Icon,
    ASelectOption: Select.Option,
    chartDialog,
    pollutionSourcesDetail,
    mapControlBox,
    mapControlBoxArea,
  },
};
</script>

<style lang="less" scoped>
.analysisAir-container {
  width: 100%;
  height: 100%;
  position: relative;
  .container-bg {
    position: absolute;
    pointer-events: none;
    z-index: 2;
    width: 100%;
    height: 100%;
    background-size: 100% 100%;
    opacity: 0.8;
    background-image: url("~@/assets/department/<EMAIL>");
  }
  .center-map {
    position: absolute;
    height: calc(1080px - 0.94rem);
    // margin-top: 0.15rem;
    > div {
      width: 100%;
      height: 100%;
    }
  }
  .right-container-box {
    position: absolute;
    pointer-events: auto;
    overflow: hidden;
    z-index: 9;
    top: 0;
    right: 0;
    height: calc(1080px - 0.94rem);
    width: 4.6rem;
    padding: 0.3rem 0.3rem 0;
    background-image: url(~@/assets/air_bg.png) !important;
    background-size: 100% 100% !important;
    // width: 200px;
    // background-color: pink;
    .right-item-box {
      .sub-title {
        > img {
          width: 50%;
          height: 0.1rem;
        }
      }
      .sub_tabs1 {
        display: flex;
        height: 0.3rem;
        line-height: 0.26rem;
        border: 0.5px solid #0e2344;
        box-sizing: border-box;
        width: 100%;
        margin-top: 0.1rem;
        margin-bottom: 0.05rem;
        .tab {
          font-size: 0.12rem;
          font-family: PingFang SC;
          font-weight: 500;
          color: #ffffff;
          background: #0a204a;
          cursor: pointer;
          text-align: center;
          overflow: hidden; // 超出边框外隐藏
          text-overflow: ellipsis; // 属性规定当文本溢出包含元素时发生的事情。
          white-space: nowrap; // 规定段落中的文本不进行换行
          flex: 1;
        }
        .active {
          background-color: #2e8ef0;
        }
      }
    }
  }

  .cause-analysis-container {
    background: url("~@/assets/<EMAIL>") 100% 100% no-repeat;
    // border-image:url("~@/assets/<EMAIL>") 100 100 stretch;
    margin-top: 20px;
    padding: 20px 30px;
    width: 402px;
    height: 179px;
    .cause-analysis-box {
      width: 100%;
      height: 100%;
      overflow-y: auto;
      box-sizing: border-box;
      padding-right: 10px;
      &::-webkit-scrollbar-track {
        background: rgb(239, 239, 239);
        background: transparent;
        border-radius: 2px;
      }

      &::-webkit-scrollbar-thumb {
        background: #469fe78f;
        border-radius: 0.05rem;
      }

      &::-webkit-scrollbar-thumb:hover {
        background: #469fe7;
      }

      &::-webkit-scrollbar-corner {
        background: #469fe7;
      }
    }
    p {
      margin: 0;
    }
    .cause-analysis-title {
      font-family: PingFangSC-Medium;
      color: #38bafe;
      font-size: 14px;
      font-weight: bold;
    }
    .cause-analysis-content {
      font-size: 14px;
      color: #c2e2fa;
    }
  }
  .fingerprint-container {
    width: 100%;
    height: 260px;
    overflow-y: auto;
    &::-webkit-scrollbar {
      display: none;
    }
    .fingerprint-item {
      cursor: pointer;
      margin-bottom: 10px;
      width: 100%;
      // border: 1px solid #0E73BA;
      background: url("~@/assets/<EMAIL>") 100% 100% no-repeat;
      padding: 8px 14px;
      .fingerprint-title {
        margin-left: 20px;
        display: flex;
        position: relative;
        .serial-number {
          width: 30px;
          height: 24px;
          display: flex;
          justify-content: center;
          align-items: center;
          background: url("~@/assets/<EMAIL>");
          span {
            font-size: 16px;
            font-family: YouSheBiaoTiHei;
            font-weight: 400;
            color: #dd4545;
            background: linear-gradient(0deg, #43bcf4 0%, #9ed5ff 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
        }
        .title {
          flex: 1;
          font-size: 14px;
          font-family: Source Han Sans CN;
          font-weight: 500;
          color: #c2e2fa;
          margin-left: 12px;
          white-space: nowrap;
          width: 100%;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .more {
          font-size: 10px;
          font-family: PingFang SC;
          font-weight: 400;
          color: #738999;
          cursor: pointer;
        }
      }
      .fingerprint-content {
        display: flex;
        .title {
          flex: 1;
          margin-left: 62px;
          font-size: 14px;
          font-family: PingFang SC;
          font-weight: 500;
          color: #8dabc0;
        }
        .position {
          font-size: 12px;
          font-family: PingFang SC;
          font-weight: 400;
          color: #abc9e0;
          display: flex;
          align-items: center;
        }
      }
    }
  }
}
// 右到左
.right-to-left {
  animation: pu-randa-r2l 1.5s linear;
}
@keyframes pu-randa-r2l {
  0% {
    transform: translate(120%, 0);
  }
  50% {
    transform: translate(120%, 0);
  }
  100% {
    transform: translate(0, 0);
  }
}
// 左到右
.left-to-right {
  animation: pu-randa-l2r 1.5s linear;
}
@keyframes pu-randa-l2r {
  0% {
    transform: translate(-120%, 0);
  }
  50% {
    transform: translate(-120%, 0);
  }
  100% {
    transform: translate(0, 0);
  }
}

.air-station-main {
  transition: 1.5s;
  width: 1.6rem;
  height: 1.3rem;
  // background: rgba(23, 56, 92, 0.5);
  position: absolute;
  bottom: 0.8rem;
  right: 4.5rem;
  z-index: 100;
  .gou {
    width: 0.16rem;
    margin-right: 0.1rem;
    height: 0.16rem;
  }
  .list-air {
    display: flex;
  }
  > div {
    display: inline-block;
    align-items: center;
    margin-top: 0.06rem;
    > input {
      margin-right: 0.1rem;
      width: 0.16rem;
      height: 0.16rem;
    }
    > :nth-of-type(2) {
      width: 0.5rem;
      padding-left: 8px;
    }
  }
  padding: 0.05rem 0 0 0.25rem;
}
</style>
