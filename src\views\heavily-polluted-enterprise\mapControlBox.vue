<template>
  <div class="marker-control-box" :style="{ right: '4.8rem' }">
    <!-- 按钮 S -->
    <div
      class="flex items-center marker-control-btn"
      @click="showIconList = !showIconList"
    >
      <div class="marker-control-btn-icon"></div>
      <span>地图图层</span>
    </div>
    <!-- 按钮 E -->

    <!-- 图标选择弹窗 S -->

    <transition
      :enter-active-class="enterClass"
      :leave-active-class="leaveClass"
    >
      <div v-show="showIconList" class="icon-selecte-box">
        <!-- <a-checkbox
          v-model="allSelectItem"
          class="check-item"
          @click="selectAllChange"
        >
          <div class="flex items-center">
            <span class="name flex flex-1">全选</span>
          </div>
        </a-checkbox> -->
        <a-checkbox
          v-for="item in iconList"
          :key="item.name"
          v-model="item.selected"
          class="check-item"
          @change="checkChange(item.index)"
        >
          <div class="flex items-center">
            <img :src="item.icon" />
            <span class="name flex flex-1">{{ item.name }}</span>
          </div>
        </a-checkbox>
      </div>
    </transition>
    <!-- 图标选择弹窗 E -->
  </div>
</template>

<script>
import { Checkbox } from "ant-design-vue";
import gficon from "@/assets/images/<EMAIL>"; //固废
import ldicon from "@/assets/images/<EMAIL>"; //雷达
import jkicon from "@/assets/images/<EMAIL>"; //监控
import syicon from "@/assets/images/<EMAIL>"; //事业
import gdicon from "@/assets/images/<EMAIL>"; //工地
import zsicon from "@/assets/images/<EMAIL>"; //噪声
import cyicon from "@/assets/images/<EMAIL>"; //餐饮
import pwicon from "@/assets/images/<EMAIL>"; //排污
import dlicon from "@/assets/images/<EMAIL>"; //电量
import airAnalysis from "@/assets/images/airAnalysis.png"; //大气污染源分析
import truckicon from "@/assets/images/-s-hyc.png"; //大气污染源分析
export default {
  components: {
    ACheckbox: Checkbox,
  },
  props: {
    typeNum: {
      type: Number,
      default: 0,
    },
  },
  watch: {
    typeNum: {
      handler(newValue) {
        switch (newValue) {
          case 0:
            this.iconList = [
              {
                name: "建筑工地",
                icon: gdicon,
                index: 0,
                selected: true,
              },
              {
                name: "印刷企业",
                icon: gdicon,
                index: 1,
                selected: false,
              },
              {
                name: "汽修行业",
                icon: gdicon,
                index: 2,
                selected: false,
              },
              {
                name: "餐饮油烟",
                icon: cyicon,
                index: 3,
                selected: false,
              },
              {
                name: "电力监管",
                icon: dlicon,
                index: 7,
                selected: false,
              },
              // {
              //   name: '固废回收',
              //   icon: gficon,
              //   index: 8,
              //   selected: false,
              // },
              {
                name: "机动车尾气",
                icon: truckicon,
                index: 13,
                selected: false,
              },
              // {
              //   name: '环境噪声',
              //   icon: zsicon,
              //   index: 6,
              //   selected: false,
              // },
            ];
            break;
          case 8:
            this.iconList = [
              {
                name: "危废监管",
                icon: gficon,
                index: 8,
                selected: false,
              },
            ];
            break;
          case 9:
            this.iconList = [
              {
                name: "排污企业",
                icon: pwicon,
                index: 9,
                selected: false,
              },
            ];
            break;

          case 11:
            this.iconList = [
              {
                name: "全部",
                icon: pwicon,
                index: 100,
                selected: true,
              },
              {
                name: "汽修源",
                icon: gdicon,
                index: 101,
                selected: false,
              },
              {
                name: "工地源",
                icon: ldicon,
                index: 102,
                selected: false,
              },
              {
                name: "工业源",
                icon: syicon,
                index: 103,
                selected: false,
              },
              {
                name: "加油站源",
                icon: dlicon,
                index: 104,
                selected: false,
              },
              {
                name: "重型停车场",
                icon: jkicon,
                index: 105,
                selected: false,
              },
              // {
              //   name: "大气环境综合分析",
              //   icon: airAnalysis,
              //   index: 11,
              //   selected: false,
              // },
              // {
              //   name: "水环境综合分析",
              //   icon: airAnalysis,
              //   index: 12,
              //   selected: false,
              // },
            ];
            break;
          default:
            this.iconList = [];
            break;
        }
        this.checkChange(newValue);
        this.showIconList = false;
      },
      immediate: true,
    },
  },
  data() {
    return {
      showIconList: false,
      iconList: [
        {
          name: "建筑工地",
          icon: gdicon,
          index: 0,
          selected: true,
        },
        {
          name: "印刷企业",
          icon: gdicon,
          index: 1,
          selected: false,
        },
        {
          name: "汽修行业",
          icon: gdicon,
          index: 2,
          selected: false,
        },
        {
          name: "餐饮油烟",
          icon: cyicon,
          index: 3,
          selected: false,
        },
        // {
        //   name: '环境噪声',
        //   icon: zsicon,
        //   index: 6,
        //   selected: false,
        // },
        {
          name: "用电监管",
          icon: dlicon,
          index: 7,
          selected: false,
        },
        // {
        //   name: '固废回收',
        //   icon: gficon,
        //   index: 8,
        //   selected: false,
        // },
        // {
        //   name: '排污企业',
        //   icon: pwicon,
        //   index: 9,
        //   selected: false,
        // },
        {
          name: "机动车尾气",
          icon: truckicon,
          index: 13,
          selected: false,
        },
        // {
        //   name: '激光雷达',
        //   icon: ldicon,
        //   index: 10,
        //   selected: false,
        // },
        // {
        //   name: '大气环境综合分析',
        //   icon: airAnalysis,
        //   index: 11,
        //   selected: false,
        // },
        // {
        //   name: '水环境综合分析',
        //   icon: airAnalysis,
        //   index: 12,
        //   selected: false,
        // },
        // {
        //   name: '企事业单位',
        //   icon: syicon,
        //   index: 5,
        //   selected: false,
        // },
        // {
        //   name: '视频监控',
        //   icon: jkicon,
        //   index: 99,
        //   selected: false,
        // },
      ],
      allSelectItem: [],
      leaveClass: "animate__animated animate__fadeOutRight",
      enterClass: "animate__animated animate__fadeInRight",
      activeIndex: 0,
    };
  },
  methods: {
    selectAllChange() {},
    checkChange(index) {
      this.iconList.forEach((v) => {
        if (v.index != index) {
          v.selected = false;
        } else {
          v.selected = true;
        }
      });
      if (this.activeIndex === index) return;
      this.activeIndex = index;
      this.showIconList = !this.showIconList;
      if (index === 99) {
        this.$router.push("/pollutionVideo");
        return;
      }
      this.$emit("postActiveIndex", this.activeIndex);
    },
  },
};
</script>

<style lang="less" scoped>
.flex {
  display: flex;
}
.items-center {
  justify-items: center;
  align-items: center;
  vertical-align: middle;
}
.marker-control-box {
  position: fixed;
  width: 1.5rem;
  height: 0.4rem;
  top: 1.55rem;
  background: url(../../assets/images/<EMAIL>) no-repeat center center;
  background-size: 100% 100%;
  padding: 11px 32px;
  z-index: 2999;
  // transition-property: right;
  // transition-duration: .5s;

  .marker-control-btn {
    width: 100%;
    height: 100%;
    cursor: pointer;

    span {
      font-size: 14px;
      font-weight: bold;
      color: #ffffff;
    }
  }

  .marker-control-btn-icon {
    width: 17px;
    height: 15px;
    background: url(../../assets/images/<EMAIL>) no-repeat center
      center;
    background-size: 100% 100%;
    margin-right: 9px;
    margin-top: 2px;
  }

  .icon-selecte-box {
    position: absolute;
    padding: 5px 15px 5px 20px;
    margin-top: 20px;
    left: -9px;
    width: 180px;
    background-color: rgba(0, 4, 18, 0.56);
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    border-top: 2px solid #3883df;
    border-bottom: 2px solid #3883df;
  }
}

.check-item {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  // margin-left: 0;
  margin: 10px 0;

  img {
    width: 16px;
    height: 14px;
    margin: 2px 10px 0 10px;
  }

  .name {
    display: inline-block;
    color: #caf6ff;
    font-size: 14px;
  }
}
</style>
