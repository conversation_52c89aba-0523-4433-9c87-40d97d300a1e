<template>
  <div :id="id" :style="{ height: height, width: width }" />
</template>
<script lang="ts">
import * as echarts from 'echarts'
import { Component, Prop, Watch } from "vue-property-decorator";
import { mixins } from "vue-class-component";
import ResizeMixin from "./mixins/resize";
@Component({
  name: "VocLineChart"
})
export default class extends mixins(ResizeMixin) {
  @Prop({ default: "chart" }) private id!: string;
  @Prop({ default: "200px" }) private width!: string;
  @Prop({ default: "200px" }) private height!: string;
  @Prop({ required: true }) private VocData!: any;
  @Watch("VocData", { immediate: true, deep: true })
  public onMsgChanged(newValue: any, oldValue: any) {
    this.VocData = newValue;
    if (this.chart) {
      this.chart.clear();
    }
    this.$nextTick(() => {
      this.initChart();
    });
  }
  mounted() {
    this.$nextTick(() => {
      this.initChart();
      console.log(this.VocData, 'VocData')
    });
  }
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.clear();
    this.chart.dispose();
    this.chart = null;
  }
  private initChart() {

    if(this.chart === null || this.chart === undefined) {
      this.chart = echarts.init(
        document.getElementById(this.id) as HTMLDivElement
      );
    }
    this.chart.setOption({
      grid: {
        top: "20%",
        left: "7%",
        bottom: "15%",
        right: "5%"
      },
      legend: {
        right: "5%",
        textStyle: {
          color: "#fff"
        }
      },
      tooltip: {
        trigger: "axis"
      },
      xAxis: {
        type: "category",
        axisLine: {
          lineStyle: {
            color: "gray"
          }
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: "#fff"
        },
        boundaryGap: false,
        data: this.VocData.vocxData
      },
      yAxis: {
        name: this.VocData.type === 1 ? "mg/m³" : 'kg/h',
        nameTextStyle: {
          color: "#fff"
        },
        type: "value",
        axisTick: {
          show: false
        },
        axisLine: {
          show: false
        },
        splitLine: {
          lineStyle: {
            color: "gray"
          }
        },
        axisLabel: {
          color: "#fff"
        }
      },
      series: [
        {
          name: "小时值",
          data: this.VocData.type === 1 ?  this.VocData.vocyData : this.VocData.emissionyData,
          type: "line",
          smooth: true,
          lineStyle: {
            color: "#018AD1"
          },
          itemStyle: {
            color: "#018AD1"
          },
          areaStyle: {
            color: {
              type: "linear",
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: "#01A9F7" // 0% 处的颜色
                },
                {
                  offset: 1,
                  color: "RGBA(1, 161, 237, .3)" // 100% 处的颜色
                }
              ],
              global: false // 缺省为 false
            }
          }
        },
        {
          name: "日均值",
          data: this.VocData.type ===1 ? this.VocData.voczData :  this.VocData.emissionzData,
          type: "line",
          smooth: true,
          lineStyle: {
            color: "#B5B359"
          },
          itemStyle: {
            color: "#B5B359"
          },
          areaStyle: {
            color: {
              type: "linear",
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: "RGBA(181, 179, 89, 1)" // 0% 处的颜色
                },
                {
                  offset: 1,
                  color: "RGBA(181, 179, 89, .3)" // 100% 处的颜色
                }
              ],
              global: false // 缺省为 false
            }
          }
        }
      ]
    } as {});
  }
}
</script>
