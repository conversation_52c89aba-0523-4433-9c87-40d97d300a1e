<template>
  <div id="grid3D"></div>
</template>


<script>
import * as echarts from 'echarts'
import 'echarts-gl'
// import dayjs from 'dayjs'
export default {
  name: 'cardBox',
  props: {
    noiseDataDN: {
      type: Array,
      default: [],
    },
  },
  data() {
    return {
      bottomArr: [],
    }
  },
  watch: {
    noiseDataDN: {
      handler(newVal) {
        let data = []
        let name = []
        newVal.forEach((v) => {
          data.push(v.ldn)
          name.push(v.stationName)
        })
        this.creatEharts(data, name)
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {
    let data = []
    let name = []
    this.noiseDataDN.forEach((v) => {
      data.push(v.ldn)
      name.push(v.stationName)
      this.bottomArr.push(41)
    })
    this.creatEharts(data, name)
  },
  methods: {
    creatEharts(data, name) {
      let option = {}
      let myChart = echarts.init(document.getElementById('grid3D'))
      // option = {
      //   backgroundColor: '',
      //   tooltip: {
      //     trigger: 'axis',
      //     axisPointer: {
      //       type: 'shadow',
      //     },
      //     formatter: '{b}<br/>{a0}: {c0}%',
      //   },
      //   // legend: {
      //   //   top: '-2%',
      //   //   right: '6%',
      //   //   itemWidth: 12,
      //   //   itemHeight: 5,
      //   //   // itemGap: 343,
      //   //   icon: 'horizontal',
      //   //   zlevel: 999,
      //   //   z: 9999,
      //   //   textStyle: {
      //   //     color: '#B6D0D8',
      //   //     fontSize: 13,
      //   //   },
      //   //   data: [this.lastYear, this.thisYear],
      //   // },
      //   grid: {
      //     left: '3%',
      //     right: '-1%',
      //     bottom: '-4%',
      //     top: '9%',
      //     containLabel: true,
      //   },
      //   xAxis: [
      //     {
      //       type: 'category',
      //       data: name,
      //       axisLine: {
      //         show: true,
      //         lineStyle: {
      //           color: '#385982',
      //           width: 1,
      //           type: 'dashed',
      //         },
      //       },

      //       axisTick: {
      //         show: true,
      //       },
      //       axisLabel: {
      //         show: true,
      //         fontSize: 14,
      //         color: '#B6D0D8',
      //         rotate: 45,
      //         textStyle: {
      //           color: '#a4a7aa',
      //         },
      //       },
      //     },
      //   ],
      //   yAxis: [
      //     {
      //       name: '单位:dB',
      //       nameTextStyle: {
      //         color: '#A6CBE2',
      //         fontSize: 12,
      //       },
      //       min: 40,
      //       axisLine: {
      //         show: false,
      //         lineStyle: {
      //           color: '#494e54',
      //         },
      //       },
      //       axisTick: {
      //         //y轴刻度线
      //         show: false,
      //       },
      //       splitLine: {
      //         show: true,
      //         lineStyle: {
      //           color: '#385982',
      //           type: 'dashed',
      //         },
      //       },
      //       axisLabel: {
      //         color: '#B6D0D8',
      //       },
      //     },
      //   ],
      //   series: [
      //     {
      //       data: data,
      //       type: 'bar',
      //       barMaxWidth: 'auto',
      //       barWidth: 12,
      //       itemStyle: {
      //         color: {
      //           x: 0,
      //           y: 1,
      //           x2: 0,
      //           y2: 0,
      //           type: 'linear',
      //           colorStops: [
      //             {
      //               offset: 0,
      //               color: '#00D7E9',
      //             },
      //             {
      //               offset: 1,
      //               color: 'rgba(0, 167, 233,0.5)',
      //             },
      //           ],
      //         },
      //       },
      //       label: {
      //         show: true,
      //         position: 'top',
      //         distance: 10,
      //         color: '#fff',
      //       },
      //     },
      //     {
      //       data: [1, 1, 1, 1, 1, 1],
      //       type: 'pictorialBar',
      //       barMaxWidth: 12,
      //       symbol: 'diamond',
      //       symbolOffset: [0, '50%'],
      //       symbolSize: [12, 6],
      //     },
      //     {
      //       data: data,
      //       type: 'pictorialBar',
      //       barMaxWidth: 12,
      //       symbolPosition: 'end',
      //       symbol: 'diamond',
      //       symbolOffset: [0, '-50%'],
      //       symbolSize: [12, 7],
      //       zlevel: 2,
      //     },
      //   ],
      // }

      option = {
        backgroundColor: '',
        textStyle: {
          color: '#c0c3cd',
          fontSize: 14,
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
          formatter: '{b}<br/>{a0}: {c0}dB(A)',
        },
        // toolbox: {
        //   show: false,
        //   feature: {
        //     saveAsImage: {
        //       backgroundColor: '#031245',
        //     },
        //     restore: {},
        //   },
        //   iconStyle: {
        //     borderColor: '#c0c3cd',
        //   },
        // },
        // legend: {
        //   top: 10,
        //   itemWidth: 8,
        //   itemHeight: 8,
        //   icon: 'circle',
        //   left: 'center',
        //   padding: 0,
        //   textStyle: {
        //     color: '#c0c3cd',
        //     fontSize: 14,
        //     padding: [2, 0, 0, 0],
        //   },
        // },
        color: ['#00D7E9', 'rgba(0, 215, 233, 0.9)'],
        grid: {
          left: '3%',
          right: '-1%',
          bottom: '-3%',
          top: '11%',
          containLabel: true,
        },
        xAxis: [
          {
            type: 'category',
            data: name,
            axisLine: {
              show: true,
              lineStyle: {
                color: '#385982',
                width: 1,
                type: 'dashed',
              },
            },

            axisTick: {
              show: true,
            },
            axisLabel: {
              show: true,
              fontSize: 14,
              color: '#B6D0D8',
              rotate: 45,
              textStyle: {
                color: '#a4a7aa',
              },
            },
          },
        ],
        yAxis: [
          {
            name: '单位:dB(A)',
            nameTextStyle: {
              color: '#A6CBE2',
              fontSize: 12,
            },
            // min: 40,
            axisLine: {
              show: false,
              lineStyle: {
                color: '#494e54',
              },
            },
            axisTick: {
              //y轴刻度线
              show: false,
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: '#385982',
                type: 'dashed',
              },
            },
            axisLabel: {
              color: '#B6D0D8',
            },
          },
        ],
        series: [
          {
            name: '数值',
            data: data,
            type: 'bar',
            barMaxWidth: 'auto',
            barWidth: 14,
            itemStyle: {
              color: {
                x: 0,
                y: 1,
                x2: 0,
                y2: 0,
                type: 'linear',
                colorStops: [
                  {
                    offset: 0,
                    color: '#00D7E9',
                  },
                  {
                    offset: 1,
                    color: 'rgba(0, 167, 233,0.2)',
                  },
                ],
              },
            },
            label: {
              show: false,
              position: 'top',
              distance: 10,
              color: '#fff',
            },
          },
          {
            data: this.bottomArr,
            type: 'pictorialBar',
            barMaxWidth: 14,
            symbol: 'diamond',
            symbolOffset: [0, '50%'],
            symbolSize: [16, 6],
          },
          {
            data: data,
            type: 'pictorialBar',
            barMaxWidth: 14,

            symbolPosition: 'end',
            symbol: 'diamond',
            symbolOffset: [0, '-50%'],
            symbolSize: [14, 8],
            zlevel: 2,
          },
        ],
      }
      if (option) {
        myChart.setOption(option)
      }
    },
  },
}
</script>

<style lang="less">
#grid3D {
  height: 285px;
  width: 100%;
}
</style>
