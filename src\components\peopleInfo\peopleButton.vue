<template>
  <div class="btn-area" :class="className" @click="btnClick">
    <span class="btn">{{ text }}</span>
  </div>
</template>

<script>
export default {
  name: "peopleButton",
  props: {
    text: {
      type: String,
      default: "",
    },
    className: {
      type: String,
      default: "",
    },
  },
  methods: {
    btnClick() {
      this.$emit("btnClick");
    },
  },
};
</script>

<style lang="less" scoped>
.btn-area {
  width: 90px;
  height: 22px;
  display: flex;
  position: relative;
  top: -10px;
  justify-content: center;
  position: relative;
  z-index: 10;
  // &:last-child {
  // margin-left: 15px;
  // }
  .btn {
    font-size: 14px;
    font-family: PingFang SC;
    font-weight: 400;
    color: #3c6271;
    cursor: pointer;
  }
}
.choose-btn {
  background-image: url("../../assets/images/xz.png");
  background-size: 100% 100%;
  .btn {
    font-size: 14px;
    font-family: PingFang SC;
    font-weight: 400;
    color: #3fc5fc;
    text-align: center;
    background: linear-gradient(0deg, #3dbef3 0%, #ffdada 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}
</style>
