<style lang="less" scoped>
::-webkit-scrollbar-track {
  background: rgba(116, 182, 224, 0.1);
}
::-webkit-scrollbar-thumb {
  background: rgba(116, 182, 224, 0.5);
}
::-webkit-scrollbar-thumb:hover {
  background: rgba(116, 182, 224, 0.5);
}
.arrow-icon {
  width: 13px !important;
  height: 8px;
  cursor: pointer;
  filter: grayscale(80%);
  &.active {
    filter: grayscale(0%);
  }
}
.title-flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
  > div {
    display: flex;
    //font-size: 0.14rem;
    font-weight: 400;

    > div {
      width: 0.8rem;
      height: 0.28rem;
      line-height: 0.3rem;
      color: #0e8bff;
      // background: rgba(14, 139, 255, 0.32);
      // border: 1px solid rgba(14, 139, 255, 1);
      text-align: center;
      // clip-path: polygon(100% 0, 100% 100%, 12% 100%, 0% 70%, 0 0);
      background: url(~@/assets/<EMAIL>);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      cursor: pointer;
    }
    div.type-active {
      background: url(~@/assets/<EMAIL>);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      color: rgba(0, 234, 255, 1);
    }
    :nth-of-type(2) {
      margin-left: 0.1rem;
    }
  }
}

::v-deep .date-picker-top {
  width: 200px;
  .el-input__inner {
    height: 30px;
    width: 200px;
    background: transparent;
    color: #3adbff;
    border-color: #3adbff;
  }
  .el-input__icon {
    line-height: 30px;
  }
  .el-input__prefix,
  .el-input__suffix {
    color: #3adbff;
  }
}

.home-main-one {
  width: 100%;
  position: relative;
  .construction-plant-dep {
    position: absolute;
    top: 0;
    right: 20px;
    width: 4rem;
    // height: calc(1080px - 2rem);
    // width: 4.6rem;
    margin-top: 0.1rem;
    z-index: 3;
    padding: 0.2rem 0.1rem;
    // background-color: rgba(6, 55, 92, 0.75);
    background: rgba(0, 4, 18, 0.5);
    border: 1px solid;
    border-image: linear-gradient(0deg, #29558b, #1f4574) 10 10;
    .video-main {
      .sub-title {
        > img {
          width: 50%;
          height: 0.1rem;
        }
      }
      .box-title {
        justify-content: start;
        .title {
          margin-right: 0.5rem;
        }
        .unit {
          color: #cbe49d;
          font-size: 14px;
        }
      }
      .title-video {
        display: flex;
        align-items: center;
      }
      img {
        width: 1.2rem;
      }
    }
    .gongditable {
      width: 100%;
      .table-header {
        display: flex;
        text-align: center;
        align-items: center;
        height: 0.4rem;
        line-height: 0.4rem;
        color: #3be6ff;
        font-size: 0.16rem;
        > div {
          width: 20%;
        }
        > div:nth-child(2n) {
          width: 30%;
        }
        > div:last-child {
          width: 30%;
        }
      }
      .table_content {
        // max-height: 7.2rem;
        // min-height: 2rem;
        height: 200px;
        overflow: auto;
        &::-webkit-scrollbar {
          width: 0px !important;
          height: 0px !important;
        }

        .table-body {
          display: flex;
          align-items: center;
          font-size: 0.14rem;
          text-align: center;
          height: 0.55rem;
          overflow: hidden;

          &:nth-child(odd) {
            background: #0f245e;
          }
          div {
            width: 20%;
            // &:nth-child(3n + 1) {
            //   padding-left: 0.15rem;
            //   box-sizing: border-box;
            // }
            &:nth-child(2n) {
              width: 30%;
            }
            &:last-child {
              width: 30%;
            }
            text-align: center;
            overflow: hidden; //超出的文本隐藏
            text-overflow: ellipsis; //溢出用省略号显示
            white-space: nowrap; //溢出不换行
          }
          .rankIndex {
            background: url("~@/assets/rehearseAnalyse/<EMAIL>") no-repeat;
            background-size: contain;
            background-position: center;
          }
          .firstRank {
            background: url("~@/assets/rehearseAnalyse/<EMAIL>") no-repeat;
            background-size: contain;
            background-position: center;
          }
          .api_box {
            display: flex;
            justify-content: center;
            align-items: center;
            .aqi {
              width: 45px;
              height: 20px;
              background: #0d573c;
              border-radius: 3px;
              display: flex;
              justify-content: center;
              align-items: center;
            }
          }
        }
      }

      .thead,
      .tbody {
        font-size: 0.14rem;
        text-align: left;
        height: 0.43rem;
        line-height: 0.43rem;
      }
      .tbody {
        .td:nth-of-type(1) {
          width: 48%;
          overflow: hidden; //超出的文本隐藏
          text-overflow: ellipsis; //溢出用省略号显示
          white-space: nowrap; //溢出不换行
          display: flex;
          align-items: center;
        }
        .td:nth-of-type(2) {
          width: 19%;
          display: flex;
          align-items: center;
        }
        .td:nth-of-type(3) {
          width: 0.02rem;
          height: 0.3rem;
          margin-top: 0.05rem;
          background: rgba(0, 234, 255, 1);
          margin-right: 0.05rem;
        }
        .td:nth-of-type(4) {
          width: 30%;
          display: flex;
          align-items: center;
        }
      }
      .tbody:nth-child(odd) {
        background: #00e40034;
      }
      // .tbody:nth-child(even) {
      //   // background: #041433;
      // }
    }
  }
  .construction-plant-dep2 {
    position: absolute;
    top: 350px;
    right: 0;
    width: 4.2rem;
    .box-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      box-sizing: border-box;
      padding-right: 20px;
    }
    .second_item {
      width: 398px;
      height: 70px;
      background: url("~@/assets/rehearseAnalyse/<EMAIL>") no-repeat;
      background-size: contain;
      display: flex;
      justify-content: space-between;
      align-items: center;
      box-sizing: border-box;
      padding: 0 20px;
      margin-bottom: 10px;
      &:hover {
        background: url("~@/assets/rehearseAnalyse/<EMAIL>") no-repeat;
      }
      .left_box {
        .title {
          width: 300px;
          font-family: PingFang SC;
          font-weight: 300;
          font-size: 14px;
          color: #c7daea;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .content_box {
          display: flex;
          align-items: center;
          .item_item {
            margin-right: 10px;
            .number {
              font-size: 18px;
              margin-right: 4px;
            }
          }
        }
      }
      .right_box {
        .number {
          font-size: 22px;
        }
      }
    }
  }
  .construction-plant-dep3 {
    position: absolute;
    top: 570px;
    right: 0;
    width: 4.2rem;
    .box-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      box-sizing: border-box;
      padding-right: 20px;
    }
    .third_content {
      width: 400px;
      display: flex;
      justify-content: space-between;
      box-sizing: border-box;
      padding-right: 20px;
      overflow: auto;
    }
    .third_item {
      width: 197px;

      .time_title {
        width: 197px;
        height: 44px;
        background: url("~@/assets/rehearseAnalyse/<EMAIL>") no-repeat;
        background-size: contain;
        display: flex;
        justify-content: center;
        align-items: center;
        span {
          margin-top: 4px;
          font-family: PingFang SC;
          font-weight: 600;
          font-size: 16px;
          color: #66e8ff;
          line-height: 26px;
          text-shadow: 1px 1px 2px rgba(16, 61, 90, 0.61);
          background: linear-gradient(0deg, #b7fafb 0%, #8bcdec 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }
      .third_item_content {
        border: 1px solid #1a3e61;
        box-sizing: border-box;
        margin: 4px;
        // padding: 10px;
        .td_box {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-weight: 400;
          font-size: 12px;
          color: #74b6e0;
          height: 32px;
          &:nth-child(2n-1) {
            background: rgba(14, 77, 171, 0.2);
          }
          &:nth-child(5n) {
            background: transparent;
          }
          &.content {
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
          }
          .tr_box_1 {
            width: 60%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            border-right: 1px solid #1a3e61;
            border-bottom: 1px solid #1a3e61;
          }
          .tr_box_2 {
            width: 40%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            border-bottom: 1px solid #1a3e61;
          }
        }
      }
    }
  }
  .weather_box {
    position: absolute;
    top: 10px;
    right: 430px;
    border: 1px solid #7fa7c9;
    z-index: 10;
    .weather_item {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: 65px;
      height: 56px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 15px;
      color: #aab8ca;
      cursor: pointer;
      &:not(:last-child) {
        border-bottom: 1px solid #7fa7c9;
      }
      &.active {
        // background: linear-gradient(
        //   90deg,
        //   #062752 0%,
        //   #0b9bff 50%,
        //   #062752 100%
        // );
        background: rgb(2, 91, 160);
        // span {
        //   display: block;
        //   text-shadow: 0px 3px 4px rgba(2, 17, 20, 0.15);
        //   background-image: linear-gradient(0deg, rgba(11, 155, 255, 0.46) 0%);
        // }
      }
    }
  }
  .animimate_box {
    width: 397px;
    height: 233px;
    overflow: hidden;
    position: absolute;
    left: 1100px;
    bottom: 110px;
  }
  .warning_box {
    width: 397px;
    height: 233px;
    background: url("~@/assets/rehearseAnalyse/<EMAIL>") no-repeat;
    background-size: contain;
    position: relative;
    .title {
      width: 100%;
      position: absolute;
      top: 27px;
      display: flex;
      justify-content: space-between;
      box-sizing: border-box;
      padding: 0 30px;
      .title_text {
        font-family: PingFang SC;
        font-weight: 600;
        font-size: 18px;
        color: #ffffff;
        line-height: 34px;
        text-shadow: 0px 3px 4px rgba(2, 17, 20, 0.15);
        font-style: italic;
        background: linear-gradient(0deg, rgba(11, 155, 255, 1) 0%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      .title_date {
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 14px;
        color: #ffffff;
        line-height: 34px;
        text-shadow: 0px 3px 4px rgba(2, 17, 20, 0.15);
        font-style: italic;
        background: linear-gradient(0deg, rgba(35, 193, 252, 1) 0%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }
    .content_box {
      display: flex;
      position: absolute;
      top: 65px;
      justify-content: space-between;
      box-sizing: border-box;
      // background: pink;
      padding: 0 30px;
      align-items: center;
      .left_box {
        width: 96px;
        display: flex;
        flex-direction: column;
        .status {
          width: 96px;
          height: 24px;
          background: url("~@/assets/rehearseAnalyse/status.png") no-repeat;
          background-size: contain;
          display: flex;
          justify-content: center;
          align-items: center;
          .text {
            font-family: PangMenZhengDao;
            font-weight: 400;
            font-size: 16px;
            color: #ffffff;
            line-height: 34px;
            text-shadow: 0px 3px 4px rgba(2, 17, 20, 0.15);
            font-style: italic;
            background: linear-gradient(0deg, rgba(11, 155, 255, 1) 0%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
        }
      }
      .right_box {
        margin-left: 20px;
        width: 220px;
        text-align: justify;
        flex-shrink: 0;
        flex-grow: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 6;
        // height: 150px;
      }
    }
  }

  .no-data {
    height: 200px;
    font-size: 0.2rem;
    display: flex;
    color: #24a6f1;
    align-items: center;
    font-size: 16px;
    justify-content: center;
    text-align: center;
  }
  // 右到左
  .pu-randa-r2l {
    animation: pu-randa-r2l 0.5s linear;
  }
  @keyframes pu-randa-r2l {
    0% {
      transform: translate(120%, 0);
    }
    50% {
      transform: translate(120%, 0);
    }
    100% {
      transform: translate(0, 0);
    }
  }
  .to_table {
    position: absolute;
    top: 0.5rem;
    left: 0.5rem;
    width: 1.8rem;
    height: 0.53rem;
    z-index: 99;
    cursor: pointer;
  }
  .timeRuler {
    position: absolute;
    bottom: 0.32rem;
    left: 0.62rem;
    z-index: 9999;
    cursor: pointer;
    display: flex;
    .playBtn {
      width: 0.63rem;
      height: 0.6rem;
      background: rgba(0, 0, 0, 0.5);
      text-align: center;
      line-height: 0.6rem;
      margin-right: 0.05rem;
      margin-top: auto;
    }
    .timeCom {
      width: 17.28rem;
      .rili {
        width: 100%;
        height: 0.36rem;
        background: rgba(0, 0, 0, 0.5);
        text-align: center;
        line-height: 0.36rem;
        position: relative;
        .rili_icon {
          position: absolute;
          top: 8px;
          left: 44%;
          font-size: 20px;
        }
        /deep/.el-input__inner {
          background-color: transparent;
          border: 1px solid transparent;
          color: #cddfff;
          font-size: 16px;
          cursor: pointer;
        }
      }
      .ruler {
        position: relative;
        .color {
          position: absolute;
          height: 0.06rem;
          top: 0;
          left: 0;
          background: rgb(0, 152, 69);
          z-index: 5;
        }
        .tips {
          position: absolute;
          top: -0.28rem;
          left: -0.3rem;
          z-index: 5;
          width: 0.6rem;
          height: 0.2rem;
          font-size: 0.14rem;
          text-align: center;
          line-height: 0.2rem;
          background: rgba(0, 0, 0, 0.5);
          border-radius: 0.04rem;
          &::after {
            content: "";
            position: absolute;
            box-sizing: border-box;
            border: 0.07rem solid rgba(0, 0, 0, 0.5);
            border-right: 0.07rem solid transparent;
            border-bottom: 0.07rem solid transparent;
            border-left: 0.07rem solid transparent;
            bottom: -0.135rem;
            left: 0.23rem;
          }
        }
        .calibration {
          display: flex;
          background: rgba(0, 0, 0, 0.4);
          margin-bottom: 0.03rem;
          .item {
            flex: 1;
            height: 0.15rem;
            font-size: 0.12rem;
            color: #cddfff;
            position: relative;
            text-align: right;
            .timeNum {
              position: absolute;
              left: -0.15rem;
            }
          }
        }
        .num {
          height: 0.06rem;
          background: #dedede;
          display: flex;
          .item {
            position: relative;
            width: 0.72rem;
            height: 100%;
            box-sizing: border-box;
            border-left: 0.01rem solid #fff;
            z-index: 9;
          }
        }
      }
    }
  }
  .leftTab {
    position: absolute;
    top: 1.75rem;
    left: 0.5rem;
    z-index: 9999;
    .item {
      width: 1.88rem;
      height: 0.53rem;
      font-size: 0.2rem;
      font-family: PingFang SC;
      font-weight: 400;
      color: #b0c1df;
      line-height: 0.53rem;
      background: url("../assets/recheck/<EMAIL>") no-repeat;
      background-size: 100%;
      margin-bottom: 0.38rem;
      text-align: right;
      padding-right: 0.52rem;
      cursor: pointer;
    }
    .itemActive {
      width: 2.51rem;
      font-size: 0.24rem;
      font-weight: 600;
      color: #f8fcfc;
      background: url("../assets/recheck/<EMAIL>") no-repeat;
      background-size: 100%;
      padding-right: 0.54rem;
    }
  }
  .bottomTab {
    width: 196px;
    height: 34px;
    background: rgba(6, 55, 92, 0.6);
    border-radius: 4px;
    display: flex;
    position: absolute;
    bottom: 140px;
    left: 50px;
    z-index: 9999;
    .item {
      flex: 1;
      text-align: center;
      line-height: 34px;
      font-size: 14px;
      font-family: PingFang SC;
      font-weight: 400;
      color: #8999ab;
      cursor: pointer;
    }
    .item_active {
      background: #0a7bcc;
      border-radius: 4px;
      color: #ffffff;
    }
  }
  .topTab {
    position: absolute;
    top: 0.1rem;
    left: 7.93rem;
    display: flex;
    z-index: 9999;
    .air_btn,
    .water_btn {
      width: 1.57rem;
      height: 0.54rem;
      font-size: 0.18rem;
      font-family: PingFang SC;
      font-weight: 400;
      color: #24f19c;
      line-height: 0.554rem;
      text-align: center;
      background: url("../assets/recheck/<EMAIL>") no-repeat;
      background-size: 100%;
      margin-right: 0.1rem;
    }
    .btn_active {
      font-size: 0.18rem;
      color: #e7f6fb;
      background: url("../assets/recheck/<EMAIL>") no-repeat;
      background-size: 100%;
    }
  }
  .common-content-inner {
    height: 2.1rem;
    .weather-main {
      display: flex;
      justify-content: space-between;
      box-sizing: border-box;
      padding-right: 0.3rem;
      overflow: hidden;
      .temperature-flex {
        display: flex;
        align-items: flex-end;
        animation: temperature-flex 2s;
        > div:first-child {
          width: 1.3rem;
          position: relative;
          .temperature-number {
            // position: absolute;
            line-height: 0.7rem;
            font-size: 0.9rem;
            font-family: SimHei;
          }
          .temperature-tag {
            position: absolute;
            font-size: 0.2rem;
            top: 0;
          }
        }
        .AQI-number {
          display: inline-block;
          padding: 0 10px;
          // background-color: #32c860;
          line-height: 18px;
          border-radius: 4px;
        }
      }
      @keyframes temperature-flex {
        0% {
          transform: translate(-100%, 0);
        }
        50% {
          transform: translate(-100%, 0);
        }
        100% {
          transform: translate(0, 0);
        }
      }

      .state-img img {
        width: 0.8rem;
        height: 0.8rem;
        animation: state-img 2s;
      }
      @keyframes state-img {
        0% {
          transform: translate(150%, 0);
        }
        50% {
          transform: translate(150%, 0);
        }
        100% {
          transform: translate(0, 0);
        }
      }
    }
    .Meteorological-main {
      margin-top: 0.35rem;
      height: 0.84rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
      box-sizing: border-box;
      .Meteorological-item {
        text-align: center;
        width: 1.2rem;
        height: 0.84rem;
        background-color: #0f245e;
        text-align: center;
        animation: Meteorological-item 3s linear;
        img {
          width: 0.22rem;
          height: 0.22rem;
          margin-top: 0.1rem;
        }
      }
      @keyframes Meteorological-item {
        0% {
          transform: scale(0);
        }
        66% {
          transform: scale(0);
        }
        100% {
          transform: scale(1);
        }
      }
    }
  }
  .monitor {
    display: flex;
    color: #ffffff;
    padding: 0.12rem;
    align-items: center;
    font-size: 0.14rem;
    height: 0.5rem !important;
    justify-content: space-between;
    .spot {
      width: 0.12rem;
      height: 0.12rem;
      background: rgba(242, 83, 22, 1);
      border-radius: 50%;
      margin-right: 0.12rem;
    }
    .line {
      width: 1px;
      height: 0.48rem;
      background: white;
      margin-right: 0.12rem;
      margin-left: 0.12rem;
    }
  }
  .middle-center .middle-part .middle-content .middle-content-right {
    padding: 0.3rem 0.5rem 0.3rem 0;
  }
  .monitor:nth-child(odd) {
    background: rgba(15, 36, 94, 1);
  }
  .monitor:nth-child(even) {
    background: rgba(4, 20, 51, 1);
  }
  .table-data {
    width: 100%;
    margin-top: 0.1rem;
    overflow: hidden;
    .table-data-thead {
      animation: table-head 2s linear;
      .tr {
        display: flex;
        justify-content: space-between;
        .th {
          background: transparent !important;
          color: #ffffff;
          padding: 0;
          text-align: center;
          border: none;
          // line-height: 0.34rem;
          font-size: 0.16rem;
          height: 0.34rem;
        }
        > :nth-of-type(1) {
          width: 20%;
        }
        > :nth-of-type(2) {
          width: 20%;
        }
        > :nth-of-type(3) {
          width: 20%;
        }
        > :nth-of-type(4) {
          width: 20%;
        }
        > :nth-of-type(5) {
          width: 20%;
        }
      }
    }
    .table-data-tbody {
      height: calc(205px - 0.1rem);
      .tr {
        display: flex;
        justify-content: space-between;
        .td {
          color: #ffffff;
          padding: 0;
          font-size: 0.14rem;
          text-align: center;
          height: 0.45rem;
          line-height: 0.45rem;
          align-items: center;
          border: none;
        }
        > :nth-of-type(1) {
          width: 20%;
        }
        > :nth-of-type(2) {
          width: 20%;
        }
        > :nth-of-type(3) {
          width: 20%;
        }
        > :nth-of-type(4) {
          width: 20%;
        }
        > :nth-of-type(5) {
          display: flex;
          align-items: center;
          width: 20%;
        }
      }
    }
    .table-data-tbody .tr:nth-child(odd) {
      background: #0f245e;
    }
    .table-data-tbody .tr:nth-child(even) {
      background: transparent;
    }
    .abnormal {
      //异常
      color: rgb(255, 17, 0);
      border: 1px solid rgb(255, 17, 0);
      width: 0.44rem;
      height: 0.2rem;
      line-height: 0.2rem;
      border-radius: 0.1rem;
      margin: 0 auto;
    }
    .normal {
      //正常
      color: #00ff18;
      border: 1px solid #00ff18;
      width: 0.44rem;
      height: 0.2rem;
      line-height: 0.2rem;
      border-radius: 0.1rem;
      margin: 0 auto;
    }
  }
  .table-data1 {
    width: 100%;
    overflow: hidden;
    .table-data-thead {
      animation: table-head 2s linear;
      .tr {
        display: flex;
        justify-content: space-between;
        .th {
          background: transparent !important;
          color: #ffffff;
          padding: 0;
          text-align: center;
          border: none;
          // line-height: 0.34rem;
          font-size: 0.16rem;
          height: 0.34rem;
        }
        > :nth-of-type(1) {
          width: 9.66%;
        }
        > :nth-of-type(2) {
          width: 23.66%;
        }
        > :nth-of-type(3) {
          width: 14.66%;
        }
        > :nth-of-type(4) {
          width: 16.66%;
        }
        > :nth-of-type(5) {
          width: 16.66%;
        }
        > :nth-of-type(6) {
          width: 18.66%;
        }
      }
    }

    .table-data-tbody {
      height: calc(205px - 0.1rem);
      .tr {
        display: flex;
        justify-content: space-between;
        .td {
          color: #ffffff;
          padding: 0;
          font-size: 0.14rem;
          text-align: center;
          height: 0.34rem;
          line-height: 0.34rem;
          border: none;
        }
        > :nth-of-type(1) {
          display: flex;
          align-items: center;
          justify-content: center;
          text-align: center;
          width: 9.66%;
        }
        > :nth-of-type(2) {
          width: 22.66%;
          text-align: left;
        }
        > :nth-of-type(3) {
          width: 14.66%;
        }
        > :nth-of-type(4) {
          width: 16.66%;
        }
        > :nth-of-type(5) {
          width: 16.66%;
        }
        > :nth-of-type(6) {
          width: 18.66%;
        }
      }
    }
    .table-data-tbody .tr:nth-child(odd) {
      background: #0f245e;
    }
    .table-data-tbody .tr:nth-child(even) {
      background: transparent;
    }
    .tdBefore {
      //前三
      width: 0.26rem;
      display: block;
      text-align: center;
      height: 0.2rem;
      line-height: 0.2rem;
      background: rgba(255, 133, 9, 1);
      border-radius: 0.06rem;
      margin: 0 auto;
    }
    .tdAfter {
      //前三外
      width: 0.26rem;
      display: block;
      text-align: center;
      height: 0.2rem;
      line-height: 0.2rem;
      background: rgba(18, 136, 226, 1);
      border-radius: 0.06rem;
      margin: 0 auto;
    }
  }
  .table-data-warning {
    width: 100%;
    overflow: hidden;
    .table-data-thead {
      animation: table-head 2s linear;
      .tr {
        display: flex;
        justify-content: space-between;
        .th {
          background: transparent !important;
          color: #ffffff;
          padding: 0;
          text-align: center;
          border: none;
          // line-height: 0.34rem;
          font-size: 0.16rem;
          height: 0.34rem;
        }
        > :nth-of-type(1) {
          width: 10%;
        }
        > :nth-of-type(2) {
          width: 28%;
        }
        > :nth-of-type(3) {
          width: 40%;
        }
        > :nth-of-type(4) {
          width: 30%;
        }
      }
    }
    .table-data-tbody {
      height: 1.75rem;
      .tr {
        display: flex;
        justify-content: space-between;
        .td {
          color: #ffffff;
          padding: 0;
          font-size: 0.14rem;
          height: 0.45rem;
          line-height: 0.45rem;
          border: none;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
        > :nth-of-type(1) {
          width: 10%;
          text-align: center;
        }
        > :nth-of-type(2) {
          width: 28%;
          text-align: center;
        }
        > :nth-of-type(3) {
          width: 40%;
        }
        > :nth-of-type(4) {
          width: 30%;
          text-align: center;
        }
      }
    }
    .table-data-tbody .tr:nth-child(odd) {
      background: #0f245e;
    }
    .table-data-tbody .tr:nth-child(even) {
      background: #041433;
    }
  }
  @keyframes table-head {
    0% {
      transform: translate(100%, 0);
    }
    50% {
      transform: translate(100%, 0);
    }
    100% {
      transform: translate(0, 0);
    }
  }
  .table-data-tbody .tr:nth-child(odd) {
    animation: left-entry 3s linear;
  }
  .table-data-tbody .tr:nth-child(even) {
    animation: right-entry 3s linear;
  }
  @keyframes left-entry {
    0% {
      transform: translate(-100%, 0);
    }
    66% {
      transform: translate(-100%, 0);
    }
    100% {
      transform: translate(0, 0);
    }
  }
  @keyframes right-entry {
    0% {
      transform: translate(100%, 0);
    }
    66% {
      transform: translate(100%, 0);
    }
    100% {
      transform: translate(0, 0);
    }
  }
  .type-radio {
    .water-monitor-tab {
      height: 0.24rem;
      font-size: 0.14rem;
      color: rgba(255, 255, 255, 1);
      background: rgba(12, 39, 92, 1);
      border-radius: 0;
      padding: 0 0.1rem;
      border: none;
    }
    .ant-radio-group-solid
      .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
      background: #05c1a6;
      border: none;
    }
    .ant-radio-button-wrapper:not(:first-child)::before {
      background: transparent;
    }
    .ant-radio-button-wrapper-checked::before {
      background: transparent !important;
    }
    .ant-radio-button-wrapper-checked {
      z-index: 1;
      font-size: 0.18rem !important;
      border-color: #05c1a6 !important;
      -webkit-box-shadow: -1px 0 0 0 #05c1a6;
      box-shadow: -1px 0 0 0 #05c1a6;
    }
    .ant-radio-group-small .ant-radio-button-wrapper {
      // width: 16.66%;
      // text-align: center;
    }
    .ant-radio-group {
      display: flex;
      justify-content: space-around;
      background-color: #0f245e;
      animation: ant-radio-group 2s linear;
    }
  }
  .type-radio-water {
    .water-monitor-tab {
      height: 0.24rem;
      font-size: 0.14rem;
      color: rgba(255, 255, 255, 1);
      background: rgba(12, 39, 92, 1);
      border-radius: 0;
      padding: 0 0.1rem;
      border: none;
    }
    .ant-radio-group-solid
      .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
      background: #0897d4;
      border: none;
    }
    .ant-radio-button-wrapper:not(:first-child)::before {
      background: transparent;
    }
    .ant-radio-button-wrapper-checked::before {
      background: transparent !important;
    }
    .ant-radio-button-wrapper-checked {
      z-index: 1;
      border-color: #0897d4 !important;
      font-size: 0.18rem !important;
      -webkit-box-shadow: -1px 0 0 0 #0897d4;
      box-shadow: -1px 0 0 0 #0897d4;
    }
    .ant-radio-group-small .ant-radio-button-wrapper {
      // width: 12.5%;
      // text-align: center;
      // text-overflow: ellipsis;
      // overflow: hidden;
      // white-space: nowrap;
    }
    .ant-radio-group {
      display: flex;
      justify-content: space-around;
      background-color: #0f245e;
      animation: ant-radio-group 2s linear;
    }
  }
  @keyframes ant-radio-group {
    0% {
      transform: rotateY(-90deg);
    }
    50% {
      transform: rotateY(-90deg);
    }
    100% {
      transform: rotateY(0deg);
    }
  }
  .wind-animation {
    animation: fly-wind 3.5s linear infinite;
  }
  .type-detail {
    color: #fff;
  }
  .swiper-main {
    width: 100%;
    height: calc(100% - 1rem);
    box-sizing: border-box;
    padding: 0 0.65rem;
    position: absolute;
    top: 1rem;
    padding-top: 207px;
    left: 0;
    background: url("../assets/home_shadow.png") no-repeat;
    background-color: rgba(0, 0, 0, 0.3);
    .mainOption {
      .swiper-item {
        padding: 0 0.2rem;
        display: flex;
        > div {
          width: 33.33%;
          padding: 0 0.2rem;
          overflow: hidden;
          position: relative;
          .common-content-inner {
            > .car-main-flex {
              display: flex;
              // justify-content: space-between;
              flex-wrap: wrap;
              > div {
                display: flex;
                align-items: flex-end;
                width: 33.33%;
                margin-top: 0.3rem;
                .img-box {
                  width: 0.56rem;
                  height: 0.56rem;
                  margin-right: 0.22rem;
                  overflow: hidden;
                  > img {
                    width: 100%;
                    height: 100%;
                    animation: carImg 2s linear;
                  }
                  @keyframes carImg {
                    0% {
                      transform: translate(200%, 0);
                    }
                    50% {
                      transform: translate(200%, 0);
                    }
                    75% {
                      transform: translate(100%, 0);
                    }
                    100% {
                      transform: translate(0, 0);
                    }
                  }
                }
                .detail {
                  font-size: 0.17rem;
                  font-family: Source Han Sans SC;
                  font-weight: 400;
                  color: rgba(255, 255, 255, 1);
                  overflow: hidden;
                  > div:first-child {
                    animation: detail1 3s linear;
                  }
                  @keyframes detail1 {
                    0% {
                      transform: translate(0, 1rem);
                    }
                    66% {
                      transform: translate(0, 1rem);
                    }
                    100% {
                      transform: translate(0, 0%);
                    }
                  }
                  > div:last-child {
                    animation: detail2 3s linear;
                    > span:first-child {
                      font-size: 0.26rem;
                      color: rgba(0, 252, 249, 1);
                    }
                    > span:last-child {
                      font-size: 0.16rem;
                    }
                  }
                  @keyframes detail2 {
                    0% {
                      transform: translate(0, -1rem);
                    }
                    66% {
                      transform: translate(0, -1rem);
                    }
                    100% {
                      transform: translate(0, 0%);
                    }
                  }
                }
              }
            }
          }
          .title {
            width: 100%;
            font-size: 0.26rem;
            align-items: center;
            animation: title 1s linear;
            > .title-shadow {
              text-shadow: 0 0 5px blue, 0 0 5px blue;
            }
            @keyframes title {
              0% {
                transform: translate(-100%, 0);
              }
              100% {
                transform: translate(0, 0);
              }
            }
          }

          .sub-title {
            margin-bottom: 0.2rem;
            img {
              width: 100%;
            }
            animation: sub-title 1.5s;
          }
          @keyframes sub-title {
            0% {
              transform: translate(100%, 0);
            }
            100% {
              transform: translate(0, 0);
            }
          }
          .airPie,
          .waterPie {
            width: 1.25rem;
            height: 1rem;
            overflow: hidden;
          }
          .air-environmental {
            font-size: 0.18rem;
            color: white;
            width: 0.2rem;
            animation: air-environmental 2s linear;
          }
          @keyframes air-environmental {
            0% {
              transform: translate(0, 120%);
            }
            50% {
              transform: translate(0, 120%);
            }
            100% {
              transform: translate(0, 0);
            }
          }
          .water-environmental {
            font-size: 0.18rem;
            color: white;
            width: 0.2rem;
            animation: water-environmental 2s linear;
          }
          @keyframes water-environmental {
            0% {
              transform: translate(0, -120%);
            }
            50% {
              transform: translate(0, -120%);
            }
            100% {
              transform: translate(0, 0);
            }
          }
          .content-title {
            font-size: 0.22rem;
            font-family: Source Han Sans SC;
            color: rgba(204, 204, 204, 0.8);
            text-align: center;
            margin-top: 0.5rem;
            animation: content-title 1s linear;
          }
          @keyframes content-title {
            0% {
              transform: scale(0);
            }
            100% {
              transform: scale(1);
            }
          }
        }
      }
    }
    .change-main {
      display: flex;
      justify-content: center;
      margin-top: 0.6rem;
      > div {
        cursor: pointer;
        width: 1rem;
        height: 1rem;
        // animation: change-main 1.5s linear;
        .active {
          background-color: rgba(0, 252, 249, 1);
        }
        > div {
          margin: 0.4rem auto;
          width: 0.6rem;
          height: 0.1rem;
          background-color: gray;
          border-radius: 2px;
        }
      }
    }
    @keyframes change-main {
      0% {
        margin: 0.5rem 2rem 0;
      }
      100% {
        margin: 0.5rem 0.1rem 0;
      }
    }
  }
  .table-site {
    animation: content-one 1.5s linear;
    width: 100%;
    border: 1px solid rgba(54, 218, 234, 0.42);
    .table-name {
      font-size: 0.12rem;
      width: 0.76rem;
      background: rgba(15, 36, 94, 1);
      text-align: center;
    }
    .table-text {
      padding-left: 0.06rem;
    }
    td {
      font-size: 0.12rem;
      height: 0.34rem;
      color: #ffffff;
      font-weight: 500;
    }
  }
  @keyframes content-one {
    0% {
      transform: scale(0);
    }
    100% {
      transform: scale(1);
    }
  }
  .content-twos {
    // height: 1.7rem;
    display: flex;
    justify-content: space-between;
  }
  .content-threes {
    height: 2.1rem;
    .table-data {
      width: 100%;
      .table-data-thead {
        .tr {
          display: flex;
          justify-content: space-between;
          .th {
            background: transparent !important;
            color: #ffffff;
            padding: 0;
            text-align: center;
            border: none;
            line-height: 0.43rem;
            font-size: 0.14rem;
            height: 0.43rem;
          }
          > :nth-of-type(1) {
            width: 20%;
          }
          > :nth-of-type(2) {
            width: 15%;
          }
          > :nth-of-type(3) {
            width: 35%;
          }
          > :nth-of-type(4) {
            width: 25%;
          }
        }
      }
      .table-data-tbody {
        height: calc(2.1rem - 0.35rem);
        .tr {
          display: flex;
          justify-content: space-between;
          .td {
            color: #ffffff;
            padding: 0;
            font-size: 0.14rem;
            text-align: center;
            height: 0.3rem;
            line-height: 0.3rem;
            border: none;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
          }
          > :nth-of-type(1) {
            width: 20%;
            text-align: left;
            box-sizing: border-box;
            padding-left: 0.1rem;
          }
          > :nth-of-type(2) {
            width: 15%;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
          }
          > :nth-of-type(3) {
            width: 35%;
          }
          > :nth-of-type(4) {
            width: 25%;
          }
        }
      }
      .table-data-tbody .tr:nth-child(odd) {
        background: rgba(5, 47, 97, 1);
      }
      .table-data-tbody .tr:nth-child(even) {
        background: transparent;
      }
      .tdBefore {
        //前三
        width: 0.26rem;
        display: block;
        text-align: center;
        height: 0.2rem;
        line-height: 0.2rem;
        background: rgba(255, 133, 9, 1);
        border-radius: 0.06rem;
        margin: 0 auto;
      }
      .tdAfter {
        //前三外
        width: 0.26rem;
        display: block;
        text-align: center;
        height: 0.2rem;
        line-height: 0.2rem;
        background: rgba(18, 136, 226, 1);
        border-radius: 0.06rem;
        margin: 0 auto;
      }
    }
  }
  .content-list {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    .content-item {
      width: 50%;
      display: flex;
      padding-left: 5%;
      padding-top: 20px;
      .img-box {
        width: 0.8rem;
        height: 0.8rem;
        overflow: hidden;
      }
      img {
        width: 75px;
        height: 75px;
        animation: taskImg 1s linear;
        animation-iteration-count: 1;
      }
      @keyframes taskImg {
        0% {
          transform: translate(200%, 0);
        }
        50% {
          transform: translate(200%, 0);
        }
        75% {
          transform: translate(100%, 0);
        }
        100% {
          transform: translate(0, 0);
        }
      }
      .content-info {
        margin-left: 15px;
        overflow: hidden;
        width: calc(100% - 0.8rem);
        height: 100%;
        .content-info-title {
          width: 100%;
          height: 30px;
          font-size: 19px;
          line-height: 35px;
          font-family: PingFang SC;
          font-weight: 500;
          color: #d5eaf1;
          animation: titleFrame 1.5s linear;
          animation-iteration-count: 1;
        }
        @keyframes titleFrame {
          0% {
            transform: translate(0, 1rem);
          }
          66% {
            transform: translate(0, 1rem);
          }
          100% {
            transform: translate(0, 0%);
          }
        }
        .number {
          line-height: 50px;
          font-size: 30px;
          height: 31px;
          width: 100%;
          font-family: PingFang SC;
          font-weight: 500;
          color: #32ceff;
          animation: detail2 1.5s linear;
          animation-iteration-count: 1;
        }
        @keyframes detail2 {
          0% {
            transform: translate(0, -1rem);
          }
          66% {
            transform: translate(0, -1rem);
          }
          100% {
            transform: translate(0, 0%);
          }
        }
        .unit {
          font-size: 20px;
          color: #fff;
        }
      }
    }
  }
}
</style>
<style lang="less">
.home-main-one {
  position: relative;
  .title-select {
    display: flex;
    justify-content: space-between;
    .ant-select-selection {
      .ant-select-selection__rendered {
        min-width: 1.1rem;
        text-align: center;
      }
      height: 0.3rem;
      border: none;
      border-radius: unset;
      background: url(../assets/<EMAIL>);
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
    .ant-select-selection-selected-value {
      width: 100%;
      color: white;
      font-size: 0.16rem !important;
    }
    .ant-select-focused .ant-select-selection,
    .ant-select-selection:focus,
    .ant-select-selection:active {
      border-color: #0897d4;
      border-right-width: 0 !important;
      outline: 0;
      box-shadow: none;
    }
  }
  .title-select1 {
    display: flex;
    justify-content: space-between;
    .ant-select-selection {
      height: 0.3rem;
      // background: rgba(14, 139, 255, 0.32);
      border: none;
      border-radius: unset;
      // clip-path: polygon(100% 0, 100% 100%, 12% 100%, 0% 70%, 0 0);
      background: url(../assets/<EMAIL>);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      .ant-select-selection__rendered {
        text-align: center;
      }
    }
    .ant-select-selection-selected-value {
      width: 100%;
      color: white;
      font-size: 0.16rem !important;
    }
    .ant-select-focused .ant-select-selection,
    .ant-select-selection:focus,
    .ant-select-selection:active {
      border-color: #40a9ff;
      border-right-width: 0 !important;
      outline: 0;
      box-shadow: none;
    }
  }
  .table-data1 .table-data-tbody .tr > .td {
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }
  .title-select-size-type {
    .ant-select-selection {
      width: 0.7rem;
      display: flex;
      justify-content: center;
    }
  }
  .title-select-size-airname {
    .ant-select-selection {
      width: 1rem;
      display: flex;
      justify-content: center;
      margin: 0 0.1rem;
    }
  }
  .title-select-size-water {
    .ant-select-selection {
      margin-left: 0.1rem;
    }
  }
  .select-main {
    width: 1.2rem;
    display: flex;
    justify-content: space-between;
    .ant-select-selection {
      height: 0.3rem;
      width: 100%;
      border: none;
      outline: none;
      border-radius: unset;
      background: url("~@/assets/<EMAIL>");
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
    .ant-select-selection-selected-value {
      color: rgba(0, 234, 255, 1);
    }
    .ant-select-focused .ant-select-selection,
    .ant-select-selection:focus,
    .ant-select-selection:active {
      border: none;
      border-right-width: 0 !important;
      outline: none;
      box-shadow: none;
    }
  }
}
.picker_class {
  background: #052f61;
  border: none;
  color: #cddfff;
  .el-date-picker__header-label,
  .el-picker-panel__icon-btn,
  .el-month-table td .cell,
  .el-year-table td .cell {
    color: #cddfff;
  }
  .popper__arrow {
    border-top-color: #052f61 !important;
    &::after {
      border-top-color: #052f61 !important;
    }
  }
  .el-date-table th {
    border: none;
    color: #cddfff;
  }
  .el-date-picker__header--bordered {
    border: none;
  }
  .el-date-table td.disabled div,
  .el-month-table td.disabled .cell,
  .el-year-table td.disabled .cell {
    background-color: #000a38;
  }
  .el-picker-panel__footer {
    background: transparent;
  }
  .el-input__inner {
    background: transparent;
    border: 1px solid #cddfff;
    color: #cddfff;
    font-size: 16px;
  }
}
.date-picker {
  .el-range-separator {
    color: #cddfff;
  }
  .el-range-input {
    background: transparent;
    border: 1px solid #cddfff;
    color: #cddfff;
    font-size: 16px;
    cursor: pointer;
    border-radius: 4px;
    width: auto;
  }
}
.ant-modal-recheck {
  top: 1.65rem !important;
  right: calc(50% - 500px) !important;
  .ant-modal-content {
    width: 15.6rem !important;
    min-height: 6.9rem !important;
    max-height: 8.5rem !important;
    padding: 0.7rem 0rem 0.54rem 1.1rem;
    box-sizing: border-box;
    background-color: transparent !important;
    background-image: url("../assets/<EMAIL>") !important;
    background-size: 100% 100% !important;
  }
  .ant-modal-footer {
    border: none !important;
  }
  .ant-modal-close {
    top: 0.49rem !important;
    right: 0.86rem !important;
    svg {
      font-size: 0.2rem !important;
      color: #42adfb;
    }
  }
  .ant-modal-close-x {
    background: RGBA(12, 39, 94, 1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    width: 0.29rem;
    height: 0.29rem;
    justify-content: center;
    border: 0.01rem solid #358fd3;
  }
  .ant-modal-body {
    padding: 0;
    overflow-y: auto;
    max-height: 726px;
    &::-webkit-scrollbar-thumb {
      border-radius: 10px;
      width: 1px;
      background: transparent;
    }
    &::-webkit-scrollbar-track {
      border-radius: 10px;
      background: transparent;
    }
    .smapContainer {
      display: flex;
      flex-wrap: wrap;
      .sigleSmBox {
        width: 205px;
        height: 266px;
        background: url("../assets/recheck/<EMAIL>") no-repeat;
        background-size: 100%;
        position: relative;
        padding: 16px 8px 10px;
        box-sizing: border-box;
        margin-top: 20px;
        .dzIcon {
          font-size: 16px;
          font-family: PingFang SC;
          font-weight: 400;
          color: #dbf5ff;
        }
        .smBorderImg {
          position: absolute;
          width: 191px;
          height: 215px;
        }
        .smImgBox {
          position: absolute;
          width: 168px;
          height: 200px;
          overflow: hidden;
          top: 44px;
          left: 18px;
          .smImg {
            position: absolute;
            height: 200px;
            width: 355.5px;
            top: 5px;
            left: -80px;
            cursor: pointer;
          }
        }
      }
    }
  }
}

.timeShow {
  margin: 10px 0;
  display: flex;
  justify-content: center;
  user-select: none;
}
.timeline {
  height: 28px;
}
</style>
<template>
  <section class="home-main-one">
    <department-bg-map
      ref="mapRef"
      :mapZoom="12.4"
      :mengBan="mengBan"
      :mapStyle="mapStyle"
      :curHour="curHour"
      :curtype="curtype"
      :curtypeName="curtypeName"
      :pagetype="pageType"
      :selDate="selDate"
      :heathMapMax="heathMapMaxMap[curtype]"
      :maxPoint="maxPoint"
      :timeType="curDataType"
      :weatherType="weatherType"
      :markerList="pollutionStationList"
      :distance="distance"
      :isShowWind="isShowWind"
      :indexClose="indexClose"
      @noDataFn="noDataFn"
      @todayNoData="todayNoData"
      @getPhoto="getPhoto"
      @getRightData="getRightData"
      @topBtnOpen="topBtnOpen"
      @selectPoint="selectPoint"
      @handleGetWarning="handleGetWarning"
      @handleClickMarker="handleClickPollutionItem"
      @mapReady="mapReady"
    ></department-bg-map>
    <!--    大气环境-->
    <img
      v-if="pageType === 'air'"
      src="@/assets/<EMAIL>"
      class="to_table"
      @click="toDataTable"
    />
    <div class="swiper-main" v-if="false">
      <div class="mainOption">
        <div :style="{ display: curMainIndex === 1 ? 'block' : 'none' }">
          <div class="swiper-item" v-if="curMainIndex === 1">
            <div>
              <div class="title" style="display: flex; align-items: flex-end">
                <div class="title-shadow">{{ `气象在线监测` }}</div>
                <div style="font-size: 0.18rem; margin-left: 0.1rem">
                  {{ weather.updateTime }}
                </div>
              </div>
              <div class="sub-title">
                <img src="@/assets/biaoti_long.png" alt class="title-img" />
              </div>
              <div class="common-content-inner" ref="meteorologicalVisible">
                <div class="weather-main">
                  <div class="temperature-flex">
                    <div>
                      <span class="temperature-number">{{ weather.temp }}</span>
                      <span class="temperature-tag">℃</span>
                    </div>
                    <div style="font-size: 0.16rem">
                      <div>
                        <span>{{ weather.minOrMax }}℃</span>
                      </div>
                      <div>
                        <span>{{ weather.weather }}</span>
                      </div>
                      <div>
                        <span>AQI指数：</span>
                        <span
                          class="AQI-number"
                          :style="{
                            background: weather.aqiColor,
                            color: weather.color,
                          }"
                          >{{ weather.aqi }}</span
                        >
                      </div>
                    </div>
                  </div>
                  <div class="state-img">
                    <img :src="weather.weatherImg" alt />
                  </div>
                </div>
                <div class="Meteorological-main">
                  <div class="Meteorological-item">
                    <img
                      src="@/assets/dongnanfeng.png"
                      :style="{
                        animation: windLevelNumber,
                      }"
                      alt
                    />
                    <div class="type-name" style="color: #67e0e3">
                      {{
                        weather.windDirectionMark
                          ? weather.windDirectionMark
                          : "无持续风向微风"
                      }}
                    </div>
                    <div class="type-detail">{{ weather.windLevel }}级</div>
                  </div>
                  <div class="Meteorological-item">
                    <img src="@/assets/shidu.png" alt />
                    <div class="type-name" style="color: #67e0e3">湿度</div>
                    <div class="type-detail">
                      {{ weather.relativeHumidity }}%
                    </div>
                  </div>
                  <div class="Meteorological-item">
                    <img src="@/assets/qiya.png" alt />
                    <div class="type-name" style="color: #67e0e3">气压</div>
                    <div class="type-detail">
                      {{ weather.atmosphericPressure }}mb
                    </div>
                  </div>
                  <div class="Meteorological-item">
                    <img src="@/assets/wuranwu.png" alt />
                    <div class="type-name" style="color: #67e0e3">污染物</div>
                    <div class="type-detail">{{ weather.pollutant }}</div>
                  </div>
                </div>
              </div>
              <div class="content-title">24小时环境气象呈报</div>
            </div>
            <div>
              <div class="title title-select1">
                <div class="title-shadow">{{ `污染天数统计` }}</div>
              </div>
              <div class="sub-title">
                <img src="@/assets/biaoti_long.png" alt class="title-img" />
              </div>
              <div class="common-content-inner">
                <div
                  style="
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    overflow: hidden;
                  "
                >
                  <div class="air-environmental">大气</div>
                  <div
                    class="airPie"
                    v-for="(item, index) in airQuality"
                    :key="index"
                  >
                    <PollutionPie
                      :id="'PollutionAir' + index"
                      :width="'1.25rem'"
                      :height="'1rem'"
                      :propData="item"
                    />
                  </div>
                </div>
                <div
                  style="
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    overflow: hidden;
                  "
                >
                  <div
                    style="font-size: 0.18rem; color: white; width: 0.2rem"
                    class="water-environmental"
                  >
                    水环境
                  </div>
                  <div
                    class="waterPie"
                    v-for="(item, index) in waterQuality"
                    :key="index"
                  >
                    <PollutionPie
                      :id="'PollutionWater' + index"
                      :width="'1.25rem'"
                      :height="'1rem'"
                      :propData="item"
                    />
                  </div>
                </div>
              </div>
              <div class="content-title">
                大气环境和水污染物本年超标天数统计
              </div>
            </div>
            <!-- <pollutionSourcesDetail /> -->
            <div>
              <div @mouseenter="clearMainTimer()" @mouseleave="openMainTimer()">
                <div class="title title-select1">
                  <div class="title-shadow">{{ `重污企业排放量排名` }}</div>
                  <a-select
                    :defaultValue="pollutionType"
                    class="title-select-size"
                    @change="pollutionStationChange"
                  >
                    <a-icon
                      slot="suffixIcon"
                      type="caret-down"
                      style="color: white"
                    />
                    <a-select-option value="s" key="s">废水</a-select-option>
                    <a-select-option value="e" key="e">废气</a-select-option>
                    <a-select-option value="w" key="w">废料</a-select-option>
                  </a-select>
                </div>
                <div class="sub-title">
                  <img src="@/assets/biaoti_long.png" alt class="title-img" />
                </div>
                <div class="common-content-inner">
                  <div class="table-data1">
                    <div class="table-data-thead">
                      <div class="tr">
                        <div class="th">排行</div>
                        <div class="th">公司</div>
                        <div class="th">
                          排放量({{
                            pollutionType === "s"
                              ? "kg"
                              : pollutionType == "e"
                              ? "m³"
                              : "吨"
                          }})
                        </div>
                        <div class="th">行业</div>
                        <div class="th">环保负责人</div>
                        <div class="th">负责人电话</div>
                      </div>
                    </div>
                    <swiper
                      :options="swiperOptionPollution"
                      class="table-data-tbody"
                      v-if="heavyPollution.length > 5"
                    >
                      <swiper-slide
                        class="tr"
                        v-for="(item, index) in heavyPollution"
                        :key="index"
                      >
                        <div class="td">
                          <div class="tdAfter">{{ index + 1 }}</div>
                        </div>
                        <div class="td">
                          <a-tooltip>
                            <template slot="title">
                              {{ item.heavilyPollutingEnterpriseName }}
                            </template>
                            {{ item.heavilyPollutingEnterpriseNickname }}
                          </a-tooltip>
                        </div>
                        <div class="td" v-if="pollutionType == 's'">
                          {{
                            item.wastewaterDischarge
                              ? item.wastewaterDischarge
                              : "-"
                          }}
                        </div>
                        <div class="td" v-if="pollutionType == 'e'">
                          {{
                            item.exhaustEmissions ? item.exhaustEmissions : "-"
                          }}
                        </div>
                        <div class="td" v-if="pollutionType == 'w'">
                          {{ item.wasteDischarge ? item.wasteDischarge : "-" }}
                        </div>
                        <div class="td">
                          <a-tooltip>
                            <template slot="title">
                              {{ item.industryCategory }}
                            </template>
                            {{ item.industryCategory }}
                          </a-tooltip>
                        </div>
                        <div class="td">{{ item.principal }}</div>
                        <div class="td">
                          <a-tooltip>
                            <template slot="title">
                              {{ item.principalPhoneNumber }}
                            </template>
                            {{ item.principalPhoneNumber }}
                          </a-tooltip>
                        </div>
                      </swiper-slide>
                    </swiper>
                    <div class="table-data-tbody" v-else>
                      <div
                        class="tr"
                        v-for="(item, index) in heavyPollution"
                        :key="index"
                      >
                        <div class="td">
                          <div class="tdAfter">{{ index + 1 }}</div>
                        </div>
                        <div class="td">
                          <a-tooltip>
                            <template slot="title">
                              {{ item.heavilyPollutingEnterpriseName }}
                            </template>
                            {{ item.heavilyPollutingEnterpriseNickname }}
                          </a-tooltip>
                        </div>
                        <div class="td" v-if="pollutionType == 's'">
                          {{
                            item.sewageDischarge ? item.sewageDischarge : "-"
                          }}
                        </div>
                        <div class="td" v-if="pollutionType == 'e'">
                          {{
                            item.exhaustEmissions ? item.exhaustEmissions : "-"
                          }}
                        </div>
                        <div class="td" v-if="pollutionType == 'w'">
                          {{ item.wasteDischarge ? item.wasteDischarge : "-" }}
                        </div>
                        <div class="td">
                          <a-tooltip>
                            <template slot="title">
                              {{ item.industryCategory }}
                            </template>
                            {{ item.industryCategory }}
                          </a-tooltip>
                        </div>
                        <div class="td">{{ item.principal }}</div>
                        <div class="td">
                          <a-tooltip>
                            <template slot="title">
                              {{ item.principalPhoneNumber }}
                            </template>
                            {{ item.principalPhoneNumber }}
                          </a-tooltip>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="content-title">重点污染源企业排放量排名统计</div>
            </div>
          </div>
        </div>
        <div :style="{ display: curMainIndex === 2 ? 'block' : 'none' }">
          <div class="swiper-item" v-if="curMainIndex === 2">
            <div>
              <div @mouseenter="clearMainTimer()" @mouseleave="openMainTimer()">
                <div class="title title-select">
                  <div class="title-shadow">{{ `空气监测趋势` }}</div>
                  <a-select
                    v-model="airStationIndex"
                    class="title-select-size"
                    @change="airStationChange"
                  >
                    <a-icon
                      slot="suffixIcon"
                      type="caret-down"
                      style="color: white"
                    />
                    <a-select-option
                      :value="index"
                      v-for="(item, index) in airStationList"
                      :key="index"
                    >
                      {{ item.positionName }}</a-select-option
                    >
                  </a-select>
                </div>
                <div class="sub-title">
                  <img src="@/assets/biaoti_long.png" alt class="title-img" />
                </div>
                <div class="common-content-inner type-radio">
                  <a-radio-group
                    :value="airTypeIndex"
                    size="small"
                    buttonStyle="solid"
                    style="width: 100%"
                    @change="airTypeChange"
                  >
                    <a-radio-button
                      v-for="(item, index) in airTypes"
                      :key="index"
                      :value="index"
                      class="water-monitor-tab"
                      >{{ item.name }}</a-radio-button
                    >
                  </a-radio-group>
                  <div
                    @mouseenter="clearAirTimer()"
                    @mouseleave="openAirTimer()"
                  >
                    <LineChart
                      style="margin-top: 0.1rem"
                      :id="'airMonitoring'"
                      :width="'5.56rem'"
                      :height="'1.7rem'"
                      :propData="airMonitoring"
                      :lineColor="'#05C1A6'"
                    ></LineChart>
                  </div>
                </div>
              </div>
              <div class="content-title">空气监测站点趋势图例</div>
            </div>
            <div>
              <div @mouseenter="clearMainTimer()" @mouseleave="openMainTimer()">
                <div class="title title-select">
                  <div class="title-shadow">{{ `空气监测值` }}</div>
                  <a-select
                    v-model="airStationIndex"
                    class="title-select-size"
                    @change="airStationChange"
                  >
                    <a-icon
                      slot="suffixIcon"
                      type="caret-down"
                      style="color: white"
                    />
                    <a-select-option
                      :value="index"
                      v-for="(item, index) in airStationList"
                      :key="index"
                    >
                      {{ item.positionName }}</a-select-option
                    >
                  </a-select>
                </div>
                <div class="sub-title">
                  <img src="@/assets/biaoti_long.png" alt class="title-img" />
                </div>
                <div
                  class="common-content-inner"
                  style="border: none; margin-top: 0.1rem"
                >
                  <div class="table-data">
                    <div class="table-data-thead">
                      <div class="tr">
                        <div class="th">指标项</div>
                        <div class="th">数值</div>
                        <div class="th">标准</div>
                        <div class="th">单位</div>
                        <div class="th">状态</div>
                      </div>
                    </div>
                    <swiper
                      :options="swiperOptionAirs"
                      class="table-data-tbody"
                      v-if="airsData.length > 4"
                    >
                      <swiper-slide
                        class="tr"
                        v-for="(item, index) in airsData"
                        :key="index"
                      >
                        <div class="td">{{ item.pollutionCode }}</div>
                        <div class="td">
                          {{ item.value ? item.value : "-" }}
                        </div>
                        <div class="td">
                          {{ item.standard ? item.standard : "-" }}
                        </div>
                        <div class="td">{{ item.unit ? item.unit : "-" }}</div>
                        <div class="td">
                          <div :class="item.type == 0 ? 'normal' : 'abnormal'">
                            {{ item.type == 0 ? "正常" : "超标" }}
                          </div>
                        </div>
                      </swiper-slide>
                    </swiper>
                    <div class="table-data-tbody" v-else>
                      <div
                        class="tr"
                        v-for="(item, index) in airsData"
                        :key="index"
                      >
                        <div class="td">{{ item.pollutionCode }}</div>
                        <div class="td">
                          {{ item.value ? item.value : "-" }}
                        </div>
                        <div class="td">
                          {{ item.standard ? item.standard : "-" }}
                        </div>
                        <div class="td">{{ item.unit ? item.unit : "-" }}</div>
                        <div class="td">
                          <div :class="item.type == 0 ? 'normal' : 'abnormal'">
                            {{ item.type == 0 ? "正常" : "超标" }}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="content-title">空气监测站点值详情</div>
            </div>
            <div>
              <div class="title">
                <div class="title-shadow">{{ `车辆类型统计` }}</div>
              </div>
              <div class="sub-title">
                <img src="@/assets/biaoti_long.png" alt class="title-img" />
              </div>
              <div class="common-content-inner">
                <div class="car-main-flex">
                  <div v-for="(item, index) in carTypeList" :key="index">
                    <div class="img-box">
                      <img
                        :src="
                          require('../assets/home-carTypes/' + index + '.png')
                        "
                        alt=""
                      />
                    </div>
                    <div class="detail">
                      <div>{{ item.typeName }}</div>
                      <div>
                        <span>{{ item.online }}/</span
                        ><span>{{ item.count }}辆</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="content-title">
                <!-- 在线车辆类型统计<span style="font-size:0.18rem;"
                  >(注:<span style="color:rgba(0, 252, 249, 1)">‘9’</span
                  >表示在线车辆,<span style="color:white">‘9’</span
                  >表示车辆总数)</span
                > -->
                在线车辆类型统计(在线车辆/车辆总数)
              </div>
            </div>
          </div>
        </div>
        <div :style="{ display: curMainIndex === 3 ? 'block' : 'none' }">
          <div class="swiper-item" v-if="curMainIndex === 3">
            <div>
              <div @mouseenter="clearMainTimer()" @mouseleave="openMainTimer()">
                <div class="title title-select">
                  <div class="title-shadow">{{ `水质监测趋势` }}</div>
                  <a-select
                    v-model="stationId"
                    class="title-select-size"
                    @change="waterStationChange"
                  >
                    <a-icon
                      slot="suffixIcon"
                      type="caret-down"
                      style="color: white; width: 14px; height: 8px"
                    />
                    <a-select-option
                      :value="item.stationId"
                      v-for="(item, index) in waterStation"
                      :key="index"
                    >
                      {{ item.stationName }}</a-select-option
                    >
                  </a-select>
                </div>
                <div class="sub-title">
                  <img src="@/assets/biaoti_long.png" alt class="title-img" />
                </div>
                <div class="common-content-inner type-radio-water">
                  <a-radio-group
                    :value="curWaterIndex"
                    size="small"
                    buttonStyle="solid"
                    style="width: 100%"
                    @change="waterTypeChange"
                  >
                    <a-radio-button
                      v-for="(item, index) in waterTrendList"
                      :key="index"
                      :value="index"
                      class="water-monitor-tab"
                      >{{ item.name }}</a-radio-button
                    >
                  </a-radio-group>
                  <div
                    @mouseenter="clearWaterTimer()"
                    @mouseleave="openWaterTimer()"
                  >
                    <LineChart
                      style="margin-top: 0.1rem"
                      :id="'waterMonitors'"
                      :width="'5.56rem'"
                      :height="'1.7rem'"
                      :propData="waterTrendAll"
                      :lineColor="'#0897D4'"
                    />
                  </div>
                </div>
              </div>
              <div class="content-title">水质监测站点监测趋势图例</div>
            </div>
            <div>
              <div @mouseenter="clearMainTimer()" @mouseleave="openMainTimer()">
                <div class="title title-select">
                  <div class="title-shadow">{{ `水质监测值` }}</div>
                  <a-select
                    v-model="stationId"
                    class="title-select-size"
                    @change="waterStationChange"
                  >
                    <a-icon
                      slot="suffixIcon"
                      type="caret-down"
                      style="color: white; width: 14px; height: 8px"
                    />
                    <a-select-option
                      :value="item.stationId"
                      v-for="(item, index) in waterStation"
                      :key="index"
                    >
                      {{ item.stationName }}</a-select-option
                    >
                  </a-select>
                </div>
                <div class="sub-title">
                  <img src="@/assets/biaoti_long.png" alt class="title-img" />
                </div>
                <div class="common-content-inner">
                  <div class="table-data">
                    <div class="table-data-thead">
                      <div class="tr">
                        <div class="th">指标项</div>
                        <div class="th">数值</div>
                        <div class="th">标准</div>
                        <div class="th">单位</div>
                        <div class="th">状态</div>
                      </div>
                    </div>
                    <swiper
                      :options="swiperOptionWater"
                      class="table-data-tbody"
                      v-if="waterData.length > 4"
                    >
                      <swiper-slide
                        class="tr"
                        v-for="(item, index) in waterData"
                        :key="index"
                      >
                        <div class="td">{{ item.itemName }}</div>
                        <div class="td">{{ item.value }}</div>
                        <div class="td">
                          {{
                            item.alarmValue != null && item.alarmValue != "null"
                              ? item.alarmValue
                              : "-"
                          }}
                        </div>
                        <div class="td">
                          {{
                            item.unit != null && item.unit != "null"
                              ? item.unit
                              : "-"
                          }}
                        </div>
                        <div class="td">
                          <div
                            :class="
                              item.alarmStatus == 0 ? 'normal' : 'abnormal'
                            "
                          >
                            {{ item.alarmStatus == 0 ? "正常" : "超标" }}
                          </div>
                        </div>
                      </swiper-slide>
                    </swiper>
                    <div
                      class="table-data-tbody"
                      v-else-if="waterData.length < 4 && waterData.length !== 0"
                    >
                      <div
                        class="tr"
                        v-for="(item, index) in waterData"
                        :key="index"
                      >
                        <div class="td">{{ item.itemName }}</div>
                        <div class="td">{{ item.value }}</div>
                        <div class="td">
                          {{
                            item.alarmValue != null && item.alarmValue != "null"
                              ? item.alarmValue
                              : "-"
                          }}
                        </div>
                        <div class="td">{{ item.unit }}</div>
                        <div class="td">
                          <div
                            :class="
                              item.alarmStatus == 0 ? 'normal' : 'abnormal'
                            "
                          >
                            {{ item.alarmStatus == 0 ? "正常" : "超标" }}
                          </div>
                        </div>
                      </div>
                    </div>
                    <div style="height: 1.7rem" v-else>
                      <img
                        style="
                          width: 1.7rem;
                          position: relative;
                          left: 2rem;
                          top: 0.3rem;
                        "
                        src="@/assets/pollution-not.png"
                        alt=""
                      />
                    </div>
                  </div>
                </div>
              </div>
              <div class="content-title">水质监测站点值详情</div>
            </div>
            <div>
              <div @mouseenter="clearMainTimer()" @mouseleave="openMainTimer()">
                <div class="title">
                  <div class="title-shadow">{{ `实时监测告警` }}</div>
                </div>
                <div class="sub-title">
                  <img src="@/assets/biaoti_long.png" alt class="title-img" />
                </div>
                <div class="table-data-warning">
                  <div class="table-data-thead">
                    <div class="tr">
                      <div class="th">类型</div>
                      <div class="th">站点</div>
                      <div class="th">报警</div>
                      <div class="th">时间</div>
                    </div>
                  </div>
                  <swiper
                    :options="swiperOption"
                    class="table-data-tbody"
                    v-if="airWarning.length > 4"
                  >
                    <swiper-slide
                      class="tr"
                      v-for="(item, index) in airWarning"
                      :key="index"
                    >
                      <div class="td">
                        {{ item.type == 1 ? "空气" : "水质" }}
                      </div>
                      <div class="td">{{ item.stationName }}</div>
                      <div class="td">
                        <a-tooltip>
                          <template slot="title">{{
                            item.alarmContent
                          }}</template>
                          {{ item.alarmContent }}
                        </a-tooltip>
                      </div>
                      <div class="td">{{ item.alarmTime }}</div>
                    </swiper-slide>
                  </swiper>
                  <div class="table-data-tbody" v-else>
                    <div
                      class="tr"
                      v-for="(item, index) in airWarning"
                      :key="index"
                    >
                      <div class="td">
                        {{ item.type == 1 ? "空气" : "水质" }}
                      </div>
                      <div class="td">{{ item.stationName }}</div>
                      <div class="td">
                        <a-tooltip>
                          <template slot="title">{{
                            item.alarmContent
                          }}</template>
                          {{ item.alarmContent }}
                        </a-tooltip>
                      </div>
                      <div class="td">{{ item.alarmTime }}</div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="content-title">监测站点监测设备和参数的预警信息</div>
            </div>
          </div>
        </div>
        <div :style="{ display: curMainIndex === 4 ? 'block' : 'none' }">
          <div class="swiper-item" v-if="curMainIndex === 4">
            <div>
              <div @mouseenter="clearMainTimer()" @mouseleave="openMainTimer()">
                <div class="title title-select">
                  <div class="title-shadow">{{ `设备信息` }}</div>
                  <div>
                    <a-select
                      v-model="stationTypeName"
                      class="title-select-size title-select-size-type"
                      @change="stationTypeNameChange"
                    >
                      <a-icon
                        slot="suffixIcon"
                        type="caret-down"
                        style="color: white; width: 14px; height: 8px"
                      />
                      <a-select-option :value="'空气'">空气</a-select-option>
                      <a-select-option :value="'水'">水</a-select-option>
                    </a-select>
                    <!-- 空气 -->
                    <template v-if="stationTypeName == '空气'">
                      <!-- 站点类型 -->
                      <a-select
                        v-model="stationTypeIndnx"
                        class="title-select-size title-select-size-airname"
                        @change="stationTypeCodeChange"
                      >
                        <a-icon
                          slot="suffixIcon"
                          type="caret-down"
                          style="color: white; width: 14px; height: 8px"
                        />
                        <a-select-option
                          :value="String(index)"
                          v-for="(item, index) in airStationTypeList"
                          :key="index"
                        >
                          {{ item.stationTypeName }}</a-select-option
                        >
                      </a-select>
                      <!-- 站点名称 -->
                      <a-select
                        v-model="airStationTypeName"
                        class="title-select-size"
                        @change="airStationTypeNameChange"
                      >
                        <a-icon
                          slot="suffixIcon"
                          type="caret-down"
                          style="color: white; width: 14px; height: 8px"
                        />
                        <a-select-option
                          :value="String(index)"
                          v-for="(item, index) in airStationTypeList[
                            stationTypeIndnx
                          ].stationList"
                          :key="index"
                        >
                          {{ item.positionName }}</a-select-option
                        >
                      </a-select>
                    </template>
                    <!-- 水 -->
                    <template v-if="stationTypeName == '水'">
                      <!-- 站点名称 -->
                      <a-select
                        v-model="stationIdIndex"
                        class="title-select-size title-select-size-water"
                        @change="stationIdIndexChange"
                      >
                        <a-icon
                          slot="suffixIcon"
                          type="caret-down"
                          style="color: white; width: 14px; height: 8px"
                        />
                        <a-select-option
                          :value="String(index)"
                          v-for="(item, index) in waterStation"
                          :key="index"
                        >
                          {{ item.stationName }}</a-select-option
                        >
                      </a-select>
                    </template>
                  </div>
                </div>
                <div class="sub-title">
                  <img src="@/assets/biaoti_long.png" alt class="title-img" />
                </div>
                <div class="common-content-inner">
                  <table border="1" class="table-site">
                    <tr>
                      <td class="table-name">设备站点</td>
                      <td
                        colspan="3"
                        class="table-text"
                        v-if="stationTypeName == '空气'"
                      >
                        {{ equipmentDetail.positionName }}
                      </td>
                      <td
                        colspan="3"
                        class="table-text"
                        v-if="stationTypeName == '水'"
                      >
                        {{ equipmentDetail.stationName }}
                      </td>
                    </tr>
                    <tr>
                      <td class="table-name">设备类型</td>
                      <td class="table-text">
                        {{ stationTypeName == "空气" ? "空气站" : "水站" }}
                      </td>
                      <td class="table-name">设备状态</td>
                      <td
                        class="table-text"
                        style="display: flex; align-items: center"
                      >
                        <img
                          src="@/assets/state.png"
                          alt=""
                          style="
                            width: 0.15rem;
                            height: 0.14rem;
                            margin-right: 0.1rem;
                          "
                          v-if="equipmentDetail.online"
                        />
                        <img
                          src="@/assets/lxtb.png"
                          alt=""
                          style="
                            width: 0.15rem;
                            height: 0.15rem;
                            margin-right: 0.1rem;
                          "
                          v-if="!equipmentDetail.online"
                        />
                        {{ equipmentDetail.online ? "在线" : "离线" }}
                      </td>
                    </tr>
                    <tr>
                      <td class="table-name">设备位置</td>
                      <td
                        colspan="3"
                        class="table-text"
                        v-if="stationTypeName == '空气'"
                      >
                        {{ equipmentDetail.address }}
                      </td>
                      <td
                        colspan="3"
                        class="table-text"
                        v-if="stationTypeName == '水'"
                      >
                        {{ equipmentDetail.stationAddress }}
                      </td>
                    </tr>
                    <tr>
                      <td class="table-name">设备参数</td>
                      <td colspan="3" class="table-text">6参数</td>
                    </tr>
                    <tr>
                      <td class="table-name">更新频率</td>
                      <td colspan="3" class="table-text">5分钟</td>
                    </tr>
                  </table>
                </div>
              </div>
              <div class="content-title">设备信息状态查询</div>
            </div>
            <div>
              <div @mouseenter="clearMainTimer()" @mouseleave="openMainTimer()">
                <div class="title title-select">
                  <div class="title-shadow">{{ `设备数量` }}</div>
                </div>
                <div class="sub-title">
                  <img src="@/assets/biaoti_long.png" alt class="title-img" />
                </div>
                <div class="common-content-inner content-twos">
                  <div v-for="(item, index) in stateList" :key="index">
                    <SitePie
                      :id="`siteTypeS${index}`"
                      :width="'1.3rem'"
                      :height="'1.5rem'"
                      :propData="item"
                    />
                  </div>
                </div>
              </div>
              <div class="content-title">设备在线状态查看</div>
            </div>
            <div>
              <div @mouseenter="clearMainTimer()" @mouseleave="openMainTimer()">
                <div class="title">
                  <div class="title-shadow">{{ `离线设备列表` }}</div>
                </div>
                <div class="sub-title">
                  <img src="@/assets/biaoti_long.png" alt class="title-img" />
                </div>
                <div class="common-content-inner content-threes">
                  <div class="table-data">
                    <div class="table-data-thead">
                      <div class="tr">
                        <div class="th">名称</div>
                        <div class="th">类型</div>
                        <div class="th">位置</div>
                        <div class="th">时间</div>
                      </div>
                    </div>
                    <swiper
                      :options="swiperOptionWarn"
                      class="table-data-tbody"
                      v-if="offLineList.length >= 5"
                    >
                      <swiper-slide
                        class="tr"
                        v-for="(item, index) in offLineList"
                        :key="index"
                      >
                        <div class="td" :title="item.name">{{ item.name }}</div>
                        <div class="td" :title="item.type">{{ item.type }}</div>
                        <div
                          class="td"
                          :title="item.address"
                          style="text-align: left"
                        >
                          {{ item.address }}
                        </div>
                        <div class="td">
                          {{ item.offlineTime ? item.offlineTime : "-" }}
                        </div>
                      </swiper-slide>
                    </swiper>
                    <div class="table-data-tbody" v-else>
                      <div
                        class="tr"
                        v-for="(item, index) in offLineList"
                        :key="index"
                      >
                        <div class="td" :title="item.name">{{ item.name }}</div>
                        <div class="td" :title="item.type">{{ item.type }}</div>
                        <div
                          class="td"
                          :title="item.address"
                          style="text-align: left"
                        >
                          {{ item.address }}
                        </div>
                        <div class="td">
                          {{ item.offlineTime ? item.offlineTime : "-" }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="content-title">离线设备状态告警</div>
            </div>
          </div>
        </div>
        <div :style="{ display: curMainIndex === 5 ? 'block' : 'none' }">
          <div class="swiper-item" v-if="curMainIndex === 5">
            <div>
              <div>
                <div class="title title-select">
                  <div class="title-shadow">{{ `任务完成情况` }}</div>
                  <div>
                    <a-select
                      v-model="selectTaskTypeCompletionType"
                      class="title-select-size title-select-size-type"
                      @change="getSelectTaskTypeCompletionChange"
                      :key="1"
                    >
                      <a-icon
                        slot="suffixIcon"
                        type="caret-down"
                        style="color: white; width: 14px; height: 8px"
                      />
                      <a-select-option
                        v-for="item in timeTypeList1"
                        :key="'qwe' + item.value"
                        :value="item.value"
                        >{{ item.label }}</a-select-option
                      >
                    </a-select>
                  </div>
                </div>
                <div class="sub-title">
                  <img src="@/assets/biaoti_long.png" alt class="title-img" />
                </div>
                <div class="common-content-inner">
                  <taskProcessing
                    v-if="curMainIndex === 5"
                    :id="'taskProcessing1' + new Date().getTime()"
                    :width="'100%'"
                    :height="'2.2rem'"
                    :propData="completeData"
                    :smooth="true"
                  />
                </div>
              </div>
              <div class="content-title">综合任务完成统计</div>
            </div>
            <div>
              <div>
                <div class="title title-select">
                  <div class="title-shadow">{{ `任务分类统计` }}</div>
                  <div>
                    <a-select
                      v-model="taskType"
                      class="title-select-size title-select-size-type"
                      @change="getSelectTypeStatisticChange"
                      :key="2"
                    >
                      <a-icon
                        slot="suffixIcon"
                        type="caret-down"
                        style="color: white; width: 14px; height: 8px"
                      />
                      <a-select-option
                        v-for="(item, index) in timeTypeList"
                        :key="'asd' + index"
                        :value="item.value"
                        >{{ item.label }}</a-select-option
                      >
                    </a-select>
                  </div>
                </div>
                <div class="sub-title">
                  <img src="@/assets/biaoti_long.png" alt class="title-img" />
                </div>
                <div class="common-content-inner content-twos">
                  <div class="content-list" v-if="contentListVisiable">
                    <!-- 大气环境 -->
                    <div
                      class="content-item"
                      v-for="item in taskClassificationStatistics"
                      :key="item.id"
                    >
                      <div class="img-box">
                        <img :src="item.icon" alt="" />
                      </div>
                      <div class="content-info">
                        <div class="content-info-title">{{ item.name }}</div>
                        <div class="number">
                          {{ item.total }} <span class="unit">件</span>
                        </div>
                      </div>
                    </div>
                    <!-- <div class="content-item">
                      <div class="img-box">
                        <img src="../assets/swiperHome/shj.png" alt="">
                      </div>
                      <div class="content-info">
                        <div class="content-info-title">水环境</div>
                        <div class="number">17 <span class="unit">件</span></div>
                      </div>
                    </div>
                    <div class="content-item">
                      <div class="img-box">
                        <img src="../assets/swiperHome/wry.png" alt="">
                      </div>
                      <div class="content-info">
                        <div class="content-info-title">污染源</div>
                        <div class="number">17 <span class="unit">件</span></div>
                      </div>
                    </div>
                    <div class="content-item">
                      <div class="img-box">
                        <img src="../assets/swiperHome/rwdd.png" alt="">
                      </div>
                      <div class="content-info">
                        <div class="content-info-title">任务调度</div>
                        <div class="number">17 <span class="unit">件</span></div>
                      </div>
                    </div> -->
                  </div>
                </div>
              </div>
              <div class="content-title">任务完成分类统计</div>
            </div>
            <div>
              <div>
                <div class="title">
                  <div class="title-shadow">{{ `任务执行率` }}</div>
                </div>
                <div class="sub-title">
                  <img src="@/assets/biaoti_long.png" alt class="title-img" />
                </div>
                <div class="common-content-inner content-threes">
                  <taskExecutionRateLine
                    v-if="curMainIndex === 5"
                    :id="'dayTask1' + new Date().getTime()"
                    :width="'100%'"
                    :height="'2.2rem'"
                    :propData="executionRate"
                    :smooth="true"
                  ></taskExecutionRateLine>
                </div>
              </div>
              <div class="content-title">任务执行率趋势图例</div>
            </div>
          </div>
        </div>
      </div>
      <div class="change-main" v-if="curMainIndex != 0">
        <div v-for="index in 5" :key="index" @click="changeIndex(index)">
          <div :class="{ active: index == curMainIndex }"></div>
        </div>
      </div>
    </div>
    <!--    大气环境右侧-->
    <template v-if="pageType === 'air'">
      <div class="construction-plant-dep pu-randa-r2l">
        <div class="video-main">
          <div class="common-title box-title">
            <div class="title">{{ `全区空气质量街道排名` }}</div>
            <!-- <div class="unit">单位：ug/m³</div> -->
          </div>

          <div class="sub-title" style="margin: 0">
            <img src="@/assets/biaoti.png" alt class="title-img" />
            <div v-if="rankingAlarmList.length > 0" class="gongditable">
              <div class="table-header">
                <div>排行</div>
                <div>街道名称</div>
                <div>AQI</div>
                <div>等级</div>
                <div>首要污染物</div>
              </div>
              <div class="table_content">
                <div
                  class="table-body"
                  v-for="(item, index) in rankingAlarmList"
                  :key="index"
                >
                  <div class="rankIndex" :class="{ firstRank: index === 0 }">
                    {{ index + 1 }}
                  </div>
                  <div>{{ item.streetName }}</div>
                  <div class="api_box">
                    <span class="aqi" :style="{ background: item.bgColor }">{{
                      item.aqi
                    }}</span>
                  </div>
                  <div :style="{ color: item.textColor }">
                    {{ classList[Number(item.levelId) - 1] }}
                  </div>
                  <div>{{ item.primaryPollutant }}</div>
                </div>
              </div>
            </div>
            <div v-else class="no-data">暂无排名信息</div>
          </div>
        </div>
      </div>
      <div class="construction-plant-dep2 pu-randa-r2l">
        <div class="video-main">
          <div class="common-title box-title">
            <div class="title">{{ `污染源关联分析` }}</div>
            <a-select
              style="width: 1rem"
              @change="changeDistance"
              v-model="distance"
              class="select-main"
            >
              <a-icon
                slot="suffixIcon"
                type="caret-down"
                style="color: rgba(0, 234, 255, 1); width: 14px; height: 8px"
              />
              <a-select-option
                v-for="item in distanceList"
                :key="item.name"
                :value="item.value"
              >
                {{ item.name }}
              </a-select-option>
            </a-select>
          </div>

          <div class="sub-title" style="margin: 0">
            <img src="@/assets/biaoti.png" alt class="title-img" />
          </div>
          <div
            class="second_List"
            style="height: 150px; overflow:auto"
            v-if="pollutionStationList && pollutionStationList.length"
          >
            <div
              class="second_item"
              v-for="item in pollutionStationList"
              :key="item.id"
              @click="handleClickPollutionItem(item.id)"
            >
              <div class="left_box">
                <h2 class="title">{{ item.name }}</h2>
                <div class="content_box">
                  <!-- <div class="item_item">
                    <span class="label">设备状态：</span>
                    <span class="label">{{item.devieList && item.devieList.length && item.devieList[0].isOnline ? '在线' : '离线'}}</span>
                  </div> -->
                  <div class="item_item">
                    <span class="label">污染源类型：</span>
                    <span class="label">{{
                      pollutionTypeObj[item.pollutionType]
                    }}</span>
                  </div>
                  <!-- <div class="item_item">
                    <span class="label">浓度：</span>
                    <span class="label">
                      <span class="number">12.3</span>
                      <span class="unit">ug/m</span>
                    </span>
                  </div> -->
                </div>
              </div>
              <div class="right_box">
                <span class="number">{{ item.distance }}</span>
                <span class="unit">km</span>
              </div>
            </div>
          </div>
          <div
            v-else
            class="second_List"
            style="height: 150px;border-box: box-sizing; padding-right: 20px;display: flex; align-items: center; justify-content: center"
          >
            暂无数据
          </div>
        </div>
      </div>
      <div class="construction-plant-dep3 pu-randa-r2l">
        <div class="video-main">
          <div class="common-title box-title">
            <div class="title">{{ `空气污染气象条件预报` }}</div>
          </div>

          <div class="sub-title" style="margin: 0">
            <img src="@/assets/biaoti.png" alt class="title-img" />
          </div>
          <div class="third_content">
            <div
              class="third_item"
              v-for="(item, index) in air_pollution_diffusionList"
              :key="index"
            >
              <div class="time_title">
                <span>{{ item.monitorDate }}</span>
              </div>
              <div class="third_item_content">
                <div class="td_box">
                  <div class="tr_box_1">气象扩散条件等级</div>
                  <div class="tr_box_2">预计AQI</div>
                </div>
                <div class="td_box">
                  <div class="tr_box_1">
                    {{ item.airPollutionMeteorologicalIndexLevel }}
                  </div>
                  <div class="tr_box_2">{{ item.airQualityIndex }}</div>
                </div>
                <div class="td_box">
                  <div class="tr_box_1">空气质量等级</div>
                  <div class="tr_box_2">首要污染物</div>
                </div>
                <div class="td_box">
                  <div class="tr_box_1">{{ item.airQualityIndexLevel }}</div>
                  <div class="tr_box_2">{{ item.primaryPollutant }}</div>
                </div>
                <div class="td_box content" :title="item.remark">
                  {{ item.remark }}
                </div>
              </div>
            </div>
          </div>
          <div
            class="third_content"
            style="height: 260px; display: flex; justify-content: center; align-items: center"
          >
            暂无数据
          </div>
        </div>
      </div>
    </template>

    <!--    气象环境右侧-->
    <template v-if="pageType === 'weather'">
      <!--      气象数据排名-->
      <div
        class="construction-plant-dep pu-randa-r2l"
        style="border: none;background-color: transparent"
      >
        <div class="video-main">
          <div
            class="common-title box-title title-flex"
            style="display: flex;justify-content: space-between"
          >
            <div class="title">气象数据排名</div>
            <div>
              <div
                :class="{ 'type-active': formData.type === 2 }"
                @click="changeType(2)"
              >
                区域排名
              </div>
              <div
                :class="{ 'type-active': formData.type === 1 }"
                @click="changeType(1)"
              >
                站点排名
              </div>
            </div>
          </div>

          <div
            class="sub-title"
            style="margin: 0;display: flex;flex-direction: column;background: rgba(0,0,0,.3)"
          >
            <img src="@/assets/biaoti.png" alt class="title-img" />
            <div style="display: flex;gap: 0 10px;margin-top: 10px;">
              <a-select
                style="width: 1rem;margin-left: auto;"
                @change="getWeatherSort"
                v-model="formData.timeType"
                class="select-main"
              >
                <a-icon
                  slot="suffixIcon"
                  type="caret-down"
                  style="color: rgba(0, 234, 255, 1); width: 14px; height: 8px"
                />
                <a-select-option
                  v-for="item in dateTypeList"
                  :key="item.name"
                  :value="item.value"
                >
                  {{ item.name }}
                </a-select-option>
              </a-select>
              <el-date-picker
                v-model="formData.findTime"
                :type="datePickerType[formData.timeType]"
                value-format="yyyy-MM-dd HH:mm:ss"
                placeholder="选择日期"
                class="date-picker-top"
                popper-class="picker_class"
                @change="getWeatherSort"
              >
              </el-date-picker>
            </div>

            <div v-loading="loading" class="gongditable">
              <div class="table-header">
                <div>排行</div>
                <div>名称</div>
                <div
                  style="display: flex;align-items: center;margin-left: 3px;"
                >
                  <span>温度</span>
                  <div style="display: flex;flex-direction: column">
                    <img
                      src="@/assets/map_icon/arrow.svg"
                      alt=""
                      class="arrow-icon"
                      :class="{ active: sortData.temperature === 'asc' }"
                      @click="sort('temperature', 'asc')"
                    />
                    <img
                      src="@/assets/map_icon/arrow.svg"
                      alt=""
                      class="arrow-icon"
                      :class="{ active: sortData.temperature === 'desc' }"
                      style="transform: rotate(180deg)"
                      @click="sort('temperature', 'desc')"
                    />
                  </div>
                </div>
                <div
                  style="display: flex;align-items: center;margin-left: 3px;"
                >
                  <span>降水量</span>
                  <div style="display: flex;flex-direction: column">
                    <img
                      src="@/assets/map_icon/arrow.svg"
                      alt=""
                      class="arrow-icon"
                      :class="{ active: sortData.rainfall === 'asc' }"
                      @click="sort('rainfall', 'asc')"
                    />
                    <img
                      src="@/assets/map_icon/arrow.svg"
                      alt=""
                      class="arrow-icon"
                      :class="{ active: sortData.rainfall === 'desc' }"
                      style="transform: rotate(180deg)"
                      @click="sort('rainfall', 'desc')"
                    />
                  </div>
                </div>
              </div>
              <div
                v-if="tableList.length > 0"
                class="table_content"
                style="height: 350px"
              >
                <div
                  class="table-body"
                  v-for="(item, index) in tableList"
                  :key="index"
                >
                  <div class="rankIndex" :class="{ firstRank: index === 0 }">
                    {{ index + 1 }}
                  </div>
                  <div>{{ item.name }}</div>
                  <div style="color: #00F283">{{ item.temperature }}℃</div>
                  <div style="color: #00F283">{{ item.rainfall }}mm</div>
                </div>
              </div>
              <div v-else class="no-data" style="height: 350px;">
                暂无排名信息
              </div>
            </div>
          </div>
        </div>
      </div>

      <div
        class="construction-plant-dep2 pu-randa-r2l"
        style="margin-top: 15px;top: 500px"
      >
        <div class="video-main">
          <div class="common-title box-title">
            <div class="title">24小时站点数据趋势</div>
            <a-select
              style="width: 2rem"
              @change="changeWeatherStation"
              v-model="weatherStationId"
              class="select-main"
            >
              <a-icon
                slot="suffixIcon"
                type="caret-down"
                style="color: rgba(0, 234, 255, 1); width: 14px; height: 8px"
              />
              <a-select-option
                v-for="item in weatherStationList"
                :key="item.stationId"
                :value="item.stationId"
              >
                {{ item.stationName }}
              </a-select-option>
            </a-select>
          </div>

          <div class="sub-title" style="margin: 0">
            <img src="@/assets/biaoti.png" alt class="title-img" />
          </div>
          <div
            v-loading="trendLoading"
            class="common-content-inner type-radio-water"
            style="margin-top: 20px;"
          >
            <a-radio-group
              v-model="trendType"
              size="small"
              buttonStyle="solid"
              style="width: fit-content"
              @change="trendTypeChange"
            >
              <a-radio-button
                v-for="item in trendData.itemList"
                :key="item.id"
                :value="item.chineseName"
                class="water-monitor-tab"
                >{{ item.chineseName }}</a-radio-button
              >
            </a-radio-group>
            <div v-if="trendChartData">
              <LineChartDashed
                style="margin-top: 0.1rem"
                id="trendChart"
                width="100%"
                height="3.7rem"
                :propData="trendChartData"
                lineColor="#0897D4"
                smooth
              />
            </div>
          </div>

          <!--          <div class="second_List" style="height: 150px; overflow:auto" v-if="pollutionStationList && pollutionStationList.length">-->
          <!--            <div class="second_item" v-for="item in pollutionStationList" :key="item.id" @click="handleClickPollutionItem(item.id)">-->
          <!--              <div class="left_box">-->
          <!--                <h2 class="title">{{item.name}}</h2>-->
          <!--                <div class="content_box">-->
          <!--                  &lt;!&ndash; <div class="item_item">-->
          <!--                    <span class="label">设备状态：</span>-->
          <!--                    <span class="label">{{item.devieList && item.devieList.length && item.devieList[0].isOnline ? '在线' : '离线'}}</span>-->
          <!--                  </div> &ndash;&gt;-->
          <!--                  <div class="item_item">-->
          <!--                    <span class="label">污染源类型：</span>-->
          <!--                    <span class="label">{{pollutionTypeObj[item.pollutionType]}}</span>-->
          <!--                  </div>-->
          <!--                  &lt;!&ndash; <div class="item_item">-->
          <!--                    <span class="label">浓度：</span>-->
          <!--                    <span class="label">-->
          <!--                      <span class="number">12.3</span>-->
          <!--                      <span class="unit">ug/m</span>-->
          <!--                    </span>-->
          <!--                  </div> &ndash;&gt;-->
          <!--                </div>-->
          <!--              </div>-->
          <!--              <div class="right_box">-->
          <!--                <span class="number">{{item.distance}}</span>-->
          <!--                <span class="unit">km</span>-->
          <!--              </div>-->
          <!--            </div>-->
          <!--          </div>-->
          <!--          <div v-else class="second_List" style="height: 150px;border-box: box-sizing; padding-right: 20px;display: flex; align-items: center; justify-content: center">-->
          <!--            暂无数据-->
          <!--          </div>-->
        </div>
      </div>
    </template>

    <div class="topTab">
      <div
        class="air_btn"
        :class="pageType == 'air' ? 'btn_active' : ''"
        style="cursor: pointer"
        @click="pageTypeCHange('air')"
      >
        大气环境
      </div>
      <!--      <div class="water_btn" style="cursor: not-allowed">-->
      <!--        &lt;!&ndash; <div-->
      <!--        class="water_btn"-->
      <!--        :class="pageType == 'water' ? 'btn_active' : ''"-->
      <!--        :style="topBtnFlg?'cursor: pointer;':'cursor: not-allowed;'"-->
      <!--        @click="pageTypeCHange('water')"-->
      <!--      > &ndash;&gt;-->
      <!--        &lt;!&ndash; style="cursor: not-allowed" &ndash;&gt;-->
      <!--        水环境-->
      <!--      </div>-->
      <div
        class="air_btn"
        :class="pageType == 'weather' ? 'btn_active' : ''"
        style="cursor: pointer"
        @click="toWeatherPage"
      >
        气象环境
      </div>
    </div>
    <!--    大气环境-->
    <div v-if="pageType === 'air'" class="leftTab">
      <div
        v-for="item in pageType == 'air' ? leftTabs : leftWaterTabs"
        :key="item.id"
        class="item"
        :class="curtype == item.id ? 'itemActive' : ''"
        @click="curtypeChange(item)"
      >
        {{ item.name }}
      </div>
    </div>
    <template v-if="pageType === 'air'">
      <div class="bottomTab">
        <div
          class="item"
          :class="curDataType == 1 ? 'item_active' : ''"
          @click="toggleType('1')"
        >
          天
        </div>
        <div
          class="item"
          :class="curDataType == 2 ? 'item_active' : ''"
          @click="toggleType('2')"
        >
          月
        </div>
        <div
          v-if="false"
          class="item"
          :class="curDataType == 3 ? 'item_active' : ''"
          @click="toggleType('3')"
        >
          年
        </div>
      </div>
      <div class="timeRuler">
        <div class="playBtn" @click="playBtnFn">
          <img
            :src="playStutas ? bof : zant"
            alt=""
            style="width: 18px; height: 22px"
          />
        </div>
        <div class="timeCom">
          <template v-if="curDataType == 1">
            <div class="timeShow">当前时间：{{ showTime }}</div>
            <div class="timeline">
              <TimeLine
                ref="Timeline2"
                :initTime="currentTime"
                @timeChange="timeChange2"
                :timeSegments="timeSegments"
              ></TimeLine>
            </div>
          </template>

          <template v-else-if="curDataType == 2">
            <div class="timeShow">当前时间：{{ showTime2 }}</div>
            <div class="timeline">
              <TimeLine
                ref="Timeline2"
                :initTime="currentTime"
                @timeChange="timeChange2"
                :timeSegments="timeSegments"
                :initZoomIndex="7"
              ></TimeLine>
            </div>
          </template>

          <div v-else class="ruler">
            <div class="num">
              <div
                class="item"
                v-for="v in ruleCaliNum"
                :key="v"
                @click="rulerClick"
                :data-index="v"
              ></div>
            </div>

            <div class="color" :style="{ right: 1728 - curSite + 'px' }"></div>
            <div class="tips" :style="{ left: curSite - 30 + 'px' }">
              {{ toHourMin(curSite) }}
            </div>
            <div class="calibration">
              <!-- moment().daysInMonth() -->
              <template v-if="curDataType == 1">
                <div class="item" v-for="v in ruleCaliNum" :key="v">
                  <span
                    class="timeNum"
                    v-show="
                      ((v - 1) % 4 == 0 && pageType == 'water') ||
                        pageType == 'air'
                    "
                    >{{
                      v - 1 > 9 ? v - 1 + ":00" : "0" + (v - 1 + "") + ":00"
                    }}</span
                  >
                  <span v-if="v === 24">24:00</span>
                </div>
              </template>
              <template v-if="curDataType == 2">
                <div class="item" v-for="v in ruleCaliNum" :key="v">
                  <span class="timeNum" style="left: -0.05rem">{{
                    v > 9 ? v : "0" + v
                  }}</span>
                </div>
              </template>
              <template v-if="curDataType == 3">
                <div class="item" v-for="v in ruleCaliNum" :key="v">
                  <span class="timeNum" style="left: -0.05rem">{{
                    v > 9 ? v : "0" + v
                  }}</span>
                </div>
              </template>
            </div>
          </div>
          <div class="rili">
            <!--          <i v-if="curDataType != 1" class="el-icon-date rili_icon"></i>-->
            <el-date-picker
              key="1"
              v-if="curDataType == 1"
              v-model="dateRange"
              type="datetimerange"
              size="small"
              :default-time="['00:00:00', '23:59:59']"
              popper-class="picker_class"
              class="date-picker"
              prefix-icon="null"
              :clearable="false"
              :editable="false"
              :picker-options="pickerOptions"
              @change="dateRangeChange"
              placeholder="选择日期"
            >
            </el-date-picker>
            <el-date-picker
              key="2"
              v-if="curDataType == 2"
              v-model="dateRange"
              type="daterange"
              format="yyyy年MM月dd日"
              size="small"
              popper-class="picker_class"
              prefix-icon="null"
              :clearable="false"
              class="date-picker"
              :editable="false"
              :picker-options="pickerOptions"
              @change="dateRangeChange"
              placeholder="选择日期"
            >
            </el-date-picker>
            <el-date-picker
              key="3"
              v-if="curDataType == 3"
              v-model="selDate"
              value-format="yyyy-MM-dd"
              format="yyyy年"
              type="year"
              size="small"
              popper-class="picker_class"
              prefix-icon="null"
              :clearable="false"
              :editable="false"
              :picker-options="pickerOptions"
              @change="clearAll"
              placeholder="选择日期"
            >
            </el-date-picker>
          </div>
        </div>
      </div>
    </template>
    <!-- 气象筛选 -->
    <div class="weather_box">
      <div
        class="weather_item"
        :class="{ active: isShowWind }"
        @click="isShowWind = !isShowWind"
      >
        <img :src="isShowWind ? activeWindImage : defaultWindImage" alt="" />
        <span>风场</span>
      </div>
      <!-- <div
        class="weather_item"
        @click="weatherType = item.type"
        :class="{ active: item.type === weatherType }"
        v-for="item in weatherIconList"
        :key="item.type"
      >
        <img
          :src="
           item.defaultImage
          "
          alt=""
        />
        <span>{{ item.text }}</span>
      </div> -->
    </div>
    <!-- 自然灾害预警 -->
    <div
      class="animimate_box"
      v-if="
        meteorologyWarningList && meteorologyWarningList.length && showWarnBox
      "
    >
      <vue-seamless-scroll
        :data="meteorologyWarningList"
        :class-option="defaultOption"
      >
        <div style=" height: 233px;display: flex;overflow:hidden;">
          <div
            class="warning_box"
            v-for="(item, index) in meteorologyWarningList"
            :key="index"
          >
            <h2 class="title">
              <span class="title_text">{{ item.typeName }}</span>
              <span class="title_date">{{ item.effective }}</span>
            </h2>
            <div class="content_box">
              <div class="left_box">
                <img
                  class="status_img"
                  src="@/assets/rehearseAnalyse/<EMAIL>"
                  alt=""
                />
                <div class="status">
                  <span class="text">{{ item.level }}</span>
                </div>
              </div>
              <div class="right_box" :title="item.description">
                {{ item.description }}
              </div>
            </div>
          </div>
        </div>
      </vue-seamless-scroll>
    </div>

    <!-- 弹框部分-->
    <a-modal
      :visible="visibleCheck"
      class="ant-modal-recheck"
      :footer="null"
      :destroyOnClose="true"
      @cancel="handleCancel"
    >
      <div class="smapContainer">
        <div
          class="sigleSmBox"
          v-for="(item, i) in samllMapList"
          :key="i"
          :style="{ 'margin-right': i / 5 || i == 0 ? '22px' : '0' }"
        >
          <div class="dzIcon">
            <img
              src="../assets/recheck/<EMAIL>"
              style="width: 18px; height: 21px; margin-left: 10px"
              alt=""
            />
            {{ toMoadlTime(selDate) }}{{ curDataType == "1" ? i : i + 1
            }}{{ curDataType == "1" ? "时" : "日" }}
          </div>
          <img
            src="../assets/recheck/<EMAIL>"
            class="smBorderImg"
            alt=""
          />
          <div class="smImgBox">
            <img :src="item.url" class="smImg" @click="preview(item)" />
          </div>
        </div>
      </div>
      <preview-image
        ref="previewImage"
        :show-viewer="showViewer"
        :url-list="urlList"
        @closeViewer="closeViewer"
      />
    </a-modal>
    <!-- 污染源详情 -->
    <div ref="modal" style="width:100%;height:100%">
      <a-modal
        :visible.sync="dialogVisible"
        class="ant-modal-recheck"
        :footer="null"
        :maskClosable="true"
        :centered="true"
        @cancel="handleCancel"
        :getContainer="() => $refs.modal"
        style="top: 0.2rem !important;"
      >
        <div class="content">
          <pollutionSourcesDetail v-if="dialogVisible" :id="companyId" />
        </div>
      </a-modal>
    </div>
  </section>
</template>

<script lang="ts">
import dayjs from "dayjs";
import LineChartDashed from "@/components/Charts/LineChartDashed.vue";
import moment from "moment";
import { Component, Vue, Watch } from "vue-property-decorator";
import { Swiper, SwiperSlide } from "vue-awesome-swiper";
import DepartmentBgMap from "@/components/GaoDeMap/rehearseMap.vue";
import DepartmentMapSmall from "@/components/GaoDeMap/deparmentMapSmall.vue";
import { Icon, Modal, Radio, Select, Table, Tooltip } from "ant-design-vue";
import LineChart from "@/components/Charts/LineChart.vue";
import PollutionPie from "@/components/Charts/PollutionPie.vue";
import PieChartSolid from "@/components/Charts/PieChartSolid.vue";
import DoublePie from "@/components/Charts/DoublePie.vue";
import RadarChart from "@/components/Charts/RadarChart.vue";
import SitePie from "@/components/Charts/SitePie.vue";
import taskExecutionRateLine from "@/components/Charts/taskExecutionRateLine.vue";
import taskProcessing from "@/components/Charts/taskProcessing.vue";
import PreviewImage from "./previewimg.vue";

import {
  airStation,
  airTrend,
  earlyWarning,
  getAirTypes,
  getCarList,
  getOffline,
  getOnlineCount,
  heavilyPollutingEnterpriseList,
  pollutionCount,
  recentWeather,
  selectMonthTaskCompletion,
  selectTaskTypeCompletion,
  selectTypeStatistic,
  typeStationInfoList,
  waterQuality,
  waterStationRecord,
} from "@/api/homeTable";
import { getAllAqiInfo } from "@/api/air";
import { getMonitorItemDetailRecord, getStationList } from "@/api/water";
import gif01 from "@/assets/sunlight.gif";
import gif02 from "@/assets/cloudy.gif";
import gif03 from "@/assets/overcast.gif";
import gif04 from "@/assets/rain.gif";
import gif05 from "@/assets/xue.gif";
import bof from "@/assets/recheck/-s-bofang.png";
import zant from "@/assets/recheck/-s-zanting.png";
import airBlank from "@/assets/recheck/air_blank.png";
import waterBlank from "@/assets/recheck/water_blank.png";

import {
  air_pollution_diffusion,
  meteorology_early_warning,
  rangeByPollutionSource,
} from "@/api/practiceCheck";
import vueSeamlessScroll from "vue-seamless-scroll";

import pollutionSourcesDetail from "./pollutionSourcesDetail/index.vue";
import {
  Item,
  WeatherData,
  weatherSort,
  WeatherSortQuery,
  WeatherStation,
  weatherStationList,
  WeatherStationTrend,
  weatherStationTrend,
} from "@/api/weather";
import { AirData } from "@/types";
import markerIcon from "@/assets/map_icon/marker.png";
import markerIconActive from "@/assets/map_icon/marker-active.png";

interface EchartData {
  bottomList: string[];
  dataList: string[];
  unit?: string | number;
  colorType?: string;
}
interface InData {
  name: string;
  dataList: any[];
  colorList: string[];
}

let vm: any = null;

let AMap;
let maps;
let infoWindow;

@Component({
  name: "swiperHome",
  components: {
    LineChart,
    PieChartSolid,
    RadarChart,
    Swiper,
    SwiperSlide,
    ATable: Table,
    ASelect: Select,
    AIcon: Icon,
    ASelectOption: Select.Option,
    ARadioGroup: Radio.Group,
    ARadioButton: Radio.Button,
    DoublePie,
    AModal: Modal,
    ATooltip: Tooltip,
    DepartmentBgMap,
    DepartmentMapSmall,
    PollutionPie,
    SitePie,
    taskExecutionRateLine,
    taskProcessing,
    PreviewImage,
    vueSeamlessScroll,
    pollutionSourcesDetail,
    LineChartDashed,
  },
})
export default class extends Vue {
  private showViewer: any = false;
  private urlList: any = [];
  private preview(item: any) {
    if (!item.flg) return;
    this.urlList = [item.url];
    this.showViewer = true;
  }
  private closeViewer() {
    this.showViewer = false;
    this.urlList = [];
  }
  // 污染源类型 0 汽修 1 工地源 2 工业源 3加油站 4 停车场
  private pollutionTypeObj: any = {
    0: "汽修源",
    1: "工地源",
    2: "工业源",
    3: "加油站",
    4: "停车场",
  };
  private isShowWind: boolean = true;
  defaultWindImage: any = require("@/assets/rehearseAnalyse/<EMAIL>");
  activeWindImage: any = require("@/assets/rehearseAnalyse/<EMAIL>");
  weatherIconList: any = [
    {
      type: 1,
      text: "风速",
      defaultImage: require("@/assets/rehearseAnalyse/<EMAIL>"),
      activeImage: require("@/assets/rehearseAnalyse/<EMAIL>"),
    },
    {
      type: 2,
      text: "温度",
      defaultImage: require("@/assets/rehearseAnalyse/<EMAIL>"),
      activeImage: require("@/assets/rehearseAnalyse/<EMAIL>"),
    },
    {
      type: 3,
      text: "湿度",
      defaultImage: require("@/assets/rehearseAnalyse/<EMAIL>"),
      activeImage: require("@/assets/rehearseAnalyse/<EMAIL>"),
    },
    {
      type: 4,
      text: "气压",
      defaultImage: require("@/assets/rehearseAnalyse/<EMAIL>"),
      activeImage: require("@/assets/rehearseAnalyse/<EMAIL>"),
    },
  ];
  weatherType: number = 1;
  defaultOption: any = {
    step: 5, // 数值越大速度滚动越快
    hoverStop: true, // 是否开启鼠标悬停stop
    openWatch: true, // 开启数据实时监控刷新dom
    direction: 2, // 0向下 1向上 2向左 3向右
    limitMoveNum: 2, // 开始无缝滚动的数据量 this.dataList.length
    // singleHeight: 0, // 单步运动停止的高度(默认值0是无缝不停止的滚动)
    singleWidth: 397, // 单步运动停止的宽度(默认值0是无缝不停止的滚动)
    waitTime: 5000, // 单步运动停止的时间(默认值1000ms)
  };
  stationList: any = [];
  distanceList: any = [
    { name: "0.5公里", value: "500" },
    { name: "1公里", value: "1000" },
    { name: "1.5公里", value: "1500" },
    { name: "2公里", value: "2000" },
  ];
  distance: string = "500";

  /**
   * 气象环境相关变量
   * */
  dateTypeList = [
    { name: "小时", value: 1 },
    { name: "天", value: 2 },
  ];
  datePickerType = {
    1: "datetime",
    2: "date",
  };
  date = null;
  sortData = {
    temperature: null,
    rainfall: null,
  };
  formData: WeatherSortQuery = {
    // 1 站点 2 区域
    type: 2,
    // 1 小时 2 天
    timeType: 1,
    findTime: dayjs().format("YYYY-MM-DD HH:mm:ss"),
    // 排序 temperature ：温度  rainfall：降雨量
    sortItem: null,
    // desc  asc
    sortType: null,
  };

  // 是否显示右下角预警弹窗
  showWarnBox = true;
  toWeatherPage() {
    this.showWarnBox = false;
    this.pageType = "weather";
    this.getWeatherSort();
    this.getWeatherTrend();
    const mapRef = this.$refs.mapRef;
    maps.remove(mapRef.GBLayer);
    maps.remove(mapRef.AirLayer);
    clearInterval(this.interval);
    this.playStutas = false;
  }

  /**改变排名类型*/
  changeType(type: number) {
    this.formData.type = type;
    this.getWeatherSort();
  }

  /**获取气象数据排名数据*/
  tableList: WeatherData[] = [];
  loading = false;
  getWeatherSort() {
    this.loading = true;
    weatherSort(this.formData)
      .then((res) => {
        this.tableList = res.data.data;
      })
      .finally(() => {
        this.loading = false;
      });
  }

  sort(sortItem, sortType) {
    this.sortData[sortItem] = sortType;
    this.formData.sortItem = sortItem;
    this.formData.sortType = sortType;
    this.getWeatherSort();
  }

  /**
   * 24小时站点数据趋势
   * */
  weatherStationId: string = null;
  weatherStationList: WeatherStation[] = [];
  trendLoading = false;
  getWeatherTrend() {
    this.trendLoading = true;
    weatherStationList()
      .then((res) => {
        this.weatherStationList = res.data.data;
        this.weatherStationId = this.weatherStationList[0].stationId;
        this.drawMarker();
        this.changeWeatherStation();
      })
      .finally(() => {
        this.trendLoading = false;
      });
  }

  /**切换观测站点*/
  trendData: Partial<WeatherStationTrend> = {};
  changeWeatherStation() {
    maps.clearInfoWindow();

    // 选中地图上对应marker
    const marker = this.weatherMarkerList.find(
      (item) => item.data.stationId === this.weatherStationId
    );
    this.selectMarker(marker);

    this.trendLoading = true;
    this.trendChartData = null;
    const endTime = dayjs().format("YYYY-MM-DD HH:mm:ss");
    // 往前24小时
    const startTime = dayjs()
      .subtract(1, "day")
      .format("YYYY-MM-DD HH:mm:ss");
    weatherStationTrend({
      type: 1,
      stationId: this.weatherStationId,
      startTime,
      endTime,
    })
      .then((res) => {
        this.trendData = res.data.data;
        this.trendType = this.trendData.itemList[0].chineseName;
        this.trendTypeChange();
      })
      .finally(() => {
        this.trendLoading = false;
      });
  }

  /**在地上标绘观测站点*/
  weatherMarkerList = [];
  preMarker = null;
  drawMarker() {
    //@ts-ignore
    window.closeInfoWindow = this.closeInfoWindow; // 解决字符串模板@click无效的问题

    infoWindow = new AMap.InfoWindow({
      isCustom: true,
      offset: new AMap.Pixel(0, 0),
    });

    for (const item of this.weatherStationList) {
      const marker = new AMap.Marker({
        map: maps,
        zIndex: 99,
        position: new AMap.LngLat(item.lng, item.lat),
        content:
          item.stationId === this.weatherStationId
            ? this.getMarkerContentActive(item)
            : this.getMarkerContent(),
        offset: new AMap.Pixel(0, -50),
      });

      marker.data = item;

      if (item.stationId === this.weatherStationId) {
        this.preMarker = marker;
      }

      marker.on("click", () => {
        maps.clearInfoWindow();
        if (this.weatherStationId === item.stationId) {
          // 显示信息窗
          this.showInfoWindow(marker);
        } else {
          this.weatherStationId = item.stationId;
          this.changeWeatherStation();
        }
      });
      this.weatherMarkerList.push(marker);
    }
  }

  getMarkerContent() {
    return `<div class="weather-marker"></div>`;
  }

  getMarkerContentActive(data) {
    return `
    <div class="weather-marker-active">
      <div class="title">${data.stationName}</div>
      <div class="marker"></div>
    </div>`;
  }

  /**将marker选中*/
  selectMarker(marker) {
    // 取消上一个点选中
    this.preMarker?.setContent(this.getMarkerContent());
    this.preMarker?.setzIndex(99);
    this.preMarker = marker;
    // 设置当前点选中
    marker.setContent(this.getMarkerContentActive(marker.data));
    marker.setzIndex(999);
    // 地图跳转到点击点
    const position = marker.getPosition();
    maps.panTo(position);
  }

  showInfoWindow(marker) {
    const row = marker.data;
    infoWindow.setContent(`
          <div class="weather-info">
            <div class="header">
              <div class="title">${row.stationName}</div>
              <div class="close-btn" onclick="closeInfoWindow()"></div>
            </div>
            <div class="content">
              ${this.trendData.itemList
                .map((item) => {
                  const key = item.chineseName;
                  const data = this.trendData[key];
                  return `
                  <div class="item">
                    <div class="label">${key}</div>
                    ${
                      this.trendData[key].y?.length
                        ? `<div class="value">${this.trendData[key].y.at(-1)}${
                            data.unit
                          }</div>`
                        : `<div class="value">暂无数据</div>`
                    }
                  </div>
              `;
                })
                .join("")}
            </div>
          </div>
        `);
    infoWindow.open(maps, marker.getPosition());
  }

  closeInfoWindow() {
    maps.clearInfoWindow();
  }

  /**切换观测类型*/
  trendType: string;
  trendChartData: AirData;
  trendTypeChange() {
    const current = this.trendData[this.trendType] as Item;
    this.trendChartData = {
      unit: current.unit,
      bottomList: current.x,
      dataList: current.y,
    };
    this.$forceUpdate();
  }

  point: any = {};
  pollutionStationList: any = [];
  executionRate: any = {};
  completeData: any = {};
  timeTypeList: any = [
    { label: "当天", value: 1 },
    { label: "本周", value: 2 },
    { label: "本月", value: 3 },
    { label: "全部", value: 4 },
  ];
  timeTypeList1: any = [
    { label: "当天", value: 1 },
    { label: "本周", value: 2 },
    { label: "本月", value: 3 },
    { label: "全部", value: 4 },
  ];
  selectTaskTypeCompletionType: number | string = 1;
  taskType: number | string = 1;
  taskClassificationStatistics: any = [];
  dqhj: string = require("@/assets/swiperHome/dqhj.png");
  rwdd: string = require("@/assets/swiperHome/rwdd.png");
  shj: string = require("@/assets/swiperHome/shj.png");
  wry: string = require("@/assets/swiperHome/wry.png");
  contentListVisiable: boolean = true;
  swiperOptionAlertS: any = {
    direction: "vertical",
    slidesPerView: 15,
    slidesPerGroup: 1,
    loop: false,
    autoplay: {
      delay: 0,
      disableOnInteraction: false,
    },
  };
  dialogVisible: boolean = false;
  rankingAlarmList: any = [];
  classList: any = ["优", "良", "轻度污染", "中度污染", "重度污染", "严重污染"];

  indexClose: any = 0; // 关闭弹框
  private tableType = "site"; //site
  private siteRanking = false;
  private airTable = [];

  // 空气类型
  @Watch("stationTypeIndnx", { immediate: true, deep: true })
  public onIndex(newValue: number | string, oldValue: number | string) {
    if (newValue) {
      // this.()
    }
  }
  // 空气名称
  @Watch("airStationTypeName", { immediate: true, deep: true })
  public onName(newValue: number | string, oldValue: number | string) {
    if (newValue) {
      // this.()
    }
  }
  // 水名称
  @Watch("stationIdIndex", { immediate: true, deep: true })
  public onStationIdIndex(
    newValue: number | string,
    oldValue: number | string
  ) {
    if (newValue) {
      // this.()
    }
  }

  // private mapStyle = "amap://styles/0739a6632d849ae1b5e6f1c52f7d12c3";
  private mapStyle = "amap://styles/0739a6632d849ae1b5e6f1c52f7d12c3";
  @Watch("curMainIndex", { immediate: true, deep: true }) public onMsgChanged(
    newValue: number,
    oldValue: number
  ) {
    switch (this.curMainIndex) {
      case 1:
        clearInterval(this.airTimer);
        clearInterval(this.waterTimer);
        break;
      case 2:
        this.openAirTimer();
        clearInterval(this.waterTimer);
        break;
      case 3:
        this.openWaterTimer();
        clearInterval(this.airTimer);
        break;
      default:
        clearInterval(this.waterTimer);
        clearInterval(this.airTimer);
        break;
    }
  }
  @Watch("curDataType", { immediate: true })
  private curDataTypeChange(num: any) {
    this.pageTypeFlg = true;
    if (num == 1) {
      this.selDate = moment().format("YYYY-MM-DD");
      this.ruleCaliNum = 24;
      const h = +moment().format("HH");
      const m = +moment().format("mm");
      // const todaySite = h * 72
      const todaySite = h * 72;
      this.curSite = todaySite;
      this.curHour = Math.floor(this.curSite / 72);
    } else if (num == 2) {
      this.clearAll();
      this.selDate = moment()
        .subtract(1, "days")
        .format("YYYY-MM-DD");
      this.ruleCaliNum = moment(this.selDate).daysInMonth();
      const d = +moment().format("DD") - 1;
      const h = +moment().format("HH");
      const DL =
        this.ruleCaliNum == 30
          ? 56.7
          : this.ruleCaliNum == 31
          ? 55.74
          : this.ruleCaliNum == 29
          ? 59.6
          : 61.4;
      const HL =
        this.ruleCaliNum == 30
          ? 2.4
          : this.ruleCaliNum == 31
          ? 2.3
          : this.ruleCaliNum == 29
          ? 2.48
          : 2.56;
      const todaySite = d * DL;
      this.curSite = todaySite;
      this.curHour = Math.floor(this.curSite / DL);
    } else {
      this.ruleCaliNum = 12;
    }
    this.$nextTick(() => {
      clearInterval(this.rulerTimer);
      clearInterval(this.rulerTimerM);
      this.playStutas = false;
      if (!vm || !vm.$refs.mapSwiper) return;
      vm.$refs.mapSwiper.$swiper.slideToLoop(this.curHour, 300, true);
    });
  }
  @Watch("selDate", { deep: true, immediate: true })
  private selDateChange() {
    if (this.curDataType == "1") {
      this.ruleCaliNum = 24;
      // this.getDeduction(this.selDate,this.curtype,1)
    } else if (this.curDataType == "2") {
      // this.getDeduction(this.selDate,this.curtype,1)
      this.ruleCaliNum = moment(this.selDate).daysInMonth();
    } else {
      this.ruleCaliNum = 12;
    }
  }
  @Watch("samllMapList", { deep: true })
  private samllMapListChange() {
    if (!this.samllMapList.length) return;
    this.$nextTick(() => {
      if (!vm || !vm.$refs.mapSwiper) return;
      vm.$refs.mapSwiper.$swiper.slideToLoop(this.curHour, 300, true);
    });
  }
  @Watch("curHour")
  public curHourChange(num: any) {
    if (this.curDataType == "1") {
      if (num == 24) {
        if (!vm.$refs.mapSwiper) return;
        vm.$refs.mapSwiper.$swiper.slideToLoop(23, 300, true);
        return;
      }
      this.$nextTick(() => {
        if (!vm.$refs.mapSwiper) return;
        vm.$refs.mapSwiper.$swiper.slideToLoop(num, 300, true);
      });
    } else if (this.curDataType == "2") {
      if (num == this.ruleCaliNum) {
        if (!vm.$refs.mapSwiper) return;
        vm.$refs.mapSwiper.$swiper.slideToLoop(this.ruleCaliNum - 1, 300, true);
      }
      this.$nextTick(() => {
        if (!vm.$refs.mapSwiper) return;
        vm.$refs.mapSwiper.$swiper.slideToLoop(num, 300, true);
      });
    }
  }
  @Watch("curSite", {})
  private curSiteChange(num: number) {
    if (this.curDataType == "1") {
      const h = +moment().format("HH");
      const m = +moment().format("mm");
      const todaySite = h * 72 + m * 1.2;
      if (
        num > 1728 ||
        (todaySite <= num && moment().format("YYYY-MM-DD") == this.selDate)
      ) {
        clearInterval(this.rulerTimer);
        // if (!this.first) this.playStutas = !this.playStutas
        this.playStutas = false;
        // this.palyFlg = true
      }
    } else if (this.curDataType == "2") {
      this.ruleCaliNum = moment(this.selDate).daysInMonth();
      const d = +moment().format("DD") - 1;
      const h = +moment().format("HH");
      const DL =
        this.ruleCaliNum == 30
          ? 56.7
          : this.ruleCaliNum == 31
          ? 55.74
          : this.ruleCaliNum == 29
          ? 59.6
          : 61.4;
      const HL =
        this.ruleCaliNum == 30
          ? 2.4
          : this.ruleCaliNum == 31
          ? 2.3
          : this.ruleCaliNum == 29
          ? 2.48
          : 2.56;
      const todaySite = d * DL + h * HL;
      if (
        (moment().format("YYYY-MM") == moment(this.selDate).format("YYYY-MM") &&
          todaySite <= num) ||
        num >= 1728
      ) {
        clearInterval(this.rulerTimerM);
        // if (!this.first) this.playStutas = !this.playStutas
        this.playStutas = false;
        // this.palyFlg = true
      }
    }
  }
  @Watch("pageType")
  private pageTypeChange() {
    this.samllMapList = [];
    this.analyse = null;
    this.maxList = [];
    this.maxPoint = null;
    this.pageTypeFlg = true;
    if (this.pageType == "air") {
      this.curtype = "aqi";
      this.curtypeName = "AQI";
    } else {
      this.curtype = "wqi";
      this.curtypeName = "WQI";
    }
  }
  @Watch("curtypeName")
  private curtypeNameChange() {
    this.pageTypeFlg = true;
  }
  mengBan: any = "";
  private curMainIndex = 0;
  private pollutionShow = false;
  private mainTimer: any = null;
  private airTimer: any = null;
  private waterTimer: any = null;
  private waterTimerSelect: any = null;
  private airTimerSelect: any = null;
  private nameList: any = ["空气", "污染", "水质", "设备"];
  // AQI
  readonly AQIAirColors: Array<any> = [
    {
      color: "rgb(0,255,0)",
      textColor: "#333",
      min: 0,
      max: 50,
    },
    {
      color: "rgb(255,255,0)",
      textColor: "#333",
      min: 51,
      max: 100,
    },
    {
      color: "rgb(255,126,0)",
      textColor: "white",
      min: 101,
      max: 150,
    },
    {
      color: "rgb(255,0,0)",
      textColor: "white",
      min: 151,
      max: 200,
    },
    {
      color: "rgb(153,0,76)",
      textColor: "white",
      min: 201,
      max: 300,
    },
    {
      color: "rgb(126,0,35)",
      textColor: "white",
      min: 301,
      max: 999,
    },
  ];
  //气象监测
  private weather = {};
  // 空气污染
  private airQuality: any[] = [];
  // 水质污染
  private waterQuality: any[] = [];
  private waterTimeType = "1";
  // 水质监测趋势
  waterTrendAll: any[] = [];
  curWaterIndex = 0;
  // 空气监测趋势类型
  private airTypes: { name: string; value: string | number }[] = [];
  // 空气监测趋势选中下标
  private airTypeIndex = 0;
  // 空气监测趋势
  private airMonitoring: EchartData = {
    bottomList: [],
    dataList: [],
    unit: "",
    colorType: "",
  };
  //车辆类型分布
  private carTypeProp: {
    name: string;
    indicator: { name: string; max: number }[];
    dataList: number[];
  } = {
    name: "车辆类型分布",
    indicator: [
      { name: "垃圾清运车", max: 100 },
      { name: "雾炮车", max: 100 },
      { name: "餐厨清运车", max: 100 },
      { name: "洒水车", max: 100 },
      { name: "洗扫车", max: 100 },
      { name: "执法车", max: 100 },
    ],
    dataList: [50, 60, 40, 50, 60, 70],
  };
  //车辆出勤统计
  private carAttendance: {
    name: string;
    indicator: { name: string; max: number }[];
    dataList: number[];
  } = {
    name: "车辆出勤统计",
    indicator: [
      { name: "垃圾清运车", max: 100 },
      { name: "雾炮车", max: 100 },
      { name: "餐厨清运车", max: 100 },
      { name: "洒水车", max: 100 },
      { name: "洗扫车", max: 100 },
      { name: "执法车", max: 100 },
    ],
    dataList: [40, 60, 50, 70, 60, 50],
  };
  // 重污企业排放量
  private swiperOptionPollution = {
    direction: "vertical",
    slidesPerView: 5,
    slidesPerGroup: 1,
    loop: true,
    autoplay: {
      delay: 5000,
      disableOnInteraction: false,
    },
  };
  // 重污企业排放量
  private heavyPollution = [];
  // 空气监测站点
  private swiperOptionAirs = {
    direction: "vertical",
    slidesPerView: 5,
    slidesPerGroup: 1,
    loop: true,
    autoplay: {
      delay: 5000,
      disableOnInteraction: false,
    },
  };
  private airStationList: any[] = [];
  private airStationIndex = 0;
  // 空气站点记录
  private airsData = [];
  // 水质监测站点
  private swiperOptionWater = {
    direction: "vertical",
    slidesPerView: 5,
    slidesPerGroup: 1,
    loop: true,
    autoplay: {
      delay: 5 * 1000,
      disableOnInteraction: false,
    },
  };
  private waterStation: any[] = [];
  private stationId = "20171207000002";
  private pollutionType = "s";
  // 水质监测记录
  private waterData = [];
  // 实时监测预警
  private swiperOption = {
    direction: "vertical",
    slidesPerView: 4,
    slidesPerGroup: 1,
    loop: true,
    autoplay: {
      delay: 5 * 1000,
      disableOnInteraction: false,
    },
  };
  // 实时监测预警
  private airWarning = [];
  // 车辆类型列表
  private carTypeList: any[] = [];
  // 设备信息
  private stationTypeName: any = "空气";
  // 设备状态
  private stateList: any[] = [
    {
      name: "空气站 47个",
      number: 24,
      total: 47,
      startColor: "rgba(64, 119, 200, 1)",
      endColor: "rgba(33, 185, 241, 1)",
    },
    {
      name: "水站 10个",
      number: 4,
      total: 10,
      startColor: "rgba(64, 119, 200, 1)",
      endColor: "rgba(33, 185, 241, 1)",
    },
    {
      name: "摄像头 8个",
      number: 4,
      total: 8,
      startColor: "rgba(64, 119, 200, 1)",
      endColor: "rgba(33, 185, 241, 1)",
    },
  ];
  // 离线设备列表
  private swiperOptionWarn = {
    direction: "vertical",
    slidesPerView: 5,
    slidesPerGroup: 1,
    loop: true,
    autoplay: {
      delay: 5000,
      disableOnInteraction: false,
    },
  };
  // 离线设备列表
  private offLineList: any[] = [
    {
      name: "连心桥站",
      type: "水站站",
      address: "沙河源街道新桥社区东北方向",
      time: "5月8日 13:20",
    },
    {
      name: "连心桥站",
      type: "水站站",
      address: "沙河源街道新桥社区东北方向",
      time: "5月8日 13:20",
    },
    {
      name: "连心桥站",
      type: "水站站",
      address: "沙河源街道新桥社区东北方向",
      time: "5月8日 13:20",
    },
    {
      name: "连心桥站",
      type: "水站站",
      address: "沙河源街道新桥社区东北方向",
      time: "5月8日 13:20",
    },
    {
      name: "连心桥站",
      type: "水站站",
      address: "沙河源街道新桥社区东北方向",
      time: "5月8日 13:20",
    },
    {
      name: "连心桥站",
      type: "水站站",
      address: "沙河源街道新桥社区东北方向",
      time: "5月8日 13:20",
    },
  ];
  // 演练复盘参数-----start
  private pageTypeFlg: any = false;
  // 天月年 类型
  private curDataType: any = "1";
  private ruleCaliNum: any = 24;
  // 热力图最大值map
  heathMapMaxMap = {
    aqi: 300,
    "105": 250,
    "104": 420,
    "101": 2340,
    "102": 800,
    "103": 90,
    "100": 800,
  };
  //选择日期
  private selDate = moment().format("YYYY-MM-DD");
  // 禁止选择今天之后日期
  private pickerOptions: any = {
    disabledDate(time: any) {
      return time.getTime() > Date.now();
    },
  };
  // 当前进度条走到的小时数
  private curHour: any = 0;
  // 当前进度条位置
  private curSite: any = 0;
  // 播放状态
  private playStutas: any = false;
  // 播放禁止
  private palyFlg: any = false;
  // 进度条定时器
  private rulerTimer: any = null;
  private rulerTimerM: any = null;
  private interval: any = null;

  // 左侧空气tab栏
  private leftTabs: any = [
    { name: "AQI", id: "aqi" },
    { name: "PM₁₀", id: "104" },
    { name: "PM₂.₅", id: "105" },
    { name: "SO₂", id: "100" },
    { name: "NO₂", id: "101" },
    { name: "O₃", id: "102" },
    { name: "CO", id: "103" },
  ];
  // 左侧水质tab栏 wqi ,002 氨氮， 004 总磷，006 高猛， 009溶解氧
  private leftWaterTabs: any = [
    { name: "WQI", id: "wqi" },
    { name: "总磷", id: "004" },
    { name: "氨氮", id: "002" },
    { name: "溶解氧", id: "009" },
    { name: "高锰酸盐指数", id: "006" },
    // { name: 'PH值', id: 'wqi' },
  ];
  // 当前污染监测参数类型
  private curtype = "aqi";
  private curtypeName = "AQI";
  // 当前页面类型--大气、水
  private pageType = "air";
  private bof: any = bof;
  private zant: any = zant;
  private first: any = true;
  private hasNoData: any = false;
  private samllMapList: any = [];
  private clickRule: any = true;
  private swiperOptionAlert = {
    effect: "coverflow",
    grabCursor: true,
    slidesPerView: 3,
    slidesPerGroup: 1,
    centeredSlides: true,
    // loop: true,
    spaceBetween: 0,
    coverflowEffect: {
      rotate: 5,
      stretch: 46, //每个slide之间的拉伸值，越大slide靠得越紧。 默认0。
      depth: 100, //slide的位置深度。值越大z轴距离越远，看起来越小。 默认100。
      modifier: 1, //depth和rotate和stretch的倍率，相当于depth*modifier、rotate*modifier、stretch*modifier，值越大这三个参数的效果越明显。默认1。
      slideShadows: true,
    },
    on: {
      transitionEnd: function() {
        // vm.playStutas = true
        // vm.playBtnFn()
        const _this: any = this;
        if (_this.activeIndex == vm.curHour) return;
        if (vm.first) {
          vm.first = false;
          return;
        }
        if (vm.curDataType == "1" && vm.clickRule) {
          const selDates =
            vm.selDate +
            " " +
            (vm.curHour > 9 ? vm.curHour : "0" + vm.curHour) +
            ":00:00";
          vm.curHour = _this.activeIndex;
          vm.curSite = _this.activeIndex * 72;
        } else if (vm.curDataType == "2" && vm.clickRule) {
          const selDates =
            moment(vm.selDate).format("YYYY-MM") +
            "-" +
            (vm.curHour + 1 > 9 ? vm.curHour + 1 : "0" + (vm.curHour + 1)) +
            " " +
            "00:00:00";
          const DL =
            vm.ruleCaliNum == 30
              ? 56.7
              : vm.ruleCaliNum == 31
              ? 55.74
              : vm.ruleCaliNum == 29
              ? 59.6
              : 61.4;
          vm.curHour = _this.activeIndex;
          vm.curSite = _this.activeIndex * DL;
        }
      },
    },
  };
  private swiperOptionAlertV = {
    direction: "vertical",
    slidesPerView: 5,
    slidesPerGroup: 1,
    loop: true,
    autoplay: {
      delay: 3500,
      disableOnInteraction: false,
    },
  };
  private analyse: any = null; // 右侧分析
  private maxPoint: any = null; // 右侧小地图中心点
  private maxList: any = []; // 右侧表格
  private samllMapHeatData: any = []; // 小地图
  private visibleCheck: any = false;
  private waterTimeFlg: any = true;
  private meteorologyWarningList: any = [];
  private air_pollution_diffusionList: any = [];
  // 演练复盘参数-----end
  private topBtnFlg: any = false;
  private topBtnOpen() {
    this.topBtnFlg = true;
  }
  private pageTypeCHange(type: any) {
    this.showWarnBox = true;
    // if (!this.topBtnFlg) return;
    this.pageType = type;
    this.closeInfoWindow();
    // 清除气象环境页面marker
    this.weatherMarkerList.forEach((item) => {
      item.setMap(null);
    });
    this.weatherMarkerList = [];

    const mapRef = this.$refs.mapRef;
    mapRef.GBLayer.setMap(maps);
    mapRef.AirLayer.setMap(maps);
  }

  private companyId: any = null;

  private dateRange = [
    moment().format("YYYY-MM-DD") + " 00:00:00",
    moment().format("YYYY-MM-DD HH:mm:ss"),
  ];

  // 天时间轴
  // 显示时间段
  private currentTime = this.dateRange[0];

  private timeSegments = [
    {
      name: "时间段1",
      // beginTime: this.dateRange[0],
      // endTime: this.dateRange[1],
      beginTime: new Date(this.dateRange[0]).getTime(),
      endTime: Math.min(new Date(this.dateRange[1]).getTime(), Date.now()),
      color: "#009845",
      startRatio: 0.65,
      endRatio: 0.9,
    },
  ];

  private showTime = this.currentTime;
  private showTime2 = this.currentTime;

  @Watch("currentTime", { immediate: true })
  public onTime2Changed(newValue: any, oldValue: any) {
    this.showTime = dayjs(this.currentTime).format("YYYY-MM-DD HH:mm");
    this.showTime2 = dayjs(this.currentTime).format("YYYY-MM-DD");
  }

  created() {
    vm = this;
    this.handleGetWarning(moment());
  }

  mapReady() {
    AMap = this.$refs.mapRef.AMap;
    maps = this.$refs.mapRef.maps;
  }
  mounted() {
    // Promise.all([
    //   this.fetchRecentWeather(),
    //   this.fetchPollutionCount(),
    //   this.getWaterQuality(),
    //   this.fetchHeavyPollution(),
    //   this.getAirStation(),
    //   this.fetchWaterStationList(),
    //   // this.fetchEarlyWarning(),
    //   this.fetchCarList(),
    //   this.getEquipmentStatus(),
    //   this.fetchOffline(),
    //   this.getTypeStationInfoList(),
    //   this.getSelectMonthTaskCompletion(),
    //   this.getSelectTaskTypeCompletion(),
    //   this.getSelectTypeStatistic()
    // ]).then(() => {
    //   setTimeout(() => {
    //     this.curMainIndex = 1;
    //     this.openMainTimer();
    //     this.stationTypeNameChange("空气");
    //   }, 500);
    // });
  }
  // 显示时间段
  timeChange2(t) {
    this.currentTime = t;
  }

  dateRangeChange() {
    this.clearAll();
    this.currentTime = this.dateRange[0];
    this.timeSegments = [
      {
        name: "时间段1",
        beginTime: new Date(this.dateRange[0]).getTime(),
        endTime: Math.min(new Date(this.dateRange[1]).getTime(), Date.now()),
        color: "#009845",
        startRatio: 0.65,
        endRatio: 0.9,
      },
    ];
  }

  toggleType(type) {
    this.curDataType = null;
    this.$nextTick(() => {
      this.curDataType = type;
    });
    switch (type) {
      case "1":
        this.dateRange = [
          moment().format("YYYY-MM-DD") + " 00:00:00",
          moment().format("YYYY-MM-DD HH:mm:ss"),
        ];
        break;
      case "2":
        this.dateRange = [
          moment()
            .subtract(1, "days")
            .format("YYYY-MM-DD") + " 00:00:00",
          moment().format("YYYY-MM-DD HH:mm:ss"),
        ];
        break;
    }
    this.dateRangeChange();
  }

  beforeDestroy() {
    clearInterval(this.airTimer);
    clearInterval(this.waterTimer);
    clearInterval(this.mainTimer);
    // 复盘定时器
    clearInterval(this.rulerTimer);
    clearInterval(this.rulerTimerM);
  }

  private handleClickPollutionItem(id: any) {
    this.companyId = id;
    this.dialogVisible = true;
  }
  private handleGetWarning(date: any) {
    this.handleGetMeteorology_early_warning(date);
    this.handleGetAir_pollution_diffusion(date);
  }

  private handleGetMeteorology_early_warning(date: any) {
    const params = {
      findDate: moment(date).format("YYYY-MM-DD"),
    };
    meteorology_early_warning(params).then((res: any) => {
      this.meteorologyWarningList = res?.data?.data || [];
    });
  }
  private handleGetAir_pollution_diffusion(date: any) {
    const params = {
      findDate: moment(date).format("YYYY-MM-DD"),
    };
    air_pollution_diffusion(params).then((res: any) => {
      this.air_pollution_diffusionList = res?.data?.data || [];
      // this.air_pollution_diffusionList = this.air_pollution_diffusionList.slice(0,2)
    });
  }
  // 切换查询距离
  changeDistance() {
    this.handleGetPollutionSource();
  }
  selectPoint(point: any) {
    this.point = point;
    this.handleGetPollutionSource();
  }
  handleGetPollutionSource() {
    const params = {
      lng: this.point[0],
      lat: this.point[1],
      // lng: 103.992961,
      // lat: 30.709217,
      distance: this.distance,
    };
    rangeByPollutionSource(params).then((res) => {
      this.pollutionStationList = res.data.data;
    });
  }
  private sMapMore() {
    this.visibleCheck = true;
  }
  private handleCancel() {
    this.visibleCheck = false;
    this.dialogVisible = false;
    this.companyId = null;
  }
  private toMoadlTime(time: any) {
    if (this.curDataType == "1") {
      return moment(time).format("MM月DD日");
    } else if (this.curDataType == "2") {
      return moment(time).format("MM月");
    }
  }
  private sMapTime(num: any) {
    let str: any = "";
    if (
      (this.selDate == moment().format("YYYY-MM-DD") &&
        this.curDataType == "1") ||
      (moment(this.selDate).format("YYYY-MM") == moment().format("YYYY-MM") &&
        this.curDataType == "2")
    ) {
      if (this.curDataType == "1") {
        if (
          this.curHour + 1 == this.samllMapList.length ||
          this.curHour == this.samllMapList.length
        ) {
          if (num == 1) {
            str = "";
          } else if (num == 2) {
            str = "";
          } else {
            str =
              this.curHour + num > 9
                ? this.curHour + num
                : "0" + (this.curHour + num);
          }
        } else if (this.curHour + 2 == this.samllMapList.length) {
          if (num == 2) {
            str = "";
          } else {
            str =
              this.curHour + num > 9
                ? this.curHour + num
                : "0" + (this.curHour + num);
          }
        } else if (this.curHour == 0) {
          const timeN = this.samllMapList.length;
          if (num == -1) {
            // str = timeN > 9 ? timeN : '0' + timeN
            str = "";
          } else if (num == -2) {
            // str = timeN - 1 > 9 ? timeN - 1 : '0' + (timeN - 1)
            str = "";
          } else {
            str =
              this.curHour + num > 9
                ? this.curHour + num
                : "0" + (this.curHour + num);
          }
        } else if (this.curHour == 1) {
          const timeN = this.samllMapList.length;
          if (num == -2) {
            str = "";
          } else {
            str =
              this.curHour + num > 9
                ? this.curHour + num
                : "0" + (this.curHour + num);
          }
        } else {
          str =
            this.curHour + num > 9
              ? this.curHour + num
              : "0" + (this.curHour + num);
        }
      } else if (this.curDataType == "2") {
        if (
          this.curHour + 1 == this.ruleCaliNum ||
          this.curHour + 1 == this.samllMapList.length
        ) {
          if (num == 1) {
            str = "";
          } else if (num == 2) {
            str = "";
          } else {
            let val = this.curHour + 1 + num;
            str = val > 9 ? val : "0" + val;
          }
        } else if (this.curHour == 0) {
          if (num == -1) {
            str = "";
          } else if (num == -2) {
            str = "";
          } else {
            let val = this.curHour + 1 + num;
            str = val > 9 ? val : "0" + val;
          }
        } else if (this.curHour == 1) {
          if (num == -1) {
            let val = this.curHour + 1 + num;
            str = val > 9 ? val : "0" + val;
          } else if (num == -2) {
            str = "";
          } else {
            let val = this.curHour + 1 + num;
            str = val > 9 ? val : "0" + val;
          }
        } else if (
          this.curHour == this.ruleCaliNum - 2 ||
          this.curHour + 2 == this.samllMapList.length
        ) {
          if (num == 2) {
            str = "";
          } else {
            let val = this.curHour + 1 + num;
            str = val > 9 ? val : "0" + val;
          }
        } else {
          str =
            this.curHour + num + 1 > 9
              ? this.curHour + num + 1
              : "0" + (this.curHour + num + 1);
        }
      }
    } else {
      if (this.curDataType == "1") {
        if (
          this.curHour + 1 == this.samllMapList.length ||
          this.curHour == this.samllMapList.length
        ) {
          if (num == 1) {
            str = "";
          } else if (num == 2) {
            str = "";
          } else {
            str =
              this.curHour + num > 9
                ? this.curHour + num
                : "0" + (this.curHour + num);
          }
        } else if (this.curHour + 2 == this.samllMapList.length) {
          if (num == 2) {
            str = "";
          } else {
            str =
              this.curHour + num > 9
                ? this.curHour + num
                : "0" + (this.curHour + num);
          }
        } else if (this.curHour == 0) {
          if (num == -1) {
            str = "";
          } else if (num == -2) {
            str = "";
          } else {
            str =
              this.curHour + num > 9
                ? this.curHour + num
                : "0" + (this.curHour + num);
          }
        } else if (this.curHour == 1) {
          if (num == -2) {
            str = "";
          } else {
            str =
              this.curHour + num > 9
                ? this.curHour + num
                : "0" + (this.curHour + num);
          }
        } else if (this.curHour == 23) {
          if (num == 2) {
            str = "";
          } else {
            str =
              this.curHour + num > 9
                ? this.curHour + num
                : "0" + (this.curHour + num);
          }
        } else {
          str =
            this.curHour + num > 9
              ? this.curHour + num
              : "0" + (this.curHour + num);
        }
      } else if (this.curDataType == "2") {
        if (this.curHour + 1 == this.ruleCaliNum) {
          if (num == 1) {
            str = "";
          } else if (num == 2) {
            str = "";
          } else {
            let val = this.curHour + 1 + num;
            str = val > 9 ? val : "0" + val;
          }
        } else if (this.curHour == 0) {
          if (num == -1) {
            str = "";
          } else if (num == -2) {
            str = "";
          } else {
            let val = this.curHour + 1 + num;
            str = val > 9 ? val : "0" + val;
          }
        } else if (this.curHour == 1) {
          if (num == -1) {
            let val = this.curHour + 1 + num;
            str = val > 9 ? val : "0" + val;
          } else if (num == -2) {
            str = "";
          } else {
            let val = this.curHour + 1 + num;
            str = val > 9 ? val : "0" + val;
          }
        } else if (this.curHour == this.ruleCaliNum - 2) {
          if (num == 2) {
            str = "";
          } else if (num == 1) {
            str = this.ruleCaliNum;
          } else {
            let val = this.curHour + 1 + num;
            str = val > 9 ? val : "0" + val;
          }
        } else {
          str =
            this.curHour + num + 1 > 9
              ? this.curHour + num + 1
              : "0" + (this.curHour + num + 1);
        }
      }
    }
    return str;
  }
  private getPhoto(url: any, idx: any, flg: any) {
    // this.$nextTick(() => {
    const src = this.pageType == "air" ? airBlank : waterBlank;
    if (flg || this.pageTypeFlg) {
      this.pageTypeFlg = false;
      this.samllMapList = [];
      if (
        (this.selDate == moment().format("YYYY-MM-DD") &&
          this.curDataType == "1") ||
        (moment(this.selDate).format("YYYY-MM") == moment().format("YYYY-MM") &&
          this.curDataType == "2")
      ) {
        let idxNum =
          this.curDataType == "1"
            ? +moment().format("HH")
            : +moment().format("DD");
        for (let i = 0; i < idxNum; i++) {
          this.samllMapList.push({ url: src, flg: false });
        }
      } else {
        let idxNum =
          this.curDataType == "1" ? 24 : +moment(this.selDate).daysInMonth();
        for (let i = 0; i < idxNum; i++) {
          this.samllMapList.push({ url: src, flg: false });
        }
      }
    } else {
      if (
        (this.selDate == moment().format("YYYY-MM-DD") &&
          this.curDataType == "1") ||
        (moment(this.selDate).format("YYYY-MM") == moment().format("YYYY-MM") &&
          this.curDataType == "2")
      ) {
        this.samllMapList = [];
        let idxNum =
          this.curDataType == "1"
            ? +moment().format("HH")
            : +moment().format("DD");
        if (this.samllMapList.length != idxNum) {
          for (let i = 0; i < idxNum; i++) {
            this.samllMapList.push({ url: src, flg: false });
          }
        }
        this.$set(this.samllMapList, idx, { url, flg: true });
      } else {
        if (this.samllMapList.length != this.ruleCaliNum) {
          for (let i = 0; i < this.ruleCaliNum; i++) {
            this.$set(this.samllMapList, i, { url: src, flg: false });
          }
        }
        this.$set(this.samllMapList, idx, { url, flg: true });
      }
    }
    // })
  }
  private convertRGBtoRGBA(rgb: string, alpha: any) {
    const rgbValues = rgb.match(/\d+/g);
    if (rgbValues && rgbValues.length === 3) {
      const [r, g, b] = rgbValues;
      return `rgba(${r},${g},${b},${alpha})`;
    } else {
      return null;
    }
  }
  private getRightData(
    maxList: any,
    maxPoint: any,
    analyse: any,
    heatData: any,
    streetSortList: any
  ) {
    this.analyse = analyse;
    this.maxPoint = maxPoint;
    this.maxList = maxList;
    this.samllMapHeatData = heatData;
    this.rankingAlarmList = (streetSortList || []).map((item: any) => {
      this.AQIAirColors.forEach((it: any) => {
        if (item.aqi < it.max && item.aqi >= it.min) {
          item.textColor = it.color;
          item.bgColor = this.convertRGBtoRGBA(it.color, 0.5);
        }
      });
      return item;
    });
  }
  // 播放按钮
  private playBtnFn() {
    if (this.playStutas) {
      clearInterval(this.interval);
      this.playStutas = false;
      return;
    }

    if (this.curSite > 1728 || this.palyFlg) {
      this.playStutas = false;
      clearInterval(this.rulerTimer);
      clearInterval(this.rulerTimerM);
      return;
    }
    if (this.curDataType == "1") {
      const h = +moment().format("HH");
      const m = +moment().format("mm");
      const todaySite1 = h * 72 + m * 1.2;
      if (
        todaySite1 <= this.curSite &&
        moment().format("YYYY-MM-DD") == this.selDate
      ) {
        this.playStutas = false;
        clearInterval(this.rulerTimer);
        clearInterval(this.rulerTimerM);
        clearInterval(this.interval);
        return;
      }
    } else if (this.curDataType == "2") {
      let ruleCaliNum = moment(this.selDate).daysInMonth();
      const d = +moment().format("DD") - 1;
      const h = +moment().format("HH");
      const DL =
        ruleCaliNum == 30
          ? 56.7
          : ruleCaliNum == 31
          ? 55.74
          : ruleCaliNum == 29
          ? 59.6
          : 61.4;
      const HL =
        ruleCaliNum == 30
          ? 2.4
          : ruleCaliNum == 31
          ? 2.3
          : ruleCaliNum == 29
          ? 2.48
          : 2.56;
      const todaySite = d * DL + h * HL;
      if (
        moment().format("YYYY-MM") == moment(this.selDate).format("YYYY-MM") &&
        todaySite <= this.curSite
      ) {
        this.playStutas = false;
        clearInterval(this.rulerTimer);
        clearInterval(this.rulerTimerM);
        return;
      }
    }
    this.playStutas = !this.playStutas;
    this.first = false;
    this.palyFlg = false;
    // 时间条暂停时
    if (!this.playStutas) {
      clearInterval(this.rulerTimer);
      clearInterval(this.rulerTimerM);
      return;
    }
    if (this.curDataType == 1) {
      // this.rulerTimer = setInterval(() => {
      //   this.curSite += 1.2;
      //   this.curHour =
      //     Math.floor(this.curSite / 72) == 24
      //       ? 23
      //       : Math.floor(this.curSite / 72);
      // }, 30);
      // 显示时间段
      this.currentTime = Math.max(
        this.currentTime,
        new Date(this.timeSegments[0].beginTime).getTime()
      );
      this.interval = setInterval(() => {
        // 超过当前时间后停止播放
        if (
          new Date(this.currentTime).getTime() >=
          new Date(this.timeSegments[0].endTime).getTime()
        ) {
          clearInterval(this.interval);
          this.playStutas = false;
          return;
        }
        this.currentTime += 120 * 1000;
        this.currentTime = Math.min(
          this.currentTime,
          new Date(this.timeSegments[0].endTime).getTime()
        );
        this.$refs.Timeline2.setTime(this.currentTime);
        // 获取小时
        this.curHour = new Date(this.showTime).getHours();
        // 获取日期
        this.selDate = dayjs(this.currentTime).format("YYYY-MM-DD");
      }, 40);
    } else if (this.curDataType == 2) {
      // const DL =
      //   this.ruleCaliNum == 30
      //     ? 56.7
      //     : this.ruleCaliNum == 31
      //     ? 55.74
      //     : this.ruleCaliNum == 29
      //     ? 59.6
      //     : 61.4;
      // const HL =
      //   this.ruleCaliNum == 30
      //     ? 2.4
      //     : this.ruleCaliNum == 31
      //     ? 2.3
      //     : this.ruleCaliNum == 29
      //     ? 2.48
      //     : 2.56;
      // this.rulerTimerM = setInterval(() => {
      //   this.curSite += HL;
      //   this.curHour =
      //     Math.floor(this.curSite / DL) >= this.ruleCaliNum
      //       ? this.ruleCaliNum - 1
      //       : Math.floor(this.curSite / DL);
      // }, 3);

      this.currentTime = Math.max(
        this.currentTime,
        new Date(this.timeSegments[0].beginTime).getTime()
      );
      this.interval = setInterval(() => {
        // 超过当前时间后停止播放
        if (
          new Date(this.currentTime).getTime() >=
          new Date(this.timeSegments[0].endTime).getTime()
        ) {
          clearInterval(this.interval);
          this.playStutas = false;
          return;
        }
        this.currentTime += 1000 * 60 * 45;
        this.currentTime = Math.min(
          this.currentTime,
          new Date(this.timeSegments[0].endTime).getTime()
        );
        this.$refs.Timeline2.setTime(this.currentTime);
        // 获取日期
        this.selDate = dayjs(this.currentTime).format("YYYY-MM-DD");
      }, 50);
    } else {
    }
    // 时间条开启时indexClose改变
    if (this.playStutas === true) {
      this.indexClose++;
    }
    setTimeout(() => {
      this.clickRule = true;
    }, 500);
  }
  // 事件尺点击
  private rulerClick(e: any) {
    this.first = false;
    this.clickRule = false;
    const index = +e.target.dataset.index - 1;
    const offsetX = e.offsetX;
    const DL =
      this.ruleCaliNum == 30
        ? 56.7
        : this.ruleCaliNum == 31
        ? 55.74
        : this.ruleCaliNum == 29
        ? 59.6
        : 61.4;
    const HL =
      this.ruleCaliNum == 30
        ? 2.4
        : this.ruleCaliNum == 31
        ? 2.3
        : this.ruleCaliNum == 29
        ? 2.48
        : 2.56;
    if (this.curDataType == "1") {
      this.curSite = index * 72 + offsetX;
      clearInterval(this.rulerTimer);
      clearInterval(this.rulerTimerM);
      const h = +moment().format("HH");
      const m = +moment().format("mm");
      const todaySite = h * 72 + m * 1.2;
      if (
        todaySite <= this.curSite &&
        moment().format("YYYY-MM-DD") == this.selDate
      ) {
        this.curSite = todaySite;
      }
    } else if (this.curDataType == "2") {
      this.curSite = index * DL + offsetX;
      clearInterval(this.rulerTimer);
      clearInterval(this.rulerTimerM);
      const h = +moment().format("DD");
      const m = +moment().format("HH");
      const todaySite = h * DL + m * HL;
      if (
        todaySite <= this.curSite &&
        moment().format("YYYY-MM") == moment(this.selDate).format("YYYY-MM")
      ) {
        this.curSite = todaySite;
      }
    }

    if (this.playStutas) this.playStutas = !this.playStutas;
    // this.palyFlg = false
    this.playBtnFn();
  }
  /* 大气演练图片获取 */
  // getDeduction(date:any) {
  //   const data = {
  //     findDate: date,
  //     type: '',
  //     timeType: '',
  //   }
  //   deduction(data).then((res) => {
  //     this.mengBan = res.data.data.imageUrl
  //   })
  // }
  // 切换监测参数
  private curtypeChange(item: any) {
    this.samllMapList = [];
    this.analyse = null;
    this.maxList = [];
    this.maxPoint = null;
    this.curtype = item.id;
    this.curtypeName = item.name;
    if (
      moment().format("YYYY-MM-DD") == this.selDate &&
      this.curDataType == "1"
    ) {
      this.first = true;
      const h = +moment().format("HH");
      const m = +moment().format("mm");
      const todaySite = h * 72;
      this.curSite = todaySite;
      this.curHour = Math.floor(this.curSite / 72);
    } else if (
      moment().format("YYYY-MM") == moment(this.selDate).format("YYYY-MM") &&
      this.curDataType == "2"
    ) {
      this.first = true;
      let ruleCaliNum = moment(this.selDate).daysInMonth();
      const d = +moment().format("DD") - 1;
      const h = +moment().format("HH");
      const DL =
        ruleCaliNum == 30
          ? 56.7
          : ruleCaliNum == 31
          ? 55.74
          : ruleCaliNum == 29
          ? 59.6
          : 61.4;
      const HL =
        ruleCaliNum == 30
          ? 2.4
          : ruleCaliNum == 31
          ? 2.3
          : ruleCaliNum == 29
          ? 2.48
          : 2.56;
      // const todaySite = d * DL
      const todaySite = d * DL;
      this.curSite = todaySite;
      this.curHour = Math.floor(this.curSite / DL);
    }
  }
  // 清除定时器，距离，暂停
  private clearAll() {
    this.curSite = 0;
    this.curHour = 0;
    this.playStutas = false;
    // this.palyFlg = false
    clearInterval(this.rulerTimer);
    clearInterval(this.rulerTimerM);
    clearInterval(this.interval);
  }
  // 没有热力图数据
  private noDataFn(findDate: any, num: any) {
    if (
      (moment().format("YYYY-MM-DD") == this.selDate &&
        this.curDataType == "1") ||
      (moment().format("YYYY-MM") == moment(this.selDate).format("YYYY-MM") &&
        this.curDataType == "2")
    ) {
      if (
        (moment(findDate).format("HH") >= moment().format("HH") &&
          this.curDataType == "1") ||
        (moment().format("DD") >= moment(findDate).format("DD") &&
          this.curDataType == "2")
      ) {
        this.curHour += num;
        if (this.curDataType == "1") {
          this.curSite = this.curHour * 72;
        } else if (this.curDataType == "2") {
          const DL =
            this.ruleCaliNum == 30
              ? 56.7
              : this.ruleCaliNum == 31
              ? 55.74
              : this.ruleCaliNum == 29
              ? 59.6
              : 61.4;
          this.curSite = this.curHour * DL;
        }
        // this.playStutas = true
        // this.playBtnFn()
      } else if (this.pageType == "water") {
        if (this.curHour > 4) {
          this.curHour += num;
        } else {
          this.curHour = 0;
        }
        if (this.curDataType == "1") {
          this.curSite = this.curHour * 72;
        } else if (this.curDataType == "2") {
          const DL =
            this.ruleCaliNum == 30
              ? 56.7
              : this.ruleCaliNum == 31
              ? 55.74
              : this.ruleCaliNum == 29
              ? 59.6
              : 61.4;
          this.curSite = this.curHour * DL;
        }
      }
    }
    // this.clearAll()
    // this.$message.warning('暂无监测数据！')
    // if (!vm.$refs.mapSwiper) return
    // vm.$refs.mapSwiper.$swiper.slideToLoop(0, 300, true)
  }
  // 当天后续没有数据了
  private todayNoData() {
    this.playStutas = false;
    this.palyFlg = true;
    clearInterval(this.rulerTimer);
    clearInterval(this.rulerTimerM);
  }
  // 距离像素转换为时分
  private toHourMin(num: number) {
    if (this.curDataType == 1) {
      const h = Math.floor(this.curSite / 72);
      const m = Math.floor((this.curSite % 72) / 1.2);
      return `${h > 9 ? h : "0" + h}:${m > 9 ? m : "0" + m}`;
    } else if (this.curDataType == 2) {
      const DL =
        this.ruleCaliNum == 30
          ? 56.7
          : this.ruleCaliNum == 31
          ? 55.74
          : this.ruleCaliNum == 29
          ? 59.6
          : 61.4;
      const HL =
        this.ruleCaliNum == 30
          ? 2.4
          : this.ruleCaliNum == 31
          ? 2.3
          : this.ruleCaliNum == 29
          ? 2.48
          : 2.56;
      const m = Math.floor(this.curSite / DL) + 1;
      const h = Math.floor((this.curSite % DL) / HL);
      const MM = moment(this.selDate).format("MM");
      if (h == 25 || h == 24) {
        return `${MM}月${m > 9 ? m : "0" + m}日`;
      } else if (m > this.ruleCaliNum) {
        return `${MM}月${this.ruleCaliNum}日`;
      }
      return `${MM}月${m > 9 ? m : "0" + m}日`;
    } else {
    }
  }
  private clearMainTimer(): void {
    clearInterval(this.mainTimer);
  }
  private clearAirTimer(): void {
    clearInterval(this.airTimer);
  }
  private clearWaterTimer(): void {
    clearInterval(this.waterTimer);
  }
  private changeIndex(index: number): void {
    if (index != this.curMainIndex) {
      clearInterval(this.mainTimer);
      this.curMainIndex = index;
      this.openMainTimer();
    }
  }
  get windLevelNumber(): string {
    if ((this.weather as any).windLevel) {
      const windlevel =
        3.77 - (this.weather as any).windLevel.slice(0, -1) * 0.27;
      return `fly-wind ${windlevel}s linear infinite`;
    } else {
      return "";
    }
  }
  //div切换定时器
  private openMainTimer() {
    this.mainTimer = setInterval(() => {
      this.curMainIndex = this.curMainIndex + 1 > 4 ? 1 : this.curMainIndex + 1;
    }, 30000);
  }
  //开启空气定时器
  private openAirTimer() {
    this.airTimer = setInterval((e: any) => {
      if (this.airTypeIndex >= this.airTypes.length - 1) {
        this.airTypeIndex = 0;
        if (this.airStationIndex >= this.airStationList.length - 1) {
          this.airStationIndex = 0;
        } else {
          this.airStationIndex += 1;
        }
        this.fetchAirTypes();
        this.fetchAirStation();
      } else {
        this.airTypeIndex += 1;
        this.fetchAirTrend();
      }
    }, 8000);
  }
  //开启水质定时器
  private openWaterTimer() {
    this.waterTimer = setInterval((e: any) => {
      if (this.curWaterIndex + 1 < this.waterTrendList.length) {
        this.curWaterIndex = this.curWaterIndex + 1;
        this.typeCode = this.waterTrendList[this.curWaterIndex].itemCode;
        this.typeUnit = this.waterTrendList[
          this.curWaterIndex
        ].concentrationUnit;
        this.fetchWaterTrend();
      } else {
        this.curWaterIndex = 0;
        let curStationIndex: number = this.waterStation.findIndex((item) => {
          return this.stationId == item.stationId;
        });
        curStationIndex =
          curStationIndex + 1 < this.waterStation.length
            ? curStationIndex + 1
            : 0;
        this.waterTrendList = this.waterStation[curStationIndex].monitorItems;
        this.typeCode = this.waterTrendList[this.curWaterIndex].itemCode;
        this.typeUnit = this.waterTrendList[
          this.curWaterIndex
        ].concentrationUnit;
        this.stationId = this.waterStation[curStationIndex].stationId;
        this.fetchWaterTrend();
        this.fetchWaterStationRecord();
      }
    }, 8000);
  }
  //气象监测
  private fetchRecentWeather() {
    return new Promise<void>((resolve, reject) => {
      recentWeather().then((res) => {
        const data = res.data.data;
        for (const item of this.AQIAirColors) {
          if (data.aqi <= item.max && data.aqi >= item.min) {
            data.aqiColor = item.color;
            data.color = item.textColor;
            break;
          }
        }
        if (data.weather.indexOf("晴") != -1) {
          data.weatherImg = gif01;
        } else if (data.weather.indexOf("云") != -1) {
          data.weatherImg = gif02;
        } else if (data.weather.indexOf("阴") != -1) {
          data.weatherImg = gif03;
        } else if (data.weather.indexOf("雨") != -1) {
          data.weatherImg = gif04;
        } else if (data.weather.indexOf("雪") != -1) {
          data.weatherImg = gif05;
        }
        if (data.pollutant == "PM10") {
          data.pollutant = "PM₁₀";
        } else if (data.pollutant == "PM2.5") {
          data.pollutant = "PM₂.₅";
        } else if (data.pollutant == "O3") {
          data.pollutant = "O₃";
        } else if (data.pollutant == "SO2") {
          data.pollutant = "SO₂";
        } else if (data.pollutant == "NO2") {
          data.pollutant = "NO₂";
        }
        data.updateTime = data.updateTime
          ? data.updateTime.slice(0, 16)
          : undefined;
        this.weather = data;
        resolve();
      });
    });
  }
  // 环境质量影响--空气污染
  private fetchPollutionCount() {
    return new Promise<void>((resolve, reject) => {
      pollutionCount().then((res: any) => {
        const colorArr = [
          { startColor: "#4077C8", endColor: "#21B9F1" },
          { startColor: "#03CCF6", endColor: "#0EFDF0" },
          { startColor: "#33D3B7", endColor: "#00FFD5" },
          { startColor: "#622DBB", endColor: "#AB7AFB" },
        ];
        const data = res.data.data;
        let total = 0;
        const airQuality: any = [];
        data.forEach((item: any, index: number) => {
          total += item.value;
          airQuality.push({
            name: item.key,
            number: item.value,
            startColor: colorArr[index].startColor,
            endColor: colorArr[index].endColor,
          });
        });
        airQuality.forEach((item: any) => {
          item.total = total;
          switch (item.name) {
            case "NO2":
              item.name = "NO₂";
              break;
            case "SO2":
              item.name = "SO₂";
              break;
            case "O3":
              item.name = "O₃";
              break;
            case "PM10":
              item.name = "PM₁₀";
              break;
            case "PM2.5":
              item.name = "PM₂.₅";
              break;
          }
        });
        this.airQuality = airQuality;
        resolve();
      });
    });
  }
  //水质污染
  private getWaterQuality() {
    return new Promise<void>((resolve, reject) => {
      waterQuality().then((res: any) => {
        const colorArr = [
          { startColor: "#4077C8", endColor: "#21B9F1" },
          { startColor: "#03CCF6", endColor: "#0EFDF0" },
          { startColor: "#33D3B7", endColor: "#00FFD5" },
          { startColor: "#622DBB", endColor: "#AB7AFB" },
        ];
        const data = res.data.data;
        let total = 0;
        const filterArr: any = []; //不为0的项
        const emptyArr: any = []; //为0的项
        data.forEach((item: any) => {
          total = total + item.number; //总共的数据
          if (filterArr.length < 3 && item.number > 0) {
            filterArr.push(item);
          }
          if (item.number == 0) {
            emptyArr.push(item);
          }
        });
        if (filterArr.length < 3) {
          for (let i = 0; i < 3 - filterArr.length; i++) {
            filterArr.push(emptyArr[i]);
          }
        }
        let selectNumber = 0; //已选的数值总和
        filterArr.forEach((item: any) => {
          selectNumber += item.number;
        });
        filterArr.push({ name: "其他", number: total - selectNumber });
        filterArr.forEach((item: any, index: number) => {
          item.total = total;
          item.startColor = colorArr[index].startColor;
          item.endColor = colorArr[index].endColor;
        });
        this.waterQuality = filterArr;
        resolve();
      });
    });
  }
  //重污企业
  private fetchHeavyPollution() {
    // heavyPollutionList
    return new Promise<void>((resolve, reject) => {
      heavilyPollutingEnterpriseList(510106, this.pollutionType).then(
        (res: any) => {
          const data = res.data.data;
          this.heavyPollution = data;
          resolve();
        }
      );
    });
  }
  private pollutionStationChange(e: any) {
    this.pollutionType = e;
    this.fetchHeavyPollution();
  }
  // 切换空气类型
  private airTypeChange(e: any) {
    clearInterval(this.airTimer);
    this.airTypeIndex = e.target.value;
    this.fetchAirTrend();
    this.openAirTimer();
  }
  // 空气监测站点
  private getAirStation() {
    return new Promise<void>((resolve, reject) => {
      getAllAqiInfo({ areaCode: "510106" }).then((res: any) => {
        const data = res.data.data;
        this.airStationList = data;
        Promise.all([this.fetchAirTypes(), this.fetchAirStation()]).then(() => {
          resolve();
        });
      });
    });
  }
  //获取空气监测类型
  private fetchAirTypes() {
    return new Promise<void>((resolve, reject) => {
      getAirTypes({
        stationCode: this.airStationList[this.airStationIndex].stationCode,
      }).then((res) => {
        const data = res.data.data;
        const airTypes: { name: string; value: string | number }[] = [];
        for (const key in data) {
          let airType = "";
          if (key == "SO2") {
            airType = "SO₂";
          } else if (key == "NO2") {
            airType = "NO₂";
          } else if (key == "O3") {
            airType = "O₃";
          } else if (key == "PM10") {
            airType = "PM₁₀";
          } else if (key == "PM2.5") {
            airType = "PM₂.₅";
          }
          airTypes.push({
            name: airType || key,
            value: data[key],
          });
        }
        this.airTypes = airTypes;
        this.fetchAirTrend().then(() => {
          resolve();
        });
      });
    });
  }
  //空气监测趋势
  private fetchAirTrend() {
    const params = {
      pollutantCode: this.airTypes[this.airTypeIndex].value,
      stationCode: this.airStationList[this.airStationIndex].stationCode,
    };
    return new Promise<void>((resolve, reject) => {
      airTrend(params).then((res) => {
        const data = res.data.data;
        const bottomList = [],
          dataList = [];
        let unit = "";
        let colorType = "";
        for (const key in data) {
          bottomList.push(`${key}时`);
          dataList.push(data[key]);
        }
        if (this.airTypes[this.airTypeIndex].value == "103") {
          unit = "mg/m³";
        } else {
          unit = "μg/m³";
        }
        if (this.airTypes[this.airTypeIndex].value == "100") {
          colorType = "SO2";
        } else if (this.airTypes[this.airTypeIndex].value == "101") {
          colorType = "NO2";
        } else if (this.airTypes[this.airTypeIndex].value == "102") {
          colorType = "O3";
        } else if (this.airTypes[this.airTypeIndex].value == "103") {
          colorType = "CO";
        } else if (this.airTypes[this.airTypeIndex].value == "104") {
          colorType = "PM10";
        } else if (this.airTypes[this.airTypeIndex].value == "105") {
          colorType = "PM25";
        }
        this.airMonitoring.bottomList = bottomList;
        this.airMonitoring.dataList = dataList;
        this.airMonitoring.unit = unit;
        this.airMonitoring.colorType = colorType;
        resolve();
      });
    });
  }
  //空气站点改变
  private airStationChange(e: any) {
    clearInterval(this.airTimer);
    this.airTypeIndex = 0;
    this.airStationIndex = e;
    this.fetchAirTypes();
    this.fetchAirStation();
    this.openAirTimer();
  }
  //空气监测记录
  private fetchAirStation() {
    return new Promise<void>((resolve, reject) => {
      airStation({
        stationCode: this.airStationList[this.airStationIndex].stationCode,
        stationTypeId: this.airStationList[this.airStationIndex].stationTypeId,
      }).then((res) => {
        const data = res.data.data;
        data.forEach((item: any) => {
          if (item.pollutionCode == "SO2") {
            item.pollutionCode = "SO₂";
          } else if (item.pollutionCode == "NO2") {
            item.pollutionCode = "NO₂";
          } else if (item.pollutionCode == "O3") {
            item.pollutionCode = "O₃";
          } else if (item.pollutionCode == "PM10") {
            item.pollutionCode = "PM₁₀";
          } else if (item.pollutionCode == "PM2.5") {
            item.pollutionCode = "PM₂.₅";
          }
          if (item.unit == "μg/m3") {
            item.unit = "μg/m³";
          }
        });
        this.airsData = data;
        resolve();
      });
    });
  }
  //获取水质监测站点
  fetchWaterStationList() {
    return new Promise<void>((resolve, reject) => {
      getStationList("").then((res) => {
        const data = res.data.data;
        this.waterStation = data;
        this.waterTrendList = data[0].monitorItems;
        this.typeCode = data[0].monitorItems[0].itemCode;
        this.typeUnit = data[0].monitorItems[0].concentrationUnit;
        this.stationId = data[0].stationId;
        Promise.all([
          this.fetchWaterTrend(),
          this.fetchWaterStationRecord(),
        ]).then(() => {
          resolve();
        });
      });
    });
  }
  waterTrendList: any[] = [];
  //水质站点改变
  waterStationChange(e: any) {
    clearInterval(this.waterTimer);
    this.waterStation.find((item: any, index: number) => {
      if (item.stationId == e) {
        this.waterTrendList = item.monitorItems;
        this.typeCode = item.monitorItems[0].itemCode;
        this.curWaterIndex = 0;
        this.stationId = e;
        this.fetchWaterTrend();
        this.fetchWaterStationRecord();
        this.openWaterTimer();
      }
    });
  }
  //水质监测记录
  fetchWaterStationRecord() {
    return new Promise<void>((resolve, reject) => {
      waterStationRecord(this.stationId).then((res) => {
        const data = res.data.data;
        this.waterData = data || [];
        resolve();
      });
    });
  }
  //水质监测趋势
  fetchWaterTrend() {
    return new Promise<void>((resolve, reject) => {
      getMonitorItemDetailRecord(this.stationId, this.typeCode).then((res) => {
        const data = res.data.data ? res.data.data.hourMonitorItems : [];
        const obj: any = {
          name: "",
          bottomList: [],
          dataList: [],
          unit: this.typeUnit,
        };
        data.forEach((item: any) => {
          obj.bottomList.push(`${item.hour}时`);
          obj.dataList.push(Number(item.monitorValue).toFixed(2));
        });
        this.waterTrendAll = obj;
        resolve();
      });
    });
  }
  private typeCode = "";
  private typeUnit = "";
  //水质监测类型改变
  waterTypeChange(e: any) {
    clearInterval(this.waterTimer);
    this.curWaterIndex = e.target.value;
    // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
    // @ts-ignore
    this.typeCode = this.waterTrendList[this.curWaterIndex].itemCode;
    // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
    // @ts-ignore
    this.typeUnit = this.waterTrendList[this.curWaterIndex].concentrationUnit;
    this.fetchWaterTrend();
    this.openWaterTimer();
  }
  // 监测预警
  private fetchEarlyWarning() {
    return new Promise<void>((resolve, reject) => {
      earlyWarning().then((res) => {
        const data = res.data.data;
        data.forEach((item: any) => {
          item.alarmTime = item.alarmTime.slice(5, 16);
          let str = "";
          switch (item.keyWord) {
            case "NO2":
              str = "NO₂";
              break;
            case "SO2":
              str = "SO₂";
              break;
            case "O3":
              str = "O₃";
              break;
            case "PM10":
              str = "PM₁₀";
              break;
            case "PM2.5":
              str = "PM₂.₅";
              break;
          }
          item.alarmContent = item.alarmContent.replace(item.keyWord, str);
        });
        this.airWarning = data;
        resolve();
      });
    });
  }
  //车辆类型列表
  private fetchCarList() {
    return new Promise<void>((resolve, reject) => {
      getCarList().then((res) => {
        const data = res.data.data;

        this.carTypeList = data;
        resolve();
      });
    });
  }
  // 设备信息类型切换
  private stationTypeNameChange(val: any) {
    this.stationTypeName = val;
    if (val === "空气") {
      this.stationTypeIndnx = "0";
      this.airStationTypeName = "0";
      this.equipmentDetail = this.airStationTypeList[0].stationList[0];
    } else {
      this.stationIdIndex = "0";
      this.equipmentDetail = this.waterStation[this.stationIdIndex];
    }
  }
  // 设备信息---获取空气站点列表
  private airStationTypeList: any[] = [];
  // 设备信息------空气站类型下标
  private stationTypeIndnx: any = "0";
  // 设备信息------空气站名称下标
  private airStationTypeName: any = "0";
  // 设备信息------水站名称下标
  private stationIdIndex: any = "0";

  // 设备信息---获取空气站点列表
  private getTypeStationInfoList() {
    typeStationInfoList().then((res: any) => {
      this.airStationTypeList = res.data.data;
    });
  }
  // 设备信息详情
  private equipmentDetail: any = {};
  // 设备信息---空气站点类型
  private stationTypeCodeChange(val: any) {
    this.stationTypeIndnx = val;
    this.airStationTypeName = "0";
    this.equipmentDetail = this.airStationTypeList[val].stationList[
      this.airStationTypeName
    ];
  }
  // 设备信息---空气站点名称
  private airStationTypeNameChange(val: any) {
    this.airStationTypeName = val;
    this.equipmentDetail = this.airStationTypeList[
      this.stationTypeIndnx
    ].stationList[val];
  }
  // 设备信息---水站名称
  private stationIdIndexChange(val: any) {
    this.stationIdIndex = val;
    this.equipmentDetail = this.waterStation[val];
  }
  // 设备状态
  private getEquipmentStatus() {
    getOnlineCount().then((res: any) => {
      const data = res.data.data;
      if (res.data.data) {
        this.stateList = [
          {
            name: `空气站 ${data.airCount.total}个`,
            number: data.airCount.onlineCount,
            total: data.airCount.total,
            startColor: "rgba(64, 119, 200, 1)",
            endColor: "rgba(33, 185, 241, 1)",
          },
          {
            name: `水站 ${data.waterCount.total}个`,
            number: data.waterCount.onlineCount,
            total: data.waterCount.total,
            startColor: "rgba(64, 119, 200, 1)",
            endColor: "rgba(33, 185, 241, 1)",
          },
          {
            name: `摄像头 ${data.cameraCount.total}个`,
            number: data.cameraCount.onlineCount,
            total: data.cameraCount.total,
            startColor: "rgba(64, 119, 200, 1)",
            endColor: "rgba(33, 185, 241, 1)",
          },
        ];
      }
    });
  }
  // 离线设备列表
  private fetchOffline() {
    getOffline().then((res: any) => {
      const list: any = [];
      for (const item of res.data.data) {
        list.push({
          name: item.deviceName,
          type: item.deviceType == 1 ? "空气站" : "水站",
          address: item.deviceAddress,
          time: "5月8日 13:20",
        });
      }
      this.offLineList = list;
    });
  }
  // 任务执行率
  getSelectMonthTaskCompletion() {
    selectMonthTaskCompletion().then((res) => {
      const bottomList = res.data.data.map((item: any) => item.yearMonth);
      const dataList = res.data.data.map((item: any) =>
        ((item.complete / item.total) * 100).toFixed(2)
      );
      const dataList1 = res.data.data.map((item: any) => item.complete);
      const dataList2 = res.data.data.map((item: any) => item.unComplete);
      const dataList3 = res.data.data.map((item: any) => item.total);
      this.executionRate = {
        bottomList,
        dataList,
        dataList1,
        dataList2,
        dataList3,
      };
    });
  }
  getSelectTaskTypeCompletionChange(value: string | number) {
    this.selectTaskTypeCompletionType = value;
    this.getSelectTaskTypeCompletion();
  }
  // 任务类型完成情况
  getSelectTaskTypeCompletion() {
    selectTaskTypeCompletion(this.selectTaskTypeCompletionType).then((res) => {
      this.completeData = {
        bottomList: res.data.data.map((item: any) => item.typeName),
        dataList: res.data.data.map((item: any) => item.completed),
        dataList1: res.data.data.map((item: any) => item.uncompleted),
        dataList2: res.data.data.map((item: any) => item.closeCount || 0),
        // dataList2: data.taskCompletion.map((item) => item.rate.substr(0, item.rate.indexOf('%')))
      };
    });
  }
  getSelectTypeStatisticChange(value: string | number) {
    this.taskType = value;
    this.getSelectTypeStatistic();
  }
  // 任务分类统计
  getSelectTypeStatistic() {
    this.contentListVisiable = false;
    selectTypeStatistic(this.taskType).then((res) => {
      this.contentListVisiable = true;
      this.taskClassificationStatistics = res.data.data.map((item: any) => {
        let icon = "";
        if (item.name === "大气环境") {
          icon = this.dqhj;
        }
        if (item.name === "污染源") {
          icon = this.wry;
        }
        if (item.name === "水环境") {
          icon = this.shj;
        }
        if (item.name === "任务调度") {
          icon = this.rwdd;
        }
        return {
          icon,
          ...item,
        };
      });
    });
  }
  // 跳转数据统计
  toDataTable() {
    this.$router.push("/dataTable");
  }
}
</script>
