<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  padding: 0;
  box-sizing: border-box;
  .center-map {
    position: absolute;
    height: calc(1080px - 0.94rem);
    width: 100%;
    > div {
      width: 100%;
      height: 100%;
    }
  }
  .container-bg {
    overflow: hidden;
    position: absolute;
    pointer-events: none;
    z-index: 2;
    width: 100%;
    height: 100%;
    width: 1920px;
    background-size: 100% 100%;
    /*background-image: url("../../assets/department/marker.png");*/
    .left-part {
      position: absolute;
      pointer-events: auto;
      z-index: 2;
      top: 0;
      left: 0;
      height: calc(1080px - 0.94rem);
      width: 3rem;
      padding: 0.3rem 0.3rem 0;
      .list {
        .btn-list {
          display: flex;
          margin-bottom: 20px;
          .btn {
            text-align: center;
            line-height: 41px;
            width: 100px;
            height: 52px;
            background-repeat: no-repeat;
            background-position: center center;
            background-size: cover;
          }
        }
        cursor: pointer;
        margin-top: 0.7rem;
        padding-left: 0.52rem;
        .left-list {
          width: 1.81rem;
          height: 0.66rem;
          padding-left: 0.65rem;
          box-sizing: border-box;
          background-size: 100% 100% !important;
          line-height: 0.66rem;
          margin-bottom: 0.2rem;
          span {
            font-size: 12px;
            color: white;
            &:nth-child(2) {
              margin-left: 0.05rem;
              font-size: 15px;
            }
          }
        }
      }
    }
    .list-remark {
      padding-left: 0.52rem;
      font-size: 0.14rem;
      font-weight: 400;
      color: #86c6ff;
    }
    .right-part-copy {
      position: absolute;
      pointer-events: auto;
      z-index: 2;
      top: 0;
      right: -5rem;
      opacity: 0.4;
      height: calc(1080px - 0.94rem);
      width: 4.6rem;
      padding: 0.3rem 0.6rem 0.23rem 0.3rem;
      display: flex;
      flex-direction: column;
      // justify-content: space-around;
      transition: all 1.5s ease-out;
      .depart-select {
        display: flex;
        justify-content: flex-end;
      }
      .right-part-one {
        z-index: 9999;
        margin-top: -0.2rem;
        height: calc(2.5rem - (1080px - 1080px) / 3);
        .title {
          font-size: 0.2rem;
          font-family: Source Han Sans CN;
          font-weight: 500;
          text-shadow: 0 0 5px blue, 0 0 5px blue;
        }
        .real-time {
          width: 4rem;
          height: 1.75rem;
        }
        .button-list {
          height: 0.24rem;
          background-color: #0f245e;
          margin: 0.1rem 0;
          /*padding: 0 0.1rem;*/
          display: flex;
          align-items: center;
          box-sizing: border-box;
          color: white;
          line-height: 0.24rem;
          font-size: 0.12rem;
          span {
            flex: 1;
            padding: 0 0.05rem;
            cursor: pointer;
            display: inline-block;
            height: 100%;
            text-align: center;
          }
          .active {
            background-color: #0084ff;
          }
        }
      }
      .right-part-two {
        z-index: 9999;

        height: calc(2.5rem - (1080px - 1080px) / 3);
        .title {
          font-size: 0.2rem;
          font-family: Source Han Sans CN;
          font-weight: 500;
          text-shadow: 0 0 5px blue, 0 0 5px blue;
        }
        .executive-department {
          width: 100%;
          height: 2.1rem;
          position: relative;
          // display: flex;
          // align-items: center;
          // justify-content: center;
          .major-ndoe {
            position: absolute;
            text-align: center;
            font-size: 0.14rem;
            font-weight: 500;
            color: #f9f9f9;
            line-height: 0.5rem;
            top: 1.2rem;
            left: 1.56rem;
          }
          .node {
            position: absolute;
            height: 0.76rem;
            width: 1.46rem;
            text-align: center;
            font-size: 0.14rem;
            font-weight: 500;
            color: #f9f9f9;
            line-height: 0.5rem;
            background-size: 100% 100%;
            &:nth-child(1) {
              /*background-image: url('../../assets/department/<EMAIL>');*/
              top: 30px;
              left: -10px;
            }
            &:nth-child(2) {
              /*background-image: url('../../assets/department/<EMAIL>');*/
              top: 30px;
              left: 110px;
            }
            &:nth-child(3) {
              /*background-image: url('../../assets/department/<EMAIL>');*/
              top: 30px;
              left: 240px;
            }
          }
          .node2 {
            position: absolute;
            height: 0.76rem;
            width: 1.46rem;
            text-align: center;
            font-size: 0.14rem;
            font-weight: 500;
            color: #f9f9f9;
            line-height: 0.5rem;
            background-size: 100% 100%;
            &:nth-child(1) {
              /*background-image: url('../../assets/department/<EMAIL>');*/
              top: 30px;
              left: -10px;
            }
            &:nth-child(2) {
              /*background-image: url('../../assets/department/<EMAIL>');*/
              top: 30px;
              left: 230px;
            }
          }
          .alarm-title {
            margin-top: 0.1rem;
            margin-bottom: 0.1rem;
            width: 100%;
            height: 0.49rem;
            background-image: url("../../assets/task/<EMAIL>");
            background-size: 100% 100%;
            font-size: 0.16rem;
            font-family: Source Han Sans CN;
            font-weight: 500;
            color: #dcf0ff;
            display: flex;
            justify-content: center;
            align-items: center;
            span {
              margin-top: -12px;
            }
            // span {
            //   margin-top: 12px;
            // }
          }
          .alarm-type-area {
            height: 1.24rem;
          }
          .single-alarm {
            height: 0.62rem;
          }
          .alarm-list {
            display: flex;
            align-items: center;
            justify-content: space-between;
            > div {
              width: calc(50% - 0.06rem);
              height: 0.52rem;
              background-image: url("../../assets/task/<EMAIL>");
              background-size: 100% 100%;
              font-size: 0.13rem;
              font-family: PingFang SC;
              font-weight: 400;
              color: #d8e6f3;
              text-align: center;
              line-height: 0.52rem;
              display: flex;
              align-items: center;
              justify-content: center;
              position: relative;
              img {
                position: absolute;
                top: 0.06rem;
                left: 0.06rem;
              }
            }
            .complteted-icon {
              background-image: url("../../assets/task/<EMAIL>");
              background-size: 100% 100%;
            }
            .complteted-icons {
              background-image: url("../../assets/task/<EMAIL>");
              background-size: 100% 100%;
            }
          }
          // .main-bg {
          //   width: 3.75rem;
          //   height: 1.01rem;
          //   background-size: 100% 100%;
          //   position: relative;
          //   background-image: url('../../assets/task/<EMAIL>');
          //   .alarm-type {
          //     width: 1.48rem;
          //     height: 0.39rem;
          //     line-height: 0.39rem;
          //     text-align: center;
          //     background-size: 100% 100%;
          //     background-image: url('../../assets/task/<EMAIL>');
          //     position: absolute;
          //     left: 1.12rem;
          //     top: 0.15rem;
          //     font-size: 0.14rem;
          //     font-family: PingFang SC;
          //     font-weight: 400;
          //     color: #FCDE79;
          //   }
          //   .alarm-node {
          //     display: inline-block;
          //     padding: 0.11rem 0.12rem;
          //     width: 1.2rem;
          //     height: 0.4rem;
          //     box-sizing: border-box;
          //     text-align: center;
          //     background-size: 100% 100%;
          //     background-image: url('../../assets/task/<EMAIL>');
          //     font-size: 0.12rem;
          //     font-family: PingFang SC;
          //     font-weight: 400;
          //     color: #AEC4D9;
          //     cursor: pointer;
          //   }
          //   .alarm-nodes {
          //    background-image: url('../../assets/task/<EMAIL>');
          //   }
          //   .animation-area {
          //     width: 3.7rem;
          //     height: 1.3rem;
          //     position: absolute;
          //     top: -0.25rem;
          //     left: 0.31rem;
          //     .alarm-node:nth-child(1) {
          //       position: absolute;
          //       // top:-0.12rem;
          //       left: -0.55rem;
          //       top: -0.2rem;
          //       z-index: 2;
          //     }
          //     .alarm-node:nth-child(2) {
          //       position: absolute;
          //       left: -0.55rem;
          //       top: -0.2rem;
          //       z-index: 2;
          //     }
          //     .alarm-node:nth-child(3) {
          //       position: absolute;
          //       left: -0.55rem;
          //       top: -0.2rem;
          //       z-index: 2;
          //     }
          //     .alarm-node:nth-child(4) {
          //       position: absolute;
          //       left: -0.55rem;
          //       top: -0.2rem;
          //       z-index: 2;
          //     }
          //   }
          //   .custom-svg {
          //     position: absolute;
          //     top:0;
          //     left: 0;
          //     // top: -0.25rem;
          //     // left: 0.25rem;
          //     visibility: hidden;
          //     pointer-events: none;
          //   }
          // }
        }
      }
      .right-part-three {
        z-index: 9999;
        max-height: calc(3.2rem - (1080px - 1080px) / 3);
        /*overflow: hidden;*/
        overflow: inherit;
        .title {
          font-size: 0.2rem;
          font-family: Source Han Sans CN;
          font-weight: 500;
          text-shadow: 0 0 5px blue, 0 0 5px blue;
        }
        .reback {
          width: 100%;
          height: 2.82rem;
          margin-top: 0.35rem;
          box-sizing: border-box;
          .reback-list {
            display: flex;
            .left {
              padding-top: 0.05rem;
              box-sizing: border-box;
              display: flex;
              flex-direction: column;
              align-items: flex-end;
              span {
                color: #c9dbeb;
                font-size: 0.13rem;
              }
            }
            .center {
              width: 0.16rem;
              height: 0.14rem;
              margin: 0.05rem;
              background-size: 100% 100%;
              background-image: url("../../assets/department/<EMAIL>");
              position: relative;
            }
            .after {
              &::after {
                width: 0.01rem;
                height: 1.29rem;
                position: absolute;
                content: "";
                top: 0.12rem;
                left: 0.07rem;
                background-color: #1293f2;
              }
            }
            .right {
              width: 2.8rem;
              height: 1.12rem;
              padding: 0.28rem;
              box-sizing: border-box;
              background-size: 100% 100%;
              background-image: url("../../assets/department/<EMAIL>");
              .title {
                color: white;
                font-size: 0.15rem;
              }
              .content {
                color: #9bc8f1;
                font-size: 0.14rem;
                width: 100%;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: normal;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 2; /*规定最多显示两行*/
              }
              .check {
                width: 0.72rem;
                height: 0.2rem;
                text-align: center;
                line-height: 0.2rem;
                background: rgba(1, 55, 166, 0.41);
                border-radius: 2px;
                color: #00bde5;
                font-size: 0.11rem;
                cursor: pointer;
              }
            }
          }
        }
        .alram-detail {
          width: 100%;
          max-height: calc(3rem - (1080px - 1080px) / 3);
          .top {
            height: calc(2rem - (1080px - 1080px) / 3);
            background-image: url("../../assets/bottom.png");
            background-size: 100% 100%;
            padding: 0.2rem 0.3rem;
            .list {
              display: flex;
              align-items: center;
              span {
                display: inline-block;
                &:nth-child(1) {
                  width: 0.7rem;
                  font-size: 0.14rem;
                  font-family: PingFang SC;
                  font-weight: 400;
                  color: #5abefe;
                }
                &:nth-child(2) {
                  width: calc(100% - 0.7rem);
                  font-size: 0.14rem;
                  font-family: PingFang SC;
                  font-weight: 400;
                  color: #dcf0ff;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                  overflow: hidden;
                }
              }
            }
          }
          .deatil {
            text-overflow: ellipsis;
            overflow: hidden;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
          }
          .bottom {
            margin-top: -0.6rem;
            /*height: calc(1.8rem - (1080px - 100vh) / 3);*/
            background-image: url("../../assets/top.png");
            background-size: 100% 100%;
            padding: 0.2rem 0.3rem;
            .des {
              font-size: 0.14rem;
              font-family: PingFang SC;
              font-weight: 400;
              margin-bottom: 0.05rem;
              color: #5abefe;
            }
            .table {
              color: white;
              .table-header {
                background-color: rgba(15, 36, 94, 0.6);
                span {
                  width: 25%;
                  display: inline-block;
                  height: 0.25rem;
                  text-align: center;
                  line-height: 0.25rem;
                  color: #5abefe;
                  font-size: 0.14rem;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                }
              }
              .table-list {
                &:nth-child(2n) {
                  background-color: rgba(3, 21, 61, 0.6);
                }
                &:nth-child(2n + 1) {
                  background-color: rgba(15, 36, 94, 0.6);
                }
                span {
                  width: 25%;
                  display: inline-block;
                  height: 0.25rem;
                  text-align: center;
                  line-height: 0.25rem;
                  color: #bccede;
                  font-size: 0.14rem;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                }
                .active {
                  color: #ff5f58;
                }
              }
            }
            .list-area {
              max-height: 0.6rem;
              overflow-y: auto;
              &::-webkit-scrollbar-track {
                // background: rgb(239, 239, 239);
                background: transparent;
                border-radius: 2px;
              }

              &::-webkit-scrollbar-thumb {
                background: #469fe7bb;
                border-radius: 0.05rem;
              }

              &::-webkit-scrollbar-thumb:hover {
                background: #469fe7;
              }

              &::-webkit-scrollbar-corner {
                background: #469fe7;
              }
            }
          }
          .no-datas {
            line-height: 0.6rem;
            font-size: 0.14rem;
            text-align: center;
          }
        }
      }
    }
    .right-part {
      position: absolute;
      pointer-events: auto;
      z-index: 2;
      top: 0;
      right: 0;
      opacity: 1;
      height: calc(1080px - 0.94rem);
      width: 4.6rem;
      padding: 0.3rem 0.6rem 0.43rem 0.3rem;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      transition: all 1.5s ease-out;
      .depart-select {
        display: flex;
        justify-content: flex-end;
      }
      .right-part-one {
        margin-top: 0.2rem;
        height: calc(2.5rem - (1080px - 1080px) / 3);
        .title {
          font-size: 0.2rem;
          font-family: Source Han Sans CN;
          font-weight: 500;
          text-shadow: 0 0 5px blue, 0 0 5px blue;
        }
        .real-time {
          width: 4rem;
          height: 1.75rem;
        }
        .button-list {
          height: 0.24rem;
          background-color: #0f245e;
          margin: 0.1rem 0 0.05rem;
          padding: 0 0.1rem;
          /*display: flex;*/
          box-sizing: border-box;
          color: white;
          line-height: 0.24rem;
          font-size: 0.12rem;
          span {
            padding: 0 0.1rem;
            cursor: pointer;
            display: inline-block;
            height: 100%;
          }
          .active {
            background-color: #0084ff;
          }
        }
      }
      .right-part-two {
        height: calc(2.5rem - (1080px - 1080px) / 3);
        .title {
          font-size: 0.2rem;
          font-family: Source Han Sans CN;
          font-weight: 500;
          text-shadow: 0 0 5px blue, 0 0 5px blue;
        }
        .executive-department {
          width: 100%;
          height: 2.1rem;
        }
      }
      .right-part-three {
        height: calc(2.8rem - (1080px - 1080px) / 3);
        /*overflow: hidden;*/
        .title {
          font-size: 0.2rem;
          font-family: Source Han Sans CN;
          font-weight: 500;
          text-shadow: 0 0 5px blue, 0 0 5px blue;
        }
        .reback {
          width: 100%;
          height: 2.82rem;
          margin-top: 0.35rem;
          box-sizing: border-box;
          .reback-list {
            display: flex;
            .left {
              padding-top: 0.05rem;
              box-sizing: border-box;
              display: flex;
              flex-direction: column;
              align-items: flex-end;
              span {
                color: #c9dbeb;
                font-size: 0.13rem;
              }
            }
            .center {
              width: 0.16rem;
              height: 0.14rem;
              margin: 0.05rem;
              background-size: 100% 100%;
              background-image: url("../../assets/department/<EMAIL>");
              position: relative;
            }
            .after {
              &::after {
                width: 0.01rem;
                height: 1.29rem;
                position: absolute;
                content: "";
                top: 0.12rem;
                left: 0.07rem;
                background-color: #1293f2;
              }
            }
            .right {
              width: 2.99rem;
              height: 1.12rem;
              padding: 0.28rem;
              box-sizing: border-box;
              background-size: 100% 100%;
              background-image: url("../../assets/department/<EMAIL>");
              .title {
                color: white;
                font-size: 0.15rem;
              }
              .content {
                color: #9bc8f1;
                font-size: 0.14rem;
                width: 100%;
                overflow: hidden; /*超出部分隐藏*/
                white-space: nowrap; /*不换行*/
                text-overflow: ellipsis; /*超出部分文字以...显示*/
              }
              .check {
                width: 0.72rem;
                height: 0.2rem;
                text-align: center;
                line-height: 0.2rem;
                background: rgba(1, 55, 166, 0.41);
                border-radius: 2px;
                color: #00bde5;
                font-size: 0.11rem;
                cursor: pointer;
              }
            }
          }
        }
      }
    }
    .no-feed {
      width: 100%;
      height: 2rem;
      margin-top: 0.35rem;
      box-sizing: border-box;
      color: #ffffff;
      font-size: 0.18rem;
      text-align: center;
      line-height: 2rem;
    }
    .right-part-to-right {
      right: -5rem;
      opacity: 0.4;
    }
    .right-part-to-left {
      right: 0;
      opacity: 1;
    }
    .bottom-part {
      position: absolute;
      pointer-events: auto;
      z-index: 2;
      bottom: 0;
      left: 0.4rem;
      // height: 2.48rem;
      width: calc(100% - 4.9rem);
      .procress {
        .procress-name {
          pointer-events: auto;
          background-image: url("../../assets/emergency/anniubg.png");
          background-size: 100% 100%;
          width: 0.96rem;
          height: 0.4rem;
          font-size: 0.16rem;
          font-weight: 400;
          color: rgba(165, 248, 255, 1);
          text-align: center;
          line-height: 0.4rem;
        }
        .procress-area {
          display: flex;
          position: relative;
          .taskAlert {
            position: absolute;
            pointer-events: auto;
            width: 2.68rem;
            height: 2.05rem;
            /*background: rgba(8, 45, 120, 0.8);*/
            background-image: url("../../assets/emergency/<EMAIL>");
            background-size: 100% 100%;
            top: -1.8rem;
            z-index: 999999;
            padding: 0.56rem 0.47rem 0.84rem 0.32rem;
            .replyContent {
              padding-left: 0.22rem;
              width: 100%;
              /*display: -webkit-box;*/
              /*-webkit-line-clamp: 2;*/
              /*-webkit-box-orient: vertical;*/
              /*overflow: hidden;*/
              color: #f2f9ff;
              font-size: 0.14rem;
              line-height: 0.17rem;
              position: relative;
              &::before {
                width: 12px;
                height: 12px;
                content: "";
                left: 0;
                background: #00cfb9;
                border-radius: 50%;
                position: absolute;
              }
              .btn {
                font-size: 0.14rem;
                font-weight: 400;
                color: #00eaff;
                float: right;
                cursor: pointer;
              }
            }
            .replyContent1 {
              &::before {
                background: #89c34a;
              }
            }
            .replyContent2 {
              &::before {
                background: #04b9ee;
              }
            }
            .replyContent3 {
              &::before {
                background: #e6a33b;
              }
            }
            .replyContent4 {
              &::before {
                background: #153f9f;
              }
            }
            .replyContent5 {
              &::before {
                background: #e7dd6c;
              }
            }
            .replyContent6 {
              &::before {
                background: #6897f6;
              }
            }
            .replyContent7 {
              &::before {
                background: #4fbdb4;
              }
            }
            .replyContent8 {
              &::before {
                background: #4c3b83;
              }
            }
            .creatTime {
              margin-top: 0.2rem;
              padding-left: 0.22rem;
              font-size: 0.14rem;
              color: #dcf0ff;
            }
          }
          .department-list {
            pointer-events: auto;
            width: 1.8rem;
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
            > div {
              cursor: pointer;
              margin-top: 0.1rem;
              display: flex;
              align-items: center;
              justify-content: flex-end;
              span {
                position: relative;
                &:first-child {
                  display: inline-block;
                  font-size: 0.12rem;
                  line-height: 0.12rem;
                  font-weight: 500;
                  color: #cee1f0;
                }
                &:nth-child(2) {
                  margin-left: 0.15rem;
                  width: 13px;
                  height: 13px;
                }
              }
              &:nth-child(1) {
                span {
                  &:nth-child(2) {
                    border: 1px solid #89c34a;
                    border-radius: 50%;
                  }
                }
                .department-active {
                  background: rgba(72, 97, 128, 0.6);
                  border-radius: 3px;
                  padding: 0.08rem 0.12rem;
                }
                .depart-circle-active {
                  &:after {
                    position: absolute;
                    content: "";
                    width: 0.07rem;
                    height: 0.07rem;
                    background: #89c34a;
                    border-radius: 50%;
                    top: 0.02rem;
                    left: 0.02rem;
                  }
                }
              }
              &:nth-child(2) {
                span {
                  &:nth-child(2) {
                    border: 1px solid #2980ff;
                    border-radius: 50%;
                  }
                }
                .department-active {
                  background: rgba(72, 97, 128, 0.6);
                  border-radius: 3px;
                  padding: 0.08rem 0.12rem;
                }
                .depart-circle-active {
                  &:after {
                    position: absolute;
                    content: "";
                    width: 0.07rem;
                    height: 0.07rem;
                    background: #2980ff;
                    border-radius: 50%;
                    top: 0.02rem;
                    left: 0.02rem;
                  }
                }
              }
              &:nth-child(3) {
                span {
                  &:nth-child(2) {
                    border: 1px solid #eaa03f;
                    border-radius: 50%;
                  }
                }
                .department-active {
                  background: rgba(72, 97, 128, 0.6);
                  border-radius: 3px;
                  padding: 0.08rem 0.12rem;
                }
                .depart-circle-active {
                  &:after {
                    position: absolute;
                    content: "";
                    width: 0.07rem;
                    height: 0.07rem;
                    background: #eaa03f;
                    border-radius: 50%;
                    top: 0.02rem;
                    left: 0.02rem;
                  }
                }
              }
              &:nth-child(4) {
                span {
                  &:nth-child(2) {
                    border: 1px solid #083295;
                    border-radius: 50%;
                  }
                }
                .department-active {
                  background: rgba(72, 97, 128, 0.6);
                  border-radius: 3px;
                  padding: 0.08rem 0.12rem;
                }
                .depart-circle-active {
                  &:after {
                    position: absolute;
                    content: "";
                    width: 0.07rem;
                    height: 0.07rem;
                    background: #083295;
                    border-radius: 50%;
                    top: 0.02rem;
                    left: 0.02rem;
                  }
                }
              }
              &:nth-child(5) {
                span {
                  &:nth-child(2) {
                    border: 1px solid #e7dd6c;
                    border-radius: 50%;
                  }
                }
                .department-active {
                  background: rgba(72, 97, 128, 0.6);
                  border-radius: 3px;
                  padding: 0.08rem 0.12rem;
                }
                .depart-circle-active {
                  &:after {
                    position: absolute;
                    content: "";
                    width: 0.07rem;
                    height: 0.07rem;
                    background: #e7dd6c;
                    border-radius: 50%;
                    top: 0.02rem;
                    left: 0.02rem;
                  }
                }
              }
              &:nth-child(6) {
                span {
                  &:nth-child(2) {
                    border: 1px solid #6897f6;
                    border-radius: 50%;
                  }
                }
                .department-active {
                  background: rgba(72, 97, 128, 0.6);
                  border-radius: 3px;
                  padding: 0.08rem 0.12rem;
                }
                .depart-circle-active {
                  &:after {
                    position: absolute;
                    content: "";
                    width: 0.07rem;
                    height: 0.07rem;
                    background: #6897f6;
                    border-radius: 50%;
                    top: 0.02rem;
                    left: 0.02rem;
                  }
                }
              }
              &:nth-child(7) {
                span {
                  &:nth-child(2) {
                    border: 1px solid #4fbdb4;
                    border-radius: 50%;
                  }
                }
                .department-active {
                  background: rgba(72, 97, 128, 0.6);
                  border-radius: 3px;
                  padding: 0.08rem 0.12rem;
                }
                .depart-circle-active {
                  &:after {
                    position: absolute;
                    content: "";
                    width: 0.07rem;
                    height: 0.07rem;
                    background: #4fbdb4;
                    border-radius: 50%;
                    top: 0.02rem;
                    left: 0.02rem;
                  }
                }
              }
              &:nth-child(8) {
                span {
                  &:nth-child(2) {
                    border: 1px solid #4c3b83;
                    border-radius: 50%;
                  }
                }
                .department-active {
                  background: rgba(72, 97, 128, 0.6);
                  border-radius: 3px;
                  padding: 0.08rem 0.12rem;
                }
                .depart-circle-active {
                  &:after {
                    position: absolute;
                    content: "";
                    width: 0.07rem;
                    height: 0.07rem;
                    background: #4c3b83;
                    border-radius: 50%;
                    top: 0.02rem;
                    left: 0.02rem;
                  }
                }
              }
            }
          }
          .timeLine-text-line {
            margin-left: 0.1rem;
            width: calc(100% - 1.9rem);
            overflow: hidden;
            .line-one {
              height: 2px;
              background-color: rgba(255, 255, 255, 0.4);
              width: 100%;
              position: absolute;
              top: 0rem;
              &:not(:nth-child(3)) {
                background: linear-gradient(
                  to right,
                  #ccc,
                  #ccc 5px,
                  transparent 5px,
                  transparent
                );
                background-size: 0.1rem 100%;
              }
              // &:first-child {
              //   background-color: rgba(255,255,255,.4);
              // }
              // &:nth-child(3) {
              //   top: 0.38rem;
              //   background: linear-gradient(to right, #ccc, #ccc 5px, transparent 5px, transparent);
              //   background-size: 0.1rem 100%;
              // }
              // &:nth-child(4) {
              //   top: 0.76rem;
              //   background: linear-gradient(to right, #ccc, #ccc 5px, transparent 5px, transparent);
              //   background-size: 0.1rem 100%;
              // }
              // &:nth-child(5) {
              //   top: 1.14rem;
              //   background: linear-gradient(to right, #ccc, #ccc 5px, transparent 5px, transparent);
              //   background-size: 0.1rem 100%;
              // }
            }

            .line-scrool {
              background: transparent;
              width: 100%;
              position: absolute;
              top: 0;
              height: 1.4rem;
            }
            ul {
              padding: 0;
            }
            #timess {
              width: 100%;
              height: 0rem;
              font-size: 0.1rem;
              color: #6aa1cb;
              -moz-user-select: none;
              /*火狐*/
              -webkit-user-select: none;
              /*webkit浏览器*/
              -ms-user-select: none;
              /*IE10*/
              -khtml-user-select: none;
              /*早期浏览器*/
              user-select: none;
            }
            #timecs {
              height: 0.02rem;
              position: relative;
              cursor: pointer;
              top: 0rem;
            }

            #timecs li {
              float: left;
              background: #b4f7fd;
              height: 6px;
              width: 1px;
              position: absolute;
              list-style: none;
            }
          }
        }
      }
    }
    .bottom-part-copy {
      position: absolute;
      pointer-events: auto;
      z-index: 2;
      bottom: -5rem;
      left: 0.4rem;
      // height: 2.48rem;
      width: calc(100% - 4.9rem);
      .procress {
        .procress-name {
          pointer-events: auto;
          background-image: url("../../assets/emergency/anniubg.png");
          background-size: 100% 100%;
          width: 0.96rem;
          height: 0.4rem;
          font-size: 0.16rem;
          font-weight: 400;
          color: rgba(165, 248, 255, 1);
          text-align: center;
          line-height: 0.4rem;
        }
        .procress-area {
          display: flex;
          position: relative;
        }
      }
    }
    .bottom-part-to-bottom {
      bottom: -5rem;
      opacity: 0.4;
    }
    .bottom-part-to-top {
      bottom: 0.1rem;
      opacity: 1;
    }
    .enterprise-search {
      position: absolute;
      pointer-events: auto;
      z-index: 2;
      top: 0.5rem;
      right: 50%;
      display: flex;
      align-items: center;
      width: 7.5rem;
      height: 0.6rem;
      margin-right: -3.5rem;
      > :nth-of-type(1) {
        display: flex;
        align-items: center;
        width: 6.5rem;
        height: 0.6rem;
        background: rgba(5, 7, 95, 0.7);
        border: 1px solid #034fa8;
        input {
          width: 100%;
          margin-left: 0.25rem;
          // margin-right: 0.25rem;
          background: transparent;
          border: none;
          outline: none;
          font-size: 0.22rem;
        }
        input::-webkit-input-placeholder {
          color: #ffffff;
        }
        input::-moz-placeholder {
          /* Mozilla Firefox 19+ */
          color: #ffffff;
        }
        input:-moz-placeholder {
          /* Mozilla Firefox 4 to 18 */
          color: #ffffff;
        }
        input:-ms-input-placeholder {
          /* Internet Explorer 10-11 */
          color: #ffffff;
        }
      }
      > :nth-of-type(2) {
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(5, 7, 95, 0.7);
        border: 1px solid #034fa8;
        height: 0.6rem;
        width: 1rem;
        img {
          cursor: pointer;
        }
      }
    }
  }
}
.quanpin {
  position: absolute;
  top: -0.88rem;
  right: 0.5rem;
  transition: 1.5s;
  z-index: 99;
}
.fullscreen {
  width: 100%;
  height: 100%;
}
.task-scoll-box {
  height: 520px;
  overflow: scroll;
  &::-webkit-scrollbar {
    display: none;
  }
}
</style>
<style lang="less">
.el-carousel__item {
  display: flex;
  align-items: center;
}
// .el-carousel__container {
//   height: 188px;
// }
.ant-select-dropdown-menu-item-active:not(.ant-select-dropdown-menu-item-disabled) {
  background-color: #0061c6 !important;
  color: white !important;
}
.depart-select {
  .select-main {
    width: 2.77rem !important;
    height: 0.47rem !important;
    display: flex;
    justify-content: space-between;
    .ant-select-selection__rendered {
      width: 100%;
      height: 100%;
      line-height: 0.47rem;
      .ant-select-selection-selected-value {
        width: 100%;
        height: 100%;
        padding-left: 0.56rem;
        color: #a9d3ff;
        font-size: 0.16rem !important;
      }
    }
    .ant-select-arrow {
      right: 0.3rem;
    }
    .ant-select-selection,
    .ant-select-focused {
      width: 100%;
      height: 100%;
      border: none !important;
      outline: none !important;
      border-radius: unset;
      background: url(../../assets/department/<EMAIL>);
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
    .ant-select-selection-selected-value {
      color: rgba(0, 234, 255, 1);
    }
    .ant-select-focused .ant-select-selection,
    .ant-select-selection:focus,
    .ant-select-selection:active {
      border-color: transparent !important;
      border-right-width: 0 !important;
      outline: 0 !important;
      box-shadow: none !important;
      border: none !important;
    }
    .ant-select-selection__rendered {
      outline: none;
    }
  }
}
.select-dispatch {
  width: 100%;
  .ant-select-selection {
    background: rgba(9, 21, 42, 0.6);
    box-shadow: 0px 0px 6px 1px rgba(18, 143, 239, 0.8) inset;
    border: none;
    color: white;
  }
}
.select-plan {
  width: 100%;
  .ant-select-selection {
    display: flex;
    background: rgba(9, 21, 42, 0.6);
    box-shadow: 0px 0px 6px 1px rgba(18, 143, 239, 0.8) inset;
    border: none;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    color: white;
  }
  .ant-select-selection__rendered {
    width: 100%;
    height: 100%;
  }
  .ant-select-selection-selected-value {
    color: rgba(0, 234, 255, 1);
    font-size: 0.17rem;
    width: 90%;
  }

  .ant-select-focused .ant-select-selection,
  .ant-select-selection:focus,
  .ant-select-selection:active {
    border-color: #40a9ff;
    border-right-width: 0 !important;
    outline: 0;
    box-shadow: none;
  }
}
.date-dispatch {
  .ant-input {
    border: none !important;
    margin-left: 0 !important;
    background: rgba(9, 21, 42, 0.6) !important;
    box-shadow: 0px 0px 6px 1px rgba(18, 143, 239, 0.8) inset !important;
    color: white;
  }
}
.ant-modal-alarm {
  top: 1.8rem !important;
  .ant-modal-content {
    width: 5.09rem !important;
    min-height: 4.4rem !important;
    background-color: transparent !important;
    background-image: url("../../assets/department/<EMAIL>") !important;
    background-size: 100% 100% !important;
  }
  .ant-modal-footer {
    border: none !important;
    display: none !important;
  }
  .ant-modal-close-x {
    padding-top: 20px !important;
    color: #42adfb !important;
  }
  .ant-modal-body {
    padding: 0.16rem 0.54rem 0.28rem !important;
    min-height: 4.4rem;
    box-sizing: border-box;
    .title {
      color: #dcf0ff;
      font-size: 0.16rem;
      width: 3.25rem;
      height: 0.3rem;
      line-height: 0.3rem;
      background-image: url("../../assets/department/<EMAIL>");
      background-size: 100% 100%;
      margin-bottom: 0.24rem;
      margin-left: -0.36rem;
      padding-left: 0.3rem;
    }
    .content {
      .list {
        display: flex;
        align-items: center;
        margin-bottom: 0.16rem;
        > span {
          color: #5abefe;
          display: inline-block;
          font-size: 0.14rem;
          &:nth-child(1) {
            width: 0.8rem;
          }
          &:nth-child(2) {
            color: white;
            display: inline-block;
            width: calc(100% - 0.8rem);
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
        .radio-group {
          margin-left: 0.12rem;
          display: inline-block;
          span {
            color: white !important;
          }
        }
        .ant-cascader-picker {
          background: rgba(9, 21, 42, 0.6);
          box-shadow: 0px 0px 6px 1px rgba(18, 143, 239, 0.8);
          opacity: 0.77;
          border-color: #128fef;
          margin-bottom: 0 !important;
          .ant-cascader-input {
            height: 0.4rem;
          }
          .ant-cascader-picker-label {
            width: 95% !important;
          }
        }
        .ant-input {
          background: rgba(9, 21, 42, 0.6);
          box-shadow: 0px 0px 6px 1px rgba(18, 143, 239, 0.8);
          opacity: 0.77;
          border-color: #128fef;
          margin-left: 0.16rem;
        }
      }
      .textarea {
        align-items: flex-start;
      }
      .parmas {
        margin-bottom: 0.012rem;
      }
      .table {
        margin-bottom: 0.16rem;
        color: white;
        .table-header {
          background-color: rgba(15, 36, 94, 0.6);
          span {
            width: 25%;
            display: inline-block;
            height: 0.3rem;
            text-align: center;
            line-height: 0.3rem;
            color: #5abefe;
            font-size: 0.14rem;
          }
        }
        .table-list {
          &:nth-child(2n) {
            background-color: rgba(3, 21, 61, 0.6);
          }
          &:nth-child(2n + 1) {
            background-color: rgba(15, 36, 94, 0.6);
          }
          span {
            width: 25%;
            display: inline-block;
            height: 0.3rem;
            text-align: center;
            line-height: 0.3rem;
            color: #bccede;
            font-size: 0.14rem;
          }
          .active {
            color: #ff5f58;
          }
        }
      }
    }
  }
  .footer {
    display: flex;
    align-items: center;
    padding-left: 0.8rem;
    padding-top: 0.2rem;
    padding-bottom: 0.3rem;
    color: #ffffff;
    .cancel {
      text-align: center;
      cursor: pointer;
      line-height: 0.31rem;
      width: 0.9rem;
      height: 0.31rem;
      background-image: url("../../assets/department/<EMAIL>");
      background-size: 100% 100%;
    }
    .confirm {
      text-align: center;
      cursor: pointer;
      line-height: 0.31rem;
      width: 0.9rem;
      height: 0.31rem;
      background-image: url("../../assets/department/<EMAIL>");
      background-size: 100% 100%;
      margin-left: 0.22rem;
    }
  }
  .ant-modal-footer {
    padding: 0.28rem 0.54rem;
    .footer {
      display: flex;
      align-items: center;
      padding-left: 0.8rem;
      color: #ffffff;
      .cancel {
        text-align: center;
        cursor: pointer;
        line-height: 0.31rem;
        width: 0.9rem;
        height: 0.31rem;
        background-image: url("../../assets/department/<EMAIL>");
        background-size: 100% 100%;
      }
      .confirm {
        text-align: center;
        cursor: pointer;
        line-height: 0.31rem;
        width: 0.9rem;
        height: 0.31rem;
        background-image: url("../../assets/department/<EMAIL>");
        background-size: 100% 100%;
        margin-left: 0.22rem;
      }
    }
  }
}
.ant-modal-alarmss {
  .ant-modal-body {
    padding-top: 0.2rem !important;
  }
}
.ant-modal-process {
  width: 991.5px !important;
  height: 667.5px !important;
  .title {
    height: 37px;
    background-image: url("../../assets/department/dptitle.png") !important;
    background-repeat: no-repeat;
    font-size: 16px;
    font-family: PingFang SC;
    font-weight: 400;
    color: #dcf0ff;
    padding-left: 30px;
    padding-right: 10px;
    line-height: 37px;
    display: flex;
    justify-content: space-between;
  }
  .close {
    widows: 16px;
    height: 16px;
    cursor: pointer;
  }
  .content {
    overflow: hidden;
    width: 904px;
    height: 533px;
  }
  .ant-modal-content {
    overflow: hidden;
    height: 667.5px !important;
    position: relative;
    // max-width: 1000px!important;
    // width: 5.09rem !important;
    // max-height: 1000px !important;
    padding: 20px !important;
    background-color: transparent !important;
    background-image: url("../../assets/department/dpbg.png") !important;
    background-size: 100% 100% !important;
    // background-size:cover;
    // background-position: center center;
  }
}
.ant-modal-taskDetail {
  width: 509px !important;
  height: 653px !important;
  .title {
    height: 37px;
    background-image: url("../../assets/department/dptitle.png") !important;
    background-repeat: no-repeat;
    font-size: 16px;
    font-family: PingFang SC;
    font-weight: 400;
    color: #dcf0ff;
    padding-left: 30px;
    padding-right: 0px;
    line-height: 37px;
    display: flex;
    justify-content: space-between;
  }
  .close {
    widows: 16px;
    height: 16px;
    cursor: pointer;
  }
  .content {
    margin-top: 20px;
    overflow: hidden;
    // width: 904px;
    // height: 533px;
    .list {
      display: flex;
      .list-label {
        display: flex;
        font-size: 14px;
        font-family: PingFang SC;
        font-weight: 400;
        color: #5abefe;
        width: 75px;
        line-height: 30px;
        justify-content: space-between;
      }
      .value {
        font-size: 14px;
        font-family: PingFang SC;
        font-weight: 400;
        color: #dcf0ff;
        flex: 1;
        line-height: 30px;
      }
    }
    .img-list {
      margin-top: 20px;
      background: url("~@/assets/department/<EMAIL>");
      width: 419px;
      padding: 20px;
      height: 268px;
      box-sizing: border-box;
      overflow: hidden;
    }
  }
  .ant-modal-content {
    overflow: hidden;
    position: relative;
    padding: 20px !important;
    background-color: transparent !important;
    background-image: url("../../assets/department/<EMAIL>") !important;
    background-size: 100% 100% !important;
    // background-size:cover;
    // background-position: center center;
  }
}
.law-vehicle,
.law-vehicle-2 {
  width: 0.9rem;
  height: 0.91rem;
  position: absolute;
  pointer-events: auto;
  z-index: 99;
  top: 0.4rem;
  right: 4.5rem;
  cursor: pointer;
  background-image: url("../../assets/department/<EMAIL>");
  background-size: 100% 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  .vehicle-text {
    font-size: 0.1rem;
    font-weight: bold;
    color: #ffffff;
    top: 0;
  }
  .within-vehicle {
    position: absolute;
    width: 100%;
    height: 100%;
    background-image: url("../../assets/department/<EMAIL>");
  }
  @-webkit-keyframes rotation {
    from {
      -webkit-transform: rotate(0deg);
    }
    to {
      -webkit-transform: rotate(360deg);
    }
  }
  .Rotation {
    -webkit-transform: rotate(360deg);
    animation: rotation 2s linear infinite;
    -moz-animation: rotation 2s linear infinite;
    -webkit-animation: rotation 2s linear infinite;
    -o-animation: rotation 2s linear infinite;
  }
}
.law-vehicle-2 {
  top: 1.5rem;
}
.ant-select-dropdown-menu-item {
  font-size: 0.16rem !important;
}
.bottom-part {
  .procress-area {
    .bottom-area {
      height: 1.62rem;
      overflow-y: auto;
      margin-bottom: 0.2rem;
      width: 100%;
      &::-webkit-scrollbar {
        // display: none; /* Chrome Safari */
      }
      .bottom-areas {
        display: flex;
        position: relative;
      }
    }
    display: flex;
    position: relative;
    .department-list {
      width: 1.8rem;
      display: flex;
      flex-direction: column;
      justify-content: flex-end;
      padding-bottom: 0.2rem;
      > div {
        cursor: pointer;
        margin-top: 0.1rem;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        height: 0.28rem;
        span {
          position: relative;
          &:first-child {
            display: inline-block;
            font-size: 0.12rem;
            line-height: 0.12rem;
            font-weight: 500;
            color: #cee1f0;
          }
          &:nth-child(2) {
            margin-left: 0.15rem;
            width: 13px;
            height: 13px;
          }
        }
        &:nth-child(1) {
          span {
            &:nth-child(2) {
              border: 1px solid #89c34a;
              border-radius: 50%;
            }
          }
          .department-active {
            background: rgba(72, 97, 128, 0.6);
            border-radius: 3px;
            padding: 0.05rem 0.12rem;
          }
          .depart-circle-active {
            &:after {
              position: absolute;
              content: "";
              width: 0.07rem;
              height: 0.07rem;
              background: #89c34a;
              border-radius: 50%;
              top: 0.02rem;
              left: 0.02rem;
            }
          }
        }
        &:nth-child(2) {
          span {
            &:nth-child(2) {
              border: 1px solid #2980ff;
              border-radius: 50%;
            }
          }
          .department-active {
            background: rgba(72, 97, 128, 0.6);
            border-radius: 3px;
            padding: 0.05rem 0.12rem;
          }
          .depart-circle-active {
            &:after {
              position: absolute;
              content: "";
              width: 0.07rem;
              height: 0.07rem;
              background: #2980ff;
              border-radius: 50%;
              top: 0.02rem;
              left: 0.02rem;
            }
          }
        }
        &:nth-child(3) {
          span {
            &:nth-child(2) {
              border: 1px solid #eaa03f;
              border-radius: 50%;
            }
          }
          .department-active {
            background: rgba(72, 97, 128, 0.6);
            border-radius: 3px;
            padding: 0.05rem 0.12rem;
          }
          .depart-circle-active {
            &:after {
              position: absolute;
              content: "";
              width: 0.07rem;
              height: 0.07rem;
              background: #e6a33b;
              border-radius: 50%;
              top: 0.02rem;
              left: 0.02rem;
            }
          }
        }
        &:nth-child(4) {
          span {
            &:nth-child(2) {
              border: 1px solid #083295;
              border-radius: 50%;
            }
          }
          .department-active {
            background: rgba(72, 97, 128, 0.6);
            border-radius: 3px;
            padding: 0.05rem 0.12rem;
          }
          .depart-circle-active {
            &:after {
              position: absolute;
              content: "";
              width: 0.07rem;
              height: 0.07rem;
              background: #153f9f;
              border-radius: 50%;
              top: 0.02rem;
              left: 0.02rem;
            }
          }
        }
        &:nth-child(5) {
          span {
            &:nth-child(2) {
              border: 1px solid #083295;
              border-radius: 50%;
            }
          }
          .department-active {
            background: rgba(72, 97, 128, 0.6);
            border-radius: 3px;
            padding: 0.05rem 0.12rem;
          }
          .depart-circle-active {
            &:after {
              position: absolute;
              content: "";
              width: 0.07rem;
              height: 0.07rem;
              background: #153f9f;
              border-radius: 50%;
              top: 0.02rem;
              left: 0.02rem;
            }
          }
        }
        &:nth-child(6) {
          span {
            &:nth-child(2) {
              border: 1px solid #083295;
              border-radius: 50%;
            }
          }
          .department-active {
            background: rgba(72, 97, 128, 0.6);
            border-radius: 3px;
            padding: 0.05rem 0.12rem;
          }
          .depart-circle-active {
            &:after {
              position: absolute;
              content: "";
              width: 0.07rem;
              height: 0.07rem;
              background: #153f9f;
              border-radius: 50%;
              top: 0.02rem;
              left: 0.02rem;
            }
          }
        }
      }
    }
    .timeLine-text-line {
      margin-left: 0.1rem;
      width: calc(100% - 1.9rem);
      overflow: hidden;
      .line-one {
        height: 2px;
        background-color: rgba(255, 255, 255, 0.4);
        width: 100%;
        position: absolute;
        top: 0.36rem;
      }

      .line-scrool {
        height: 0.4rem;
        background: transparent;
        width: 100%;
        position: absolute;
      }
      ul {
        padding: 0;
      }
      .tttt {
        height: 0rem;
        cursor: move;
        position: relative;
        pointer-events: auto;
      }
      #timess {
        width: 100%;
        height: 0rem;
        margin-top: 1.36rem;
        font-size: 0.1rem;
        color: #6aa1cb;
        -moz-user-select: none;
        /*火狐*/
        -webkit-user-select: none;
        /*webkit浏览器*/
        -ms-user-select: none;
        /*IE10*/
        -khtml-user-select: none;
        /*早期浏览器*/
        user-select: none;
      }
      #timecs {
        height: 0.02rem;
        position: relative;
        cursor: pointer;
        top: 0rem;
      }
      #timecs1 {
        height: 0.02rem;
        position: relative;
        cursor: pointer;
        top: 0rem;
      }
      .dd12 {
        position: fixed;
        width: calc(100% - 6.8rem);
        overflow: hidden;
        height: 100%;
      }
      #timecs1 li {
        float: left;
        position: absolute;
        list-style: none;
        /*background: rgba(255, 255, 255, 0.4);*/
        /*height: 0.06rem;*/
        /*width: 0.01rem;*/
        top: 0.2rem;
        text-indent: -0.12rem;
      }

      #timecs li {
        float: left;
        position: absolute;
        list-style: none;
        /*background: rgba(255, 255, 255, 0.4);*/
        /*height: 0.06rem;*/
        /*width: 0.01rem;*/
        top: 0.2rem;
        text-indent: -0.12rem;
      }
      #timecs > .time1 {
        width: 0.12rem;
        height: 0.12rem;
        background: linear-gradient(
          0deg,
          rgba(0, 150, 81, 1),
          rgba(16, 162, 95, 1),
          rgba(0, 208, 112, 1)
        );
        border-radius: 50%;
        z-index: 10;
        position: absolute;
        z-index: 99999;
      }
      #timecs > .time2 {
        background: #89c34a;
      }
      #timecs > .time3 {
        background: #04b9ee;
      }
      #timecs > .time4 {
        background: #e6a33b;
      }
      #timecs > .time5 {
        background: #153f9f;
      }
      #timecs > .time6 {
        background: #e7dd6c;
      }
      #timecs > .time7 {
        background: #6897f6;
      }
      #timecs > .time8 {
        background: #4fbdb4;
      }
      #timecs > .time9 {
        background: #4c3b83;
      }
      #timecs > .time1-active {
        width: 0.14rem;
        height: 0.14rem;
        background: #00cfb9;
        border-radius: 50%;
        z-index: 9999991;
        position: absolute;
      }
      .time-active1 .time2 {
        background: #89c34a !important;
        top: 0rem;
      }
      .time-active2 .time3 {
        background: #04b9ee !important;
      }
      .time-active3 .time4 {
        background: #e6a33b !important;
      }
      .time-active4 .time5 {
        background: #153f9f !important;
      }
      .time-active5 .time6 {
        background: #e7dd6c !important;
        top: 0rem;
      }
      .time-active6 .time7 {
        background: #6897f6 !important;
      }
      .time-active7 .time8 {
        background: #4fbdb4 !important;
      }
      .time-active8 .time9 {
        background: #4fbdb4 !important;
      }
    }
  }
}
.ant-modal-task {
  top: 1.6rem !important;
  width: 5.63rem !important;
  .ant-modal-content {
    width: 5.63rem;
    min-height: 5.8rem;
    background-size: 100% 100%;
    background-image: url("../../assets/emergency/<EMAIL>");
    background-color: transparent !important;
  }
  .ant-modal-body {
    height: 100%;
    min-height: 5.8rem;
    padding: 0.35rem 0.24rem;
    .content {
      padding-left: 0.16rem;
      .title {
        width: 3.04rem;
        height: 0.36rem;
        background: linear-gradient(
          90deg,
          rgba(9, 70, 148, 0.6),
          rgba(38, 47, 78, 0.6)
        );
        font-size: 0.16rem;
        font-weight: 400;
        color: #fafdff;
        line-height: 0.36rem;
        padding-left: 0.2rem;
        border-left: 0.02rem solid #308ec1;
      }
      .close {
        img {
          position: absolute;
          right: 0.45rem;
          cursor: pointer;
          top: 0.5rem;
        }
      }
      .txt {
        height: 100%;
        width: 100%;
        padding: 0.3rem 0.5rem 0.3rem 0.3rem;
        .department-name {
          font-size: 0.18rem;
          font-weight: 500;
          color: #5abefe;
          line-height: 0.18rem;
          padding-left: 0.2rem;
          position: relative;
          &::before {
            content: "";
            position: absolute;
            width: 0.1rem;
            height: 0.1rem;
            left: 0;
            top: 0.04rem;
            background-color: #00cfb9;
            border-radius: 50%;
          }
        }
        .department-remark {
          padding-top: 0.2rem;
          font-size: 0.16rem;
          font-weight: 400;
          color: #dbefff;
          line-height: 0.24rem;
        }
        .department-time {
          display: flex;
          justify-content: space-between;
          padding-top: 0.2rem;
          span {
            color: #7294b0;
            font-size: 0.14rem;
          }
        }
        .department-image {
          margin-top: 0.25rem;
          height: 2.68rem;
          width: 100%;
          background-size: 100% 100%;
          background-image: url("../../assets/emergency/spjk.png");
          padding: 0.21rem 0.24rem 0.22rem 0.25rem;
          box-sizing: border-box;
          .img {
            width: 100%;
            height: 100%;
          }
        }
      }
    }
  }
  .ant-modal-close {
    display: none !important;
  }
  .ant-modal-footer {
    border: none !important;
  }
}
</style>
<template>
  <div class="container">
    <div
      class="container-bg"
      ref="containerBg"
      style="transition: all 2s"
      :style="{ backgroundImage: displayState ? 'url(' + bgImage + ')' : '' }"
    >
      <!-- 左侧部分 -->
      <section
        class="left-part"
        style="transition: all 1s"
        :style="{ opacity: displayState ? '1' : '0' }"
      >
        <div class="list">
          <div class="btn-list">
            <div
              class="btn"
              @click="handleClickTimeType(1)"
              :style="{
                backgroundImage: `url(${
                  timeType === 1 ? xuanzhong : wxuanzhong
                })`,
              }"
            >
              当前
            </div>
            <div
              class="btn"
              @click="handleClickTimeType(2)"
              :style="{
                backgroundImage: `url(${
                  timeType === 2 ? xuanzhong : wxuanzhong
                })`,
              }"
            >
              近七天
            </div>
          </div>
          <div
            v-for="(item, index) in list"
            :key="index"
            class="left-list"
            :style="{
              backgroundImage:
                'url(' + (active === index ? item.icon2 : item.icon1) + ')',
            }"
            @click="handleChangeType(index)"
          >
            <!-- <span>{{
              item.type === 0
                ? "全部"
                : item.type === 1
                ? "水环境"
                : item.type === 2
                ? "大气环境"
                : item.type === 3
                ? "污染源监管"
                : "任务调度"
            }}</span>
            <span>{{ enentTypeList[index] }}</span> -->
            <span>{{
              enentTypeList.find((it) => it.id == index)
                ? enentTypeList.find((it) => it.id == index).name
                : ""
            }}</span>
            <span>{{
              enentTypeList.find((it) => it.id == index)
                ? enentTypeList.find((it) => it.id == index).total
                : 0
            }}</span>
          </div>
        </div>
        <!--        <div class="list-remark">注：一周任务</div>-->
      </section>
      <!-- 车辆详情跳转 -->
      <div class="law-vehicle" @click="toDetail('/vehicleManage')">
        <div class="within-vehicle Rotation"></div>
        <div class="vehicle-text">环保环</div>
        <div class="vehicle-text">卫车辆</div>
      </div>
      <div class="law-vehicle-2" @click="toDetail('/patrolKanban')">
        <div class="within-vehicle Rotation"></div>
        <div class="vehicle-text">巡岗</div>
        <div class="vehicle-text">看板</div>
      </div>
      <!-- 右侧部分 告警详情 -->
      <section
        class="right-part-copy"
        :class="{ 'right-part-to-left': isToRight ? 'right-part-to-left' : '' }"
        style="transition: all 1s"
        :style="{ opacity: displayState ? '1' : '0' }"
      >
        <!-- <div class="select-area depart-select">
          <a-select
            v-model="defaultpartMent"
            style="width: 1rem"
            class="select-main"
            @change="changeDepartment"
          >
            <a-icon
              slot="suffixIcon"
              type="caret-down"
              style="
                color: rgba(0, 234, 255, 1);
                width: 0.23rem;
                height: 0.11rem;
              "
            />
            <a-select-option
              :value="item.departmentId"
              v-for="(item, index) in departMentList"
              :key="index"
              class="select-option"
            >
              {{ item.departmentName }}</a-select-option
            >
          </a-select>
        </div> -->
        <div class="right-part-three">
          <div
            class="title"
            style="display: flex; justify-content: space-between"
          >
            <span>任务详情</span>
            <span
              v-if="taskDetail.typeId == 4"
              style="
                font-size: 16px;
                line-height: 20px;
                font-family: Source Han Sans CN;
                font-weight: 400;
                color: #3adbff;
                cursor: pointer;
              "
              @click="taskVisible = true"
              >查看更多&gt;&gt;</span
            >
          </div>
          <div class="sub-title">
            <img src="@/assets/biaoti.png" alt />
          </div>
          <div class="alram-detail">
            <div class="top">
              <div class="list">
                <span>任务标题：</span>
                <span
                  :title="
                    taskDetail.typeId == 4
                      ? taskDetail.title
                      : taskDetail.content
                  "
                >
                  {{
                    taskDetail.typeId == 4
                      ? taskDetail.title
                      : taskDetail.content
                  }}</span
                >
              </div>
              <div class="list">
                <span>任务类型：</span>
                <span>{{ taskDetail.typeName }}</span>
              </div>
              <div class="list">
                <span>创建时间：</span>
                <span>{{ taskDetail.createTime }}</span>
              </div>
              <div class="list">
                <span>地址：</span>
                <span :title="taskDetail.address">{{
                  taskDetail.address
                }}</span>
              </div>
            </div>
            <div class="bottom">
              <div class="des">详情:</div>
              <div v-show="taskAlarmList.length" class="table">
                <div class="table-header">
                  <span>指标项</span>
                  <span>超标项</span>
                  <span>标准</span>
                  <span>单位</span>
                </div>
                <div class="list-area">
                  <div
                    v-for="(item, index) in taskAlarmList"
                    :key="index"
                    class="table-list"
                  >
                    <span>{{ item.alarmItemName }}</span>
                    <span class="active">{{ item.alarmValue }}</span>
                    <span>{{
                      item.alarmValueType == 0
                        ? ![null, undefined].includes(item.alarmThreshold)
                          ? item.alarmThreshold
                          : ""
                        : item.alarmValueType == 1
                        ? (![null, undefined].includes(item.alarmMinValue)
                            ? ">" + item.alarmMinValue
                            : "") +
                          (![null, undefined].includes(item.alarmMaxValue)
                            ? ",<" + item.alarmMaxValue
                            : "")
                        : item.alarmValueType == 2
                        ? (![null, undefined].includes(item.alarmMinValue)
                            ? "<" + item.alarmMinValue
                            : "") +
                          (![null, undefined].includes(item.alarmMaxValue)
                            ? ",>" + item.alarmMaxValue
                            : "")
                        : ""
                    }}</span>
                    <span>{{ item.alarmItemUnit || "--" }}</span>
                  </div>
                </div>
              </div>
              <div v-show="taskDetail.typeId == 4" class="deatil">
                {{ taskDetail.content }}
              </div>
              <div
                v-show="taskDetail.typeId == 4 ? false : !taskAlarmList.length"
                class="no-datas"
              >
                暂无数据
              </div>
            </div>
          </div>
          <!--          <swiper-->
          <!--                  v-if="feedbackList.length"-->
          <!--                  :options="-->
          <!--              feedbackList.length > 1 ? swiperRebackOption : swiperRebackOption1-->
          <!--            "-->
          <!--                  class="reback"-->
          <!--          >-->
          <!--            <swiper-slide-->
          <!--                    class="reback-list"-->
          <!--                    v-for="(item, index) in feedbackList"-->
          <!--                    :key="index"-->
          <!--            >-->
          <!--              <div class="left">-->
          <!--                <span>{{ item.time2 }}</span>-->
          <!--                <span>{{ item.time1 }}</span>-->
          <!--              </div>-->
          <!--              &lt;!&ndash;                      :class="{'after': index !==2 ? 'after' : ''}"&ndash;&gt;-->
          <!--              <div-->
          <!--                      class="center"-->
          <!--                      :style="{-->
          <!--                  visibility: feedbackList.length == 1 ? 'hidden' : ''-->
          <!--                }"-->
          <!--              >-->
          <!--                <div class="after"></div>-->
          <!--              </div>-->
          <!--              <div class="right">-->
          <!--                <div class="right-bg">-->
          <!--                  <div class="title" style="display: flex; align-items: center">-->
          <!--                    <img-->
          <!--                            :src="-->
          <!--                        item.type == 1-->
          <!--                          ? zhzfjIcon-->
          <!--                          : item.type == 2-->
          <!--                          ? zjjIcon-->
          <!--                          : item.type == 3-->
          <!--                          ? sthjjIcon-->
          <!--                          : shwIcon-->
          <!--                      "-->
          <!--                            alt=""-->
          <!--                            style="width: 24px; height: 24px; margin-right: 10px"-->
          <!--                    />{{ item.departmentName ? item.departmentName : '&#45;&#45;' }}-->
          <!--                  </div>-->
          <!--                  <div class="content" :title="item.remark">-->
          <!--                    任务反馈：{{ item.remark ? item.remark : '&#45;&#45;' }}-->
          <!--                  </div>-->
          <!--                  <div class="check" v-if="false">查看图片</div>-->
          <!--                </div>-->
          <!--              </div>-->
          <!--            </swiper-slide>-->
          <!--          </swiper>-->
          <!--          <div v-else class="no-feed">暂无反馈</div>-->
        </div>
        <div class="right-part-two" style="display: none">
          <div
            class="title"
            style="display: flex; justify-content: space-between"
          >
            <span>执行部门</span>
            <span
              style="
                font-size: 16px;
                line-height: 20px;
                font-family: Source Han Sans CN;
                font-weight: 400;
                color: #3adbff;
                cursor: pointer;
              "
              @click="processVisible = true"
              >处置流程&gt;&gt;</span
            >
          </div>
          <div class="sub-title">
            <img src="@/assets/biaoti.png" alt />
          </div>
          <div class="executive-department">
            <div class="alarm-title" v-if="executiveDepartmentList2.length">
              <span>{{ executiveDepartmentList2[0].taskNodeName }}</span>
            </div>
            <swiper
              v-if="executiveDepartmentList1.length"
              :options="
                executiveDepartmentList1.length > 2
                  ? swiperRebackOptions
                  : swiperRebackOptionss
              "
              class="alarm-type-area"
            >
              <swiper-slide
                v-for="(item, index) in executiveDepartmentList1"
                :key="index"
                class="single-alarm"
              >
                <div class="alarm-list">
                  <div
                    :class="{
                      'complteted-icon':
                        item[0].completeStatus == 1 ? 'complteted-icon' : '',
                      'complteted-icons':
                        item[0].completeStatus == 2 ? 'complteted-icons' : '',
                    }"
                  >
                    <img
                      :src="
                        item[0].completeStatus == 0
                          ? jxIcon
                          : item[0].completeStatus == 1
                          ? wcIcon
                          : tdIcon
                      "
                      alt=""
                    /><span>{{ item[0].taskNodeName }}</span>
                  </div>
                  <div
                    :class="{
                      'complteted-icon':
                        item[1].completeStatus == 1 ? 'complteted-icon' : '',
                      'complteted-icons':
                        item[1].completeStatus == 2 ? 'complteted-icons' : '',
                    }"
                    v-if="item[1]"
                  >
                    <img
                      :src="
                        item[1].completeStatus == 0
                          ? jxIcon
                          : item[1].completeStatus == 1
                          ? wcIcon
                          : tdIcon
                      "
                      alt=""
                    /><span>{{ item[1].taskNodeName }}</span>
                  </div>
                </div>
              </swiper-slide>
            </swiper>
            <!-- <div class="main-bg"> -->
            <!-- <div class="alarm-type" v-if="executiveDepartmentList2.length">{{executiveDepartmentList2[0].taskNodeName}}</div>
              <div class="animation-area">
                <div class="alarm-node" v-show="executiveDepartmentList1.length" :style="{
                backgroundImage:
                  'url(' +
                  (item.completeStatus
                    ? defaultComplateImage
                    : defaultUnComplateImage) +
                  ')'
              }"
              :class="'bg'+(index+1)"  v-for="(item, index) in executiveDepartmentList1" :key="index"> {{ item.taskNodeName }}</div>
                <svg
                  class="custom-svg svg1"
                  xmlns="http://www.w3.org/2000/svg"
                  xmlns:xlink="http://www.w3.org/1999/xlink"
                  width="320px" height="131.5px">
                  <path fill-rule="evenodd"  stroke="rgb(40, 101, 158)" stroke-width="1px" stroke-linecap="butt" stroke-linejoin="miter" fill-opacity="0.671" opacity="0.651" fill="rgb(6, 13, 46)"
                  d="M154.797,0.500 C239.688,0.500 308.507,29.497 308.507,65.266 C308.507,101.035 239.688,130.031 154.797,130.031 C69.905,130.031 1.087,101.035 1.087,65.266 C1.087,29.497 69.905,0.500 154.797,0.500 Z"/>
                </svg>
                <svg
                  class="custom-svg svg2"
                  xmlns="http://www.w3.org/2000/svg"
                  xmlns:xlink="http://www.w3.org/1999/xlink"
                  width="320px" height="131.5px">
                  <path fill-rule="evenodd"  stroke="rgb(40, 101, 158)" stroke-width="1px" stroke-linecap="butt" stroke-linejoin="miter" fill-opacity="0.671" opacity="0.651" fill="rgb(6, 13, 46)"
                  d="M1.087,65.266 C1.087,29.497 69.905,0.500 154.797,0.500 C239.688,0.500 308.507,29.497 308.507,65.266 C308.507,101.035 239.688,130.031 154.797,130.031 C69.905,130.031 1.087,101.035 1.087,65.266 Z"/>
                </svg>
                <svg
                  class="custom-svg svg3"
                  xmlns="http://www.w3.org/2000/svg"
                  xmlns:xlink="http://www.w3.org/1999/xlink"
                  width="320px" height="131.5px">
                  <path fill-rule="evenodd"  stroke="rgb(40, 101, 158)" stroke-width="1px" stroke-linecap="butt" stroke-linejoin="miter" fill-opacity="0.671" opacity="0.651" fill="rgb(6, 13, 46)"
                  d="M154.797,130.031 C69.905,130.031 1.087,101.035 1.087,65.266 C1.087,29.497 69.905,0.500 154.797,0.500 C239.688,0.500 308.507,29.497 308.507,65.266 C308.507,101.035 239.688,130.031 154.797,130.031 Z"/>
                </svg>
                <svg
                  class="custom-svg svg4"
                  xmlns="http://www.w3.org/2000/svg"
                  xmlns:xlink="http://www.w3.org/1999/xlink"
                  width="320px" height="131.5px">
                  <path fill-rule="evenodd"  stroke="rgb(40, 101, 158)" stroke-width="1px" stroke-linecap="butt" stroke-linejoin="miter" fill-opacity="0.671" opacity="0.651" fill="rgb(6, 13, 46)"
                  d="M308.507,65.266 C308.507,101.035 239.688,130.031 154.797,130.031 C69.905,130.031 1.087,101.035 1.087,65.266 C1.087,29.497 69.905,0.500 154.797,0.500 C239.688,0.500 308.507,29.497 308.507,65.266 Z"/>
                </svg>
              </div> -->
            <!-- </div> -->
            <!-- <div
              class="node"
              v-for="(item, index) in executiveDepartmentList1"
              :key="index"
              :style="{
                backgroundImage:
                  'url(' +
                  (item.completeStatus
                    ? defaultComplateImage
                    : defaultUnComplateImage) +
                  ')'
              }"
              :class="{
                node2: executiveDepartmentList1.length == 2 ? 'node2' : ''
              }"
            >
              {{ item.taskNodeName }}
            </div>
            <div class="major-ndoe" v-if="executiveDepartmentList2.length">
              {{ executiveDepartmentList2[0].taskNodeName }}
            </div> -->
            <!-- <div id="1" v-show="executiveDepartmentList1.length == 3">
              <lottie
                :id="'quanke' + new Date().getTime() + 1"
                :options="defaultOptions"
                :width="370"
                :height="310"
              />
            </div>
            <div id="2" v-show="executiveDepartmentList1.length == 2">
              <lottie
                :id="'quanke' + new Date().getTime() + 2"
                :options="defaultOptions1"
                :width="370"
                :height="310"
              />
            </div> -->
          </div>
        </div>
        <div class="right-part-one" style="margin-top: 40px">
          <div
            class="title"
            style="display: flex; justify-content: space-between"
          >
            <span>执行反馈</span>
            <span
              style="
                font-size: 16px;
                line-height: 20px;
                font-family: Source Han Sans CN;
                font-weight: 400;
                color: #3adbff;
                cursor: pointer;
              "
              @click="processVisible = true"
              >处置流程&gt;&gt;</span
            >
          </div>
          <div class="sub-title">
            <img src="@/assets/biaoti.png" alt />
          </div>
          <div class="task-scoll-box">
            <template v-if="excutorArr.length">
              <taskFeedbackItem
                v-for="(obj, ind) in excutorArr"
                :key="ind + 'tfbi'"
                :hiddenbtom="ind == excutorArr.length - 1"
                :excutor="obj"
              ></taskFeedbackItem>
            </template>
            <template v-else>
              <div
                style="
                  width: 100%;
                  text-align: center;
                  height: 200px;
                  line-height: 200px;
                "
              >
                暂无执行反馈
              </div>
            </template>
          </div>
        </div>
        <!-- <div class="right-part-one">
          <div class="title">
            <span>监测数据</span>
          </div>
          <div class="sub-title">
            <img src="@/assets/biaoti.png" alt />
          </div>
          <div v-if="pollutNameList.length" class="button-list">
            <span
                    v-for="(item, index) in pollutNameList"
                    :key="index"
                    :class="{ active: activeIndex == index ? 'active' : '' }"
                    :style="{ 'flex-grow': item.length > 4 ? 2 : 1 }"
                    @click="handleClick(index)"
            >{{ item }}</span
            >
          </div>
          <real-time-monitor
                  :id="'realTimeMonitor' + new Date().getTime()"
                  :width="'4rem'"
                  :height="'1.75rem'"
                  :propData="realTimeData"
                  :smooth="true"
          />
        </div> -->
      </section>
      <!-- 右侧部分 告警统计 -->
      <section
        class="right-part"
        :class="{
          'right-part-to-right ': isToRight ? 'right-part-to-right ' : '',
        }"
        style="transition: all 1s"
        :style="{ opacity: displayState ? '1' : '0' }"
      >
        <!-- <div class="select-area depart-select">
          <a-select
            v-model="defaultpartMent"
            style="width: 1rem"
            class="select-main"
            @change="changeDepartment"
          >
            <a-icon
              slot="suffixIcon"
              type="caret-down"
              style="
                color: rgba(0, 234, 255, 1);
                width: 0.23rem;
                height: 0.11rem;
              "
            />
            <a-select-option
              :value="item.departmentId"
              v-for="(item, index) in departMentList"
              :key="index"
              class="select-option"
            >
              {{ item.departmentName }}</a-select-option
            >
          </a-select>
        </div> -->
        <div class="right-part-one">
          <div class="title">
            <span>任务完成情况</span>
          </div>
          <div class="sub-title">
            <img src="@/assets/biaoti.png" alt />
          </div>
          <taskProcessing
            :id="'taskProcessing1' + new Date().getTime()"
            :width="'4rem'"
            :height="'2.2rem'"
            :propData="completeData"
            :smooth="true"
          />
          <!--          <mission-accomplished-->
          <!--            :id="'accomplished' + new Date().getTime()"-->
          <!--            :width="'4rem'"-->
          <!--            :height="'1.75rem'"-->
          <!--            :propData="completeData"-->
          <!--            :smooth="true"-->
          <!--          />-->
        </div>
        <div class="right-part-two">
          <div
            class="title"
            style="
              display: flex;
              justify-content: space-between;
              align-items: flex-end;
            "
          >
            <span>任务分类统计</span>
            <!--            <span style="color: #86c6ff; font-size: 0.14rem"-->
            <!--              >数据统计：一周</span-->
            <!--            >-->
          </div>
          <div class="sub-title">
            <img src="@/assets/biaoti.png" alt />
          </div>
          <task-type
            :id="'taskType' + new Date().getTime()"
            :width="'4rem'"
            :height="'1.75rem'"
            :propData="taskTypeData"
            :smooth="true"
          />
        </div>
        <div class="right-part-three">
          <div class="title">
            <span>任务执行率</span>
          </div>
          <div class="sub-title">
            <img src="@/assets/biaoti.png" alt />
          </div>
          <!-- <dayTask
            :id="'dayTask' + new Date().getTime()"
            :width="'4rem'"
            :height="'2.2rem'"
            :propData="taskHandleData"
            :smooth="true"
          /> -->
          <taskExecutionRateLine
            :id="'dayTask' + new Date().getTime()"
            :width="'4rem'"
            :height="'2.2rem'"
            :propData="executionRate"
            :smooth="true"
          ></taskExecutionRateLine>
        </div>
        <!-- <div></div> -->
      </section>
      <!-- 底部任务进度 -->
      <section
        :class="{
          'bottom-part-to-bottom': isToRight ? 'bottom-part-to-bottom' : '',
        }"
        class="bottom-part"
        style="transition: all 1s"
        v-show="show"
        v-if="taskData.length"
        :style="{ opacity: displayState ? '1' : '0' }"
      >
        <section class="procress">
          <bottomTable
            :taskData="taskData"
            @handleActiveMaker="handleActiveMaker"
          ></bottomTable>
        </section>
      </section>
      <!--底部echarts -->
      <section
        :class="{ 'bottom-part-to-top': isToRight ? 'bottom-part-to-top' : '' }"
        class="bottom-part-copy"
        style="transition: all 1s"
        v-show="!show"
        :style="{ opacity: displayState ? '1' : '0' }"
      >
        <section class="procress">
          <bottomChart
            v-if="!show"
            :pollutNameList="pollutNameList"
            :pollutList="pollutList"
          ></bottomChart>
        </section>
      </section>
      <!-- 顶部搜索 -->
      <section class="enterprise-search">
        <div>
          <input
            type="text"
            placeholder="请输入关键词搜索"
            v-model="enterpriseSearch"
            @keyup.enter="enterpriseSearchBtn"
          />
          <a-icon
            type="close-circle"
            style="font-size: 0.2rem; cursor: pointer; margin-right: 0.15rem"
            v-if="enterpriseSearch != ''"
            @click="clearSearch"
          />
        </div>
        <div>
          <img src="@/assets/search.png" alt="" @click="enterpriseSearchBtn" />
        </div>
      </section>
      <a-modal
        :visible="visibles"
        class="ant-modal-task"
        :footer="null"
        :destroyOnClose="true"
        @cancel="handleClose"
      >
        <div class="content">
          <div class="close" @click="handleClose">
            <img src="../../assets/guanbi.png" alt="" class="close" />
          </div>
          <div class="title">部门反馈</div>
          <div class="txt">
            <div class="department-name">{{ remarkDetail.departmentName }}</div>
            <div class="department-remark">
              {{ remarkDetail.remark || remarkDetail.content }}
            </div>
            <div class="department-time">
              <span
                >{{ remarkDetail.departmentName }}-{{
                  remarkDetail.userName
                }}</span
              >
              <span>{{
                remarkDetail.time
                  ? remarkDetail.time.substr(0, remarkDetail.time.length - 3)
                  : "--"
              }}</span>
            </div>
            <div
              class="department-image"
              v-show="
                remarkDetail.taskAnnexList && remarkDetail.taskAnnexList.length
              "
            >
              <img
                v-show="indexs == 0"
                class="img"
                v-for="(item, indexs) in remarkDetail.taskAnnexList"
                :key="indexs"
                :src="item.annexUrl"
                :preview="'item' + 0"
              />
            </div>
          </div>
        </div>
      </a-modal>
      <!-- 任务下发 -->
      <a-modal
        :visible.sync="visible"
        class="ant-modal-alarm"
        :class="{
          'ant-modal-alarmss':
            taskDetail.dispatch || taskDetail.myCompleteStatus == 0
              ? 'ant-modal-alarmss'
              : '',
        }"
        @cancel="handleCancel"
      >
        <div class="title">任务下发</div>
        <div class="content">
          <div
            v-if="taskDetail.dispatch || taskDetail.myCompleteStatus == 0"
            class="list"
          >
            <span>选择类型：</span>
            <div class="radio-group">
              <a-radio-group v-model="type">
                <a-radio :value="1" v-if="taskDetail.dispatch && false">
                  执行
                </a-radio>
                <a-radio :value="2" v-if="taskDetail.myCompleteStatus == 0">
                  处置
                </a-radio>
              </a-radio-group>
            </div>
          </div>
          <div
            v-if="taskDetail.dispatch || taskDetail.myCompleteStatus == 0"
            class="list date-dispatch"
            :class="{ textarea: type === 2 ? 'textarea' : '' }"
          >
            <span>{{ type === 1 ? "新增执行：" : "任务处置：" }}</span>
            <span
              v-if="type === 2"
              style="display: inline-block; width: calc(100% - 0.8rem)"
            >
              <a-textarea
                class="select-dispatch"
                style="color: white"
                v-model="text"
                placeholder="请输入任务处置详情"
                :maxLength="250"
                :auto-size="{ minRows: 3, maxRows: 5 }"
              />
            </span>
            <span
              v-if="type === 1"
              style="display: inline-block; width: calc(100% - 0.8rem)"
            >
              <a-cascader
                :options="displayDepartmentList"
                placeholder="选择执行人"
                :fieldNames="fieldNames"
                @change="changeList"
                class="list date-dispatch"
              />
              <!-- <a-select
                    v-model="planId"
                    class="select-area select-plan"
                    size="large"
            >
              <a-icon
                      slot="suffixIcon"
                      type="caret-down"
                      style="color: #41abf9; width: 0.14rem; height: 0.07rem"
              />
              <a-select-option
                      :value="item.planId"
                      v-for="(item, index) in planList"
                      :key="index"
                      class="select-option"
              >
                {{ item.planName }}</a-select-option
              >
            </a-select> -->
            </span>
          </div>
          <div
            v-if="
              type === 1 &&
                (taskDetail.dispatch || taskDetail.myCompleteStatus == 0)
            "
            class="list date-dispatch"
            style="align-items: flex-start"
          >
            <span>内容描述：</span>
            <span style="display: inline-block; width: calc(100% - 0.8rem)">
              <a-textarea
                class="select-dispatch"
                style="color: white"
                v-model="text"
                placeholder="请输入任务处置详情"
                :maxLength="250"
                :auto-size="{ minRows: 3, maxRows: 5 }"
              />
            </span>
          </div>
          <div
            v-if="taskDetail.dispatch || taskDetail.myCompleteStatus == 0"
            class="footer"
          >
            <div class="cancel" @click="handleCancel">取消</div>
            <div class="confirm" @click="handDispatch">
              {{ type == 1 ? "派遣" : "处置" }}
            </div>
          </div>
        </div>
      </a-modal>
    </div>
    <!-- 处置流程 -->
    <a-modal
      :visible.sync="processVisible"
      class="ant-modal-process"
      :footer="null"
      :maskClosable="true"
      :centered="true"
    >
      <div class="title">
        <span>处置流程</span>
        <div class="close" @click="processVisible = false">
          <img src="../../assets/guanbi.png" alt="" class="close" />
        </div>
      </div>
      <div class="content">
        <orgTree
          v-if="processVisible"
          :orgTreeList="orgTreeList"
          :typeId="typeId"
        ></orgTree>
      </div>
    </a-modal>
    <!-- 任务详情 -->
    <a-modal
      :visible.sync="taskVisible"
      class="ant-modal-taskDetail"
      :footer="null"
      :maskClosable="true"
      :centered="true"
    >
      <div class="title">
        <span>任务详情</span>
        <div class="close" @click="taskVisible = false">
          <img src="../../assets/guanbi.png" alt="" class="close" />
        </div>
      </div>
      <div class="content">
        <div class="list">
          <span class="list-label">任务标题：</span>
          <span
            class="value"
            :title="
              taskDetail.typeId == 4 ? taskDetail.title : taskDetail.content
            "
          >
            {{
              taskDetail.typeId == 4 ? taskDetail.title : taskDetail.content
            }}</span
          >
        </div>
        <div class="list">
          <span class="list-label">事件类型：</span>
          <span class="value">{{ taskDetail.typeName }}</span>
        </div>
        <div class="list">
          <span class="list-label">创建时间：</span>
          <span class="value">{{ taskDetail.createTime }}</span>
        </div>
        <div class="list">
          <span class="list-label">地址：</span>
          <span class="value" :title="taskDetail.address">{{
            taskDetail.address
          }}</span>
        </div>
        <div class="list">
          <div class="list-label">详情：</div>
          <div class="value">
            <template v-if="taskDetail.typeId == 4" class="deatil">
              {{ taskDetail.content }}
            </template>
            <template
              v-if="taskDetail.typeId == 4 ? false : !taskAlarmList.length"
              class="no-datas"
            >
              暂无数据
            </template>
          </div>
        </div>
        <div class="img-list" v-if="imgList.length">
          <ComImage
            v-for="(pic, indexs) in imgList"
            style="width: 379px"
            v-show="indexs === 0"
            :key="indexs"
            :src="pic"
            fit="cover"
            lazy
            :preview-src-list="imgList"
          />
          <!-- <el-carousel
            height="248"
            :autoplay="false"
            arrow="always"
            v-if="imgList.length > 1"
          >
            <el-carousel-item
              style="height: 228px"
              v-for="item in imgList"
              :key="item"
            >
              <el-image
                :src="item"
                fit="contain"
                style="width: 379px"
              ></el-image>
            </el-carousel-item>
          </el-carousel>
          <el-image
            v-for="item in imgList"
            :key="item"
            :src="item"
            fit="contain"
            style="width: 379px"
          ></el-image> -->
        </div>
      </div>
    </a-modal>
    <!-- 地图蒙层 -->
    <section class="center-map">
      <center-map
        :mapMarkerList="taskData"
        :activeIndex="active"
        :taskIndex="taskIndex"
        @markerClick="markersClick"
        :currObj="currObj"
        @change="changeMarker"
      />
      <section class="quanpin" @click="fullScreen">
        <img src="@/assets/quanping.png" alt />
      </section>
    </section>
  </div>
</template>

<script>
import lottie from "@/components/lottie";
import * as animationData from "../../../public/images/data";
import * as animationData1 from "../../../public/images1/data";
import centerMap from "./map";
import {
  Icon,
  Select,
  Modal,
  Radio,
  Input,
  DatePicker,
  message,
  Cascader,
} from "ant-design-vue";
import realTimeMonitor from "@/components/Charts/realTimeMonitors";
import missionAccomplished from "@/components/Charts/missionAccomplished";
import taskProcessing from "@/components/Charts/taskProcessing";
import dayTask from "@/components/Charts/dayTask";
import taskType from "@/components/Charts/taskType";
import taskExecutionRateLine from "@/components/Charts/taskExecutionRateLine.vue";
import bottomTable from "./components/bottom-table/index.vue";
import bottomChart from "./components/bottom-chart/index";
import orgTree from "./components/org-tree/index.vue";
import taskFeedbackItem from "./components/taskFeedbackItem/index.vue";
import { Swiper, SwiperSlide } from "vue-awesome-swiper";
import {
  getDepartment,
  getPersonList,
  getTopTaskExecutorList,
} from "@/api/login";
import { socketUrl2 } from "../../utils";
import moment from "moment";
import jq from "jquery";
import { clearInterval, setInterval } from "timers";
import anime from "animejs/lib/anime.es.js";
import { selectTypeStatistic } from "@/api/homeTable";
import ComImage from "@/components/image/src/main.vue";
export default {
  name: "index1",
  components: {
    centerMap,
    AIcon: Icon,
    ASelect: Select,
    ASelectOption: Select.Option,
    realTimeMonitor,
    Swiper,
    SwiperSlide,
    AModal: Modal,
    ARadio: Radio,
    ARadioGroup: Radio.Group,
    ATextarea: Input.TextArea,
    missionAccomplished,
    taskProcessing,
    dayTask,
    taskType,
    lottie,
    ADatePicker: DatePicker,
    ACascader: Cascader,
    taskExecutionRateLine,
    bottomTable,
    taskFeedbackItem,
    bottomChart,
    orgTree,
    ComImage,
  },
  data() {
    const _this = this;
    return {
      processVisible: false,
      excutorArr: [],
      orgTreeList: [],
      typeId: 0,
      xuanzhong: require("@/assets/department/<EMAIL>"),
      wxuanzhong: require("@/assets/department/<EMAIL>"),
      sthjjIcon: require("../../assets/<EMAIL>"),
      zhzfjIcon: require("../../assets/<EMAIL>"),
      zjjIcon: require("../../assets/<EMAIL>"),
      shwIcon: require("../../assets/<EMAIL>"),
      jxIcon: require("../../assets/task/<EMAIL>"),
      tdIcon: require("../../assets/task/<EMAIL>"),
      wcIcon: require("../../assets/task/<EMAIL>"),
      defaultComplateImage: require("../../assets/task/<EMAIL>"),
      defaultUnComplateImage: require("../../assets/task/<EMAIL>"),
      defaultOptions: { animationData: animationData },
      defaultOptions1: { animationData: animationData1 },
      bgImage: require("../../assets/department/<EMAIL>"),
      swiperOption: {
        direction: "vertical",
        slidesPerView: 4,
        slidesPerGroup: 1,
        loop: true,
        autoplay: {
          delay: 2800,
          disableOnInteraction: false,
        },
        on: {
          click: function(e) {
            _this.currObj = JSON.parse(e.target.getAttribute("data-href"));
          },
        },
      },
      swiperOption1: {
        direction: "vertical",
        slidesPerView: 4,
        slidesPerGroup: 1,
        loop: false,
        autoplay: {
          delay: 2800,
          disableOnInteraction: false,
        },
        on: {
          click: function(e) {
            _this.currObj = JSON.parse(e.target.getAttribute("data-href"));
          },
        },
      },
      swiperRebackOption: {
        direction: "vertical",
        slidesPerView: 2,
        slidesPerGroup: 1,
        loop: true,
        autoplay: {
          delay: 2800,
          disableOnInteraction: false,
        },
      },
      swiperRebackOptions: {
        direction: "vertical",
        slidesPerView: 2,
        slidesPerGroup: 1,
        loop: false,
        autoplay: {
          delay: 2800,
          disableOnInteraction: false,
        },
      },
      swiperRebackOptionss: {
        direction: "vertical",
        slidesPerView: 2,
        slidesPerGroup: 1,
        loop: false,
        autoplay: false,
      },
      swiperRebackOption1: {
        direction: "vertical",
        slidesPerView: 2,
        slidesPerGroup: 1,
        loop: false,
        autoplay: {
          delay: 2800,
          disableOnInteraction: false,
        },
      },
      statusIcon1: require("../../assets/department/<EMAIL>"),
      statusIcon2: require("../../assets/department/<EMAIL>"),
      active: 0,
      visible: false,
      taskDetail: {},
      type: 1,
      initIndex: 0,
      planId: undefined,
      isToRight: false,
      text: "",
      moment,
      list: [
        {
          type: 0,
          icon1: require("../../assets/department/<EMAIL>"),
          icon2: require("../../assets/department/<EMAIL>"),
        },
        {
          type: 1,
          icon1: require("../../assets/department/<EMAIL>"),
          icon2: require("../../assets/department/<EMAIL>"),
        },
        {
          type: 2,
          icon1: require("../../assets/department/<EMAIL>"),
          icon2: require("../../assets/department/<EMAIL>"),
        },
        {
          type: 3,
          icon1: require("../../assets/department/<EMAIL>"),
          icon2: require("../../assets/department/<EMAIL>"),
        },
        {
          type: 4,
          icon1: require("../../assets/department/<EMAIL>"),
          icon2: require("../../assets/department/<EMAIL>"),
        },
      ],
      currObj: {},
      taskData: [],
      taskIndex: -1,
      enentTypeList: [0, 0, 0, 0, 0],
      defaultpartMent: undefined,
      feedbackList: [],
      departMentList: [],
      executiveDepartmentList1: [],
      executiveDepartmentList2: [],
      realTimeData: {
        bottomList: [],
        dataList: [],
        standard: 0,
        max: 0,
        unit: "",
        name: "",
      },
      pollutNameList: [],
      pollutList: [],
      planList: [],
      taskAlarmList: [],
      taskHandleData: {
        bottomList: [],
        dataList: [],
      },
      activeIndex: 0,
      completeData: {
        bottomList: [],
        dataList: [],
        dataList1: [],
        dataList2: [],
      },
      executionRate: {
        bottomList: [],
        dataList: [],
        dataList1: [],
        dataList2: [],
        dataList3: [],
      },
      taskTypeData: {
        bottomList: [],
        dataList: [],
      },
      rebackList: [],
      socket: null,
      displayState: true,
      show: true,
      isFirst: false,
      endTime: undefined,
      userId: undefined,
      departmentId: undefined,
      taskDetailState: false,
      replyContent: "",
      creatTime: "",
      departmentList: [],
      departmentStatus: {
        st: 1,
        zf: 1,
        ns: 1,
        jj: 1,
      },
      task1NodeIndex: [],
      task2NodeIndex: [],
      task3NodeIndex: [],
      task4NodeIndex: [],
      startTimes: null,
      endTimes: null,
      nextTime: 15,
      fetchTimer: null,
      next: 0,
      totalLength: 0,
      totalDistance: [],
      remarkDetail: {},
      visibles: false,
      taskId: "",
      enterpriseSearch: "",
      imageIcon: require("../../assets/task/<EMAIL>"),
      scrollTop: 0,
      displayDepartmentList: [],
      fieldNames: {
        emitPath: false,
        multiple: false,
        expandTrigger: "hover",
        value: "needsData",
        children: "departmentAccountList",
        label: "departmentName",
        accountId: "",
        disabled: "disabled",
        depId: "",
      },
      departmentType: undefined,
      heartbeatTimer: null,
      timeType: 1,
      taskVisible: false,
      imgList: [],
    };
  },
  created() {
    window.taskDetails = this.taskDetails;
    window._that = this;
  },
  mounted() {
    const UserInfor = localStorage.getItem("UserInfor");
    this.userId = UserInfor ? JSON.parse(UserInfor).user.userId : undefined;
    this.departmentId = UserInfor
      ? JSON.parse(UserInfor).department.departmentId
      : undefined;
    this.departmentType = UserInfor
      ? JSON.parse(UserInfor).department.departmentType
      : undefined;
    this.getDepartment();
    this.getPersonList();
    // window.addEventListener("scroll",this.handScroll,true);
    this.getSelectTypeStatistic();
  },
  methods: {
    changeList(e) {
      // console.log(e, 123)
    },
    getSelectTypeStatistic() {
      selectTypeStatistic(4).then((res) => {
        let total = 0;
        res.data.data.forEach((item) => {
          total += item.total;
        });
        this.taskTypeData = {
          bottomList: [],
          total: total,
          dataList: res.data.data.map((item) => {
            item.value = item.total;
            return item;
          }),
        };
        this.taskTypeData.dataList = this.taskTypeData.dataList.sort(
          (a, b) => b.value - a.value
        );
      });
    },
    handleActiveMaker(data) {
      this.taskId = data.taskId;
      this.taskDetail = data;
      this.isToRight = true;
      this.currObj = data;
      this.visible = false;
      this.show = false;
      this.taskIndex = this.taskData.findIndex(
        (item) => item.taskId === this.taskDetail.taskId
      );
      this.socket.send(JSON.stringify({ code: 4, taskId: data.taskId }));
      this.socket.send(JSON.stringify({ code: 6, taskId: data.taskId }));
      this.socket.send(JSON.stringify({ code: 9, taskId: data.taskId }));
      this.socket.send(
        JSON.stringify({
          code: 12,
          userId: this.userId,
          stationId: data.stationId,
          stationTypeId: data.stationTypeId,
        })
      );
      this.socket.send(JSON.stringify({ code: 17, taskId: this.taskId }));
    },
    // 获取执行者列表
    getPersonList() {
      if (this.departmentType == 1) {
        getTopTaskExecutorList().then((res) => {
          const { data } = res.data;
          const data1 = {
            departmentList: data,
            userList: [],
          };
          this.displayDepartmentList = this.getDepartListAll(
            JSON.parse(JSON.stringify(data1))
          ).departmentAccountList;
        });
      } else {
        getPersonList(this.departmentId, 1).then((res) => {
          this.displayDepartmentList = this.getDepartListAll(
            JSON.parse(JSON.stringify(res.data.data))
          ).departmentAccountList;
        });
      }
    },
    // 获取递归后的部门数组
    getDepartListAll(data) {
      const departmentAccountList = data.departmentList || [];
      const userAccountList =
        data.userList === null
          ? []
          : data.userList.map((item) => {
              if (item.isAdmin) {
                item.userName = item.userName + "(管理员)";
              }
              return item;
            });
      for (const item of departmentAccountList) {
        item.needsData = { departmentId: item.departmentId };
      }
      for (const item of userAccountList) {
        item.departmentName = item.userName;
        item.needsData = {
          userId: item.userId,
          departmentId: item.departmentId,
          userName: item.userName,
        };
        item.disabled = item.userId === this.userId;
      }
      data.departmentAccountList = departmentAccountList.concat(
        userAccountList
      );
      for (let i = 0; i < departmentAccountList.length; i++) {
        this.getDepartListAll(departmentAccountList[i]);
      }
      return data;
    },
    handScroll() {
      _that.taskDetailState = false;
      let rightBox = document.getElementsByClassName("bottom-area")[0];
      this.scrollTop = rightBox.scrollTop / 100;
      // let scrollTop = rightBox.scrollTop;//这个元素的内容顶部（卷起来的）到它的视口可见内容（的顶部）的距离的度量
      // let scrollHeight = rightBox.clientHeight;//元素内部的高度(单位像素)，包含内边距，但不包括水平滚动条、边框和外边距
      // let scrollHeight = rightBox.scrollHeight;//元素内容高度，包括不可见的部分
      // if(scrollTop +scrollHeight>=scrollHeight ){
      //   console.log("到底了！")
      // }
    },
    enterpriseSearchBtn() {
      if (this.enterpriseSearch !== "") {
        this.isFirst = false;
        this.taskDetail = {};
        this.isToRight = false;
        this.taskData = [];
        this.taskIndex = -1;
        this.socket.send(
          JSON.stringify({
            code: 2,
            timeType: this.timeType,
            keywords: this.enterpriseSearch,
          })
        );
        this.socket.send(
          JSON.stringify({
            code: 3,
            timeType: this.timeType,
            departmentId: this.defaultpartMent,
            userId: this.userId,
            keywords: this.enterpriseSearch,
          })
        );
      }
    },
    clearSearch() {
      if (this.enterpriseSearch !== "") {
        this.enterpriseSearch = "";
        this.isFirst = false;
        this.isToRight = false;
        this.taskData = [];
        this.taskIndex = -1;
        this.taskDetail = {};
        this.socket.send(
          JSON.stringify({
            code: 2,
            timeType: this.timeType,
            keywords: this.enterpriseSearch,
          })
        );
        this.socket.send(
          JSON.stringify({
            code: 3,
            timeType: this.timeType,
            departmentId: this.defaultpartMent,
            userId: this.userId,
            keywords: this.enterpriseSearch,
          })
        );
      }
    },
    disabledDate(current) {
      return current < moment().startOf("day");
    },
    fullScreen() {
      this.displayState = !this.displayState;
    },
    changeDepartment() {
      this.isToRight = false;
      this.isFirst = false;
      this.send();
    },
    // 二级页面
    toDetail(url) {
      this.$router.push(url);
    },
    changeMarker(args) {
      this.activeIndex = 0;
      if (args) {
        this.isToRight = true;
        this.handleTaskDetail(args);
      } else {
        this.isToRight = false;
        this.taskDetail = {};
      }
    },
    markersClick(args) {
      this.activeIndex = 0;
      if (args) {
        const data = args.w.data;
        this.isToRight = true;
        // if (data.dispatch || Number(data.myCompleteStatus) === 0) {
        if (Number(data.myCompleteStatus) === 0) {
          this.visible = true;
        }
        if (data.dispatch) {
          this.type = 1;
        }
        if (Number(data.myCompleteStatus) === 0) {
          this.type = 2;
        }
        this.handleTaskDetail(args);
        this.show = false;
      } else {
        this.isToRight = false;
        this.visible = false;
        this.taskDetail = {};
        // this.show = true
      }
    },
    handleTaskDetail(args) {
      const data = JSON.parse(JSON.stringify(args.w.data));
      this.taskId = data.taskId;
      data.createTime = data.createTime
        ? data.createTime.substr(0, data.createTime.length - 3)
        : "";
      this.taskDetail = data;
      this.socket.send(JSON.stringify({ code: 4, taskId: data.taskId }));
      this.socket.send(JSON.stringify({ code: 6, taskId: data.taskId }));
      this.socket.send(JSON.stringify({ code: 9, taskId: data.taskId }));
      this.socket.send(
        JSON.stringify({
          code: 12,
          userId: this.userId,
          stationId: data.stationId,
          stationTypeId: data.stationTypeId,
        })
      );
      this.socket.send(JSON.stringify({ code: 17, taskId: this.taskId }));
    },
    handleChangeType(index) {
      this.active = index;
      this.isToRight = false;
      this.taskData = [];
      this.taskIndex = -1;
      this.taskDetail = {};
      this.socket.send(
        JSON.stringify({
          code: 3,
          timeType: this.timeType,
          departmentId: this.defaultpartMent,
          stationTypeId: index ? index : undefined,
          userId: this.userId,
          keywords: this.enterpriseSearch,
        })
      );
      this.socket.send(
        JSON.stringify({
          code: 2,
          timeType: this.timeType,
          keywords: this.enterpriseSearch,
        })
      );
    },
    getDepartment() {
      getDepartment()
        .then((res) => {
          this.departMentList = res.data.data || [];
          this.defaultpartMent = this.departMentList.length
            ? this.departMentList[0].departmentId
            : undefined;
        })
        .finally(() => {
          const currentRoute = JSON.parse(localStorage.getItem("currentRoute"));
          if (currentRoute.name == "部门联动") {
            this.connect();
          }
        });
    },
    handleCancel(e) {
      this.visible = false;
      this.endTime = undefined;
      this.planId = undefined;
      this.text = "";
    },
    // handleClick(index) {
    //   this.activeIndex = index
    //   this.realTimeData = this.pollutList[index]
    // },
    beforeDestroy() {
      window.removeEventListener("scroll", this.handScroll);
      // localStorage.setItem('depart', '0')
      localStorage.removeItem("depart");
      this.socket.close();
      this.socket = null;
      window._that = null;
      clearInterval(this.fetchTimer);
      clearInterval(this.heartbeatTimer);
    },
    handDispatch() {
      if (this.type == 1) {
        if (!this.planId) {
          message.warning("请选择任务处置");
          return;
        }
        if (!this.endTime) {
          message.warning("请选择结束时间");
          return;
        }
        this.socket.send(
          JSON.stringify({
            code: 11,
            departmentId: this.departmentId,
            planId: this.planId,
            parentId: this.taskDetail.taskId,
            userId: this.userId,
            endTime: this.endTime,
          })
        );
      }
      if (this.type == 2) {
        if (!this.text) {
          message.warning("请输入任务处置详情");
          return;
        }
        this.socket.send(
          JSON.stringify({
            code: 10,
            taskId: this.taskDetail.taskId,
            userId: this.userId,
            completeRemark: this.text,
          })
        );
      }
    },
    connect() {
      this.socket = new WebSocket(socketUrl2());
      // 监听socket连接
      this.socket.onopen = this.open;
      // 监听socket错误信息
      this.socket.onerror = this.error;
      // 监听socket消息
      this.socket.onmessage = this.getMessage;
      // 断开链接
      this.socket.onclose = this.close;
    },
    close() {
      this.reconcent();
    },
    reconcent() {
      this.connect();
    },
    heartbeat() {
      if (this.heartbeatTimer) {
        clearInterval(this.heartbeatTimer);
      }
      this.heartbeatTimer = setInterval(() => {
        this.socket.send(JSON.stringify({ code: 0 }));
      }, 30 * 1000);
    },
    open() {
      this.send();
      this.heartbeat();
    },
    send() {
      this.taskData = [];
      this.isFirst = false;
      this.active = 0;
      this.socket.send(JSON.stringify({ code: 1 }));
      this.socket.send(
        JSON.stringify({
          code: 2,
          timeType: this.timeType,
          keywords: this.enterpriseSearch,
        })
      );
      this.socket.send(
        JSON.stringify({
          code: 3,
          timeType: this.timeType,
          keywords: this.enterpriseSearch,
        })
      );
      this.socket.send(JSON.stringify({ code: 18 }));
    },
    error() {
      console.error("系统连接错误");
    },
    getMessage(msg) {
      const data = JSON.parse(msg.data);
      if (data) {
        this.heartbeat();
      }
      if (data.code == -1) {
        this.completeData = {
          bottomList: data.taskCompletion.map((item) => item.typeName),
          dataList: data.taskCompletion.map((item) => item.completed),
          dataList1: data.taskCompletion.map((item) => item.uncompleted),
          dataList2: data.taskCompletion.map((item) => item.closeCount),
          // dataList2: data.taskCompletion.map((item) => item.rate.substr(0, item.rate.indexOf('%')))
        };
        // this.taskHandleData = {
        //   bottomList: data.taskCompletion.map((item) =>
        //     item.create_date.substr(5)
        //   ),
        //   dataList: data.taskCompletion.map((item) => item.node_completed)
        // }
      }
      if (data.code == -2) {
        this.enentTypeList = [
          { id: 0, name: "全部", total: data.count },
          ...data.typeStatistics,
        ];
        // this.taskTypeData = {
        //   bottomList: [],
        //   total: data.count,
        //   dataList: data.typeStatistics.map((item) => {
        //     item.value = item.total
        //     return item
        //   })
        // }
        // this.taskTypeData.dataList = this.taskTypeData.dataList.sort((a, b) => b.value - a.value)
      }
      if (data.code == -3) {
        this.taskData = data.linkageTaskList || [];
        this.taskId = this.taskDetail.taskId
          ? this.taskDetail.taskId
          : this.taskData.length
          ? this.taskData[0].taskId
          : "";
        if (this.taskData.length) {
          this.show = true;
          jq("#timecs li").remove();
          jq("#timecs1 li").remove();
          this.socket.send(JSON.stringify({ code: 17, taskId: this.taskId }));
        } else {
          this.show = false;
        }
        // if (!this.isFirst) {
        //   this.isFirst = true
        //   this.enentTypeList = [0, 0, 0, 0, 0]
        //   this.enentTypeList[0] = data.linkageTaskList.length
        //   const data1 = (data.linkageTaskList || []).forEach((item) => {
        //     switch (item.typeId) {
        //       case 1:
        //         this.enentTypeList[1] = this.enentTypeList[1] + 1
        //         break
        //       case 2:
        //         this.enentTypeList[2] = this.enentTypeList[2] + 1
        //         break
        //       case 3:
        //         this.enentTypeList[3] = this.enentTypeList[3] + 1
        //         break
        //       case 4:
        //         this.enentTypeList[4] = this.enentTypeList[4] + 1
        //       // case 11:
        //       //   this.enentTypeList[1] = this.enentTypeList[1] + 1
        //       //   break
        //     }
        //   })
        // }
      }
      // 部门联的任务详情
      if (data.code == -17 && data.success) {
        // this.scrollTop = 0
        this.departmentList = [];
        // jq('.tttt').remove()
        this.taskRemark = data.executiveDepartmentList;
        const taskList = [];
        const departmentList = [];
        (data.executiveDepartmentList || []).forEach((item, index) => {
          const type =
            item.departmentId == 11
              ? "st"
              : item.departmentId == 12
              ? "zf"
              : item.departmentId == 13
              ? "ns"
              : "jj";
          departmentList.push({
            name: item.departmentName,
            type: type,
            initIndex: index,
          });
          for (const task of item.feedbackList) {
            task.times = new Date(task.time).getTime();
            task.top = index * 0.38 - 0.06 + "rem";
            task.initIndex = index;
            task.type =
              item.departmentId == 11
                ? 1
                : item.departmentId == 12
                ? 2
                : item.departmentId == 13
                ? 3
                : 4;
            task.departmentName = task.departmentName;
            task.replyContent =
              task.departmentName +
              "-" +
              task.userName +
              "：" +
              (task.remark || task.content);
            taskList.push(task);
          }
        });
        this.departmentList = departmentList;
        this.departmentStatus = new Array(departmentList.length).fill(1);
        this.startTimes = new Date(data.task.createTime).getTime();
        if (data.task.completeStatus) {
          this.endTimes = new Date(data.task.completeTime).getTime();
        }
        if (taskList.length !== 0) {
          const sort = taskList.sort((a, b) => {
            return a.times - b.times;
          });
          // 时间轴开始时间
          let startTime = "";
          // 时间轴结束时间
          let endTime = "";
          if (!this.endTimes) {
            startTime = new Date(
              moment(new Date(Number(this.startTimes))).format(
                "YYYY-MM-DD HH:00:00"
              )
            ).valueOf();
            endTime =
              new Date(
                moment(new Date(Number(new Date().getTime()))).format(
                  "YYYY-MM-DD HH:00:00"
                )
              ).valueOf() + 3600000;
          } else {
            startTime = new Date(
              moment(new Date(Number(this.startTimes))).format(
                "YYYY-MM-DD HH:00:00"
              )
            ).valueOf();
            endTime =
              new Date(
                moment(new Date(Number(this.endTimes))).format(
                  "YYYY-MM-DD HH:00:00"
                )
              ).valueOf() + 3600000;
          }
          this.timeLineDataTime = {
            startTime,
            endTime,
          };
          this.timeLineTimes = moment(
            new Date(Number(this.timeLineDataTime.startTime) + 86400000)
          ).format("YYYY-MM-DD HH:00:00");
          for (const item of taskList) {
            item.timec = startTime;
          }
          this.taskList = taskList;
          jq(".time1").remove();
          jq("#timecs").css({
            width: 0,
            left: 0,
          });
          jq("#timecs1").css({
            width: 0,
            left: 0,
          });
          jq("#timecs1 li").remove();
          jq("#timecs li").remove();
          this.$nextTick(() => {
            this.htmlLoad(startTime, endTime);
            this.taskList.forEach((item, index) => {
              this.addtimes(
                item,
                sort.slice(-1)[0].times,
                sort[0].times,
                index
              );
            });
            if (!jq("#timess li") || jq("#timess li").length === 0) return;
            let lefts = jq("#timess li")[jq("#timess li").length - 1].style
              .left;
            lefts = Number(lefts.substr(0, lefts.length - 3));
            const totalAllLength = jq(".timeLine-text-line").width() / 100;
            if (lefts > totalAllLength) {
              jq("#timess").style.left = (totalAllLength - lefts) * 100 + "px";
            }
            this.totalLength = jq("#timess").width() / 100;
            let currLeft = jq("#timecs .time1")[this.next].style.left;
            currLeft = currLeft.replace(/rem/g, "");
            // jq('#time .time1')[this.next].click()
            if (Number(currLeft) >= Number(this.totalLength)) {
              const left =
                Number(this.totalLength) - Number(currLeft) + 1.35 + "px";
              jq("#time").css({ left: -left });
            }
            let length = jq("#timecs .time1").length;
            for (let i = 0; i < length; i++) {
              let currLeft = jq("#timecs .time1")[i].style.left;
              this.totalDistance.push(
                Number(currLeft.replace(/rem/g, "")) * 100
              );
            }
            this.fetchTimer = setInterval(() => {
              this.nextOpen();
            }, 5 * 1000);
          });
        } else {
          jq("#timecs li").remove();
          jq("#timecs1 li").remove();
          jq("#timecs").css({
            width: 0,
            left: 0,
          });
          jq("#timecs1").css({
            width: 0,
            left: 0,
          });
        }
      }
      if (data.code == -4) {
        // 图片列表
        // this.imgList = data.task.taskAnnexList.map(item => item.annexUrl).filter(item => ['png', 'jpeg', 'jpg', 'gif', 'bmp'].includes(item.split('.')[1].toLocaleLowerCase()))
        this.imgList = data.task.taskAnnexList
          .map((item) => item.annexUrl)
          .filter(
            (item) =>
              item.endsWith(".png") ||
              item.endsWith(".jpg") ||
              item.endsWith(".jpeg") ||
              item.endsWith(".gif") ||
              item.endsWith(".bmp")
          );
        this.excutorArr = data.task.executiveFeedbackList;
        this.orgTreeList = data.task.taskNodeList;
        this.typeId = data.task.typeId;
        // this.executiveDepartmentList1 = data.executiveDepartmentList.filter(
        //   (item) => item.parentId
        // )
        this.executiveDepartmentList1 = this.arrSlice(
          data.executiveDepartmentList.filter((item) => item.parentId) || []
        );
        this.executiveDepartmentList2 = data.executiveDepartmentList
          .filter((item) => !item.parentId)
          .map((item) => {
            item.taskNodeName =
              this.taskDetail.stationTypeId === 1
                ? "水环境告警"
                : this.taskDetail.stationTypeId === 2
                ? "大气环境告警"
                : this.taskDetail.stationTypeId === 3
                ? "重污告警"
                : "巡岗告警";
            return item;
          });
        this.feedbackList = (data.feedbackList || []).map((item) => {
          item.time1 = item.time.substr(0, item.time.indexOf(" "));
          item.time1 = item.time1.replace(/-/g, ".");
          item.time2 = item.time.substr(
            item.time.indexOf(" ") + 1,
            item.time.length - item.time.indexOf(" ")
          );
          item.type =
            (item.departmentName.indexOf("综合") ||
              item.departmentName.indexOf("执法")) > -1
              ? 1
              : item.departmentName.indexOf("住建") > -1
              ? 2
              : (item.departmentName.indexOf("生态") ||
                  item.departmentName.indexOf("环境")) > -1
              ? 3
              : 4;
          return item;
        });
        if (this.executiveDepartmentList1.length) {
          this.$nextTick(() => {
            // this.initAnimation()
          });
        }
      }
      if (data.code == -6) {
        const data1 = data.itemVOList || [];
        this.pollutNameList = data1.map((item) => item.pollutantName);
        this.pollutList = data1.map((item) => {
          const bottomList = item.mapList.map((item) => item.name);
          const dataList = item.mapList.map((item) => item.value || 0);
          const data1 = JSON.parse(JSON.stringify(dataList)).sort(
            (a, b) => a - b
          );
          return {
            bottomList: bottomList,
            dataList: dataList,
            standard: item.alarmStandard,
            max:
              (data1.length ? data1[data1.length - 1] : 0) < item.alarmStandard
                ? item.alarmStandard
                : data1.length
                ? data1[data1.length - 1]
                : 0,
            unit: item.unit,
            name: item.pollutantName,
          };
        });
        this.realTimeData = this.pollutList[0];
      }
      if (data.code == -9) {
        this.taskAlarmList = (data.taskAlarmList || []).map((item) => {
          item.alarmTime = item.alarmTime.replace(/T/, " ");
          item.alarmTime = item.alarmTime.substr(5, 11);
          return item;
        });
      }
      if (data.code == -12) {
        this.planList = data.planList || [];
      }
      if (data.code == -11 && data.success) {
        message.success("派遣成功");
        this.handleCancel();
        this.taskIndex = this.taskData.findIndex(
          (item) => item.taskId === this.taskDetail.taskId
        );
        this.socket.send(
          JSON.stringify({
            code: 3,
            timeType: this.timeType,
            // departmentId: this.defaultpartMent,
            // stationTypeId: this.active ? this.active : undefined,
            // userId: this.userId,
            keywords: this.enterpriseSearch,
          })
        );
      }
      if (data.code == -10 && data.success) {
        message.success("处置成功");
        this.handleCancel();
        this.taskIndex = this.taskData.findIndex(
          (item) => item.taskId === this.taskDetail.taskId
        );
        this.socket.send(
          JSON.stringify({
            code: 3,
            timeType: this.timeType,
            // departmentId: this.defaultpartMent,
            // stationTypeId: this.active ? this.active : undefined,
            // userId: this.userId,
            keywords: this.enterpriseSearch,
          })
        );
        this.socket.send(JSON.stringify({ code: 4, taskId: this.taskId }));
      }
      if (data.code == -18 && data.success) {
        const bottomList = data.data.map((item) => item.yearMonth);
        const dataList = data.data.map((item) =>
          ((item.complete / item.total) * 100).toFixed(2)
        );
        const dataList1 = data.data.map((item) => item.complete);
        const dataList2 = data.data.map((item) => item.unComplete);
        const dataList3 = data.data.map((item) => item.total);
        this.executionRate = {
          bottomList,
          dataList,
          dataList1,
          dataList2,
          dataList3,
        };
      }
    },
    arrSlice(arr) {
      return arr
        .sort(() => Math.random() > 0.5) // 打乱
        .map((e, i) => (i % 2 ? null : [arr[i], arr[i + 1]])) // 两两取出
        .filter(Boolean);
    },
    initAnimation() {
      var path1 = anime.path(".svg1 path");
      var path2 = anime.path(".svg2 path");
      var path3 = anime.path(".svg3 path");
      var path4 = anime.path(".svg4 path");
      anime({
        targets: ".animation-area .bg1",
        translateX: path1("x"),
        translateY: path1("y"),
        easing: "linear",
        duration: 20000,
        loop: true,
      });
      anime({
        targets: ".animation-area .bg2",
        translateX: path2("x"),
        translateY: path2("y"),
        easing: "linear",
        duration: 20000,
        loop: true,
      });
      anime({
        targets: ".animation-area .bg3",
        translateX: path3("x"),
        translateY: path3("y"),
        easing: "linear",
        duration: 20000,
        loop: true,
      });
      anime({
        targets: ".animation-area .bg4",
        translateX: path4("x"),
        translateY: path4("y"),
        easing: "linear",
        duration: 20000,
        loop: true,
      });
    },
    htmlLoad(startTime, endTime) {
      // eslint-disable-next-line @typescript-eslint/no-this-alias
      const _this = this;
      let _move = false;
      let _x;
      let dataTime = "";
      jq(".tttt")
        .click(function() {
          // 1;
        })
        .mousedown(function(e) {
          if (_this.fetchTimer) {
            clearInterval(_this.fetchTimer);
          }
          _move = true;
          _this.move = true;
          _x = e.pageX - parseInt(jq("#timecs").css("left"));
          jq("#timecs").fadeTo(20, 1);
          jq("#timecs1").fadeTo(20, 1);
        });
      jq(".tttt")
        .mousemove(function(e) {
          // _this.pageX = e.pageX;
          _this.move = true;
          if (_move) {
            _this.taskDetailState = false;
            // for (const item of document.querySelectorAll(".time1-active")) {
            //     item.className = "time1";
            // }
            const x = e.pageX - _x;
            jq("#timecs").css({
              left: x,
            });
            jq("#timecs1").css({
              left: x,
            });
            if (x > 0) {
              jq("#timecs").css({
                left: 0,
              });
              jq("#timecs1").css({
                left: 0,
              });
              dataTime = moment(
                new Date(Number(_this.timeLineDataTime.startTime) + 86400000)
              ).format("YYYY-MM-DD HH:00:00");
              // _this.timeLineTimes = dataTime;
            } else if (jq("#timecs").width() - jq("#timess").width() + x < 0) {
              if (_this.timeLength <= 20) {
                jq("#timecs").css({
                  left: 0,
                });
                jq("#timecs1").css({
                  left: 0,
                });
              } else {
                jq("#timecs").css({
                  left: -(jq("#timecs").width() - jq("#timess").width()),
                });
                jq("#timecs1").css({
                  left: -(jq("#timecs").width() - jq("#timess").width()),
                });
              }
            } else if (x < 0) {
              const number =
                (Math.ceil(Math.abs(x) / (jq("#timess").width() / 24)) +
                  24 -
                  1) *
                3600000;
              dataTime = moment(
                new Date(Number(_this.timeLineDataTime.startTime) + number)
              ).format("YYYY-MM-DD HH:00:00");
              // _this.timeLineTimes = dataTime;
            }
            _this.left = Math.abs(
              Number(
                jq("#timecs")
                  .css("left")
                  .substr(0, jq("#timecs").css("left").length - 2)
              )
            );
          }
        })
        .mouseup(function() {
          // // 水质
          // _this.getMonitorItemDetailRecord();
          // // 空气
          // _this.getAqiTrend();
          // // 重污
          // _this.getTwentyFourHour();
          if (_this.fetchTimer && _move) {
            clearInterval(_this.fetchTimer);
            _this.fetchTimer = setInterval(() => {
              _this.nextOpen();
            }, 5 * 1000);
          }
          jq(".tttt").fadeTo("fast", 1);
          _move = false;
          _this.move = false;
        })
        .mouseleave(function() {
          if (_this.fetchTimer && _move) {
            clearInterval(_this.fetchTimer);
            _this.fetchTimer = setInterval(() => {
              _this.nextOpen();
            }, 5 * 1000);
          }
          _move = false;
          _this.move = false;
        });
      this.time(startTime, endTime);
    },
    time(hour, endTime) {
      const date = endTime;
      let length = this.departmentList.length - 1;
      let timeLength = 0;
      const timess = (date - hour) / 1000 / 60 + 80;
      for (let i = 0; i < Math.floor(timess); i++) {
        let time1 = "";
        const house =
          new Date(hour + i * 60000).getHours() >= 10
            ? new Date(hour + i * 60000).getHours()
            : "0" + new Date(hour + i * 60000).getHours();
        const minutes =
          new Date(hour + i * 60000).getMinutes() >= 10
            ? new Date(hour + i * 60000).getMinutes()
            : "0" + new Date(hour + i * 60000).getMinutes();
        const sxsx = house + ":" + minutes;
        // if (minutes == "00") {
        //   timeLength++;
        //   time1 = `<li style='left:${i / 6 *
        //   0.06+0.4}rem;top: ${0.1+0.38*length}rem'>${sxsx}</li>`;
        // }
        const initTop = length >= 3 ? 1.16 : 0.38 * length;
        if (minutes == "00") {
          timeLength++;
          time1 = `<li style='left:${(i / 6) * 0.06 +
            0.4}rem;top: ${initTop}rem'>${sxsx}</li>`;
        }
        this.timeLength = timeLength;
        jq("#timecs1").append(time1);
      }
      // console.log(Math.floor(timess) * 0.01, 'Math.floor(timess) * 0.01')
      jq("#timecs").css({
        width: Math.floor(timess) * 0.01 + "rem",
      });
      jq("#timecs1").css({
        width: Math.floor(timess) * 0.01 + "rem",
      });
    },
    addtimes(data, endTime, startTime, index) {
      // console.log(index, 'index')
      if (data.type == 1) {
        this.task1NodeIndex.push(index);
      } else if (data.type == 2) {
        this.task2NodeIndex.push(index);
      } else if (data.type == 3) {
        this.task3NodeIndex.push(index);
      } else if (data.type == 4) {
        this.task4NodeIndex.push(index);
      }
      const replyContent = data.replyContent.replace(/\s/g, "");
      const content = data.content;
      const taskLogId = data.taskLogId;
      const str = `'${data.departmentName}---${data.times}---${content}---${replyContent}---${taskLogId}---${data.type}---${index}---${data.initIndex}'`;
      const timess = Math.floor((data.times - data.timec) / 1000 / 60);
      const activeClass = "time" + (data.initIndex + 2);
      const time1 = `<li class='time1 ${activeClass}'" style='left:${timess *
        0.01 +
        0.4}rem;top:${data.top}' onclick=taskDetails(${str})></li>`;
      jq("#timecs").append(time1);
      // if (endTime - startTime > 90000000) {
      //     jq("#time").css({
      //         left: "-" + (timess * 0.01 - 11.5) + "rem"
      //     });
      // }
    },
    nextOpen() {},
    taskDetails(data) {
      // console.log(jq(document).scrollTop(), 233999)
      let top = data.split("---")[7] * 0.38 - 0.06;
      if (data.split("---")[7] == 0) {
        jq("#timecs").addClass("time-active1");
        //@ts-ignore
        // eslint-disable-next-line no-undef
        _that.class = "time-active1";
      } else if (data.split("---")[7] == 1) {
        jq("#timecs").addClass("time-active2");
        //@ts-ignore
        // eslint-disable-next-line no-undef
        _that.class = "time-active2";
      } else if (data.split("---")[7] == 2) {
        jq("#timecs").addClass("time-active3");
        //@ts-ignore
        // eslint-disable-next-line no-undef
        _that.class = "time-active3";
      } else if (data.split("---")[7] == 3) {
        jq("#timecs").addClass("time-active4");
        //@ts-ignore
        // eslint-disable-next-line no-undef
        _that.class = "time-active4";
      } else if (data.split("---")[7] == 4) {
        jq("#timecs").addClass("time-active5");
        //@ts-ignore
        // eslint-disable-next-line no-undef
        _that.class = "time-active5";
      } else if (data.split("---")[7] == 5) {
        jq("#timecs").addClass("time-active6");
        //@ts-ignore
        // eslint-disable-next-line no-undef
        _that.class = "time-active6";
      } else if (data.split("---")[7] == 6) {
        jq("#timecs").addClass("time-active7");
        //@ts-ignore
        // eslint-disable-next-line no-undef
        _that.class = "time-active7";
      } else if (data.split("---")[7] == 7) {
        jq("#timecs").addClass("time-active8");
        //@ts-ignore
        // eslint-disable-next-line no-undef
        _that.class = "time-active8";
      }
      //@ts-ignore
      // eslint-disable-next-line no-undef
      _that.type = data.split("---")[5];
      //@ts-ignore
      // eslint-disable-next-line no-undef
      _that.initIndex = data.split("---")[7];
      //@ts-ignore
      // eslint-disable-next-line no-undef
      _that.next = Number(data.split("---")[6]);
      jq(".time1-active").removeClass("time1-active");
      // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
      //@ts-ignore
      // eslint-disable-next-line no-undef
      window.clearTimeout(_that.alertTime);
      // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
      //@ts-ignore
      // eslint-disable-next-line no-undef
      _that.departmentName = data.split("---")[0];
      // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
      //@ts-ignore
      // eslint-disable-next-line no-undef
      _that.creatTime = moment(new Date(Number(data.split("---")[1]))).format(
        "YYYY-MM-DD HH:mm"
      );
      // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
      //@ts-ignore
      // eslint-disable-next-line no-undef
      _that.content = data.split("---")[2];
      // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
      //@ts-ignore
      // eslint-disable-next-line no-undef
      _that.replyContent = data.split("---")[3];
      // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
      //@ts-ignore
      // eslint-disable-next-line no-undef
      _that.taskLogId = data.split("---")[4];
      // console.log(this.taskList, 'this.taskList')
      this.remarkDetail = this.taskList.find(
        (item) => item.taskLogId === this.taskLogId
      );
      // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
      //@ts-ignore
      const className = event.target.className;
      // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
      //@ts-ignore
      // eslint-disable-next-line no-undef
      event.target.className = className + " " + "time1-active";
      // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
      //@ts-ignore
      // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
      //@ts-ignore
      const lefts =
        Number(
          jq("#timecs")[0].style.left.substr(
            0,
            jq("#timecs")[0].style.left.length - 2
          )
        ) / 100;
      // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
      //@ts-ignore
      const left =
        Number(
          event.target.style.left.substr(0, event.target.style.left.length - 3)
        ) +
        0.62 +
        lefts;
      //@ts-ignore
      // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
      //@ts-ignore
      document.querySelector(".taskAlert").style.left = `${left}rem`; //@ts-ignore
      // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
      //@ts-ignore
      document.querySelector(".taskAlert").style.top = `${top -
        1.74 -
        _that.scrollTop}rem`;
      // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
      //@ts-ignore
      // eslint-disable-next-line no-undef
      _that.taskDetailState = true;
      jq(document).one("click", function() {
        // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
        //@ts-ignore
        // eslint-disable-next-line no-undef
        _that.taskDetailState = false;
        jq(".time1-active").removeClass("time1-active");
        //@ts-ignore
        // eslint-disable-next-line no-undef
        jq("#timecs").removeClass(_that.class);
        // for (const item of document.querySelectorAll(".time1-active")) {
        //     // item.className = "time1";
        //     item.removeClass('time1-active')
        // }
      });
      // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
      //@ts-ignore
      // eslint-disable-next-line no-undef
      event.stopPropagation();
      // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
      // @ts-ignore
      // eslint-disable-next-line no-undef
      _that.alertTime = setTimeout(() => {
        // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
        //@ts-ignore
        // eslint-disable-next-line no-undef
        _that.taskDetailState = false;
        jq(".time1-active").removeClass("time1-active");
        //@ts-ignore
        // eslint-disable-next-line no-undef
        jq("#timecs").removeClass(_that.class);
        // for (const item of document.querySelectorAll(".time1-active")) {
        //     item.className = "time1";
        // }
      }, 5 * 1000);
    },
    handleDepartment(item) {
      this.$set(
        this.departmentStatus,
        item.initIndex,
        !this.departmentStatus[item.initIndex]
      );
      if (item.initIndex < 4) {
        jq(".time" + (item.initIndex + 2)).css(
          "display",
          this.departmentStatus[item.initIndex] ? "block" : "none"
        );
      } else {
        jq(".time5").css(
          "display",
          this.departmentStatus[item.initIndex] ? "block" : "none"
        );
      }
    },
    hanldeDetail() {
      this.remarkDetail = this.taskList.find(
        (item) => item.taskLogId === this.taskLogId
      );
      clearInterval(this.fetchTimer);
      this.visibles = true;
    },
    handleClose() {
      this.visibles = false;
      this.fetchTimer = setInterval(() => {
        this.nextOpen();
      }, 5 * 1000);
    },
    handleClickTimeType(index) {
      this.timeType = index;
      this.isToRight = false;
      this.socket.send(
        JSON.stringify({
          code: 2,
          // departmentId: this.defaultpartMent,
          // stationTypeId: index ? index : undefined,
          // userId: this.userId,
          timeType: index,
          keywords: this.enterpriseSearch,
        })
      );
      this.socket.send(
        JSON.stringify({
          code: 3,
          // departmentId: this.defaultpartMent,
          // stationTypeId: index ? index : undefined,
          // userId: this.userId,
          timeType: index,
          keywords: this.enterpriseSearch,
        })
      );
    },
  },
};
</script>
