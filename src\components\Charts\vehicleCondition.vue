<style lang="less" scoped>
    .no-data {
        font-size: 0.2rem;
        display: flex;
        align-items: center;
        justify-content: center;
    }
</style>
<template>
    <div
            :id="id"
            :style="{ height: height, width: width }"
            v-if="propData.dataList.length != 0"
    />
    <div v-else class="no-data" :style="{ height: height, width: width }">
        数据未更新
    </div>
<!--    <div v-else>-->
<!--        <img-->
<!--                style="width:1.7rem;position:relative;left:2rem;top:0.3rem"-->
<!--                src="@/assets/pollution-not.png"-->
<!--                alt=""-->
<!--        />-->
<!--    </div>-->
</template>

<script lang="ts">
import * as echarts from 'echarts'
import { Component, Prop, Watch } from "vue-property-decorator";
import { mixins } from "vue-class-component";
import ResizeMixin from "./mixins/resize";
interface AirData {
    bottomList: string[];
    dataList: string[];
    dataList1: string[];
    // dataList2: string[];
}
@Component({
    name: "vehicleCondition"
})
export default class extends mixins(ResizeMixin) {
    @Prop({ default: "chart" }) private id!: string;
    @Prop({ default: "200px" }) private width!: string;
    @Prop({ default: "200px" }) private height!: string;
    @Prop({ required: true }) private propData!: AirData;
    // private chart: any = null;
    private option: any = {};
    @Watch("propData", { immediate: true, deep: true }) public onMsgChanged(
        newValue: AirData,
        oldValue: AirData
    ) {
        this.propData = newValue;
        if (newValue.dataList.length) {
            if (this.chart) {
                this.chart.clear();
            }
            this.$nextTick(() => {
                this.initChart();
            });
        }
    }
    mounted() {
        setTimeout(() => {
            this.initChart();
        }, 2000);
    }
    beforeDestroy() {
        if (!this.chart) {
            return;
        }
        this.chart.clear();
        this.chart.dispose();
        this.chart = null;
    }
    private initChart() {

        if(this.chart === null || this.chart === undefined) {
            this.chart = echarts.init(
                document.getElementById(this.id) as HTMLDivElement
            );
        }
        this.chart.setOption({
            legend: {
                right: 30,
                textStyle: {
                    color: '#7BB7ED',
                    fontSize: 12
                }
            },
            tooltip: {
                show: true,
                trigger: "axis",
                axisPointer: {
                    type: "none",
                    label: {
                        backgroundColor: "#6a7985"
                    }
                },
                // formatter: (params:any) => {
                //     console.log(params, 123)
                //     return params[0].name + '<br>' + '全部: ' + params[0].value + '<br>' + '工作中: ' + params[1].value
                // }
            },
            grid: {
                height: "80%",
                left: '5%',
                top: '15%',
                bottom: '5%',
                containLabel: true
            },
            xAxis: {
                data: this.propData.bottomList,
                axisLine: {
                    lineStyle: {
                        color: '#1E2275'
                    }
                },
                axisTick: {
                    show: false
                },
                axisLabel: {
                    color: '#86C6FF',
                    interval:0,
                    fontSize: 14,

                },
                boundaryGap: true
            },
            yAxis: {
                type: "value",
                name: '单位：辆',
                nameTextStyle: {
                    color: '#86C6FF',
                    fontSize: 14
                },
                axisLine: {
                    show: false,
                    lineStyle: {
                        color: '#1E2275'
                    }
                },
                axisTick: {
                    show: false
                },
                axisLabel: {
                    color: '#86C6FF',
                    fontSize: 14
                },
                splitLine: {
                    show: true,
                    lineStyle: {
                        type: 'dashed',
                        color: '#1E2275'
                    }
                }
            },
            // animationDurationUpdate: 1200,
            series: [
                {
                    type: 'bar',
                    name: '全部',
                    itemStyle: {
                        color: 'rgba(0,222,255, 1)',
                        barBorderRadius: [5, 5, 0, 0]
                    },
                    barWidth: 10,
                    z: 2,
                    data: this.propData.dataList1
                },
                // {
                //     /* eslint-disable @typescript-eslint/ban-ts-ignore */
                //     //@ts-ignore
                //     type: 'bar',
                //     name: '工作中',
                //     z: 2,
                //     itemStyle: {
                //         normal: {
                //             color: 'rgba(0,222,255, 1)'
                //         }
                //     },
                //     barWidth: 10,
                //     barGap: '-100%', // Make series be ove
                //     data: this.propData.dataList
                // },
                {
                    /* eslint-disable @typescript-eslint/ban-ts-ignore */
                    //@ts-ignore
                    type: 'line',
                    name: '工作中',
                    z: 2,
                    data: this.propData.dataList,
                    lineStyle: {
                        color: '#C68513'
                    },
                    smooth: true,
                    symbol: "circle",
                    symbolSize: 6,
                    itemStyle: {
                      color: '#C68513',
                      borderColor: '#fff',
                      borderWidth: 3
                    },
                    areaStyle: {
                        color: {
                            type: "linear",
                            x: 0,
                            y: 0,
                            x2: 0,
                            y2: 1,
                            colorStops: [
                                {
                                    offset: 0,
                                    color: "RGBA(198,133,19, .4)"
                                },
                                {
                                    offset: 1,
                                    color: "transparent" // 100% 处的颜色
                                }
                            ],
                            global: false
                        },
                        shadowColor: "rgba(0,85,250,0)",
                        shadowBlur: 20
                    },
                }
            ]
        } as EChartOption<EChartOption>);
    }
}
</script>

