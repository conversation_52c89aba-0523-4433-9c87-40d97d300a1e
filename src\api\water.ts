import request from '@/utils/request'
import { AxiosPromise } from 'axios'

/**
 * @method fetchCameraUrl
 * @param {number} stationId 站点id
 * @description 获取监控直播地址
 */
export function fetchCameraUrl(channelId?: string): AxiosPromise<any> {
  return request({
    url: `/water/water-camera/live/${channelId}`,
    method: 'get',
  })
}

/**
 * @method fetchCameraList
 * @param {number} stationId 站点id
 * @description 根据水站点信息Id 获取摄像头列表
 */
export function fetchCameraList(stationId?: string): AxiosPromise<any> {
  return request({
    url: stationId ? `/water/water-camera/list/${stationId}` : `/water/water-camera/list`,
    method: 'get',
  })
}
/**
 * @method fetchCameraList
 * @param {number} data 站点id
 * @description 根据水站点信息Id 获取摄像头列表
 */
export function fetchCameraLists(data: any): AxiosPromise<any> {
  return request({
    url: `/water/bigData/water-camera/pageList`,
    method: 'post',
    data,
  })
}

/**
 * @method fetchStationWithRecord
 * @param {type} data 说明
 * @description 获取水质监测站列表及实时记录
 */
export function fetchStationWithRecord(): AxiosPromise<any> {
  return request({
    url: '/water/monitor/getStationWithRecord?districtCode=510106',
    method: 'get',
  })
}

/**
 * @method getTodayMonitorItemRecord
 * @param {type} data 说明
 * @description 获取今日监测项记录时间与值得映射
 */
export function getTodayMonitorItemRecord(): AxiosPromise<any> {
  return request({
    url: '/water/monitor/getTodayMonitorItemRecord?districtCode=510106',
    method: 'get',
  })
}

/**
 * @method functionName
 * @param {type} data 说明
 * @description 获取全部水质预警 （区）
 */
export function fetchRecentAlarm(): AxiosPromise<any> {
  return request({
    url: '/water/water-alarm/recent',
    method: 'get',
  })
}

/**
 * @method functionName
 * @param {number} stationId 站点id
 * @description 根据站点id 获取最新预警记录
 */
export function fetchRecentAlarmById(stationId: number): AxiosPromise<any> {
  return request({
    url: '/water/water-alarm/recent',
    method: 'get',
    params: { stationId },
  })
}

/**
 * @method fetchHeavilyPollutingEnterpriseList Deprecated
 * @param {type} data 说明
 * @description 获取重污企业数据列表
 */
export function fetchHeavilyPollutingEnterpriseList(): AxiosPromise<any> {
  return request({
    url: '/water/monitor/getHeavilyPollutingEnterpriseList?districtCode=510106',
    method: 'get',
  })
}

/**
 * @method getStationList
 * @param {type} data 说明
 * @description 获取监测站列表
 */
export function getStationList(keywords: any): AxiosPromise<any> {
  return request({
    url: '/water/monitor/getStationList',
    method: 'get',
    params: {
      districtCode: '510106',
      keywords: keywords,
    },
  })
}

/**
 * @method getRealtimeMonitorRecordList
 * @param {type} data 说明
 * @description 获取实时监测记录
 */
export function getRealtimeMonitorRecordList(stationId: string): AxiosPromise<any> {
  return request({
    url: '/water/monitor/getRealtimeMonitorRecordList',
    method: 'get',
    params: {
      stationId,
    },
  })
}

/**
 * @method getMonthMonitorItemRecord
 * @param {type} data 说明
 * @description 获取本月监测项记录时间与值得映射
 */
export function getMonthMonitorItemRecord(): AxiosPromise<any> {
  return request({
    url: '/water/monitor/getMonthMonitorItemRecord?districtCode=510106',
    method: 'get',
  })
}

/**
 * @method getHourMonitorRecordList
 * @param {type} data 说明
 * @description 获取小时监测数据列表
 */
export function getHourMonitorRecordList(stationId: string): AxiosPromise<any> {
  return request({
    url: '/water/monitor/getHourMonitorRecordList',
    method: 'get',
    params: { stationId },
  })
}

/**
 * @method getMonitorItemDetailRecord
 * @param {string} stationId 站点id itemCode 监测项CODE
 * @description 获取监测项详情记录
 */
export function getMonitorItemDetailRecord(stationId: string, itemCode: string): AxiosPromise<any> {
  return request({
    url: '/water/monitor/getMonitorItemDetailRecord',
    method: 'get',
    params: {
      stationId,
      itemCode,
    },
  })
}

/**
 * @method getHeavilyPollutingEnterpriseList
 * @param {type} data 说明
 * @description 获取重污企业数据列表
 */
export function getHeavilyPollutingEnterpriseList(): AxiosPromise<any> {
  return request({
    url: '/water/monitor/getHeavilyPollutingEnterpriseList?districtCode=510106',
    method: 'get',
  })
}

/**
 * @method getAllStationRealTimeRecordList
 * @param {type} data 说明
 * @description
 */
export function getAllStationRealTimeRecordList(): AxiosPromise<any> {
  return request({
    url: '/water/monitor/getAllStationRealTimeRecordList?districtCode=510106',
    method: 'get',
  })
}

/**
 * @method functionName
 * @param {type} data 说明
 * @description
 */
export function riverList(): AxiosPromise<any> {
  return request({
    url: '/water/monitor/getRiverList',
    method: 'get',
    params: {
      districtCode: 510106,
    },
  })
}

/**
 * @method functionName
 * @param {type} data 获取水质打卡信息
 * @description
 */
export function getPunch(data: any): AxiosPromise<any> {
  return request({
    url: '/water/punch/getPunch',
    method: 'get',
    params: data,
  })
}

/**
 * @method functionName
 * @param {type} data 说明
 * @description
 */
export function getRiverInfoList(): AxiosPromise<any> {
  return request({
    url: '/water/monitor/getRiverInfoList',
    method: 'get',
  })
}
/**
 * @method functionName
 * @param {type} data 说明
 * @description
 */
export function functionName(data: any): AxiosPromise<any> {
  return request({
    url: 'url',
    method: 'get',
    params: data,
  })
}
/**
 * @method getAlarmList
 * @param {pageNum} 分页页码
 * @param { pageSize } 分页条数
 * @description 获取水质告警统计
 */
export function getAlarmList(data: any): AxiosPromise<any> {
  return request({
    url: '/water/alarm/pageList',
    method: 'post',
    data,
  })
}

/**
 * @description 根据水站id获取最近一条的巡岗记录
 * @param stationId
 * @method getlatestRecord
 */
export function getlatestRecord(stationId: any) {
  return request({
    url: '/task/getRecentlyWaterDailyPatrol?stationId=' + stationId,
    method: 'get',
  })
}
/**
 * @method getWaterPatrolRecordListByStation
 * @description 根据站点获取 站点的最近20条巡岗记录
 * @param { stationId } 站点id
 * @param { pageNum } 分页页码
 * @param { pageSize } 分页数量
 */
export function getWaterPatrolRecordListByStation(params: any): AxiosPromise<any> {
  return request({
    url: '/task/getWaterDailyPatrolPage',
    method: 'get',
    params,
  })
}

/**
 * @description获取今年考核表
 * @method getAssesThisYear
 */
export function getAssesThisYear(): AxiosPromise<any> {
  return request({
    url: '/water/water-assessment-goal/listThisYear',
    method: 'get',
  })
}
/**
 * @description获取摄像头列表
 * @method getCameraList
 */
export function getCameraList() {
  return request({
    url: '/water/bigData/water-camera/list',
    method: 'get',
  })
}
/**
 * @description获取排口站点列表
 * @method getDrainList
 */
export function getDrainList(keywords: any) {
  return request({
    url: '/water/drain/bigData/listDrain',
    method: 'get',
    params: {
      keywords: keywords,
    },
  })
}

/**
 * @description 获取排口详情
 * @method getDrainDetail
 */
export function getDrainDetail(drainId: any) {
  return request({
    url: '/water/drain/bigDataGetDrainDetail',
    method: 'get',
    params: {
      drainId: drainId,
    },
  })
}

/**
 * 获取排口和排口下的监控列表
 * getDrainListCarmera
 */
export function getDrainListCarmera() {
  return request({
    url: '/water/drain/listDrainAndCamera',
    method: 'get',
  })
}

/**
 * 获取排口抓拍记录
 * getListRealTime
 */
export function getListRealTime(drainId: any, monitorId: any) {
  return request({
    url: '/water/water-camera-smart-snap/listRealTime',
    method: 'get',
    params: {
      drainId: drainId,
      monitorId: monitorId,
    },
  })
}

/**
 * 获取同比环比数据
 * getlistTwoDayByDrainId
 */
export function getlistTwoDayByDrainId(drainId: any) {
  return request({
    url: '/water/drain/monitorRecord/bigData/listTwoDayByDrainId',
    method: 'get',
    params: {
      drainId: drainId,
    },
  })
}

/**
 * 获取采样监测数据
 * getAssayRecord
 */
export function getAssayRecord(drainId: any, number: any = 10) {
  return request({
    url: '/water/drain/assayRecord/bigDataList',
    method: 'get',
    params: {
      drainId: drainId,
      number: number,
    },
  })
}

/**
 * 获取监测详情
 * getByRecordId
 */
export function getByRecordId(drainId: any, recordId: any) {
  return request({
    url: '/water/drain/monitorRecord/bigData/getByRecordId',
    method: 'get',
    params: {
      drainId: drainId,
      recordId: recordId,
    },
  })
}
/**
 * 获取排户列表
 * getDrainPerson
 */
export function getDrainPerson(keywords: any) {
  return request({
    url: '/water/drain/merchant/bigData/listByKeywords',
    method: 'get',
    params: {
      keywords: keywords,
    },
  })
}

/**
 * 获取排户详情数据
 * getDrainPersonDetail
 */
export function getDrainPersonDetail(id: any) {
  return request({
    url: '/water/drain/merchant/bigData/getDetailById',
    method: 'get',
    params: {
      id: id,
    },
  })
}
/**
 * 获取录像列表
 * playbackListApi
 */
export function playbackListApi(params: any) {
  return request({
    url: '/water/water-camera/playbackList',
    method: 'get',
    params,
  })
}
/**
 * 获取录像列表
 * playbackStartApi
 */
export function playbackStartApi(params: any) {
  return request({
    url: '/water/water-camera/playbackStart',
    method: 'get',
    params,
  })
}


// 获取排口监控列表
export function drainCameraList(drainId: string) {
  return request({
    url: '/water/bigData/drain/listCamera',
    method: 'get',
    params: { drainId },
  })
}

// 查询排口告警列表
export function drainAlarmList(params) {
  return request({
    url: '/water/bigData/drain/pageAlarm',
    method: 'get',
    params,
  })
}

// 查询联动任务列表
export function pageTask(drainId) {
  return request({
    url: '/water/bigData/drain/pageTask',
    method: 'get',
    params: { pageNum: 1, pageSize: 1000, drainId },
  })
}


// 分页查询监控ai抓拍告警
export function pageAlarm(params) {
  return request({
    url: '/water/bigData/water-camera/pageAlarm',
    method: 'get',
    params,
  })
}


// 查询监控 关联联动任务列表
export function monitorPageTask(cameraId) {
  return request({
    url: '/water/bigData/water-camera/pageTask',
    method: 'get',
    params: { pageNum: 1, pageSize: 1000, cameraId },
  })
}


// 获取任务详情
export function getTaskDetail(taskId) {
  return request({
    url: '/water/bigData/water-camera/getTaskDetail',
    method: 'get',
    params: { taskId },
  })
}

// 获取任务详情
export function getDrainTask(taskId) {
  return request({
    url: '/water/bigData/drain/getTaskDetail',
    method: 'get',
    params: { taskId },
  })
}
