<template>
  <div
    class="right-second-container"
    v-if="feedbackList && feedbackList.length"
  >
    <div class="item" v-for="item in feedbackList" :key="item.taskLogId">
      <template
        v-if="
          item.logTypeId !== 2 &&
            item.logTypeId !== 3 &&
            item.logTypeId !== 5 &&
            item.logTypeId !== 6 &&
            item.logTypeId !== 7 &&
            item.logTypeId !== 8 &&
            item.logTypeId !== 15 &&
            item.logTypeId !== 16
        "
      >
        <img class="icon" src="@/assets/patrolKanban/<EMAIL>" alt="" />
        <div class="header">
          <span class="title"
            >{{ item.departmentName ? `${item.departmentName} - ` : ""
            }}{{ item.userName }}</span
          ><span v-if="item.time">{{ item.time }}</span>
          <!-- <span class="time">{{item.}}</span> -->
        </div>
        <template v-if="item.logTypeId !== 1">
          <div class="content">
            <div class="content-text">
              巡检巡查情况：{{ item.remark || "--" }}
            </div>
            <div
              class="image-list"
              v-if="item.taskAnnexList && item.taskAnnexList.length"
            >
              <div class="img-box">
                <div
                  class="img"
                  :class="item.taskAnnexList.length === 1 ? 'one_pic' : ''"
                  v-for="(it, idx) in item.taskAnnexList"
                  :key="idx + 'taskimg'"
                  :style="{
                    display: imgFlag ? '' : idx > 1 ? 'none' : '',
                  }"
                >
                  <div
                    class="more-image"
                    v-if="idx == 1 && !imgFlag && item.taskAnnexList.length > 2"
                  >
                    +{{ item.taskAnnexList.length - 2 }}
                  </div>
                  <ComImage
                    style="width: 120px; height: 80px"
                    :src="it.annexUrl"
                    fit="cover"
                    :preview-src-list="[
                      ...item.taskAnnexList.map((i) => i.annexUrl),
                    ]"
                  >
                  </ComImage>
                </div>
              </div>
            </div>
            <div class="result-box" v-if="item.patrolIssuesManifest">
              <span class="label">本次巡查问题:</span>
              <span
                class="value"
                v-if="
                  item.patrolIssuesManifest && item.patrolIssuesManifest.name
                "
                @click="handlePreview(item.patrolIssuesManifest.pdfUrl)"
                >{{ item.patrolIssuesManifest.name }}.pdf</span
              >
              <span class="value" v-else>无</span>
            </div>
          </div>
          <div class="content_second" v-if="item.patrolIssuesManifest">
            <div style="margin-bottom: 10px">
              <span
                v-if="
                  item.patrolIssuesManifest &&
                    !item.patrolIssuesManifest.rectificationTimeLimit
                "
                >整改结果：{{ item.patrolIssuesManifest.rectificationRemark }}</span
              ><span v-else><span>立即整改：</span> <span style="color: #F95858">否</span></span>
            </div>
            <div
              v-if="
                item.patrolIssuesManifest &&
                  item.patrolIssuesManifest.rectificationTimeLimit
              "
            >
              整改时限：{{
                item.patrolIssuesManifest.rectificationTimeLimit || "--"
              }}
            </div>
            <div
              class="image-list"
              v-if="
                item.patrolIssuesManifest &&
                  item.patrolIssuesManifest.rectificationImages &&
                  item.patrolIssuesManifest.rectificationImages.length
              "
            >
              <div class="img-box">
                <div
                  class="img"
                  :class="
                    item.patrolIssuesManifest.rectificationImages.length === 1
                      ? 'one_pic'
                      : ''
                  "
                  v-for="(it, idx) in item.patrolIssuesManifest
                    .rectificationImages"
                  :key="idx + 'taskimg'"
                  :style="{
                    display: imgFlag ? '' : idx > 1 ? 'none' : '',
                  }"
                >
                  <div
                    class="more-image"
                    v-if="
                      idx == 1 &&
                        !imgFlag &&
                        item.patrolIssuesManifest.rectificationImages.length > 2
                    "
                  >
                    +{{
                      item.patrolIssuesManifest.rectificationImages.length - 2
                    }}
                  </div>
                  <ComImage
                    style="width: 120px; height: 80px"
                    :src="it.url"
                    fit="cover"
                    :preview-src-list="[
                      ...item.patrolIssuesManifest.rectificationImages.map(
                        (i) => i.url
                      ),
                    ]"
                  >
                  </ComImage>
                </div>
              </div>
            </div>
          </div>
        </template>
        <div class="content_second" v-else>
          <div>{{ item.content || "--" }}</div>
        </div>
      </template>
    </div>
  </div>
  <div
    class="right-second-container"
    style="display: flex; justify-content:center; align-items: center"
    v-else
  >
    暂无数据
  </div>
</template>

<script>
import ComImage from "@/components/image/src/main.vue";
export default {
  name: "",
  props: {
    feedbackList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      imgFlag: false,
    };
  },
  created() {},
  methods: {
    // 预览pdf
    handlePreview(url) {
      console.log(url);
      window.open(url);
    },
  },
  computed: {},
  watch: {},
  components: { ComImage },
};
</script>

<style lang="less" scoped>
.right-second-container {
  margin-top: 20px;
  // background: pink;
  height: 520px;
  overflow-y: auto;
  .item {
    position: relative;
    box-sizing: border-box;
    padding-left: 20px;
    .icon {
      position: absolute;
      left: -6px;
      top: -5px;
      width: 32px;
      height: 32px;
    }

    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      box-sizing: border-box;
      padding: 0 10px;
      .title {
        font-size: 15px;
        font-family: PingFang SC;
        font-weight: 500;
        color: #d6f4ff;
      }
      .time {
        font-size: 14px;
        font-family: PingFang SC;
        font-weight: 400;
        color: #93b2c7;
      }
    }
    &:not(:first-child) {
      .icon {
        top: 12px;
      }
      .header {
        padding-top: 15px;
      }
    }
    &::before {
      content: "";
      width: 3px;
      height: 100%;
      border: 3px solid;
      border-image: linear-gradient(180deg, #22b4ff, #0aa4d7) 10 10;
      background: linear-gradient(0deg, #000000 0%, #ffffff 100%);
      opacity: 0.3;
      position: absolute;
      left: 7px;
      top: 10px;
    }
    .content {
      width: 372px;
      margin-top: 15px;
      // background: url("~@/assets/patrolKanban/<EMAIL>") no-repeat center;
      // background-size: cover;
      box-sizing: border-box;
      // padding: 20px;
      border: 1px solid;
      padding: 20px;
      // opacity: 0.4;
      background: linear-gradient(
        130deg,
        #075aa848 0%,
        rgba(5, 80, 151, 0.1) 100%
      );
      border-image: linear-gradient(-49deg, #000000, #187cdfd0) 10 10;
      .content-text {
        font-size: 14px;
        font-family: PingFang SC;
        font-weight: 400;
        color: #d6f4ff;
      }
      .image-list {
        width: 100%;
        height: 80px;
      }
      .result-box {
        display: flex;
        margin-top: 15px;
        font-size: 14px;
        font-family: PingFang SC;
        font-weight: 400;
        color: #d6f4ff;
        .value {
          margin-left: 10px;
          color: #44a3d9;
          flex: 1;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }
      }
    }
    .content_second {
      width: 372px;
      // margin-left: 4px;
      margin-top: 15px;
      // height: 80px;
      background: linear-gradient(
        130deg,
        #075aa848 0%,
        rgba(5, 80, 151, 0.1) 100%
      );
      border: 1px solid;
      padding: 20px;
      // opacity: 0.4;
      border-image: linear-gradient(-49deg, #000000, #187cdfd0) 10 10;
    }
  }
  .img-box {
    display: flex;
    flex-wrap: wrap;
    overflow-x: scroll;
    margin-top: 24px;
    // height:80px;
    &::-webkit-scrollbar {
      display: none;
    }
    .img {
      width: 120px;
      height: 120px;
      overflow: hidden;
      // max-width: 130px;
      // max-height: 80px;
      margin-right: 10px;
      margin-bottom: 10px;
      position: relative;
      .more-image {
        position: absolute;
        width: 100%;
        height: 80px;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        color: #fff;
        font-size: 42px;
        z-index: 2;
        cursor: pointer;
        pointer-events: none;
      }
      &:not(:first-child) {
        margin-left: 10px;
      }
    }
    .one_pic {
      width: 130px;
      height: 86px;
    }
  }
}
</style>
