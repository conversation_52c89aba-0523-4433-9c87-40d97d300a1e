<style lang="less" scoped>
.home-map-top {
  .home-map-right {
    .title {
      // text-shadow: 0 0 5px blue, 0 0 5px blue;
      color: #cccccc;
      font-size: 0.2rem;
      margin-bottom: 0.2rem;
    }
    .sub-title-content {
      height: 3.5rem;
      // border: 1px solid #fff;
    }
    .car-type {
      height: 4rem;
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      > div {
        width: 33.33%;
      }
    }
    .car-number {
      height: 3rem;
    }
  }
}
section.vehicles-page {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
}
</style>
<template>
  <section class="vehicles-page">
    <div class="comment-content">
      <div class="title">出勤车辆类型统计</div>
      <div class="sub-title-content car-type">
        <div v-for="(item, index) in carListData" :key="index">
          <SitePie
            :id="'carType' + index"
            :width="'1.4rem'"
            :height="'1.4rem'"
            :propData="item"
          />
        </div>
      </div>
    </div>
    <div class="comment-content">
      <div class="title">出勤车辆本月总里程数</div>
      <div class="sub-title-content car-number">
        <CarLineChartDashed
          :id="'waterMonitor'"
          :width="'4.5rem'"
          :height="'3rem'"
          :propData="carNumber"
          :smooth="true"
        />
      </div>
    </div>
  </section>
</template>
<script lang="ts">
interface LineChartData {
  bottomList: string[];
  dataList: string | number[];
  unit?: string;
  warnValue?: number;
  miniValue?: number;
  maxValue?: number;
}
import { Component, Vue, Watch } from "vue-property-decorator";
import SitePie from "@/components/Charts/SitePie.vue";
import CarLineChartDashed from "@/components/Charts/CarLineChartDashed.vue";
import { getOnlieCar, getCarData } from "@/api/headvily-pollution";
import { getHpeSum } from "@/api/homeMap";
@Component({
  name: "VehiclesPage",
  components: {
    SitePie,
    CarLineChartDashed
  }
})
export default class extends Vue {
  private carTypeList: any[] = [
    {
      name: "洗扫车 36辆",
      number: 6,
      total: 36,
      startColor: "rgba(64, 119, 200, 1)",
      endColor: "rgba(33, 185, 241, 1)"
    },
    {
      name: "雾炮车 32辆",
      number: 9,
      total: 32,
      startColor: "rgba(64, 119, 200, 1)",
      endColor: "rgba(33, 185, 241, 1)"
    },
    {
      name: "垃圾清运车 36辆",
      number: 12,
      total: 36,
      startColor: "rgba(64, 119, 200, 1)",
      endColor: "rgba(33, 185, 241, 1)"
    },
    {
      name: "执法车 36辆",
      number: 9,
      total: 36,
      startColor: "rgba(64, 119, 200, 1)",
      endColor: "rgba(33, 185, 241, 1)"
    },
    {
      name: "洒水车 28辆",
      number: 4,
      total: 28,
      startColor: "rgba(64, 119, 200, 1)",
      endColor: "rgba(33, 185, 241, 1)"
    },
    {
      name: "餐厨清运车 26辆",
      number: 10,
      total: 26,
      startColor: "rgba(64, 119, 200, 1)",
      endColor: "rgba(33, 185, 241, 1)"
    }
  ];
  // 出勤车辆本月总里程数
  private carNumber: LineChartData = {
    bottomList: [
      "洗扫车",
      "雾炮车",
      "执法车",
      "洒水车",
      "垃圾清运车",
      "餐厨清运车"
    ],
    dataList: [488, 413, 446, 224, 493, 517],
    unit: "km"
  };
  private carListData:any [] = []
  mounted() {
    this.getOnlieCar()
    this.getCarData()
    // 1
  }
  private getOnlieCar() {
    getOnlieCar().then(res => {
      const data = (res.data.data || []).map((item:any) => {
        return {
          type: item.typeId || 1,
          longitude: item.locLongitude,
          latitude: item.locLatitude,
          license: item.carBrand,
          carId: item.id,
          deptId: item.deptId
        }
      })
      /* eslint-disable @typescript-eslint/ban-ts-ignore */
      //@ts-ignore
      this.$bus.emit('sendCarList', data)
    })
  }
  private getCarData() {
    getCarData().then(res => {
      const data = res.data.data
      console.log(data, 12)
      this.carListData = (data.typeCountList || []).map((item:any) => {
        return {
          name: item.typeName+' ' +item.count + '辆',
          number: item.online,
          total: item.count,
          startColor: "rgba(64, 119, 200, 1)",
          endColor: "rgba(33, 185, 241, 1)"
        }
      })
      this.carNumber = {
        bottomList: (data.typeMileageList || []).map((item:any) => item.typeName),
        dataList: (data.typeMileageList || []).map((item:any) => item.mileage || 0),
        unit: "km"
      }
    })
  }
}
</script>
