<template>
  <div id="video-tem-ali" style="width: 370px" ref="video-tem-ali">
    <div
      v-if="vidoeUrl"
      :id="'aliplayer_' + playerId"
      class="prism-player"
      style="width: 370px; height: 260px"
    ></div>
    <div
      v-else
      style="
        width: 370px;
        height: 260px;
        line-height: 1.4rem;
        font-size: 0.16rem;
        text-align: center;
      "
      >该摄像头暂不在线</div
    >
  </div>
</template>
<script setup name="video-tem-ali">
  // const { proxy } = getCurrentInstance()
  // const router = useRouter()
  // const route = useRoute()
  const videoTemAli = ref()
  defineExpose({
    dom: videoTemAli,
  })
</script>
<script>
// import LivePlayer from '@liveqing/liveplayer'
// import VueAliplayer from "vue-aliplayer";
export default {
  name: 'LivePlayer',
  // components: { "ali-player": VueAliplayer },
  props: {
    playerId: {
      // eslint-disable-next-line vue/require-prop-type-constructor
      type: Number || String,
      default: 1,
    },
    vidoeUrl: {
      type: String,
      default: '',
    },
    snapUrl: {
      type: String,
      default: '',
    },
    online: {
      type: Boolean,
      default: true,
    },
    type: {
      type: Number,
      default: 1,
    },
    outPage: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      // playerId: 'aliplayer_' + Math.random().toString(36).substr(2),
      scriptTagStatus: 0,
      isReload: false,
      instance: null,
      player: null,
    }
  },
  watch: {
    playerId: {
      handler(newVal, oldVal) {
        console.log(newVal,'--------------------------newVal');
        if (newVal) {
          this.$nextTick(() => {
            this.initVideo()
          })
        }
      },
    },
  },
  mounted() {
    this.initVideo()
  },
  beforeDestroy() {
    this.player.pause()
    this.player.dispose()
  },
  methods: {
    initVideo() {
      if (this.vidoeUrl) {
        if (this.player) {
          this.player.dispose()
        }
        // eslint-disable-next-line no-undef
        this.player = new Aliplayer({
          id: `aliplayer_${this.playerId}`,
          width: '100%',
          useH5Prism: true,
          // 支持播放地址播放,此播放优先级最高
          source: this.vidoeUrl,
          autoplay: true,
          isLive: true,
          cover: this.snapUrl,
          rePlay: false,
          // playsinline: true,
          // preload: false,
          // controlBarVisibility: "hover",
          skinLayout: [
            {
              name: 'H5Loading',
              align: 'cc',
            },
            {
              name: 'thumbnail',
            },
            {
              name: 'controlBar',
              align: 'blabs',
              x: 0,
              y: 0,
              children: [
                {
                  name: 'playButton',
                  align: 'tl',
                  x: 15,
                  y: 12,
                },
                {
                  name: 'fullScreenButton',
                  align: 'tr',
                  x: 10,
                  y: 12,
                },
                {
                  name: 'volume',
                  align: 'tr',
                  x: 5,
                  y: 10,
                },
              ],
            },
          ],
        })
      }
    },
  },
}
</script>
<style lang="postcss" scoped>
</style>