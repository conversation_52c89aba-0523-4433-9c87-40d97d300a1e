<template>
  <div class="dialog-content-box">
    <a-spin :spinning="spinning">
      <DialogContentContainer
        v-if="!curTypeData.dotShow && curData"
        :date="curContentInfoData.updateTime"
        :tabbar="curTypeData.tabbar"
        @tabbarClick="tabbarClick"
      >
        <component
          :is="activeComponentName"
          :echartData="curContentInfoData.echartData"
          :echartDataArray="curContentInfoData.echartDataArray"
          :chartX="curContentInfoData.chartX"
          :nameArr="curContentInfoData.chartLegendNameArr"
          :dataType="curContentInfoData.dataType"
          :defaultInfoData="curContentInfoData.defaultInfoData"
          :pollutionMonitorData="curContentInfoData.pollutionMonitorData"
          :videoBoxData="curContentInfoData.videoBoxData"
          :dialogElectronicGunData="curContentInfoData.dialogElectronicGunData"
        ></component>
      </DialogContentContainer>

      <!-- <a-empty description="此站点暂无数据" v-else /> -->
      <div v-else>
        <div class="empty">
          <a-spin size="large" class="loading" />
          <!-- <div class="text">摄像头不在线</div> -->
          <img src="../../../../assets/images/novideo.png" alt="" />
        </div>
      </div>
    </a-spin>
  </div>
</template>

<script>
import DefaultInfo from './components/defaultInfo.vue'
import DialogTrendLine from './components/dialogTrendLine.vue'
import DialogElectronicGun from './components/dialogElectronicGun.vue'
import DialogPollutionMonitor from './components/dialogPollutionMonitor.vue'
import DialogVideoBox from './components/dialogVideoBox.vue'
import WaterPlants from './components/waterPlants/index.vue'
import DialogContentContainer from './components/dialogContentContainer.vue'
import { getMapDialogInfo } from '@/api/emergencyDetails/emergencyEvent'

export default {
  name: 'emergencyCommandDialogContent',
  components: {
    DefaultInfo,
    DialogTrendLine,
    DialogElectronicGun,
    DialogPollutionMonitor,
    DialogVideoBox,
    WaterPlants,
    DialogContentContainer,
  },
  props: {
    // 当前marker的数据
    data: {
      type: Object,
      default: () => ({}),
    },
  },
  watch: {
    data: {
      handler(data) {
        console.log('新的锚点数据', data)
      },
      immediate: true,
    },
    // 监听当前类型数据变化 ==》 进行数据更新
    curTypeData: {
      handler(data) {
        console.log('当前弹窗的结构数据', data)
        if (!data) return

        const titleEle = document.querySelector(
          '.dialog-container .header .title'
        )
        if (titleEle) titleEle.innerText = data.cardName

        if (data.tabbar && data.tabbar.length) {
          // 切换组件
          activeComponentName.value = data.tabbar[0].componentName

          spinning.value = true
          console.log(
            { type: data.name, id: props.data.id },
            '{ type: data.name, id: props.data.id }'
          )
          // 请求数据
          getMapDialogInfo({ type: data.name, id: props.data.id })
            .then((res) => {
              console.log('获取弹窗数据成功', res)
              const { code, data } = res
              if (code === 200) {
                curData.value = data
              }
            })
            .catch((err) => {
              console.log('获取弹窗数据失败', err)
            })
            .finally(() => {
              console.log('获取弹窗数据请求完成')
              spinning.value = false
            })
        }
      },
      deep: true,
      immediate: true,
    },
    curData: {
      handler(data) {
        console.log('获取到了数据', data)
        // 组合成对应的数据结构
      },
    },
  },
  computed: {
    // 根据类型获取当前所需的结构和数据
    curTypeData() {
      const { data } = this
      return typeMap[data.type]
    },
    // 当前要显示的主体内容
    curContentInfoData() {
      const { name } = this.curTypeData
      switch (name) {
        case '医院':
          return this.creatHospitalData(curData.value)
        case '汽修':
          return this.createCarFix(curData.value)
        case '印刷':
          return this.createPrintingData(curData.value)
        case '餐饮':
          return this.createRestaurant(curData.value)
        case '加油站':
          return this.createGas(curData.value)
        case '工地':
          return this.createBuildingData(curData.value)
        case '仓库':
          return this.createStorageData(curData.value)
        case '音柱':
          return this.createMicrophoneData(curData.value)
        case '摄像头':
          return this.createCameraData(curData.value)
        case '声光报警器':
          return this.createSGBJData(curData.value)
        case '自来水厂':
          return this.createWaterWorkData(curData.value)

        default:
          return {}
      }
    },
  },
  data() {
    return {
      // 类型对照
      typeMap: {
        1: {
          name: '医院',
          cardName: '医疗机构详情',
          tabbar: [
            {
              name: '医院信息',
              componentName: 'defaultInfo',
              data: {},
            },
            {
              name: '污染监测',
              componentName: 'dialogPollutionMonitor',
              data: {},
            },
          ],
        },
        2: {
          name: '重污',
          cardName: '重污机构详情',
          tabbar: [],
          dotShow: true,
        },
        3: {
          name: '汽修',
          cardName: '汽修企业详情',
          tabbar: [
            {
              name: '商户信息',
              componentName: 'defaultInfo',
              data: {},
            },
            {
              name: '监测趋势',
              componentName: 'dialogTrendLine',
              data: {},
            },
            {
              name: '视频监控',
              componentName: 'dialogVideoBox',
              data: {},
            },
          ],
        },
        4: {
          name: '印刷',
          cardName: '印刷企业详情',
          tabbar: [
            {
              name: '企业信息',
              componentName: 'defaultInfo',
              data: {},
            },
            {
              name: '监测趋势',
              componentName: 'dialogTrendLine',
              data: {},
            },
            {
              name: '视频监控',
              componentName: 'dialogVideoBox',
              data: {},
            },
          ],
        },
        5: {
          name: '餐饮',
          cardName: '餐饮企业详情',
          tabbar: [
            {
              name: '商户信息',
              componentName: 'defaultInfo',
              data: {},
            },
            {
              name: '监测趋势',
              componentName: 'dialogTrendLine',
              data: {},
            },
          ],
        },
        6: {
          name: '加油站',
          cardName: '加油站详情',
          tabbar: [
            {
              name: '商户信息',
              componentName: 'defaultInfo',
              data: {},
            },
            {
              name: '监测趋势',
              componentName: 'dialogTrendLine',
              data: {},
            },
            {
              name: '电枪监控',
              componentName: 'dialogElectronicGun',
              data: {},
            },
          ],
        },
        7: {
          name: '工地',
          cardName: '在建工地详情',
          tabbar: [
            {
              name: '工地信息',
              componentName: 'defaultInfo',
              data: {},
            },
            {
              name: '监测趋势',
              componentName: 'dialogTrendLine',
              data: {},
            },
          ],
        },
        8: {
          name: '仓库',
          cardName: '物资仓库详情',
          tabbar: [
            {
              name: '仓库信息',
              componentName: 'defaultInfo',
              data: {},
            },
          ],
        },
        9: {
          name: '音柱',
          cardName: '音视频详情',
          tabbar: [
            {
              name: '设备信息',
              componentName: 'defaultInfo',
              data: {},
            },
          ],
        },
        10: {
          name: '摄像头',
          cardName: '监控详情',
          tabbar: [
            {
              name: '',
              componentName: 'dialogVideoBox',
              data: {},
            },
          ],
        },
        11: {
          name: '自来水厂',
          cardName: '自来水厂详情',
          tabbar: [
            {
              name: '企业信息',
              componentName: 'defaultInfo',
              data: {},
            },
            {
              name: '监测趋势',
              componentName: 'waterPlants',
              data: {},
            },
          ],
        },
        12: {
          name: '声光报警器',
          cardName: '声光报警器详情',
          tabbar: [
            {
              name: '设备信息',
              componentName: 'defaultInfo',
              data: {},
            },
          ],
        },
      },
      spinning: false,
      // 当前使用的组件名称
      activeComponentName: 'defaultInfo',
      // 默认详情页数
      defaultInfoData: null,
      pollutionMonitorData: null,
      // 存储获取到的当前弹窗对应的数据
      curData: null,
    }
  },
  methods: {
    /**
     * tabbar点击切换事件
     */
    tabbarClick(info) {
      console.log('tabbar点击事件', info)
      this.activeComponentName = info.componentName
    },
    /**
     * 构造医院数据
     * @param {Object} data 获取到的数据
     */
    creatHospitalData(data) {
      const { detail, countOfDischarge } = data
      const { countOfWastewater, countOfGas, countOfWaste, updateTime } =
        countOfDischarge
      return {
        defaultInfoData: {
          type: 'yiyuan',
          name: detail.heavilyPollutingEnterpriseName,
          address: detail.heavilyPollutingEnterpriseAddress,
          infoList: [
            {
              name: '行政区划',
              value: detail.administrativeDivision,
              warnValue: '',
            },
            {
              name: '企业类型',
              value: detail.heavilyPollutingEnterpriseType,
              warnValue: '',
            },
            {
              name: '企业状态',
              value: detail.heavilyPollutingEnterpriseStatus,
              warnValue: '',
            },
            {
              name: '行业类型',
              value: detail.industryCategory,
              warnValue: '',
            },
            {
              name: '环保负责人',
              value: detail.principal,
              warnValue: '',
            },
            {
              name: '排污许可证',
              value: detail.heavilyPollutingEnterpriseType,
              warnValue: '',
            },
            {
              name: '负责人电话',
              value: detail.principalPhoneNumber,
              warnValue: '',
            },
            {
              name: '环境应用预案',
              value: detail.haveEnvironmentalEmergencyPlan,
              warnValue: '',
            },
          ],
        },
        pollutionMonitorData: [countOfWastewater, countOfGas, countOfWaste],
        updateTime,
      }
    },
    /**
     * 构造汽修数据
     * @param {Object} data 获取到的数据
     */
    createCarFix(data) {
      const { json, updateTime, garageCameraList } = data
      let list = []
      if (json && json.list) list = json.list

      // TODO：多条视频切换机制
      let videoItem = garageCameraList[0]
      if (!videoItem) videoItem = {}

      return {
        defaultInfoData: {
          type: 'qiye',
          name: data.garageName,
          address: data.garageAddress,
          infoList: [
            {
              name: '所在街道',
              value: data.streetName,
              warnValue: '',
            },
            {
              name: '联系人',
              value: data.contactPerson,
              warnValue: '',
            },
            {
              name: '是否超标',
              value: data.vocExceedingStandard ? '' : '未超标',
              warnValue: data.vocExceedingStandard ? '超标' : '',
            },
            {
              name: '联系电话',
              value: data.phoneNumber,
              warnValue: '',
            },
          ],
        },
        echartData: list.map((item) => item.vocAvgConcentration),
        chartX: list.map((item) => item.monitorTime),
        chartLegendNameArr: ['VOC浓度'],
        updateTime,
        videoBoxData: {
          list: [],
          video: {
            videoUrl: videoItem.liveUrl,
            online: videoItem.online,
          },
        },
      }
    },
    /**
     * 构造印刷数据
     * @param {Object} data 获取到的数据
     */
    createPrintingData(data) {
      const {
        printFactoryName,
        printFactoryAddress,
        streetName,
        contactPerson,
        vocExceedingStandard,
        phoneNumber,
        jsonObject,
        updateTime,
        printFactoryCamera,
      } = data

      let list = []
      if (jsonObject && jsonObject.list) list = jsonObject.list

      let videoItem = printFactoryCamera || {}

      return {
        defaultInfoData: {
          type: 'qiye',
          name: printFactoryName,
          address: printFactoryAddress,

          infoList: [
            {
              name: '所在街道',
              value: streetName,
              warnValue: '',
            },
            {
              name: '联系人',
              value: contactPerson,
              warnValue: '',
            },
            {
              name: '是否超标',
              value: vocExceedingStandard ? '' : '未超标',
              warnValue: vocExceedingStandard ? '超标' : '',
            },
            {
              name: '联系电话',
              value: phoneNumber,
              warnValue: '',
            },
          ],
        },
        echartData: list.map((item) => item.vocAvgConcentration),
        chartX: list.map((item) => item.monitorTime),
        chartLegendNameArr: ['VOC浓度'],
        videoBoxData: {
          list: [],
          video: {
            ezopen: videoItem.ezopen,
            accessToken: videoItem.accessToken,
            online: videoItem.online,
          },
        },
        updateTime,
      }
    },
    /**
     * 构造餐饮数据
     * @param {Object} data 获取到的数据
     */
    createRestaurant(data) {
      const {
        restaurantName,
        restaurantAddress,
        streetName,
        contactPerson,
        phoneNumber,
        isAlarm,
        json,
      } = data

      let list = []
      if (json) list = json.list

      return {
        defaultInfoData: {
          type: 'qiye',
          name: restaurantName,
          address: restaurantAddress,

          infoList: [
            {
              name: '所在街道',
              value: streetName,
              warnValue: '',
            },
            {
              name: '联系人',
              value: contactPerson,
              warnValue: '',
            },
            {
              name: '是否超标',
              value: isAlarm ? '' : '未超标',
              warnValue: isAlarm ? '超标' : '',
            },
            {
              name: '联系电话',
              value: phoneNumber,
              warnValue: '',
            },
          ],
        },
        echartData: list.map((item) => item.concentration),
        chartX: list.map((item) => item.monitorTime),
        chartLegendNameArr: ['油烟浓度'],
        dataType: 'mg/m³',
      }
    },
    /**
     * 构造加油站数据
     * @param {Object} data 获取到的数据
     */
    createGas(data) {
      const {
        gasName,
        address,
        streetName,
        contactUser,
        mobile,
        updateTime,
        gasConcentrationState,
        tightness,
        tankState,
        environmentList,
        dispenserList,
      } = data

      const tankStateMap = {
        0: '正常',
        1: '预警',
        2: '报警',
      }

      const tightnessMap = {
        0: '正常',
        1: '预警',
        2: '报警',
        N: '无效',
      }
      const list = environmentList || []
      const dialogElectronicGunData = []
      dispenserList.forEach((item) => {
        item.fuelGunList.forEach((ele) => {
          dialogElectronicGunData.push({
            name: `电枪编号${ele.fuelGunName}`,
            content: [`加油油品：${ele.oils}`, `油品编号：${ele.oilGrade}`],
            percent: ele.gasLiquidRatio,
            percentName: '气液比',
          })
        })
      })

      return {
        defaultInfoData: {
          type: 'qiye',
          name: gasName,
          address,
          infoList: [
            {
              name: '所在街道',
              value: streetName,
              warnValue: '',
            },
            {
              name: '联系人',
              value: contactUser,
              warnValue: '',
            },
            {
              name: '油气浓度状态',
              value: gasConcentrationState ? '' : '正常',
              warnValue: gasConcentrationState ? '报警' : '',
            },
            {
              name: '联系电话',
              value: mobile,
              warnValue: '',
            },
            {
              name: '油罐状态',
              value: tankState ? '' : tankStateMap[tankState],
              warnValue: tankState ? tankStateMap[tankState] : '',
            },
            {
              name: '密闭性',
              value:
                tightnessMap[tightness] !== '正常'
                  ? ''
                  : tightnessMap[tightness],
              warnValue:
                tightnessMap[tightness] !== '正常'
                  ? tightnessMap[tightness]
                  : '',
            },
          ],
        },
        echartData: list.map((item) => item.oilConcentration),
        chartX: list.map((item) => item.monitorTime),
        dataType: 'mg/m³',
        chartLegendNameArr: ['油气值'],
        updateTime,
        dialogElectronicGunData,
      }
    },
    /**
     * 构造加油站数据
     * @param {Object} data 获取到的数据
     */
    createBuildingData(data) {
      let { detail, trend } = data

      if (!detail) detail = {}
      if (!trend) trend = {}

      const {
        projectName,
        constructionSiteAddress,
        constructionCompanyName,
        projectType,
        reviewDate,
        safeRecordNum,
        projectState,
        principal,
        linkPhone,
        monitorValue,
        pollutantState,
      } = detail

      const echartDataArray = []
      const pm10 = trend.data || []
      const npm10 = trend.npm10 || []

      echartDataArray.push(pm10.map((item) => item.value))
      echartDataArray.push(npm10.map((item) => item.value))

      const stateMap = {
        1: '正常',
        2: '暂停',
        3: '竣工',
        4: '已拆机',
        0: '删除',
      }

      return {
        defaultInfoData: {
          type: 'qiye',
          name: projectName,
          address: constructionSiteAddress,
          infoList: [
            {
              name: 'PM10',
              value: pollutantState ? '' : `${monitorValue} ug/m³`,
              warnValue: pollutantState ? monitorValue : '',
            },
            {
              name: '联系人',
              value: principal,
              warnValue: '',
            },
            {
              name: '工程状态',
              value: stateMap[projectState],
              warnValue: '',
            },
            {
              name: '联系电话',
              value: linkPhone,
              warnValue: '',
            },
            {
              name: '工地类型',
              value: '砂浆混凝土',
              warnValue: '',
            },
            {
              name: '工程类型',
              value: projectType,
              warnValue: '',
            },
            {
              name: '备案号',
              value: safeRecordNum,
              warnValue: '',
            },
            {
              name: '备案时间',
              value: reviewDate,
              warnValue: '',
            },
            {
              name: '施工单位',
              value: constructionCompanyName,
              warnValue: '',
            },
          ],
        },
        echartDataArray,
        chartX: pm10.map((item) => item.monitorTime),
        chartLegendNameArr: ['pm10', 'npm10'],
        dataType: 'ug/m³',
      }
    },
    /**
     * 构造仓库数据
     * @param {Object} data 获取到的数据
     */
    createStorageData(data) {
      const { name, address, userName, phone, streetName } = data
      return {
        defaultInfoData: {
          type: 'cangku',
          name,
          address,
          infoList: [
            {
              name: '仓库管理员',
              value: userName,
              warnValue: '',
            },
            {
              name: '所属单位',
              value: streetName,
              warnValue: '',
            },
            {
              name: '联系电话',
              value: phone,
              warnValue: '',
            },
          ],
        },
      }
    },

    /**
     * 构造音柱数据
     * @param {Object} data 获取到的数据
     */
    createMicrophoneData(data) {
      const { name, address, streetName, state } = data

      return {
        defaultInfoData: {
          type: 'yinpin',
          name,
          address,
          infoList: [
            {
              name: '所在区域',
              value: streetName,
              warnValue: '',
            },
            {
              name: '设备状态',
              value: state ? '在线' : '',
              warnValue: state ? '' : '离线',
            },
          ],
        },
      }
    },

    /**
     * 构造声光报警器数据
     * @param {Object} data 获取到的数据
     */
    createSGBJData(data) {
      const { name, address, streetName, state } = data

      return {
        defaultInfoData: {
          type: 'shengguang',
          name,
          address,
          infoList: [
            {
              name: '所在区域',
              value: streetName,
              warnValue: '',
            },
            {
              name: '设备状态',
              value: state ? '在线' : '',
              warnValue: state ? '' : '离线',
            },
          ],
        },
      }
    },

    /**
     * 构造摄像头数据
     * @param {Object} data 获取到的数据
     */
    createCameraData(data) {
      let { name, address, streetName, state, camera } = data

      if (!camera) camera = {}

      return {
        videoBoxData: {
          list: [
            {
              name: '摄像头名称',
              value: name,
              warnValue: '',
            },
            {
              name: '所在区域',
              value: streetName,
              warnValue: '',
            },
            {
              name: '设备状态',
              value: state ? '在线' : '',
              warnValue: state ? '' : '离线',
            },
            {
              name: '所在地址',
              value: address,
              warnValue: '',
            },
          ],
          video: {
            videoUrl: camera.flv,
            online: state,
          },
        },
      }
    },

    /**
     * 构造自来水厂数据
     * @param {Object} data 获取到的数据
     */
    createWaterWorkData(data) {
      let { detail, record } = data
      // console.log('data--------------728', Object.keys(record));
      if (!detail) detail = {}
      if (!record) record = []

      const {
        heavilyPollutingEnterpriseName,
        heavilyPollutingEnterpriseAddress,
        administrativeDivision,
        heavilyPollutingEnterpriseType,
        heavilyPollutingEnterpriseStatus,
        principal,
        haveSewageDischargePermission,
        principalPhoneNumber,
        haveEnvironmentalEmergencyPlan,
        industryCategory,
      } = detail
      return {
        defaultInfoData: {
          type: 'qiye',
          name: heavilyPollutingEnterpriseName,
          address: heavilyPollutingEnterpriseAddress,
          infoList: [
            {
              name: '行政区域',
              value: administrativeDivision || '—',
              warnValue: '',
            },
            {
              name: '企业类型',
              value: heavilyPollutingEnterpriseType || '—',
              warnValue: '',
            },
            {
              name: '企业状态',
              value: heavilyPollutingEnterpriseStatus === '正常' ? '正常' : '',
              warnValue:
                heavilyPollutingEnterpriseStatus === '正常' ? '' : '异常',
            },
            {
              name: '环保负责人',
              value: principal || '—',
              warnValue: '',
            },
            {
              name: '排污许可证',
              value: haveSewageDischargePermission || '—',
              warnValue: '',
            },
            {
              name: '联系电话',
              value: principalPhoneNumber,
              warnValue: '',
            },
            {
              name: '环境应急预案',
              value: haveEnvironmentalEmergencyPlan || '—',
              warnValue: '',
            },
            {
              name: '',
              value: '',
              warnValue: '',
            },
            {
              name: '行业类型',
              value: industryCategory || '—',
              warnValue: '',
            },
          ],
        },
        echartData: {
          ph: record.PH.map((item) => item.value),
          total_phosphorus: record.总磷.map((item) => item.value),
          nh3n: record.氨氮.map((item) => item.value),
          dissolved_oxygen: record.溶解氧.map((item) => item.value),
          codmn: record.高锰酸盐指数.map((item) => item.value),
        },
        chartX: {
          ph: record.PH.map((item) => item.monitorTime),
          total_phosphorus: record.总磷.map((item) => item.monitorTime),
          nh3n: record.氨氮.map((item) => item.monitorTime),
          dissolved_oxygen: record.溶解氧.map((item) => item.monitorTime),
          codmn: record.高锰酸盐指数.map((item) => item.monitorTime),
        },
        dataType: 'mg/L',
        chartLegendNameArr:
          {
            ph: 'PH',
            total_phosphorus: '总磷',
            nh3n: '氨氮',
            dissolved_oxygen: '溶解氧',
            codmn: '高锰酸盐指数',
          } || {},
      }
    },
  },
}
</script>


<style lang="less" scoped>
:deep(.ant-empty) {
  color: #fff;
}
.empty {
  width: 400px;
  height: 240px;
  margin-bottom: 10px;
  position: relative;
  img {
    width: 100%;
    height: 100%;
    margin-bottom: 4px;
  }
  .loading {
    position: absolute;
    display: none;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  .text {
    color: #fff;
    font-size: 16px;
    text-align: center;
    position: absolute;
    top: 54.5%;
    left: 50%;
    transform: translate(-50%, 0%);
  }
}
</style>
