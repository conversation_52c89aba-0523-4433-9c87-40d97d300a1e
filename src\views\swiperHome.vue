<style lang="less" scoped>
.home-main-one {
  width: 100%;
  position: relative;
  .construction-plant-dep {
    position: absolute;
    top: 0;
    right: 0;
    height: calc(1080px - 2rem);
    width: 4.6rem;
    padding: 0.3rem 0.25rem 0;
    background-color: rgba(6, 55, 92, 0.75);
    z-index: 9999;
    .swiper-container-dep {
      position: relative;
      width: 100%;
      height: 100%;
      overflow: hidden;
      top: 0.3rem;
    }
    .swiper-slide {
      text-align: center;
      display: flex;
      justify-content: center;
      align-items: center;
      transition: 300ms;
      transform: scale(0.8);
      width: 1.3rem;
      height: 1.46rem;
      // background: url('../assets/recheck/<EMAIL>') no-repeat;
      // background-size: 100%;
      position: relative;
      z-index: 1;
      overflow: hidden;
      // box-sizing: border-box;
      // border: 4px solid transparent;
      // border-image: url('../assets/recheck/<EMAIL>') 8 8 round;
      // border-image-source: url("../assets/recheck/<EMAIL>");
      // border-image-slice: 10 fill;
      // border-image-repeat: round;
      .img_box {
        width: 120px;
        height: 135px;
        overflow: hidden;
        position: absolute;
        top: 6px;
        left: 11.5px;
      }
      .img_item {
        height: 1.35rem;
        width: 2.4rem;
        position: absolute;
        left: -0.45rem;
        top: 0;
      }
      .img_bg {
        position: absolute;
        width: 140px;
        height: 150px;
        top: -1px;
        left: 2px;
      }
      .img_index {
        position: absolute;
        color: #fff;
        top: 0;
      }
    }
    .swiper-slide-active,
    .swiper-slide-duplicate-active {
      transform: scale(1);
    }
    .swiper_line {
      width: 100%;
      position: relative;
      img {
        width: 100%;
        height: 60px;
      }
      .line_num {
        position: absolute;
        top: 10px;
        .item {
          width: 35px;
          position: absolute;
          text-align: center;
          &::after {
            content: "";
            position: absolute;
            width: 6px;
            height: 6px;
            background: #90a0ab;
            border-radius: 50%;
            top: -5px;
            left: 15px;
          }
          &:nth-child(3) {
            &::after {
              content: "";
              position: absolute;
              width: 22px;
              height: 8px;
              background: linear-gradient(90deg, #77ebf5 0%, #1465c2 100%);
              border-radius: 4px;
              top: -6px;
              left: 6px;
            }
          }
        }
      }
    }
    .key_position {
      width: 100%;
      height: 180px;
      margin-top: 18px;
      display: flex;
      &_map {
        flex: 1;
        display: flex;
        img {
          width: 25px;
          height: 100%;
        }
        &_map {
          flex: 1;
          border: 7px solid rgba(9, 46, 94, 0.9);
          box-sizing: border-box;
        }
      }
      &_table {
        width: 210px;
        max-height: 180px;
        box-sizing: border-box;
        border: 1px solid #0d355a;
        margin-left: 15px;
        &_header {
          font-size: 14px;
          font-family: Source Han Sans CN;
          font-weight: 500;
          color: #38bafe;
          height: 40px;
          line-height: 40px;
          box-sizing: border-box;
          display: flex;
          text-align: center;
          background: linear-gradient(
            90deg,
            rgba(11, 40, 107, 0.3) 0%,
            rgba(11, 40, 107, 1) 50%,
            rgba(11, 40, 107, 0.3) 100%
          );
          &_item {
            width: 60px;
            box-sizing: border-box;
            border-right: 1px solid #0d355a;
            overflow: hidden; // 超出边框外隐藏
            text-overflow: ellipsis; // 属性规定当文本溢出包含元素时发生的事情。
            white-space: nowrap; // 规定段落中的文本不进行换行
            &:nth-child(1) {
              width: 90px;
            }
          }
        }
        &_body {
          max-height: 140px;
          box-sizing: border-box;
          border-bottom: 1px solid #0d355a;
          overflow-y: auto;
          overflow-x: hidden;
          &::-webkit-scrollbar-track {
            background: rgba(57, 177, 255, 0.16);
          }
          &::-webkit-scrollbar-thumb {
            background: rgba(57, 177, 255, 0.26);
          }
        }
        &_cell {
          height: 28px;
          width: 210px;
          line-height: 28px;
          font-size: 12px;
          font-family: PingFang SC;
          font-weight: 300;
          color: #d8e8fe;
          box-sizing: border-box;
          display: flex;
          text-align: center;
          background: transparent;
          border: 0px;
          transform: scale(1);
          &:nth-child(even) {
            background: linear-gradient(
              90deg,
              rgba(11, 40, 107, 0.3) 0%,
              rgba(11, 40, 107, 1) 50%,
              rgba(11, 40, 107, 0.3) 100%
            );
          }
          &_item {
            width: 60px;
            box-sizing: border-box;
            border-right: 1px solid #0d355a;
            &:nth-child(1) {
              width: 90px;
              overflow: hidden; // 超出边框外隐藏
              text-overflow: ellipsis; // 属性规定当文本溢出包含元素时发生的事情。
              white-space: nowrap; // 规定段落中的文本不进行换行
            }
          }
        }
      }
    }
    .key_position_nodata {
      text-align: center;
      margin-top: 25px;
      img {
        width: 260px;
        height: 130px;
      }
      .no_data_text {
        font-size: 14px;
        font-family: PingFang SC;
        font-weight: 500;
        color: #d8e8fe;
        line-height: 30px;
        text-shadow: 0 0 5px rgb(63, 63, 221), 0 0 5px rgb(13, 13, 218);
      }
    }
    .rek_analyse {
      height: 200px;
      width: 100%;
      margin-top: 17px;
      background: url("../assets/recheck/<EMAIL>") no-repeat;
      background-size: 100% 200px;
      padding: 15px 25px;
      box-sizing: border-box;
      font-size: 12px;
      &_box {
        height: 170px;
        max-height: 170px;
        overflow: hidden;
      }
      &_nodata {
        text-align: center;
        img {
          width: 260px;
          height: 130px;
          margin-top: 20px;
        }
        .no_data_text {
          font-size: 14px;
          font-family: PingFang SC;
          font-weight: 500;
          color: #d8e8fe;
          line-height: 30px;
          text-shadow: 0 0 5px rgb(63, 63, 221), 0 0 5px rgb(13, 13, 218);
        }
      }
      &_content {
        height: 170px;
        max-height: 170px;
        width: 100%;
        overflow-y: auto;
        &::-webkit-scrollbar-track {
          background: rgba(57, 177, 255, 0.16);
        }
        &::-webkit-scrollbar-thumb {
          background: rgba(57, 177, 255, 0.26);
        }
        div {
          margin-top: 4px;
          white-space: pre-wrap;
        }
        span {
          color: #38bafe;
        }
      }
    }
  }
  .to_table {
    position: absolute;
    top: 0.5rem;
    left: 0.5rem;
    width: 1.8rem;
    height: 0.53rem;
    z-index: 99;
    cursor: pointer;
  }
  .timeRuler {
    position: absolute;
    bottom: 0.32rem;
    left: 0.62rem;
    z-index: 9999;
    cursor: pointer;
    display: flex;
    .playBtn {
      width: 0.63rem;
      height: 0.6rem;
      background: rgba(0, 0, 0, 0.3);
      text-align: center;
      line-height: 0.6rem;
      margin-right: 0.05rem;
    }
    .timeCom {
      width: 17.28rem;
      .rili {
        width: 100%;
        height: 0.36rem;
        background: rgba(0, 0, 0, 0.4);
        text-align: center;
        line-height: 0.36rem;
        position: relative;
        .rili_icon {
          position: absolute;
          top: 8px;
          left: 44%;
          font-size: 20px;
        }
        /deep/.el-input__inner {
          background-color: transparent;
          border: 1px solid transparent;
          color: #cddfff;
          font-size: 16px;
          cursor: pointer;
        }
      }
      .ruler {
        position: relative;
        .color {
          position: absolute;
          height: 0.06rem;
          top: 0;
          left: 0;
          background: rgb(0, 152, 69);
          z-index: 5;
        }
        .tips {
          position: absolute;
          top: -0.28rem;
          left: -0.3rem;
          z-index: 5;
          width: 0.6rem;
          height: 0.2rem;
          font-size: 0.14rem;
          text-align: center;
          line-height: 0.2rem;
          background: rgba(0, 0, 0, 0.4);
          border-radius: 0.04rem;
          &::after {
            content: "";
            position: absolute;
            box-sizing: border-box;
            border: 0.07rem solid rgba(0, 0, 0, 0.4);
            border-right: 0.07rem solid transparent;
            border-bottom: 0.07rem solid transparent;
            border-left: 0.07rem solid transparent;
            bottom: -0.135rem;
            left: 0.23rem;
          }
        }
        .calibration {
          display: flex;
          background: rgba(0, 0, 0, 0.4);
          margin-bottom: 0.03rem;
          .item {
            flex: 1;
            height: 0.15rem;
            font-size: 0.12rem;
            color: #cddfff;
            position: relative;
            text-align: right;
            .timeNum {
              position: absolute;
              left: -0.15rem;
            }
          }
        }
        .num {
          height: 0.06rem;
          background: #dedede;
          display: flex;
          .item {
            position: relative;
            width: 0.72rem;
            height: 100%;
            box-sizing: border-box;
            border-left: 0.01rem solid #fff;
            z-index: 9;
          }
        }
      }
    }
  }
  .leftTab {
    position: absolute;
    top: 1.75rem;
    left: 0.5rem;
    z-index: 9999;
    .item {
      width: 1.88rem;
      height: 0.53rem;
      font-size: 0.2rem;
      font-family: PingFang SC;
      font-weight: 400;
      color: #889fc8;
      line-height: 0.53rem;
      background: url("../assets/recheck/<EMAIL>") no-repeat;
      background-size: 100%;
      margin-bottom: 0.38rem;
      text-align: right;
      padding-right: 0.52rem;
      cursor: pointer;
    }
    .itemActive {
      width: 2.51rem;
      font-size: 0.24rem;
      font-weight: 600;
      color: #cddfff;
      background: url("../assets/recheck/<EMAIL>") no-repeat;
      background-size: 100%;
      padding-right: 0.54rem;
    }
  }
  .bottomTab {
    width: 196px;
    height: 34px;
    background: rgba(6, 55, 92, 0.6);
    border-radius: 4px;
    display: flex;
    position: absolute;
    bottom: 140px;
    left: 50px;
    z-index: 9999;
    .item {
      flex: 1;
      text-align: center;
      line-height: 34px;
      font-size: 14px;
      font-family: PingFang SC;
      font-weight: 400;
      color: #8999ab;
      cursor: pointer;
    }
    .item_active {
      background: #0a7bcc;
      border-radius: 4px;
      color: #ffffff;
    }
  }
  .topTab {
    position: absolute;
    top: 0.1rem;
    left: 7.93rem;
    display: flex;
    z-index: 9999;
    .air_btn,
    .water_btn {
      width: 1.57rem;
      height: 0.54rem;
      font-size: 0.18rem;
      font-family: PingFang SC;
      font-weight: 400;
      color: #24a6f1;
      line-height: 0.554rem;
      text-align: center;
      background: url("../assets/recheck/<EMAIL>") no-repeat;
      background-size: 100%;
      margin-right: 0.1rem;
      cursor: pointer;
    }
    .btn_active {
      font-size: 0.18rem;
      color: #e7f6fb;
      background: url("../assets/recheck/<EMAIL>") no-repeat;
      background-size: 100%;
    }
  }
  .common-content-inner {
    height: 2.1rem;
    .weather-main {
      display: flex;
      justify-content: space-between;
      box-sizing: border-box;
      padding-right: 0.3rem;
      overflow: hidden;
      .temperature-flex {
        display: flex;
        align-items: flex-end;
        animation: temperature-flex 2s;
        > div:first-child {
          width: 1.3rem;
          position: relative;
          .temperature-number {
            // position: absolute;
            line-height: 0.7rem;
            font-size: 0.9rem;
            font-family: SimHei;
          }
          .temperature-tag {
            position: absolute;
            font-size: 0.2rem;
            top: 0;
          }
        }
        .AQI-number {
          display: inline-block;
          padding: 0 10px;
          // background-color: #32c860;
          line-height: 18px;
          border-radius: 4px;
        }
      }
      @keyframes temperature-flex {
        0% {
          transform: translate(-100%, 0);
        }
        50% {
          transform: translate(-100%, 0);
        }
        100% {
          transform: translate(0, 0);
        }
      }

      .state-img img {
        width: 0.8rem;
        height: 0.8rem;
        animation: state-img 2s;
      }
      @keyframes state-img {
        0% {
          transform: translate(150%, 0);
        }
        50% {
          transform: translate(150%, 0);
        }
        100% {
          transform: translate(0, 0);
        }
      }
    }
    .Meteorological-main {
      margin-top: 0.35rem;
      height: 0.84rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
      box-sizing: border-box;
      .Meteorological-item {
        text-align: center;
        width: 1.2rem;
        height: 0.84rem;
        background-color: #0f245e;
        text-align: center;
        animation: Meteorological-item 3s linear;
        img {
          width: 0.22rem;
          height: 0.22rem;
          margin-top: 0.1rem;
        }
      }
      @keyframes Meteorological-item {
        0% {
          transform: scale(0);
        }
        66% {
          transform: scale(0);
        }
        100% {
          transform: scale(1);
        }
      }
    }
  }
  .monitor {
    display: flex;
    color: #ffffff;
    padding: 0.12rem;
    align-items: center;
    font-size: 0.14rem;
    height: 0.5rem !important;
    justify-content: space-between;
    .spot {
      width: 0.12rem;
      height: 0.12rem;
      background: rgba(242, 83, 22, 1);
      border-radius: 50%;
      margin-right: 0.12rem;
    }
    .line {
      width: 1px;
      height: 0.48rem;
      background: white;
      margin-right: 0.12rem;
      margin-left: 0.12rem;
    }
  }
  .middle-center .middle-part .middle-content .middle-content-right {
    padding: 0.3rem 0.5rem 0.3rem 0;
  }
  .monitor:nth-child(odd) {
    background: rgba(15, 36, 94, 1);
  }
  .monitor:nth-child(even) {
    background: rgba(4, 20, 51, 1);
  }
  .table-data {
    width: 100%;
    margin-top: 0.1rem;
    overflow: hidden;
    .table-data-thead {
      animation: table-head 2s linear;
      .tr {
        display: flex;
        justify-content: space-between;
        .th {
          background: transparent !important;
          color: #ffffff;
          padding: 0;
          text-align: center;
          border: none;
          // line-height: 0.34rem;
          font-size: 0.16rem;
          height: 0.34rem;
        }
        > :nth-of-type(1) {
          width: 20%;
        }
        > :nth-of-type(2) {
          width: 20%;
        }
        > :nth-of-type(3) {
          width: 20%;
        }
        > :nth-of-type(4) {
          width: 20%;
        }
        > :nth-of-type(5) {
          width: 20%;
        }
      }
    }
    .table-data-tbody {
      height: calc(205px - 0.1rem);
      .tr {
        display: flex;
        justify-content: space-between;
        .td {
          color: #ffffff;
          padding: 0;
          font-size: 0.14rem;
          text-align: center;
          height: 0.45rem;
          line-height: 0.45rem;
          align-items: center;
          border: none;
        }
        > :nth-of-type(1) {
          width: 20%;
        }
        > :nth-of-type(2) {
          width: 20%;
        }
        > :nth-of-type(3) {
          width: 20%;
        }
        > :nth-of-type(4) {
          width: 20%;
        }
        > :nth-of-type(5) {
          display: flex;
          align-items: center;
          width: 20%;
        }
      }
    }
    .table-data-tbody .tr:nth-child(odd) {
      background: #0f245e;
    }
    .table-data-tbody .tr:nth-child(even) {
      background: transparent;
    }
    .abnormal {
      //异常
      color: rgb(255, 17, 0);
      border: 1px solid rgb(255, 17, 0);
      width: 0.44rem;
      height: 0.2rem;
      line-height: 0.2rem;
      border-radius: 0.1rem;
      margin: 0 auto;
    }
    .normal {
      //正常
      color: #00ff18;
      border: 1px solid #00ff18;
      width: 0.44rem;
      height: 0.2rem;
      line-height: 0.2rem;
      border-radius: 0.1rem;
      margin: 0 auto;
    }
  }
  .table-data1 {
    width: 100%;
    overflow: hidden;
    .table-data-thead {
      animation: table-head 2s linear;
      .tr {
        display: flex;
        justify-content: space-between;
        .th {
          background: transparent !important;
          color: #ffffff;
          padding: 0;
          text-align: center;
          border: none;
          // line-height: 0.34rem;
          font-size: 0.16rem;
          height: 0.34rem;
        }
        > :nth-of-type(1) {
          width: 9.66%;
        }
        > :nth-of-type(2) {
          width: 23.66%;
        }
        > :nth-of-type(3) {
          width: 14.66%;
        }
        > :nth-of-type(4) {
          width: 16.66%;
        }
        > :nth-of-type(5) {
          width: 16.66%;
        }
        > :nth-of-type(6) {
          width: 18.66%;
        }
      }
    }

    .table-data-tbody {
      height: calc(205px - 0.1rem);
      .tr {
        display: flex;
        justify-content: space-between;
        .td {
          color: #ffffff;
          padding: 0;
          font-size: 0.14rem;
          text-align: center;
          height: 0.34rem;
          line-height: 0.34rem;
          border: none;
        }
        > :nth-of-type(1) {
          display: flex;
          align-items: center;
          justify-content: center;
          text-align: center;
          width: 9.66%;
        }
        > :nth-of-type(2) {
          width: 22.66%;
          text-align: left;
        }
        > :nth-of-type(3) {
          width: 14.66%;
        }
        > :nth-of-type(4) {
          width: 16.66%;
        }
        > :nth-of-type(5) {
          width: 16.66%;
        }
        > :nth-of-type(6) {
          width: 18.66%;
        }
      }
    }
    .table-data-tbody .tr:nth-child(odd) {
      background: #0f245e;
    }
    .table-data-tbody .tr:nth-child(even) {
      background: transparent;
    }
    .tdBefore {
      //前三
      width: 0.26rem;
      display: block;
      text-align: center;
      height: 0.2rem;
      line-height: 0.2rem;
      background: rgba(255, 133, 9, 1);
      border-radius: 0.06rem;
      margin: 0 auto;
    }
    .tdAfter {
      //前三外
      width: 0.26rem;
      display: block;
      text-align: center;
      height: 0.2rem;
      line-height: 0.2rem;
      background: rgba(18, 136, 226, 1);
      border-radius: 0.06rem;
      margin: 0 auto;
    }
  }
  .table-data-warning {
    width: 100%;
    overflow: hidden;
    .table-data-thead {
      animation: table-head 2s linear;
      .tr {
        display: flex;
        justify-content: space-between;
        .th {
          background: transparent !important;
          color: #ffffff;
          padding: 0;
          text-align: center;
          border: none;
          // line-height: 0.34rem;
          font-size: 0.16rem;
          height: 0.34rem;
        }
        > :nth-of-type(1) {
          width: 10%;
        }
        > :nth-of-type(2) {
          width: 28%;
        }
        > :nth-of-type(3) {
          width: 40%;
        }
        > :nth-of-type(4) {
          width: 30%;
        }
      }
    }
    .table-data-tbody {
      height: 1.75rem;
      .tr {
        display: flex;
        justify-content: space-between;
        .td {
          color: #ffffff;
          padding: 0;
          font-size: 0.14rem;
          height: 0.45rem;
          line-height: 0.45rem;
          border: none;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
        > :nth-of-type(1) {
          width: 10%;
          text-align: center;
        }
        > :nth-of-type(2) {
          width: 28%;
          text-align: center;
        }
        > :nth-of-type(3) {
          width: 40%;
        }
        > :nth-of-type(4) {
          width: 30%;
          text-align: center;
        }
      }
    }
    .table-data-tbody .tr:nth-child(odd) {
      background: #0f245e;
    }
    .table-data-tbody .tr:nth-child(even) {
      background: #041433;
    }
  }
  @keyframes table-head {
    0% {
      transform: translate(100%, 0);
    }
    50% {
      transform: translate(100%, 0);
    }
    100% {
      transform: translate(0, 0);
    }
  }
  .table-data-tbody .tr:nth-child(odd) {
    animation: left-entry 3s linear;
  }
  .table-data-tbody .tr:nth-child(even) {
    animation: right-entry 3s linear;
  }
  @keyframes left-entry {
    0% {
      transform: translate(-100%, 0);
    }
    66% {
      transform: translate(-100%, 0);
    }
    100% {
      transform: translate(0, 0);
    }
  }
  @keyframes right-entry {
    0% {
      transform: translate(100%, 0);
    }
    66% {
      transform: translate(100%, 0);
    }
    100% {
      transform: translate(0, 0);
    }
  }
  .type-radio {
    .water-monitor-tab {
      height: 0.24rem;
      font-size: 0.14rem;
      color: rgba(255, 255, 255, 1);
      background: rgba(12, 39, 92, 1);
      border-radius: 0;
      padding: 0 0.1rem;
      border: none;
    }
    .ant-radio-group-solid
      .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
      background: #05c1a6;
      border: none;
    }
    .ant-radio-button-wrapper:not(:first-child)::before {
      background: transparent;
    }
    .ant-radio-button-wrapper-checked::before {
      background: transparent !important;
    }
    .ant-radio-button-wrapper-checked {
      z-index: 1;
      font-size: 0.18rem !important;
      border-color: #05c1a6 !important;
      -webkit-box-shadow: -1px 0 0 0 #05c1a6;
      box-shadow: -1px 0 0 0 #05c1a6;
    }
    .ant-radio-group-small .ant-radio-button-wrapper {
      // width: 16.66%;
      // text-align: center;
    }
    .ant-radio-group {
      display: flex;
      justify-content: space-around;
      background-color: #0f245e;
      animation: ant-radio-group 2s linear;
    }
  }
  .type-radio-water {
    .water-monitor-tab {
      height: 0.24rem;
      font-size: 0.14rem;
      color: rgba(255, 255, 255, 1);
      background: rgba(12, 39, 92, 1);
      border-radius: 0;
      padding: 0 0.1rem;
      border: none;
    }
    .ant-radio-group-solid
      .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
      background: #0897d4;
      border: none;
    }
    .ant-radio-button-wrapper:not(:first-child)::before {
      background: transparent;
    }
    .ant-radio-button-wrapper-checked::before {
      background: transparent !important;
    }
    .ant-radio-button-wrapper-checked {
      z-index: 1;
      border-color: #0897d4 !important;
      font-size: 0.18rem !important;
      -webkit-box-shadow: -1px 0 0 0 #0897d4;
      box-shadow: -1px 0 0 0 #0897d4;
    }
    .ant-radio-group-small .ant-radio-button-wrapper {
      // width: 12.5%;
      // text-align: center;
      // text-overflow: ellipsis;
      // overflow: hidden;
      // white-space: nowrap;
    }
    .ant-radio-group {
      display: flex;
      justify-content: space-around;
      background-color: #0f245e;
      animation: ant-radio-group 2s linear;
    }
  }
  @keyframes ant-radio-group {
    0% {
      transform: rotateY(-90deg);
    }
    50% {
      transform: rotateY(-90deg);
    }
    100% {
      transform: rotateY(0deg);
    }
  }
  .wind-animation {
    animation: fly-wind 3.5s linear infinite;
  }
  .type-detail {
    color: #fff;
  }
  .swiper-main {
    width: 100%;
    height: calc(100% - 1rem);
    box-sizing: border-box;
    padding: 0 0.65rem;
    position: absolute;
    top: 1rem;
    padding-top: 207px;
    left: 0;
    background: url("../assets/home_shadow.png") no-repeat;
    background-color: rgba(0, 0, 0, 0.3);
    .mainOption {
      .swiper-item {
        padding: 0 0.2rem;
        display: flex;
        > div {
          width: 33.33%;
          padding: 0 0.2rem;
          overflow: hidden;
          position: relative;
          .common-content-inner {
            > .car-main-flex {
              display: flex;
              // justify-content: space-between;
              flex-wrap: wrap;
              > div {
                display: flex;
                align-items: flex-end;
                width: 33.33%;
                margin-top: 0.3rem;
                .img-box {
                  width: 0.56rem;
                  height: 0.56rem;
                  margin-right: 0.22rem;
                  overflow: hidden;
                  > img {
                    width: 100%;
                    height: 100%;
                    animation: carImg 2s linear;
                  }
                  @keyframes carImg {
                    0% {
                      transform: translate(200%, 0);
                    }
                    50% {
                      transform: translate(200%, 0);
                    }
                    75% {
                      transform: translate(100%, 0);
                    }
                    100% {
                      transform: translate(0, 0);
                    }
                  }
                }
                .detail {
                  font-size: 0.17rem;
                  font-family: Source Han Sans SC;
                  font-weight: 400;
                  color: rgba(255, 255, 255, 1);
                  overflow: hidden;
                  > div:first-child {
                    animation: detail1 3s linear;
                  }
                  @keyframes detail1 {
                    0% {
                      transform: translate(0, 1rem);
                    }
                    66% {
                      transform: translate(0, 1rem);
                    }
                    100% {
                      transform: translate(0, 0%);
                    }
                  }
                  > div:last-child {
                    animation: detail2 3s linear;
                    > span:first-child {
                      font-size: 0.26rem;
                      color: rgba(0, 252, 249, 1);
                    }
                    > span:last-child {
                      font-size: 0.16rem;
                    }
                  }
                  @keyframes detail2 {
                    0% {
                      transform: translate(0, -1rem);
                    }
                    66% {
                      transform: translate(0, -1rem);
                    }
                    100% {
                      transform: translate(0, 0%);
                    }
                  }
                }
              }
            }
          }
          .title {
            width: 100%;
            font-size: 0.26rem;
            align-items: center;
            animation: title 1s linear;
            > .title-shadow {
              text-shadow: 0 0 5px blue, 0 0 5px blue;
            }
            @keyframes title {
              0% {
                transform: translate(-100%, 0);
              }
              100% {
                transform: translate(0, 0);
              }
            }
          }

          .sub-title {
            margin-bottom: 0.2rem;
            img {
              width: 100%;
            }
            animation: sub-title 1.5s;
          }
          @keyframes sub-title {
            0% {
              transform: translate(100%, 0);
            }
            100% {
              transform: translate(0, 0);
            }
          }
          .airPie,
          .waterPie {
            width: 1.25rem;
            height: 1rem;
            overflow: hidden;
          }
          .air-environmental {
            font-size: 0.18rem;
            color: white;
            width: 0.2rem;
            animation: air-environmental 2s linear;
          }
          @keyframes air-environmental {
            0% {
              transform: translate(0, 120%);
            }
            50% {
              transform: translate(0, 120%);
            }
            100% {
              transform: translate(0, 0);
            }
          }
          .water-environmental {
            font-size: 0.18rem;
            color: white;
            width: 0.2rem;
            animation: water-environmental 2s linear;
          }
          @keyframes water-environmental {
            0% {
              transform: translate(0, -120%);
            }
            50% {
              transform: translate(0, -120%);
            }
            100% {
              transform: translate(0, 0);
            }
          }
          .content-title {
            font-size: 0.22rem;
            font-family: Source Han Sans SC;
            color: rgba(204, 204, 204, 0.8);
            text-align: center;
            margin-top: 0.5rem;
            animation: content-title 1s linear;
          }
          @keyframes content-title {
            0% {
              transform: scale(0);
            }
            100% {
              transform: scale(1);
            }
          }
        }
      }
    }
    .change-main {
      display: flex;
      justify-content: center;
      margin-top: 0.6rem;
      > div {
        cursor: pointer;
        width: 1rem;
        height: 1rem;
        // animation: change-main 1.5s linear;
        .active {
          background-color: rgba(0, 252, 249, 1);
        }
        > div {
          margin: 0.4rem auto;
          width: 0.6rem;
          height: 0.1rem;
          background-color: gray;
          border-radius: 2px;
        }
      }
    }
    @keyframes change-main {
      0% {
        margin: 0.5rem 2rem 0;
      }
      100% {
        margin: 0.5rem 0.1rem 0;
      }
    }
  }
  .table-site {
    animation: content-one 1.5s linear;
    width: 100%;
    border: 1px solid rgba(54, 218, 234, 0.42);
    .table-name {
      font-size: 0.12rem;
      width: 0.76rem;
      background: rgba(15, 36, 94, 1);
      text-align: center;
    }
    .table-text {
      padding-left: 0.06rem;
    }
    td {
      font-size: 0.12rem;
      height: 0.34rem;
      color: #ffffff;
      font-weight: 500;
    }
  }
  @keyframes content-one {
    0% {
      transform: scale(0);
    }
    100% {
      transform: scale(1);
    }
  }
  .content-twos {
    // height: 1.7rem;
    display: flex;
    justify-content: space-between;
  }
  .content-threes {
    height: 2.1rem;
    .table-data {
      width: 100%;
      .table-data-thead {
        .tr {
          display: flex;
          justify-content: space-between;
          .th {
            background: transparent !important;
            color: #ffffff;
            padding: 0;
            text-align: center;
            border: none;
            line-height: 0.43rem;
            font-size: 0.14rem;
            height: 0.43rem;
          }
          > :nth-of-type(1) {
            width: 20%;
          }
          > :nth-of-type(2) {
            width: 15%;
          }
          > :nth-of-type(3) {
            width: 35%;
          }
          > :nth-of-type(4) {
            width: 25%;
          }
        }
      }
      .table-data-tbody {
        height: calc(2.1rem - 0.35rem);
        .tr {
          display: flex;
          justify-content: space-between;
          .td {
            color: #ffffff;
            padding: 0;
            font-size: 0.14rem;
            text-align: center;
            height: 0.3rem;
            line-height: 0.3rem;
            border: none;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
          }
          > :nth-of-type(1) {
            width: 20%;
            text-align: left;
            box-sizing: border-box;
            padding-left: 0.1rem;
          }
          > :nth-of-type(2) {
            width: 15%;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
          }
          > :nth-of-type(3) {
            width: 35%;
          }
          > :nth-of-type(4) {
            width: 25%;
          }
        }
      }
      .table-data-tbody .tr:nth-child(odd) {
        background: rgba(5, 47, 97, 1);
      }
      .table-data-tbody .tr:nth-child(even) {
        background: transparent;
      }
      .tdBefore {
        //前三
        width: 0.26rem;
        display: block;
        text-align: center;
        height: 0.2rem;
        line-height: 0.2rem;
        background: rgba(255, 133, 9, 1);
        border-radius: 0.06rem;
        margin: 0 auto;
      }
      .tdAfter {
        //前三外
        width: 0.26rem;
        display: block;
        text-align: center;
        height: 0.2rem;
        line-height: 0.2rem;
        background: rgba(18, 136, 226, 1);
        border-radius: 0.06rem;
        margin: 0 auto;
      }
    }
  }
  .content-list {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    .content-item {
      width: 50%;
      display: flex;
      padding-left: 5%;
      padding-top: 20px;
      .img-box {
        width: 0.8rem;
        height: 0.8rem;
        overflow: hidden;
      }
      img {
        width: 75px;
        height: 75px;
        animation: taskImg 1s linear;
        animation-iteration-count: 1;
      }
      @keyframes taskImg {
        0% {
          transform: translate(200%, 0);
        }
        50% {
          transform: translate(200%, 0);
        }
        75% {
          transform: translate(100%, 0);
        }
        100% {
          transform: translate(0, 0);
        }
      }
      .content-info {
        margin-left: 15px;
        overflow: hidden;
        width: calc(100% - 0.8rem);
        height: 100%;
        .content-info-title {
          width: 100%;
          height: 30px;
          font-size: 19px;
          line-height: 35px;
          font-family: PingFang SC;
          font-weight: 500;
          color: #d5eaf1;
          animation: titleFrame 1.5s linear;
          animation-iteration-count: 1;
        }
        @keyframes titleFrame {
          0% {
            transform: translate(0, 1rem);
          }
          66% {
            transform: translate(0, 1rem);
          }
          100% {
            transform: translate(0, 0%);
          }
        }
        .number {
          line-height: 50px;
          font-size: 30px;
          height: 31px;
          width: 100%;
          font-family: PingFang SC;
          font-weight: 500;
          color: #32ceff;
          animation: detail2 1.5s linear;
          animation-iteration-count: 1;
        }
        @keyframes detail2 {
          0% {
            transform: translate(0, -1rem);
          }
          66% {
            transform: translate(0, -1rem);
          }
          100% {
            transform: translate(0, 0%);
          }
        }
        .unit {
          font-size: 20px;
          color: #fff;
        }
      }
    }
  }
}
.depMap_bg {
  background-image: url("../assets/recheck/-s-bg.png") !important;
  position: relative;
  z-index: 9;
}
.analysisChartBox {
  position: absolute;
  right: 500px;
  bottom: 115px;
}
</style>
<style lang="less">
.home-main-one {
  .title-select {
    display: flex;
    justify-content: space-between;
    .ant-select-selection {
      .ant-select-selection__rendered {
        min-width: 1.1rem;
        text-align: center;
      }
      height: 0.3rem;
      border: none;
      border-radius: unset;
      background: url(../assets/<EMAIL>);
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
    .ant-select-selection-selected-value {
      width: 100%;
      color: white;
      font-size: 0.16rem !important;
    }
    .ant-select-focused .ant-select-selection,
    .ant-select-selection:focus,
    .ant-select-selection:active {
      border-color: #0897d4;
      border-right-width: 0 !important;
      outline: 0;
      box-shadow: none;
    }
  }
  .title-select1 {
    display: flex;
    justify-content: space-between;
    .ant-select-selection {
      height: 0.3rem;
      // background: rgba(14, 139, 255, 0.32);
      border: none;
      border-radius: unset;
      // clip-path: polygon(100% 0, 100% 100%, 12% 100%, 0% 70%, 0 0);
      background: url(../assets/<EMAIL>);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      .ant-select-selection__rendered {
        text-align: center;
      }
    }
    .ant-select-selection-selected-value {
      width: 100%;
      color: white;
      font-size: 0.16rem !important;
    }
    .ant-select-focused .ant-select-selection,
    .ant-select-selection:focus,
    .ant-select-selection:active {
      border-color: #40a9ff;
      border-right-width: 0 !important;
      outline: 0;
      box-shadow: none;
    }
  }
  .table-data1 .table-data-tbody .tr > .td {
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }
  .title-select-size-type {
    .ant-select-selection {
      width: 0.7rem;
      display: flex;
      justify-content: center;
    }
  }
  .title-select-size-airname {
    .ant-select-selection {
      width: 1rem;
      display: flex;
      justify-content: center;
      margin: 0 0.1rem;
    }
  }
  .title-select-size-water {
    .ant-select-selection {
      margin-left: 0.1rem;
    }
  }
}
.picker_class {
  background: #052f61;
  border: none;
  color: #cddfff;
  .el-date-picker__header-label,
  .el-picker-panel__icon-btn,
  .el-month-table td .cell,
  .el-year-table td .cell {
    color: #cddfff;
  }
  .popper__arrow {
    border-top-color: #052f61 !important;
    &::after {
      border-top-color: #052f61 !important;
    }
  }
  .el-date-table th {
    border: none;
    color: #cddfff;
  }
  .el-date-picker__header--bordered {
    border: none;
  }
  .el-date-table td.disabled div,
  .el-month-table td.disabled .cell,
  .el-year-table td.disabled .cell {
    background-color: #000a38;
  }
}
.ant-modal-recheck {
  top: 1.65rem !important;
  right: calc(50% - 500px) !important;
  .ant-modal-content {
    width: 15.6rem !important;
    min-height: 6.9rem !important;
    max-height: 8.5rem !important;
    padding: 0.7rem 0rem 0.54rem 1.1rem;
    box-sizing: border-box;
    background-color: transparent !important;
    background-image: url("../assets/<EMAIL>") !important;
    background-size: 100% 100% !important;
  }
  .ant-modal-footer {
    border: none !important;
  }
  .ant-modal-close {
    top: 0.49rem !important;
    right: 0.86rem !important;
    svg {
      font-size: 0.2rem !important;
      color: #42adfb;
    }
  }
  .ant-modal-close-x {
    background: RGBA(12, 39, 94, 1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    width: 0.29rem;
    height: 0.29rem;
    justify-content: center;
    border: 0.01rem solid #358fd3;
  }
  .ant-modal-body {
    padding: 0;
    overflow-y: auto;
    max-height: 726px;
    &::-webkit-scrollbar-thumb {
      border-radius: 10px;
      width: 1px;
      background: transparent;
    }
    &::-webkit-scrollbar-track {
      border-radius: 10px;
      background: transparent;
    }
    .smapContainer {
      display: flex;
      flex-wrap: wrap;
      .sigleSmBox {
        width: 205px;
        height: 266px;
        background: url("../assets/recheck/<EMAIL>") no-repeat;
        background-size: 100%;
        position: relative;
        padding: 16px 8px 10px;
        box-sizing: border-box;
        margin-top: 20px;
        .dzIcon {
          font-size: 16px;
          font-family: PingFang SC;
          font-weight: 400;
          color: #dbf5ff;
        }
        .smBorderImg {
          position: absolute;
          width: 191px;
          height: 215px;
        }
        .smImgBox {
          position: absolute;
          width: 168px;
          height: 200px;
          overflow: hidden;
          top: 44px;
          left: 18px;
          .smImg {
            position: absolute;
            height: 200px;
            width: 355.5px;
            top: 5px;
            left: -80px;
            cursor: pointer;
          }
        }
      }
    }
  }
}
</style>
<template>
  <section class="home-main-one depMap_bg">
    <department-bg-map
      :mapZoom="12.5"
      :mapStyle="mapStyle"
      :curHour="curHour"
      :curtype="curtype"
      :curtypeName="curtypeName"
      :pagetype="pageType"
      :selDate="selDate"
      :heathMapMax="heathMapMaxMap[curtype]"
      :maxPoint="maxPoint"
      :timeType="curDataType"
      @noDataFn="noDataFn"
      @todayNoData="todayNoData"
      @getPhoto="getPhoto"
      @getRightData="getRightData"
      @topBtnOpen="topBtnOpen"
      @handleChangeAnalysis="handleChangeAnalysis"
      @handleGetCharacteristicRadarChart="handleGetCharacteristicRadarChart"
    ></department-bg-map>
    <img src="@/assets/<EMAIL>" class="to_table" @click="toDataTable" />
    <div class="swiper-main" v-if="false">
      <div class="mainOption">
        <div :style="{ display: curMainIndex === 1 ? 'block' : 'none' }">
          <div class="swiper-item" v-if="curMainIndex === 1">
            <div>
              <div class="title" style="display: flex; align-items: flex-end">
                <div class="title-shadow">{{ `气象在线监测` }}</div>
                <div style="font-size: 0.18rem; margin-left: 0.1rem">
                  {{ weather.updateTime }}
                </div>
              </div>
              <div class="sub-title">
                <img src="@/assets/biaoti_long.png" alt class="title-img" />
              </div>
              <div class="common-content-inner" ref="meteorologicalVisible">
                <div class="weather-main">
                  <div class="temperature-flex">
                    <div>
                      <span class="temperature-number">{{ weather.temp }}</span>
                      <span class="temperature-tag">℃</span>
                    </div>
                    <div style="font-size: 0.16rem">
                      <div>
                        <span>{{ weather.minOrMax }}℃</span>
                      </div>
                      <div>
                        <span>{{ weather.weather }}</span>
                      </div>
                      <div>
                        <span>AQI指数：</span>
                        <span
                          class="AQI-number"
                          :style="{
                            background: weather.aqiColor,
                            color: weather.color,
                          }"
                          >{{ weather.aqi }}</span
                        >
                      </div>
                    </div>
                  </div>
                  <div class="state-img">
                    <img :src="weather.weatherImg" alt />
                  </div>
                </div>
                <div class="Meteorological-main">
                  <div class="Meteorological-item">
                    <img
                      src="@/assets/dongnanfeng.png"
                      :style="{
                        animation: windLevelNumber,
                      }"
                      alt
                    />
                    <div class="type-name" style="color: #67e0e3">
                      {{
                        weather.windDirectionMark
                          ? weather.windDirectionMark
                          : "无持续风向微风"
                      }}
                    </div>
                    <div class="type-detail">{{ weather.windLevel }}级</div>
                  </div>
                  <div class="Meteorological-item">
                    <img src="@/assets/shidu.png" alt />
                    <div class="type-name" style="color: #67e0e3">湿度</div>
                    <div class="type-detail">
                      {{ weather.relativeHumidity }}%
                    </div>
                  </div>
                  <div class="Meteorological-item">
                    <img src="@/assets/qiya.png" alt />
                    <div class="type-name" style="color: #67e0e3">气压</div>
                    <div class="type-detail">
                      {{ weather.atmosphericPressure }}mb
                    </div>
                  </div>
                  <div class="Meteorological-item">
                    <img src="@/assets/wuranwu.png" alt />
                    <div class="type-name" style="color: #67e0e3">污染物</div>
                    <div class="type-detail">{{ weather.pollutant }}</div>
                  </div>
                </div>
              </div>
              <div class="content-title">24小时环境气象呈报</div>
            </div>
            <div>
              <div class="title title-select1">
                <div class="title-shadow">{{ `污染天数统计` }}</div>
              </div>
              <div class="sub-title">
                <img src="@/assets/biaoti_long.png" alt class="title-img" />
              </div>
              <div class="common-content-inner">
                <div
                  style="
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    overflow: hidden;
                  "
                >
                  <div class="air-environmental">大气</div>
                  <div
                    class="airPie"
                    v-for="(item, index) in this.airQuality"
                    :key="index"
                  >
                    <PollutionPie
                      :id="'PollutionAir' + index"
                      :width="'1.25rem'"
                      :height="'1rem'"
                      :propData="item"
                    />
                  </div>
                </div>
                <div
                  style="
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    overflow: hidden;
                  "
                >
                  <div
                    style="font-size: 0.18rem; color: white; width: 0.2rem"
                    class="water-environmental"
                  >
                    水环境
                  </div>
                  <div
                    class="waterPie"
                    v-for="(item, index) in this.waterQuality"
                    :key="index"
                  >
                    <PollutionPie
                      :id="'PollutionWater' + index"
                      :width="'1.25rem'"
                      :height="'1rem'"
                      :propData="item"
                    />
                  </div>
                </div>
              </div>
              <div class="content-title">
                大气环境和水污染物本年超标天数统计
              </div>
            </div>
            <div>
              <div @mouseenter="clearMainTimer()" @mouseleave="openMainTimer()">
                <div class="title title-select1">
                  <div class="title-shadow">{{ `重污企业排放量排名` }}</div>
                  <a-select
                    :defaultValue="pollutionType"
                    class="title-select-size"
                    @change="pollutionStationChange"
                  >
                    <a-icon
                      slot="suffixIcon"
                      type="caret-down"
                      style="color: white"
                    />
                    <a-select-option value="s" key="s">废水</a-select-option>
                    <a-select-option value="e" key="e">废气</a-select-option>
                    <a-select-option value="w" key="w">废料</a-select-option>
                  </a-select>
                </div>
                <div class="sub-title">
                  <img src="@/assets/biaoti_long.png" alt class="title-img" />
                </div>
                <div class="common-content-inner">
                  <div class="table-data1">
                    <div class="table-data-thead">
                      <div class="tr">
                        <div class="th">排行</div>
                        <div class="th">公司</div>
                        <div class="th">
                          排放量({{
                            pollutionType === "s"
                              ? "kg"
                              : pollutionType == "e"
                              ? "m³"
                              : "吨"
                          }})
                        </div>
                        <div class="th">行业</div>
                        <div class="th">环保负责人</div>
                        <div class="th">负责人电话</div>
                      </div>
                    </div>
                    <swiper
                      :options="swiperOptionPollution"
                      class="table-data-tbody"
                      v-if="heavyPollution.length > 5"
                    >
                      <swiper-slide
                        class="tr"
                        v-for="(item, index) in heavyPollution"
                        :key="index"
                      >
                        <div class="td">
                          <div class="tdAfter">{{ index + 1 }}</div>
                        </div>
                        <div class="td">
                          <a-tooltip>
                            <template slot="title">
                              {{ item.heavilyPollutingEnterpriseName }}
                            </template>
                            {{ item.heavilyPollutingEnterpriseNickname }}
                          </a-tooltip>
                        </div>
                        <div class="td" v-if="pollutionType == 's'">
                          {{
                            item.wastewaterDischarge
                              ? item.wastewaterDischarge
                              : "-"
                          }}
                        </div>
                        <div class="td" v-if="pollutionType == 'e'">
                          {{
                            item.exhaustEmissions ? item.exhaustEmissions : "-"
                          }}
                        </div>
                        <div class="td" v-if="pollutionType == 'w'">
                          {{ item.wasteDischarge ? item.wasteDischarge : "-" }}
                        </div>
                        <div class="td">
                          <a-tooltip>
                            <template slot="title">
                              {{ item.industryCategory }}
                            </template>
                            {{ item.industryCategory }}
                          </a-tooltip>
                        </div>
                        <div class="td">{{ item.principal }}</div>
                        <div class="td">
                          <a-tooltip>
                            <template slot="title">
                              {{ item.principalPhoneNumber }}
                            </template>
                            {{ item.principalPhoneNumber }}
                          </a-tooltip>
                        </div>
                      </swiper-slide>
                    </swiper>
                    <div class="table-data-tbody" v-else>
                      <div
                        class="tr"
                        v-for="(item, index) in heavyPollution"
                        :key="index"
                      >
                        <div class="td">
                          <div class="tdAfter">{{ index + 1 }}</div>
                        </div>
                        <div class="td">
                          <a-tooltip>
                            <template slot="title">
                              {{ item.heavilyPollutingEnterpriseName }}
                            </template>
                            {{ item.heavilyPollutingEnterpriseNickname }}
                          </a-tooltip>
                        </div>
                        <div class="td" v-if="pollutionType == 's'">
                          {{
                            item.sewageDischarge ? item.sewageDischarge : "-"
                          }}
                        </div>
                        <div class="td" v-if="pollutionType == 'e'">
                          {{
                            item.exhaustEmissions ? item.exhaustEmissions : "-"
                          }}
                        </div>
                        <div class="td" v-if="pollutionType == 'w'">
                          {{ item.wasteDischarge ? item.wasteDischarge : "-" }}
                        </div>
                        <div class="td">
                          <a-tooltip>
                            <template slot="title">
                              {{ item.industryCategory }}
                            </template>
                            {{ item.industryCategory }}
                          </a-tooltip>
                        </div>
                        <div class="td">{{ item.principal }}</div>
                        <div class="td">
                          <a-tooltip>
                            <template slot="title">
                              {{ item.principalPhoneNumber }}
                            </template>
                            {{ item.principalPhoneNumber }}
                          </a-tooltip>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="content-title">重点污染源企业排放量排名统计</div>
            </div>
          </div>
        </div>
        <div :style="{ display: curMainIndex === 2 ? 'block' : 'none' }">
          <div class="swiper-item" v-if="curMainIndex === 2">
            <div>
              <div @mouseenter="clearMainTimer()" @mouseleave="openMainTimer()">
                <div class="title title-select">
                  <div class="title-shadow">{{ `空气监测趋势` }}</div>
                  <a-select
                    v-model="airStationIndex"
                    class="title-select-size"
                    @change="airStationChange"
                  >
                    <a-icon
                      slot="suffixIcon"
                      type="caret-down"
                      style="color: white"
                    />
                    <a-select-option
                      :value="index"
                      v-for="(item, index) in airStationList"
                      :key="index"
                    >
                      {{ item.positionName }}</a-select-option
                    >
                  </a-select>
                </div>
                <div class="sub-title">
                  <img src="@/assets/biaoti_long.png" alt class="title-img" />
                </div>
                <div class="common-content-inner type-radio">
                  <a-radio-group
                    :value="airTypeIndex"
                    size="small"
                    buttonStyle="solid"
                    style="width: 100%"
                    @change="airTypeChange"
                  >
                    <a-radio-button
                      v-for="(item, index) in airTypes"
                      :key="index"
                      :value="index"
                      class="water-monitor-tab"
                      >{{ item.name }}</a-radio-button
                    >
                  </a-radio-group>
                  <div
                    @mouseenter="clearAirTimer()"
                    @mouseleave="openAirTimer()"
                  >
                    <LineChart
                      style="margin-top: 0.1rem"
                      :id="'airMonitoring'"
                      :width="'5.56rem'"
                      :height="'1.7rem'"
                      :propData="airMonitoring"
                      :lineColor="'#05C1A6'"
                    ></LineChart>
                  </div>
                </div>
              </div>
              <div class="content-title">空气监测站点趋势图例</div>
            </div>
            <div>
              <div @mouseenter="clearMainTimer()" @mouseleave="openMainTimer()">
                <div class="title title-select">
                  <div class="title-shadow">{{ `空气监测值` }}</div>
                  <a-select
                    v-model="airStationIndex"
                    class="title-select-size"
                    @change="airStationChange"
                  >
                    <a-icon
                      slot="suffixIcon"
                      type="caret-down"
                      style="color: white"
                    />
                    <a-select-option
                      :value="index"
                      v-for="(item, index) in airStationList"
                      :key="index"
                    >
                      {{ item.positionName }}</a-select-option
                    >
                  </a-select>
                </div>
                <div class="sub-title">
                  <img src="@/assets/biaoti_long.png" alt class="title-img" />
                </div>
                <div
                  class="common-content-inner"
                  style="border: none; margin-top: 0.1rem"
                >
                  <div class="table-data">
                    <div class="table-data-thead">
                      <div class="tr">
                        <div class="th">指标项</div>
                        <div class="th">数值</div>
                        <div class="th">标准</div>
                        <div class="th">单位</div>
                        <div class="th">状态</div>
                      </div>
                    </div>
                    <swiper
                      :options="swiperOptionAirs"
                      class="table-data-tbody"
                      v-if="airsData.length > 4"
                    >
                      <swiper-slide
                        class="tr"
                        v-for="(item, index) in airsData"
                        :key="index"
                      >
                        <div class="td">{{ item.pollutionCode }}</div>
                        <div class="td">
                          {{ item.value ? item.value : "-" }}
                        </div>
                        <div class="td">
                          {{ item.standard ? item.standard : "-" }}
                        </div>
                        <div class="td">{{ item.unit ? item.unit : "-" }}</div>
                        <div class="td">
                          <div :class="item.type == 0 ? 'normal' : 'abnormal'">
                            {{ item.type == 0 ? "正常" : "超标" }}
                          </div>
                        </div>
                      </swiper-slide>
                    </swiper>
                    <div class="table-data-tbody" v-else>
                      <div
                        class="tr"
                        v-for="(item, index) in airsData"
                        :key="index"
                      >
                        <div class="td">{{ item.pollutionCode }}</div>
                        <div class="td">
                          {{ item.value ? item.value : "-" }}
                        </div>
                        <div class="td">
                          {{ item.standard ? item.standard : "-" }}
                        </div>
                        <div class="td">{{ item.unit ? item.unit : "-" }}</div>
                        <div class="td">
                          <div :class="item.type == 0 ? 'normal' : 'abnormal'">
                            {{ item.type == 0 ? "正常" : "超标" }}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="content-title">空气监测站点值详情</div>
            </div>
            <div>
              <div class="title">
                <div class="title-shadow">{{ `车辆类型统计` }}</div>
              </div>
              <div class="sub-title">
                <img src="@/assets/biaoti_long.png" alt class="title-img" />
              </div>
              <div class="common-content-inner">
                <div class="car-main-flex">
                  <div v-for="(item, index) in carTypeList" :key="index">
                    <div class="img-box">
                      <img
                        :src="
                          require('../assets/home-carTypes/' + index + '.png')
                        "
                        alt=""
                      />
                    </div>
                    <div class="detail">
                      <div>{{ item.typeName }}</div>
                      <div>
                        <span>{{ item.online }}/</span
                        ><span>{{ item.count }}辆</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="content-title">
                <!-- 在线车辆类型统计<span style="font-size:0.18rem;"
                  >(注:<span style="color:rgba(0, 252, 249, 1)">‘9’</span
                  >表示在线车辆,<span style="color:white">‘9’</span
                  >表示车辆总数)</span
                > -->
                在线车辆类型统计(在线车辆/车辆总数)
              </div>
            </div>
          </div>
        </div>
        <div :style="{ display: curMainIndex === 3 ? 'block' : 'none' }">
          <div class="swiper-item" v-if="curMainIndex === 3">
            <div>
              <div @mouseenter="clearMainTimer()" @mouseleave="openMainTimer()">
                <div class="title title-select">
                  <div class="title-shadow">{{ `水质监测趋势` }}</div>
                  <a-select
                    v-model="stationId"
                    class="title-select-size"
                    @change="waterStationChange"
                  >
                    <a-icon
                      slot="suffixIcon"
                      type="caret-down"
                      style="color: white; width: 14px; height: 8px"
                    />
                    <a-select-option
                      :value="item.stationId"
                      v-for="(item, index) in waterStation"
                      :key="index"
                    >
                      {{ item.stationName }}</a-select-option
                    >
                  </a-select>
                </div>
                <div class="sub-title">
                  <img src="@/assets/biaoti_long.png" alt class="title-img" />
                </div>
                <div class="common-content-inner type-radio-water">
                  <a-radio-group
                    :value="curWaterIndex"
                    size="small"
                    buttonStyle="solid"
                    style="width: 100%"
                    @change="waterTypeChange"
                  >
                    <a-radio-button
                      v-for="(item, index) in waterTrendList"
                      :key="index"
                      :value="index"
                      class="water-monitor-tab"
                      >{{ item.name }}</a-radio-button
                    >
                  </a-radio-group>
                  <div
                    @mouseenter="clearWaterTimer()"
                    @mouseleave="openWaterTimer()"
                  >
                    <LineChart
                      style="margin-top: 0.1rem"
                      :id="'waterMonitors'"
                      :width="'5.56rem'"
                      :height="'1.7rem'"
                      :propData="waterTrendAll"
                      :lineColor="'#0897D4'"
                    />
                  </div>
                </div>
              </div>
              <div class="content-title">水质监测站点监测趋势图例</div>
            </div>
            <div>
              <div @mouseenter="clearMainTimer()" @mouseleave="openMainTimer()">
                <div class="title title-select">
                  <div class="title-shadow">{{ `水质监测值` }}</div>
                  <a-select
                    v-model="stationId"
                    class="title-select-size"
                    @change="waterStationChange"
                  >
                    <a-icon
                      slot="suffixIcon"
                      type="caret-down"
                      style="color: white; width: 14px; height: 8px"
                    />
                    <a-select-option
                      :value="item.stationId"
                      v-for="(item, index) in waterStation"
                      :key="index"
                    >
                      {{ item.stationName }}</a-select-option
                    >
                  </a-select>
                </div>
                <div class="sub-title">
                  <img src="@/assets/biaoti_long.png" alt class="title-img" />
                </div>
                <div class="common-content-inner">
                  <div class="table-data">
                    <div class="table-data-thead">
                      <div class="tr">
                        <div class="th">指标项</div>
                        <div class="th">数值</div>
                        <div class="th">标准</div>
                        <div class="th">单位</div>
                        <div class="th">状态</div>
                      </div>
                    </div>
                    <swiper
                      :options="swiperOptionWater"
                      class="table-data-tbody"
                      v-if="waterData.length > 4"
                    >
                      <swiper-slide
                        class="tr"
                        v-for="(item, index) in waterData"
                        :key="index"
                      >
                        <div class="td">{{ item.itemName }}</div>
                        <div class="td">{{ item.value }}</div>
                        <div class="td">
                          {{
                            item.alarmValue != null && item.alarmValue != "null"
                              ? item.alarmValue
                              : "-"
                          }}
                        </div>
                        <div class="td">
                          {{
                            item.unit != null && item.unit != "null"
                              ? item.unit
                              : "-"
                          }}
                        </div>
                        <div class="td">
                          <div
                            :class="
                              item.alarmStatus == 0 ? 'normal' : 'abnormal'
                            "
                          >
                            {{ item.alarmStatus == 0 ? "正常" : "超标" }}
                          </div>
                        </div>
                      </swiper-slide>
                    </swiper>
                    <div
                      class="table-data-tbody"
                      v-else-if="waterData.length < 4 && waterData.length !== 0"
                    >
                      <div
                        class="tr"
                        v-for="(item, index) in waterData"
                        :key="index"
                      >
                        <div class="td">{{ item.itemName }}</div>
                        <div class="td">{{ item.value }}</div>
                        <div class="td">
                          {{
                            item.alarmValue != null && item.alarmValue != "null"
                              ? item.alarmValue
                              : "-"
                          }}
                        </div>
                        <div class="td">{{ item.unit }}</div>
                        <div class="td">
                          <div
                            :class="
                              item.alarmStatus == 0 ? 'normal' : 'abnormal'
                            "
                          >
                            {{ item.alarmStatus == 0 ? "正常" : "超标" }}
                          </div>
                        </div>
                      </div>
                    </div>
                    <div style="height: 1.7rem" v-else>
                      <img
                        style="
                          width: 1.7rem;
                          position: relative;
                          left: 2rem;
                          top: 0.3rem;
                        "
                        src="@/assets/pollution-not.png"
                        alt=""
                      />
                    </div>
                  </div>
                </div>
              </div>
              <div class="content-title">水质监测站点值详情</div>
            </div>
            <div>
              <div @mouseenter="clearMainTimer()" @mouseleave="openMainTimer()">
                <div class="title">
                  <div class="title-shadow">{{ `实时监测告警` }}</div>
                </div>
                <div class="sub-title">
                  <img src="@/assets/biaoti_long.png" alt class="title-img" />
                </div>
                <div class="table-data-warning">
                  <div class="table-data-thead">
                    <div class="tr">
                      <div class="th">类型</div>
                      <div class="th">站点</div>
                      <div class="th">报警</div>
                      <div class="th">时间</div>
                    </div>
                  </div>
                  <swiper
                    :options="swiperOption"
                    class="table-data-tbody"
                    v-if="airWarning.length > 4"
                  >
                    <swiper-slide
                      class="tr"
                      v-for="(item, index) in airWarning"
                      :key="index"
                    >
                      <div class="td">
                        {{ item.type == 1 ? "空气" : "水质" }}
                      </div>
                      <div class="td">{{ item.stationName }}</div>
                      <div class="td">
                        <a-tooltip>
                          <template slot="title">{{
                            item.alarmContent
                          }}</template>
                          {{ item.alarmContent }}
                        </a-tooltip>
                      </div>
                      <div class="td">{{ item.alarmTime }}</div>
                    </swiper-slide>
                  </swiper>
                  <div class="table-data-tbody" v-else>
                    <div
                      class="tr"
                      v-for="(item, index) in airWarning"
                      :key="index"
                    >
                      <div class="td">
                        {{ item.type == 1 ? "空气" : "水质" }}
                      </div>
                      <div class="td">{{ item.stationName }}</div>
                      <div class="td">
                        <a-tooltip>
                          <template slot="title">{{
                            item.alarmContent
                          }}</template>
                          {{ item.alarmContent }}
                        </a-tooltip>
                      </div>
                      <div class="td">{{ item.alarmTime }}</div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="content-title">监测站点监测设备和参数的预警信息</div>
            </div>
          </div>
        </div>
        <div :style="{ display: curMainIndex === 4 ? 'block' : 'none' }">
          <div class="swiper-item" v-if="curMainIndex === 4">
            <div>
              <div @mouseenter="clearMainTimer()" @mouseleave="openMainTimer()">
                <div class="title title-select">
                  <div class="title-shadow">{{ `设备信息` }}</div>
                  <div>
                    <a-select
                      v-model="stationTypeName"
                      class="title-select-size title-select-size-type"
                      @change="stationTypeNameChange"
                    >
                      <a-icon
                        slot="suffixIcon"
                        type="caret-down"
                        style="color: white; width: 14px; height: 8px"
                      />
                      <a-select-option :value="'空气'">空气</a-select-option>
                      <a-select-option :value="'水'">水</a-select-option>
                    </a-select>
                    <!-- 空气 -->
                    <template v-if="stationTypeName == '空气'">
                      <!-- 站点类型 -->
                      <a-select
                        v-model="stationTypeIndnx"
                        class="title-select-size title-select-size-airname"
                        @change="stationTypeCodeChange"
                      >
                        <a-icon
                          slot="suffixIcon"
                          type="caret-down"
                          style="color: white; width: 14px; height: 8px"
                        />
                        <a-select-option
                          :value="String(index)"
                          v-for="(item, index) in airStationTypeList"
                          :key="index"
                        >
                          {{ item.stationTypeName }}</a-select-option
                        >
                      </a-select>
                      <!-- 站点名称 -->
                      <a-select
                        v-model="airStationTypeName"
                        class="title-select-size"
                        @change="airStationTypeNameChange"
                      >
                        <a-icon
                          slot="suffixIcon"
                          type="caret-down"
                          style="color: white; width: 14px; height: 8px"
                        />
                        <a-select-option
                          :value="String(index)"
                          v-for="(item, index) in airStationTypeList[
                            stationTypeIndnx
                          ].stationList"
                          :key="index"
                        >
                          {{ item.positionName }}</a-select-option
                        >
                      </a-select>
                    </template>
                    <!-- 水 -->
                    <template v-if="stationTypeName == '水'">
                      <!-- 站点名称 -->
                      <a-select
                        v-model="stationIdIndex"
                        class="title-select-size title-select-size-water"
                        @change="stationIdIndexChange"
                      >
                        <a-icon
                          slot="suffixIcon"
                          type="caret-down"
                          style="color: white; width: 14px; height: 8px"
                        />
                        <a-select-option
                          :value="String(index)"
                          v-for="(item, index) in waterStation"
                          :key="index"
                        >
                          {{ item.stationName }}</a-select-option
                        >
                      </a-select>
                    </template>
                  </div>
                </div>
                <div class="sub-title">
                  <img src="@/assets/biaoti_long.png" alt class="title-img" />
                </div>
                <div class="common-content-inner">
                  <table border="1" class="table-site">
                    <tr>
                      <td class="table-name">设备站点</td>
                      <td
                        colspan="3"
                        class="table-text"
                        v-if="stationTypeName == '空气'"
                      >
                        {{ equipmentDetail.positionName }}
                      </td>
                      <td
                        colspan="3"
                        class="table-text"
                        v-if="stationTypeName == '水'"
                      >
                        {{ equipmentDetail.stationName }}
                      </td>
                    </tr>
                    <tr>
                      <td class="table-name">设备类型</td>
                      <td class="table-text">
                        {{ stationTypeName == "空气" ? "空气站" : "水站" }}
                      </td>
                      <td class="table-name">设备状态</td>
                      <td
                        class="table-text"
                        style="display: flex; align-items: center"
                      >
                        <img
                          src="@/assets/state.png"
                          alt=""
                          style="
                            width: 0.15rem;
                            height: 0.14rem;
                            margin-right: 0.1rem;
                          "
                          v-if="equipmentDetail.online"
                        />
                        <img
                          src="@/assets/lxtb.png"
                          alt=""
                          style="
                            width: 0.15rem;
                            height: 0.15rem;
                            margin-right: 0.1rem;
                          "
                          v-if="!equipmentDetail.online"
                        />
                        {{ equipmentDetail.online ? "在线" : "离线" }}
                      </td>
                    </tr>
                    <tr>
                      <td class="table-name">设备位置</td>
                      <td
                        colspan="3"
                        class="table-text"
                        v-if="stationTypeName == '空气'"
                      >
                        {{ equipmentDetail.address }}
                      </td>
                      <td
                        colspan="3"
                        class="table-text"
                        v-if="stationTypeName == '水'"
                      >
                        {{ equipmentDetail.stationAddress }}
                      </td>
                    </tr>
                    <tr>
                      <td class="table-name">设备参数</td>
                      <td colspan="3" class="table-text">6参数</td>
                    </tr>
                    <tr>
                      <td class="table-name">更新频率</td>
                      <td colspan="3" class="table-text">5分钟</td>
                    </tr>
                  </table>
                </div>
              </div>
              <div class="content-title">设备信息状态查询</div>
            </div>
            <div>
              <div @mouseenter="clearMainTimer()" @mouseleave="openMainTimer()">
                <div class="title title-select">
                  <div class="title-shadow">{{ `设备数量` }}</div>
                </div>
                <div class="sub-title">
                  <img src="@/assets/biaoti_long.png" alt class="title-img" />
                </div>
                <div class="common-content-inner content-twos">
                  <div v-for="(item, index) in stateList" :key="index">
                    <SitePie
                      :id="`siteTypeS${index}`"
                      :width="'1.3rem'"
                      :height="'1.5rem'"
                      :propData="item"
                    />
                  </div>
                </div>
              </div>
              <div class="content-title">设备在线状态查看</div>
            </div>
            <div>
              <div @mouseenter="clearMainTimer()" @mouseleave="openMainTimer()">
                <div class="title">
                  <div class="title-shadow">{{ `离线设备列表` }}</div>
                </div>
                <div class="sub-title">
                  <img src="@/assets/biaoti_long.png" alt class="title-img" />
                </div>
                <div class="common-content-inner content-threes">
                  <div class="table-data">
                    <div class="table-data-thead">
                      <div class="tr">
                        <div class="th">名称</div>
                        <div class="th">类型</div>
                        <div class="th">位置</div>
                        <div class="th">时间</div>
                      </div>
                    </div>
                    <swiper
                      :options="swiperOptionWarn"
                      class="table-data-tbody"
                      v-if="offLineList.length >= 5"
                    >
                      <swiper-slide
                        class="tr"
                        v-for="(item, index) in offLineList"
                        :key="index"
                      >
                        <div class="td" :title="item.name">{{ item.name }}</div>
                        <div class="td" :title="item.type">{{ item.type }}</div>
                        <div
                          class="td"
                          :title="item.address"
                          style="text-align: left"
                        >
                          {{ item.address }}
                        </div>
                        <div class="td">
                          {{ item.offlineTime ? item.offlineTime : "-" }}
                        </div>
                      </swiper-slide>
                    </swiper>
                    <div class="table-data-tbody" v-else>
                      <div
                        class="tr"
                        v-for="(item, index) in offLineList"
                        :key="index"
                      >
                        <div class="td" :title="item.name">{{ item.name }}</div>
                        <div class="td" :title="item.type">{{ item.type }}</div>
                        <div
                          class="td"
                          :title="item.address"
                          style="text-align: left"
                        >
                          {{ item.address }}
                        </div>
                        <div class="td">
                          {{ item.offlineTime ? item.offlineTime : "-" }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="content-title">离线设备状态告警</div>
            </div>
          </div>
        </div>
        <div :style="{ display: curMainIndex === 5 ? 'block' : 'none' }">
          <div class="swiper-item" v-if="curMainIndex === 5">
            <div>
              <div>
                <div class="title title-select">
                  <div class="title-shadow">{{ `任务完成情况` }}</div>
                  <div>
                    <a-select
                      v-model="selectTaskTypeCompletionType"
                      class="title-select-size title-select-size-type"
                      @change="getSelectTaskTypeCompletionChange"
                      :key="1"
                    >
                      <a-icon
                        slot="suffixIcon"
                        type="caret-down"
                        style="color: white; width: 14px; height: 8px"
                      />
                      <a-select-option
                        v-for="item in timeTypeList1"
                        :key="'qwe' + item.value"
                        :value="item.value"
                        >{{ item.label }}</a-select-option
                      >
                    </a-select>
                  </div>
                </div>
                <div class="sub-title">
                  <img src="@/assets/biaoti_long.png" alt class="title-img" />
                </div>
                <div class="common-content-inner">
                  <taskProcessing
                    v-if="curMainIndex === 5"
                    :id="'taskProcessing1' + new Date().getTime()"
                    :width="'100%'"
                    :height="'2.2rem'"
                    :propData="completeData"
                    :smooth="true"
                  />
                </div>
              </div>
              <div class="content-title">综合任务完成统计</div>
            </div>
            <div>
              <div>
                <div class="title title-select">
                  <div class="title-shadow">{{ `任务分类统计` }}</div>
                  <div>
                    <a-select
                      v-model="taskType"
                      class="title-select-size title-select-size-type"
                      @change="getSelectTypeStatisticChange"
                      :key="2"
                    >
                      <a-icon
                        slot="suffixIcon"
                        type="caret-down"
                        style="color: white; width: 14px; height: 8px"
                      />
                      <a-select-option
                        v-for="(item, index) in timeTypeList"
                        :key="'asd' + index"
                        :value="item.value"
                        >{{ item.label }}</a-select-option
                      >
                    </a-select>
                  </div>
                </div>
                <div class="sub-title">
                  <img src="@/assets/biaoti_long.png" alt class="title-img" />
                </div>
                <div class="common-content-inner content-twos">
                  <div class="content-list" v-if="contentListVisiable">
                    <!-- 大气环境 -->
                    <div
                      class="content-item"
                      v-for="item in taskClassificationStatistics"
                      :key="item.id"
                    >
                      <div class="img-box">
                        <img :src="item.icon" alt="" />
                      </div>
                      <div class="content-info">
                        <div class="content-info-title">{{ item.name }}</div>
                        <div class="number">
                          {{ item.total }} <span class="unit">件</span>
                        </div>
                      </div>
                    </div>
                    <!-- <div class="content-item">
                      <div class="img-box">
                        <img src="../assets/swiperHome/shj.png" alt="">
                      </div>
                      <div class="content-info">
                        <div class="content-info-title">水环境</div>
                        <div class="number">17 <span class="unit">件</span></div>
                      </div>
                    </div>
                    <div class="content-item">
                      <div class="img-box">
                        <img src="../assets/swiperHome/wry.png" alt="">
                      </div>
                      <div class="content-info">
                        <div class="content-info-title">污染源</div>
                        <div class="number">17 <span class="unit">件</span></div>
                      </div>
                    </div>
                    <div class="content-item">
                      <div class="img-box">
                        <img src="../assets/swiperHome/rwdd.png" alt="">
                      </div>
                      <div class="content-info">
                        <div class="content-info-title">任务调度</div>
                        <div class="number">17 <span class="unit">件</span></div>
                      </div>
                    </div> -->
                  </div>
                </div>
              </div>
              <div class="content-title">任务完成分类统计</div>
            </div>
            <div>
              <div>
                <div class="title">
                  <div class="title-shadow">{{ `任务执行率` }}</div>
                </div>
                <div class="sub-title">
                  <img src="@/assets/biaoti_long.png" alt class="title-img" />
                </div>
                <div class="common-content-inner content-threes">
                  <taskExecutionRateLine
                    v-if="curMainIndex === 5"
                    :id="'dayTask1' + new Date().getTime()"
                    :width="'100%'"
                    :height="'2.2rem'"
                    :propData="executionRate"
                    :smooth="true"
                  ></taskExecutionRateLine>
                </div>
              </div>
              <div class="content-title">任务执行率趋势图例</div>
            </div>
          </div>
        </div>
      </div>
      <div class="change-main" v-if="curMainIndex != 0">
        <div v-for="index in 5" :key="index" @click="changeIndex(index)">
          <div :class="{ active: index == curMainIndex }"></div>
        </div>
      </div>
    </div>
    <div class="construction-plant-dep">
      <div style="height: 34%">
        <div class="common-title box-title" style="position: relative">
          <div class="title">污染源提取</div>
          <div
            v-if="samllMapList.length"
            style="position:absolute; font-size:14px; font-family:PingFang SC; font-weight:400; color:#38BAFE; top:10px; right:0px; cursor:pointer;"
            @click="sMapMore"
          >
            查看全部>>
          </div>
        </div>
        <div class="sub-title">
          <img src="@/assets/biaoti.png" alt class="title-img" />
          <swiper
            :options="swiperOptionAlert"
            class="swiper-container-dep"
            ref="mapSwiper"
            v-if="samllMapList.length"
          >
            <swiper-slide
              class="swiper-slide"
              v-for="(item, i) in samllMapList"
              :key="i"
            >
              <img
                src="../assets/recheck/<EMAIL>"
                alt=""
                class="img_bg"
              />
              <div class="img_box">
                <img :src="item.url" alt="" class="img_item" />
              </div>
              <!-- <div class="img_index">{{curDataType==1?(i>9?i:'0'+i):curDataType==2?(i+1>9?(i+1):'0'+(i+1)):''}}{{curDataType==1?':00':''}}</div> -->
            </swiper-slide>
          </swiper>
          <div class="swiper_line" v-if="samllMapList.length">
            <img src="@/assets/recheck/<EMAIL>" alt="" />
            <div class="line_num">
              <div class="item" style="top: 27px; left: 20px">
                {{ sMapTime(-2)
                }}{{ curDataType == 1 && sMapTime(-2) ? ":00" : "" }}
              </div>
              <div class="item" style="top: 44px; left: 90px">
                {{ sMapTime(-1)
                }}{{ curDataType == 1 && sMapTime(-1) ? ":00" : "" }}
              </div>
              <div class="item" style="top: 52px; left: 190px">
                {{ sMapTime(0)
                }}{{ curDataType == 1 && sMapTime(0) ? ":00" : "" }}
              </div>
              <div class="item" style="top: 43px; left: 295px">
                {{ sMapTime(1)
                }}{{ curDataType == 1 && sMapTime(1) ? ":00" : "" }}
              </div>
              <div class="item" style="top: 25px; left: 365px">
                {{ sMapTime(2)
                }}{{ curDataType == 1 && sMapTime(2) ? ":00" : "" }}
              </div>
            </div>
          </div>
          <div
            v-if="!samllMapList.length"
            style="font-size: 20px; margin: 100px 0 0 125px"
          >
            数据加载中...
          </div>
        </div>
      </div>
      <div style="height: 32%">
        <div class="video-main polluted-video">
          <div class="common-title box-title" style="position: relative">
            <div class="title">{{ `重点污染源定位` }}</div>
            <div
              style="position: absolute; font-size: 12px; font-family: PingFang SC; font-weight: 400; color: #2A8DCD; top:10px; right:0px;"
            >
              （当前重点污染源方圆1千米{{ curtypeName }}排行）
            </div>
          </div>
          <div class="sub-title" style="margin: 0">
            <img src="@/assets/biaoti.png" alt class="title-img" />
            <div class="key_position" v-show="maxList.length">
              <div class="key_position_map">
                <img src="../assets/recheck/<EMAIL>" alt="" />
                <div class="key_position_map_map">
                  <DepartmentMapSmall
                    :heatmapData="samllMapHeatData"
                    :maxPoint="maxPoint"
                    :heathMapMax="heathMapMaxMap[curtype]"
                    :pageType="pageType"
                  />
                </div>
                <img src="../assets/recheck/<EMAIL>" alt="" />
              </div>
              <div class="key_position_table">
                <div class="key_position_table_header">
                  <div
                    class="key_position_table_header_item"
                    :style="{
                      width:
                        (curtypeName !== 'WQI' && pageType == 'water') ||
                        (curtypeName !== 'AQI' && pageType == 'air')
                          ? '70px'
                          : '90px',
                    }"
                  >
                    站点
                  </div>
                  <div class="key_position_table_header_item">
                    {{ pageType == "air" ? "排行" : "类型" }}
                  </div>
                  <div
                    class="key_position_table_header_item"
                    :title="
                      curtypeName +
                        (pageType == 'air' &&
                        ['PM₁₀', 'PM₂.₅', 'SO₂', 'NO₂', 'O₃'].includes(
                          curtypeName
                        )
                          ? '(μg/m³)'
                          : pageType == 'air' && ['CO'].includes(curtypeName)
                          ? '(mg/m³)'
                          : pageType == 'water' &&
                            ['总磷', '氨氮', '溶解氧', '高锰酸盐指数'].includes(
                              curtypeName
                            )
                          ? '(mg/L)'
                          : '')
                    "
                    :style="{
                      width:
                        (curtypeName !== 'WQI' && pageType == 'water') ||
                        (curtypeName !== 'AQI' && pageType == 'air')
                          ? '90px'
                          : '',
                      'font-size':
                        (curtypeName !== 'WQI' && pageType == 'water') ||
                        (curtypeName !== 'AQI' && pageType == 'air')
                          ? '12px'
                          : '',
                    }"
                  >
                    {{
                      curtypeName +
                        (pageType == "air" &&
                        ["PM₁₀", "PM₂.₅", "SO₂", "NO₂", "O₃"].includes(
                          curtypeName
                        )
                          ? "(μg/m³)"
                          : pageType == "air" && ["CO"].includes(curtypeName)
                          ? "(mg/m³)"
                          : pageType == "water" &&
                            ["总磷", "氨氮", "溶解氧", "高锰酸盐指数"].includes(
                              curtypeName
                            )
                          ? "(mg/L)"
                          : "")
                    }}
                  </div>
                </div>
                <div class="key_position_table_body">
                  <template v-if="maxList.length">
                    <div
                      class="key_position_table_cell"
                      v-for="(item, i) in maxList"
                      :key="i"
                    >
                      <div
                        class="key_position_table_cell_item"
                        :title="item.positionName || item.stationName"
                        :style="{
                          width:
                            (curtypeName !== 'WQI' && pageType == 'water') ||
                            (curtypeName !== 'AQI' && pageType == 'air')
                              ? '70px'
                              : '90px',
                        }"
                      >
                        {{ item.positionName || item.stationName }}
                      </div>
                      <div class="key_position_table_cell_item">
                        {{
                          pageType == "air"
                            ? i + 1
                            : item.stationCrossType || "--"
                        }}
                      </div>
                      <div
                        class="key_position_table_cell_item"
                        :style="{
                          width:
                            (curtypeName !== 'WQI' && pageType == 'water') ||
                            (curtypeName !== 'AQI' && pageType == 'air')
                              ? '90px'
                              : '',
                        }"
                      >
                        {{ item.concentration }}
                      </div>
                    </div>
                  </template>
                  <template v-else>
                    <div style="margin-top:50px; text-align: center;">
                      暂无站点排名
                    </div>
                  </template>
                </div>
              </div>
            </div>
            <div class="key_position_nodata" v-show="!maxList.length">
              <img src="../assets/recheck/<EMAIL>" alt="" />
              <div class="no_data_text">所有监测站点正常，请继续保持</div>
            </div>
          </div>
        </div>
      </div>
      <div style="height: 34%">
        <div class="video-main polluted-video">
          <div class="common-title box-title">
            <div class="title">{{ `重点污染源分析` }}</div>
          </div>
          <div class="sub-title" style="margin: 0">
            <img src="@/assets/biaoti.png" alt class="title-img" />
            <div class="rek_analyse">
              <div class="rek_analyse_box" v-if="analyse">
                <div class="rek_analyse_content" ref="everyData">
                  <div>
                    <span>污染站点：</span>{{ analyse.station || "无" }}
                  </div>
                  <div>
                    <span
                      >{{ pageType == "air" ? "涉污街道" : "涉污河流" }}：</span
                    >{{ analyse.street || analyse.river || "无" }}
                  </div>
                  <div>
                    <span>涉污企业（1KM）：</span
                    >{{ analyse.enterprise || "无" }}
                  </div>
                  <div>
                    <span>分析依据：</span>
                    <div>{{ analyse.analysisBasis || "无" }}</div>
                  </div>
                  <div>
                    <span>分析结果：</span
                    >{{ analyse.analysisConclusion || "无" }}
                  </div>
                </div>
              </div>
              <div v-else class="rek_analyse_nodata">
                <img src="../assets/recheck/<EMAIL>" alt="" />
                <div class="no_data_text">所有监测站点正常，请继续保持</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="topTab">
      <div
        class="air_btn"
        :class="pageType == 'air' ? 'btn_active' : ''"
        :style="topBtnFlg ? 'cursor: pointer;' : 'cursor: not-allowed;'"
        @click="pageTypeCHange('air')"
      >
        大气环境
      </div>
      <div
        class="water_btn"
        :class="pageType == 'water' ? 'btn_active' : ''"
        :style="topBtnFlg ? 'cursor: pointer;' : 'cursor: not-allowed;'"
        @click="pageTypeCHange('water')"
      >
        <!-- style="cursor: not-allowed" -->
        水环境
      </div>
    </div>
    <div class="leftTab">
      <div
        v-for="item in pageType == 'air' ? leftTabs : leftWaterTabs"
        :key="item.id"
        class="item"
        :class="curtype == item.id ? 'itemActive' : ''"
        @click="curtypeChange(item)"
      >
        {{ item.name }}
      </div>
    </div>
    <div class="bottomTab">
      <div
        class="item"
        :class="curDataType == 1 ? 'item_active' : ''"
        @click="curDataType = '1'"
      >
        天
      </div>
      <div
        class="item"
        :class="curDataType == 2 ? 'item_active' : ''"
        @click="curDataType = '2'"
      >
        月
      </div>
      <div
        v-if="false"
        class="item"
        :class="curDataType == 3 ? 'item_active' : ''"
        @click="curDataType = '3'"
      >
        年
      </div>
    </div>
    <div class="timeRuler">
      <div class="playBtn" @click="playBtnFn">
        <img
          :src="playStutas ? bof : zant"
          alt=""
          style="width: 18px; height: 22px"
        />
      </div>
      <div class="timeCom">
        <div class="ruler">
          <div class="num">
            <div
              class="item"
              v-for="v in ruleCaliNum"
              :key="v"
              @click="rulerClick"
              :data-index="v"
            ></div>
          </div>
          <div class="color" :style="{ right: 1728 - curSite + 'px' }"></div>
          <div class="tips" :style="{ left: curSite - 30 + 'px' }">
            {{ toHourMin(curSite) }}
          </div>
          <div class="calibration">
            <!-- moment().daysInMonth() -->
            <template v-if="curDataType == 1">
              <div class="item" v-for="v in ruleCaliNum" :key="v">
                <span
                  class="timeNum"
                  v-show="
                    ((v - 1) % 4 == 0 && pageType == 'water') ||
                      pageType == 'air'
                  "
                  >{{
                    v - 1 > 9 ? v - 1 + ":00" : "0" + (v - 1 + "") + ":00"
                  }}</span
                >
                <span v-if="v === 24">24:00</span>
              </div>
            </template>
            <template v-if="curDataType == 2">
              <div class="item" v-for="v in ruleCaliNum" :key="v">
                <span class="timeNum" style="left: -0.05rem">{{
                  v > 9 ? v : "0" + v
                }}</span>
              </div>
            </template>
            <template v-if="curDataType == 3">
              <div class="item" v-for="v in ruleCaliNum" :key="v">
                <span class="timeNum" style="left: -0.05rem">{{
                  v > 9 ? v : "0" + v
                }}</span>
              </div>
            </template>
          </div>
        </div>
        <div class="rili">
          <i class="el-icon-date rili_icon"></i>
          <el-date-picker
            v-if="curDataType == 1"
            v-model="selDate"
            value-format="yyyy-MM-dd"
            format="yyyy年MM月dd日"
            type="date"
            size="small"
            popper-class="picker_class"
            prefix-icon="null"
            :clearable="false"
            :editable="false"
            :picker-options="pickerOptions"
            @change="clearAll"
            placeholder="选择日期"
          />
          <el-date-picker
            v-if="curDataType == 2"
            v-model="selDate"
            value-format="yyyy-MM-dd"
            format="yyyy年MM月"
            type="month"
            size="small"
            popper-class="picker_class"
            prefix-icon="null"
            :clearable="false"
            :editable="false"
            :picker-options="pickerOptions"
            @change="clearAll"
            placeholder="选择日期"
          >
          </el-date-picker>
          <el-date-picker
            v-if="curDataType == 3"
            v-model="selDate"
            value-format="yyyy-MM-dd"
            format="yyyy年"
            type="year"
            size="small"
            popper-class="picker_class"
            prefix-icon="null"
            :clearable="false"
            :editable="false"
            :picker-options="pickerOptions"
            @change="clearAll"
            placeholder="选择日期"
          >
          </el-date-picker>
        </div>
      </div>
    </div>
    <!-- 污染源特征雷达分析 -->
    <div class="analysisChartBox">
      <analysisChart :analysisData="analysisData"></analysisChart>
    </div>
    <!-- 弹框部分-->
    <a-modal
      :visible="visibleCheck"
      class="ant-modal-recheck"
      :footer="null"
      :destroyOnClose="true"
      @cancel="handleCancel"
    >
      <div class="smapContainer">
        <div
          class="sigleSmBox"
          v-for="(item, i) in samllMapList"
          :key="i"
          :style="{ 'margin-right': i / 5 || i == 0 ? '22px' : '0' }"
        >
          <div class="dzIcon">
            <img
              src="../assets/recheck/<EMAIL>"
              style="width:18px; height:21px; margin-left:10px;"
              alt=""
            />
            {{ toMoadlTime(selDate) }}{{ curDataType == "1" ? i : i + 1
            }}{{ curDataType == "1" ? "时" : "日" }}
          </div>
          <img
            src="../assets/recheck/<EMAIL>"
            class="smBorderImg"
            alt=""
          />
          <div class="smImgBox">
            <img :src="item.url" class="smImg" @click="preview(item)" />
          </div>
        </div>
      </div>
      <preview-image
        ref="previewImage"
        :show-viewer="showViewer"
        :url-list="urlList"
        @closeViewer="closeViewer"
      />
    </a-modal>
  </section>
</template>

<script lang="ts">
interface EchartData {
  bottomList: string[];
  dataList: string[];
  unit?: string | number;
  colorType?: string;
}
interface InData {
  name: string;
  dataList: any[];
  colorList: string[];
}
import moment from "moment";
import { Component, Vue, Watch } from "vue-property-decorator";
import { Swiper, SwiperSlide } from "vue-awesome-swiper";
import DepartmentBgMap from "@/components/GaoDeMap/departmentBgMap.vue";
import DepartmentMapSmall from "@/components/GaoDeMap/deparmentMapSmall.vue";
import { Table, Radio, Select, Icon, Modal, Tooltip } from "ant-design-vue";
import LineChart from "@/components/Charts/LineChart.vue";
import PollutionPie from "@/components/Charts/PollutionPie.vue";
import PieChartSolid from "@/components/Charts/PieChartSolid.vue";
import DoublePie from "@/components/Charts/DoublePie.vue";
import RadarChart from "@/components/Charts/RadarChart.vue";
import SitePie from "@/components/Charts/SitePie.vue";
import taskExecutionRateLine from "@/components/Charts/taskExecutionRateLine.vue";
import taskProcessing from "@/components/Charts/taskProcessing.vue";
import PreviewImage from "./previewimg.vue";
import analysisChart from "./swiperHome/analysisChart.vue";

import {
  recentWeather,
  pollutionCount,
  heavyPollutionList,
  earlyWarning,
  airTrend,
  airStation,
  waterStationList,
  waterStationRecord,
  getAirTypes,
  waterTrend,
  waterQuality,
  heavilyPollutingEnterpriseList,
  getCarList,
  getOnlineCount,
  getOffline,
  typeStationInfoList,
  selectMonthTaskCompletion,
  selectTaskTypeCompletion,
  selectTypeStatistic,
} from "@/api/homeTable";
import { getAllAqiInfo } from "@/api/air";
import { getStationList, getMonitorItemDetailRecord } from "@/api/water";
import gif01 from "@/assets/sunlight.gif";
import gif02 from "@/assets/cloudy.gif";
import gif03 from "@/assets/overcast.gif";
import gif04 from "@/assets/rain.gif";
import gif05 from "@/assets/xue.gif";
import bof from "@/assets/recheck/-s-bofang.png";
import zant from "@/assets/recheck/-s-zanting.png";
import airBlank from "@/assets/recheck/air_blank.png";
import waterBlank from "@/assets/recheck/water_blank.png";
import anime from "animejs/lib/anime.es.js";

let vm: any = null;

@Component({
  name: "swiperHome",
  components: {
    LineChart,
    PieChartSolid,
    RadarChart,
    Swiper,
    SwiperSlide,
    ATable: Table,
    ASelect: Select,
    AIcon: Icon,
    ASelectOption: Select.Option,
    ARadioGroup: Radio.Group,
    ARadioButton: Radio.Button,
    DoublePie,
    AModal: Modal,
    ATooltip: Tooltip,
    DepartmentBgMap,
    DepartmentMapSmall,
    PollutionPie,
    SitePie,
    taskExecutionRateLine,
    taskProcessing,
    PreviewImage,
    analysisChart,
  },
})
export default class extends Vue {
  private showViewer: any = false;
  private urlList: any = [];
  private preview(item: any) {
    console.log(item);

    if (!item.flg) return;
    this.urlList = [item.url];
    this.showViewer = true;
  }
  private closeViewer() {
    this.showViewer = false;
    this.urlList = [];
  }
  private analysisData: any = {};
  executionRate: any = {};
  completeData: any = {};
  timeTypeList: any = [
    { label: "当天", value: 1 },
    { label: "本周", value: 2 },
    { label: "本月", value: 3 },
    { label: "全部", value: 4 },
  ];
  timeTypeList1: any = [
    { label: "当天", value: 1 },
    { label: "本周", value: 2 },
    { label: "本月", value: 3 },
    { label: "全部", value: 4 },
  ];
  selectTaskTypeCompletionType: number | string = 1;
  taskType: number | string = 1;
  taskClassificationStatistics: any = [];
  dqhj: string = require("@/assets/swiperHome/dqhj.png");
  rwdd: string = require("@/assets/swiperHome/rwdd.png");
  shj: string = require("@/assets/swiperHome/shj.png");
  wry: string = require("@/assets/swiperHome/wry.png");
  contentListVisiable: boolean = true;
  // 空气类型
  @Watch("stationTypeIndnx", { immediate: true, deep: true })
  public onIndex(newValue: number | string, oldValue: number | string) {
    if (newValue) {
      // this.()
    }
  }
  // 空气名称
  @Watch("airStationTypeName", { immediate: true, deep: true })
  public onName(newValue: number | string, oldValue: number | string) {
    if (newValue) {
      // this.()
    }
  }
  // 水名称
  @Watch("stationIdIndex", { immediate: true, deep: true })
  public onStationIdIndex(
    newValue: number | string,
    oldValue: number | string
  ) {
    if (newValue) {
      // this.()
    }
  }
  private mapStyle = "amap://styles/0739a6632d849ae1b5e6f1c52f7d12c3";
  @Watch("curMainIndex", { immediate: true, deep: true }) public onMsgChanged(
    newValue: number,
    oldValue: number
  ) {
    switch (this.curMainIndex) {
      case 1:
        clearInterval(this.airTimer);
        clearInterval(this.waterTimer);
        break;
      case 2:
        this.openAirTimer();
        clearInterval(this.waterTimer);
        break;
      case 3:
        this.openWaterTimer();
        clearInterval(this.airTimer);
        break;
      default:
        clearInterval(this.waterTimer);
        clearInterval(this.airTimer);
        break;
    }
  }
  @Watch("curDataType", { immediate: true })
  private curDataTypeChange(num: any) {
    this.pageTypeFlg = true;
    if (num == 1) {
      this.selDate = moment().format("YYYY-MM-DD");
      this.ruleCaliNum = 24;
      const h = +moment().format("HH");
      const m = +moment().format("mm");
      // const todaySite = h * 72
      const todaySite = h * 72;
      this.curSite = todaySite;
      this.curHour = Math.floor(this.curSite / 72);
    } else if (num == 2) {
      this.selDate = moment()
        .subtract(1, "days")
        .format("YYYY-MM-DD");
      this.ruleCaliNum = moment(this.selDate).daysInMonth();
      const d = +moment().format("DD") - 1;
      const h = +moment().format("HH");
      const DL =
        this.ruleCaliNum == 30
          ? 56.7
          : this.ruleCaliNum == 31
          ? 55.74
          : this.ruleCaliNum == 29
          ? 59.6
          : 61.4;
      const HL =
        this.ruleCaliNum == 30
          ? 2.4
          : this.ruleCaliNum == 31
          ? 2.3
          : this.ruleCaliNum == 29
          ? 2.48
          : 2.56;
      const todaySite = d * DL;
      this.curSite = todaySite;
      this.curHour = Math.floor(this.curSite / DL);
    } else {
      this.ruleCaliNum = 12;
    }
    this.$nextTick(() => {
      clearInterval(this.rulerTimer);
      clearInterval(this.rulerTimerM);
      this.playStutas = false;
      if (!vm || !vm.$refs.mapSwiper) return;
      vm.$refs.mapSwiper.$swiper.slideToLoop(this.curHour, 300, true);
    });
  }
  @Watch("selDate")
  private selDateChange() {
    if (this.curDataType == "1") {
      this.ruleCaliNum = 24;
    } else if (this.curDataType == "2") {
      this.ruleCaliNum = moment(this.selDate).daysInMonth();
    } else {
      this.ruleCaliNum = 12;
    }
  }
  @Watch("samllMapList", { deep: true })
  private samllMapListChange() {
    if (!this.samllMapList.length) return;
    this.$nextTick(() => {
      if (!vm || !vm.$refs.mapSwiper) return;
      vm.$refs.mapSwiper.$swiper.slideToLoop(this.curHour, 300, true);
    });
  }
  @Watch("curHour")
  public curHourChange(num: any) {
    if (this.curDataType == "1") {
      if (num == 24) {
        if (!vm.$refs.mapSwiper) return;
        vm.$refs.mapSwiper.$swiper.slideToLoop(23, 300, true);
        return;
      }
      this.$nextTick(() => {
        if (!vm.$refs.mapSwiper) return;
        vm.$refs.mapSwiper.$swiper.slideToLoop(num, 300, true);
      });
    } else if (this.curDataType == "2") {
      if (num == this.ruleCaliNum) {
        if (!vm.$refs.mapSwiper) return;
        vm.$refs.mapSwiper.$swiper.slideToLoop(this.ruleCaliNum - 1, 300, true);
      }
      this.$nextTick(() => {
        if (!vm.$refs.mapSwiper) return;
        vm.$refs.mapSwiper.$swiper.slideToLoop(num, 300, true);
      });
    }
  }
  @Watch("curSite", {})
  private curSiteChange(num: number) {
    if (this.curDataType == "1") {
      const h = +moment().format("HH");
      const m = +moment().format("mm");
      const todaySite = h * 72 + m * 1.2;
      if (
        num > 1728 ||
        (todaySite <= num && moment().format("YYYY-MM-DD") == this.selDate)
      ) {
        clearInterval(this.rulerTimer);
        // if (!this.first) this.playStutas = !this.playStutas
        this.playStutas = false;
        // this.palyFlg = true
      }
    } else if (this.curDataType == "2") {
      this.ruleCaliNum = moment(this.selDate).daysInMonth();
      const d = +moment().format("DD") - 1;
      const h = +moment().format("HH");
      const DL =
        this.ruleCaliNum == 30
          ? 56.7
          : this.ruleCaliNum == 31
          ? 55.74
          : this.ruleCaliNum == 29
          ? 59.6
          : 61.4;
      const HL =
        this.ruleCaliNum == 30
          ? 2.4
          : this.ruleCaliNum == 31
          ? 2.3
          : this.ruleCaliNum == 29
          ? 2.48
          : 2.56;
      const todaySite = d * DL + h * HL;
      if (
        (moment().format("YYYY-MM") == moment(this.selDate).format("YYYY-MM") &&
          todaySite <= num) ||
        num >= 1728
      ) {
        clearInterval(this.rulerTimerM);
        // if (!this.first) this.playStutas = !this.playStutas
        this.playStutas = false;
        // this.palyFlg = true
      }
    }
  }
  @Watch("pageType")
  private pageTypeChange() {
    this.samllMapList = [];
    this.analyse = null;
    this.maxList = [];
    this.maxPoint = null;
    this.pageTypeFlg = true;
    if (this.pageType == "air") {
      this.curtype = "aqi";
      this.curtypeName = "AQI";
    } else {
      this.curtype = "wqi";
      this.curtypeName = "WQI";
    }
  }
  @Watch("curtypeName")
  private curtypeNameChange() {
    this.pageTypeFlg = true;
  }
  private curMainIndex = 0;
  private pollutionShow = false;
  private mainTimer: any = null;
  private airTimer: any = null;
  private waterTimer: any = null;
  private waterTimerSelect: any = null;
  private airTimerSelect: any = null;
  private nameList: any = ["空气", "污染", "水质", "设备"];
  // AQI
  readonly AQIAirColors: Array<any> = [
    {
      color: "rgb(0,255,0)",
      textColor: "#333",
      min: 0,
      max: 50,
    },
    {
      color: "rgb(255,255,0)",
      textColor: "#333",
      min: 51,
      max: 100,
    },
    {
      color: "rgb(255,126,0)",
      textColor: "white",
      min: 101,
      max: 150,
    },
    {
      color: "rgb(255,0,0)",
      textColor: "white",
      min: 151,
      max: 200,
    },
    {
      color: "rgb(153,0,76)",
      textColor: "white",
      min: 201,
      max: 300,
    },
    {
      color: "rgb(126,0,35)",
      textColor: "white",
      min: 301,
      max: 999,
    },
  ];
  //气象监测
  private weather = {};
  // 空气污染
  private airQuality: any[] = [];
  // 水质污染
  private waterQuality: any[] = [];
  private waterTimeType = "1";
  // 水质监测趋势
  private waterTrendAll: any[] = [];
  private curWaterIndex = 0;
  // 空气监测趋势类型
  private airTypes: { name: string; value: string | number }[] = [];
  // 空气监测趋势选中下标
  private airTypeIndex = 0;
  // 空气监测趋势
  private airMonitoring: EchartData = {
    bottomList: [],
    dataList: [],
    unit: "",
    colorType: "",
  };
  //车辆类型分布
  private carTypeProp: {
    name: string;
    indicator: { name: string; max: number }[];
    dataList: number[];
  } = {
    name: "车辆类型分布",
    indicator: [
      { name: "垃圾清运车", max: 100 },
      { name: "雾炮车", max: 100 },
      { name: "餐厨清运车", max: 100 },
      { name: "洒水车", max: 100 },
      { name: "洗扫车", max: 100 },
      { name: "执法车", max: 100 },
    ],
    dataList: [50, 60, 40, 50, 60, 70],
  };
  //车辆出勤统计
  private carAttendance: {
    name: string;
    indicator: { name: string; max: number }[];
    dataList: number[];
  } = {
    name: "车辆出勤统计",
    indicator: [
      { name: "垃圾清运车", max: 100 },
      { name: "雾炮车", max: 100 },
      { name: "餐厨清运车", max: 100 },
      { name: "洒水车", max: 100 },
      { name: "洗扫车", max: 100 },
      { name: "执法车", max: 100 },
    ],
    dataList: [40, 60, 50, 70, 60, 50],
  };
  // 重污企业排放量
  private swiperOptionPollution = {
    direction: "vertical",
    slidesPerView: 5,
    slidesPerGroup: 1,
    loop: true,
    autoplay: {
      delay: 5000,
      disableOnInteraction: false,
    },
  };
  // 重污企业排放量
  private heavyPollution = [];
  // 空气监测站点
  private swiperOptionAirs = {
    direction: "vertical",
    slidesPerView: 5,
    slidesPerGroup: 1,
    loop: true,
    autoplay: {
      delay: 5000,
      disableOnInteraction: false,
    },
  };
  private airStationList: any[] = [];
  private airStationIndex = 0;
  // 空气站点记录
  private airsData = [];
  // 水质监测站点
  private swiperOptionWater = {
    direction: "vertical",
    slidesPerView: 5,
    slidesPerGroup: 1,
    loop: true,
    autoplay: {
      delay: 5 * 1000,
      disableOnInteraction: false,
    },
  };
  private waterStation: any[] = [];
  private stationId = "20171207000002";
  private pollutionType = "s";
  // 水质监测记录
  private waterData = [];
  // 实时监测预警
  private swiperOption = {
    direction: "vertical",
    slidesPerView: 4,
    slidesPerGroup: 1,
    loop: true,
    autoplay: {
      delay: 5 * 1000,
      disableOnInteraction: false,
    },
  };
  // 实时监测预警
  private airWarning = [];
  // 车辆类型列表
  private carTypeList: any[] = [];
  // 设备信息
  private stationTypeName: any = "空气";
  // 设备状态
  private stateList: any[] = [
    {
      name: "空气站 47个",
      number: 24,
      total: 47,
      startColor: "rgba(64, 119, 200, 1)",
      endColor: "rgba(33, 185, 241, 1)",
    },
    {
      name: "水站 10个",
      number: 4,
      total: 10,
      startColor: "rgba(64, 119, 200, 1)",
      endColor: "rgba(33, 185, 241, 1)",
    },
    {
      name: "摄像头 8个",
      number: 4,
      total: 8,
      startColor: "rgba(64, 119, 200, 1)",
      endColor: "rgba(33, 185, 241, 1)",
    },
  ];
  // 离线设备列表
  private swiperOptionWarn = {
    direction: "vertical",
    slidesPerView: 5,
    slidesPerGroup: 1,
    loop: true,
    autoplay: {
      delay: 5000,
      disableOnInteraction: false,
    },
  };
  // 离线设备列表
  private offLineList: any[] = [
    {
      name: "连心桥站",
      type: "水站站",
      address: "沙河源街道新桥社区东北方向",
      time: "5月8日 13:20",
    },
    {
      name: "连心桥站",
      type: "水站站",
      address: "沙河源街道新桥社区东北方向",
      time: "5月8日 13:20",
    },
    {
      name: "连心桥站",
      type: "水站站",
      address: "沙河源街道新桥社区东北方向",
      time: "5月8日 13:20",
    },
    {
      name: "连心桥站",
      type: "水站站",
      address: "沙河源街道新桥社区东北方向",
      time: "5月8日 13:20",
    },
    {
      name: "连心桥站",
      type: "水站站",
      address: "沙河源街道新桥社区东北方向",
      time: "5月8日 13:20",
    },
    {
      name: "连心桥站",
      type: "水站站",
      address: "沙河源街道新桥社区东北方向",
      time: "5月8日 13:20",
    },
  ];
  // 演练复盘参数-----start
  private pageTypeFlg: any = false;
  // 天月年 类型
  private curDataType: any = "1";
  private ruleCaliNum: any = 24;
  // 热力图最大值map
  heathMapMaxMap = {
    aqi: 300,
    "105": 250,
    "104": 420,
    "101": 2340,
    "102": 800,
    "103": 90,
    "100": 800,
  };
  //选择日期
  private selDate = moment().format("YYYY-MM-DD");
  // 禁止选择今天之后日期
  private pickerOptions: any = {
    disabledDate(time: any) {
      return time.getTime() > Date.now();
    },
  };

  // 当前进度条走到的小时数
  private curHour: any = null;
  // 当前进度条位置
  private curSite: any = 0;
  // 播放状态
  private playStutas: any = false;
  // 播放禁止
  private palyFlg: any = false;
  // 进度条定时器
  private rulerTimer: any = null;
  private rulerTimerM: any = null;
  // 左侧空气tab栏
  private leftTabs: any = [
    { name: "AQI", id: "aqi" },
    { name: "PM₁₀", id: "104" },
    { name: "PM₂.₅", id: "105" },
    { name: "SO₂", id: "100" },
    { name: "NO₂", id: "101" },
    { name: "O₃", id: "102" },
    { name: "CO", id: "103" },
  ];
  // 左侧水质tab栏 wqi ,002 氨氮， 004 总磷，006 高猛， 009溶解氧
  private leftWaterTabs: any = [
    { name: "WQI", id: "wqi" },
    { name: "总磷", id: "004" },
    { name: "氨氮", id: "002" },
    { name: "溶解氧", id: "009" },
    { name: "高锰酸盐指数", id: "006" },
    // { name: 'PH值', id: 'wqi' },
  ];
  // 当前污染监测参数类型
  private curtype = "aqi";
  private curtypeName = "AQI";
  // 当前页面类型--大气、水
  private pageType = "air";
  private bof: any = bof;
  private zant: any = zant;
  private first: any = true;
  private hasNoData: any = false;
  private samllMapList: any = [];
  private clickRule: any = true;
  private swiperOptionAlert = {
    effect: "coverflow",
    grabCursor: true,
    slidesPerView: 3,
    slidesPerGroup: 1,
    centeredSlides: true,
    // loop: true,
    spaceBetween: 0,
    coverflowEffect: {
      rotate: 5,
      stretch: 46, //每个slide之间的拉伸值，越大slide靠得越紧。 默认0。
      depth: 100, //slide的位置深度。值越大z轴距离越远，看起来越小。 默认100。
      modifier: 1, //depth和rotate和stretch的倍率，相当于depth*modifier、rotate*modifier、stretch*modifier，值越大这三个参数的效果越明显。默认1。
      slideShadows: true,
    },
    on: {
      transitionEnd: function() {
        // vm.playStutas = true
        // vm.playBtnFn()
        const _this: any = this;
        if (_this.activeIndex == vm.curHour) return;
        if (vm.first) {
          vm.first = false;
          return;
        }
        if (vm.curDataType == "1" && vm.clickRule) {
          const selDates =
            vm.selDate +
            " " +
            (vm.curHour > 9 ? vm.curHour : "0" + vm.curHour) +
            ":00:00";
          vm.curHour = _this.activeIndex;
          vm.curSite = _this.activeIndex * 72;
        } else if (vm.curDataType == "2" && vm.clickRule) {
          const selDates =
            moment(vm.selDate).format("YYYY-MM") +
            "-" +
            (vm.curHour + 1 > 9 ? vm.curHour + 1 : "0" + (vm.curHour + 1)) +
            " " +
            "00:00:00";
          const DL =
            vm.ruleCaliNum == 30
              ? 56.7
              : vm.ruleCaliNum == 31
              ? 55.74
              : vm.ruleCaliNum == 29
              ? 59.6
              : 61.4;
          vm.curHour = _this.activeIndex;
          vm.curSite = _this.activeIndex * DL;
        }
      },
    },
  };
  private swiperOptionAlertV = {
    direction: "vertical",
    slidesPerView: 5,
    slidesPerGroup: 1,
    loop: true,
    autoplay: {
      delay: 3500,
      disableOnInteraction: false,
    },
  };
  private analyse: any = null; // 右侧分析
  private maxPoint: any = null; // 右侧小地图中心点
  private maxList: any = []; // 右侧表格
  private samllMapHeatData: any = []; // 小地图
  private visibleCheck: any = false;
  private waterTimeFlg: any = true;
  private analysisQuery: any = {};
  // 演练复盘参数-----end
  private topBtnFlg: any = false;
  private topBtnOpen() {
    this.topBtnFlg = true;
  }
  private pageTypeCHange(type: any) {
    if (!this.topBtnFlg) return;
    this.pageType = type;
  }
  created() {
    vm = this;
  }
  mounted() {
    // Promise.all([
    //   this.fetchRecentWeather(),
    //   this.fetchPollutionCount(),
    //   this.getWaterQuality(),
    //   this.fetchHeavyPollution(),
    //   this.getAirStation(),
    //   this.fetchWaterStationList(),
    //   // this.fetchEarlyWarning(),
    //   this.fetchCarList(),
    //   this.getEquipmentStatus(),
    //   this.fetchOffline(),
    //   this.getTypeStationInfoList(),
    //   this.getSelectMonthTaskCompletion(),
    //   this.getSelectTaskTypeCompletion(),
    //   this.getSelectTypeStatistic()
    // ]).then(() => {
    //   setTimeout(() => {
    //     this.curMainIndex = 1;
    //     this.openMainTimer();
    //     this.stationTypeNameChange("空气");
    //   }, 500);
    // });
  }
  beforeDestroy() {
    clearInterval(this.airTimer);
    clearInterval(this.waterTimer);
    clearInterval(this.mainTimer);
    // 复盘定时器
    clearInterval(this.rulerTimer);
    clearInterval(this.rulerTimerM);
  }
  handleGetCharacteristicRadarChart(data: any) {
    this.analysisData = data;
  }
  private handleChangeAnalysis(data: any) {
    console.log("data11111111111", data);
    this.analysisQuery = data;
    console.log("this.analysisQuery", this.analysisQuery);
  }
  private sMapMore() {
    this.visibleCheck = true;
  }
  private handleCancel() {
    this.visibleCheck = false;
  }
  private toMoadlTime(time: any) {
    if (this.curDataType == "1") {
      return moment(time).format("MM月DD日");
    } else if (this.curDataType == "2") {
      return moment(time).format("MM月");
    }
  }
  private sMapTime(num: any) {
    let str: any = "";
    if (
      (this.selDate == moment().format("YYYY-MM-DD") &&
        this.curDataType == "1") ||
      (moment(this.selDate).format("YYYY-MM") == moment().format("YYYY-MM") &&
        this.curDataType == "2")
    ) {
      if (this.curDataType == "1") {
        if (
          this.curHour + 1 == this.samllMapList.length ||
          this.curHour == this.samllMapList.length
        ) {
          if (num == 1) {
            str = "";
          } else if (num == 2) {
            str = "";
          } else {
            str =
              this.curHour + num > 9
                ? this.curHour + num
                : "0" + (this.curHour + num);
          }
        } else if (this.curHour + 2 == this.samllMapList.length) {
          if (num == 2) {
            str = "";
          } else {
            str =
              this.curHour + num > 9
                ? this.curHour + num
                : "0" + (this.curHour + num);
          }
        } else if (this.curHour == 0) {
          const timeN = this.samllMapList.length;
          if (num == -1) {
            // str = timeN > 9 ? timeN : '0' + timeN
            str = "";
          } else if (num == -2) {
            // str = timeN - 1 > 9 ? timeN - 1 : '0' + (timeN - 1)
            str = "";
          } else {
            str =
              this.curHour + num > 9
                ? this.curHour + num
                : "0" + (this.curHour + num);
          }
        } else if (this.curHour == 1) {
          const timeN = this.samllMapList.length;
          if (num == -2) {
            str = "";
          } else {
            str =
              this.curHour + num > 9
                ? this.curHour + num
                : "0" + (this.curHour + num);
          }
        } else {
          str =
            this.curHour + num > 9
              ? this.curHour + num
              : "0" + (this.curHour + num);
        }
      } else if (this.curDataType == "2") {
        if (
          this.curHour + 1 == this.ruleCaliNum ||
          this.curHour + 1 == this.samllMapList.length
        ) {
          if (num == 1) {
            str = "";
          } else if (num == 2) {
            str = "";
          } else {
            let val = this.curHour + 1 + num;
            str = val > 9 ? val : "0" + val;
          }
        } else if (this.curHour == 0) {
          if (num == -1) {
            str = "";
          } else if (num == -2) {
            str = "";
          } else {
            let val = this.curHour + 1 + num;
            str = val > 9 ? val : "0" + val;
          }
        } else if (this.curHour == 1) {
          if (num == -1) {
            let val = this.curHour + 1 + num;
            str = val > 9 ? val : "0" + val;
          } else if (num == -2) {
            str = "";
          } else {
            let val = this.curHour + 1 + num;
            str = val > 9 ? val : "0" + val;
          }
        } else if (
          this.curHour == this.ruleCaliNum - 2 ||
          this.curHour + 2 == this.samllMapList.length
        ) {
          if (num == 2) {
            str = "";
          } else {
            let val = this.curHour + 1 + num;
            str = val > 9 ? val : "0" + val;
          }
        } else {
          str =
            this.curHour + num + 1 > 9
              ? this.curHour + num + 1
              : "0" + (this.curHour + num + 1);
        }
      }
    } else {
      if (this.curDataType == "1") {
        if (
          this.curHour + 1 == this.samllMapList.length ||
          this.curHour == this.samllMapList.length
        ) {
          if (num == 1) {
            str = "";
          } else if (num == 2) {
            str = "";
          } else {
            str =
              this.curHour + num > 9
                ? this.curHour + num
                : "0" + (this.curHour + num);
          }
        } else if (this.curHour + 2 == this.samllMapList.length) {
          if (num == 2) {
            str = "";
          } else {
            str =
              this.curHour + num > 9
                ? this.curHour + num
                : "0" + (this.curHour + num);
          }
        } else if (this.curHour == 0) {
          if (num == -1) {
            str = "";
          } else if (num == -2) {
            str = "";
          } else {
            str =
              this.curHour + num > 9
                ? this.curHour + num
                : "0" + (this.curHour + num);
          }
        } else if (this.curHour == 1) {
          if (num == -2) {
            str = "";
          } else {
            str =
              this.curHour + num > 9
                ? this.curHour + num
                : "0" + (this.curHour + num);
          }
        } else if (this.curHour == 23) {
          if (num == 2) {
            str = "";
          } else {
            str =
              this.curHour + num > 9
                ? this.curHour + num
                : "0" + (this.curHour + num);
          }
        } else {
          str =
            this.curHour + num > 9
              ? this.curHour + num
              : "0" + (this.curHour + num);
        }
      } else if (this.curDataType == "2") {
        // console.log(this.curHour, this.ruleCaliNum);
        if (this.curHour + 1 == this.ruleCaliNum) {
          if (num == 1) {
            str = "";
          } else if (num == 2) {
            str = "";
          } else {
            let val = this.curHour + 1 + num;
            str = val > 9 ? val : "0" + val;
          }
        } else if (this.curHour == 0) {
          if (num == -1) {
            str = "";
          } else if (num == -2) {
            str = "";
          } else {
            let val = this.curHour + 1 + num;
            str = val > 9 ? val : "0" + val;
          }
        } else if (this.curHour == 1) {
          if (num == -1) {
            let val = this.curHour + 1 + num;
            str = val > 9 ? val : "0" + val;
          } else if (num == -2) {
            str = "";
          } else {
            let val = this.curHour + 1 + num;
            str = val > 9 ? val : "0" + val;
          }
        } else if (this.curHour == this.ruleCaliNum - 2) {
          if (num == 2) {
            str = "";
          } else if (num == 1) {
            str = this.ruleCaliNum;
          } else {
            let val = this.curHour + 1 + num;
            str = val > 9 ? val : "0" + val;
          }
        } else {
          str =
            this.curHour + num + 1 > 9
              ? this.curHour + num + 1
              : "0" + (this.curHour + num + 1);
        }
      }
    }
    return str;
  }
  private getPhoto(url: any, idx: any, flg: any) {
    // this.$nextTick(() => {
    const src = this.pageType == "air" ? airBlank : waterBlank;
    if (flg || this.pageTypeFlg) {
      this.pageTypeFlg = false;
      this.samllMapList = [];
      if (
        (this.selDate == moment().format("YYYY-MM-DD") &&
          this.curDataType == "1") ||
        (moment(this.selDate).format("YYYY-MM") == moment().format("YYYY-MM") &&
          this.curDataType == "2")
      ) {
        let idxNum =
          this.curDataType == "1"
            ? +moment().format("HH")
            : +moment().format("DD");
        for (let i = 0; i < idxNum; i++) {
          this.samllMapList.push({ url: src, flg: false });
        }
      } else {
        let idxNum =
          this.curDataType == "1" ? 24 : +moment(this.selDate).daysInMonth();
        for (let i = 0; i < idxNum; i++) {
          this.samllMapList.push({ url: src, flg: false });
        }
      }
    } else {
      if (
        (this.selDate == moment().format("YYYY-MM-DD") &&
          this.curDataType == "1") ||
        (moment(this.selDate).format("YYYY-MM") == moment().format("YYYY-MM") &&
          this.curDataType == "2")
      ) {
        this.samllMapList = [];
        let idxNum =
          this.curDataType == "1"
            ? +moment().format("HH")
            : +moment().format("DD");
        if (this.samllMapList.length != idxNum) {
          for (let i = 0; i < idxNum; i++) {
            this.samllMapList.push({ url: src, flg: false });
          }
        }
        this.$set(this.samllMapList, idx, { url, flg: true });
      } else {
        if (this.samllMapList.length != this.ruleCaliNum) {
          for (let i = 0; i < this.ruleCaliNum; i++) {
            this.$set(this.samllMapList, i, { url: src, flg: false });
          }
        }
        this.$set(this.samllMapList, idx, { url, flg: true });
      }
    }
    // })
  }
  private getRightData(
    maxList: any,
    maxPoint: any,
    analyse: any,
    heatData: any
  ) {
    this.analyse = analyse;
    this.maxPoint = maxPoint;
    this.maxList = maxList;
    this.samllMapHeatData = heatData;
  }
  // 播放按钮
  private playBtnFn() {
    if (this.curSite > 1728 || this.palyFlg) {
      this.playStutas = false;
      clearInterval(this.rulerTimer);
      clearInterval(this.rulerTimerM);
      return;
    }
    if (this.curDataType == "1") {
      const h = +moment().format("HH");
      const m = +moment().format("mm");
      const todaySite1 = h * 72 + m * 1.2;
      if (
        todaySite1 <= this.curSite &&
        moment().format("YYYY-MM-DD") == this.selDate
      ) {
        this.playStutas = false;
        clearInterval(this.rulerTimer);
        clearInterval(this.rulerTimerM);
        return;
      }
    } else if (this.curDataType == "2") {
      let ruleCaliNum = moment(this.selDate).daysInMonth();
      const d = +moment().format("DD") - 1;
      const h = +moment().format("HH");
      const DL =
        ruleCaliNum == 30
          ? 56.7
          : ruleCaliNum == 31
          ? 55.74
          : ruleCaliNum == 29
          ? 59.6
          : 61.4;
      const HL =
        ruleCaliNum == 30
          ? 2.4
          : ruleCaliNum == 31
          ? 2.3
          : ruleCaliNum == 29
          ? 2.48
          : 2.56;
      const todaySite = d * DL + h * HL;
      if (
        moment().format("YYYY-MM") == moment(this.selDate).format("YYYY-MM") &&
        todaySite <= this.curSite
      ) {
        this.playStutas = false;
        clearInterval(this.rulerTimer);
        clearInterval(this.rulerTimerM);
        return;
      }
    }
    this.playStutas = !this.playStutas;
    this.first = false;
    this.palyFlg = false;
    if (!this.playStutas) {
      clearInterval(this.rulerTimer);
      clearInterval(this.rulerTimerM);
      return;
    }
    if (this.curDataType == 1) {
      this.rulerTimer = setInterval(() => {
        this.curSite += 1.2;
        this.curHour =
          Math.floor(this.curSite / 72) == 24
            ? 23
            : Math.floor(this.curSite / 72);
      }, 10);
    } else if (this.curDataType == 2) {
      const DL =
        this.ruleCaliNum == 30
          ? 56.7
          : this.ruleCaliNum == 31
          ? 55.74
          : this.ruleCaliNum == 29
          ? 59.6
          : 61.4;
      const HL =
        this.ruleCaliNum == 30
          ? 2.4
          : this.ruleCaliNum == 31
          ? 2.3
          : this.ruleCaliNum == 29
          ? 2.48
          : 2.56;
      this.rulerTimerM = setInterval(() => {
        this.curSite += HL;
        this.curHour =
          Math.floor(this.curSite / DL) >= this.ruleCaliNum
            ? this.ruleCaliNum - 1
            : Math.floor(this.curSite / DL);
      }, 3);
    } else {
    }
    setTimeout(() => {
      this.clickRule = true;
    }, 500);
  }
  // 事件尺点击
  private rulerClick(e: any) {
    this.first = false;
    this.clickRule = false;
    const index = +e.target.dataset.index - 1;
    const offsetX = e.offsetX;
    const DL =
      this.ruleCaliNum == 30
        ? 56.7
        : this.ruleCaliNum == 31
        ? 55.74
        : this.ruleCaliNum == 29
        ? 59.6
        : 61.4;
    const HL =
      this.ruleCaliNum == 30
        ? 2.4
        : this.ruleCaliNum == 31
        ? 2.3
        : this.ruleCaliNum == 29
        ? 2.48
        : 2.56;
    if (this.curDataType == "1") {
      this.curSite = index * 72 + offsetX;
      clearInterval(this.rulerTimer);
      clearInterval(this.rulerTimerM);
      const h = +moment().format("HH");
      const m = +moment().format("mm");
      const todaySite = h * 72 + m * 1.2;
      if (
        todaySite <= this.curSite &&
        moment().format("YYYY-MM-DD") == this.selDate
      ) {
        this.curSite = todaySite;
      }
    } else if (this.curDataType == "2") {
      this.curSite = index * DL + offsetX;
      clearInterval(this.rulerTimer);
      clearInterval(this.rulerTimerM);
      const h = +moment().format("DD");
      const m = +moment().format("HH");
      const todaySite = h * DL + m * HL;
      if (
        todaySite <= this.curSite &&
        moment().format("YYYY-MM") == moment(this.selDate).format("YYYY-MM")
      ) {
        this.curSite = todaySite;
      }
    }
    if (this.playStutas) this.playStutas = !this.playStutas;
    // this.palyFlg = false
    this.playBtnFn();
  }
  // 切换监测参数
  private curtypeChange(item: any) {
    this.samllMapList = [];
    this.analyse = null;
    this.maxList = [];
    this.maxPoint = null;
    this.curtype = item.id;
    this.curtypeName = item.name;
    if (
      moment().format("YYYY-MM-DD") == this.selDate &&
      this.curDataType == "1"
    ) {
      this.first = true;
      const h = +moment().format("HH");
      const m = +moment().format("mm");
      const todaySite = h * 72;
      this.curSite = todaySite;
      this.curHour = Math.floor(this.curSite / 72);
    } else if (
      moment().format("YYYY-MM") == moment(this.selDate).format("YYYY-MM") &&
      this.curDataType == "2"
    ) {
      this.first = true;
      let ruleCaliNum = moment(this.selDate).daysInMonth();
      const d = +moment().format("DD") - 1;
      const h = +moment().format("HH");
      const DL =
        ruleCaliNum == 30
          ? 56.7
          : ruleCaliNum == 31
          ? 55.74
          : ruleCaliNum == 29
          ? 59.6
          : 61.4;
      const HL =
        ruleCaliNum == 30
          ? 2.4
          : ruleCaliNum == 31
          ? 2.3
          : ruleCaliNum == 29
          ? 2.48
          : 2.56;
      // const todaySite = d * DL
      const todaySite = d * DL;
      this.curSite = todaySite;
      this.curHour = Math.floor(this.curSite / DL);
    }
  }
  // 清除定时器，距离，暂停
  private clearAll() {
    this.curSite = 0;
    this.curHour = 0;
    this.playStutas = false;
    // this.palyFlg = false
    clearInterval(this.rulerTimer);
    clearInterval(this.rulerTimerM);
  }
  // 没有热力图数据
  private noDataFn(findDate: any, num: any) {
    if (
      (moment().format("YYYY-MM-DD") == this.selDate &&
        this.curDataType == "1") ||
      (moment().format("YYYY-MM") == moment(this.selDate).format("YYYY-MM") &&
        this.curDataType == "2")
    ) {
      if (
        (moment(findDate).format("HH") >= moment().format("HH") &&
          this.curDataType == "1") ||
        (moment().format("DD") >= moment(findDate).format("DD") &&
          this.curDataType == "2")
      ) {
        this.curHour += num;
        if (this.curDataType == "1") {
          this.curSite = this.curHour * 72;
        } else if (this.curDataType == "2") {
          const DL =
            this.ruleCaliNum == 30
              ? 56.7
              : this.ruleCaliNum == 31
              ? 55.74
              : this.ruleCaliNum == 29
              ? 59.6
              : 61.4;
          this.curSite = this.curHour * DL;
        }
        // this.playStutas = true
        // this.playBtnFn()
      } else if (this.pageType == "water") {
        if (this.curHour > 4) {
          console.log(">4", "-------------------------4435");
          this.curHour += num;
        } else {
          console.log("<4", "-------------------------4435");
          this.curHour = 0;
        }
        if (this.curDataType == "1") {
          this.curSite = this.curHour * 72;
        } else if (this.curDataType == "2") {
          const DL =
            this.ruleCaliNum == 30
              ? 56.7
              : this.ruleCaliNum == 31
              ? 55.74
              : this.ruleCaliNum == 29
              ? 59.6
              : 61.4;
          this.curSite = this.curHour * DL;
        }
      }
      console.log(this.curHour, "---------4455");
    }
    // this.clearAll()
    // this.$message.warning('暂无监测数据！')
    // if (!vm.$refs.mapSwiper) return
    // vm.$refs.mapSwiper.$swiper.slideToLoop(0, 300, true)
  }
  // 当天后续没有数据了
  private todayNoData() {
    this.playStutas = false;
    this.palyFlg = true;
    clearInterval(this.rulerTimer);
    clearInterval(this.rulerTimerM);
  }
  // 距离像素转换为时分
  private toHourMin(num: number) {
    if (this.curDataType == 1) {
      const h = Math.floor(this.curSite / 72);
      const m = Math.floor((this.curSite % 72) / 1.2);
      return `${h > 9 ? h : "0" + h}:${m > 9 ? m : "0" + m}`;
    } else if (this.curDataType == 2) {
      const DL =
        this.ruleCaliNum == 30
          ? 56.7
          : this.ruleCaliNum == 31
          ? 55.74
          : this.ruleCaliNum == 29
          ? 59.6
          : 61.4;
      const HL =
        this.ruleCaliNum == 30
          ? 2.4
          : this.ruleCaliNum == 31
          ? 2.3
          : this.ruleCaliNum == 29
          ? 2.48
          : 2.56;
      const m = Math.floor(this.curSite / DL) + 1;
      const h = Math.floor((this.curSite % DL) / HL);
      const MM = moment(this.selDate).format("MM");
      if (h == 25 || h == 24) {
        return `${MM}月${m > 9 ? m : "0" + m}日`;
      } else if (m > this.ruleCaliNum) {
        return `${MM}月${this.ruleCaliNum}日`;
      }
      return `${MM}月${m > 9 ? m : "0" + m}日`;
    } else {
    }
  }
  private clearMainTimer(): void {
    clearInterval(this.mainTimer);
  }
  private clearAirTimer(): void {
    clearInterval(this.airTimer);
  }
  private clearWaterTimer(): void {
    clearInterval(this.waterTimer);
  }
  private changeIndex(index: number): void {
    if (index != this.curMainIndex) {
      clearInterval(this.mainTimer);
      this.curMainIndex = index;
      this.openMainTimer();
    }
  }
  get windLevelNumber(): string {
    if ((this.weather as any).windLevel) {
      const windlevel =
        3.77 - (this.weather as any).windLevel.slice(0, -1) * 0.27;
      return `fly-wind ${windlevel}s linear infinite`;
    } else {
      return "";
    }
  }
  //div切换定时器
  private openMainTimer() {
    this.mainTimer = setInterval(() => {
      this.curMainIndex = this.curMainIndex + 1 > 4 ? 1 : this.curMainIndex + 1;
    }, 30000);
  }
  //开启空气定时器
  private openAirTimer() {
    this.airTimer = setInterval((e: any) => {
      if (this.airTypeIndex >= this.airTypes.length - 1) {
        this.airTypeIndex = 0;
        if (this.airStationIndex >= this.airStationList.length - 1) {
          this.airStationIndex = 0;
        } else {
          this.airStationIndex += 1;
        }
        this.fetchAirTypes();
        this.fetchAirStation();
      } else {
        this.airTypeIndex += 1;
        this.fetchAirTrend();
      }
    }, 8000);
  }
  //开启水质定时器
  private openWaterTimer() {
    this.waterTimer = setInterval((e: any) => {
      if (this.curWaterIndex + 1 < this.waterTrendList.length) {
        this.curWaterIndex = this.curWaterIndex + 1;
        this.typeCode = this.waterTrendList[this.curWaterIndex].itemCode;
        this.typeUnit = this.waterTrendList[
          this.curWaterIndex
        ].concentrationUnit;
        this.fetchWaterTrend();
      } else {
        this.curWaterIndex = 0;
        let curStationIndex: number = this.waterStation.findIndex((item) => {
          return this.stationId == item.stationId;
        });
        curStationIndex =
          curStationIndex + 1 < this.waterStation.length
            ? curStationIndex + 1
            : 0;
        this.waterTrendList = this.waterStation[curStationIndex].monitorItems;
        this.typeCode = this.waterTrendList[this.curWaterIndex].itemCode;
        this.typeUnit = this.waterTrendList[
          this.curWaterIndex
        ].concentrationUnit;
        this.stationId = this.waterStation[curStationIndex].stationId;
        this.fetchWaterTrend();
        this.fetchWaterStationRecord();
      }
    }, 8000);
  }
  //气象监测
  private fetchRecentWeather() {
    return new Promise<void>((resolve, reject) => {
      recentWeather().then((res) => {
        const data = res.data.data;
        console.log(data, "data");
        for (const item of this.AQIAirColors) {
          if (data.aqi <= item.max && data.aqi >= item.min) {
            data.aqiColor = item.color;
            data.color = item.textColor;
            break;
          }
        }
        if (data.weather.indexOf("晴") != -1) {
          data.weatherImg = gif01;
        } else if (data.weather.indexOf("云") != -1) {
          data.weatherImg = gif02;
        } else if (data.weather.indexOf("阴") != -1) {
          data.weatherImg = gif03;
        } else if (data.weather.indexOf("雨") != -1) {
          data.weatherImg = gif04;
        } else if (data.weather.indexOf("雪") != -1) {
          data.weatherImg = gif05;
        }
        if (data.pollutant == "PM10") {
          data.pollutant = "PM₁₀";
        } else if (data.pollutant == "PM2.5") {
          data.pollutant = "PM₂.₅";
        } else if (data.pollutant == "O3") {
          data.pollutant = "O₃";
        } else if (data.pollutant == "SO2") {
          data.pollutant = "SO₂";
        } else if (data.pollutant == "NO2") {
          data.pollutant = "NO₂";
        }
        data.updateTime = data.updateTime
          ? data.updateTime.slice(0, 16)
          : undefined;
        this.weather = data;
        resolve();
      });
    });
  }
  // 环境质量影响--空气污染
  private fetchPollutionCount() {
    return new Promise<void>((resolve, reject) => {
      pollutionCount().then((res: any) => {
        const colorArr = [
          { startColor: "#4077C8", endColor: "#21B9F1" },
          { startColor: "#03CCF6", endColor: "#0EFDF0" },
          { startColor: "#33D3B7", endColor: "#00FFD5" },
          { startColor: "#622DBB", endColor: "#AB7AFB" },
        ];
        const data = res.data.data;
        let total = 0;
        const airQuality: any = [];
        data.forEach((item: any, index: number) => {
          total += item.value;
          airQuality.push({
            name: item.key,
            number: item.value,
            startColor: colorArr[index].startColor,
            endColor: colorArr[index].endColor,
          });
        });
        airQuality.forEach((item: any) => {
          item.total = total;
          switch (item.name) {
            case "NO2":
              item.name = "NO₂";
              break;
            case "SO2":
              item.name = "SO₂";
              break;
            case "O3":
              item.name = "O₃";
              break;
            case "PM10":
              item.name = "PM₁₀";
              break;
            case "PM2.5":
              item.name = "PM₂.₅";
              break;
          }
        });
        this.airQuality = airQuality;
        resolve();
      });
    });
  }
  //水质污染
  private getWaterQuality() {
    return new Promise<void>((resolve, reject) => {
      waterQuality().then((res: any) => {
        const colorArr = [
          { startColor: "#4077C8", endColor: "#21B9F1" },
          { startColor: "#03CCF6", endColor: "#0EFDF0" },
          { startColor: "#33D3B7", endColor: "#00FFD5" },
          { startColor: "#622DBB", endColor: "#AB7AFB" },
        ];
        const data = res.data.data;
        let total = 0;
        const filterArr: any = []; //不为0的项
        const emptyArr: any = []; //为0的项
        data.forEach((item: any) => {
          total = total + item.number; //总共的数据
          if (filterArr.length < 3 && item.number > 0) {
            filterArr.push(item);
          }
          if (item.number == 0) {
            emptyArr.push(item);
          }
        });
        if (filterArr.length < 3) {
          for (let i = 0; i < 3 - filterArr.length; i++) {
            filterArr.push(emptyArr[i]);
          }
        }
        let selectNumber = 0; //已选的数值总和
        filterArr.forEach((item: any) => {
          selectNumber += item.number;
        });
        filterArr.push({ name: "其他", number: total - selectNumber });
        filterArr.forEach((item: any, index: number) => {
          item.total = total;
          item.startColor = colorArr[index].startColor;
          item.endColor = colorArr[index].endColor;
        });
        this.waterQuality = filterArr;
        resolve();
      });
    });
  }
  //重污企业
  private fetchHeavyPollution() {
    // heavyPollutionList
    return new Promise<void>((resolve, reject) => {
      heavilyPollutingEnterpriseList(510106, this.pollutionType).then(
        (res: any) => {
          const data = res.data.data;
          this.heavyPollution = data;
          resolve();
        }
      );
    });
  }
  private pollutionStationChange(e: any) {
    this.pollutionType = e;
    this.fetchHeavyPollution();
  }
  // 切换空气类型
  private airTypeChange(e: any) {
    clearInterval(this.airTimer);
    this.airTypeIndex = e.target.value;
    this.fetchAirTrend();
    this.openAirTimer();
  }
  // 空气监测站点
  private getAirStation() {
    return new Promise<void>((resolve, reject) => {
      getAllAqiInfo({ areaCode: "510106" }).then((res: any) => {
        const data = res.data.data;
        this.airStationList = data;
        Promise.all([this.fetchAirTypes(), this.fetchAirStation()]).then(() => {
          resolve();
        });
      });
    });
  }
  //获取空气监测类型
  private fetchAirTypes() {
    return new Promise<void>((resolve, reject) => {
      getAirTypes({
        stationCode: this.airStationList[this.airStationIndex].stationCode,
      }).then((res) => {
        const data = res.data.data;
        const airTypes: { name: string; value: string | number }[] = [];
        for (const key in data) {
          let airType = "";
          if (key == "SO2") {
            airType = "SO₂";
          } else if (key == "NO2") {
            airType = "NO₂";
          } else if (key == "O3") {
            airType = "O₃";
          } else if (key == "PM10") {
            airType = "PM₁₀";
          } else if (key == "PM2.5") {
            airType = "PM₂.₅";
          }
          airTypes.push({
            name: airType || key,
            value: data[key],
          });
        }
        this.airTypes = airTypes;
        this.fetchAirTrend().then(() => {
          resolve();
        });
      });
    });
  }
  //空气监测趋势
  private fetchAirTrend() {
    const params = {
      pollutantCode: this.airTypes[this.airTypeIndex].value,
      stationCode: this.airStationList[this.airStationIndex].stationCode,
    };
    return new Promise<void>((resolve, reject) => {
      airTrend(params).then((res) => {
        const data = res.data.data;
        const bottomList = [],
          dataList = [];
        let unit = "";
        let colorType = "";
        for (const key in data) {
          bottomList.push(`${key}时`);
          dataList.push(data[key]);
        }
        if (this.airTypes[this.airTypeIndex].value == "103") {
          unit = "mg/m³";
        } else {
          unit = "μg/m³";
        }
        if (this.airTypes[this.airTypeIndex].value == "100") {
          colorType = "SO2";
        } else if (this.airTypes[this.airTypeIndex].value == "101") {
          colorType = "NO2";
        } else if (this.airTypes[this.airTypeIndex].value == "102") {
          colorType = "O3";
        } else if (this.airTypes[this.airTypeIndex].value == "103") {
          colorType = "CO";
        } else if (this.airTypes[this.airTypeIndex].value == "104") {
          colorType = "PM10";
        } else if (this.airTypes[this.airTypeIndex].value == "105") {
          colorType = "PM25";
        }
        console.log(bottomList);
        this.airMonitoring.bottomList = bottomList;
        this.airMonitoring.dataList = dataList;
        this.airMonitoring.unit = unit;
        this.airMonitoring.colorType = colorType;
        resolve();
      });
    });
  }
  //空气站点改变
  private airStationChange(e: any) {
    clearInterval(this.airTimer);
    this.airTypeIndex = 0;
    this.airStationIndex = e;
    this.fetchAirTypes();
    this.fetchAirStation();
    this.openAirTimer();
  }
  //空气监测记录
  private fetchAirStation() {
    return new Promise<void>((resolve, reject) => {
      airStation({
        stationCode: this.airStationList[this.airStationIndex].stationCode,
        stationTypeId: this.airStationList[this.airStationIndex].stationTypeId,
      }).then((res) => {
        const data = res.data.data;
        data.forEach((item: any) => {
          if (item.pollutionCode == "SO2") {
            item.pollutionCode = "SO₂";
          } else if (item.pollutionCode == "NO2") {
            item.pollutionCode = "NO₂";
          } else if (item.pollutionCode == "O3") {
            item.pollutionCode = "O₃";
          } else if (item.pollutionCode == "PM10") {
            item.pollutionCode = "PM₁₀";
          } else if (item.pollutionCode == "PM2.5") {
            item.pollutionCode = "PM₂.₅";
          }
          if (item.unit == "μg/m3") {
            item.unit = "μg/m³";
          }
        });
        this.airsData = data;
        resolve();
      });
    });
  }
  //获取水质监测站点
  fetchWaterStationList() {
    return new Promise<void>((resolve, reject) => {
      getStationList("").then((res) => {
        const data = res.data.data;
        this.waterStation = data;
        this.waterTrendList = data[0].monitorItems;
        this.typeCode = data[0].monitorItems[0].itemCode;
        this.typeUnit = data[0].monitorItems[0].concentrationUnit;
        this.stationId = data[0].stationId;
        Promise.all([
          this.fetchWaterTrend(),
          this.fetchWaterStationRecord(),
        ]).then(() => {
          resolve();
        });
      });
    });
  }
  private waterTrendList: any[] = [];
  //水质站点改变
  waterStationChange(e: any) {
    clearInterval(this.waterTimer);
    this.waterStation.find((item: any, index: number) => {
      if (item.stationId == e) {
        this.waterTrendList = item.monitorItems;
        this.typeCode = item.monitorItems[0].itemCode;
        console.log(this.typeCode);
        this.curWaterIndex = 0;
        this.stationId = e;
        this.fetchWaterTrend();
        this.fetchWaterStationRecord();
        this.openWaterTimer();
      }
    });
  }
  //水质监测记录
  fetchWaterStationRecord() {
    return new Promise<void>((resolve, reject) => {
      waterStationRecord(this.stationId).then((res) => {
        const data = res.data.data;
        this.waterData = data || [];
        console.log(this.waterData, "this.waterData");
        resolve();
      });
    });
  }
  //水质监测趋势
  fetchWaterTrend() {
    return new Promise<void>((resolve, reject) => {
      getMonitorItemDetailRecord(this.stationId, this.typeCode).then((res) => {
        const data = res.data.data ? res.data.data.hourMonitorItems : [];
        const obj: any = {
          name: "",
          bottomList: [],
          dataList: [],
          unit: this.typeUnit,
        };
        data.forEach((item: any) => {
          obj.bottomList.push(`${item.hour}时`);
          obj.dataList.push(Number(item.monitorValue).toFixed(2));
        });
        this.waterTrendAll = obj;
        resolve();
      });
    });
  }
  private typeCode = "";
  private typeUnit = "";
  //水质监测类型改变
  waterTypeChange(e: any) {
    clearInterval(this.waterTimer);
    this.curWaterIndex = e.target.value;
    // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
    // @ts-ignore
    this.typeCode = this.waterTrendList[this.curWaterIndex].itemCode;
    // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
    // @ts-ignore
    this.typeUnit = this.waterTrendList[this.curWaterIndex].concentrationUnit;
    this.fetchWaterTrend();
    this.openWaterTimer();
  }
  // 监测预警
  private fetchEarlyWarning() {
    return new Promise<void>((resolve, reject) => {
      earlyWarning().then((res) => {
        const data = res.data.data;
        data.forEach((item: any) => {
          item.alarmTime = item.alarmTime.slice(5, 16);
          let str = "";
          switch (item.keyWord) {
            case "NO2":
              str = "NO₂";
              break;
            case "SO2":
              str = "SO₂";
              break;
            case "O3":
              str = "O₃";
              break;
            case "PM10":
              str = "PM₁₀";
              break;
            case "PM2.5":
              str = "PM₂.₅";
              break;
          }
          item.alarmContent = item.alarmContent.replace(item.keyWord, str);
        });
        this.airWarning = data;
        resolve();
      });
    });
  }
  //车辆类型列表
  private fetchCarList() {
    return new Promise<void>((resolve, reject) => {
      getCarList().then((res) => {
        const data = res.data.data;

        this.carTypeList = data;
        resolve();
      });
    });
  }
  // 设备信息类型切换
  private stationTypeNameChange(val: any) {
    this.stationTypeName = val;
    if (val === "空气") {
      this.stationTypeIndnx = "0";
      this.airStationTypeName = "0";
      console.log(this.airStationTypeList[0].stationList[0]);
      this.equipmentDetail = this.airStationTypeList[0].stationList[0];
    } else {
      this.stationIdIndex = "0";
      this.equipmentDetail = this.waterStation[this.stationIdIndex];
    }
  }
  // 设备信息---获取空气站点列表
  private airStationTypeList: any[] = [];
  // 设备信息------空气站类型下标
  private stationTypeIndnx: any = "0";
  // 设备信息------空气站名称下标
  private airStationTypeName: any = "0";
  // 设备信息------水站名称下标
  private stationIdIndex: any = "0";

  // 设备信息---获取空气站点列表
  private getTypeStationInfoList() {
    typeStationInfoList().then((res: any) => {
      this.airStationTypeList = res.data.data;
    });
  }
  // 设备信息详情
  private equipmentDetail: any = {};
  // 设备信息---空气站点类型
  private stationTypeCodeChange(val: any) {
    this.stationTypeIndnx = val;
    this.airStationTypeName = "0";
    this.equipmentDetail = this.airStationTypeList[val].stationList[
      this.airStationTypeName
    ];
  }
  // 设备信息---空气站点名称
  private airStationTypeNameChange(val: any) {
    this.airStationTypeName = val;
    this.equipmentDetail = this.airStationTypeList[
      this.stationTypeIndnx
    ].stationList[val];
  }
  // 设备信息---水站名称
  private stationIdIndexChange(val: any) {
    this.stationIdIndex = val;
    this.equipmentDetail = this.waterStation[val];
  }
  // 设备状态
  private getEquipmentStatus() {
    getOnlineCount().then((res: any) => {
      const data = res.data.data;
      if (res.data.data) {
        this.stateList = [
          {
            name: `空气站 ${data.airCount.total}个`,
            number: data.airCount.onlineCount,
            total: data.airCount.total,
            startColor: "rgba(64, 119, 200, 1)",
            endColor: "rgba(33, 185, 241, 1)",
          },
          {
            name: `水站 ${data.waterCount.total}个`,
            number: data.waterCount.onlineCount,
            total: data.waterCount.total,
            startColor: "rgba(64, 119, 200, 1)",
            endColor: "rgba(33, 185, 241, 1)",
          },
          {
            name: `摄像头 ${data.cameraCount.total}个`,
            number: data.cameraCount.onlineCount,
            total: data.cameraCount.total,
            startColor: "rgba(64, 119, 200, 1)",
            endColor: "rgba(33, 185, 241, 1)",
          },
        ];
      }
    });
  }
  // 离线设备列表
  private fetchOffline() {
    getOffline().then((res: any) => {
      console.log(res.data.data);
      const list: any = [];
      for (const item of res.data.data) {
        list.push({
          name: item.deviceName,
          type: item.deviceType == 1 ? "空气站" : "水站",
          address: item.deviceAddress,
          time: "5月8日 13:20",
        });
      }
      this.offLineList = list;
    });
  }
  // 任务执行率
  getSelectMonthTaskCompletion() {
    selectMonthTaskCompletion().then((res) => {
      const bottomList = res.data.data.map((item: any) => item.yearMonth);
      const dataList = res.data.data.map((item: any) =>
        ((item.complete / item.total) * 100).toFixed(2)
      );
      const dataList1 = res.data.data.map((item: any) => item.complete);
      const dataList2 = res.data.data.map((item: any) => item.unComplete);
      const dataList3 = res.data.data.map((item: any) => item.total);
      this.executionRate = {
        bottomList,
        dataList,
        dataList1,
        dataList2,
        dataList3,
      };
      console.log("任务执行率", res);
    });
  }
  getSelectTaskTypeCompletionChange(value: string | number) {
    this.selectTaskTypeCompletionType = value;
    this.getSelectTaskTypeCompletion();
  }
  // 任务类型完成情况
  getSelectTaskTypeCompletion() {
    console.log("this.taskType", this.taskType);
    selectTaskTypeCompletion(this.selectTaskTypeCompletionType).then((res) => {
      console.log("任务完成情况", res);
      this.completeData = {
        bottomList: res.data.data.map((item: any) => item.typeName),
        dataList: res.data.data.map((item: any) => item.completed),
        dataList1: res.data.data.map((item: any) => item.uncompleted),
        dataList2: res.data.data.map((item: any) => item.closeCount || 0),
        // dataList2: data.taskCompletion.map((item) => item.rate.substr(0, item.rate.indexOf('%')))
      };
    });
  }
  getSelectTypeStatisticChange(value: string | number) {
    this.taskType = value;
    this.getSelectTypeStatistic();
  }
  // 任务分类统计
  getSelectTypeStatistic() {
    this.contentListVisiable = false;
    selectTypeStatistic(this.taskType).then((res) => {
      this.contentListVisiable = true;
      this.taskClassificationStatistics = res.data.data.map((item: any) => {
        let icon = "";
        if (item.name === "大气环境") {
          icon = this.dqhj;
        }
        if (item.name === "污染源") {
          icon = this.wry;
        }
        if (item.name === "水环境") {
          icon = this.shj;
        }
        if (item.name === "任务调度") {
          icon = this.rwdd;
        }
        return {
          icon,
          ...item,
        };
      });
    });
  }
  // 跳转数据统计
  toDataTable() {
    this.$router.push("/dataTable");
  }
}
</script>
