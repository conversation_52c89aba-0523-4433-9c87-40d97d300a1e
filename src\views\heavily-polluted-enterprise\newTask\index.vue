<style lang="less" scoped>
// @import '@/styles/a_waterExport.scss';
// ::v-deep .el-table th.is-leaf, ::v-deep .el-table td {
//      border-bottom: 0px solid #FFF;
// }
// .el-table::before{
//   height:0
// }
.main {
  padding: 20px;
}
.search-input {
  width: 316px;
  height: 38px;
  background: #ffffff;
  .el-input__inner {
    border: 1px solid #dee0f1;
    border-radius: 6px;
    ::-webkit-input-placeholder {
      color: #c0c4cc;
      font-size: 15px;
    }
  }
}
.filter-box {
  background: #ffffff;
  box-shadow: 0px 0px 18px 2px rgba(118, 168, 183, 0.2);
  border-radius: 8px;
  margin-bottom: 20px;
  box-sizing: border-box;
  padding: 20px;
  .filter-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .filter {
    display: flex;
    > *:not(:last-child) {
      margin-right: 20px;
    }
  }
}
.key-words {
  width: 250px;
}
.card-header {
  display: flex;
  justify-content: space-between;
  .name {
    font-size: 16px;
    font-weight: bold;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    .name_adress {
      img {
        width: 11px;
        height: 14px;
        vertical-align: -2px;
      }
      font-size: 14px;
      color: #808080;
      margin-top: 7px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  .status {
    font-size: 14px;
    display: flex;
    align-items: center;
    .status_cercel {
      width: 6px;
      height: 6px;
      border-radius: 50%;
      margin-right: 6px;
    }
  }
}
.cameras {
  // display: flex;
  // flex-wrap: wrap;
  // height: 778px;
  background: #ffffff;
  box-shadow: 0px 0px 18px 2px rgba(118, 168, 183, 0.2);
  border-radius: 8px;
  padding: 33px 36px;
  // padding-right: 0;
  box-sizing: border-box;
  ::v-deep.el-card {
    border: none !important;
  }
  ::v-deep.el-card__header {
    border: none !important;
    padding-bottom: 0 !important;
  }
  .tasktitle {
    font-size: 14px;
    color: rgb(19, 206, 102);
    text-decoration: underline;
    cursor: pointer;
    // text-underline: rgb(19, 206, 102);
  }
}
.camera {
  width: 366px;
  height: 336px;
  margin: 0 30px 30px 0;
  background: #f6f7f7 !important;
  border-radius: 10px;
}
.false-camera {
  width: 100%;
  height: 230px !important;
  background-repeat: no-repeat;
  display: flex;
  justify-content: center;
  align-items: center;
  .play {
    display: flex;
    justify-content: center;
    align-content: center;
    > span {
      display: block;
      line-height: 35px;
    }
    .caret-right {
      width: 70px;
      height: 70px;
      position: relative;
      top: 3px;
      background-image: url("~@/assets/images/<EMAIL>");
      background-repeat: no-repeat;
      background-size: 100% 100%;
    }
    .el-icon-caret-right:hover {
      color: #999;
    }
  }
  .play:hover {
    border-color: #999;
  }
}
.el-pagination {
  // text-align: left;
  margin-top: 20px;
}
::v-deep .video-wrapper {
  height: 230px;
}
.box {
  display: inline-block;
  width: 72px;
  height: 26px;
  border: 1px solid #000;
  border-radius: 4px;
  font-size: 14px;
  font-family: PingFang SC;
  font-weight: 400;
  line-height: 24px;
  align-items: center;
  // color: #F39611;
}
//  <!-- 完成状态(0 进行中, 1 已完成, 2 已关闭, 3 已逾期, 4 逾期完成) -->
.box0 {
  border: 1px solid #f39611;
  color: #f39611;
}
.box1 {
  border: 1px solid rgb(19, 206, 102);
  color: rgb(19, 206, 102);
}
.box2 {
  border: 1px solid #f13e3e;
  color: #f13e3e;
}
// .box3{
//   border: 1px solid #F39611;
//   color: #F39611;
// }
// 任务逾期状态色(逾期 1,未逾期 0)
.yuqi1 {
  color: #f13e3e;
}
.yuqi0 {
  color: #f1f1f1;
}
.footers {
  display: flex;
  padding: 20px 50px;
  justify-content: flex-end;
}
</style>
<style lang="less">
.task_dialog.el-dialog {
  color: #fff;
  width: 729px;
  height: 736px;
  background: url("~@/assets/images/<EMAIL>") no-repeat;
  background-size: contain;
  position: relative;
  .title {
    position: absolute;
    left: 50px;
    top: 28px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    font-size: 16px;
    color: #dcf0ff;
  }
  .el-input__inner,
  .el-textarea__inner,
  .el-input__count-inner,
  .el-input__count,
  .el-button,
  .el-upload-dragger {
    background: transparent !important;
  }
  & *::-webkit-scrollbar {
    display: none;
  }
  .el-form-item__label,
  .el-upload__tip {
    color: #fff;
  }
  .el-dialog__headerbtn {
    z-index: 100;
  }
}
.address_dialog {
  .el-dialog__body {
    box-sizing: border-box;
    padding: 60px;
    padding-bottom: 0;
  }
  .el-dialog__footer {
    box-sizing: border-box;
    padding-right: 65px;
  }
}
.node_dialog {
  width: 566px !important;
  height: 394.5px !important;
  // background: #fff !important;
  .el-dialog__headerbtn {
    display: none;
  }
  .el-form-item__label {
    color: #ffffff;
  }
  .el-dialog__header {
    display: none;
  }
  .el-dialog__body {
    padding: 80px 40px 20px 8px;
  }
  .footer {
    margin-top: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
    .el-input__inner,
  .el-textarea__inner,
  .el-input__count-inner,
  .el-input__count,
  .el-button,
  .el-upload-dragger {
    background: transparent !important;
  }
  height: auto;
}
.preview_dialog {
  .el-dialog__headerbtn {
    display: none;
  }
  .el-dialog__body {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
  }
}
</style>
<template>
  <div class="main">
    <!-- <el-button @click="createNewTaskOpen()">任务调度</el-button> -->
    <!-- 新建任务详情 -->
    <el-dialog
      title=""
      :visible.sync="createNewTaskVisible"
      :close-on-click-modal="false"
      :modal="false"
      width="750px"
      custom-class="task_dialog"
      @close="handleBack('addTask')"
    >
      <div class="title">任务调度</div>
      <div style="height: 580px;overflow: auto">
        <el-form
          ref="addTask"
          :model="addTask"
          :rules="taskRules"
          label-width="120px"
          label-position="right"
        >
          <el-form-item label="任务标题：" prop="title">
            <el-row>
              <el-col :xs="24" :lg="20">
                <el-input
                  v-model="addTask.title"
                  maxlength="24"
                  show-word-limit
                />
              </el-col>
            </el-row>
          </el-form-item>
          <el-form-item label="任务内容：" prop="content">
            <el-row>
              <el-col :xs="24" :lg="20">
                <el-input
                  v-model="addTask.content"
                  type="textarea"
                  :rows="4"
                  maxlength="256"
                  show-word-limit
                />
              </el-col>
            </el-row>
          </el-form-item>
          <el-form-item label="结束时间：" prop="endTime">
            <el-row>
              <el-col :xs="24" :lg="20">
                <el-date-picker
                  v-model="addTask.endTime"
                  :picker-options="pickerCreateTimeOptions"
                  type="datetime"
                  format="yyyy-MM-dd HH:mm"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  placeholder="选择结束时间"
                  style="width: 100%"
                  default-time="23:00:00"
                />
              </el-col>
            </el-row>
          </el-form-item>
          <el-form-item label="任务位置：" required :error="positionError">
            <el-row>
              <el-col :xs="24" :lg="20">
                <el-button
                  size="medium"
                  type
                  style="width: 100%; color: #1890ff;"
                  @click="handleAddMap"
                  >{{
                    addTask.address ? addTask.address : "添加位置"
                  }}</el-button
                >
              </el-col>
            </el-row>
          </el-form-item>
          <el-form-item label="上传附件：">
            <el-row>
              <el-col :span="20">
                <el-upload
                  ref="upload"
                  class="new-task-upload"
                  :auto-upload="true"
                  :http-request="noop"
                  :file-list="fileList"
                  :on-preview="handleFilePreview"
                  :before-remove="fileBeforeRemove"
                  :on-remove="handleFileRemove"
                  drag
                  multiple
                  action="customize"
                >
                  <div class="el-upload__text">
                    <em>上传附件</em>
                  </div>
                  <div
                    slot="tip"
                    class="el-upload__tip"
                    v-if="isPasteSupported"
                    @paste="handlePaste"
                  >
                    只能上传image/docx/doc/xlsx/xls/ppt/pdf/zip文件
                  </div>
                </el-upload>
              </el-col>
            </el-row>
          </el-form-item>
          <el-form-item label="新建流程：">
            <flowCharts
              ref="flowCharts1"
              flow-chart-id="flowCharts1"
              :model-data="modelData"
              @add="handleAddNode"
              @delete="handleDeleteNode"
              @detail="handleCurrentChange"
            />
          </el-form-item>
        </el-form>
      </div>
      <div class="footers">
        <el-button
          v-loading="createNewTaskLoading"
          type="primary"
          :loading="addLoading"
          @click="handleAddTask()"
          >确定</el-button
        >
        <el-button
          style="margin-left: 10px"
          type="plain"
          @click="handleBack('addTask')"
          >取消</el-button
        >
      </div>
    </el-dialog>
    <!-- 附件图片预览 -->
    <el-dialog
      :visible.sync="imagePreviewVisible"
      class="preview_dialog"
      top="10vh"
      custom-class="img-height"
      :modal="true"
      :modal-append-to-body="false"
    >
      <img
        :src="imagePreviewUrl"
        style="max-height:100%;max-width:100%;margin: auto;display: block;"
        alt
      />
    </el-dialog>
    <el-dialog
    top="20vh"
      :visible.sync="dialogDrawer"
      custom-class="node_dialog"
      :modal-append-to-body="false"
      :title="number === 1 ? '添加执行者' : '添加抄送者'"
    >
      <el-form
        ref="addNode"
        :model="addNode"
        :rules="nodeRules"
        class="drawer-content"
        label-width="100px"
        label-position="right"
      >
        <el-form-item v-if="number === 1" label="执行者:" prop="obj">
          <el-cascader
            v-model="addNode.obj"
            collapse-tags
            :options="displayDepartmentList"
            :show-all-levels="true"
            :props="miltPerson"
            style="width: 100%;"
            placeholder="执行者"
          />
        </el-form-item>
        <el-form-item v-else label="抄送者:" prop="obj">
          <el-cascader
            v-model="addNode.obj"
            collapse-tags
            :options="displayDepartmentList"
            :show-all-levels="true"
            :props="miltPerson"
            style="width: 100%;"
            placeholder="抄送者"
          />
        </el-form-item>
        <el-form-item
          v-if="number === 1"
          label="任务描述:"
          prop="taskNodeContent"
        >
          <el-input
            v-model="addNode.taskNodeContent"
            size="medium"
            style="width: 100%"
            type="textarea"
            rows="5"
            maxlength="500"
          />
        </el-form-item>
        <div class="footer">
          <el-button class="search-button" @click="handleaddNodeSure"
            >确定</el-button
          >
          <el-button style="margin-left: 50px" @click="handleClose"
            >关闭</el-button
          >
        </div>
      </el-form>
    </el-dialog>
    <!-- 地图弹框 -->
    <el-dialog
      title="选择位置"
      :close-on-click-modal="false"
      :modal-append-to-body="false"
      :visible.sync="mapVisible"
      :modal="false"
      width="1000px"
      custom-class="address_dialog"
    >
      <address-map
        v-if="mapVisible"
        :address="addTask.address"
        :detail-show="detailShow"
        height="600px"
        @address="handleAddress"
        @submit="handleSubmit"
      />
      <div slot="footer">
        <el-button size="medium" plain @click="mapVisible = false"
          >取消</el-button
        >
        <el-button size="medium" type="primary" @click="handleAdMap"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
// import LivePlayer from '@liveqing/liveplayer'
import { mapGetters } from "vuex";
import dayjs from "dayjs";
import {
  getTaskPage,
  addTask,
  exportExcel,
  getTopTaskExecutorList,
  getPersonList,
  getDepartment,
} from "@/api/taskManagementNew";
import uploadMixin from "@/mixins/upload";
import addressMap from "./modules/addressMap";
import flowCharts from "./modules/index";
import taskMain from "./modules/taskOverviewDetailNew";
import { uploadFile } from "@/api/common";

const FILE_TYPE_DIC = {
  png: 0,
  jpg: 0,
  jpeg: 0,
  bmp: 0,
  gif: 0,
  docx: 1,
  doc: 1,
  xlsx: 2,
  xls: 2,
  pdf: 3,
  pptx: 4,
  ppt: 4,
  rar: 5,
  zip: 5,
};

export default {
  components: {
    flowCharts,
    addressMap,
    taskMain,
  },
  filters: {
    ellipsis(value) {
      if (!value) return "";
      if (value.length > 18) {
        return `${value.slice(0, 18)}...`;
      }
      return value;
    },
  },
  mixins: [uploadMixin],
  data() {
    const that = this;
    const timeOptionValidator = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请选择结束时间"));
      } else if (new Date(value).getTime() < new Date().getTime()) {
        callback(new Error("结束时间应大于当前时间"));
      }

      callback();
    };
    const objValidator = (rule, value, callback) => {
      if (JSON.stringify(value) === "{}") {
        callback(new Error("请选择执行者"));
        return;
      }
      const has = that.hasNode(that.addNode.obj.userId);
      if (has) {
        callback(new Error(has));
        return;
      }
      callback();
    };
    return {
      isPasteSupported: false,
      positionError: "",
      loading: false,
      riverList: [],
      stationList: [],
      cameraList: [],
      departmentList: [],
      departmentIdSearch: undefined,
      riverId: undefined,
      stationId: undefined,
      keywords: undefined,
      tableData: [],
      pageNum: 1,
      pageSize: 10,
      total: 0,
      dateYear: [],
      overdueStatus: "",
      completeStatus: "",
      stateOptions: [
        {
          label: "未完成",
          value: 0,
        },
        {
          label: "已完成",
          value: 1,
        },
        /* {
          label: '已退单',
          value: 2
        } */
      ],
      overList: [
        {
          label: "未逾期",
          value: 0,
        },
        {
          label: "已逾期",
          value: 1,
        },
      ],
      createNewTaskVisible: false,
      createNewTaskLoading: false,
      addTask: {
        title: undefined,
        content: undefined,
        taskType: undefined,
        generationMode: "0",
        departmentId: undefined,
        userId: undefined,
        endTime: undefined,
        taskNodeList: [],
        taskNodeLinkList: [],
        taskAnnexList: [],
        lng: "",
        lat: "",
        address: "",
      },
      taskRules: {
        title: [
          { required: true, message: "请输入任务标题", trigger: "blur" },
          {
            min: 3,
            max: 24,
            message: "长度在 3 到 24 个字符",
            trigger: "blur",
          },
        ],
        content: [
          { required: true, message: "请填写任务内容", trigger: "blur" },
          {
            min: 3,
            max: 256,
            message: "长度在 3 到 256 个字符",
            trigger: "blur",
          },
        ],
        endTime: [
          { validator: timeOptionValidator, required: true, trigger: "blur" },
        ],
      },
      fileList: [],
      pickerCreateTimeOptions: {
        disabledDate: (time) =>
          new Date(time).getTime() < new Date().getTime() - 8.64e7,
      },
      imagePreviewVisible: false,
      imagePreviewUrl: undefined,
      modelData: {
        nodeDataArray: [],
        linkDataArray: [],
      },
      addLoading: false,
      detailShow: true,
      mapVisible: false,
      dialogDrawer: false,
      number: 1,
      addNode: {
        taskNodeContent: undefined,
        obj: {},
      },
      nodeRules: {
        obj: [{ validator: objValidator, required: true, trigger: "change" }],
      },
      displayDepartmentList: [],
      miltPerson: {
        emitPath: false,
        multiple: false,
        expandTrigger: "hover",
        value: "needsData",
        children: "departmentAccountList",
        label: "departmentName",
        accountId: "",
        disabled: "disabled",
        depId: "",
      },
      isAdd: true,
      value: [],
      options: [],
      icon0: require("@/assets/images/toux.png"),
      icon1: require("@/assets/images/toux_2.png"),
      actorList: [],
      typeName: "任务列表",
      taskId: "",
      withStreet: 1,
      miltPersonDe: {
        emitPath: false,
        multiple: false,
        checkStrictly: true,
        expandTrigger: "hover",
        value: "departmentId",
        children: "list",
        label: "departmentName",
        accountId: "",
        disabled: "disabled",
        depId: "",
      },
      currentNode: undefined,
      selectDepartmentId: 0,
      departmentId: 0,
      userName: "",
      userId: "",
    };
  },
  computed: {
    nodeTree() {
      const { nodeDataArray, linkDataArray } = this.modelData;
      const tree = {};
      linkDataArray.forEach((item, index) => {
        const node = nodeDataArray[index + 1];
        if (tree[item.from]) {
          tree[item.from][node.userId] = node;
        } else {
          tree[item.from] = { [node.userId]: node };
        }
      });
      return tree;
    },
  },
  watch: {
    riverId(newVal) {},
  },
  created() {
    this.initData();
  },
  mounted() {
    // 检查浏览器是否支持粘贴事件
    this.isPasteSupported = "onpaste" in document;
    this.selectDepartmentId = this.departmentId || 0;
    this.getDepartment();
    this.getPersonList();
    document.addEventListener("paste", (e) => {
      this.handlePaste(e);
    });
  },
  methods: {
    handlePaste(event) {
      // 获取粘贴板中的数据
      const items = (event.clipboardData || event.originalEvent.clipboardData)
        .items;
      for (const item of items) {
        if (item.kind === "file" && item.type.includes("image")) {
          const file = item.getAsFile();
          if (file) {
            // this.$refs.upload.file = file;
            this.fileList = [file];
            // this.$refs.upload.submit();
            const formData = new FormData();
            formData.append("file", file, file.name);
            uploadFile(formData).then((res) => {
              const { code, data } = res.data;
              if (code === 200) {
                this.addTask.taskAnnexList.push({
                  data: data.url,
                  name: data.name,
                  format: data.type,
                });
                this.fileList = [
                  {
                    url: data.url,
                    name: data.name,
                    format: data.type,
                  },
                ];
                // uploadData.onSuccess()
              } else {
                this.fileList = [];
                throw new Error("上传失败，服务器处理失败");
              }
            });
          }
        }
      }
    },
    handleBeforeUpload(file) {
      // 可以在这里设置上传前的逻辑，例如限制文件类型或大小
      // 直接调用上传方法
      this.$refs.upload.uploadFiles([file]);
    },
    initData() {
      const UserInfor = JSON.parse(localStorage.getItem("UserInfor") || "{}");
      this.departmentId = UserInfor.account?.departmentId;
      this.userName = UserInfor.user?.userName;
      this.userId = UserInfor.userId;
    },
    /** 获取列表筛选部门 */
    getDepartment() {
      getDepartment().then((res) => {
        this.departmentList = res.data.data[0].list;
      });
    },

    createNewTaskOpen({address, lat, lng}) {
      this.addTask.address = address
      this.addTask.lat = lat
      this.addTask.lng = lng
      const node = {
        key: this.uuid().replace(/-/g, ""),
        taskNodeType: 2,
        taskNodeName: this.userName,
        departmentId: this.selectDepartmentId,
        userId: this.userId,
        completionCondition: 2,
        avatar: this.icon0,
      };
      this.modelData = {
        nodeDataArray: [node],
        linkDataArray: [],
      };
      this.createNewTaskVisible = true;
      // this.$emit('createNewTaskOpen')
    },
    handleFilePreview(file) {
      if (file.raw) {
        const rawFile = file.raw;
        const filetype = rawFile.name
          .split(".")
          .pop()
          .toLocaleLowerCase();
        if (FILE_TYPE_DIC[filetype] !== 0) return;
        const reader = new FileReader();
        // eslint-disable-next-line no-shadow
        const that = this;
        reader.readAsDataURL(rawFile);
        reader.onload = (e) => {
          that.imagePreviewUrl = e.target.result;
        };
      } else {
        const filetype = file.url
          .split(".")
          .pop()
          .toLocaleLowerCase();
        if (FILE_TYPE_DIC[filetype] !== 0) return;
        this.imagePreviewUrl = file.url;
      }
      this.imagePreviewVisible = true;
    },
    fileBeforeRemove(file, fileList) {
      return this.$confirm(`确定移除 ${file.name}？`);
    },
    handleFileRemove(file, fileList) {
      const index = this.addTask.taskAnnexList.findIndex(
        (item) => item.name === file.name
      );
      this.addTask.taskAnnexList.splice(index, 1);
    },
    /**
     * 覆盖原始的文件上传事件
     */
    noop(uploadData) {
      this.myUploadHandle({
        uploadData,
        checkFn: (file, filename, filetype) => {
          if (file.size / 1024 / 1024 > 200) {
            // 文件大于20MB
            this.$message.error("上传文件不能大于200M");
            uploadData.onError();
            return false;
          }

          if (FILE_TYPE_DIC[filetype] === undefined) {
            this.$message.error(
              "只能上传image/docx/doc/xlsx/xls/ppt/pdf/zip文件"
            );
            uploadData.onError();
            return false;
          }

          return true;
        },
        success: (successData) => {
          this.addTask.taskAnnexList.push(successData);
        },
      });
    },
    handleAddMap() {
      this.mapVisible = true;
    },
    /**
     * @method handleSubmit
     * @description 地图坐标返回
     */
    handleSubmit(args) {
      this.addTask.lng = args[0].position.lng;
      this.addTask.lat = args[0].position.lat;
      if (this.addTask.lng && this.addTask.lat) {
        this.positionError = "";
      }
    },
    /**
     * @method handleSubmit
     * @description 地图地址返回
     */
    handleAddress(args) {
      this.addTask.address = args;
      if (this.addTask.lng && this.addTask.lat) {
        this.positionError = "";
      }
    },
    handleAdMap() {
      this.mapVisible = false;
    },
    /**
     * 节点重复检查
     */
    hasNode(userId) {
      if (userId === this.currentNode.userId) {
        return "不能将任务指派给自己";
      }
      const currTree = this.nodeTree[this.currentNode.key];

      if (!currTree) return false;

      if (currTree[userId]) {
        return "当前节点下已存在此成员";
      }

      return false;
    },
    handleaddNodeSure() {
      this.$refs.addNode.validate((valid) => {
        if (valid) {
          const data = JSON.parse(JSON.stringify(this.addNode));
          data.userId = data.obj.userId;
          data.departmentId = data.obj?.departmentId;
          data.taskNodeType = 2;
          data.taskNodeName = data.obj?.userName;
          data.permission = this.number === 1 ? 2 : 1;
          data.completionCondition = 2;
          data.avatar = this.number === 1 ? this.icon0 : this.icon1;
          delete data.obj;
          data.key = this.uuid().replace(/-/g, "");
          this.$refs.flowCharts1.addNode(data);
          this.handleClose();
        }
      });
    },
    // 新增子节点
    handleAddNode(args) {
      this.number = args;
      this.dialogDrawer = true;
      if (this.$refs.addNode) {
        this.$refs.addNode.resetFields();
      }
      this.addNode = {
        taskNodeContent: undefined,
        obj: {},
      };
      this.isAdd = true;
    },
    // 删除节点
    handleDeleteNode(args) {
      this.$confirm("此操作将删除当前节点及存在的子节点，是否删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        center: true,
      })
        .then(() => {
          const { data } = args;
          const curr = this.modelData.linkDataArray.filter(
            (item) => item.to === data.key
          )[0];
          const keys = this.recursion(curr, []);
          const nodeDataArray = this.modelData.nodeDataArray.filter(
            (item) => keys.indexOf(item.key) === -1
          );
          const linkDataArray = this.modelData.linkDataArray.filter(
            (item) => keys.indexOf(item.to) === -1
          );
          this.modelData = {
            nodeDataArray,
            linkDataArray,
          };
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    /**
     * 当前节点发生变化
     */
    handleCurrentChange(currentNode) {
      this.currentNode = currentNode.data;
    },
    recursion(data, arr) {
      arr.push(data.to);
      const arr1 = [];
      this.modelData.linkDataArray.forEach((item) => {
        if (item.from === data.to) {
          arr1.push(item);
        }
      });
      if (arr1.length) {
        arr1.forEach((item) => {
          this.recursion(item, arr);
        });
      }
      return arr;
    },
    // 生成uuid
    uuid() {
      const s = [];
      const hexDigits = "0123456789abcdef";
      for (let i = 0; i < 36; i++) {
        s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1);
      }
      s[14] = "4"; // bits 12-15 of the time_hi_and_version field to 0010
      // eslint-disable-next-line no-bitwise
      s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1);
      // bits 6-7 of the clock_seq_hi_and_reserved to 01
      // eslint-disable-next-line no-multi-assign
      s[8] = s[13] = s[18] = s[23] = "-";

      const uuid = s.join("");
      return uuid;
    },
    // 新增任务
    handleAddTask() {
      if (this.isUploading()) return;
      // 判断是否有经纬度
      if (!this.addTask.lat || !this.addTask.lng) {
        this.positionError = "请选择任务位置";
      } else {
        this.positionError = "";
      }
      this.$refs.addTask.validate((valid) => {
        if (valid) {
          // 去除重复上传的图片
          const data1 = JSON.parse(JSON.stringify(this.modelData));
          const data2 = JSON.parse(JSON.stringify(this.addTask));
          const map = new Map();
          const swapList = data2.taskAnnexList.splice(0);
          const sendFileList = swapList.filter(
            (item) => !map.has(item.name) && map.set(item.name, true)
          );
          const params = {
            title: data2.title,
            content: data2.content,
            taskType: 2,
            departmentId: this.selectDepartmentId,
            userId: this.userId,
            startTime: this.getCurrentTime(),
            endTime: data2.endTime,
            lat: data2.lat,
            lng: data2.lng,
            address: data2.address,
            taskNodeList: data1.nodeDataArray.map((item, index) => {
              item.orderId = index + 1;
              item.taskNodeId = item.key;
              // eslint-disable-next-line no-underscore-dangle
              delete item.__gohashid;
              delete item.key;
              return item;
            }),
            taskNodeLinkList: data1.linkDataArray.map((item) => {
              // eslint-disable-next-line no-underscore-dangle
              delete item.__gohashid;
              return item;
            }),
            taskAnnexList: sendFileList.map((item) => {
              item.base64 = item.data;
              delete item.data;
              return item;
            }),
          };
          if (
            this.addTask.generationMode !== "1" &&
            params.taskNodeLinkList.length === 0
          ) {
            this.$message.warning("请新建流程图");
            return;
          }
          this.addLoading = true;
          addTask(params)
            .then((res) => {
              if (res.data.code === 200) {
                this.$message.success("新建任务成功");
                this.handleBack();
                this.getList();
                this.$bus.emit("fetchtaskList");
              } else {
                this.$message.warning(res.data.msg);
              }
              // this.$bus.emit('fetchtaskList')
            })
            .catch((err) => {
              throw new Error(err);
            })
            .finally(() => {
              this.addLoading = false;
              this.createNewTaskLoading = false;
            });
        }
      });
    },
    handleClose() {
      this.$refs.addNode.resetFields();
      this.addNode = {
        taskNodeContent: undefined,
        obj: {},
      };
      this.dialogDrawer = false;
    },
    // 获取执行者列表
    getPersonList() {
      getTopTaskExecutorList().then((res) => {
        const { data } = res.data;
        const data1 = {
          departmentList: data,
          userList: [],
        };
        this.displayDepartmentList = this.getDepartListAll(
          JSON.parse(JSON.stringify(data1))
        ).departmentAccountList;
      });
      // if (this.departmentType === 1) {
      //   getTopTaskExecutorList().then((res) => {
      //     const { data } = res.data
      //     const data1 = {
      //       departmentList: data,
      //       userList: []
      //     }
      //     this.displayDepartmentList = this.getDepartListAll(
      //       JSON.parse(JSON.stringify(data1))
      //     ).departmentAccountList
      //   })
      // } else {
      //   getPersonList(this.selectDepartmentId, this.withStreet).then((res) => {
      //     this.displayDepartmentList = this.getDepartListAll(
      //       JSON.parse(JSON.stringify(res.data.data))
      //     ).departmentAccountList
      //   })
      // }
    },
    // 获取递归后的部门数组
    getDepartListAll(data) {
      const departmentAccountList = data.departmentList || [];
      const userAccountList =
        data.userList === null
          ? []
          : data.userList.map((item) => {
              if (item.isAdmin) {
                item.userName += "(管理员)";
              }
              return item;
            });
      for (const item of departmentAccountList) {
        item.needsData = { departmentId: item.departmentId };
      }
      for (const item of userAccountList) {
        item.departmentName = item.userName;
        item.needsData = {
          userId: item.userId,
          departmentId: item?.departmentId,
          userName: item.userName,
        };
        item.disabled = item.userId === this.userId;
      }
      data.departmentAccountList = departmentAccountList.concat(
        userAccountList
      );
      for (let i = 0; i < departmentAccountList.length; i++) {
        this.getDepartListAll(departmentAccountList[i]);
      }
      return data;
    },
    getCurrentTime() {
      let date = new Date();
      const Y = date.getFullYear();
      const M =
        date.getMonth() + 1 < 10
          ? `0${date.getMonth() + 1}`
          : date.getMonth() + 1;
      const D = date.getDate() < 10 ? `0${date.getDate()}` : date.getDate();
      const hours =
        date.getHours() < 10 ? `0${date.getHours()}` : date.getHours();
      const minutes =
        date.getMinutes() < 10 ? `0${date.getMinutes()}` : date.getMinutes();
      const seconds =
        date.getSeconds() < 10 ? `0${date.getSeconds()}` : date.getSeconds();
      date = `${Y}-${M}-${D} ${hours}:${minutes}:${seconds}`;
      return date;
    },
    /** 导出Excel */
    exportExcel() {
      let startDate;
      let endDate;
      if (this.dateYear) {
        // eslint-disable-next-line prefer-destructuring
        startDate = this.dateYear[0];
        // eslint-disable-next-line prefer-destructuring
        endDate = this.dateYear[1];
      }
      const data = {
        taskType: 2,
        userId: this.userId,
        departmentId: this.departmentIdSearch,
        completeStatus: this.completeStatus,
        overdueStatus: this.overdueStatus,
        keywords: this.keywords,
        startDate,
        endDate,
        myTaskType: 1,
      };
      exportExcel(data).then((res) => {
        const url = window.URL.createObjectURL(res.data);
        const link = document.createElement("a");
        link.href = url;
        link.download = "我的任务.xlsx";
        link.click();
      });
    },
    goDetail(row) {
      this.taskId = row.taskId;
      this.typeName = "任务详情";
    },
    returnToTable() {
      this.typeName = "任务列表";
      this.getList();
    },
    handleBack() {
      this.addTask = {
        title: undefined,
        content: undefined,
        taskType: undefined,
        generationMode: "0",
        departmentId: undefined,
        userId: undefined,
        eventId: undefined,
        endTime: undefined,
        taskNodeList: [],
        taskNodeLinkList: [],
        taskAnnexList: [],
        planId: undefined,
      };
      this.fileList = [];
      this.createNewTaskVisible = false;
      // const path = this.$route.path
      // this.$router.push(path)
      // this.$emit('returnToTable')
    },
  },
  beforeDestroy() {
    document.removeEventListener('paste', null)
  }
};
</script>
