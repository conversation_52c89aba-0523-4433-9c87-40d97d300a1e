<style lang="less" scoped>
p {
  margin-bottom: 0;
}

.monitor {
  display: flex;
  color: #ffffff;
  padding: 0.12rem;
  align-items: center;
  .spot {
    width: 0.2rem;
    height: 0.2rem;
    background-image: url("../../assets/rwcztjc-jb.png");
    background-size: 100% 100%;
    margin-right: 0.12rem;
  }
  .line {
    width: 0.02rem;
    height: 0.24rem;
    background-image: url("../../assets/<EMAIL>");
    background-size: 100% 100%;
    margin: 0 0.2rem;
  }
  .position {
  }
}
.monitor:nth-child(odd) {
  background: #052f61;
}
.monitor:nth-child(even) {
  background: transparent;
}
.task {
  box-sizing: border-box;
  .task-top {
    padding: 0.15rem 0.25rem;
    box-sizing: border-box;
    background-image: url("../../assets/task2.png");
    background-size: 100% 100%;
    > div:nth-child(2) {
      display: flex;
      > :nth-of-type(1) {
        img {
          object-fit: cover;
          width: 100%;
          height: 100%;
        }
      }
      > :nth-of-type(2) {
        display: flex;
        flex-direction: column;
        padding-left: 0.1rem;
        justify-content: space-between;
        flex: 1;
        p {
          margin-bottom: 0;
        }
      }
      .task-top-name {
        color: rgba(0, 252, 249, 1);
        font-size: 0.16rem;
        // margin-left: 0.15rem;
      }
      .task-top-text {
        font-size: 0.16rem;
        color: #ffffff;
      }
      .task-top-text-big {
        display: inline-block;
        font-size: 0.16rem;
        background: rgba(18, 75, 156, 1);
        border-radius: 0.04rem;
        padding: 0.04rem;
      }
    }
  }
  .task-bottom {
    box-sizing: border-box;
    background-image: url("../../assets/task1.png");
    background-size: 100% 180%;
    padding: 0.15rem 0.25rem;
    .task-bottom-top {
      border-bottom: 1px solid rgba(255, 255, 255, 0.35);
      padding-bottom: 0.05rem;
      > :nth-of-type(1) {
        width: 0.4rem;
        height: 0.4rem;
        border-radius: 50%;
        margin-right: 0.1rem;
        img {
          width: 100%;
          height: 100%;
          border-radius: 50%;
        }
      }
      display: flex;
      align-items: center;
      .task-bottom-top-name {
        color: rgba(0, 234, 255, 1);
        font-size: 0.16rem;
      }
      .task-bottom-top-time {
        color: rgba(191, 208, 210, 1);
        font-size: 0.13rem;
      }
    }
    .task-bottom-bottom {
      font-size: 0.16rem;
      color: #ffffff;
      padding-top: 0.05rem;
    }
  }
  .lines {
    width: 2.66rem;
    height: 0.02rem;
    background: url(../../assets/<EMAIL>) no-repeat;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    margin: 0.15rem auto;
  }
  // .task-charts {
  //   padding: 0.15rem;
  //   box-sizing: border-box;
  //   background-image: url("../../assets/task3.png");
  //   background-size: 100% 100%;
  //   display: flex;
  //   justify-content: space-between;
  // }
  .playback-form {
    color: #fff;
    transition: all 1s linear;
    opacity: 0;
    pointer-events: none;
    > div:nth-child(odd) {
      margin-bottom: 0.03rem;
    }
    > div:nth-child(even) {
      margin-bottom: 0.15rem;
    }
    .playback-button {
      display: inline-block;
      margin-right: 0.1rem;
    }
    .date-picker {
      margin-right: 0.2rem;
    }
    .time-picker {
      width: 1.28rem;
    }
  }
  .show-playback {
    opacity: 1;
    pointer-events: auto;
  }
}
.f-color {
  color: rgba(255, 255, 255, 1) !important;
  font-size: 0.18rem !important;
  text-shadow: none;
}
.type-center {
  position: relative;
  width: 9.5rem;
  .type-center-top {
    margin-top: 0.57rem;
    display: flex;
    justify-content: space-between;
    .car-normal {
      p {
        margin-bottom: 0;
      }
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      width: 1.6rem;
      height: 1rem;
      background: url(../../assets/<EMAIL>);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      > :nth-of-type(1) {
        font-size: 0.19rem;
        color: rgba(255, 255, 255, 1);
      }
      > :nth-of-type(2) {
        width: 0.34rem;
        height: 0.3rem;
        margin-top: 0.11rem;
        img {
          width: 100%;
          height: 100%;
        }
      }
    }
    .car-active {
      p {
        margin-bottom: 0;
      }
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      width: 1.6rem;
      height: 1rem;
      background: url(../../assets/<EMAIL>);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      > :nth-of-type(1) {
        font-size: 0.19rem;
        color: rgba(255, 255, 255, 1);
      }
      > :nth-of-type(2) {
        width: 0.34rem;
        height: 0.3rem;
        margin-top: 0.11rem;
        img {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
  .center-map {
    position: absolute;
    width: 10.5rem;
    left: -0.5rem;
    height: calc(1080px - 2.6rem);
    margin-top: 0.15rem;
    > div {
      width: 100%;
      height: 100%;
    }
  }
}
.main-content {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  > div {
    width: 100%;
    height: 0.31rem;
  }
  .legend {
    display: flex;
    justify-content: flex-end;
    .on {
      display: flex;
      align-items: center;
      .block {
        width: 0.08rem;
        height: 0.08rem;
        background: #56ccff;
        margin: 0 0.1rem 0 0.3rem;
      }
    }
    .off {
      display: flex;
      align-items: center;
      .block {
        width: 0.08rem;
        height: 0.08rem;
        background: #719eef;
        margin: 0 0.1rem 0 0.3rem;
      }
    }
    .error {
      display: flex;
      align-items: center;
      .block {
        width: 0.08rem;
        height: 0.08rem;
        background: #f27678;
        margin: 0 0.1rem 0 0.3rem;
      }
    }
  }
  .line-one {
    display: flex;
    justify-content: space-between;
    font-size: 0.14rem;
    font-family: PingFang SC;
    font-weight: 500;
    color: rgba(255, 255, 255, 1);
    > :nth-of-type(2) {
      color: rgba(0, 222, 255, 1);
    }
  }
  .line-two {
    height: 0.06rem;
    background-color: #f27678;
    // border-radius: 0.12rem;
    display: flex;
    align-items: center;
    > div {
      height: 0.06rem;
      // border-radius: 0.12rem;
      // border-top-right-radius: 0;
      // border-bottom-right-radius: 0;
    }
  }
}
.common-content-title {
  display: flex;
  align-items: center;
  justify-content: center;
  // width: 76px;
  height: 0.3rem;
  font-size: 0.16rem;
  background: rgba(18, 75, 156, 1);
  text-align: center;
  margin-bottom: 0.3rem;
  // > span {
  //   display: inline-block;
  // }
  // span.circle {
  //   margin-right: 0.1rem;
  //   width: 0.06rem;
  //   height: 0.06rem;
  //   background: rgba(0, 234, 255, 1);
  //   border-radius: 50%;
  // }
}
.title-top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  > div {
    display: flex;
    font-size: 0.14rem;
    font-weight: 400;

    > div {
      width: 0.8rem;
      height: 0.28rem;
      line-height: 0.3rem;
      color: #0e8bff;
      // background: rgba(14, 139, 255, 0.32);
      // border: 1px solid rgba(14, 139, 255, 1);
      text-align: center;
      // clip-path: polygon(100% 0, 100% 100%, 12% 100%, 0% 70%, 0 0);
      background: url(../../assets/<EMAIL>);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      cursor: pointer;
    }
    div.type-active {
      background: url(../../assets/<EMAIL>);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      color: rgba(0, 234, 255, 1);
    }
    :nth-of-type(2) {
      margin-left: 0.1rem;
    }
  }
}
.table-data {
  width: 100%;
  .table-data-thead {
    width: 100%;
    .tr {
      display: flex;
      justify-content: space-between;
      .th {
        background: transparent !important;
        color: #ffffff;
        padding: 0;
        text-align: center;
        border: none;
        line-height: 0.34rem;
        font-size: 0.14rem;
        height: 0.34rem;
      }
      > :nth-of-type(1) {
        width: 20%;
      }
      > :nth-of-type(2) {
        width: 25%;
      }
      > :nth-of-type(3) {
        width: 20%;
      }
      > :nth-of-type(4) {
        width: 25%;
      }
    }
  }
  .table-data-tbody {
    width: 4.6rem;
    height: calc(2rem - 0.35rem);
    .tr {
      display: flex;
      justify-content: space-between;
      .td {
        color: #ffffff;
        padding: 0;
        font-size: 0.14rem;
        text-align: center;
        height: 0.34rem;
        line-height: 0.34rem;
        border: none;
      }
      > :nth-of-type(1) {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 20%;
      }
      > :nth-of-type(2) {
        width: 25%;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
      > :nth-of-type(3) {
        width: 20%;
      }
      > :nth-of-type(4) {
        width: 25%;
      }
    }
  }
  .table-data-tbody .tr:nth-child(odd) {
    background: rgba(5, 47, 97, 1);
  }
  .table-data-tbody .tr:nth-child(even) {
    background: transparent;
  }
  .tdBefore {
    //前三
    width: 0.26rem;
    display: block;
    text-align: center;
    height: 0.2rem;
    line-height: 0.2rem;
    background: rgba(255, 133, 9, 1);
    border-radius: 0.06rem;
    margin: 0 auto;
  }
  .tdAfter {
    //前三外
    width: 0.26rem;
    display: block;
    text-align: center;
    height: 0.2rem;
    line-height: 0.2rem;
    background: rgba(18, 136, 226, 1);
    border-radius: 0.06rem;
    margin: 0 auto;
  }
}
.common-main {
  padding: 0;
}
.left-region {
  width: 100%;
  height: 100%;
  .left-region-bottom {
    position: absolute;
    bottom: 0;
    left: 0;
    padding: 0 0.2rem 0 0.2rem;
    display: flex;
    justify-content: space-between;
    height: 3.7rem;
    // padding-top: 0.5rem;
    width: 1920px;
    background-image: url("../../assets/bottomBg.png");
    background-size: 100% 100%;
    // box-shadow: 0 0 10px 10px #888888;

    > div {
      position: relative;
      padding: 0.16rem;
      margin-bottom: 0.1rem;
      width: 4.46rem;
    }
    .left-part-one {
      width: 4rem;
    }
  }
  .left-region-top {
    height: 100%;
    width: 100%;
    position: relative;
    // margin-bottom: 0.35rem;
    // margin-top: 0.4rem;
    .conner-left-top {
      position: absolute;
      top: -2px;
      left: -2px;
      width: 0.1rem;
      height: 0.1rem;
      border-top: 2px solid #00eaff;
      border-left: 2px solid #00eaff;
    }
    .conner-right-top {
      position: absolute;
      top: -2px;
      right: -2px;
      width: 0.1rem;
      height: 0.1rem;
      border-top: 2px solid #00eaff;
      border-right: 2px solid #00eaff;
    }
    .conner-left-bot {
      position: absolute;
      left: -2px;
      bottom: -2px;
      width: 0.1rem;
      height: 0.1rem;
      border-bottom: 2px solid #00eaff;
      border-left: 2px solid #00eaff;
    }
    .conner-right-bot {
      position: absolute;
      right: -2px;
      bottom: -2px;
      width: 0.1rem;
      height: 0.1rem;
      border-right: 2px solid #00eaff;
      border-bottom: 2px solid #00eaff;
    }
    .center-map {
      width: 1920px;
      height: 1080px;
      position: relative;
      .type-car {
        margin-top: 0.1rem;
        position: absolute;
        left: 0;
        top: 0;
        box-sizing: border-box;
        padding: 0.2rem 0.15rem 0.1rem 0.5rem;
        width: 2.5rem;
        .type-car-text {
          font-size: 0.22rem;
        }
        .count {
          font-size: 0.24rem;
          font-weight: bold;
        }
        .line {
          font-size: 0.24rem;
        }
        > div {
          display: flex;
          align-items: center;
          justify-content: left;
          margin-bottom: 0.2rem;
          width: 2rem;
          > div {
            height: 0.5rem;
          }
          > div:nth-child(1) {
            width: 0.6rem;
            display: flex;
            align-items: center;
            justify-content: center;
            // background-image: url("../../assets/carbox.png");
            background-size: 100% 100%;
          }
          .circle {
            align-self: center;
            // border-radius: 50%;
            margin-right: 0.24rem;
            img {
              width: 0.6rem;
              height: 0.6rem;
            }
          }
        }
        .legend {
          font-size: 0.15rem;
          color: #19f1f9;
        }
      }
      .type-department {
        margin-top: 0.1rem;
        position: absolute;
        right: 0;
        top: 0;
        box-sizing: border-box;
        padding: 0.2rem 0.5rem 0.1rem 0.15rem;
        width: 2.5rem;
        img {
          width: 0.5rem;
          height: 0.56rem;
          margin: 0.09rem 0;
          cursor: pointer;
        }
        > div {
          display: flex;
          flex-direction: column;
          align-items: flex-end;
        }
      }
    }
    .img-top-right {
      position: absolute;
      background: url(../../assets/<EMAIL>);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      width: 3rem;
      height: 0.08rem;
      top: -0.2rem;
      right: 0;
    }
    .img-top-left {
      position: absolute;
      background: url(../../assets/<EMAIL>);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      width: 3rem;
      height: 0.08rem;
      top: -0.2rem;
      left: 0;
    }
    .img-bottom-right {
      position: absolute;
      background: url(../../assets/<EMAIL>);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      width: 1.73rem;
      height: 0.08rem;
      bottom: -0.2rem;
      right: 0;
    }
    .img-bottom-left {
      position: absolute;
      background: url(../../assets/<EMAIL>);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      width: 1.73rem;
      height: 0.08rem;
      bottom: -0.2rem;
      left: 0;
    }
    .img-left-top {
      position: absolute;
      background: url(../../assets/<EMAIL>);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      width: 0.08rem;
      height: 0.83rem;
      top: 0;
      left: -0.2rem;
    }
    .img-left-bottom {
      position: absolute;
      background: url(../../assets/<EMAIL>);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      width: 0.08rem;
      height: 0.83rem;
      bottom: 0;
      left: -0.2rem;
    }
    .img-right-top {
      position: absolute;
      background: url(../../assets/<EMAIL>);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      width: 0.08rem;
      height: 0.83rem;
      top: 0;
      right: -0.2rem;
    }
    .img-right-bottom {
      position: absolute;
      background: url(../../assets/<EMAIL>);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      width: 0.08rem;
      height: 0.83rem;
      bottom: 0;
      right: -0.2rem;
    }
  }
}
.display-state {
  display: none !important;
  transform: scale(0);
  transition: 1s;
}
.display-state-map {
  height: 100%;
  width: 1843px;
}
.conner-left-top {
  position: absolute;
  top: -2px;
  left: -2px;
  width: 0.1rem;
  height: 0.1rem;
  border-top: 2px solid #00eaff;
  border-left: 2px solid #00eaff;
}
.conner-right-top {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 0.1rem;
  height: 0.1rem;
  border-top: 2px solid #00eaff;
  border-right: 2px solid #00eaff;
}
.conner-left-bot {
  position: absolute;
  left: -2px;
  bottom: -2px;
  width: 0.1rem;
  height: 0.1rem;
  border-bottom: 2px solid #00eaff;
  border-left: 2px solid #00eaff;
}
.conner-right-bot {
  position: absolute;
  right: -2px;
  bottom: -2px;
  width: 0.1rem;
  height: 0.1rem;
  border-right: 2px solid #00eaff;
  border-bottom: 2px solid #00eaff;
}
.sub-title {
  margin-bottom: 0.1rem !important;
}
.map-box {
  overflow: hidden;
  width: 100%;
  height: 100%;
}
.quanpin {
  position: absolute;
  top: -0.88rem;
  right: 0.5rem;
  transition: 1.5s;
  z-index: 99;
}
.fade-enter-active,
.fade-leave-active {
  transition: all 0.5s;
  position: absolute;
}
.fade-enter, .fade-leave-to /* .fade-leave-active below version 2.1.8 */ {
  left: -5rem;
}
.common-title .title {
  text-shadow: 0 0 5px blue, 0 0 5px blue;
  font-size: 0.22rem;
}
.mile,
.mile2 {
  background: url("../../assets/lcs-k1.png");
  background-size: 100% 100%;
  width: 0.31rem;
  height: 0.45rem;
  line-height: 0.45rem;
  font-size: 0.26rem;
  text-align: center;
  margin-right: 0.05rem;
}
.mile2 {
  background: url("../../assets/lcs-k2.png");
  background-size: 100% 100%;
}
.mileage {
  display: flex;
  align-items: center;
}
.mile-cycle {
  cursor: pointer;
  width: 0.25rem;
  height: 0.25rem;
  line-height: 0.25rem;
  text-align: center;
  border: 1px solid rgba(0, 132, 255, 1);
  border-radius: 5px;
}
.mile-cycle-selected {
  background: rgba(0, 132, 255, 1);
  margin: 0 0.15rem;
}
.playback {
  width: 1.63rem;
  height: 0.35rem;
  background: linear-gradient(
    0deg,
    rgba(77, 161, 248, 1) 0%,
    rgba(54, 116, 217, 1) 100%
  );
  cursor: pointer;
  border-radius: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.playback-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  img {
    margin: 0.1rem 0;
  }
}
.playback-form {
  padding: 0.2rem 0.3rem !important;
}
.ant-form {
  color: #fff;
}
.form-item {
  margin-bottom: 0.1rem;
}
.common-content-one {
  .common-content-one-content {
    box-sizing: border-box;
    width: 3.9rem;
    height: 2.5rem;
    background: rgba(16, 29, 120, 0.53);
    padding: 0.18rem 0 0.12rem 0;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .one-top {
      padding-left: 0.14rem;
      padding-right: 0.14rem;
      display: flex;
      align-items: center;
      padding-bottom: 0.1rem;
      .one-top-left {
        width: 0.42rem;
        height: 0.42rem;
        border-radius: 50%;
        margin-right: 0.12rem;
        img {
          width: 0.42rem;
          height: 0.42rem;
          border-radius: 50%;
        }
      }
      .one-top-right {
        > :nth-of-type(1) {
          color: rgba(0, 234, 255, 1);
          font-size: 0.2rem;
        }
        > :nth-of-type(2) {
          color: rgba(191, 208, 210, 1);
          font-size: 0.14rem;
        }
      }
    }
    .line {
      background: rgba(255, 255, 255, 0.35);
      height: 0.01rem;
      padding-left: 0.14rem;
      padding-right: 0.14rem;
    }
    .one-center {
      padding-left: 0.14rem;
      padding-right: 0.14rem;
      padding-top: 0.16rem;
      font-size: 0.2rem;
      color: rgba(255, 255, 255, 1);
      line-height: 0.34rem;
      > :nth-of-type(1) {
        line-height: 0.34rem;
        // padding-bottom: 0.16rem;
        > :nth-of-type(1) {
          display: inline-block;
          padding: 0 0.07rem;
          background: rgba(253, 21, 21, 1);
          border-radius: 0.04rem;
        }
      }
      > :nth-of-type(2) {
        // padding-bottom: 0.16rem;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    .one-bottom {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      padding: 0 0.15rem;
      font-size: 0.18rem;
      color: #ffffff;
      text-align: center;
      background-image: radial-gradient(
        #1c3da8,
        rgba(28, 61, 168, 0.7),
        rgba(28, 61, 168, 0.1)
      );
    }
  }
  .common-content-one-content1 {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    box-sizing: border-box;
    width: 3.9rem;
    height: 2.5rem;
    .warn-ones {
      font-size: 0.2rem;
      display: flex;
      align-items: center;
      font-weight: bold;
      > div {
        margin-left: 0.18rem;
      }
      > :nth-of-type(1) {
        color: rgba(255, 255, 255, 1);
        font-size: 0.2rem !important;
        font-weight: normal;
        padding: 0 0.14rem;
        background: rgba(253, 21, 21, 1);
        border-radius: 0.04rem;
        white-space: nowrap;
      }
      span:nth-of-type(2) {
        color: #e81519;
        margin: 0 0.2rem;
      }
    }
    .warn-img {
      display: flex;
      justify-content: space-around;
      box-sizing: border-box;
      padding: 0 0.5rem;
      margin-top: 0.25rem;
      font-size: 0.2rem;
      > :nth-of-type(1) {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 0.68rem;
        height: 0.68rem;
        background-image: url(../../assets/<EMAIL>) !important;
        background-size: 100% 100% !important;
      }
      > :nth-of-type(2) {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 0.68rem;
        height: 0.68rem;
        background-image: url(../../assets/<EMAIL>) !important;
        background-size: 100% 100% !important;
      }
    }
    .warn-text {
      display: flex;
      justify-content: space-around;
      box-sizing: border-box;
      padding: 0 0.5rem;
      margin-top: 0.16rem;
      font-size: 0.2rem;
      > div {
        display: flex;
        align-items: baseline;
        justify-content: center;
        width: 1.35rem;
      }
    }
    .warn-open {
      // margin-top: 0.4rem;
      font-size: 0.18rem;
      color: #ffffff;
      text-align: center;
      background-image: radial-gradient(
        #1c3da8,
        rgba(28, 61, 168, 0.7),
        rgba(28, 61, 168, 0.1)
      );
    }
  }
}
.common-content-two {
  .common-content-two-content {
    box-sizing: border-box;
    width: 4.2rem;
    height: 2.5rem;
    background: #001865;
    img {
      width: 4.2rem;
      height: 2.5rem;
    }
  }
  .common-content-two-content1 {
    box-sizing: border-box;
    width: 4.2rem;
    height: 2.5rem;
  }
}
.common-content-three {
  box-sizing: border-box;
  width: 4.5rem;
  height: 2.5rem;
  .common-content-three-content {
    position: relative;
    background-image: url(../../assets/task/zhongxin.png);
    background-size: 100% 100% !important;
    height: 2.5rem;
    > div {
      div {
        margin-bottom: -0.2rem;
        text-align: center;
        color: rgba(196, 215, 255, 1);
        font-size: 0.14rem;
      }
    }
    img {
      width: 1.37rem;
      height: 1.19rem;
    }
    .three-one {
      position: absolute;
      top: 0.52rem;
      left: 0.05rem;
    }
    .three-two {
      position: absolute;
      top: 0.05rem;
      left: 1.6rem;
    }
    .three-three {
      position: absolute;
      top: 0.57rem;
      right: 0rem;
    }
  }
}
.common-content-four {
  box-sizing: border-box;
  width: 4.5rem;
  height: 2.5rem;
  display: flex;
  > :nth-of-type(1) {
    img {
      width: 0.48rem;
      height: 2.3rem;
    }
  }
  > :nth-of-type(2) {
    width: 3.88rem;
    // height: 2.24rem;
    height: auto;
    background: url(../../assets/<EMAIL>);
    background-size: 100% 100%;
    padding: 0.25rem 0.25rem 0.25rem 0.5rem;
    .four-top {
      display: flex;
      border-bottom: 1px solid rgba(255, 255, 255, 0.75);
      padding-bottom: 0.12rem;
      > :nth-of-type(1) {
        > div:nth-of-type(1) {
          font-size: 0.16rem;
          display: inline-block;
          // padding: 0 0.1rem;
          width: 0.52rem;
          text-align: center;
          height: 0.26rem;
          line-height: 0.26rem;
          background: rgba(253, 21, 21, 1);
          border-radius: 0.04rem;
          margin-right: 0.15rem;
        }
        font-size: 0.18rem;
        span {
          margin-right: 0.15rem;
          margin-left: 0.15rem;
          color: #e81519;
        }
      }
      // > :nth-of-type(2) {
      // }
    }
    .four-bottom {
      margin-top: 0.1rem;
      .four-bottom-top {
        display: flex;
        justify-content: space-between;
        .four-bottom-left {
          margin-bottom: 0.18rem;
          > :nth-of-type(1) {
            margin-bottom: 0.1rem;
            img {
              margin-right: 0.13rem;
              width: 0.25rem;
              height: 0.23rem;
            }
            display: flex;
            align-items: center;
            font-size: 0.19rem;
          }
          > :nth-of-type(2) {
            font-size: 0.18rem;
          }
        }
        .four-bottom-right {
          width: 0.72rem;
          height: 0.59rem;
        }
      }
      .four-bottom-bottom {
        width: 1.2rem;
        height: 0.34rem;
        line-height: 0.34rem;
        font-size: 0.18rem;
        text-align: center;
        background: rgba(1, 55, 166, 1);
      }
    }
  }
}
.content-text {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}
.content-text1 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
</style>
<style lang="less">
.task-management-cont {
  .video-js.vjs-fluid {
    z-index: 999;
  }
  .common-content-two-content1 {
    .water-monitor-tab {
      height: 0.24rem;
      font-size: 0.12rem;
      color: rgba(255, 255, 255, 1);
      background: rgba(12, 39, 92, 1);
      border-radius: 0;
      padding: 0 0.1rem;
      border: none;
    }
    .ant-radio-group {
      width: 100%;
      display: flex;
      justify-content: space-around;
      background-color: #0f245e;
    }
    .ant-radio-group-solid
      .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
      background: #0084ff;
      border: none;
      padding: 0 0.15rem;
    }
    .ant-radio-button-wrapper:not(:first-child)::before {
      background: transparent;
    }
    .ant-radio-button-wrapper-checked::before {
      background: transparent !important;
    }
    .ant-radio-button-wrapper-checked {
      z-index: 1;
      border-color: #0084ff !important;
      -webkit-box-shadow: -1px 0 0 0 #0084ff;
      box-shadow: -1px 0 0 0 #0084ff;
    }
    .ant-radio-group-small .ant-radio-button-wrapper {
      // width: 14.28%;
      // width: 16.66%;
      text-align: center;
    }
    .ant-radio-group {
      display: flex;
    }
    .ant-radio-group-small .ant-radio-button-wrapper {
      padding: 0;
    }
  }
}
</style>
<template>
  <!-- 车辆管理 -->
  <section class="common-main task-management-cont">
    <!-- 左侧部分 -->
    <section class="left-region">
      <section
        :class="{ 'left-region-top': true, 'display-state-map': displayState }"
      >
        <div class="map-box">
          <section class="center-map">
            <!-- <keep-alive> -->
              <gao-de-map
                ref="map"
                :mapStyle="mapStyle"
                :mapZoom="13"
                :enterType="7"
                :jointEnforcementMarker="jointEnforcementMarker"
                :viewMode="'2D'"
              ></gao-de-map>
            <!-- </keep-alive> -->
            <section class="type-car">
              <div class="type-car-text">部门联动任务统计</div>
              <div v-for="(item, index) in taskStatistics" :key="index">
                <div class="circle">
                  <img
                    :src="item.state ? item.imgs[1] : item.imgs[0]"
                    alt
                    @click="checkCarType(index)"
                  />
                </div>
                <div :style="{ color: item.state ? '#F01519' : '#19F1F9' }">
                  <div>{{ item.name }}</div>
                  <div>
                    <span class="count">{{ item.value }}</span
                    >件
                  </div>
                </div>
              </div>
              <div class="legend">注：今日任务</div>
            </section>
            <section class="type-department">
              <div>
                <img
                  src="@/assets/<EMAIL>"
                  alt=""
                  @click="toDetail('/departmentDetail?type=1')"
                />
                <div>生态环境局</div>
              </div>
              <div>
                <img
                  src="@/assets/<EMAIL>"
                  alt=""
                  @click="toDetail('/vehicleManage')"
                />
                <div>综合行政执法局</div>
              </div>
              <div>
                <img
                  src="@/assets/<EMAIL>"
                  alt=""
                  @click="toDetail('/departmentDetail?type=3')"
                />
                <div>农业和水务局</div>
              </div>
              <div>
                <img
                  src="@/assets/<EMAIL>"
                  alt=""
                  @click="toDetail('/departmentDetail?type=4')"
                />
                <div>住房建设和交通运输局</div>
              </div>
              <div>
                <img
                  src="@/assets/jiedaoban.png"
                  alt=""
                  @click="toDetail('/streetDetail?type=5')"
                />
                <div>街道办</div>
              </div>
            </section>
          </section>
        </div>
        <section class="quanpin" @click="fullScreen">
          <img src="@/assets/quanping.png" alt />
        </section>
      </section>
      <section
        :class="{ 'left-region-bottom': true }"
        style="transition: all 1s"
        :style="{
          opacity: !displayState ? '1' : '0',
          visibility: !displayState ? '' : 'hidden'
        }"
      >
        <!-- 下面第一栏(巡岗) -->
        <div class="common-title left-part-one" v-if="divType === 'patrol'">
          <div class="title title-top">
            <span>{{ `巡岗报警` }}</span>
          </div>
          <div class="sub-title">
            <img
              src="@/assets/biaoti.png"
              alt
              class="title-img"
              style="width: 3.9rem;"
            />
          </div>
          <div class="common-content common-content-one">
            <div class="common-content-one-content">
              <div>
                <div class="one-top">
                  <div class="one-top-left">
                    <!-- <a-avatar
                    size="large"
                    icon="user"
                    style="width: 0.42rem;height: 0.42rem;"
                  /> -->
                    <img
                      src="@/assets/touxiang.png"
                      alt=""
                      style="width: 0.42rem;height: 0.42rem;"
                    />
                  </div>
                  <div class="one-top-right">
                    <div>{{ taskDetails.departmentName }}</div>
                    <div>{{ taskDetails.createTime }}</div>
                  </div>
                </div>
                <div class="line"></div>
                <div class="one-center">
                  <div class="content-text1" :title="taskDetails.title">
                    <div>{{ taskType ? TASKTYPE[taskType] : "" }}</div>
                    <!-- 茶店子街道53号附近的小河道有少量漂浮物，尽快安排人员打捞。 -->
                    {{ taskDetails.title }}
                  </div>
                  <div :title="taskDetails.content">
                    任务内容：{{ taskDetails.content }}
                  </div>
                  <div>任务类型：巡岗</div>
                </div>
              </div>
              <div
                class="one-bottom"
                :title="taskDetails.address ? taskDetails.address : ''"
              >
                位置：{{ taskDetails.address ? taskDetails.address : "" }}
              </div>
            </div>
          </div>
        </div>
        <!-- 下面第一栏(站点) -->
        <div class="common-title left-part-one" v-if="divType === 'site'">
          <div class="title title-top">
            <span>{{ `监测告警` }}</span>
            <span class="f-color">{{
              monitorAlarm.alarmTime ? monitorAlarm.alarmTime : ""
            }}</span>
          </div>
          <div class="sub-title">
            <img
              src="@/assets/biaoti.png"
              alt
              class="title-img"
              style="width: 3.9rem;"
            />
          </div>
          <div class="common-content common-content-one">
            <div class="common-content-one-content1">
              <div>
                <div class="warn-ones" v-if="taskType == 1">
                  <div>{{ taskType ? TASKTYPE[taskType] : "" }}</div>
                  <div>
                    <span>{{
                      monitorAlarm.textOne ? monitorAlarm.textOne : ""
                    }}</span>
                    <span>{{
                      monitorAlarm.keyWord ? monitorAlarm.keyWord : ""
                    }}</span>
                    <span>{{
                      monitorAlarm.textTwo ? monitorAlarm.textTwo : ""
                    }}</span>
                  </div>
                </div>
                <div class="warn-ones" v-if="taskType == 2">
                  <div>{{ taskType ? TASKTYPE[taskType] : "" }}</div>
                  <div>
                    <span>{{
                      monitorAlarm.textOne ? monitorAlarm.textOne : ""
                    }}</span>
                    <span>{{
                      monitorAlarm.keyWord
                        ? airTypeNamePub[monitorAlarm.keyWord]
                        : ""
                    }}</span>
                    <span>{{
                      monitorAlarm.textTwo ? monitorAlarm.textTwo : ""
                    }}</span>
                  </div>
                </div>
                <div
                  class="warn-ones"
                  v-if="taskType == 3"
                  style="display: block;font-size:0.16rem;"
                >
                  <div style="display: inline-block;">
                    {{ taskType ? TASKTYPE[taskType] : "" }}
                  </div>
                  {{ taskDetails.content }}
                </div>
                <div class="warn-img">
                  <div>
                    {{
                      monitorAlarm.alarmValue ? monitorAlarm.alarmValue : "-"
                    }}
                  </div>
                  <div>
                    {{
                      monitorAlarm.alarmStandard
                        ? monitorAlarm.alarmStandard
                        : monitorAlarm.alarmStandardMax
                        ? monitorAlarm.alarmStandardMin +
                          " - " +
                          monitorAlarm.alarmStandardMax
                        : "-"
                    }}
                  </div>
                </div>
                <div class="warn-text">
                  <div>
                    监测值<span style="font-size:0.12rem;color: #ccc4c4;"
                      >({{
                        !monitorAlarm.unit ? "" : unitList[monitorAlarm.unit]
                      }})</span
                    >
                  </div>
                  <div>
                    报警阈值<span style="font-size:0.12rem;color: #ccc4c4;"
                      >({{
                        !monitorAlarm.unit ? "" : unitList[monitorAlarm.unit]
                      }})</span
                    >
                  </div>
                </div>
              </div>
              <div
                class="warn-open"
                v-if="taskType == 1"
                :title="taskDetails.address ? taskDetails.address : ''"
              >
                位置：{{ taskDetails.address ? taskDetails.address : "" }}
              </div>
              <div
                class="warn-open"
                v-if="taskType == 2"
                :title="taskDetails.address ? taskDetails.address : ''"
              >
                位置：{{
                  monitorAlarm.alarmAddress ? monitorAlarm.alarmAddress : ""
                }}
              </div>
              <div
                class="warn-open"
                v-if="taskType == 3"
                :title="taskDetails.address ? taskDetails.address : ''"
              >
                位置：{{ taskDetails.address ? taskDetails.address : "" }}
              </div>
            </div>
          </div>
        </div>
        <!-- 下面第二栏(巡岗) -->
        <div class="common-title left-part-two" v-if="divType === 'patrol'">
          <div style="display:flex">
            <div>
              <div class="title">{{ `报警图片` }}</div>
              <div class="sub-title">
                <img
                  src="@/assets/biaoti.png"
                  style="width:4.2rem"
                  alt
                  class="title-img"
                />
              </div>
            </div>
          </div>
          <div class="common-content common-content-two" style="border:none;">
            <div
              class="common-content-two-content"
              v-if="patrolImg && patrolImg.length !== 0"
            >
              <swiper :options="swiperImgOption">
                <swiper-slide v-for="(item, index) in patrolImg" :key="index">
                  <img :src="item" alt="" />
                </swiper-slide>
                <div class="swiper-pagination" slot="pagination"></div>
              </swiper>
            </div>
            <div class="common-content-two-content" v-else>
              <img src="@/assets/<EMAIL>" alt="" />
            </div>
          </div>
        </div>
        <!-- 下面第二栏(站点) -->
        <div class="common-title left-part-two" v-if="divType === 'site'">
          <div style="display:flex">
            <div>
              <div class="title">{{ `监测数据` }}</div>
              <div class="sub-title">
                <img
                  src="@/assets/biaoti.png"
                  style="width:4.2rem"
                  alt
                  class="title-img"
                />
              </div>
            </div>
          </div>
          <div class="common-content common-content-two" style="border:none;">
            <!-- 水 -->
            <div class="common-content-two-content1" v-if="taskType == 1">
              <a-radio-group
                v-model="airType"
                size="small"
                buttonStyle="solid"
                style="width:100%;"
                @change="waterMonitorSelectChange"
              >
                <a-radio-button
                  v-for="(item, index) in airTypes"
                  :key="index"
                  :value="`${item.pollutantCode}`"
                  class="water-monitor-tab"
                  >{{ item.pollutantName }}</a-radio-button
                >
              </a-radio-group>
              <LineChartDashed
                :id="'waterMonitor-line'"
                :width="'4.2rem'"
                :height="'2.5rem'"
                :propData="waterMonitor"
                :smooth="true"
              ></LineChartDashed>
            </div>
            <!-- 空气 -->
            <div class="common-content-two-content1" v-if="taskType == 2">
              <a-radio-group
                v-model="airType"
                size="small"
                buttonStyle="solid"
                style="width:100%;"
                @change="airTypeChange"
              >
                <a-radio-button
                  v-for="item in airTypes"
                  :key="item.pollutantCode"
                  :value="item.pollutantCode"
                  :class="{
                    'water-monitor-tab': true
                  }"
                  >{{ airTypeNamePub[item.pollutantName] }}</a-radio-button
                >
              </a-radio-group>
              <LineChartDashed
                :id="'air-trends'"
                :width="'4.2rem'"
                :height="'2.5rem'"
                :propData="airData"
                :smooth="true"
              ></LineChartDashed>
            </div>
            <div class="common-content-two-content1" v-if="taskType == 3">
              <LineChartDashed
                :id="'air-trendsqwqw'"
                :width="'4.2rem'"
                :height="'2.7rem'"
                :propData="airData"
                :smooth="true"
              ></LineChartDashed>
            </div>
          </div>
        </div>
        <!-- 下面第三栏(站点、巡岗) -->
        <div class="common-title left-part-three">
          <div class="title">{{ `关联执行部门` }}</div>
          <div class="sub-title">
            <img
              src="@/assets/biaoti.png"
              alt
              class="title-img"
              style="width: 4.5rem;"
            />
          </div>
          <!-- 一个部门以上 -->
          <div class="common-content common-content-three" style="border:none;">
            <div
              class="common-content-three-content"
              v-if="departmentDetail.length > 0 && departmentDetail.length > 1"
              :style="{
                'background-image':
                  departmentDetail.length > 2
                    ? `url(${zhongxin})`
                    : `url(${zhongxin2})`
              }"
            >
              <div
                class="three-one"
                @click="departmentState = 'one'"
                v-if="departmentDetail.length >= 1"
              >
                <div>{{ departmentDetail[0].departmentName }}</div>
                <template v-if="departmentDetail[0].state">
                  <!-- 完成选中 -->
                  <img
                    :src="checkImgTypeList.oneImg1"
                    alt=""
                    v-if="departmentState === 'one'"
                  />
                  <!-- 完成未选中 -->
                  <img :src="checkImgTypeList.oneImg2" alt="" v-else />
                </template>
                <template v-if="!departmentDetail[0].state">
                  <!-- 未完成选中 -->
                  <img
                    :src="checkImgTypeList.oneImg3"
                    alt=""
                    v-if="departmentState === 'one'"
                  />
                  <!-- 未完成未选中 -->
                  <img :src="checkImgTypeList.oneImg4" alt="" v-else />
                </template>
              </div>
              <div
                class="three-three"
                @click="departmentState = 'two'"
                v-if="departmentDetail.length >= 2"
              >
                <div>{{ departmentDetail[1].departmentName }}</div>
                <template v-if="departmentDetail[1].state">
                  <!-- 完成选中 -->
                  <img
                    :src="checkImgTypeList.twoImg1"
                    alt=""
                    v-if="departmentState === 'two'"
                  />
                  <!-- 完成未选中 -->
                  <img :src="checkImgTypeList.twoImg2" alt="" v-else />
                </template>
                <template v-if="!departmentDetail[1].state">
                  <!-- 未完成选中 -->
                  <img
                    :src="checkImgTypeList.twoImg3"
                    alt=""
                    v-if="departmentState === 'two'"
                  />
                  <!-- 未完成未选中 -->
                  <img :src="checkImgTypeList.twoImg4" alt="" v-else />
                </template>
              </div>
              <div
                class="three-two"
                @click="departmentState = 'three'"
                v-if="departmentDetail.length >= 3"
              >
                <div>{{ departmentDetail[2].departmentName }}</div>
                <template v-if="departmentDetail[2].state">
                  <!-- 完成选中 -->
                  <img
                    :src="checkImgTypeList.threeImg1"
                    alt=""
                    v-if="departmentState === 'three'"
                  />
                  <!-- 完成未选中 -->
                  <img :src="checkImgTypeList.threeImg2" alt="" v-else />
                </template>
                <template v-if="!departmentDetail[2].state">
                  <!-- 未完成选中 -->
                  <img
                    :src="checkImgTypeList.threeImg3"
                    alt=""
                    v-if="departmentState === 'three'"
                  />
                  <!-- 未完成未选中 -->
                  <img :src="checkImgTypeList.threeImg4" alt="" v-else />
                </template>
              </div>
            </div>
            <!-- 一个部门 -->
            <div
              class="common-content-three-content"
              v-if="departmentDetail.length > 0 && departmentDetail.length == 1"
              :style="{ 'background-image': `url(${zhongxin3})` }"
            >
              <div
                class="three-two"
                @click="departmentState = 'one'"
                v-if="departmentDetail.length >= 1"
              >
                <div>{{ departmentDetail[0].departmentName }}</div>
                <template v-if="departmentDetail[0].state">
                  <!-- 完成选中 -->
                  <img
                    :src="checkImgTypeList.oneImg1"
                    alt=""
                    v-if="departmentState === 'one'"
                  />
                  <!-- 完成未选中 -->
                  <img :src="checkImgTypeList.oneImg2" alt="" v-else />
                </template>
                <template v-if="!departmentDetail[0].state">
                  <!-- 未完成选中 -->
                  <img
                    :src="checkImgTypeList.oneImg3"
                    alt=""
                    v-if="departmentState === 'one'"
                  />
                  <!-- 未完成未选中 -->
                  <img :src="checkImgTypeList.oneImg4" alt="" v-else />
                </template>
              </div>
            </div>
          </div>
        </div>
        <!-- 下面第四栏(站点) -->
        <div
          class="common-title left-part-four"
          style="padding: 0.15rem;"
          v-if="divType === 'site'"
        >
          <div class="title title-top">
            <span>{{ `部门反馈` }}</span>
            <span class="f-color">{{ taskUpdateTime }}</span>
          </div>
          <div class="sub-title" style="margin-bottom: 0.3rem">
            <img
              src="@/assets/biaoti.png"
              alt
              class="title-img"
              style="width: 4.5rem;"
            />
          </div>
          <div class="common-content common-content-four" style="border:none;">
            <div>
              <img src="@/assets/<EMAIL>" alt="" />
            </div>
            <div>
              <div class="four-top">
                <div v-if="taskType == 1">
                  <div>{{ taskType ? TASKTYPE[taskType] : "" }}</div>
                  {{ monitorAlarm.textOne ? monitorAlarm.textOne : ""
                  }}<span>{{
                    monitorAlarm.keyWord ? monitorAlarm.keyWord : ""
                  }}</span
                  >{{ monitorAlarm.textTwo ? monitorAlarm.textTwo : "" }}
                </div>
                <div v-if="taskType == 2">
                  <div>{{ taskType ? TASKTYPE[taskType] : "" }}</div>
                  {{ monitorAlarm.textOne ? monitorAlarm.textOne : ""
                  }}<span>{{
                    monitorAlarm.keyWord
                      ? airTypeNamePub[monitorAlarm.keyWord]
                      : ""
                  }}</span
                  >{{ monitorAlarm.textTwo ? monitorAlarm.textTwo : "" }}
                </div>
                <div v-if="taskType == 3" style="font-size:0.14rem;">
                  <div>{{ taskType ? TASKTYPE[taskType] : "" }}</div>
                  {{ taskDetails.content }}
                </div>
              </div>
              <div class="four-bottom">
                <div class="four-bottom-top">
                  <div class="four-bottom-left">
                    <div>
                      <img src="@/assets/<EMAIL>" alt="" />
                      {{
                        feedbackDetail.departmentName
                          ? feedbackDetail.departmentName
                          : ""
                      }}
                    </div>
                    <div
                      class="content-text"
                      :title="
                        feedbackDetail.remark ? feedbackDetail.remark : ''
                      "
                    >
                      反馈内容：{{
                        feedbackDetail.remark ? feedbackDetail.remark : ""
                      }}
                    </div>
                  </div>
                  <div class="four-bottom-right">
                    <img
                      v-if="feedbackDetail.status == 2"
                      src="@/assets/<EMAIL>"
                      alt=""
                    />
                    <img v-else src="@/assets/jxz.png" alt="" />
                  </div>
                </div>
                <!-- <div class="four-bottom-bottom" v-if="feedbackDetail.status">
                  查看图片
                </div> -->
              </div>
            </div>
          </div>
        </div>
        <!-- 下面第四栏(巡岗) -->
        <div
          class="common-title left-part-four"
          style="padding: 0.15rem;"
          v-if="divType === 'patrol'"
        >
          <div class="title title-top">
            <span>{{ `部门反馈` }}</span>
            <span class="f-color">{{ taskUpdateTime }}</span>
          </div>
          <div class="sub-title" style="margin-bottom: 0.3rem">
            <img
              src="@/assets/biaoti.png"
              alt
              class="title-img"
              style="width: 4.5rem;"
            />
          </div>
          <div class="common-content common-content-four" style="border:none;">
            <div>
              <img src="@/assets/<EMAIL>" alt="" />
            </div>
            <div>
              <div class="four-top">
                <div class="content-text1" :title="taskDetails.title">
                  <div>{{ taskType ? TASKTYPE[taskType] : "" }}</div>
                  <!-- 茶店子街道53号附近的小河道 有少量漂浮物，尽快安排人员打捞。 -->
                  {{ taskDetails.title }}
                </div>
              </div>
              <div class="four-bottom">
                <div class="four-bottom-top">
                  <div class="four-bottom-left">
                    <div>
                      <img src="@/assets/<EMAIL>" alt="" />
                      {{
                        feedbackDetail.departmentName
                          ? feedbackDetail.departmentName
                          : ""
                      }}
                    </div>
                    <div
                      class="content-text"
                      :title="
                        feedbackDetail.remark ? feedbackDetail.remark : ''
                      "
                    >
                      反馈内容：{{
                        feedbackDetail.remark ? feedbackDetail.remark : ""
                      }}
                    </div>
                  </div>
                  <div class="four-bottom-right">
                    <img
                      v-if="feedbackDetail.status == 2"
                      src="@/assets/<EMAIL>"
                      alt=""
                    />
                    <img v-else src="@/assets/jxz.png" alt="" />
                  </div>
                </div>
                <!-- <div class="four-bottom-bottom" v-if="feedbackDetail.status">
                  查看图片
                </div> -->
              </div>
            </div>
          </div>
        </div>
      </section>
    </section>
  </section>
</template>

<script lang="ts">
interface InData {
  name: string;
  dataList: string[];
}
/* eslint-disable @typescript-eslint/ban-ts-ignore */
//@ts-ignore
import AMap from "AMap";
import { aqiQualityTrend } from "@/api/air";
import { Component, Vue, Watch } from "vue-property-decorator";
import GaoDeMap from "@/components/GaoDeMap/index.vue";
import { Swiper, SwiperSlide } from "vue-awesome-swiper";
import PieChart from "@/components/Charts/PieChart.vue";
import LineChartDashed from "@/components/Charts/LineChartDashed.vue";
import quanbuImg from "@/assets/quanbu.png";
import quanbuActiveImg from "@/assets/quanbu_active.png";
import kongqiImg from "@/assets/kongqi.png";
import kongqiActiveImg from "@/assets/kongqi_active.png";
import shuiImg from "@/assets/shui.png";
import shuiActiveImg from "@/assets/shui_active.png";
import renyuanImg from "@/assets/renyuan.png";
import renyuanActiveImg from "@/assets/renyuan_active.png";
import jianzhuwuImg from "@/assets/jianzhuwu.png";
import jianzhuwuActiveImg from "@/assets/jianzhuwu_active.png";
import moment from "moment";
import zhongxin from "@/assets/task/zhongxin.png";
import zhongxin2 from "@/assets/task/zhongxin-2.png";
import zhongxin3 from "@/assets/task/zhongxin-3.png";
// 生态环境局
import sthjjNo from "@/assets/task/sthjj-no.png";
import sthjjNoActive from "@/assets/task/sthjj-no-active.png";
import sthjjYes from "@/assets/task/sthjj-yes.png";
import sthjjYesActive from "@/assets/task/sthjj-yes-active.png";
// 住房建设和交通运输局
import zjjNo from "@/assets/task/zjj-no.png";
import zjjNoActive from "@/assets/task/zjj-no-active.png";
import zjjYes from "@/assets/task/zjj-yes.png";
import zjjYesActive from "@/assets/task/zjj-yes-active.png";
// 综合行政执法局
import zhxzjNo from "@/assets/task/zhxzj-no.png";
import zhxzjNoActive from "@/assets/task/zhxzj-no-active.png";
import zhxzjYes from "@/assets/task/zhxzj-yes.png";
import zhxzjYesActive from "@/assets/task/zhxzj-yes-active.png";
// 农业和水务局
import nyhswNo from "@/assets/task/nyhsw-no.png";
import nyhswNoActive from "@/assets/task/nyhsw-no-active.png";
import nyhswYes from "@/assets/task/nyhsw-yes.png";
import nyhswYesActive from "@/assets/task/nyhsw-yes-active.png";
import {
  Table,
  Icon,
  Empty,
  Avatar,
  Select,
  DatePicker,
  TimePicker,
  Input,
  Button,
  message,
  FormModel,
  Radio
} from "ant-design-vue";
import {
  getEnforcement,
  getEnforcementTaskId,
  getAqiQuality,
  getTwentyFourHour
} from "@/api/task-management";
import { getMonitorItemDetailRecord } from "@/api/water";
@Component({
  name: "VehicleManage",
  components: {
    GaoDeMap,
    Swiper,
    SwiperSlide,
    PieChart,
    ATable: Table,
    AIcon: Icon,
    AEmpty: Empty,
    AAvatar: Avatar,
    ASelect: Select,
    ADatePicker: DatePicker,
    ATimePicker: TimePicker,
    AInput: Input,
    AButton: Button,
    AFormModel: FormModel,
    AFormModelItem: FormModel.Item,
    ARadioGroup: Radio.Group,
    ARadioButton: Radio.Button,
    LineChartDashed
  }
})
export default class extends Vue {
  @Watch("airType", { immediate: false, deep: true })
  public onAirType(newValue: any, oldValue: any) {
    if (newValue) {
      if (this.taskType == 1) {
        this.getMonitorItemDetailRecord(this.taskDetails.stationId, newValue);
      } else if (this.taskType == 2) {
        this.getAqiTrend();
      }
    }
  }
  @Watch("departmentState", { immediate: false, deep: true })
  public ondepartmentType(newValue: any, oldValue: any) {
    if (newValue && this.departmentDetail.length > 0) {
      if (newValue === "one") {
        this.feedbackDetail = this.departmentDetail[0];
        this.taskUpdateTime = this.departmentDetail[0].updateTime
          ? moment(new Date(this.departmentDetail[0].updateTime)).format(
              "MM-DD HH:mm"
            )
          : "";
      } else if (newValue === "two") {
        this.feedbackDetail = this.departmentDetail[1];
        this.taskUpdateTime = this.departmentDetail[1].updateTime
          ? moment(new Date(this.departmentDetail[1].updateTime)).format(
              "MM-DD HH:mm"
            )
          : "";
      } else if (newValue === "three") {
        this.feedbackDetail = this.departmentDetail[2];
        this.taskUpdateTime = this.departmentDetail[2].updateTime
          ? moment(new Date(this.departmentDetail[2].updateTime)).format(
              "MM-DD HH:mm"
            )
          : "";
      }
    }
  }
  private zhongxin: any = zhongxin;
  private zhongxin2: any = zhongxin2;
  private zhongxin3: any = zhongxin3;
  private taskUpdateTime: any = "";
  private mapStyle = "amap://styles/0739a6632d849ae1b5e6f1c52f7d12c3";
  private swiperImgOption = {
    pagination: {
      el: ".swiper-pagination",
      clickable: true // 允许点击小圆点跳转
    },
    direction: "horizontal",
    loop: true,
    autoplay: {
      delay: 3000,
      disableOnInteraction: true
    }
  };
  // site:空气、水、重污    patrol:应急
  private divType: any = "site";
  private departmentType: any = "two";
  private displayState = true;
  private fullScreen() {
    this.displayState = !this.displayState;
  }
  private unitList = {
    "μg/m3": "μg/m³",
    "mg/m3": "mg/m³",
    "mg/L": "mg/L"
  };
  private airTypeNamePub = {
    NO2: "NO₂",
    O3: "O₃",
    PM10: "PM₁₀",
    "PM2.5": "PM₂.₅",
    NOx: "NOx",
    NO: "NO",
    SO2: "SO₂",
    CO: "CO",
    AQI: "AQI"
  };
  private TASKTYPE: any = {
    "1": "水",
    "2": "空气",
    "3": "污染源",
    "4": "巡岗"
  };
  // 空气质量趋势type
  private airTypes: any[] = [
    {
      pollutantCode: "101",
      pollutantName: "NO₂"
    },
    {
      pollutantCode: "102",
      pollutantName: "O₃"
    },
    {
      pollutantCode: "104",
      pollutantName: "PM₁₀"
    },
    {
      pollutantCode: "105",
      pollutantName: "PM₂.₅"
    },
    {
      pollutantCode: "100",
      pollutantName: "SO₂"
    },
    {
      pollutantCode: "103",
      pollutantName: "CO"
    }
  ];
  private airType: any = "101";
  // 空气质量趋势
  private airData: any = {
    bottomList: [],
    dataList: [],
    unit: "",
    colorType: ""
  };
  private taskStatistics: any[] = [
    {
      name: "总计",
      value: 0,
      state: true,
      imgs: [quanbuImg, quanbuActiveImg],
      type: "all"
    },
    {
      name: "水环境质量",
      value: 0,
      state: false,
      imgs: [shuiImg, shuiActiveImg],
      type: 1
    },
    {
      name: "大气环境质量",
      value: 0,
      state: false,
      imgs: [kongqiImg, kongqiActiveImg],
      type: 2
    },
    {
      name: "重点污染源",
      value: 0,
      state: false,
      imgs: [jianzhuwuImg, jianzhuwuActiveImg],
      type: 3
    },
    {
      name: "巡岗报警",
      value: 0,
      state: false,
      imgs: [renyuanImg, renyuanActiveImg],
      type: 4
    }
  ];
  private jointEnforcementMarker: any = [];
  private markerList: any[] = [
    // {
    //   lng: 104.054946899,
    //   lat: 30.7331305004,
    //   type: 1,
    //   id: 1
    // },
    // {
    //   lng: 104.04636383,
    //   lat: 30.6779299871,
    //   type: 2,
    //   id: 2
    // },
    // {
    //   lng: 104.0185546,
    //   lat: 30.69313541,
    //   type: 3,
    //   id: 3
    // },
    // {
    //   lng: 104.019241,
    //   lat: 30.7272282,
    //   type: 4,
    //   id: 4
    // },
    // {
    //   lng: 104.0868759,
    //   lat: 30.7337207,
    //   type: 5,
    //   id: 5
    // }
  ];
  private taskNumber: any = {};
  // 水质监测数据
  private waterMonitor: any = {
    bottomList: [],
    dataList: [],
    unit: "mg/L"
  };
  mounted() {
    this.getEnforcement();
    // this.getAqiTrend();
    // 通过marker切换div
    //@ts-ignore
    // eslint-disable-next-line no-undef
    this.$bus.on("checkType", (res: any) => {
      this.divType = res[0].type;
      this.taskId = res[0].id;
      this.getEnforcementTaskId(res[0].id);
      this.displayState = false;
    });
  }

  beforeDestroy() {
    //@ts-ignore
    this.$bus.on("checkType", () => {})
  }
  private departmentState: any = "one";
  private checkImgTypeList: any = {
    oneImg1: "",
    oneImg2: "",
    oneImg3: "",
    oneImg4: "",
    twoImg1: "",
    twoImg2: "",
    twoImg3: "",
    twoImg4: "",
    threeImg1: "",
    threeImg2: "",
    threeImg3: "",
    threeImg4: ""
  };
  private checkImgType() {
    if (this.departmentDetail.length >= 1) {
      // 第一个部门
      this.checkImgTypeList.oneImg1 =
        this.departmentDetail[0].departmentName == "生态环境局"
          ? sthjjYesActive
          : this.departmentDetail[0].departmentName == "住房建设和交通运输局"
          ? zjjYesActive
          : this.departmentDetail[0].departmentName == "综合行政执法局" ||
            this.departmentDetail[0].departmentName == "综合执法局"
          ? zhxzjYesActive
          : this.departmentDetail[0].departmentName == "农业和水务局"
          ? nyhswYesActive
          : "";
      this.checkImgTypeList.oneImg2 =
        this.departmentDetail[0].departmentName == "生态环境局"
          ? sthjjYes
          : this.departmentDetail[0].departmentName == "住房建设和交通运输局"
          ? zjjYes
          : this.departmentDetail[0].departmentName == "综合行政执法局" ||
            this.departmentDetail[0].departmentName == "综合执法局"
          ? zhxzjYes
          : this.departmentDetail[0].departmentName == "农业和水务局"
          ? nyhswYes
          : "";
      this.checkImgTypeList.oneImg3 =
        this.departmentDetail[0].departmentName == "生态环境局"
          ? sthjjNoActive
          : this.departmentDetail[0].departmentName == "住房建设和交通运输局"
          ? zjjNoActive
          : this.departmentDetail[0].departmentName == "综合行政执法局" ||
            this.departmentDetail[0].departmentName == "综合执法局"
          ? zhxzjNoActive
          : this.departmentDetail[0].departmentName == "农业和水务局"
          ? nyhswNoActive
          : "";
      this.checkImgTypeList.oneImg4 =
        this.departmentDetail[0].departmentName == "生态环境局"
          ? sthjjNo
          : this.departmentDetail[0].departmentName == "住房建设和交通运输局"
          ? zjjNo
          : this.departmentDetail[0].departmentName == "综合行政执法局" ||
            this.departmentDetail[0].departmentName == "综合执法局"
          ? zhxzjNo
          : this.departmentDetail[0].departmentName == "农业和水务局"
          ? nyhswNo
          : "";
    }

    if (this.departmentDetail.length >= 2) {
      // 第二个部门
      this.checkImgTypeList.twoImg1 =
        this.departmentDetail[1].departmentName == "生态环境局"
          ? sthjjYesActive
          : this.departmentDetail[1].departmentName == "住房建设和交通运输局"
          ? zjjYesActive
          : this.departmentDetail[1].departmentName == "综合行政执法局"
          ? zhxzjYesActive
          : this.departmentDetail[1].departmentName == "农业和水务局"
          ? nyhswYesActive
          : "";
      this.checkImgTypeList.twoImg2 =
        this.departmentDetail[1].departmentName == "生态环境局"
          ? sthjjYes
          : this.departmentDetail[1].departmentName == "住房建设和交通运输局"
          ? zjjYes
          : this.departmentDetail[1].departmentName == "综合行政执法局"
          ? zhxzjYes
          : this.departmentDetail[1].departmentName == "农业和水务局"
          ? nyhswYes
          : "";
      this.checkImgTypeList.twoImg3 =
        this.departmentDetail[1].departmentName == "生态环境局"
          ? sthjjNoActive
          : this.departmentDetail[1].departmentName == "住房建设和交通运输局"
          ? zjjNoActive
          : this.departmentDetail[1].departmentName == "综合行政执法局"
          ? zhxzjNoActive
          : this.departmentDetail[1].departmentName == "农业和水务局"
          ? nyhswNoActive
          : "";
      this.checkImgTypeList.twoImg4 =
        this.departmentDetail[1].departmentName == "生态环境局"
          ? sthjjNo
          : this.departmentDetail[1].departmentName == "住房建设和交通运输局"
          ? zjjNo
          : this.departmentDetail[1].departmentName == "综合行政执法局"
          ? zhxzjNo
          : this.departmentDetail[1].departmentName == "农业和水务局"
          ? nyhswNo
          : "";
    }

    if (this.departmentDetail.length >= 3) {
      // 第三个部门
      this.checkImgTypeList.threeImg1 =
        this.departmentDetail[2].departmentName == "生态环境局"
          ? sthjjYesActive
          : this.departmentDetail[2].departmentName == "住房建设和交通运输局"
          ? zjjYesActive
          : this.departmentDetail[2].departmentName == "综合行政执法局"
          ? zhxzjYesActive
          : this.departmentDetail[2].departmentName == "农业和水务局"
          ? nyhswYesActive
          : "";
      this.checkImgTypeList.threeImg2 =
        this.departmentDetail[2].departmentName == "生态环境局"
          ? sthjjYes
          : this.departmentDetail[2].departmentName == "住房建设和交通运输局"
          ? zjjYes
          : this.departmentDetail[2].departmentName == "综合行政执法局"
          ? zhxzjYes
          : this.departmentDetail[2].departmentName == "农业和水务局"
          ? nyhswYes
          : "";
      this.checkImgTypeList.threeImg3 =
        this.departmentDetail[2].departmentName == "生态环境局"
          ? sthjjNoActive
          : this.departmentDetail[2].departmentName == "住房建设和交通运输局"
          ? zjjNoActive
          : this.departmentDetail[2].departmentName == "综合行政执法局"
          ? zhxzjNoActive
          : this.departmentDetail[2].departmentName == "农业和水务局"
          ? nyhswNoActive
          : "";
      this.checkImgTypeList.threeImg4 =
        this.departmentDetail[2].departmentName == "生态环境局"
          ? sthjjNo
          : this.departmentDetail[2].departmentName == "住房建设和交通运输局"
          ? zjjNo
          : this.departmentDetail[2].departmentName == "综合行政执法局"
          ? zhxzjNo
          : this.departmentDetail[2].departmentName == "农业和水务局"
          ? nyhswNo
          : "";
    }
  }
  private taskId: any = null;
  // 切换图标
  private checkCarType(index: any) {
    this.jointEnforcementMarker = [];
    for (const item of this.taskStatistics) {
      item.state = false;
    }
    this.taskStatistics[index].state = true;
    if (this.taskStatistics[index].type === "all") {
      this.jointEnforcementMarker = JSON.parse(JSON.stringify(this.markerList));
      return;
    }
    for (const item of this.markerList) {
      // console.log(this.taskStatistics[index].type);
      if (this.taskStatistics[index].type == 3) {
        if (item.type === this.taskStatistics[index].type || item.type == 5) {
          this.jointEnforcementMarker.push(item);
        }
      } else {
        if (item.type === this.taskStatistics[index].type) {
          this.jointEnforcementMarker.push(item);
        }
      }
    }
  }
  // 切换标签
  private airTypeChange(e: any) {
    this.airType = e.target.value;
  }

  // 切换div类型
  private checkDivType(text: any) {
    this.divType = text;
  }
  // 二级页面
  private toDetail(url: any) {
    /* eslint-disable @typescript-eslint/ban-ts-ignore */
    //@ts-ignore
    this.$router.push(url);
  }
  private getEnforcement() {
    getEnforcement().then((res: any) => {
      const data = res.data.data;
      this.markerList = data.list;
      this.taskNumber = data.data;
      this.taskStatistics = [
        {
          name: "总计",
          value: data.total,
          state: true,
          imgs: [quanbuImg, quanbuActiveImg],
          type: "all"
        },
        {
          name: "水环境质量",
          value: data.waterCount,
          state: false,
          imgs: [shuiImg, shuiActiveImg],
          type: 1
        },
        {
          name: "大气环境质量",
          value: data.airCount,
          state: false,
          imgs: [kongqiImg, kongqiActiveImg],
          type: 2
        },
        {
          name: "重点污染源",
          value: data.pollutantCount,
          state: false,
          imgs: [jianzhuwuImg, jianzhuwuActiveImg],
          type: 3
        },
        {
          name: "巡岗报警",
          value: data.patrolCount,
          state: false,
          imgs: [renyuanImg, renyuanActiveImg],
          type: 4
        }
      ];
      this.jointEnforcementMarker = JSON.parse(JSON.stringify(this.markerList));
    });
  }
  // 任务类型
  private taskType: any = "";
  // 监测告警
  private monitorAlarm: any = {};
  // 关联执行部门
  private departmentDetail: any = [];
  // 部门反馈
  private feedbackDetail: any = {};
  // 巡岗图片
  private patrolImg: any = null;
  // 任务详情
  private taskDetails: any = null;
  // 获取任务详情
  private getEnforcementTaskId(taskId: any) {
    getEnforcementTaskId(taskId).then((res: any) => {
      if (res.data.data) {
        this.monitorAlarm = {};
        if (res.data.data.alarmVO) {
          const str = res.data.data.alarmVO.alarmContent.split(" ");
          res.data.data.alarmVO.textOne = str[0];
          res.data.data.alarmVO.textTwo = str[str.length - 1];
          res.data.data.alarmVO.alarmTime = moment(
            new Date(res.data.data.alarmVO.alarmTime)
          ).format("MM-DD HH:mm");
          this.monitorAlarm = res.data.data.alarmVO;
        }
        this.airTypes = res.data.data.pollutantItemList;
        this.airType =
          res.data.data.pollutantItemList &&
          res.data.data.pollutantItemList.length !== 0
            ? res.data.data.pollutantItemList[0].pollutantCode
            : "";
        this.departmentDetail = res.data.data.list;
        this.checkImgType();
        this.feedbackDetail =
          res.data.data.list.length !== 0 ? res.data.data.list[0] : {};
        this.taskType = res.data.data.taskType;
        this.patrolImg = res.data.data.imageUrls;
        this.taskDetails = res.data.data;
        if (this.taskType == 3) {
          this.getTwentyFourHour();
        } else if (this.taskType == 1) {
          this.getMonitorItemDetailRecord(
            this.taskDetails.stationId,
            this.airType
          );
        } else if (this.taskType == 2) {
          this.getAqiTrend();
        }
        this.departmentState = "one";
      }
    });
  }
  // 水质实时监测数据btn选择
  private waterMonitorSelectChange(e: any): void {
    this.airType = e.target.value;
    let currentSelect!: any;
    this.airTypes.forEach((record: any, index: number) => {
      if (record.pollutantCode == e.target.value) {
        currentSelect = record;
      }
    });
    this.waterMonitor.unit = currentSelect.unit;
    // this.getMonitorItemDetailRecord(this.taskDetails.stationId, e.target.value);
  }
  // 24小时空气质趋势
  private getAqiTrend() {
    getAqiQuality(this.taskDetails.stationId, this.airType).then((res: any) => {
      const airData: any = {
        bottomList: [],
        dataList: [],
        unit: "",
        colorType: ""
      };
      for (const item in res.data.data) {
        airData.bottomList.push(item.replace("_", "") + "时");
        airData.dataList.push(res.data.data[item]);
        if (this.airType == "103") {
          airData.unit = "mg/m³";
        } else {
          airData.unit = "μg/m³";
        }
        if (this.airType == "100") {
          airData.colorType = "SO2";
        } else if (this.airType == "101") {
          airData.colorType = "NO2";
        } else if (this.airType == "102") {
          airData.colorType = "O3";
        } else if (this.airType == "103") {
          airData.colorType = "CO";
        } else if (this.airType == "104") {
          airData.colorType = "PM10";
        } else if (this.airType == "105") {
          airData.colorType = "PM25";
        }
      }
      this.airData = airData;
    });
  }
  // 获取监测项详情记录
  private getMonitorItemDetailRecord(
    stationId: string,
    itemCode: string
  ): void {
    getMonitorItemDetailRecord(stationId, itemCode).then(res => {
      if (res.data.data) {
        const { hourMonitorItems } = res.data.data;
        this.waterMonitor.bottomList = [];
        this.waterMonitor.dataList = [];
        // 24小时水质质量趋势
        if (hourMonitorItems.length > 0) {
          const sortedHourMonitorItems = hourMonitorItems.sort(
            (a: any, b: any) => {
              return a.hour - b.hour;
            }
          );
          if (sortedHourMonitorItems[0].alarmValue) {
            this.waterMonitor.warnValue = sortedHourMonitorItems[0].alarmValue;
          } else {
            this.waterMonitor.miniValue =
              sortedHourMonitorItems[0].alarmMinValue;
            this.waterMonitor.maxValue =
              sortedHourMonitorItems[0].alarmMaxValue;
          }
          sortedHourMonitorItems.forEach((record: any) => {
            this.waterMonitor.bottomList.push(record.hour + "时");
            this.waterMonitor.dataList.push(record.monitorValue || "-");
          });
        }
      }
    });
  }
  // 获取重点污染源
  private getTwentyFourHour() {
    getTwentyFourHour(this.taskDetails.stationId).then((res: any) => {
      // console.log("获取重点污染源", res.data.data);
      const airData: any = {
        bottomList: [],
        dataList: [],
        unit: "",
        colorType: ""
      };
      for (const item in res.data.data) {
        airData.bottomList.push(item.replace("_", "") + "时");
        airData.dataList.push(res.data.data[item]);
        if (this.airType == "103") {
          airData.unit = "mg/m³";
        } else {
          airData.unit = "μg/m³";
        }
        if (this.airType == "100") {
          airData.colorType = "SO2";
        } else if (this.airType == "101") {
          airData.colorType = "NO2";
        } else if (this.airType == "102") {
          airData.colorType = "O3";
        } else if (this.airType == "103") {
          airData.colorType = "CO";
        } else if (this.airType == "104") {
          airData.colorType = "PM10";
        } else if (this.airType == "105") {
          airData.colorType = "PM25";
        }
      }
      this.airData = airData;
    });
  }
}
</script>
