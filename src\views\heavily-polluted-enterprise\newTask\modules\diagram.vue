<template>
  <div style="padding: 50px" />
</template>

<script>
/* eslint-disable */
import go from 'gojs'

export default {
  name: 'Diagram',
  props: {
    modelData: {
      type: Object,
      planNodeType: Object,
      require: true,
      default: () => {}
    },
    type: {
      type: Number,
      default: -1
    }
  },
  watch: {
    modelData(val) {
      this.updateModel(val)
    },
    type(val) {
      this.updateModel(this.modelData)
    }
  },
  mounted() {
    const $ = go.GraphObject.make
    const self = this
    const myDiagram =
      $(go.Diagram, this.$el,
        {
          initialContentAlignment: go.Spot.Center, // 设置整个图表在容器中的位置 https://gojs.net/latest/api/symbols/Spot.html
          allowZoom: true,
          scale: 1.0,
          minScale: 0.7,
          maxScale: 1.2,
          'toolManager.mouseWheelBehavior': go.ToolManager.WheelZoom, // 启动滚轮缩放
          maxSelectionCount: 1, // users can select only one part at a time
          validCycle: go.Diagram.CycleDestinationTree, // make sure users can only create trees
          isEnabled: true, // 是否可拖拽，默认为是
          allowLink: false,
          allowMove: false,
          allowRelink: false,
          layout: $(go.TreeLayout, { angle: 90, arrangement: go.TreeLayout.ArrangementHorizontal }),
          ModelChanged(e) {
            self.$emit('model-changed', e)
          },
          ChangedSelection(e) {
            self.$emit('changed-selection', e)
          }
        })

    myDiagram.nodeTemplate =
      $(go.Node, 'Auto',
        {
          locationSpot: go.Spot.Center,
          locationObjectName: 'SHAPE',
          layerName: 'Background'
        },
        new go.Binding('location', 'loc', go.Point.parse),
        $(go.Shape, 'Rectangle',
          {
            margin: 0,
            name: 'SHAPE',
            stroke: '#F2F6FA',
            // set the port properties:
            portId: '',
            fromLinkable: true,
            toLinkable: true,
            cursor: 'pointer'
          }, new go.Binding('fill', 'data', (data) => {
            if (data.taskParentId) {
              return '#F2F6FA' // 85abe8
            } if (Number(data.permission) === 1) {
              return '#F2F6FA' // 9cd8e2
            } if (Number(data.permission) === 2) {
              return '#F2F6FA' // aedc96
            }
            return '#F2F6FA' // ef6d6d
          }).ofObject()),
        $(go.Panel, 'Horizontal',
          {
            padding: new go.Margin(0, 0, 0, 0)
          },
          $(go.Panel, 'Table',
            {
              maxSize: new go.Size(118, 68),
              margin: new go.Margin(0, 0, 0, 0),
              defaultAlignment: go.Spot.Left
            },
            // $(go.RowColumnDefinition, { column: 10, width: 2 }),
            $(go.TextBlock, this.labelTextStyle(),
              {
                row: 0,
                column: 1,
                editable: false,
                isMultiline: true,
                minSize: new go.Size(28, 68),
                font: '14px Segoe UI,sans-serif',
                textAlign: 'center',
                verticalAlignment: go.Spot.Center,
                margin: new go.Margin(0, 10, 0, 0)
              },
              new go.Binding('background', 'data',
                // using this conversion function
                (data) => {
                  // rgba(223, 233, 246, .6)
                  if (Number(data.permission) === 1) {
                    return 'rgba(237, 117, 0, 1)'
                  } if (data.completeStatus === undefined) {
                    return 'rgba(58, 121, 206, 1)' // '#9245e4'
                  } if (Number(data.completeStatus) === 0) {
                    return 'rgba(58, 121, 206, 1)' // '#13ce66'
                  } if (Number(data.completeStatus) === 2) {
                    return 'rgba(163, 180, 197, .6)' // '#3378f4'
                  } if (Number(data.completeStatus) === 1) {
                    return 'rgba(1, 166, 71, .6)'
                  }
                })
                // bound to this Panel itself, not to the Panel.data item
                .ofObject(),
              new go.Binding('text', 'data', (data) => {
                if (Number(data.permission) === 1 && Number(data.completeStatus) !== 2) {
                  return '抄\r\n送\r\n者'
                } if (data.completeStatus === undefined) {
                  return '进\r\n行\r\n中'
                } if (Number(data.completeStatus) === 0) {
                  return '进\r\n行\r\n中'
                } if (Number(data.completeStatus) === 1) {
                  return '已\r\n完\r\n成'
                }
                return '已\r\n退\r\n单'
              }).ofObject(),
              new go.Binding('stroke', 'data', (data) => {
                if (data.taskParentId) {
                  return '#fff' // '#9245e4'
                } if (Number(data.permission) === 2) {
                  return '#fff' // '#13ce66'
                } if (Number(data.permission) === 1) {
                  return '#fff' // '#3378f4'
                }
                return '#fff'
              }).ofObject())),
          $(go.Panel, 'Table',
            { isClipping: true, scale: 2, margin: new go.Margin(0, 10, 0, 11) },
            $(go.Shape, 'Circle', { width: 16, strokeWidth: 0 }),
            $(go.Picture, {
              name: 'Picture',
              desiredSize: new go.Size(18, 18)
            },
            new go.Binding('source', 'avatar', (avatar) => self.transforPicture(avatar)))),
          // define the panel where the text will appear
          $(go.Panel, 'Table',
            {
              maxSize: new go.Size(200, 999),
              margin: new go.Margin(0, 10, 0, 3),
              defaultAlignment: go.Spot.Left
            },
            $(go.RowColumnDefinition, { column: 2, width: 8 }),
            $(go.TextBlock, this.textStyle(),
              {
                row: 0,
                column: 0,
                columnSpan: 8,
                font: '10pt Segoe UI,sans-serif',
                editable: false,
                isMultiline: false,
                minSize: new go.Size(10, 14),
                margin: new go.Margin(0, 0, 0, 3)
              },
              new go.Binding('text', 'name')),
            $(go.TextBlock, this.textStyle(),
              {
                row: 0,
                column: 0,
                columnSpan: 8,
                font: '16px Segoe UI,sans-serif',
                editable: false,
                isMultiline: false,
                minSize: new go.Size(10, 16),
                margin: new go.Margin(0, 20, 0, 3)
              },
              new go.Binding('text', 'taskNodeName')),), // end Table Panel
          $('TreeExpanderButton', { margin: new go.Margin(0, 12, 0, 0) }))
        // $(go.Shape,
        //   {
        //     fill: 'white', strokeWidth: 0,
        //     portId: '', fromLinkable: true, toLinkable: true, cursor: 'pointer'
        //   },
        //   new go.Binding('fill', 'color')
        // ),
        // $(go.TextBlock,
        //   { margin: 8, editable: true },
        //   new go.Binding('text').makeTwoWay()
        // )
      )

    myDiagram.linkTemplate =
      $(go.Link, go.Link.Orthogonal,
        {
          corner: 5, relinkableFrom: true, relinkableTo: true, selectable: false
        },
        $(go.Shape, { strokeWidth: 2 }, new go.Binding('stroke', 'toNode', (n) =>
          // if (n.data.taskParentId) {
          //   return '#9245e4'
          // } else if (n.data.planNodeType === '2') {
          //   return '#13ce66'
          // } else if (n.data.planNodeType === '1') {
          //   return '#3378f4'
          // }
          '#c0dac0').ofObject()))
    // myDiagram.commandHandler.doKeyDown = function() {
    //   var e = myDiagram.lastInput
    //   var control = e.control || e.meta
    //   var key = e.key
    //   // 取消Ctrl+Z/Y，Del/Backspace删除键的命令关联:
    //   if (control && (key === 'Z' || key === 'Y')) return
    //   if (key === 'Del' || key === 'Backspace') return
    //
    //   go.CommandHandler.protoplanNodeType.doKeyDown.call(this)
    // }
    myDiagram.addDiagramListener('ObjectSingleClicked', (e, obj) => {
      self.$emit('click', e)
    })
    myDiagram.addDiagramListener('ObjectContextClicked', (e, obj) => {
      //  document.oncontextmenu = function () {
      //   return false;
      // }
      const node = e.diagram.selection.first()
      self.showPoint(node.part.location)
    })
    this.diagram = myDiagram

    this.updateModel(this.modelData)
  },
  methods: {
    showPoint(loc) {
      const docloc = this.diagram.transformDocToView(loc)
      this.$emit('right', { loc, docloc })
    },
    model() { return this.diagram.model },
    updateModel(val) {
      // No GoJS transaction permitted when replacing Diagram.model.
      if (val instanceof go.Model) {
        this.diagram.model = val
      } else {
        const m = new go.GraphLinksModel()
        if (val) {
          for (const p in val) {
            m[p] = val[p]
          }
        }
        this.diagram.model = m
      }
    },
    updateDiagramFromData() {
      this.diagram.startTransaction()
      // This is very general but very inefficient.
      // It would be better to modify the diagramData data by calling
      // Model.setDataProperty or Model.addNodeData, et al.
      this.diagram.updateAllRelationshipsFromData()
      this.diagram.updateAllTargetBindings()
      this.diagram.commitTransaction('updated')
    },
    textStyle() {
      return { font: '10pt  Segoe UI,sans-serif', stroke: 'black' }
    },
    labelTextStyle() {
      return { font: '10pt  Segoe UI,sans-serif', stroke: 'white' }
    },
    // This converter is used by the Picture.
    transforPicture(avatar) {
      if (avatar) {
        return avatar
      }
      return 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'
    }
  }
}
</script>

<style scoped>

</style>
