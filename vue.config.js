"use strict";
// eslint-disable-next-line @typescript-eslint/no-var-requires
const path = require("path");

// eslint-disable-next-line @typescript-eslint/no-var-requires
const webpack = require("webpack");
// eslint-disable-next-line @typescript-eslint/no-var-requires
const CopyWebpackPlugin = require("copy-webpack-plugin");
// eslint-disable-next-line @typescript-eslint/no-var-requires
const CompressionPlugin = require("compression-webpack-plugin");
const productionGzipExtensions = ["js", "css"];

// eslint-disable-next-line @typescript-eslint/no-var-requires
const name = "金牛区智慧环保平台";

function resolve(dir) {
  return path.join(__dirname, dir);
}

module.exports = {
  lintOnSave: false,
  publicPath: "/",
  devServer: {
    port: 9965,
    open: true,
    overlay: {
      warnings: false,
      errors: true
    },
    progress: false,
    compress: true,
    proxy: {
      "/": {
        target: process.env.VUE_APP_BASE_API,
        changeOrigin: true
        // pathRewrite: {
        //   "/api": ""
        // }
      }
    }
  },
  configureWebpack: {
    // provide the app's title in webpack's name field, so that
    // it can be accessed in index.html to inject the correct title.
    // name: name,
    // name: globalSettings.title,
    devtool: "source-map",
    resolve: {
      alias: {
        "@": resolve("src"),
        flvjs: "flv.js/dist/flv.js"
      }
    },
    externals: {
      BMap: "BMap",
      AMap: "AMap",
      AMapUI: "AMapUI",
      BMapLib: "Heatmap"
    },
    plugins: [
      new webpack.ProvidePlugin({
        flvjs: "flvjs"
      }),
      new CopyWebpackPlugin([
        {
          from:
            "node_modules/@liveqing/liveplayer/dist/component/crossdomain.xml"
        },
        {
          from:
            "node_modules/@liveqing/liveplayer/dist/component/liveplayer.swf"
        },
        {
          from:
            "node_modules/@liveqing/liveplayer/dist/component/liveplayer-lib.min.js",
          to: "static/js/"
        }
      ]),
      // 下面是下载的插件的配置
      new CompressionPlugin({
        algorithm: "gzip",
        test: new RegExp("\\.(" + productionGzipExtensions.join("|") + ")$"),
        threshold: 10240,
        minRatio: 0.8
      }),
      new webpack.optimize.LimitChunkCountPlugin({
        maxChunks: 5,
        minChunkSize: 100
      })
    ]
  },
  chainWebpack(config) {
    config.set("name", name);

    // set preserveWhitespace
    config.module
      .rule("vue")
      .use("vue-loader")
      .loader("vue-loader")
      .tap(options => {
        options.compilerOptions.preserveWhitespace = true;
        return options;
      })
      .end();

    config
      // https://webpack.js.org/configuration/devtool/#development
      .when(process.env.NODE_ENV === "development", config =>
        config.devtool("cheap-source-map")
      );

    config.when(process.env.NODE_ENV !== "development", config => {
      config.optimization.splitChunks({
        chunks: "initial",
        cacheGroups: {
          libs: {
            name: "chunk-libs",
            test: /[\\/]node_modules[\\/]/,
            priority: 10,
            minSize: 2048,
            chunks: "initial" // only package third parties that are initially dependent
          },
          antDesignVue: {
            name: "chunk-antDesignVue", // split elementUI into a single package
            priority: 20, // the weight needs to be larger than libs and app or it will be packaged into libs or app
            test: /[\\/]node_modules[\\/]_?ant-design-vue(.*)/ // in order to adapt to cnpm
          },
          commons: {
            name: "chunk-commons",
            test: resolve("src/components"), // can customize your rules
            minChunks: 2, //  minimum common number
            priority: 5,
            minSize: 2048,
            reuseExistingChunk: true
          }
        }
      });
      // config.optimization.minimizer
      config.optimization.runtimeChunk("single");
    });
  }
};
