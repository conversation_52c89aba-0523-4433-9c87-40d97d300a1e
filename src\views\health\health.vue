<template>
  <div class="content">
    <div id="mapContainer" ref="mapContainer"></div>
    <aside class="aside">
      <transition name="el-fade-in">
        <WarterHealth v-if="tabIndex === 1" :map="map" :AMap="AMap" />
        <AirHealth v-if="tabIndex === 2" :map="map" :AMap="AMap" />
        <NoiseHealth v-if="tabIndex === 3" :map="map" :AMap="AMap" />
      </transition>
    </aside>
    <div class="tabs">
      <div
        class="tab"
        :class="tabIndex === index ? 'active' : ''"
        v-for="(item, index) in tabs"
        @click="tabIndex = index"
        :key="item.value"
      >
        {{ item.name }}
      </div>
    </div>
  </div>
</template>
<script>
import AMapLoader from "@amap/amap-jsapi-loader";
import WarterHealth from "./water-health.vue";
import NoiseHealth from "./noise-health.vue";
import AirHealth from "./airHealth/air-health.vue";

export default {
  name: "WaterHealth",
  components: {
    WarterHealth,
    NoiseHealth,
    AirHealth,
  },
  data() {
    return {
      map: null,
      AMap: null,
      tabs: [
        {
          name: "企业环境\n健康关联分析",
          value: 0,
        },
        {
          name: "水环境\n健康分析",
          value: 1,
        },
        {
          name: "大气环境\n健康分析",
          value: 2,
        },
        {
          name: "噪声环境\n健康分析",
          value: 3,
        },
      ],
      tabIndex: 2,
    };
  },
  created() {
    AMapLoader["reset"]();
  },
  mounted() {
    // load 加载
    AMapLoader.load({
      key: "777fec7ef3cc29281d60ae900fa33925", // 申请好的Web端开发者Key，首次调用 load 时必填
      version: "2.0", // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
      plugins: ["AMap.GeoJSON"],
    })
      .then((amaps) => {
        AMap = amaps;
        this.map = new AMap.Map("mapContainer", {
          resizeEnable: true, //是否监控地图容器尺寸变化
          center: [104.036169, 30.699949],
          zoom: 13.6,
          mapStyle: "amap://styles/0739a6632d849ae1b5e6f1c52f7d12c3",
          viewMode: "3D",
          pitch: 40,
        });
      })
      .catch((e) => {
        console.log(e);
      });
  },
  methods: {},
};
</script>
<style lang="less" scoped>
.content {
  position: relative;
}
::v-deep .river-content {
  width: 172px;
  height: 142px;
  font-size: 12px;
  color: #21edff;
  background: url(../../assets/health/riverName.png);
  padding: 15px;
}
#mapContainer {
  width: 100%;
  height: calc(100vh - 100px);
}
.tabs {
  position: fixed;
  bottom: 60px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
  .tab {
    width: 194px;
    height: 62px;
    background: url(../../assets/health/<EMAIL>);
    background-size: 100% 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    font-family: YouSheBiaoTiHei;
    font-size: 18px;
    color: #b9cfed;
    cursor: pointer;
    white-space: pre-line;
    line-height: 18px;
    text-align: center;

    &:hover {
      opacity: 0.9;
    }
  }
  .active {
    background: url(../../assets/health/<EMAIL>);
    background-size: 100% 100%;
    color: #fff;
  }
}
.aside {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
}
</style>
