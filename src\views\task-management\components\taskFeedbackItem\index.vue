<template>
  <div :class="excutor.highlight ? 'highlight' : ''">
    <bigBox
      :hiddenbtom="hiddenbtom"
      :person="person"
      :time="time || ''"
    >
      <contentBox :content="excutor.remark" :imgarr="imgarr"></contentBox>
    </bigBox>
  </div>
</template>


<script>
import moment from 'moment'
  import bigBox from './bigBox.vue'
  import contentBox from './content.vue'
  export default {
    components:{
      bigBox,
      contentBox
    },
    props:{
      hiddenbtom: {
        type: Boolean,
        default: false
      },
      excutor: {
        type: Object,
        default: () => { return {} }
      }
    },
    data() {
      return {}
    },
    computed:{
      person() {
        if(this.excutor.departmentName && this.excutor.userName){
          return `${this.excutor.departmentName}-${this.excutor.userName}`
        }else if(this.excutor.userName){
          return `${this.excutor.userName}`
        } else {
          return `暂无任务反馈`
        }
      },
      imgarr(){
        if (this.excutor.taskAnnexList) {
          const newArr = this.excutor.taskAnnexList.sort((a, b) => {
            if(!['png', 'jpg', 'jpeg', 'gif'].indexOf(a.format)){
              return -1
            }else{
              return 1
            }
          })
          const arr = newArr.map((e) => e.annexUrl)
          return arr
        }
        return []
      },
      time() {
        if (this.excutor.time) {
          const time = this.excutor.time ? moment(this.excutor.time).format('YYYY-MM-DD HH:mm') : ''
          return time
        }
        return ''
      }
    }
  }
</script>

<style scoped>
.highlight {
  background-color: rgba(4, 30, 66, 0.5);
}
</style>
