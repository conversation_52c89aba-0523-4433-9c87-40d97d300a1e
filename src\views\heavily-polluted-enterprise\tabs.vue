<style scoped lang="less">
.main {
  width: 5rem;
  display: flex;
  justify-content: space-between;
  margin: 0 auto;
  > div {
    font-size: 0.16rem;
    color: #02aafa;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    cursor: pointer;
    > img {
      width: 1.06rem;
      height: 0.84rem;
      border-radius: 50%;
    }
  }
  > .active {
    color: #00eaff;
    > img {
      transform: scale(1.1);
      position: relative;
      top: -0.12rem;
    }
  }
}
</style>
<template>
  <div class="main">
    <div
      class="construction-site"
      :class="{ active: index === 0 }"
      @click="changeIndex(0)"
    >
      <img v-if="index !== 0" src="../../assets/noise/<EMAIL>" alt="" />
      <img v-else src="../../assets/noise/<EMAIL>" alt="" />
      <div>大气污染</div>
    </div>
    <div
      class="printing-enterprise"
      :class="{ active: index === 9 }"
      @click="changeIndex(9)"
    >
      <img v-if="index !== 9" src="../../assets/noise/<EMAIL>" alt="" />
      <img v-else src="../../assets/noise/<EMAIL>" alt="" />
      <div>水污染</div>
    </div>
    <div
      class="printing-enterprise"
      :class="{ active: index === 8 }"
      @click="changeIndex(8)"
    >
      <img v-if="index !== 8" src="../../assets/noise/<EMAIL>" alt="" />
      <img v-else src="../../assets/noise/<EMAIL>" alt="" />
      <div>危废监管</div>
    </div>
    <div
      class="car-enterprise"
      :class="{ active: index === 11 }"
      @click="changeIndex(11)"
    >
      <img v-if="index !== 11" src="../../assets/noise/<EMAIL>" alt="" />
      <img v-else src="../../assets/noise/<EMAIL>" alt="" />
      <div>综合分析</div>
    </div>
    <!-- <div
      class="catering-enterprises"
      :class="{ active: index === 3 }"
      @click="changeIndex(3)"
    >
      <img v-if="index === 3" src="../../assets/noise/<EMAIL>" />
      <img v-else src="../../assets/heavily/<EMAIL>" />
      <div>餐饮油烟监管</div>
    </div> -->
    <!-- <div
      class="gas-station"
      :class="{ active: index === 4 }"
      @click="changeIndex(4)"
    >
      <img v-if="index !== 4" src="../../assets/heavily/<EMAIL>" alt="" />
      <img
              v-else
              src="../../assets/heavily/<EMAIL>"
              alt=""
      />
      <div>加油站油气回收</div>
    </div> -->
    <!-- <div
      class="enterprises-institutions"
      :class="{ active: index === 6 }"
      @click="changeIndex(6)"
    >
      <img v-if="index !== 6" src="../../assets/noise/<EMAIL>" alt="" />
      <img v-else src="../../assets/noise/<EMAIL>" alt="" />
      <div>噪声监测</div>
    </div>
    <div
      class="enterprises-institutions"
      :class="{ active: index === 7 }"
      @click="changeIndex(7)"
    >
      <img v-if="index !== 7" src="../../assets/electricity/<EMAIL>" alt="" />
      <img v-else src="../../assets/electricity/<EMAIL>" alt="" />
      <div>电量监测</div>
    </div>
    <div
      class="enterprises-institutions"
      :class="{ active: index === 8 }"
      @click="changeIndex(8)"
    >
      <img v-if="index !== 8" src="../../assets/solid-waste/<EMAIL>" alt="" />
      <img v-else src="../../assets/solid-waste/<EMAIL>" alt="" />
      <div>固废监测</div>
    </div>
    <div
      class="enterprises-institutions"
      :class="{ active: index === 9 }"
      @click="changeIndex(9)"
    >
      <img v-if="index !== 9" src="../../assets/heavy-corrupt/<EMAIL>" alt="" />
      <img v-else src="../../assets/heavy-corrupt/<EMAIL>" alt="" />
      <div>排污监测</div>
    </div>
    <div
      class="enterprises-institutions"
      :class="{ active: index === 5 }"
      @click="changeIndex(5)"
    >
      <img v-if="index !== 5" src="../../assets/heavily/<EMAIL>" alt="" />
      <img v-else src="../../assets/heavily/<EMAIL>" alt="" />
      <div>企事业单位</div>
    </div>
    <div
      class="enterprises-institutions"
      :class="{ active: index === 99 }"
      @click="changeIndex(99)"
    >
      <img v-if="index !== 99" src="../../assets/video/-s-zsjc.png" alt="" />
      <img v-else src="../../assets/video/-s-zsjc_xz.png" alt="" />
      <div>视频监控</div>
    </div> -->
  </div>
</template>

<script lang="ts">
// import Bus from '@/utils/bus'
// export default {
//   name: 'tabs',
//   data() {
//     return {
//       index: 0
//     }
//   },
//   methods: {
//     changeIndex(index) {
//       this.index = index
//       this.$emit('postActiveIndex', index);
//       Bus.$emit('index', index)
//     }
//   }
// }
/* eslint-disable @typescript-eslint/ban-ts-ignore */
//@ts-ignore
import Bus from "@/utils/bus";
import { Vue, Component } from "vue-property-decorator";
@Component({
  name: "tabs",
})
export default class extends Vue {
  private index = 0;
  private changeIndex(index: number) {
    if (this.index === index) return;
    this.index = index;
    if (index === 99) {
      this.$router.push("/pollutionVideo");
      return;
    }
    this.$emit("postActiveIndex", index);
    // console.log(index, 'index')
    // Bus.$emit('index', index)
  }
  // mounted() {
  //   this.index = this.activeIndex
  // }
  // @Prop({ default: 0 }) private activeIndex!: number;
  // private onIndex(newValue: any, oldValue: any) {
  //   console.log(this.index, 'gg')
  //   this.$emit("postActiveIndex", this.index);
  //   // if (((oldValue == 0) && (newValue==5)) || ((oldValue == 5) && (newValue==0))) {
  //   //   Bus.$emit('index', this.activeIndex)
  //   // } else if (newValue && newValue !== 0 && newValue !== 5) {
  //   //   this.$router.push({
  //   //     path: "/otherPages",
  //   //     query: { activeIndex: newValue }
  //   //   });
  //   //   this.$emit("postActiveIndex", Number(this.activeIndex));
  //   // }
  //   // else {
  //   //   this.$router.push({name: 'heavilyPollutedEnterprise', params: {type: newValue }});
  //   // }
  // }
}
</script>
