<template>
  <div
    :id="id"
    :class="className"
    :style="{ height: height, width: width }"
  ></div>
</template>

<script lang="ts">
import * as echarts from 'echarts'
import { Component, Prop, Watch } from "vue-property-decorator";
import { mixins } from "vue-class-component";
import ResizeMixin from "./mixins/resize";
interface TaskData {
  type: number;
  showNumber: number;
  name: string;
  number: number;
  bgColor?: string;
}
@Component({
  name: "DashboardChartAir"
})
export default class extends mixins(ResizeMixin) {
  @Prop({ default: "chart" }) private className!: string;
  @Prop({ default: "chart" }) private id!: string;
  @Prop({ default: "200px" }) private width!: string;
  @Prop({ default: "200px" }) private height!: string;
  @Prop({ required: true }) private propData!: any;
  @Watch("propData", { immediate: true, deep: true })
  public onAirType(newValue: any, oldValue: any) {
    if (this.propData && newValue) {
      this.$nextTick(() => {
        this.initChart();
      });
    }
  }
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
  }

  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.clear();
    this.chart.dispose();
    this.chart = null;
  }

  private initChart() {

    if(this.chart === null || this.chart === undefined) {
      this.chart = echarts.init(
        document.getElementById(this.id) as HTMLDivElement
      );
    }
    const bgColors: any = [
      [0.1, "rgb(0,255,0)"],
      [0.2, "rgb(255,255,0)"],
      [0.3, "rgb(255,126,0)"],
      [0.4, "rgb(255,0,0)"],
      [0.6, "rgb(153,0,76)"],
      [1, "rgb(126,0,35)"]
    ];
    // const bgColors: any = [[1, "#3262a9"]];
    // bgColors.unshift([
    //   Number(this.propData.number) / 500,
    //   this.propData.bgColor
    // ]);
    this.chart.setOption({
      title: {
        text: "AQI",
        textStyle: {
          color: "#fff",
          fontSize: 14
        },
        top: 10,
        left: "center"
      },
      series: [
        {
          startAngle: 215,
          endAngle: -35,
          min: 0,
          max: 300,
          radius: "85%",
          splitNumber: 6,
          type: "gauge",
          center: ["50%", "68%"],
          axisLine: {
            // 坐标轴线
            lineStyle: {
              // color: [
              //   [
              //     this.propData.showNumber,
              //     new echarts.graphic.LinearGradient(1, 0, 0, 1, [
              //       {
              //         offset: 0,
              //         color: "#FFDB5C"
              //       },
              //       {
              //         offset: 0.5,
              //         color: "#A5E51B"
              //       },
              //       {
              //         offset: 1,
              //         color: "#57DC86"
              //       }
              //     ])
              //   ],
              //   [1, "#083f8b"]
              // ],
              // 属性lineStyle控制线条样式
              color: [
                [0.167, "#0BC026"],
                [0.334, "#DBEC25"],
                [0.5, "#FFA200"],
                [0.667, "#E01226"],
                [1, "#891267"]
                // [0.835, "#891267"],
                // [1, "#711244"]
              ],
              // color: bgColors,
              width: 10
            }
          },
          axisLabel: {
            show: true,
            formatter: function(e: any) {
              switch (e + "") {
                // case "50":
                //   return "优";
                // case "100":
                //   return "良";
                // case "150":
                //   return "轻度污染";
                // case "200":
                //   return "中度污染";
                // case "300":
                //   return "重度污染";
                // case "500":
                //   return "严重污染";
                // default:
                //   return e;
                // case "50":
                //   return "";
                // case "150":
                //   return "";
                // case "250":
                //   return "";
                // case "350":
                //   return "";
                // case "450":
                //   return "";
                default:
                  return e;
              }
            },
            // 坐标轴小标记
            textStyle: {
              // 属性lineStyle控制线条样式
              color: "#fff",
              shadowBlur: 5
            },
            fontSize: 8
          },
          axisTick: {
            show: true,
            length: 2
          },
          splitLine: {
            show: true,
            length: 7,
            lineStyle: {
              width: 1
            }
          },
          pointer: {
            show: true,
            width: 5
          },
          itemStyle: {
            color: "#FF0000"
          },
          title: {
            show: true, // 是否显示标题,默认 true。
            offsetCenter: [0, "65%"], //相对于仪表盘中心的偏移位置，数组第一项是水平方向的偏移，第二项是垂直方向的偏移。可以是绝对的数值，也可以是相对于仪表盘半径的百分比。
            // color:
            //   this.propData.aqiText === "良" || this.propData.aqiText === "优"
            //     ? "#333"
            //     : "#fff",
            color: this.propData.bgColor,
            width: "50",
            height: "20",
            padding: [4, 8],
            borderRadius: 8,
            fontSize: 14
            // backgroundColor: this.propData.bgColor
          },
          detail: {
            fontSize: 20,
            color: "#fff",
            offsetCenter: [0, "20%"]
          },
          data: [{ value: this.propData.number, name: this.propData.aqiText }]
        }
      ]
    } as any);
  }
}
</script>
