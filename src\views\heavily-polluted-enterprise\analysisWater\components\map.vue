<template>
  <div class="analysisMap-container">
    <div ref="analysisMapContainer" class="analysisMapContainer"></div>
    <div class="btns-container" ref="btnsContainer" @click="handleClickDraw">
      <img src="@/assets/heavily-polluted-enterprise/<EMAIL>" alt="" />
    </div>
  </div>
</template>

<script>
// import AMap from 'AMap'
import AMapLoader from '@amap/amap-jsapi-loader'
import jinniuArea from '@/assets/map-geojson/jinniu_area.json'
import noiseClose from '@/assets/noise/<EMAIL>'; // 噪声弹窗关闭
import QYCB from '@/assets/heavily-polluted-enterprise/<EMAIL>'  // 企业超标
import QYZC from '@/assets/heavily-polluted-enterprise/<EMAIL>'  // 企业正常
import QYLX from '@/assets/heavily-polluted-enterprise/<EMAIL>'  // 企业离线
import electricityBg from '@/assets/noise/<EMAIL>'; // 用电弹窗背景
import districtRiver from "@/assets/jinniu-river4.png";
import pkzc from '@/assets/outlet/<EMAIL>'
import pkyc from '@/assets/outlet/<EMAIL>'
import pklx from '@/assets/outlet/<EMAIL>'
import phone from '@/assets/analysis/<EMAIL>'
let AMap
export default {
  name: '',
  data() {
    return {
      // 地图配置
      mapConfig: {
        zoom: 13.8,
        zooms: [12, 20],
        center: [104.061111, 30.714222],
        mapStyle: 'amap://styles/0739a6632d849ae1b5e6f1c52f7d12c3',
        viewMode: '2D',
        zoomEnable: true,
        dragEnable: true,
        // rotation: 45,
        // pitch: 30, // 地图倾斜
      },
      mapIcon: {
        '123': require('@/assets/map_icon/icon_2.png'),
        'other': require('@/assets/map_icon/icon_5.png'),
        'offline': require('@/assets/map_icon/icon_1.png')
      },
      currentMarker: {},
      stationMarkerList: [],
      infoWindow: null,
      AMap: null,
      circleMap: null,
      companyMarkerList: [],
      myCanvas: null,
      myContext: null,
      canvasRadius: 0,
      CanvasLayer: null,
      animFrameId: 0,
      lineLayer: null,
      rectLayerList: [],
      mouseTool: null,
      overlays: null,
      drainMarkerList: [],
      isDraw: false // 用于绘制区域的时候点击到按钮，区域不清空
    }
  },
  created() {
    AMapLoader.reset()
    window.closeCommonInfoWindow = this.closeCommonInfoWindow
  },
  mounted() {
    // // load 加载
    AMapLoader.load({
      "key": "777fec7ef3cc29281d60ae900fa33925",              // 申请好的Web端开发者Key，首次调用 load 时必填
      "version": "1.4.15",   // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
      "plugins": ['AMap.DistrictSearch','AMap.Heatmap','AMap.ControlBar','AMap.Object3DLayer','Map3D','AMap.Geocoder','AMap.CircleMarker','AMap.MouseTool'],           // 需要使用的的插件列表，如比例尺'AMap.Scale'等
      "AMapUI": {             // 是否加载 AMapUI，缺省不加载
        "version": '1.0',   // AMapUI 版本
        // "plugins":['overlay/SimpleMarker'],       // 需要加载的 AMapUI ui插件
      },
      "Loca":{                // 是否加载 Loca， 缺省不加载
        "version": '1.3.2'  // Loca 版本
      },
    }).then((amaps)=>{
      AMap = amaps
      this.initMap()
    }).catch(e => {
      console.log(e);
    })
  },
  props: {
    markerList: {
        type: Array,
        default: () => []
    },
    distance: {
      type: Number | String,
      default: 500
    },
    stationList: {
      type: Array,
      default: () => []
    },
    drainList: {
      type: Array,
      default: () => []
    },
    currentCompany: {
      type: Object,
      default: () => {}
    }
  },
  methods: {
    // 初始化地图
    initMap() {
      this.$nextTick(() => {
        const map = new AMap.Map(this.$refs.analysisMapContainer, this.mapConfig);
        this.AMap = map;
        map.on('click', (e) => {
            console.log('点击地图', e)
        })
        // this.creatGeojson(map);
        this.dynamicRivderLayer()
        this.initMouseTool()
        if(this.markerList.length) {
          this.$nextTick(() => {
            this.setMarkers()
            this.setDrainMarkers()
          })
        }
      })
    },
    dynamicRivderLayer() {
    const bounds = new AMap.Bounds([103.9363,30.662957], [104.160597,30.807361]);
    const imageLayer = new AMap.ImageLayer({
      url: districtRiver, // districtRiver
      bounds: bounds,
      zooms: [3, 18],
      opacity: 1,
      zIndex: 2,
    });
    imageLayer.setMap(this.AMap);
    // imageLayer.on('click', (e)=> {
    //   console.log(e)
    // })
  },
    // 区域覆盖物点击事件
    handleClick(e){
      console.log('区域点击事件')
    },
    //  空气等级过滤
    waterLevelFilter(waterLevel){
      if(!waterLevel) return this.mapIcon['offline']
      let icon = this.mapIcon['123']
      if(waterLevel == 'Ⅰ' || waterLevel == 'Ⅱ' || waterLevel == 'Ⅲ') icon = this.mapIcon['123']
      else icon = this.mapIcon['other']
      return icon
    },
    // 添加站点icon
    setMarkers() {
      if(this.stationMarkerList.length) {
        this.AMap.remove(this.stationMarkerList)
        this.stationMarkerList = []
      }
      this.stationMarkerList = []
      this.markerList.forEach((item, index) => {
          this.addMaker(item)
      })
      if(this.markerList.length) {
        this.currentMarker = this.markerList[0]
        this.setINfoWindow(this.markerList[0])
        this.setCanvas(this.markerList[0])
        this.AMap.setCenter([this.markerList[0].lng, this.markerList[0].lat])
        this.$emit('handleChangeStation', this.markerList[0])
      }
    },
    addMaker(item) {
      console.log('item', item)
      let image = this.waterLevelFilter(item.waterType)
      if(!item.online) image = this.mapIcon['offline']
      const icon = new AMap.Icon({
        // 图标尺寸
        size: new AMap.Size(17, 17),
        // 图标的取图地址
        image: image,
        // 图标所用图片大小
        imageSize: new AMap.Size(17, 17),
        // 图标取图偏移量
        imageOffset: new AMap.Pixel(0, 0)
      })
      const markerOptions = {
        map: this.AMap,
        position: new AMap.LngLat(item.lng, item.lat),
        offset: new AMap.Pixel(-8.5, -8.5),
        icon: icon,
        zIndex: 9,
        data: item
      }
      let markerIcon = new AMap.Marker(markerOptions)
      // AMap.event.addListener(markerIcon, ()=> {
      //     this.handleClickMarker
      // })
      markerIcon.on('click', this.handleClickMarker)
      this.stationMarkerList.push(markerIcon)
    },
    // 添加排口
    setDrainMarkers() {
      if(this.drainMarkerList && this.drainMarkerList.length) {
        this.AMap.remove(this.drainMarkerList)
      }
      this.drainList.forEach(item => {
        const icon = new AMap.Icon({
          // 图标尺寸
          size: !item.online ? new AMap.Size(47, 70) : item.isAlarm ? new AMap.Size(58, 67) : new AMap.Size(58, 67),
          // 图标的取图地址
          image: !item.online ? pklx : item.isAlarm ? pkyc : pkzc,
          // 图标所用图片大小
          imageSize: !item.online ? new AMap.Size(47, 70) : item.isAlarm ? new AMap.Size(58, 67) : new AMap.Size(58, 67),
          // 图标取图偏移量
          imageOffset: new AMap.Pixel(0, 0)
        })
        const markerOptions = {
          map: this.AMap,
          position: new AMap.LngLat(item.lng, item.lat),
          offset: new AMap.Pixel(-27, -61),
          icon: icon,
          zIndex: 9,
          data: item
        }
        let markerIcon = new AMap.Marker(markerOptions)
        markerIcon.on('click', (e) => {
          const { data } = e.target.w
          this.$emit('handleClickCompanyMarker', data)
        })
        this.drainMarkerList.push(markerIcon)
      })

    },
     // 添加除了排口，排污企业其他的弹窗
    setcommonInfoWindow(data) {
      const stationTypeList = {
        1: '印刷企业',
        2:'环保用电',
        3: '危废回收',
        4: '工地扬尘',
        5: '餐饮油烟',
        6: '印刷企业',
        7: '汽修企业',
        8: '排污企业',
        9: '排口站点',
        10: '水质站点',
        11: '空气站点',
        12: '雷达站点'
      }
      let infoWindowContent = `
          <div class="infoWindow" style="background: url('${electricityBg}') no-repeat;">
            <span class="title" style="">企业信息</span>
            <img class="close" src="${noiseClose}" style="" onclick="closeCommonInfoWindow()"/>
            <div class="content">
                <div class="flex-container">
                  <div class="item" style="width: 170px">
                    <span class="label">企业名称：</span>
                    <span class="value" title="${data.stationName}" style="white-space: nowrap;overflow: hidden;text-overflow: ellipsis;">${data.stationName || '--'}</span>
                  </div>
                  <div class="item typeBg">
                   商业污水
                  </div>
                </div>
                <div class="item">
                    <span class="label">排户类型：</span>
                    <span class="value">${stationTypeList[data.type] || '--'}</span>
                </div>
                <div class="item">
                    <span class="label">详细地址：</span>
                    <span class="value">${data.address || '--'}</span>
                </div>
                <div style="display: flex; align-items: center">
                  <div class="item">
                    <span class="label">联系人：</span>
                    <span class="value">${data.contact || '--'}</span>
                </div>
                <div class="item" style="margin-left: 20px;margin-right: 10px;">
                    <span class="label" style="margin-top: -2px;"><img src="${phone}" alt=""></span>
                    <span class="value">${data.contactPhone || '--'}</span>
                </div>
              </div>
            </div>
          </div>
          `
        if (this.infoWindow) {
        this.infoWindow.setContent(infoWindowContent)
        this.infoWindow.setPosition([data.lng, data.lat]) // 更新点标记位置
        this.infoWindow.setMap(this.AMap)
        this.infoWindow.setOffset(new AMap.Pixel(-1, -10))
      } else {
        // 创建一个自定义内容的 infowindow 实例
        this.infoWindow = new AMap.InfoWindow({
          position: [data.lng, data.lat],
          offset: new AMap.Pixel(-1, 100),
          content: infoWindowContent,
          zIndex: 10
        })
        this.infoWindow.setMap(this.AMap)
        // this.infoWindow.on('close', this.handlecloseInfoWindow2)
      }
    },
    closeCommonInfoWindow() {
      if (this.AMap) {
        this.AMap.clearInfoWindow()
      }
    },
    // 计算颜色 污染程度
    computedTextColor(data) {
        let pollutionType = `${data.waterType || '--'}类`
        let currentColor = '#01FF01'
        currentColor = data.waterType == 'Ⅰ' || data.waterType == 'Ⅱ' || data.waterType == 'Ⅲ'
          ? 'rgb(0, 255, 0)'
          : 'rgb(255, 0, 0)'
        if(!data.online) currentColor = 'gary'
        return { pollutionType, currentColor }
    },
    // 添加弹窗
    setINfoWindow(data) {
      const { pollutionType, currentColor } = this.computedTextColor(data)
      let infoWindowContent = `
          <div class="infoWindow" style="background: url('${electricityBg}') no-repeat;">
            <span class="title" style="">站点信息</span>
            <img class="close" src="${noiseClose}" style="" onclick="closeCommonInfoWindow()"/>
            <div class="content">
                <div class="time">监测时间：${data.updateTime ? data.updateTime.substring(0, 16) : "--"}</div>
                <div class="item">
                    <span class="label">站点名称：</span>
                    <span class="value">${data.stationName || '--'}</span>
                </div>
                <div class="item">
                    <span class="label">WQI：</span>
                    <span class="value" style="color:#ffffff"><b class="number" style="color: ${currentColor}">${(data.wqi || data.wqi === 0 || data.wqi === '0') ? data.wqi : '--'}</b><span style="background: #0E5EAF; display: inline-block;padding: 0 10px;border-radius 5px;">${pollutionType}</span></span>
                </div>
                <div class="item">
                    <span class="label">所属河流：</span>
                    <span class="value">${data.riverName || '--'}</span>
                </div>
                <div class="item">
                    <span class="label">详细地址：</span>
                    <span class="value">${data.stationAddress || '--'}</span>
                </div>
            </div>
          </div>
          `
        if (this.infoWindow) {
        this.infoWindow.setContent(infoWindowContent)
        this.infoWindow.setPosition([data.lng, data.lat]) // 更新点标记位置
        this.infoWindow.setMap(this.AMap)
        this.infoWindow.setOffset(new AMap.Pixel(-1, 5))
      } else {
        // 创建一个自定义内容的 infowindow 实例
        this.infoWindow = new AMap.InfoWindow({
          position: [data.lng, data.lat],
          offset: new AMap.Pixel(-1, 5),
          content: infoWindowContent,
          zIndex: 11
        })
        this.infoWindow.setMap(this.AMap)
        // this.infoWindow.on('close', this.handlecloseInfoWindow2)
      }
    },
    // 波纹动画
    setCanvas(item) {
      return
      if(this.animFrameId) {
        AMap.Util.cancelAnimFrame(this.animFrameId);
      }
      if(!this.myContext) {
        this.myCanvas = document.createElement('canvas');
        this.myCanvas.setAttribute('width', 200)
        this.myCanvas.setAttribute('height', 200)
        this.myContext = this.myCanvas.getContext('2d')
        this.myContext.fillStyle = 'rgba(255,30,30, 0.8)';
        this.myContext.strokeStyle = 'rgba(255,30,30, 0.8)';
        this.myContext.globalAlpha = 1;
        this.myContext.lineWidth = 2;
        const centerPoint = new AMap.LngLat(item.lng, item.lat)
        const pointEN = centerPoint.offset(200,200) //向东1000m，向北1000m的位置的经纬度
        const pointWS = centerPoint.offset(-200,-200) //向西1000m，向南1000m的位置的经纬度
        this.CanvasLayer = new AMap.CanvasLayer({
          canvas: this.myCanvas,
          bounds: new AMap.Bounds(pointWS, pointEN),
          // bounds: new AMap.Bounds([104.08876,30.726911], [104.07831,30.717928]),
          zooms: [12, 18],
          zIndex: 9
        });
         this.AMap.add(this.CanvasLayer)
         this.drawCanvas()
      } else {
        const centerPoint = new AMap.LngLat(item.lng, item.lat)
        const pointEN = centerPoint.offset(200,200) //向东1000m，向北1000m的位置的经纬度
        const pointWS = centerPoint.offset(-200,-200) //向西1000m，向南1000m的位置的经纬度
        this.CanvasLayer.setOptions({
          bounds: new AMap.Bounds(pointWS, pointEN),
        })
        this.drawCanvas()
      }

    },
    // 绘制波纹
    drawCanvas() {
      this.myContext.clearRect(0, 0, 200, 200)
      this.myContext.globalAlpha = (this.myContext.globalAlpha - 0.01 + 1) % 1;
      this.canvasRadius = (this.canvasRadius + 1) % 100;
      this.myContext.beginPath();
      this.myContext.arc(100, 100, this.canvasRadius, 0, 2 * Math.PI);
      this.myContext.fill();
      this.myContext.stroke();
      this.CanvasLayer.reFresh();
      this.animFrameId = AMap.Util.requestAnimFrame(this.drawCanvas);
    },
    // 点击站点
    handleClickMarker(e) {
      const { data } = e.target.w
      this.currentMarker = data
      this.setINfoWindow(data)
      this.setCanvas(data)
      this.AMap.setCenter([data.lng, data.lat])
      this.$emit('handleChangeStation', data)
      this.stationMarkerList.forEach(item => {
        if (item.w.data.stationId === data.stationId) {
          item.setLabel({
            offset: new AMap.Pixel(3, 5), //设置文本标注偏移量
            content: `<div class='dep-site-info' style="background-color:${!data.online ? 'rgba(128,128,128,0.7)' : data.isAlarm ? 'rgba(255,0,0,0.7)' : 'rgba(0,255,0,0.7)'}"></div>`, //设置文本标注内容
          })
        } else {
          item.setLabel({
            offset: new AMap.Pixel(3, 5), //设置文本标注偏移量
            content: '<div></div>', //设置文本标注内容
          })
        }
      })
    },
    // 添加站点列表marker
    setStationMarkers() {
      if(this.companyMarkerList.length) {
          this.AMap.remove(this.companyMarkerList)
          this.companyMarkerList = []
      }
      this.stationList.forEach((item) => {
        let image = QYZC
        if(item.isAlarm && item.type === 8) {
          image = QYCB
        } else {
          image = QYZC
        }
        if(!item.online) image = QYLX
        const icon = new AMap.Icon({
          // 图标尺寸
          size: new AMap.Size(41, 51),
          // 图标的取图地址
          image: image,
          // 图标所用图片大小
          imageSize: new AMap.Size(41, 51),
          // 图标取图偏移量
          imageOffset: new AMap.Pixel(0, 0)
        })
        const markerOptions = {
          map: this.AMap,
          position: new AMap.LngLat(+item.lng, +item.lat),
          offset: new AMap.Pixel(-20.5, -43.5),
          icon: icon,
          zIndex: 9,
          data: item
        }
        let markerIcon = new AMap.Marker(markerOptions)
        this.companyMarkerList.push(markerIcon)
        markerIcon.on('click', (e) => {
          const { data } = e.target.w
          if(data.type === 8 || item.type === '排口') {
            this.$emit('handleClickCompanyMarker', data)
          } else {
            this.setcommonInfoWindow(data)
          }
          this.AMap.setCenter([item.lng, item.lat])
          console.log('站点被点击了',e)
        })
      })

    },
    // 创建区域选择工具
    initMouseTool() {
      this.mouseTool = new AMap.MouseTool(this.AMap);
    },
    // 绘制区域
    draw(e){
      this.isDraw = false
      this.overlays = e.obj;
      var arr = e.obj.getPath();
      for (let i = 0; i < this.drainList.length; i++) {
        const element = this.drainList[i];
        const isIn = e.obj.contains(element.lng, element.lat)
        console.log('是否在范围内', isIn)
      }
      const pathArr = [];
      for(var i=0;i<arr.length;i++){
        pathArr.push({"lng":arr[i].lng,"lat":arr[i].lat});
      }
      // console.log(pathArr)
      this.mouseTool.close(false)//关闭，并清除覆盖物
      this.$emit('handleChangeRange', pathArr)
    },
    // 取消框选
    handleRemove() {
      this.AMap.remove(this.overlays)
      this.mouseTool.close()
      this.$emit('handleRemoveStationList')
    },
  // 点击绘制
    handleClickDraw() {
      if(this.isDraw) return
      this.closeCommonInfoWindow()
      this.isDraw = true
      if(this.overlays) this.handleRemove()
      this.mouseTool.polygon({
        fillColor:'#DCAC2B',
        strokeColor:'#DCAC2B',
        fillOpacity: 0.15,
        strokeStyle: 'dashed',
        cursor:'ani',
        strokeWeight: 1,
        zIndex: 10,
        bubble: true
        //同Polygon的Option设置,
      });
      this.mouseTool.on('draw', (e) => {
        this.draw(e)
      })
    }
  },
  computed: {},
  watch: {
    markerList: {
        handler(val) {
          this.closeCommonInfoWindow()
          this.setMarkers()
        },
        deep: true,
        immediate: true
    },
    drainList: {
      handler(val) {
        this.setDrainMarkers()
      },
      deep: true,
      immediate: true
    },
    stationList: {
        handler(val) {
            this.setStationMarkers()
        },
        deep: true,
        immediate: true
    },
    distance(val) {
      if(this.circleMap) {
        this.circleMap.setRadius(this.distance)
      }
      this.setCanvas(this.currentMarker)
    },
    currentCompany:{
      handler(val) {
        if(JSON.stringify(val) !== '{}') {
          this.setcommonInfoWindow(val)
          this.AMap.setCenter([val.lng, val.lat])
        }
      },
      deep: true,
      immediate: true
    }
  },
  components: {},
  beforeDestroy() {
    if(this.overlays) this.AMap.remove(this.overlays)
    this.stationMarkerList.length && this.AMap.remove(this.stationMarkerList)
    this.infoWindow && this.AMap.remove(this.infoWindow)
    this.circleMap && this.AMap.remove(this.circleMap)
    this.companyMarkerList.length && this.AMap.remove(this.companyMarkerList)
    this.lineLayer && this.AMap.remove(this.lineLayer)
    document.querySelector(`canvas.amap-layer`)?.getContext('webgl')?.getExtension('WEBGL_lose_context')?.loseContext()
    this.AMap.remove(this.AMap.getLayers(), this.AMap.getAllOverlays())
    this.AMap && this.AMap.destroy()
    this.AMap = null
    window.closeCommonInfoWindow = null
  }
}
</script>

<style lang="less" scoped>
.analysisMap-container {
  width: 100%;
  height: 100%;
  //   position: relative;
  .analysisMapContainer {
    width: 100%;
    height: 100%;
  }
}
.btns-container {
  position: absolute;
  cursor: pointer;
  top: 120px;
  right: 473px;
  z-index: 999;
}
</style>
<style lang="less">
.analysisMap-container {
  .amap-info-close {
    display: none;
  }
  .infoWindow {
    pointer-events: auto;
    width: 330px;
    // height: 240px;
    background-repeat: no-repeat;
    background-size: 100% 100% !important;
    padding: 15px 25px;
    position: relative;
    box-sizing: border-box;
    .title {
      position: absolute;
      top: 35px;
      left: 38px;
      font-size: 18px;
      font-family: YouSheBiaoTiHei;
      font-weight: 400;
      color: #ffffff;
      background: linear-gradient(0deg, #56d1ed 0%, #caf6ff 100%);
      background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .close {
      position: absolute;
      width: 22px;
      height: 22px;
      top: 47px;
      right: 25px;
      cursor: pointer;
    }
    .content {
      margin-top: 50px;
      padding: 0 20px 20px;
      .time {
        font-size: 12px;
        font-family: PingFang SC;
        font-weight: 400;
        color: #7798b9;
      }
      .flex-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      .item {
        display: flex;
        .label {
          font-size: 12px;
          font-family: PingFang SC;
          font-weight: 400;
          color: #dcf0ff;
          line-height: 20px;
        }
        .value {
          font-size: 12px;
          font-family: PingFang SC;
          font-weight: 400;
          color: #56a7f1;
          line-height: 20px;
          flex: 1;
        }
        .number {
          font-family: YouSheBiaoTiHei;
          font-size: 18px;
          font-weight: bold;
          margin-right: 10px;
        }
      }
    }
    .typeBg {
      background: url("~@/assets/analysis/<EMAIL>");
      background-size: 100% 100%;
      width: 71px;
      height: 26px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 12px;
      font-family: PingFang SC;
      font-weight: 400;
      color: #f6c429;
      line-height: 30px;
      // flex-shrink: 1;
      // flex-grow: 1;
    }
  }
  .dep-site-info {
    position: relative;
    width: 10px;
    height: 10px;
    left: -4.5px;
    top: -4.5px;
    border-radius: 50%;
    // transform: rotateX(70deg);
    /*-moz-animation-name: ripple;*/
    /*-webkit-animation-name: ripple;*/
    animation-name: depripple;
    animation-delay: 0s;
    animation-duration: 1s;
    -moz-animation-iteration-count: infinite;
    -webkit-animation-iteration-count: infinite;
    animation-iteration-count: infinite;
    pointer-events: none;
    // background-color: rgba(153, 0, 76, 0.7);
  }
 .color1 {
  background-color: rgba(128, 128, 128, 0.7);
 }
 .color2 {
  background-color: rgba(255, 0, 0, 0.7);
 }
 .color3 {
  background-color: rgba(0, 255, 255, 0.7);
 }
  @keyframes depripple {
    from {
      opacity: 1;
    }
    to {
      width: 60px;
      height: 60px;
      top: -30px;
      left: -25px;
      border-radius: 50%;
      opacity: 0;
    }
  }
  .amap-marker-label {
    pointer-events: none !important;
  }
  .amap-icon {
    overflow: hidden !important;
    img {
      pointer-events: none !important;
    }
  }
}
</style>
