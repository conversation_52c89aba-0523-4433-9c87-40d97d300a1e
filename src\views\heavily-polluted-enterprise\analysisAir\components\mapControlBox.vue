<template>
  <div class="marker-control-box" :style="{ right: '4.8rem' }">
    <!-- 按钮 S -->
    <div
      class="flex items-center marker-control-btn"
      @click="showIconList = !showIconList"
    >
      <div class="marker-control-btn-icon"></div>
      <span>地图图层</span>
    </div>
    <!-- 按钮 E -->

    <!-- 图标选择弹窗 S -->

    <transition
      :enter-active-class="enterClass"
      :leave-active-class="leaveClass"
    >
      <div v-show="showIconList" class="icon-selecte-box">
        <!-- <a-checkbox
          v-model="allSelectItem"
          class="check-item"
          @click="selectAllChange"
        >
          <div class="flex items-center">
            <span class="name flex flex-1">全选</span>
          </div>
        </a-checkbox> -->
        <a-checkbox
          v-for="item in iconList"
          :key="item.name"
          v-model="item.selected"
          class="check-item"
          @change="checkChange(item.index)"
        >
          <div class="flex items-center">
            <img :src="item.icon" />
            <span class="name flex flex-1">{{ item.name }}</span>
          </div>
        </a-checkbox>
      </div>
    </transition>
    <!-- 图标选择弹窗 E -->
  </div>
</template>

<script>
import { Checkbox } from "ant-design-vue";
import gficon from "@/assets/images/<EMAIL>"; //固废
import ldicon from "@/assets/images/<EMAIL>"; //雷达
import jkicon from "@/assets/images/<EMAIL>"; //监控
import syicon from "@/assets/images/<EMAIL>"; //事业
import gdicon from "@/assets/images/<EMAIL>"; //工地
import zsicon from "@/assets/images/<EMAIL>"; //噪声
import cyicon from "@/assets/images/<EMAIL>"; //餐饮
import pwicon from "@/assets/images/<EMAIL>"; //排污
import dlicon from "@/assets/images/<EMAIL>"; //电量
import airAnalysis from "@/assets/images/airAnalysis.png"; //大气污染源分析
import truckicon from "@/assets/images/-s-hyc.png"; //大气污染源分析
export default {
  components: {
    ACheckbox: Checkbox,
  },
  props: {
    typeNum: {
      type: Number,
      default: 0,
    },
    close: {
      type: Number,
      default: 1,
    },
  },
  watch: {
    showIconList: {
      handler(newVal) {
        // this.filterData();
        if (newVal === true) {
          this.$emit("closeDialogVisible");
        }
      },
      deep: true,
      immediate: true,
    },
    close: {
      handler(newVal) {
        this.showIconList = false;
      },
      deep: true,
      immediate: true,
    },
  },
  data() {
    return {
      showIconList: false,
      iconList: [
        {
          name: "汽修源",
          icon: gdicon,
          index: 0,
          selected: true,
        },
        {
          name: "工地源",
          icon: ldicon,
          index: 1,
          selected: true,
        },
        {
          name: "工业源",
          icon: syicon,
          index: 2,
          selected: true,
        },
        {
          name: "加油站源",
          icon: dlicon,
          index: 3,
          selected: true,
        },
        {
          name: "重型停车场",
          icon: jkicon,
          index: 4,
          selected: true,
        },
      ],
      allSelectItem: [],
      leaveClass: "animate__animated animate__fadeOutRight",
      enterClass: "animate__animated animate__fadeInRight",
      activeIndex: 0,
    };
  },
  methods: {
    selectAllChange() {},
    checkChange(index) {
      console.log(index);
      this.iconList.forEach((item) => {
        if (index === item.index) {
          item.selected = item.selected;
        }
      });
      this.allSelectItem = this.iconList
        .filter((item) => item.selected)
        .map((it) => it.index);
      this.$emit("handleGetSelectItem", this.allSelectItem);
      // this.showIconList = !this.showIconList
      console.log("this.allSelectItem", this.allSelectItem);
    },
  },
};
</script>

<style lang="less" scoped>
.flex {
  display: flex;
}
.items-center {
  justify-items: center;
  align-items: center;
  vertical-align: middle;
}
.marker-control-box {
  position: fixed;
  width: 1.5rem;
  height: 0.4rem;
  top: 1.55rem;
  background: url(~@/assets/images/<EMAIL>) no-repeat center center;
  background-size: 100% 100%;
  padding: 11px 32px;
  z-index: 2999;
  // transition-property: right;
  // transition-duration: .5s;

  .marker-control-btn {
    width: 100%;
    height: 100%;
    cursor: pointer;

    span {
      font-size: 14px;
      font-weight: bold;
      color: #ffffff;
    }
  }

  .marker-control-btn-icon {
    width: 17px;
    height: 15px;
    background: url(~@/assets/images/<EMAIL>) no-repeat center
      center;
    background-size: 100% 100%;
    margin-right: 9px;
    margin-top: 2px;
  }

  .icon-selecte-box {
    position: absolute;
    padding: 5px 15px 5px 20px;
    margin-top: 20px;
    left: -9px;
    width: 180px;
    background-color: rgba(0, 4, 18, 0.56);
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    border-top: 2px solid #3883df;
    border-bottom: 2px solid #3883df;
  }
}

.check-item {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  // margin-left: 0;
  margin: 10px 0;

  img {
    width: 16px;
    height: 14px;
    margin: 2px 10px 0 10px;
  }

  .name {
    display: inline-block;
    color: #caf6ff;
    font-size: 14px;
  }
}
</style>
