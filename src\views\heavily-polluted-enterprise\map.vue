<style lang="less">
.select_box {
  // position: absolute;
  top: 0;
  right: 0;
}
.select-main {
  width: 1.2rem;
  display: flex;
  justify-content: space-between;
  .ant-select-selection {
    height: 0.3rem;
    width: 100%;
    border: none;
    outline: none;
    border-radius: unset;
    background: url("~@/assets/<EMAIL>");
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }
  .ant-select-selection-selected-value {
    color: rgba(0, 234, 255, 1);
  }
  .ant-select-focused .ant-select-selection,
  .ant-select-selection:focus,
  .ant-select-selection:active {
    border: none;
    border-right-width: 0 !important;
    outline: none;
    box-shadow: none;
  }
}
.info-main {
  width: 3.2rem;
  height: 2.5rem;
  background: url("../../assets/<EMAIL>") no-repeat;
  background-size: 100% 100%;
  padding: 0.3rem 0.05rem 0 0.2rem;
  .title-flex {
    display: flex;
    align-items: center;
    margin-bottom: 0.1rem;
    .name {
      color: #00eaff;
      font-size: 0.18rem;
      width: 70%;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }
    .more {
      text-decoration: underline;
      font-size: 0.16rem;
      cursor: pointer;
    }
    .close-img {
      width: 0.22rem;
      height: 0.22rem;
      margin-left: 0.12rem;
      cursor: pointer;
    }
  }
}
.site-info {
  position: relative;
  width: 10px;
  height: 10px;
  left: 0;
  top: 0;
  border-radius: 50%;
  /*background-color: red;*/
  transform: rotateX(70deg);
  /*-moz-animation-name: ripple;*/
  /*-webkit-animation-name: ripple;*/
  animation-name: ripple;
  animation-delay: 0s;
  animation-duration: 1s;
  -moz-animation-iteration-count: infinite;
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
}
.site-info-pollutantState {
  background-color: #ff5053;
}
.site-info-offline {
  background-color: #ccc4c6;
}
.site-info-online {
  background-color: #78fffd;
}
@keyframes ripple {
  from {
    opacity: 1;
  }
  to {
    width: 40px;
    height: 40px;
    top: -15px;
    left: -15px;
    border-radius: 50%;
    opacity: 0;
  }
}
.top_Mcontent {
  background: url("../../assets/<EMAIL>") no-repeat;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  min-width: 130px;
  text-align: center;
  line-height: 40px;
  padding: 0 15px;
  padding-left: 27px;
  height: 40px;
  font-size: 11px;
  font-family: PingFang SC;
  font-weight: 400;
  color: #ffffff;
}
.ant-modal-recheck {
  top: 1.65rem !important;
  right: calc(50% - 500px) !important;
  .ant-modal-content {
    width: 15.6rem !important;
    min-height: 6.9rem !important;
    max-height: 8.5rem !important;
    padding: 0.7rem 0rem 0.54rem 1.1rem;
    box-sizing: border-box;
    background-color: transparent !important;
    background-image: url("~@/assets/<EMAIL>") !important;
    background-size: 100% 100% !important;
  }
  .ant-modal-footer {
    border: none !important;
  }
  .ant-modal-close {
    top: 0.49rem !important;
    right: 0.86rem !important;
    svg {
      font-size: 0.2rem !important;
      color: #42adfb;
    }
  }
  .ant-modal-close-x {
    background: RGBA(12, 39, 94, 1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    width: 0.29rem;
    height: 0.29rem;
    justify-content: center;
    border: 0.01rem solid #358fd3;
  }
  .ant-modal-body {
    padding: 0;
    overflow-y: auto;
    max-height: 726px;
    &::-webkit-scrollbar-thumb {
      border-radius: 10px;
      width: 1px;
      background: transparent;
    }
    &::-webkit-scrollbar-track {
      border-radius: 10px;
      background: transparent;
    }
    .smapContainer {
      display: flex;
      flex-wrap: wrap;
      .sigleSmBox {
        width: 205px;
        height: 266px;
        background: url("~@/assets/recheck/<EMAIL>") no-repeat;
        background-size: 100%;
        position: relative;
        padding: 16px 8px 10px;
        box-sizing: border-box;
        margin-top: 20px;
        .dzIcon {
          font-size: 16px;
          font-family: PingFang SC;
          font-weight: 400;
          color: #dbf5ff;
        }
        .smBorderImg {
          position: absolute;
          width: 191px;
          height: 215px;
        }
        .smImgBox {
          position: absolute;
          width: 168px;
          height: 200px;
          overflow: hidden;
          top: 44px;
          left: 18px;
          .smImg {
            position: absolute;
            height: 200px;
            width: 355.5px;
            top: 5px;
            left: -80px;
            cursor: pointer;
          }
        }
      }
    }
  }
}
</style>

<template>
  <div style="position: relative">
    <div ref="mapContainer" class="mapContainer" id="container"></div>
    <div class="relationship_box" v-if="isRadar">
      <div class="header_box">
        <a-select
          style="width: 1rem"
          @change="changeDistance"
          v-model="distance"
          class="select-main select_box"
        >
          <a-icon
            slot="suffixIcon"
            type="caret-down"
            style="color: rgba(0, 234, 255, 1); width: 14px; height: 8px"
          />
          <a-select-option
            v-for="item in distanceList"
            :key="item.name"
            :value="item.value"
          >
            {{ item.name }}
          </a-select-option>
        </a-select>
      </div>
      <div class="content">
        <div
          class="content_item"
          :class="{ active: item.id == companyId }"
          v-for="(item, index) in pollutionStationList"
          @click="handleClickRaderItem(item.id)"
          :key="index"
        >
          <div class="left_box" style="margin-right: 10px">
            <div class="title" :title="item.name">
              <span>{{ item.name }}</span>
            </div>
            <div class="item_list">
              <div class="item_item">
                <span class="label">污染源类型：</span>
                <span class="label">{{
                  pollutionTypeObj[item.pollutionType] || "--"
                }}</span>
                <!-- <div class="item_item">
                  <span class="label">设备状态：</span>
                  <span class="label">{{
                    item.devieList &&
                    item.devieList.length &&
                    item.devieList[0].isOnline
                      ? "在线"
                      : "离线"
                  }}</span> -->
              </div>
              <!-- <div class="item_item">
                <span class="label">浓度：</span>
                <span class="value">
                  <span class="number">12.3 </span>
                  <span class="unit">ug/m³</span>
                </span>
              </div> -->
            </div>
          </div>
          <div class="right_box">
            <span class="number">{{ item.distance }}</span>
            <span class="unit">km</span>
          </div>
        </div>
      </div>
    </div>
    <div style="position: absolute; right:100px; bottom: 200px;">
      <newTask ref="newTaskRef"></newTask>
    </div>
    <!-- 污染源详情 -->
    <div ref="modal" style="width:100%;height:100%">
      <a-modal
        v-if="dialogVisible"
        :visible.sync="dialogVisible"
        class="ant-modal-recheck"
        :footer="null"
        :maskClosable="true"
        :centered="true"
        @cancel="handleCancel"
        style="top: 0.2rem !important;"
        :getContainer="() => $refs.modal"
      >
        <div class="content">
          <pollutionSourcesDetail :id="companyId" />
        </div>
      </a-modal>
    </div>
  </div>
</template>
<script>
import { webglcontextlostHandle } from "@/utils/index";
// import AMap from 'AMap'
import AMapLoader from "@amap/amap-jsapi-loader";
import districtRiver from "@/assets/jinniu-river.png";
import yszcIcon from "@/assets/heavyImages/<EMAIL>";
import ysglIcon from "@/assets/heavyImages/<EMAIL>";
import yslxIcon from "@/assets/heavyImages/<EMAIL>";
import qxzcIcon from "@/assets/heavyImages/<EMAIL>";
import qxgjIcon from "@/assets/heavyImages/<EMAIL>";
import qxlxIcon from "@/assets/heavyImages/<EMAIL>";
import yyzcIcon from "@/assets/heavyImages/<EMAIL>";
import yygjIcon from "@/assets/heavyImages/<EMAIL>";
import yylxIcon from "@/assets/heavyImages/<EMAIL>";
import jyzcIcon from "@/assets/heavyImages/<EMAIL>";
import jygjIcon from "@/assets/heavyImages/<EMAIL>";
import jylxIcon from "@/assets/heavyImages/<EMAIL>";
import markerIcon1 from "@/assets/heavily-polluted-enterprise/<EMAIL>"; //印刷图标
import markerIcon5 from "@/assets/heavily-polluted-enterprise/yinshua1.png"; //印刷图标
import markerIcon2 from "@/assets/heavily-polluted-enterprise/<EMAIL>"; //汽修图标
import markerIcon6 from "@/assets/heavily-polluted-enterprise/qixiu1.png"; //汽修图标
import markerIcon3 from "@/assets/heavily-polluted-enterprise/<EMAIL>"; //餐饮图标
import markerIcon9 from "@/assets/heavily-polluted-enterprise/<EMAIL>"; //餐饮图标
import markerIcon4 from "@/assets/heavily-polluted-enterprise/<EMAIL>"; //加油站图标
import markerIcon7 from "@/assets/heavily-polluted-enterprise/canyinyouyan1.png"; //餐饮图标
import markerIcon10 from "@/assets/heavily-polluted-enterprise/<EMAIL>"; //餐饮图标
import markerIcon8 from "@/assets/heavily-polluted-enterprise/jiayouzhan1.png"; //加油站图标
import closeImg from "@/assets/<EMAIL>";
import EZUIKit from "../../../public/static/ezuikit.js";
import jinniuStreet from "@/assets/map-geojson/jinniu_street";
import { getToken } from "@/utils/authority";
import { socketUrl1 } from "@/utils/index";
import chaobiao from "@/assets/heavyImages/<EMAIL>";
import chaobiaoActive from "@/assets/chaobiao-active.png";
import weichaobiao from "@/assets/heavyImages/<EMAIL>";
import weichaobiaoActive from "@/assets/weichaobiao-active.png";
import diaoxian from "@/assets/heavyImages/<EMAIL>";
import diaoxianActive from "@/assets/diaoxian-active.png";
import stationIconPic from "@/assets/<EMAIL>";
import Gdicon from "@/assets/<EMAIL>";
import { getCurSitePM10 } from "@/api/headvily-pollution";
import buildingIconPic from "@/assets/zwqy.png";
import buildingActiveIconPic from "@/assets/-s-zwqy.png";
import shuichang from "@/assets/shuichangbg.png";
import marker from "@/assets/marker.png";
import mc from "@/assets/<EMAIL>";
import qsyIcon from "@/assets/heavyImages/<EMAIL>";
import noiseNormal from "@/assets/noise/<EMAIL>"; // 噪声地图图标-正常
import noiseOver from "@/assets/noise/<EMAIL>"; // 噪声地图图标-超标
import noiseOffline from "@/assets/noise/<EMAIL>"; // 噪声地图图标-离线
import noiseBg from "@/assets/noise/<EMAIL>"; // 噪声弹窗背景
import noiseLx from "@/assets/noise/<EMAIL>"; // 噪声弹窗类型
import noiseIcon from "@/assets/noise/<EMAIL>"; // 噪声弹窗图标
import noiseClose from "@/assets/noise/<EMAIL>"; // 噪声弹窗关闭
import noiseTime from "@/assets/noise/<EMAIL>"; // 噪声弹窗时间
import electricityNormal from "@/assets/electricity/<EMAIL>"; // 用电地图图标-正常
import electricityOver from "@/assets/electricity/<EMAIL>"; // 用电地图图标-超标
import electricityOffline from "@/assets/electricity/<EMAIL>"; // 用电地图图标-离线
import electricityBg from "@/assets/noise/<EMAIL>"; // 用电弹窗背景
import electricityIcon from "@/assets/electricity/<EMAIL>"; // 用电弹窗背景
import solidWasteNormal from "@/assets/solid-waste/<EMAIL>"; // 固废地图图标-正常
import solidWasteOver from "@/assets/solid-waste/<EMAIL>"; // 固废地图图标-超标
import solidWasteOffline from "@/assets/solid-waste/<EMAIL>"; // 固废地图图标-离线
import solidWasteIcon from "@/assets/solid-waste/<EMAIL>"; // 固废弹框图标
import gongdiIcon from "@/assets/solid-waste/<EMAIL>"; // 建筑工地弹框图标
import youyanIcon from "@/assets/solid-waste/<EMAIL>"; // 油烟浓度弹框图标
import heavyCorruptNormal from "@/assets/heavy-corrupt/<EMAIL>"; // 重污地图图标-正常
import heavyCorruptOver from "@/assets/heavy-corrupt/<EMAIL>"; // 重污地图图标-超标
import heavyCorruptOffline from "@/assets/heavy-corrupt/<EMAIL>"; // 重污地图图标-离线
import heavyCorruptKong from "@/assets/heavy-corrupt/<EMAIL>"; // 重污地图弹窗空图片
import truckOffLine from "@/assets/exhaustGasTruck/<EMAIL>"; // 黑烟车离线
import truckNormal from "@/assets/exhaustGasTruck/<EMAIL>"; // 黑烟车正常
import truckOver from "@/assets/exhaustGasTruck/<EMAIL>"; // 黑烟车超标
import truckIcon from "@/assets/exhaustGasTruck/<EMAIL>"; // 黑烟车弹窗图片
import { heatmapData } from "./heatmapData";
import dayjs from "dayjs";

import companyIcon from "@/assets/rehearseAnalyse/<EMAIL>";

import { rangeByPollutionSource } from "@/api/practiceCheck";

import newTask from "./newTask/index";
import pollutionSourcesDetail from "../pollutionSourcesDetail/index";

import {
  getByGroupFileName,
  getByAngleAndGroupFileName,
  findPointData,
  getLastView,
} from "@/api/randa";
import moment from "moment";

let AMap;
let getTimer = null;

export default {
  name: "",
  components: {
    EZUIKit,
    newTask,
    pollutionSourcesDetail,
  },
  props: {
    markerList: {
      type: Array,
      default: [],
    },
    activeIndex: {
      type: Number,
      default: 1,
    },
    accessToken: {
      type: String,
      default: "",
    },
    isClose: {
      type: Boolean,
      default: false,
    },
    isWx: {
      type: Boolean,
      default: false,
    },
    currCompanyId: {
      type: String,
      default: "",
    },
    currentSelectedBuilding: {
      type: String,
      default: "",
    },
    currentSelectedCompanyId: {
      type: String,
      default: "",
    },
    randaItem: {
      type: String,
      default: "",
    },
    isRadar: {
      type: Boolean,
      default: false,
    },
    randaType: {
      type: String,
      default: "消光系数",
    },
    lukuangIndex: {
      type: Number,
      default: 0,
    },
    cotrolsIndex: {
      type: Number,
      default: 0,
    },
    radarItem: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      showInfoWindow: false,
      maps: null,
      mapCenter: [104.061111, 30.714222],
      mapCenter1: [103.9843, 30.72044],
      infoWindow: undefined,
      isOpen: false,
      printFactoryCamera: "",
      mapStyle: "amap://styles/0739a6632d849ae1b5e6f1c52f7d12c3",
      mapStyle1: "amap://styles/0739a6632d849ae1b5e6f1c52f7d12c3",
      cameraList: null,
      randaLayer: [],
      randaLayer1: null,
      randaTimer: null,
      randaImg: "",
      randaAngle: 0,
      isFirstRanda: false,
      isChoose: false,
      vidoeUrl: "",
      socket: null,
      socketData: null,
      makerList: [],
      curr: undefined,
      siteMarkerList: [],
      waterBuildingMarkerList: [],
      factoryMarkerClear: null,
      factoryTextClear: null,
      fiveMarkerClear: null,
      fiveTextClear: null,
      heatMapLayer: null,
      heatmapData: [],
      HPLabelId: "",
      groupFileName: "",
      randaClickFlg: false,
      distanceList: [
        { name: "0.5公里", value: "500" },
        { name: "1公里", value: "1000" },
        { name: "1.5公里", value: "1500" },
        { name: "2公里", value: "2000" },
      ],
      distance: "500",
      point: [],
      pollutionStationList: [],
      companyMarkerList: [],
      stationMarkerList: [],
      dialogVisible: false,
      companyId: -1,
      // 污染源类型 0 汽修 1 工地源 2 工业源 3加油站 4 停车场
      pollutionTypeObj: {
        0: "汽修源",
        1: "工地源",
        2: "工业源",
        3: "加油站",
        4: "停车场",
      },
      randarDataInfo: {},
    };
  },
  beforeDestroy() {
    console.log("--beforeDestroy---polluted---244");
    this.closeInfoWindow();
    if (this.maps) {
      document
        .querySelector(`canvas.amap-layer`)
        ?.getContext("webgl")
        ?.getExtension("WEBGL_lose_context")
        ?.loseContext();
      this.maps.remove(this.maps.getLayers(), this.maps.getAllOverlays());
      this.maps && this.maps.destroy();
      this.maps = null;
    }
    this.makerList = [];
    this.siteMarkerList = [];
    this.waterBuildingMarkerList = [];
    this.factoryMarkerClear = null;
    this.factoryTextClear = null;
    this.fiveMarkerClear = null;
    this.fiveTextClear = null;
    // 销毁前清空方法 防止内存泄漏
    window.closeInfoWindow = null;
    window.HPClick = null;
    window.closeInfo = null;
    window.handleClickTask = null;
    AMap = null;
    this.mapRuler?.turnOff();
    this.mapRuler = null;
  },
  watch: {
    markerList: {
      handler(newVal, oldVal) {
        if (newVal) {
          if (this.activeIndex === 10) return;
          if (!this.maps) return;
          this.isOpen = false;
          this.currItem = undefined;
          // this.initMap()
          if (this.makerList.length) {
            this.maps.remove(this.makerList);
            this.makerList = [];
          }
          if (this.siteMarkerList.length) {
            this.closeInfoWindow();
            this.maps.remove(this.siteMarkerList);
            this.siteMarkerList = [];
            if (this.curMarkerClick) {
              this.closeInfoWindow();
            }
          }
          if (this.waterBuildingMarkerList.length) {
            this.maps.remove(this.waterBuildingMarkerList);
            this.waterBuildingMarkerList = [];
          }
          if (this.enterpriseMarker) {
            this.maps.remove(this.enterpriseMarker);
          }
          if (this.factoryMarkerClear) {
            this.maps.remove(this.factoryMarkerClear);
          }
          if (this.fiveMarkerClear) {
            this.maps.remove(this.fiveMarkerClear);
          }
          if (this.factoryTextClear !== null) {
            this.maps.remove(this.factoryTextClear);
            this.maps.remove(this.factoryMarkerClear);
            this.factoryTextClear = null;
            this.factoryMarkerClear = null;
          }
          if (this.fiveTextClear !== null) {
            this.maps.remove(this.fiveMarkerClear);
            this.maps.remove(this.fiveTextClear);
            this.fiveMarkerClear = null;
            this.fiveTextClear = null;
          }
          if (this.text !== null && this.enterpriseMarker) {
            this.maps.remove(this.text);
            this.maps.remove(this.enterpriseMarker);
            this.text = null;
            this.enterpriseMarker = null;
          }
          if (this.infoWindow) {
            this.closeInfoWindow();
          }
          this.addMarkers();
        }
      },
      deep: true,
    },
    radarItem: {
      handler(newVal, oldVal) {
        this.clearRandaLayer();
        clearInterval(getTimer);
        getTimer = null;
        if (newVal) {
          this.isChoose = true;
          this.isFirstRanda = true;
          this.randaClickFlg = false;
          this.groupFileName = newVal.fileName;
          this.$emit("getGroupFileName", newVal.fileName);
          this.randaImg = newVal.xg;
          this.randaAngle = newVal.angle;
          this.addCanvasLayer(this.maps, {
            lng: newVal.maxLng,
            lat: newVal.maxLat,
          });
        }
      },
      deep: true,
    },
    isClose: {
      handler(newVal, oldVal) {
        if (newVal) {
          this.clearCarSelected();
        }
      },
    },
    currCompanyId: {
      handler(newVal, oldVal) {
        if (newVal) {
          this.changeMaker();
        }
      },
    },
    currentSelectedBuilding: {
      handler(newVal, oldVal) {
        if (newVal) {
          this.changeMaker();
        }
      },
    },
    currentSelectedCompanyId: {
      handler(newVal, oldVal) {
        if (newVal) {
          if (this.activeIndex === 9) {
            const onj = this.markerList.filter(
              (v) => v.heavilyPollutingEnterpriseId == newVal
            )[0].pointList[0];
            this.HPLabelId = onj ? onj.pointId : "";
          }
          this.changeMaker();
        }
      },
    },
    activeIndex: {
      handler(newVal, oldVal) {
        if (this.randaTimer) clearTimeout(this.randaTimer);
        if (getTimer) {
          clearInterval(getTimer);
          getTimer = null;
        }
        if (!newVal) return;
        if (newVal !== 10) {
          this.maps && this.maps.destroy();
          this.initMap();
        } else {
          this.maps && this.maps.destroy();
          this.initMap("2D");
          this.getInitRanda();
        }
        // this.maps.setZoom(newVal == 3 ? 13.8 : 12.8)
        // this.maps.setCenter(newVal == 3 ? new AMap.LngLat(104.05, 30.69) : new AMap.LngLat(104.05, 30.7))
      },
      immediate: true,
    },

    isWx: {
      handler(newVal, oldVal) {
        // map.add(new AMap.TileLayer.Satellite())
        this.initMap("2D");
        if (!this.isChoose) {
          this.getInitRanda();
        } else {
          this.clearRandaLayer();
          switch (this.randaType) {
            case "消光系数":
              this.randaImg = this.radarItem.xg;
              break;
            case "PM₁₀":
              this.randaImg = this.radarItem.pm10;
              break;
            case "PM₂.₅":
              this.randaImg = this.radarItem.pm25;
              break;
            default:
              this.randaImg = this.radarItem.tp;
              break;
          }
          this.addCanvasLayer(this.maps, {
            lng: this.radarItem.maxLng,
            lat: this.radarItem.maxLat,
          });
        }
      },
      immediate: false,
    },

    lukuangIndex: {
      handler(newVal, oldVal) {
        this.initMap("2D");
        this.getInitRanda();
      },
      immediate: false,
    },
    cotrolsIndex: {
      handler(newVal, oldVal) {
        if (newVal === 3) {
          this.mapRuler.turnOn();
        } else {
          this.mapRuler.turnOff();
        }
      },
      immediate: false,
    },
    randaItem: {
      handler(groupFileName) {
        return;
        this.randaImg = "";
        if (this.randaImg) {
          this.maps.clearInfoWindow();
          this.clearRandaLayer();
        }
        this.randaAngle = 4;
        console.log("---389");
        this.getInitRanda();
      },
      deep: true,
    },
    randaType: {
      handler(newVal) {
        if (this.activeIndex != 10) return;
        if (this.randaImg) {
          this.maps.clearInfoWindow();
          // this.clearRandaLayer()
          this.randaImg = "";
        }
        if (getTimer) {
          clearInterval(getTimer);
          getTimer = null;
        }
        if (!this.isChoose) {
          this.randaAngle = 0;
          this.getInitRanda();
        } else {
          this.clearRandaLayer();
          switch (newVal) {
            case "消光系数":
              this.randaImg = this.radarItem.xg;
              break;
            case "PM₁₀":
              this.randaImg = this.radarItem.pm10;
              break;
            case "PM₂.₅":
              this.randaImg = this.radarItem.pm25;
              break;
            default:
              this.randaImg = this.radarItem.tp;
              break;
          }
          this.addCanvasLayer(this.maps, {
            lng: this.radarItem.maxLng,
            lat: this.radarItem.maxLat,
          });
        }
      },
    },
  },
  created() {
    AMapLoader["reset"]();
  },
  mounted() {
    // // load 加载
    AMapLoader.load({
      key: "777fec7ef3cc29281d60ae900fa33925", // 申请好的Web端开发者Key，首次调用 load 时必填
      version: "1.4.15", // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
      plugins: [
        "AMap.DistrictSearch",
        "AMap.Heatmap",
        "AMap.ControlBar",
        "AMap.Object3DLayer",
        "Map3D",
        "AMap.Geocoder",
        "AMap.CircleMarker",
        "AMap.MouseTool",
        "AMap.RangingTool", // 这个是测距插件
      ], // 需要使用的的插件列表，如比例尺'AMap.Scale'等
      AMapUI: {
        // 是否加载 AMapUI，缺省不加载
        version: "1.0", // AMapUI 版本
        // "plugins":['overlay/SimpleMarker'],       // 需要加载的 AMapUI ui插件
      },
      Loca: {
        // 是否加载 Loca， 缺省不加载
        version: "1.3.2", // Loca 版本
      },
    })
      .then((amaps) => {
        AMap = amaps;
        if (this.activeIndex === 10) {
          this.initMap("2D");
          this.getInitRanda();
        } else {
          this.initMap();
        }
        this.addMarkers();
      })
      .catch((e) => {
        console.log(e);
      });
    // window.jumpVideo = this.jumpVideo
    // this.initMap()
    this.connect();
    window.closeInfoWindow = this.closeInfoWindow; // 解决字符串模板@click无效的问题
    window.HPClick = this.HPClick; // 解决字符串模板@click无效的问题
    window.handleClickTask = this.handleClickTask;
    window.closeInfo = () => {
      if (this.maps) {
        this.maps.clearInfoWindow();
      }
    };
  },
  methods: {
    // 点击任务调度
    handleClickTask() {
      const { address, lat, lng } = this.randarDataInfo || {};
      this.$refs.newTaskRef.createNewTaskOpen({ address, lat, lng });
    },
    // 切换查询距离
    changeDistance() {
      this.handleGetPollutionSource();
      this.setCircle();
    },
    handleGetPollutionSource() {
      const params = {
        // lng: 103.992961,
        // lat: 30.709217,
        lng: this.point.lng,
        lat: this.point.lat,
        distance: this.distance,
      };
      rangeByPollutionSource(params).then((res) => {
        this.pollutionStationList = res.data.data;
        this.setMarkers();
      });
    },
    // 添加站点icon
    setMarkers() {
      if (this.stationMarkerList.length) {
        this.maps.remove(this.stationMarkerList);
        this.stationMarkerList = [];
      }
      this.stationMarkerList = [];
      this.pollutionStationList.forEach((item, index) => {
        this.addMaker(item);
      });
    },
    addMaker(item) {
      const icon = new AMap.Icon({
        // 图标尺寸
        size: new AMap.Size(41, 51),
        // 图标的取图地址
        image: companyIcon,
        // 图标所用图片大小
        imageSize: new AMap.Size(41, 51),
        // 图标取图偏移量
        imageOffset: new AMap.Pixel(0, 0),
      });
      const markerOptions = {
        map: this.maps,
        position: new AMap.LngLat(item.lng, item.lat),
        offset: new AMap.Pixel(-20.5, -51),
        icon: icon,
        zIndex: 9,
        data: item,
      };
      let markerIcon = new AMap.Marker(markerOptions);
      // AMap.event.addListener(markerIcon, ()=> {
      //     this.handleClickMarker
      // })
      markerIcon.on("click", this.handleClickMarker);
      this.stationMarkerList.push(markerIcon);
    },
    // 添加圆圈
    setCircle() {
      if (this.circleMap) {
        this.circleMap.setCenter(this.point);
        this.circleMap.setRadius(this.distance);
      } else {
        //  添加圆圈
        let circle = new AMap.Circle({
          map: this.maps,
          zIndex: 1000,
          center: this.point, //设置线覆盖物路径
          radius: this.distance,
          // radius: 1000,
          strokeColor: "#fff", //边框线颜色
          strokeStyle: "dashed",
          strokeOpacity: 0.5, //边框线透明度
          strokeWeight: 1, //边框线宽
          fillColor: "#fff", //填充色
          fillOpacity: 0, //填充透明度
        });
        this.circleMap = circle;
      }
    },
    handleClickRaderItem(id) {
      this.companyId = id;
      this.dialogVisible = true;
    },
    handleClickMarker(e) {
      const id = e.target.w.data.id;
      this.companyId = id;
      this.dialogVisible = true;
    },
    handleCancel() {
      this.dialogVisible = false;
      this.companyId = -1;
    },

    // 重污弹窗label点击事件
    HPClick(e, m) {
      this.HPLabelId = e;
      this.openHeavyCorruptSite(
        this.markerList.filter((v) => v.companyId === m)[0]
      );
    },
    /* jumpVideo() {
      this.$router.push({
        name: "moreVideo",
        params: { socketData: this.socketData, socket: this.socket, type: this.activeIndex }
      });
    }, */
    // 建立连接
    connect() {
      this.socket = new WebSocket(socketUrl1());
      // 监听socket连接
      this.socket.onopen = this.open;
      // 监听socket错误信息
      this.socket.onerror = this.error;
      // 监听socket消息
      this.socket.onmessage = this.getMessage;
      this.$once("hook:beforeDestroy", () => {
        this.socket.onopen = () => {};
        this.socket.onerror = () => {};
        this.socket.onmessage = () => {};
      });
    },
    open() {
      this.send();
    },
    send() {
      this.socket.send(JSON.stringify({ code: 1, token: "token" }));
    },
    error() {
      console.error("系统连接错误");
    },
    getMessage(msg) {
      const data = JSON.parse(msg.data);
      if (data.code === -1 && data.success) {
        if (this.activeIndex === 1) {
          this.socket.send(JSON.stringify({ code: 2, token: getToken() }));
        } else if (this.activeIndex === 2) {
          this.socket.send(JSON.stringify({ code: 10, token: getToken() }));
        }
      }
      // 印刷
      if (data.code === -2 && data.success) {
        this.socketData = data;
      }
      // 汽修
      if (data.code === -10 && data.success) {
        this.socket.send(
          JSON.stringify({
            code: 15,
            token: getToken(),
            garageId: this.currCompanyId,
            pageNum: 1,
            pageSize: 36,
          })
        );
        this.socketData = data;
      }
      if (data.code === -15 && data.success) {
        this.cameraList = data.cameraList ? data.cameraList.records || [] : [];
      }
    },
    // 初始化地图
    initMap(type = "3D") {
      let mapSettingObj = {
        center: type == "3D" ? this.mapCenter : this.mapCenter1,
        zoom: type == "3D" ? 13.3 : 13.5,
        viewMode: type,
        // layers:
        //   type == '2D' && !this.isWx ? [new AMap.TileLayer.Satellite()] : [],
        zoomEnable: true,
        dragEnable: true,
        zooms: type == "3D" ? [12, 18] : [13, 20],
      };
      if (type == "3D") {
        mapSettingObj.pitch = 40;
        mapSettingObj.position = [104.05, 30.7];
      }
      const map = new AMap.Map(this.$refs.mapContainer, mapSettingObj);

      if (type == "2D" && !this.isWx) {
        map.add(new AMap.TileLayer.Satellite());
      }
      if (type == "2D" && this.lukuangIndex === 2) {
        map.add(new AMap.TileLayer.Traffic());
      }
      // 处理webgl上下文丢失事件
      webglcontextlostHandle.call(this);
      if (type == "3D") {
        // 添加 3D 罗盘控制
        AMap.plugin(["AMap.ControlBar"], () => {
          map.addControl(
            new AMap.ControlBar({
              position: {
                bottom: "-0.2rem",
                right: "4.8rem",
                zIndex: 2,
                transition: "1.5s",
              },
            })
          );
        });
        // 添加3D图层
        this.draw3dMap(map);
        // 设置地图样式
        map.setMapStyle(this.mapStyle);
        // 添加街道划分区域地图数据
        this.creatGeojson(map);
        // 添加行政区外的覆盖物
        this.createOverlay(map);
      } else {
        map.setMapStyle(this.mapStyle1);
      }
      this.maps = map;

      // 添加测距
      var startMarkerOptions = {
        icon: new AMap.Icon({
          size: new AMap.Size(19, 31), //图标大小
          imageSize: new AMap.Size(19, 31),
          image: "http://webapi.amap.com/theme/v1.3/markers/b/start.png",
        }),
      };
      var endMarkerOptions = {
        icon: new AMap.Icon({
          size: new AMap.Size(19, 31), //图标大小
          imageSize: new AMap.Size(19, 31),
          image: "http://webapi.amap.com/theme/v1.3/markers/b/end.png",
        }),
        offset: new AMap.Pixel(-9, -31),
      };
      var midMarkerOptions = {
        icon: new AMap.Icon({
          size: new AMap.Size(19, 31), //图标大小
          imageSize: new AMap.Size(19, 31),
          image: "http://webapi.amap.com/theme/v1.3/markers/b/mid.png",
        }),
        offset: new AMap.Pixel(-9, -31),
      };
      var lineOptions = {
        strokeStyle: "solid",
        strokeColor: "#18e04a",
        strokeOpacity: 1,
        strokeWeight: 2,
      };
      var rulerOptions = {
        startMarkerOptions: startMarkerOptions,
        midMarkerOptions: midMarkerOptions,
        endMarkerOptions: endMarkerOptions,
        lineOptions: lineOptions,
        startLabelText: "单击确定地点,双击结束",
      };
      const ruler = new AMap.RangingTool(map, rulerOptions); // 初始化插件
      this.mapRuler = ruler;
      AMap.event.addListener(ruler, "end", function(e) {
        ruler.turnOff();
      });
      // 添加雷达地图点击事件
      map.on("click", (ev) => {
        // console.log(ev,'---------ev-----470');
        if (this.activeIndex !== 10) return;
        if (this.cotrolsIndex === 3 || this.cotrolsIndex == 4) return;
        // 触发事件的地理坐标，AMap.LngLat 类型
        let lnglat = ev.lnglat;
        this.randaClickFlg = true;
        // 触发事件的像素坐标，AMap.Pixel 类型
        let pixel = ev.pixel;
        this.point = lnglat;
        this.setCircle();
        this.handleGetPollutionSource();
        const location = { lng: lnglat.lng, lat: lnglat.lat };
        this.$emit("randaClick", location);
        this.openRandaSite({
          lng: lnglat.lng,
          lat: lnglat.lat,
          x: pixel.x,
          y: pixel.y,
        });
      });
    },

    // 添加行政区外的覆盖物
    createOverlay(map) {
      const that = this;
      // 添加金牛区地理信息数据 3
      new AMap.DistrictSearch({
        extensions: "all",
        subdistrict: 0,
      }).search("金牛区", function(status, result) {
        console.log(result, "result");
        if (status !== "complete") return;
        // 外多边形坐标数组和内多边形坐标数组
        const outer = [
          new AMap.LngLat(-360, 90, true),
          new AMap.LngLat(-360, -90, true),
          new AMap.LngLat(360, -90, true),
          new AMap.LngLat(360, 90, true),
        ];
        const holes = result.districtList[0].boundaries;
        const pathArray = [outer];
        pathArray.push.apply(pathArray, holes);
        const polygon = new AMap.Polygon({
          pathL: pathArray,
          //线条颜色，使用16进制颜色代码赋值。默认值为#006600
          strokeColor: "rgb(255,255,255)",
          strokeWeight: 0,
          //轮廓线透明度，取值范围[0,1]，0表示完全透明，1表示不透明。默认为0.9
          strokeOpacity: 0,
          //多边形填充颜色，使用16进制颜色代码赋值，如：#FFAA00
          fillColor: "rgba(3,4,130)",
          // fillColor: "rgba(4,20,50)",
          // fillColor: "#0A1C5F",
          //多边形填充透明度，取值范围[0,1]，0表示完全透明，1表示不透明。默认为0.9
          fillOpacity: 0,
          //轮廓线样式，实线:solid，虚线:dashed
          strokeStyle: "solid",
          strokeDasharray: [10, 2, 10],
        });
        polygon.setPath(pathArray);
        map.add(polygon);
        //限制地图显示范围
        function lockMapBounds() {
          const bounds = map.getBounds();
          // map.setLimitBounds(bounds)
        }
        //启用地图范围限定
        lockMapBounds();
      });
    },
    // 地图热力图图层
    drawHeatMap() {
      this.heatMapLayer = new Loca.HeatmapLayer({
        map: this.maps,
      });
      const list = heatmapData.map((v) => {
        return {
          coordinate: [v.lng, v.lat],
          count: v.count,
        };
      });
      this.heatMapLayer
        .setData(list, {
          lnglat: "coordinate",
          value: "count",
        })
        .setOptions({
          style: {
            radius: 10,
            color: {
              0.5: "#2c7bb6",
              0.65: "#abd9e9",
              0.7: "#ffffbf",
              0.9: "#fde468",
              1.0: "#d7191c",
            },
          },
        })
        .render();
    },
    clearHeatMapLayer() {
      this.heatMapLayer.setMap(null);
    },
    // 添加街道划分区域地图数据
    creatGeojson(map) {
      const that = this;
      // 添加金牛区地理信息数据 1
      const geojson = new AMap.GeoJSON({
        geoJSON: jinniuStreet,
        getPolygon: function(geojson, lnglats) {
          // const area: any = AMap.GeometryUtil.ringArea(lnglats[0]);
          if (geojson.properties.name !== "金牛区") {
            const text = new AMap.Text({
              text: geojson.properties.name,
              anchor: "center", // 设置文本标记锚点
              draggable: false,
              cursor: "pointer",
              zIndex: 10,
              angle: 0,
              style: {
                padding: ".75rem 1.25rem",
                "margin-bottom": "1rem",
                "border-radius": ".25rem",
                "background-color": "transparent",
                "border-width": 0,
                "text-align": "center",
                "font-size": "14px",
                color: "#36E9EF",
              },
              position: [
                geojson.properties.center.lng,
                geojson.properties.center.lat,
              ],
            });
            text.setMap(map);

            const path = geojson.geometry.coordinates[0].map((item) =>
              item[0]
                ? new AMap.LngLat(item[0], item[1])
                : new AMap.LngLat(item.lng, item.lat)
            );
            const polygon = new AMap.Polygon({
              path: path,
              // strokeColor: "#0ea9f9",
              strokeColor: "#2fbeb5",
              strokeWeight: 3,
              strokeOpacity: 1,
              "pointer-events": "none",
              fillOpacity: 0, // 多边形填充透明度
              fillColor: "rgba(0,49,113, 0.15)",
              zIndex: 10,
            });
            map.add(polygon);
          }
        },
      });

      // 添加金牛区地理信息数据 2
      geojson.setMap(map);
    },
    //添加3D图层
    draw3dMap(map) {
      const object3Dlayer = new AMap.Object3DLayer();
      map.add(object3Dlayer);
      new AMap.GeoJSON({
        geoJSON: jinniuStreet,
        getPolygon: function(geojson, lnglats) {
          // const area: any = AMap.GeometryUtil.ringArea(lnglats[0]);
          if (geojson.properties.name !== "金牛区") {
            const bounds = geojson.geometry.coordinates[0].map((item) =>
              item[0]
                ? new AMap.LngLat(item[0], item[1])
                : new AMap.LngLat(item.lng, item.lat)
            );
            const height = -5000;
            const color = "rgba(47, 110, 238, 0.45)"; // rgba
            const prism = new AMap.Object3D.Prism({
              path: bounds,
              height: height,
              color: color,
            });

            prism.transparent = true;
            object3Dlayer.add(prism);
          }
        },
      });
    },
    // 清除雷达图层
    clearRandaLayer() {
      if (!this.maps) return;
      this.maps.remove(this.randaLayer);
      // this.maps.remove(this.randaLayer1)
      this.maps.clearMap();
      this.randaLayer = [];
      // this.randaLayer1 = null
    },
    // 名称获取初始雷达图
    getInitRanda() {
      this.isChoose = false;
      let type =
        this.randaType == "消光系数"
          ? "1"
          : this.randaType == "PM₁₀"
          ? "2"
          : this.randaType == "PM₂.₅"
          ? "3"
          : "4";
      if (this.randaLayer) {
        this.maps.remove(this.randaLayer);
        this.maps.clearMap(this);
        this.randaLayer = [];
      }
      getLastView({ type })
        .then((res) => {
          const { data } = res.data;
          this.isFirstRanda = true;
          this.randaClickFlg = false;
          if (!data) {
            this.clearRandaLayer();
            return;
          }
          this.groupFileName = data.groupFileName;
          this.$emit("getGroupFileName", data.groupFileName);
          this.$emit("randaExistDate", data.monitorTime);
          this.randaImg = data.imageUrl;
          this.randaAngle = data.angle;
          this.addCanvasLayer(this.maps, {
            lng: data.maxLng,
            lat: data.maxLat,
          });
        })
        .finally(() => {
          if (getTimer) {
            clearInterval(getTimer);
            getTimer = null;
          }
          if (this.randaAngle == 360) return;
          getTimer = setInterval(() => {
            this.getTwoAngleRanda();
          }, 20 * 1000);
        });
    },
    // 获取后2度角度雷达图
    getTwoAngleRanda() {
      let type =
        this.randaType == "消光系数"
          ? "1"
          : this.randaType == "PM₁₀"
          ? "2"
          : this.randaType == "PM₂.₅"
          ? "3"
          : "4";
      if (this.randaAngle == 360) return;
      getLastView({ angle: this.randaAngle + 2, type }).then((res) => {
        const { data } = res.data;
        this.isFirstRanda = false;
        if (!data) {
          return;
        }
        this.randaImg = data.imageUrl;
        this.randaAngle = data.angle;
        this.addCanvasLayer(this.maps, { lng: data.maxLng, lat: data.maxLat });
      });
    },
    // 添加canvas图层---雷达图
    addCanvasLayer(map, location) {
      // this.maps.setCenter([103.9843,30.72044])
      const _this = this;
      // const myCanvas1 = document.createElement('canvas');
      const myCanvas = document.createElement("canvas");
      const ctx = myCanvas.getContext("2d");
      // const ctx1 = myCanvas1.getContext("2d");
      myCanvas.setAttribute("width", 1400);
      myCanvas.setAttribute("height", 1400);
      // myCanvas1.setAttribute('width',1400)
      // myCanvas1.setAttribute('height',1400)
      let CanvasLayer = new AMap.CanvasLayer({
        canvas: myCanvas,
        bounds: new AMap.Bounds(
          [103.93181694808233, 30.67051246950482],
          [104.04134052340231, 30.765138086296666]
        ),
        // bounds: new AMap.Bounds([103.95, 30.65], [104.148, 30.808]),
        zooms: [3, 18],
        zIndex: 15,
      });
      this.randaLayer.push(CanvasLayer);
      CanvasLayer.setMap(map);
      const canvasWidth = ctx.canvas.width;
      const canvasHeight = ctx.canvas.height;
      //分成多少等份
      const num = 90;
      //半径
      const radius = 700;

      //一份多少弧度
      const angle = (Math.PI * 2) / num;

      //坐标原点
      const x0 = canvasWidth / 2;
      const y0 = canvasHeight / 2;
      // CanvasLayer1.setMap(map);
      const ag = Math.PI / 180; // 1度对应的弧度
      //上一次绘制的结束弧度等于当前次的起始弧度
      for (let i = 0; i < num; i++) {
        let startAngle = 0;
        let endAngle = 0;
        if (this.isFirstRanda && i <= this.randaAngle / 4) {
          startAngle = -Math.PI / 2;
          endAngle = (i + 1) * angle - Math.PI / 2;
          // _this.randaAngle = _this.randaAngle + 4
        } else if (this.isFirstRanda && i > this.randaAngle / 4) {
          startAngle = -Math.PI / 2;
          endAngle = ag * this.randaAngle - Math.PI / 2;
        } else if (!this.isFirstRanda) {
          startAngle = ag * (this.randaAngle - 2) - Math.PI / 2;
          endAngle = ag * this.randaAngle - Math.PI / 2;
        }
        this.randaTimer = setTimeout(() => {
          test(startAngle, endAngle);
          if (i >= 89) {
            if (!this.isFirstRanda || this.randaClickFlg) return;
            this.$emit("randaClick", location);
            this.openRandaSite(location, true);
          }
        }, i * 100);
      }
      // function test1(startAngle, endAngle,num) {
      //     // ctx1.clearRect(0, 0, 4000, 4000)
      //     ctx1.beginPath();
      //     ctx1.moveTo(x0, y0);
      //     ctx1.arc(x0, y0, radius, startAngle, endAngle)
      //     ctx1.lineTo(x0, y0, radius, startAngle, endAngle)
      //     ctx1.fillStyle = '#ffffff00';
      //     // ctx1.fill();
      //     // ctx1.strokeStyle="red"
      //     ctx1.strokeStyle='#ffffff00'
      //     ctx1.setLineDash([5,10]);
      //     ctx1.lineDashOffset=-50;
      //     ctx1.stroke()
      // }
      function test(startAngle, endAngle) {
        let fillImg = new Image();
        fillImg.src = _this.randaImg;
        fillImg.onload = function() {
          ctx.globalAlpha = 0.8;
          ctx.fillStyle = ctx.createPattern(fillImg, "repeat");
          ctx.clearRect(0, 0, 1400, 1400);
          ctx.beginPath();
          ctx.moveTo(x0, y0);
          ctx.arc(x0, y0, radius, startAngle, endAngle);
          ctx.fill();
        };
      }
      // this.randaLayer1 = CanvasLayer1
    },
    changeMaker() {
      if (this.activeIndex === 1) {
        if (this.makerList.length) {
          this.makerList.forEach((item) => {
            const data = item.w.data;
            if (data.printFactoryId === Number(this.currCompanyId)) {
              item.setLabel({
                offset: new AMap.Pixel(3, 22), //设置文本标注偏移量
                content: `<div class='${
                  data.vocExceedingStandard
                    ? "site-info-pollutantState"
                    : data.monitorDeviceState
                    ? "site-info-online"
                    : "site-info-offline"
                } site-info'></div>`, //设置文本标注内容
              });
              this.openGarage(item.w.data);
              // const icon = new AMap.Icon({
              //   // 图标尺寸
              //   size: new AMap.Size(23.8, 29.4),
              //   // 图标的取图地址
              //   image: markerIcon5,
              //   // 图标所用图片大小
              //   imageSize: new AMap.Size(23.8, 29.4),
              //   // 图标取图偏移量
              //   imageOffset: new AMap.Pixel(0, 0)
              // })
              // item.setIcon(icon)
              // this.openGarage(item.w.data)
              // if (this.currItem) {
              //   this.clearCarSelected()
              // }
              // this.createMark(data)
              this.$emit("clickMaker", { data: data, type: this.activeIndex });
              this.currItem = item;
            } else {
              item.setLabel({
                offset: new AMap.Pixel(3, 22), //设置文本标注偏移量
                content: "<div></div>", //设置文本标注内容
              });
            }
          });
        }
      } else if (this.activeIndex === 2) {
        if (this.makerList.length) {
          this.makerList.forEach((item) => {
            const data = item.w.data;
            if (data.garageId === Number(this.currCompanyId)) {
              item.setLabel({
                offset: new AMap.Pixel(3, 22), //设置文本标注偏移量
                content: `<div class='${
                  data.vocExceedingStandard
                    ? "site-info-pollutantState"
                    : data.monitorDeviceState
                    ? "site-info-online"
                    : "site-info-offline"
                } site-info'></div>`, //设置文本标注内容
              });
              this.openGarage(item.w.data);
              // const icon = new AMap.Icon({
              //   // 图标尺寸
              //   size: new AMap.Size(23.8, 29.4),
              //   // 图标的取图地址
              //   image: markerIcon6,
              //   // 图标所用图片大小
              //   imageSize: new AMap.Size(23.8, 29.4),
              //   // 图标取图偏移量
              //   imageOffset: new AMap.Pixel(0, 0)
              // })
              // item.setIcon(icon)
              // this.openGarage(item.w.data)
              // // this.createMark(data)
              // if (this.currItem) {
              //   this.clearCarSelected()
              // }
              this.$emit("clickMaker", { data: data, type: this.activeIndex });
              this.currItem = item;
            } else {
              item.setLabel({
                offset: new AMap.Pixel(3, 22), //设置文本标注偏移量
                content: "<div></div>", //设置文本标注内容
              });
            }
          });
        }
      } else if (this.activeIndex === 4) {
        if (this.makerList.length) {
          this.makerList.forEach((item) => {
            const data = item.w.data;
            if (data.gasId === this.currCompanyId) {
              item.setLabel({
                offset: new AMap.Pixel(3, 22), //设置文本标注偏移量
                content: `<div class='${
                  data.gasConcentrationState
                    ? "site-info-pollutantState"
                    : data.online
                    ? "site-info-online"
                    : "site-info-offline"
                } site-info'></div>`, //设置文本标注内容
              });
              this.openRestaurant(item.w.data);
              // const icon = new AMap.Icon({
              //   // 图标尺寸
              //   size: new AMap.Size(23.8, 29.4),
              //   // 图标的取图地址
              //   image: markerIcon8,
              //   // 图标所用图片大小
              //   imageSize: new AMap.Size(23.8, 29.4),
              //   // 图标取图偏移量
              //   imageOffset: new AMap.Pixel(0, 0)
              // })
              // item.setIcon(icon)
              // this.openRestaurant(item.w.data)
              // if (this.currItem) {
              //   this.clearCarSelected()
              // }
              this.$emit("clickMaker", { data: data, type: this.activeIndex });
              this.currItem = item;
            } else {
              item.setLabel({
                offset: new AMap.Pixel(3, 22), //设置文本标注偏移量
                content: "<div></div>", //设置文本标注内容
              });
            }
          });
        }
      } else if (this.activeIndex === 3) {
        if (this.makerList.length) {
          this.makerList.forEach((item, index) => {
            const data = item.w.data;
            if (data.id === this.currCompanyId) {
              item.setLabel({
                offset: new AMap.Pixel(3, 22), //设置文本标注偏移量
                content: `<div class='${
                  data.isAlarm
                    ? "site-info-pollutantState"
                    : data.monitorDeviceState
                    ? "site-info-online"
                    : "site-info-offline"
                } site-info'></div>`, //设置文本标注内容
              });
              this.openRestaurant(item.w.data);
              // if(index){
              this.maps.setCenter([data.lng, data.lat]);
              this.maps.setZoom(13.7);
              // }
              // const icon = new AMap.Icon({
              //   // 图标尺寸
              //   size: new AMap.Size(40, 80),
              //   // 图标的取图地址
              //   image: data.isAlarm === 0 ? markerIcon7 : markerIcon9,
              //   // 图标所用图片大小
              //   imageSize: new AMap.Size(40, 80),
              //   // 图标取图偏移量
              //   imageOffset: new AMap.Pixel(0, 0)
              // })
              // this.openRestaurant(item.w.data)
              // item.setIcon(icon)
              // if (this.currItem) {
              //   this.clearCarSelected()
              // }
              this.$emit("clickMaker", { data: data, type: this.activeIndex });
              this.currItem = item;
            } else {
              item.setLabel({
                offset: new AMap.Pixel(3, 22), //设置文本标注偏移量
                content: "<div></div>", //设置文本标注内容
              });
            }
          });
        }
      } else if (this.activeIndex === 5) {
        this.waterBuildingMarkerList.forEach((item, index) => {
          const data = item.w.data;
          if (data.value === this.currentSelectedBuilding) {
            const icon = new AMap.Icon({
              // 图标尺寸
              size: new AMap.Size(32, 28),
              // 图标的取图地址
              image: buildingActiveIconPic,
              // 图标所用图片大小
              imageSize: new AMap.Size(32, 28),
              // 图标取图偏移量
              imageOffset: new AMap.Pixel(0, 0),
            });
            const qsyMarker = new AMap.Icon({
              // 图标尺寸
              size: new AMap.Size(23.8, 29.4),
              // 图标的取图地址
              image: qsyIcon,
              // 图标所用图片大小
              imageSize: new AMap.Size(23.8, 29.4),
              // 图标取图偏移量
              imageOffset: new AMap.Pixel(0, 0),
            });
            item.setIcon(qsyMarker);
            item.setLabel({
              offset: new AMap.Pixel(3, 22), //设置文本标注偏移量
              content: `<div class='site-info-online site-info'></div>`, //设置文本标注内容
            });
            // if (this.currItem) {
            //   this.clearCarSelected()
            // }
            this.currItem = item;
            // if(index){
            this.maps.setCenter([data.lng, data.lat]);
            this.maps.setZoom(14.8);
            // }
            // if (this.text !== null && this.enterpriseMarker) {
            //   this.maps.remove(this.text);
            //   this.maps.remove(this.enterpriseMarker);
            //   this.text = null;
            //   this.enterpriseMarker = null;
            // }
            // const enterprise = new AMap.Icon({
            //   size: new AMap.Size(144, 27),
            //   image: mc,
            //   imageSize: new AMap.Size(144, 27),
            //   imageOffset: new AMap.Pixel(0, 0)
            // });
            // this.enterpriseMarker = new AMap.Marker({
            //   map: this.maps,
            //   icon: enterprise,
            //   zIndex: 999,
            //   position: [data.lng, data.lat],
            //   offset: new AMap.Pixel(-72, 15)
            // });
            // this.text = new AMap.Text({
            //   text: data.name,
            //   anchor: "center", // 设置文本标记锚点
            //   cursor: "pointer",
            //   zIndex: 9999,
            //   style: {
            //     width: "144px",
            //     height: "27px",
            //     "background-color": "transparent",
            //     "border-width": 0,
            //     "text-align": "center",
            //     "font-size": "14px",
            //     color: "#fff"
            //   },
            //   position: [data.lng, data.lat],
            //   offset: new AMap.Pixel(0, 30)
            // });
            // this.text.setMap(this.maps);
          } else {
            item.setLabel({
              offset: new AMap.Pixel(3, 22), //设置文本标注偏移量
              content: `<div></div>`, //设置文本标注内容
            });
          }
        });
      } else if (this.activeIndex === 0) {
        this.isOpen = true;
        this.siteMarkerList.forEach((items, index) => {
          const item = items.w.data;
          if (
            item.companyId.toString() ===
            this.currentSelectedCompanyId.toString()
          ) {
            items.setLabel({
              offset: item.siteId
                ? new AMap.Pixel(20, 18)
                : new AMap.Pixel(3, 22), //设置文本标注偏移量
              content: `<div class='${
                item.pollutantState
                  ? "site-info-pollutantState"
                  : item.online
                  ? "site-info-online"
                  : "site-info-offline"
              } site-info'></div>`, //设置文本标注内容
            });
            this.openSite(item);
            this.maps.setCenter([item.gcLng, item.gcLat]);
            this.maps.setZoom(14.8);
          } else if (!item.siteId) {
            items.setLabel({
              offset: new AMap.Pixel(3, 22), //设置文本标注偏移量
              content: "<div></div>", //设置文本标注内容
            });
          } else if (item.siteId) {
            items.setLabel({
              offset: new AMap.Pixel(-55, -32), //设置文本标注偏移量
              content: `<div class="top_Mcontent">${item.alias}</div>`, //设置文本标注内容
            });
          }
        });
      } else if (this.activeIndex === 6) {
        this.isOpen = true;
        this.siteMarkerList.forEach((items, index) => {
          const item = items.w.data;
          if (
            item.companyId.toString() ===
            this.currentSelectedCompanyId.toString()
          ) {
            items.setLabel({
              offset: new AMap.Pixel(3, 22), //设置文本标注偏移量
              content: `<div class='${
                item.isAlarm
                  ? "site-info-pollutantState"
                  : item.online
                  ? "site-info-online"
                  : "site-info-offline"
              } site-info'></div>`, //设置文本标注内容
            });
            this.openNoiseSite(item);
            // if(index){
            this.maps.setCenter([item.longitude, item.latitude]);
            this.maps.setZoom(13.7);
            // }
          } else {
            items.setLabel({
              offset: new AMap.Pixel(3, 22), //设置文本标注偏移量
              content: "<div></div>", //设置文本标注内容
            });
          }
        });
      } else if (this.activeIndex === 7) {
        this.isOpen = true;
        this.siteMarkerList.forEach((items, index) => {
          const item = items.w.data;
          if (
            item.companyId.toString() ===
            this.currentSelectedCompanyId.toString()
          ) {
            items.setLabel({
              offset: new AMap.Pixel(3, 22), //设置文本标注偏移量
              content: `<div class='${
                item.isAlarm
                  ? "site-info-pollutantState"
                  : item.online
                  ? "site-info-online"
                  : "site-info-offline"
              } site-info'></div>`, //设置文本标注内容
            });
            this.openElectricitySite(item);
            // if(index){
            this.maps.setCenter([item.longitude, item.latitude]);
            this.maps.setZoom(13.6);
            // }
          } else {
            items.setLabel({
              offset: new AMap.Pixel(3, 22), //设置文本标注偏移量
              content: "<div></div>", //设置文本标注内容
            });
          }
        });
      } else if (this.activeIndex === 8) {
        this.isOpen = true;
        this.siteMarkerList.forEach((items, index) => {
          const item = items.w.data;
          if (
            item.companyId.toString() ===
            this.currentSelectedCompanyId.toString()
          ) {
            items.setLabel({
              offset: new AMap.Pixel(3, 22), //设置文本标注偏移量
              content: `<div class='${
                item.isAlarm
                  ? "site-info-pollutantState"
                  : item.online
                  ? "site-info-online"
                  : "site-info-offline"
              } site-info'></div>`, //设置文本标注内容
            });
            this.openSolidWasteSite(item);
            // if(index){
            this.maps.setCenter([item.lng, item.lat]);
            this.maps.setZoom(14.2);
            // }
          } else {
            items.setLabel({
              offset: new AMap.Pixel(3, 22), //设置文本标注偏移量
              content: "<div></div>", //设置文本标注内容
            });
          }
        });
      } else if (this.activeIndex === 9) {
        this.isOpen = true;
        this.siteMarkerList.forEach((items, index) => {
          const item = items.w.data;
          if (
            item.companyId.toString() ===
            this.currentSelectedCompanyId.toString()
          ) {
            items.setLabel({
              offset: new AMap.Pixel(3, 22), //设置文本标注偏移量
              content: `<div class='${
                item.isAlarm
                  ? "site-info-pollutantState"
                  : item.online
                  ? "site-info-online"
                  : "site-info-offline"
              } site-info'></div>`, //设置文本标注内容
            });
            this.openHeavyCorruptSite(item);
            // if(index){
            this.maps.setZoom(12.9);
            this.maps.setCenter([item.longitude, item.latitude]);
            // }
          } else {
            items.setLabel({
              offset: new AMap.Pixel(3, 22), //设置文本标注偏移量
              content: "<div></div>", //设置文本标注内容
            });
          }
        });
      } else if (this.activeIndex === 13) {
        this.isOpen = true;
        this.siteMarkerList.forEach((items, index) => {
          const item = items.w.data;
          if (
            item.companyId.toString() ===
            this.currentSelectedCompanyId.toString()
          ) {
            items.setLabel({
              offset: new AMap.Pixel(3, 22), //设置文本标注偏移量
              content: `<div class='${
                item.isAlarm
                  ? "site-info-pollutantState"
                  : item.online
                  ? "site-info-online"
                  : "site-info-offline"
              } site-info'></div>`, //设置文本标注内容
            });
            this.openExhaustGasTruckSite(item);
            // if(index){
            this.maps.setZoom(13.6);
            this.maps.setCenter([item.lng, item.lat]);
            // }
          } else {
            items.setLabel({
              offset: new AMap.Pixel(3, 22), //设置文本标注偏移量
              content: "<div></div>", //设置文本标注内容
            });
          }
        });
      }
    },
    clearCarSelected(items) {
      let markerImg = "";
      if (this.activeIndex === 1) {
        markerImg = markerIcon1;
      } else if (this.activeIndex === 2) {
        markerImg = markerIcon2;
      } else if (this.activeIndex === 3) {
        markerImg = markerIcon3;
      } else if (this.activeIndex === 4) {
        markerImg = markerIcon4;
      } else if (this.activeIndex === 5) {
        markerImg = buildingIconPic;
      } else if (this.activeIndex === 0) {
        if (items) {
          const item = items.w.data;
          // 超标
          const chaobiaoMarker = new AMap.Icon({
            size: new AMap.Size(23.8, 29.4),
            image: chaobiao,
            imageSize: new AMap.Size(23.8, 29.4),
            imageOffset: new AMap.Pixel(0, 0),
          });
          // 未超标
          const weichaobiaoMarker = new AMap.Icon({
            size: new AMap.Size(23.8, 29.4),
            image: weichaobiao,
            imageSize: new AMap.Size(23.8, 29.4),
            imageOffset: new AMap.Pixel(0, 0),
          });
          // 掉线
          const diaoxianMarker = new AMap.Icon({
            size: new AMap.Size(23.8, 29.4),
            image: diaoxian,
            imageSize: new AMap.Size(23.8, 29.4),
            imageOffset: new AMap.Pixel(0, 0),
          });
          // 摄像头
          const sxt = new AMap.Icon({
            size: new AMap.Size(23.8, 29.4),
            image: stationIconPic,
            imageSize: new AMap.Size(23.8, 29.4),
            imageOffset: new AMap.Pixel(0, 0),
          });
          if (item.cameraCount) {
            // 摄像头
            items.setIcon(sxt);
          } else if (item.online) {
            // 判断是否掉线
            if (item.pollutantState) {
              // 超标选中
              this.closeInfoWindow();
              items.setIcon(chaobiaoMarker);
            } else {
              // 未超标选中
              this.closeInfoWindow();
              items.setIcon(weichaobiaoMarker);
            }
          } else {
            items.setIcon(diaoxianMarker);
          }
        }
      } else if (this.activeIndex === 6) {
        if (items) {
          const item = items.w.data;
          // 超标
          const chaobiaoMarker = new AMap.Icon({
            size: new AMap.Size(23.8, 29.4),
            image: noiseOver,
            imageSize: new AMap.Size(23.8, 29.4),
            imageOffset: new AMap.Pixel(0, 0),
          });
          // 未超标
          const weichaobiaoMarker = new AMap.Icon({
            size: new AMap.Size(23.8, 29.4),
            image: noiseNormal,
            imageSize: new AMap.Size(23.8, 29.4),
            imageOffset: new AMap.Pixel(0, 0),
          });
          // 掉线
          const diaoxianMarker = new AMap.Icon({
            size: new AMap.Size(23.8, 29.4),
            image: noiseOffline,
            imageSize: new AMap.Size(23.8, 29.4),
            imageOffset: new AMap.Pixel(0, 0),
          });
          // 摄像头
          const sxt = new AMap.Icon({
            size: new AMap.Size(23.8, 29.4),
            image: stationIconPic,
            imageSize: new AMap.Size(23.8, 29.4),
            imageOffset: new AMap.Pixel(0, 0),
          });
          if (item.cameraCount) {
            // 摄像头
            items.setIcon(sxt);
          } else if (item.online) {
            // 判断是否掉线
            if (item.pollutantState) {
              // 超标选中
              this.closeInfoWindow();
              items.setIcon(chaobiaoMarker);
            } else {
              // 未超标选中
              this.closeInfoWindow();
              items.setIcon(weichaobiaoMarker);
            }
          } else {
            items.setIcon(diaoxianMarker);
          }
        }
      }
      if (this.activeIndex !== 0 || this.activeIndex !== 6) {
        const icon = new AMap.Icon({
          // 图标尺寸
          size: new AMap.Size(23.8, 29.4),
          // 图标的取图地址
          image: markerImg,
          // 图标所用图片大小
          imageSize: new AMap.Size(23.8, 29.4),
          // 图标取图偏移量
          imageOffset: new AMap.Pixel(0, 0),
        });
        this.currItem.setIcon(icon);
        this.$emit("close");
      }
    },
    // 添加marker
    addMarkers() {
      if (![0, 5, 6, 7, 8, 9, 13].includes(this.activeIndex)) {
        for (const item of this.markerList) {
          let markerImg = "";
          if (this.activeIndex === 1) {
            markerImg = item.vocExceedingStandard
              ? ysglIcon
              : item.monitorDeviceState
              ? yszcIcon
              : yslxIcon;
          } else if (this.activeIndex === 2) {
            markerImg = item.vocExceedingStandard
              ? qxgjIcon
              : item.monitorDeviceState
              ? qxzcIcon
              : qxlxIcon;
          } else if (this.activeIndex === 3) {
            markerImg =
              // item.isAlarm
              //   ? yygjIcon
              //   :
              item.monitorDeviceState && item.isAlarm
                ? yygjIcon
                : item.monitorDeviceState && !item.isAlarm
                ? yyzcIcon
                : yylxIcon;
          } else if (this.activeIndex === 4) {
            markerImg = item.gasConcentrationState
              ? jygjIcon
              : item.online
              ? jyzcIcon
              : jylxIcon;
          }
          const icon = new AMap.Icon({
            // 图标尺寸
            size: new AMap.Size(23.8, 29.4),
            // 图标的取图地址
            image: markerImg,
            // 图标所用图片大小
            imageSize: new AMap.Size(23.8, 29.4),
            // 图标取图偏移量
            imageOffset: new AMap.Pixel(0, 0),
          });
          const marker = new AMap.Marker({
            map: this.maps,
            position:
              this.activeIndex === 4
                ? new AMap.LngLat(Number(item.longitude), Number(item.latitude))
                : new AMap.LngLat(Number(item.lng), Number(item.lat)),
            icon: icon,
            offset: new AMap.Pixel(-11.9, -14.7),
            data: item,
          });
          this.makerList.push(marker);
          // marker.hide()
          AMap.event.addListener(marker, "click", (e) => {
            const data = e.target.w.data;
            this.maps.setCenter([data.lng, data.lat]);
            let id =
              this.activeIndex == 1
                ? data.printFactoryCamera.printFactoryId
                : this.activeIndex == 2
                ? data.garageId
                : this.activeIndex == 3
                ? data.id
                : data.gasId;
            this.$emit("clickMaker", {
              data: data,
              type: this.activeIndex,
            });
            if (id == this.currCompanyId) {
              this.changeMaker();
            }
          });
        }
      } else {
        if (this.activeIndex === 0) {
          this.isOpen = true;
          this.setConstructionSiteMarker();
        } else if (this.activeIndex === 5) {
          this.creatBuildingMarker();
        } else if (this.activeIndex === 6) {
          this.isOpen = true;
          this.createNoiseMarker();
        } else if (this.activeIndex === 7) {
          this.isOpen = true;
          this.createElectricityMarker();
          // this.clearHeatMapLayer()
        } else if (this.activeIndex === 8) {
          this.isOpen = true;
          this.createSolidWasteMarker();
          this.heatmapData = heatmapData;
          // this.drawHeatMap()
        } else if (this.activeIndex === 9) {
          this.isOpen = true;
          this.createHeavCorruptMarker();
        } else if (this.activeIndex === 13) {
          this.isOpen = true;
          this.createExhaustGasTruckMarker();
        }
      }
    },
    // 创建每个maker下面的备注
    createMark(params) {
      if (this.text !== null && this.enterpriseMarker) {
        this.maps.remove(this.text);
        this.maps.remove(this.enterpriseMarker);
        this.text = null;
        this.enterpriseMarker = null;
      }
      const enterprise = new AMap.Icon({
        size: new AMap.Size(144, 27),
        image: mc,
        imageSize: new AMap.Size(144, 27),
        imageOffset: new AMap.Pixel(0, 0),
      });
      this.enterpriseMarker = new AMap.Marker({
        map: this.maps,
        icon: enterprise,
        zIndex: 999,
        position: [params.lng, params.lat],
        offset: new AMap.Pixel(-72, 15),
      });
      this.text = new AMap.Text({
        text:
          this.activeIndex === 1
            ? params.printFactoryName
            : this.activeIndex === 2
            ? params.garageName
            : "",
        anchor: "center", // 设置文本标记锚点
        cursor: "pointer",
        zIndex: 9999,
        style: {
          width: "144px",
          height: "27px",
          "background-color": "transparent",
          "border-width": 0,
          "text-align": "center",
          "font-size": "14px",
          color: "#fff",
        },
        position: [params.lng, params.lat],
        offset: new AMap.Pixel(0, 30),
      });
      this.text.setMap(this.maps);
    },
    creatBuildingMarker() {
      // if (this.factoryTextClear !== null) {
      //   this.maps.remove(this.factoryTextClear);
      //   this.maps.remove(this.factoryMarkerClear);
      //   this.factoryTextClear = null;
      //   this.factoryMarkerClear = null;
      // }
      // if (this.fiveTextClear !== null) {
      //   this.maps.remove(this.fiveMarkerClear);
      //   this.maps.remove(this.fiveTextClear);
      //   this.fiveMarkerClear = null;
      //   this.fiveTextClear = null;
      // }
      // if (this.waterBuildingMarkerList.length !== 0) {
      //   this.maps.remove(this.waterBuildingMarkerList);
      //   this.waterBuildingMarkerList = [];
      // }
      // 重污染企业
      const building = new AMap.Icon({
        // 图标尺寸
        size: new AMap.Size(32, 32),
        // 图标的取图地址
        image: buildingIconPic,
        // 图标所用图片大小
        imageSize: new AMap.Size(32, 32),
        // 图标取图偏移量
        imageOffset: new AMap.Pixel(0, 0),
      });
      const qsyMarker = new AMap.Icon({
        // 图标尺寸
        size: new AMap.Size(23.8, 29.4),
        // 图标的取图地址
        image: qsyIcon,
        // 图标所用图片大小
        imageSize: new AMap.Size(23.8, 29.4),
        // 图标取图偏移量
        imageOffset: new AMap.Pixel(0, 0),
      });
      // 重污染企业Active
      const buildingActiveIcon = new AMap.Icon({
        // 图标尺寸
        size: new AMap.Size(32, 28),
        // 图标的取图地址
        image: buildingActiveIconPic,
        // 图标所用图片大小
        imageSize: new AMap.Size(32, 28),
        // 图标取图偏移量
        imageOffset: new AMap.Pixel(0, 0),
      });
      // 水五（七）厂
      const shuichangBg = new AMap.Icon({
        // 图标尺寸
        size: new AMap.Size(111, 39),
        // 图标的取图地址
        image: shuichang,
        // 图标所用图片大小
        imageSize: new AMap.Size(111, 39),
        // 图标取图偏移量
        imageOffset: new AMap.Pixel(0, 0),
      });
      this.markerList.forEach((build, index) => {
        let waterbuildingMarker;
        if (this.currentSelectedBuilding === build.value) {
          waterbuildingMarker = new AMap.Marker({
            map: this.maps,
            icon: qsyMarker,
            position: [build.lng, build.lat],
            offset: new AMap.Pixel(-16, -14),
            data: build,
            zIndex: 9999,
          });
          waterbuildingMarker.setLabel({
            offset: new AMap.Pixel(3, 22), //设置文本标注偏移量
            content: `<div class='site-info-online site-info'></div>`, //设置文本标注内容
          });
          this.currItem = waterbuildingMarker;
          if (this.text !== null && this.enterpriseMarker) {
            this.maps.remove(this.text);
            this.maps.remove(this.enterpriseMarker);
            this.text = null;
            this.enterpriseMarker = null;
          }
          // const enterprise = new AMap.Icon({
          //   size: new AMap.Size(144, 27),
          //   image: mc,
          //   imageSize: new AMap.Size(144, 27),
          //   imageOffset: new AMap.Pixel(0, 0)
          // });
          // this.enterpriseMarker = new AMap.Marker({
          //   map: this.maps,
          //   icon: enterprise,
          //   zIndex: 999,
          //   position: [build.lng, build.lat],
          //   offset: new AMap.Pixel(-72, 15)
          // });
          // this.text = new AMap.Text({
          //   text: build.name,
          //   anchor: "center", // 设置文本标记锚点
          //   cursor: "pointer",
          //   zIndex: 9999,
          //   style: {
          //     width: "144px",
          //     height: "27px",
          //     "background-color": "transparent",
          //     "border-width": 0,
          //     "text-align": "center",
          //     "font-size": "14px",
          //     color: "#fff"
          //   },
          //   position: [build.lng, build.lat],
          //   offset: new AMap.Pixel(0, 30)
          // });
          // this.text.setMap(this.maps);
        } else {
          waterbuildingMarker = new AMap.Marker({
            map: this.maps,
            icon: qsyMarker,
            position: [build.lng, build.lat],
            offset: new AMap.Pixel(-16, -16),
            data: build,
            zIndex: 9999,
          });
          waterbuildingMarker.setLabel({
            offset: new AMap.Pixel(3, 22), //设置文本标注偏移量
            content: `<div></div>`, //设置文本标注内容
          });
        }
        AMap.event.addListener(
          waterbuildingMarker,
          "click",
          this.buildMapClick
        );
        this.waterBuildingMarkerList.push(waterbuildingMarker);
        // 水七厂
        if (build.value == "510100000004") {
          this.factoryMarkerClear = new AMap.Marker({
            map: this.maps,
            icon: shuichangBg,
            zIndex: 999999,
            position: [build.lng, build.lat],
            offset: new AMap.Pixel(-55.5, -50),
            data: marker,
          });
          this.factoryTextClear = new AMap.Text({
            text: "水七厂",
            anchor: "center", // 设置文本标记锚点
            cursor: "pointer",
            zIndex: 9999999,
            style: {
              width: "111px",
              height: "39px",
              "background-color": "transparent",
              "border-width": 0,
              "text-align": "center",
              "font-size": "16px",
              color: "#fff",
            },
            position: [build.lng, build.lat],
            offset: new AMap.Pixel(0, -30),
          });
          this.factoryTextClear.setMap(this.maps);
        } else if (build.value == "0028CDSCDSWC01") {
          // 水五厂
          this.fiveMarkerClear = new AMap.Marker({
            map: this.maps,
            icon: shuichangBg,
            zIndex: 999999,
            position: [build.lng, build.lat],
            offset: new AMap.Pixel(-55.5, -50),
            data: marker,
          });
          this.fiveTextClear = new AMap.Text({
            text: "水五厂",
            anchor: "center", // 设置文本标记锚点
            cursor: "pointer",
            zIndex: 9999999,
            style: {
              width: "111px",
              height: "39px",
              "background-color": "transparent",
              "border-width": 0,
              "text-align": "center",
              "font-size": "16px",
              color: "#fff",
            },
            position: [build.lng, build.lat],
            offset: new AMap.Pixel(0, -30),
          });
          this.fiveTextClear.setMap(this.maps);
        }
        if (index) {
          this.maps.setCenter([build.lng, build.lat]);
        }
      });
    },
    buildMapClick(data) {
      // if (this.currItem) {
      //   this.clearCarSelected()
      // }
      const map = this.maps;
      if (data.target) {
        const data1 = data.target.w.data;
        // this.currentSelectedBuilding = data.target.w.data.value;
        this.curMarkerClick = "";
        // this.creatBuildingMarker();
        // this.setConstructionSiteMarker();
        this.$emit("curCompanyChange", data.target.w.data.value);
        if (this.text !== null && this.enterpriseMarker) {
          map.remove(this.text);
          map.remove(this.enterpriseMarker);
          this.text = null;
          this.enterpriseMarker = null;
        }
        const qsyMarker = new AMap.Icon({
          // 图标尺寸
          size: new AMap.Size(23.8, 29.4),
          // 图标的取图地址
          image: qsyIcon,
          // 图标所用图片大小
          imageSize: new AMap.Size(23.8, 29.4),
          // 图标取图偏移量
          imageOffset: new AMap.Pixel(0, 0),
        });
        this.waterBuildingMarkerList.forEach((item) => {
          if (item == data.target) {
            item.setLabel({
              offset: new AMap.Pixel(3, 22), //设置文本标注偏移量
              content: `<div class='site-info-online site-info'></div>`, //设置文本标注内容
            });
          } else {
            item.setLabel({
              offset: new AMap.Pixel(3, 22), //设置文本标注偏移量
              content: `<div></div>`, //设置文本标注内容
            });
          }
        });
        this.maps.setCenter([data1.lng, data1.lat]);
        // const enterprise = new AMap.Icon({
        //   size: new AMap.Size(144, 27),
        //   image: mc,
        //   imageSize: new AMap.Size(144, 27),
        //   imageOffset: new AMap.Pixel(0, 0)
        // });
        // this.enterpriseMarker = new AMap.Marker({
        //   map: this.maps,
        //   icon: enterprise,
        //   zIndex: 999,
        //   position: [data.target.w.data.lng, data.target.w.data.lat],
        //   offset: new AMap.Pixel(-72, 15)
        // });
        // this.text = new AMap.Text({
        //   text: data.target.w.data.name,
        //   anchor: "center", // 设置文本标记锚点
        //   cursor: "pointer",
        //   zIndex: 9999,
        //   style: {
        //     width: "144px",
        //     height: "27px",
        //     "background-color": "transparent",
        //     "border-width": 0,
        //     "text-align": "center",
        //     "font-size": "14px",
        //     color: "#fff"
        //   },
        //   position: [data.target.w.data.lng, data.target.w.data.lat],
        //   offset: new AMap.Pixel(0, 30)
        // });
        // this.text.setMap(this.maps);
      }
    },
    setConstructionSiteMarker() {
      if (this.siteMarkerList.length !== 0) {
        this.maps.remove(this.siteMarkerList);
        this.siteMarkerList = [];
      }
      // 超标
      const chaobiaoMarker = new AMap.Icon({
        size: new AMap.Size(23.8, 29.4),
        image: chaobiao,
        imageSize: new AMap.Size(23.8, 29.4),
        imageOffset: new AMap.Pixel(0, 0),
      });
      // 超标选中
      // const chaobiaoActiveMarker = new AMap.Icon({
      //   size: new AMap.Size(23.8, 29.4),
      //   image: chaobiaoActive,
      //   imageSize: new AMap.Size(23.8, 29.4),
      //   imageOffset: new AMap.Pixel(0, 0)
      // });
      // 未超标
      const weichaobiaoMarker = new AMap.Icon({
        size: new AMap.Size(23.8, 29.4),
        image: weichaobiao,
        imageSize: new AMap.Size(23.8, 29.4),
        imageOffset: new AMap.Pixel(0, 0),
      });
      // 未超标选中
      // const weichaobiaoActiveMarker = new AMap.Icon({
      //   size: new AMap.Size(23.8, 29.4),
      //   image: weichaobiaoActive,
      //   imageSize: new AMap.Size(23.8, 29.4),
      //   imageOffset: new AMap.Pixel(0, 0)
      // });
      // 掉线
      const diaoxianMarker = new AMap.Icon({
        size: new AMap.Size(23.8, 29.4),
        image: diaoxian,
        imageSize: new AMap.Size(23.8, 29.4),
        imageOffset: new AMap.Pixel(0, 0),
      });
      // 未掉线
      const diaoxianActiveMarker = new AMap.Icon({
        size: new AMap.Size(23.8, 29.4),
        image: diaoxianActive,
        imageSize: new AMap.Size(23.8, 29.4),
        imageOffset: new AMap.Pixel(0, 0),
      });
      // 摄像头
      const sxt = new AMap.Icon({
        size: new AMap.Size(23.8, 29.4),
        image: stationIconPic,
        imageSize: new AMap.Size(23.8, 29.4),
        imageOffset: new AMap.Pixel(0, 0),
      });

      // 引领性工地
      const GDI = new AMap.Icon({
        size: new AMap.Size(60, 41),
        image: Gdicon,
        imageSize: new AMap.Size(60, 41),
        imageOffset: new AMap.Pixel(0, 0),
      });
      this.markerList.forEach((item, index) => {
        let siteMarkerItem;
        if (item.cameraCount) {
          // 摄像头
          siteMarkerItem = new AMap.Marker({
            map: this.maps,
            icon: sxt,
            position: [item.gcLng, item.gcLat],
            offset:
              item.companyId == this.currentSelectedCompanyId
                ? new AMap.Pixel(-15.5, -15.5)
                : new AMap.Pixel(-15.5, -15.5),
            data: item,
            zIndex: 19,
          });
        } else if (item.online) {
          // 判断是否掉线
          if (item.companyId == this.currentSelectedCompanyId) {
            if (item.pollutantState) {
              // 超标选中
              siteMarkerItem = new AMap.Marker({
                map: this.maps,
                icon: chaobiaoMarker,
                position: [item.gcLng, item.gcLat],
                offset:
                  item.companyId == this.currentSelectedCompanyId
                    ? new AMap.Pixel(-15.5, -15.5)
                    : new AMap.Pixel(-15.5, -15.5),
                data: item,
                zIndex: 19,
              });
              siteMarkerItem.setLabel({
                offset: new AMap.Pixel(3, 22), //设置文本标注偏移量
                content: `<div class='${
                  item.pollutantState
                    ? "site-info-pollutantState"
                    : item.online
                    ? "site-info-online"
                    : "site-info-offline"
                } site-info'></div>`, //设置文本标注内容
              });
            } else {
              // 未超标选中
              siteMarkerItem = new AMap.Marker({
                map: this.maps,
                icon: weichaobiaoMarker,
                position: [item.gcLng, item.gcLat],
                offset:
                  item.companyId == this.currentSelectedCompanyId
                    ? new AMap.Pixel(-11.9, -14.7)
                    : new AMap.Pixel(-15.5, -15.5),
                data: item,
                zIndex: 19,
              });
              siteMarkerItem.setLabel({
                offset: new AMap.Pixel(3, 22), //设置文本标注偏移量
                content: `<div class='${
                  item.pollutantState
                    ? "site-info-pollutantState"
                    : item.online
                    ? "site-info-online"
                    : "site-info-offline"
                } site-info'></div>`, //设置文本标注内容
              });
            }
            this.currItem = siteMarkerItem;
            this.openSite(item);
          } else {
            if (item.pollutantState) {
              // 超标
              siteMarkerItem = new AMap.Marker({
                map: this.maps,
                icon: chaobiaoMarker,
                position: [item.gcLng, item.gcLat],
                offset:
                  item.companyId == this.currentSelectedCompanyId
                    ? new AMap.Pixel(-15.5, -15.5)
                    : new AMap.Pixel(-15.5, -15.5),
                data: item,
                zIndex: 19,
              });
            } else {
              // 未超标
              siteMarkerItem = new AMap.Marker({
                map: this.maps,
                icon: weichaobiaoMarker,
                position: [item.gcLng, item.gcLat],
                offset:
                  item.companyId == this.currentSelectedCompanyId
                    ? new AMap.Pixel(-15.5, -15.5)
                    : new AMap.Pixel(-15.5, -15.5),
                data: item,
                zIndex: 19,
              });
            }
          }
        } else if (item.siteId) {
          /* 引领性工地 */
          siteMarkerItem = new AMap.Marker({
            map: this.maps,
            icon: GDI,
            position: [item.gcLng, item.gcLat],
            // offset:new AMap.Pixel(-15.5, -15.5),
            data: item,
            zIndex: 19,
          });
          siteMarkerItem.setLabel({
            offset: new AMap.Pixel(-55, -32), //设置文本标注偏移量
            content: `<div class="top_Mcontent">${item.alias}</div>`, //设置文本标注内容
          });
        } else {
          if (item.companyId == this.currentSelectedCompanyId) {
            // 掉线选中
            siteMarkerItem = new AMap.Marker({
              map: this.maps,
              icon: diaoxianMarker,
              position: [item.gcLng, item.gcLat],
              offset:
                item.companyId == this.currentSelectedCompanyId
                  ? new AMap.Pixel(-15.5, -15.5)
                  : new AMap.Pixel(-15.5, -15.5),
              data: item,
              zIndex: 19,
            });
            siteMarkerItem.setLabel({
              offset: new AMap.Pixel(3, 22), //设置文本标注偏移量
              content: `<div class='${
                item.pollutantState
                  ? "site-info-pollutantState"
                  : item.online
                  ? "site-info-online"
                  : "site-info-offline"
              } site-info'></div>`, //设置文本标注内容
            });
            this.currItem = siteMarkerItem;
            this.openSite(item);
          } else {
            // 掉线
            siteMarkerItem = new AMap.Marker({
              map: this.maps,
              icon: diaoxianMarker,
              position: [item.gcLng, item.gcLat],
              offset:
                item.companyId == this.currentSelectedCompanyId
                  ? new AMap.Pixel(-15.5, -15.5)
                  : new AMap.Pixel(-15.5, -15.5),
              data: item,
              zIndex: 19,
            });
          }
        }
        AMap.event.addListener(siteMarkerItem, "click", this.siteClick);
        this.siteMarkerList.push(siteMarkerItem);
        // if(!index){
        //   this.maps.setCenter([item.gcLng,item.gcLat])
        // }
      });
    },
    //工地点击事件
    siteClick(marker) {
      const data = marker.target.w.data;
      this.siteMarkerList.forEach((items) => {
        const item = items.w.data;
        if (item.companyId === data.companyId) {
          if (this.currItem) {
            // this.clearCarSelected(this.currItem)
          }
          this.currItem = items;
          items.setLabel({
            offset: new AMap.Pixel(3, 22), //设置文本标注偏移量
            content: `<div class='${
              item.pollutantState
                ? "site-info-pollutantState"
                : item.online
                ? "site-info-online"
                : "site-info-offline"
            } site-info'></div>`, //设置文本标注内容
          });
        } else {
          items.setLabel({
            offset: new AMap.Pixel(3, 22), //设置文本标注偏移量
            content: "<div></div>", //设置文本标注内容
          });
        }
      });
      this.curMarkerClick = data.companyId;
      this.openSite(marker.target.w.data);
      this.maps.setCenter([data.longitude, data.latitude]);
      this.$emit(
        "curSiteChange",
        this.curMarkerClick,
        data.siteId,
        data.projectId,
        data.alias
      );
    },
    //点击工地打开弹窗
    openSite(marker) {
      if (marker) {
        marker.monitorValue = Math.round(marker.monitorValue);
        let infoWindow;
        const time = marker.monitorDataTime
          ? marker.monitorDataTime.slice(0, 16)
          : "-";
        const str = `
          <div style="pointer-events: auto;width:3.74rem; height:2.59rem; background: url('${electricityBg}') no-repeat; background-size: 100% 100%; padding:0.15rem 0.25rem; position: relative;">
            <span style="position: absolute;top:0.32rem;left:0.38rem;font-size: 0.18rem;font-family: YouSheBiaoTiHei;font-weight: 400;color: #FFFFFF;background: linear-gradient(0deg, #56D1ED 0%, #CAF6FF 100%);-webkit-background-clip: text;-webkit-text-fill-color: transparent;">在建工地监测</span>
            <img src="${noiseClose}" style="position: absolute;width:0.22rem; height:0.22rem;top:0.47rem;right:0.25rem;" onclick="closeInfoWindow()"/>
            <div style="height:0.82rem; margin-top:0.5rem;display:flex;padding-left:0.3rem;padding-top:0.15rem;">
              <img src="${gongdiIcon}" style="width:0.6rem;height:0.7rem" />
              <div style="padding-left:0.08rem;">
                <div style="font-size: 0.16rem;font-family: PingFang SC;font-weight: 400;color: #DCF0FF;line-height: 0.32rem;width:2rem;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;" title="${
                  marker.alias
                }">${marker.alias}</div>
                <span style="font-size: 0.14rem;font-family: PingFang SC;font-weight: 400;color: #7798B9;margin-top:0.12rem;">监测时间：${time}</span>
              </div>
            </div>
            <div style="margin-left: 0.30rem;margin-top: 0.05rem;color:#56A7F1;">
              <div style="display:flex;justify-content: space-between;padding-right:0.25rem;">
                <span style="font-size: 0.12rem;">
                  PM₁₀：
                  <span style="color:${
                    marker.pollutantState ? "#e80e0e" : "#18e04a"
                  };font-family: YouSheBiaoTiHei;font-size:0.2rem">${
          marker.monitorValue
        }</span>
                  <span style="color:#D3E6F5;">ug/m³</span>
                </span>
                <span style="line-height:35px;font-size: 0.12rem;">工程状态：<span style="font-size: 0.12rem;color:#D3E6F5;">${
                  marker.projectState ? "在建" : "--"
                }</span></span>
              </div>
              <div style="font-size: 0.12rem;font-family: PingFang SC;font-weight: 400;">
                工地类型：<span style="color:#D3E6F5;font-size: 0.14rem;">砂浆混凝土</span>
              </div>
            </div>
          </div>
          `;
        infoWindow = new AMap.InfoWindow({
          // anchor: "bottom-center",
          isCustom: true,
          content: str, //使用默认信息窗体框样式，显示信息内容
          offset: marker.siteId
            ? new AMap.Pixel(21, -5)
            : new AMap.Pixel(-3, 2),
        });
        this.infoWindow = infoWindow;
        if (this.isOpen) {
          infoWindow.open(this.maps, [marker.gcLng, marker.gcLat]);
          // this.maps.setCenter([marker.gcLng, marker.gcLat])
        }
      }
    },
    // 点击打开油烟弹框
    openRestaurant(marker) {
      this.isOpen = true;
      let infoWindow;
      const time = marker.updateTime
        ? marker.updateTime.slice(0, 16)
        : "暂无更新时间";
      const str = `
          <div style="pointer-events: auto;width:3.74rem; height:2.59rem; background: url('${electricityBg}') no-repeat; background-size: 100% 100%; padding:0.15rem 0.25rem; position: relative;">
            <span style="position: absolute;top:0.32rem;left:0.38rem;font-size: 0.18rem;font-family: YouSheBiaoTiHei;font-weight: 400;color: #FFFFFF;background: linear-gradient(0deg, #56D1ED 0%, #CAF6FF 100%);-webkit-background-clip: text;-webkit-text-fill-color: transparent;">餐饮油烟监测</span>
            <img src="${noiseClose}" style="position: absolute;width:0.22rem; height:0.22rem;top:0.47rem;right:0.25rem;" onclick="closeInfoWindow()"/>
            <div style="height:0.82rem; margin-top:0.5rem;display:flex;padding-left:0.3rem;padding-top:0.15rem;">
              <img src="${youyanIcon}" style="width:0.6rem;height:0.7rem" />
              <div style="padding-left:0.08rem;">
                <div style="font-size: 0.16rem;font-family: PingFang SC;font-weight: 400;color: #DCF0FF;line-height: 0.32rem;width:2rem;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;" title="${
                  this.activeIndex == 3 ? marker.restaurantName : marker.gasName
                }">${
        this.activeIndex == 3 ? marker.restaurantName : marker.gasName
      }</div>
                <span style="font-size: 0.14rem;font-family: PingFang SC;font-weight: 400;color: #7798B9;margin-top:0.12rem;">监测时间：${time}</span>
              </div>
            </div>
            <div style="margin-left: 0.30rem;margin-top: 0.15rem;color:#56A7F1;">
              <div style="font-size: 0.12rem;">监测设备：<span style="color:#D3E6F5;font-size: 0.14rem;">${
                marker.monitorDeviceState ? "在线" : "离线"
              }</span></div>
              <div style="font-size: 0.12rem;">
                ${this.activeIndex == 3 ? "油烟浓度" : "油气浓度"}：
                <span style="font-family: YouSheBiaoTiHei;font-size: 0.14rem;font-weight: 500;color:${
                  (this.activeIndex == 3
                  ? marker.concentration > marker.concentrationThreshold
                  : marker.oilConcentration > marker.oilConcentrationThreshold)
                    ? "#ff7072"
                    : "#3EFC19"
                }; font-size:0.22rem;">
                  ${
                    this.activeIndex == 3
                      ? marker.concentration &&
                        marker.concentration != 0 &&
                        marker.concentration != 0.0
                        ? marker.concentration
                        : "--"
                      : marker.oilConcentration &&
                        marker.oilConcentration != 0 &&
                        marker.oilConcentration != 0.0
                      ? marker.oilConcentration
                      : "--"
                  }
                </span>
                <span style="color:#D3E6F5;">mg/m³</span>
              </div>
            </div>
          </div>
          `;
      infoWindow = new AMap.InfoWindow({
        isCustom: true,
        content: str, //使用默认信息窗体框样式，显示信息内容
      });
      this.infoWindow = infoWindow;
      if (this.isOpen) {
        infoWindow.open(this.maps, [
          this.activeIndex == 3 ? marker.lng : marker.longitude,
          this.activeIndex == 3 ? marker.lat : marker.latitude,
        ]);
        // this.maps.setCenter([this.activeIndex == 3 ? marker.lng : marker.longitude, this.activeIndex == 3 ? marker.lat : marker.latitude])
      }
    },
    //点击汽修打开弹窗
    openGarage(marker) {
      this.isOpen = true;
      let infoWindow;
      const time = marker.updateTime ? marker.updateTime.slice(5, 16) : "-";
      const str = `
          <div class="siteMsgWindow" style="width: 3.6rem">
            <div>
              <div class="title-site" title="${
                this.activeIndex === 1
                  ? marker.printFactoryName
                  : marker.garageName
              }">${
        this.activeIndex === 1 ? marker.printFactoryName : marker.garageName
      }</div>
              <img src="https://oss-chengdu.vankeytech.com:9000/vankeytech-ep/sys/shanchu3.png" alt onclick="closeInfoWindow()"/>
            </div>
            <div>
              <img src="https://oss-chengdu.vankeytech.com:9000/vankeytech-ep/sys/PM10.png" alt="" />
              <span>VOC浓度：</span>
              <span style="color:${
                marker.vocConcentration > marker.vocConcentrationThreshold
                  ? "#e80e0e"
                  : "#18e04a"
              };display: inline-block;max-width: 0.55rem">${
        marker.vocConcentration
      }</span>
              <span>mg/m³</span>
              <span style="background:${
                marker.vocConcentration > marker.vocConcentrationThreshold
                  ? "#e80e0e"
                  : "#18e04a"
              }">${
        marker.vocConcentration > marker.vocConcentrationThreshold
          ? "超标"
          : "未超标"
      }</span>
            </div>
            <div>告警时间：${time}</div>
          </div>
          `;
      infoWindow = new AMap.InfoWindow({
        // anchor: "bottom-center",
        isCustom: true,
        content: str, //使用默认信息窗体框样式，显示信息内容
      });
      this.infoWindow = infoWindow;
      if (this.isOpen) {
        infoWindow.open(this.maps, [marker.lng, marker.lat]);
        // this.maps.setCenter([marker.lng, marker.lat])
      }
    },
    // 关闭弹窗
    closeInfoWindow() {
      console.log("关闭弹窗");
      if (this.maps) {
        this.maps.clearInfoWindow();
        if (this.isRadar) {
          if (this.circleMap) {
            this.maps.remove(this.circleMap);
            this.circleMap = null;
          }
          this.$emit("randaClick", {});
          this.pollutionStationList = [];
          this.maps.remove(this.stationMarkerList);
        }
      }
    },
    // 噪声地图图标以及弹窗
    createNoiseMarker() {
      if (this.siteMarkerList.length !== 0) {
        this.maps.remove(this.siteMarkerList);
        this.siteMarkerList = [];
      }
      // 超标
      const noiseOverMarker = new AMap.Icon({
        size: new AMap.Size(34, 42),
        image: noiseOver,
        imageSize: new AMap.Size(25.5, 31.5),
        imageOffset: new AMap.Pixel(0, 0),
      });
      // 未超标
      const noiseNormalMarker = new AMap.Icon({
        size: new AMap.Size(34, 42),
        image: noiseNormal,
        imageSize: new AMap.Size(25.5, 31.5),
        imageOffset: new AMap.Pixel(0, 0),
      });
      // 掉线
      const noiseOfflineMarker = new AMap.Icon({
        size: new AMap.Size(34, 42),
        image: noiseOffline,
        imageSize: new AMap.Size(25.5, 31.5),
        imageOffset: new AMap.Pixel(0, 0),
      });
      this.markerList.forEach((item, index) => {
        let siteMarkerItem;
        if (item.online) {
          // 判断是否掉线
          if (item.companyId == this.currentSelectedCompanyId) {
            if (item.isAlarm) {
              // 超标选中
              siteMarkerItem = new AMap.Marker({
                map: this.maps,
                icon: noiseOverMarker,
                position: [item.longitude, item.latitude],
                offset:
                  item.companyId == this.currentSelectedCompanyId
                    ? new AMap.Pixel(-15.5, -15.5)
                    : new AMap.Pixel(-15.5, -15.5),
                data: item,
                zIndex: 19,
              });
              siteMarkerItem.setLabel({
                offset: new AMap.Pixel(3, 22), //设置文本标注偏移量
                content: `<div class='${
                  item.isAlarm
                    ? "site-info-pollutantState"
                    : item.online
                    ? "site-info-online"
                    : "site-info-offline"
                } site-info'></div>`, //设置文本标注内容
              });
            } else {
              // 未超标选中
              siteMarkerItem = new AMap.Marker({
                map: this.maps,
                icon: noiseNormalMarker,
                position: [item.longitude, item.latitude],
                offset:
                  item.companyId == this.currentSelectedCompanyId
                    ? new AMap.Pixel(-11.9, -14.7)
                    : new AMap.Pixel(-15.5, -15.5),
                data: item,
                zIndex: 19,
              });
              siteMarkerItem.setLabel({
                offset: new AMap.Pixel(3, 22), //设置文本标注偏移量
                content: `<div class='${
                  item.isAlarm
                    ? "site-info-pollutantState"
                    : item.online
                    ? "site-info-online"
                    : "site-info-offline"
                } site-info'></div>`, //设置文本标注内容
              });
            }
            this.currItem = siteMarkerItem;
            this.openNoiseSite(item);
          } else {
            if (item.isAlarm) {
              // 超标
              siteMarkerItem = new AMap.Marker({
                map: this.maps,
                icon: noiseOverMarker,
                position: [item.longitude, item.latitude],
                offset:
                  item.companyId == this.currentSelectedCompanyId
                    ? new AMap.Pixel(-15.5, -15.5)
                    : new AMap.Pixel(-15.5, -15.5),
                data: item,
                zIndex: 19,
              });
            } else {
              // 未超标
              siteMarkerItem = new AMap.Marker({
                map: this.maps,
                icon: noiseNormalMarker,
                position: [item.longitude, item.latitude],
                offset:
                  item.companyId == this.currentSelectedCompanyId
                    ? new AMap.Pixel(-15.5, -15.5)
                    : new AMap.Pixel(-15.5, -15.5),
                data: item,
                zIndex: 19,
              });
            }
          }
        } else {
          if (item.companyId == this.currentSelectedCompanyId) {
            // 掉线选中
            siteMarkerItem = new AMap.Marker({
              map: this.maps,
              icon: noiseOfflineMarker,
              position: [item.longitude, item.latitude],
              offset:
                item.companyId == this.currentSelectedCompanyId
                  ? new AMap.Pixel(-15.5, -15.5)
                  : new AMap.Pixel(-15.5, -15.5),
              data: item,
              zIndex: 19,
            });
            siteMarkerItem.setLabel({
              offset: new AMap.Pixel(3, 22), //设置文本标注偏移量
              content: `<div class='${
                item.isAlarm
                  ? "site-info-pollutantState"
                  : item.online
                  ? "site-info-online"
                  : "site-info-offline"
              } site-info'></div>`, //设置文本标注内容
            });
            this.currItem = siteMarkerItem;
            this.openNoiseSite(item);
          } else {
            // 掉线
            siteMarkerItem = new AMap.Marker({
              map: this.maps,
              icon: noiseOfflineMarker,
              position: [item.longitude, item.latitude],
              offset:
                item.companyId == this.currentSelectedCompanyId
                  ? new AMap.Pixel(-15.5, -15.5)
                  : new AMap.Pixel(-15.5, -15.5),
              data: item,
              zIndex: 19,
            });
          }
        }
        // if(!index){
        //   this.maps.setCenter([item.longitude, item.latitude])
        // }
        AMap.event.addListener(siteMarkerItem, "click", this.siteNoiseClick);
        this.siteMarkerList.push(siteMarkerItem);
      });
    },
    openNoiseSite(marker) {
      const markerData = marker.relData;
      let infoWindow;
      let str = "";
      if (marker.relData) {
        const time = markerData.monitorTime
          ? markerData.monitorTime.slice(0, 16)
          : "--";
        str = `
          <div style="pointer-events: auto;width:3.74rem; height:3.18rem; box-sizing:border-box;background: url('${noiseBg}') no-repeat; background-size: 100% 100%; padding:0.15rem 0.25rem;position: relative;">
            <span style="position: absolute;top:0.4rem;left:0.51rem;font-size: 0.18rem;font-family: YouSheBiaoTiHei;font-weight: 400;color: #FFFFFF;background: linear-gradient(0deg, #56D1ED 0%, #CAF6FF 100%);-webkit-background-clip: text;-webkit-text-fill-color: transparent;">噪声监测</span>
            <img src="${noiseClose}" style="width:0.22rem; height:0.22rem;position: absolute;top:0.57rem;right:0.25rem;" onclick="closeInfoWindow()"/>
            <div style="height:0.82rem; margin-top:0.5rem;display:flex;padding-left:0.3rem;padding-top:0.15rem;">
              <img src="${noiseIcon}" style="width:0.6rem;height:0.7rem" />
              <div style="padding-left:0.08rem;">
                <div style="font-size: 0.16rem;font-family: PingFang SC;font-weight: 400;color: #DCF0FF;line-height: 0.32rem;">${
                  marker.stationName
                }</div>
                <span style="font-size: 0.14rem;font-family: PingFang SC;font-weight: 400;color: #7798B9;margin-top:0.12rem;">监测时间：${time}</span>
              </div>
            </div>
            <div style="padding:0 0.1rem; margin-top:0.05rem; display:flex; justify-content: space-around;">
              <div style="width:1.26rem; height:1.13rem; background: url('${noiseLx}') no-repeat; background-size: 100% 100%; padding:0.18rem 0rem;">
                <div style="font-size: 0.2rem; font-family: YouSheBiaoTiHei; font-weight: 400; color: #FFFFFF; width:100%;  text-align: center;">${
                  marker.functionTypeName
                }</div>
                <div style="font-size: 0.1rem; font-family: PingFang SC; font-weight: 400; color: #FFFFFF; width:100%;  text-align: center;">功能区类型</div>
              </div>
              <div style="font-size: 0.12rem; font-family: YouSheBiaoTiHei; color: #D1DDF7;">
                <div>Leq：<span style="font-size: 0.16rem; font-family: YouSheBiaoTiHei; color: ${
                  !marker.isAlarm ? "#D1DDF7" : "#EE505F"
                };">${markerData.leq}</span> dB(A)</div>
                <div>Lmax：<span style="font-size: 0.16rem; font-family: YouSheBiaoTiHei; color: ${
                  !marker.isAlarm ? "#D1DDF7" : "#EE505F"
                };">${markerData.lmax}</span> dB(A)</div>
                <div>Lmin：<span style="font-size: 0.16rem; font-family: YouSheBiaoTiHei; color: ${
                  !marker.isAlarm ? "#D1DDF7" : "#EE505F"
                };">${markerData.lmin}</span> dB(A)</div>
                <div>标准差：<span style="font-size: 0.16rem; font-family: YouSheBiaoTiHei; color: ${
                  !marker.isAlarm ? "#D1DDF7" : "#EE505F"
                };">${markerData.sd}</span></div>
                <div>有效采集率：<span style="font-size: 0.16rem; font-family: YouSheBiaoTiHei; color: ${
                  !marker.isAlarm ? "#D1DDF7" : "#EE505F"
                };">${markerData.rate}</span> %</div>
              </div>
            </div>
          </div>
          `;
      } else {
        str = `
          <div style="width:3.74rem; height:3.18rem; box-sizing:border-box;background: url('${noiseBg}') no-repeat; background-size: 100% 100%; padding:0.15rem 0.25rem;position: relative;">
            <span style="position: absolute;top:0.4rem;left:0.45rem;font-size: 0.18rem;font-family: YouSheBiaoTiHei;font-weight: 400;color: #FFFFFF;background: linear-gradient(0deg, #56D1ED 0%, #CAF6FF 100%);-webkit-background-clip: text;-webkit-text-fill-color: transparent;">噪声监测</span>
            <img src="${noiseClose}" style="width:0.22rem; height:0.22rem;position: absolute;top:0.57rem;right:0.25rem;" onclick="closeInfoWindow()"/>
            <div style="height:0.82rem; margin-top:0.5rem;display:flex;padding-left:0.3rem;padding-top:0.15rem;">
              <img src="${noiseIcon}" style="width:0.6rem;height:0.7rem" />
              <div style="padding-left:0.08rem;">
                <div style="font-size: 0.18rem;font-family: PingFang SC;font-weight: 400;color: #DCF0FF;line-height: 0.32rem;">${
                  marker.stationName
                }</div>
                <span style="font-size: 0.14rem;font-family: PingFang SC;font-weight: 400;color: #7798B9;margin-top:0.08rem;">监测时间：--</span>
              </div>
            </div>
            <div style="padding:0 0.1rem; margin-top:0.05rem; display:flex; justify-content: space-around;">
              <div style="width:1.26rem; height:1.13rem; background: url('${noiseLx}') no-repeat; background-size: 100% 100%; padding:0.18rem 0rem;">
                <div style="font-size: 0.2rem; font-family: YouSheBiaoTiHei; font-weight: 400; color: #FFFFFF; width:100%;  text-align: center;">--</div>
                <div style="font-size: 0.1rem; font-family: PingFang SC; font-weight: 400; color: #FFFFFF; width:100%;  text-align: center;">功能区类型</div>
              </div>
              <div style="font-size: 0.12rem; font-family: YouSheBiaoTiHei; color: #D1DDF7;">
                <div>Leq：<span style="font-size: 0.16rem; font-family: YouSheBiaoTiHei; color: ${
                  true ? "#D1DDF7" : "#EE505F"
                };">--</span> dB(A)</div>
                <div>Lmax：<span style="font-size: 0.16rem; font-family: YouSheBiaoTiHei; color: ${
                  true ? "#D1DDF7" : "#EE505F"
                };">--</span> dB(A)</div>
                <div>Lmin：<span style="font-size: 0.16rem; font-family: YouSheBiaoTiHei; color: ${
                  true ? "#D1DDF7" : "#EE505F"
                };">--</span> dB(A)</div>
                <div>标准差：<span style="font-size: 0.16rem; font-family: YouSheBiaoTiHei; color: ${
                  true ? "#D1DDF7" : "#EE505F"
                };">--</span></div>
                <div>有效采集率：<span style="font-size: 0.16rem; font-family: YouSheBiaoTiHei; color: ${
                  true ? "#D1DDF7" : "#EE505F"
                };">--</span> %</div>
              </div>
            </div>
          </div>
          `;
      }
      infoWindow = new AMap.InfoWindow({
        // anchor: "bottom-center",
        isCustom: true,
        content: str, //使用默认信息窗体框样式，显示信息内容
        offset: new AMap.Pixel(0, 5),
      });
      this.infoWindow = infoWindow;
      if (this.isOpen) {
        infoWindow.open(this.maps, [marker.longitude, marker.latitude]);
      }
    },
    siteNoiseClick(marker) {
      const data = marker.target.w.data;
      this.siteMarkerList.forEach((items) => {
        const item = items.w.data;
        if (item.companyId === data.companyId) {
          if (this.currItem) {
            // this.clearCarSelected(this.currItem)
          }
          this.currItem = items;
          items.setLabel({
            offset: new AMap.Pixel(3, 22), //设置文本标注偏移量
            content: `<div class='${
              item.pollutantState
                ? "site-info-pollutantState"
                : item.online
                ? "site-info-online"
                : "site-info-offline"
            } site-info'></div>`, //设置文本标注内容
          });
        } else {
          items.setLabel({
            offset: new AMap.Pixel(3, 22), //设置文本标注偏移量
            content: "<div></div>", //设置文本标注内容
          });
        }
      });
      this.curMarkerClick = data.companyId;
      this.openNoiseSite(marker.target.w.data);
      // this.maps.setCenter([data.longitude,data.latitude])
      this.$emit("curSiteChange", this.curMarkerClick);
    },
    // 用电量地图图标以及弹窗
    createElectricityMarker() {
      if (this.siteMarkerList.length !== 0) {
        this.maps.remove(this.siteMarkerList);
        this.siteMarkerList = [];
      }
      // 超标
      const electricityOverMarker = new AMap.Icon({
        size: new AMap.Size(34, 42),
        image: electricityOver,
        imageSize: new AMap.Size(25.5, 31.5),
        imageOffset: new AMap.Pixel(0, 0),
      });
      // 未超标
      const electricityNormalMarker = new AMap.Icon({
        size: new AMap.Size(34, 42),
        image: electricityNormal,
        imageSize: new AMap.Size(25.5, 31.5),
        imageOffset: new AMap.Pixel(0, 0),
      });
      // 掉线
      const electricityOfflineMarker = new AMap.Icon({
        size: new AMap.Size(34, 42),
        image: electricityOffline,
        imageSize: new AMap.Size(25.5, 31.5),
        imageOffset: new AMap.Pixel(0, 0),
      });
      this.markerList.forEach((item, index) => {
        let siteMarkerItem;
        if (item.online) {
          // 判断是否掉线
          if (item.companyId == this.currentSelectedCompanyId) {
            if (item.isAlarm) {
              // 超标选中
              siteMarkerItem = new AMap.Marker({
                map: this.maps,
                icon: electricityOverMarker,
                position: [item.longitude, item.latitude],
                offset:
                  item.companyId == this.currentSelectedCompanyId
                    ? new AMap.Pixel(-15.5, -15.5)
                    : new AMap.Pixel(-15.5, -15.5),
                data: item,
                zIndex: 19,
              });
              siteMarkerItem.setLabel({
                offset: new AMap.Pixel(3, 22), //设置文本标注偏移量
                content: `<div class='${
                  item.isAlarm
                    ? "site-info-pollutantState"
                    : item.online
                    ? "site-info-online"
                    : "site-info-offline"
                } site-info'></div>`, //设置文本标注内容
              });
            } else {
              // 未超标选中
              siteMarkerItem = new AMap.Marker({
                map: this.maps,
                icon: electricityNormalMarker,
                position: [item.longitude, item.latitude],
                offset:
                  item.companyId == this.currentSelectedCompanyId
                    ? new AMap.Pixel(-11.9, -14.7)
                    : new AMap.Pixel(-15.5, -15.5),
                data: item,
                zIndex: 19,
              });
              siteMarkerItem.setLabel({
                offset: new AMap.Pixel(3, 22), //设置文本标注偏移量
                content: `<div class='${
                  item.isAlarm
                    ? "site-info-pollutantState"
                    : item.online
                    ? "site-info-online"
                    : "site-info-offline"
                } site-info'></div>`, //设置文本标注内容
              });
            }
            this.currItem = siteMarkerItem;
            this.openElectricitySite(item);
          } else {
            if (item.isAlarm) {
              // 超标
              siteMarkerItem = new AMap.Marker({
                map: this.maps,
                icon: electricityOverMarker,
                position: [item.longitude, item.latitude],
                offset:
                  item.companyId == this.currentSelectedCompanyId
                    ? new AMap.Pixel(-15.5, -15.5)
                    : new AMap.Pixel(-15.5, -15.5),
                data: item,
                zIndex: 19,
              });
            } else {
              // 未超标
              siteMarkerItem = new AMap.Marker({
                map: this.maps,
                icon: electricityNormalMarker,
                position: [item.longitude, item.latitude],
                offset:
                  item.companyId == this.currentSelectedCompanyId
                    ? new AMap.Pixel(-15.5, -15.5)
                    : new AMap.Pixel(-15.5, -15.5),
                data: item,
                zIndex: 19,
              });
            }
          }
        } else {
          if (item.companyId == this.currentSelectedCompanyId) {
            // 掉线选中
            siteMarkerItem = new AMap.Marker({
              map: this.maps,
              icon: electricityOfflineMarker,
              position: [item.longitude, item.latitude],
              offset:
                item.companyId == this.currentSelectedCompanyId
                  ? new AMap.Pixel(-15.5, -15.5)
                  : new AMap.Pixel(-15.5, -15.5),
              data: item,
              zIndex: 19,
            });
            siteMarkerItem.setLabel({
              offset: new AMap.Pixel(3, 22), //设置文本标注偏移量
              content: `<div class='${
                item.isAlarm
                  ? "site-info-pollutantState"
                  : item.online
                  ? "site-info-online"
                  : "site-info-offline"
              } site-info'></div>`, //设置文本标注内容
            });
            this.currItem = siteMarkerItem;
            this.openElectricitySite(item);
          } else {
            // 掉线
            siteMarkerItem = new AMap.Marker({
              map: this.maps,
              icon: electricityOfflineMarker,
              position: [item.longitude, item.latitude],
              offset:
                item.companyId == this.currentSelectedCompanyId
                  ? new AMap.Pixel(-15.5, -15.5)
                  : new AMap.Pixel(-15.5, -15.5),
              data: item,
              zIndex: 19,
            });
          }
        }
        // if(!index){
        //   this.maps.setCenter([item.longitude, item.latitude])
        // }
        AMap.event.addListener(
          siteMarkerItem,
          "click",
          this.siteElectricityClick
        );
        this.siteMarkerList.push(siteMarkerItem);
      });
    },
    openElectricitySite(marker) {
      let infoWindow;
      let str = "";
      if (marker && marker.monitorValue != null) {
        const time = marker.dataTime ? marker.dataTime.slice(0, 16) : "--";
        str = `
          <div style="pointer-events: auto;width:3.74rem; height:2.59rem; background: url('${electricityBg}') no-repeat; background-size: 100% 100%; padding:0.15rem 0.25rem; position: relative;">
            <span style="position: absolute;top:0.32rem;left:0.45rem;font-size: 0.18rem;font-family: YouSheBiaoTiHei;font-weight: 400;color: #FFFFFF;background: linear-gradient(0deg, #56D1ED 0%, #CAF6FF 100%);-webkit-background-clip: text;-webkit-text-fill-color: transparent;">用电量监测</span>
            <img src="${noiseClose}" style="position: absolute;width:0.22rem; height:0.22rem;top:0.47rem;right:0.25rem;" onclick="closeInfoWindow()"/>
            <div style="height:0.82rem; margin-top:0.5rem;display:flex;padding-left:0.3rem;padding-top:0.15rem;">
              <img src="${electricityIcon}" style="width:0.6rem;height:0.7rem" />
              <div style="padding-left:0.08rem;">
                <div style="font-size: 0.16rem;font-family: PingFang SC;font-weight: 400;color: #DCF0FF;line-height: 0.32rem;width:2rem;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;" title="${
                  marker.enterpriseName
                }">${marker.enterpriseName}</div>
                <span style="font-size: 0.14rem;font-family: PingFang SC;font-weight: 400;color: #7798B9;margin-top:0.12rem;">监测时间：${time}</span>
              </div>
            </div>
            <div style="margin-left: 0.30rem;margin-top: 0.15rem;color:#56A7F1;">
              <div style="font-size: 0.12rem;">监测设备：<span style="color:#D3E6F5;font-size: 0.14rem;">${
                marker.online ? "在线" : "离线"
              }</span></div>
              <div style="font-size: 0.12rem;">
                用电量：<span style="font-family: YouSheBiaoTiHei;font-weight: 500;color:${
                  !marker.isAlarm ? "#3EFC19" : "#ff7072"
                }; font-size:0.22rem;">${marker.thisUseElectricity}</span>
                <span style="color:#D3E6F5;">KWH</span>
              </div>
            </div>
          </div>
          `;
      } else {
        str = `
          <div style="width:3.74rem; height:2.59rem; background: url('${electricityBg}') no-repeat; background-size: 100% 100%; padding:0.15rem 0.25rem; position: relative;">
            <span style="position: absolute;top:0.32rem;left:0.45rem;font-size: 0.18rem;font-family: YouSheBiaoTiHei;font-weight: 400;color: #FFFFFF;background: linear-gradient(0deg, #56D1ED 0%, #CAF6FF 100%);-webkit-background-clip: text;-webkit-text-fill-color: transparent;">用电量监测</span>
            <img src="${noiseClose}" style="position: absolute;width:0.22rem; height:0.22rem;top:0.47rem;right:0.25rem;" onclick="closeInfoWindow()"/>
            <div style="height:0.82rem; margin-top:0.5rem;display:flex;padding-left:0.3rem;padding-top:0.15rem;">
              <img src="${electricityIcon}" style="width:0.6rem;height:0.7rem" />
              <div style="padding-left:0.08rem;">
                <div style="font-size: 0.16rem;font-family: PingFang SC;font-weight: 400;color: #DCF0FF;line-height: 0.32rem;width:2rem;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;" title="${
                  marker.enterpriseName
                }">${marker.enterpriseName}</div>
                <span style="font-size: 0.14rem;font-family: PingFang SC;font-weight: 400;color: #7798B9;margin-top:0.12rem;">监测时间：--</span>
              </div>
            </div>
            <div style="margin-left: 0.30rem;margin-top: 0.15rem;">
              <div>监测设备：<span>${
                marker.online ? "在线" : "离线"
              }</span></div>
              <div style="font-size: 0.14rem;font-family: PingFang SC;font-weight: 500;color: #FEFEFE;">
                用电量：<span style="color:#3EFC19; font-size:0.22rem;">--</span>KWH
              </div>
            </div>
          </div>
          `;
      }
      infoWindow = new AMap.InfoWindow({
        // anchor: "bottom-center",
        isCustom: true,
        content: str, //使用默认信息窗体框样式，显示信息内容
        offset: new AMap.Pixel(0, 3),
      });
      this.infoWindow = infoWindow;
      if (this.isOpen) {
        infoWindow.open(this.maps, [marker.longitude, marker.latitude]);
      }
    },
    siteElectricityClick(marker) {
      const data = marker.target.w.data;
      this.siteMarkerList.forEach((items) => {
        const item = items.w.data;
        if (item.companyId === data.companyId) {
          if (this.currItem) {
            // this.clearCarSelected(this.currItem)
          }
          this.currItem = items;
          items.setLabel({
            offset: new AMap.Pixel(3, 22), //设置文本标注偏移量
            content: `<div class='${
              item.pollutantState
                ? "site-info-pollutantState"
                : item.online
                ? "site-info-online"
                : "site-info-offline"
            } site-info'></div>`, //设置文本标注内容
          });
        } else {
          items.setLabel({
            offset: new AMap.Pixel(3, 22), //设置文本标注偏移量
            content: "<div></div>", //设置文本标注内容
          });
        }
      });
      this.curMarkerClick = data.companyId;
      this.openElectricitySite(marker.target.w.data);
      // this.maps.setCenter([data.longitude,data.latitude])
      this.$emit("curSiteChange", this.curMarkerClick);
    },
    // 固废地图图标以及弹窗
    createSolidWasteMarker() {
      if (this.siteMarkerList.length !== 0) {
        this.maps.remove(this.siteMarkerList);
        this.siteMarkerList = [];
      }
      // 超标
      const solidWasteOverMarker = new AMap.Icon({
        size: new AMap.Size(34, 42),
        image: solidWasteOver,
        imageSize: new AMap.Size(25.5, 31.5),
        imageOffset: new AMap.Pixel(0, 0),
      });
      // 未超标
      const solidWasteNormalMarker = new AMap.Icon({
        size: new AMap.Size(34, 42),
        image: solidWasteNormal,
        imageSize: new AMap.Size(25.5, 31.5),
        imageOffset: new AMap.Pixel(0, 0),
      });
      // 掉线
      const solidWasteOfflineMarker = new AMap.Icon({
        size: new AMap.Size(34, 42),
        image: solidWasteOffline,
        imageSize: new AMap.Size(25.5, 31.5),
        imageOffset: new AMap.Pixel(0, 0),
      });
      this.markerList.forEach((item, index) => {
        let siteMarkerItem;
        if (item.online) {
          // 判断是否掉线
          if (item.companyId == this.currentSelectedCompanyId) {
            if (item.isAlarm) {
              // 超标选中
              siteMarkerItem = new AMap.Marker({
                map: this.maps,
                icon: solidWasteOverMarker,
                position: [item.lng, item.lat],
                offset:
                  item.companyId == this.currentSelectedCompanyId
                    ? new AMap.Pixel(-15.5, -15.5)
                    : new AMap.Pixel(-15.5, -15.5),
                data: item,
                zIndex: 19,
              });
              siteMarkerItem.setLabel({
                offset: new AMap.Pixel(3, 22), //设置文本标注偏移量
                content: `<div class='${
                  item.isAlarm
                    ? "site-info-pollutantState"
                    : item.online
                    ? "site-info-online"
                    : "site-info-offline"
                } site-info'></div>`, //设置文本标注内容
              });
            } else {
              // 未超标选中
              siteMarkerItem = new AMap.Marker({
                map: this.maps,
                icon: solidWasteNormalMarker,
                position: [item.lng, item.lat],
                offset:
                  item.companyId == this.currentSelectedCompanyId
                    ? new AMap.Pixel(-11.9, -14.7)
                    : new AMap.Pixel(-15.5, -15.5),
                data: item,
                zIndex: 19,
              });
              siteMarkerItem.setLabel({
                offset: new AMap.Pixel(3, 22), //设置文本标注偏移量
                content: `<div class='${
                  item.isAlarm
                    ? "site-info-pollutantState"
                    : item.online
                    ? "site-info-online"
                    : "site-info-offline"
                } site-info'></div>`, //设置文本标注内容
              });
            }
            this.currItem = siteMarkerItem;
            this.openSolidWasteSite(item);
          } else {
            if (item.isAlarm) {
              // 超标
              siteMarkerItem = new AMap.Marker({
                map: this.maps,
                icon: solidWasteOverMarker,
                position: [item.lng, item.lat],
                offset:
                  item.companyId == this.currentSelectedCompanyId
                    ? new AMap.Pixel(-15.5, -15.5)
                    : new AMap.Pixel(-15.5, -15.5),
                data: item,
                zIndex: 19,
              });
            } else {
              // 未超标
              siteMarkerItem = new AMap.Marker({
                map: this.maps,
                icon: solidWasteNormalMarker,
                position: [item.lng, item.lat],
                offset:
                  item.companyId == this.currentSelectedCompanyId
                    ? new AMap.Pixel(-15.5, -15.5)
                    : new AMap.Pixel(-15.5, -15.5),
                data: item,
                zIndex: 19,
              });
            }
          }
        } else {
          if (item.companyId == this.currentSelectedCompanyId) {
            // 掉线选中
            siteMarkerItem = new AMap.Marker({
              map: this.maps,
              icon: solidWasteOfflineMarker,
              position: [item.lng, item.lat],
              offset:
                item.companyId == this.currentSelectedCompanyId
                  ? new AMap.Pixel(-15.5, -15.5)
                  : new AMap.Pixel(-15.5, -15.5),
              data: item,
              zIndex: 19,
            });
            siteMarkerItem.setLabel({
              offset: new AMap.Pixel(3, 22), //设置文本标注偏移量
              content: `<div class='${
                item.isAlarm
                  ? "site-info-pollutantState"
                  : item.online
                  ? "site-info-online"
                  : "site-info-offline"
              } site-info'></div>`, //设置文本标注内容
            });
            this.currItem = siteMarkerItem;
            this.openSolidWasteSite(item);
          } else {
            // 掉线
            siteMarkerItem = new AMap.Marker({
              map: this.maps,
              icon: solidWasteOfflineMarker,
              position: [item.lng, item.lat],
              offset:
                item.companyId == this.currentSelectedCompanyId
                  ? new AMap.Pixel(-15.5, -15.5)
                  : new AMap.Pixel(-15.5, -15.5),
              data: item,
              zIndex: 19,
            });
          }
        }
        // if(!index){
        //   this.maps.setCenter([item.lng, item.lat])
        // }
        AMap.event.addListener(
          siteMarkerItem,
          "click",
          this.siteSolidWasteClick
        );
        this.siteMarkerList.push(siteMarkerItem);
      });
    },
    openSolidWasteSite(marker) {
      let infoWindow;
      let str = "";
      if (marker && marker.wasteTotal != null) {
        const time = marker.dataTime ? marker.dataTime.slice(0, 16) : "--";
        str = `
          <div style="pointer-events: auto;width:3.74rem; height:2.82rem; background: url('${electricityBg}') no-repeat; background-size: 100% 100%; padding:0.15rem 0.25rem;transform: translateX(0.06rem);">
            <span style="position: absolute;top:0.36rem;left:0.45rem;font-size: 0.18rem;font-family: YouSheBiaoTiHei;font-weight: 400;color: #FFFFFF;background: linear-gradient(0deg, #56D1ED 0%, #CAF6FF 100%);-webkit-background-clip: text;-webkit-text-fill-color: transparent;">固废回收监测</span>
            <img src="${noiseClose}" style="position: absolute;width:0.22rem; height:0.22rem;top:0.54rem;right:0.25rem;" onclick="closeInfoWindow()"/>
            <div style="height:0.82rem; margin-top:0.5rem;display:flex;padding-left:0.3rem;padding-top:0.15rem;">
              <img src="${solidWasteIcon}" style="width:0.6rem;height:0.7rem" />
              <div style="padding-left:0.08rem;">
                <div style="font-size: 0.16rem;font-family: PingFang SC;font-weight: 400;color: #DCF0FF;line-height: 0.32rem;width:2rem;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;" title="${
                  marker.deviceName
                }">${marker.deviceName || "--"}</div>
                <span style="font-size: 0.14rem;font-family: PingFang SC;font-weight: 400;color: #7798B9;margin-top:0.12rem;">监测时间：${time}</span>
              </div>
            </div>
            <div style="margin-top:0.15rem">
              <div style="display: flex;justify-content: space-around;height:0.3rem;width:100%;line-height:0.3rem;text-align: center;font-size: 0.14rem;font-family: PingFang SC;font-weight: 500;color: #FEFEFE;background: url('${noiseTime}') no-repeat; background-size: 100% 100%;">
                <span style="flex:1;">今日贮存(kg)</span><span style="flex:1;">今日产生(kg)</span><span style="flex:1;">今日转运(kg)</span>
              </div>
              <div style="display: flex;justify-content: space-around;text-align: center; font-size: 0.2rem;font-family: YouSheBiaoTiHei;font-weight: 400;color: #EEF2FA;line-height: 0.4rem;">
                <span style="flex:1;color:${
                  marker.isStockAlarm ? "#FF3030" : "#EEF2FA"
                }">${marker.wasteTotal}</span>
                <span style="flex:1;color:${
                  marker.isInAlarm ? "#FF3030" : "#EEF2FA"
                }">${marker.thisTotalIn}</span>
                <span style="flex:1;color:"#FF3030">${
                  marker.thisTotalOut
                }</span>
              </div>
            </div>
          </div>
          `;
      } else {
        str = `
        <div style="width:3.74rem; height:2.82rem; background: url('${electricityBg}') no-repeat; background-size: 100% 100%; padding:0.15rem 0.25rem;transform: translateX(0.06rem);">
          <span style="position: absolute;top:0.36rem;left:0.45rem;font-size: 0.18rem;font-family: YouSheBiaoTiHei;font-weight: 400;color: #FFFFFF;background: linear-gradient(0deg, #56D1ED 0%, #CAF6FF 100%);-webkit-background-clip: text;-webkit-text-fill-color: transparent;">固废回收监测</span>
          <img src="${noiseClose}" style="position: absolute;width:0.22rem; height:0.22rem;top:0.54rem;right:0.25rem;" onclick="closeInfoWindow()"/>
          <div style="height:0.82rem; margin-top:0.5rem;display:flex;padding-left:0.3rem;padding-top:0.15rem;">
            <img src="${solidWasteIcon}" style="width:0.6rem;height:0.7rem" />
            <div style="padding-left:0.08rem;">
              <div style="font-size: 0.16rem;font-family: PingFang SC;font-weight: 400;color: #DCF0FF;line-height: 0.32rem;width:2rem;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;">${marker.deviceName}</div>
              <span style="font-size: 0.14rem;font-family: PingFang SC;font-weight: 400;color: #7798B9;margin-top:0.12rem;">监测时间：--</span>
            </div>
          </div>
          <div style="margin-top:0.15rem">
            <div style="display: flex;justify-content: space-around;height:0.3rem;width:100%;line-height:0.3rem;text-align: center;font-size: 0.14rem;font-family: PingFang SC;font-weight: 500;color: #FEFEFE;background: url('${noiseTime}') no-repeat; background-size: 100% 100%;">
              <span style="flex:1;">今日贮存(kg)</span><span style="flex:1;">今日产生(kg)</span><span style="flex:1;">今日转运(kg)</span>
            </div>
            <div style="display: flex;justify-content: space-around;text-align: center; font-size: 0.2rem;font-family: YouSheBiaoTiHei;font-weight: 400;color: #EEF2FA;line-height: 0.4rem;">
              <span style="flex:1;">--</span><span style="flex:1;">--</span><span style="flex:1;">--</span>
            </div>
          </div>
        </div>
        `;
      }
      infoWindow = new AMap.InfoWindow({
        // anchor: "bottom-center",
        isCustom: true,
        content: str, //使用默认信息窗体框样式，显示信息内容
        offset: new AMap.Pixel(-8, 3),
      });
      this.infoWindow = infoWindow;
      if (this.isOpen) {
        infoWindow.open(this.maps, [marker.lng, marker.lat]);
      }
    },
    siteSolidWasteClick(marker) {
      const data = marker.target.w.data;
      this.siteMarkerList.forEach((items) => {
        const item = items.w.data;
        if (item.companyId === data.companyId) {
          if (this.currItem) {
            // this.clearCarSelected(this.currItem)
          }
          this.currItem = items;
          items.setLabel({
            offset: new AMap.Pixel(3, 22), //设置文本标注偏移量
            content: `<div class='${
              item.pollutantState
                ? "site-info-pollutantState"
                : item.online
                ? "site-info-online"
                : "site-info-offline"
            } site-info'></div>`, //设置文本标注内容
          });
        } else {
          items.setLabel({
            offset: new AMap.Pixel(3, 22), //设置文本标注偏移量
            content: "<div></div>", //设置文本标注内容
          });
        }
      });
      this.curMarkerClick = data.companyId;
      this.openSolidWasteSite(marker.target.w.data);
      // this.maps.setCenter([data.lng,data.lat])
      this.$emit("curSiteChange", this.curMarkerClick);
    },
    // 重污地图图标以及弹窗
    createHeavCorruptMarker() {
      if (this.siteMarkerList.length !== 0) {
        this.maps.remove(this.siteMarkerList);
        this.siteMarkerList = [];
      }
      // 超标
      const heavyCorruptOverMarker = new AMap.Icon({
        size: new AMap.Size(34, 42),
        image: heavyCorruptOver,
        imageSize: new AMap.Size(25.5, 31.5),
        imageOffset: new AMap.Pixel(0, 0),
      });
      // 未超标
      const heavyCorruptNormalMarker = new AMap.Icon({
        size: new AMap.Size(34, 42),
        image: heavyCorruptNormal,
        imageSize: new AMap.Size(25.5, 31.5),
        imageOffset: new AMap.Pixel(0, 0),
      });
      // 掉线
      const heavyCorruptOfflineMarker = new AMap.Icon({
        size: new AMap.Size(34, 42),
        image: heavyCorruptOffline,
        imageSize: new AMap.Size(25.5, 31.5),
        imageOffset: new AMap.Pixel(0, 0),
      });
      this.markerList.forEach((item, index) => {
        let siteMarkerItem;
        if (item.online) {
          // 判断是否掉线
          if (item.companyId == this.currentSelectedCompanyId) {
            if (item.isAlarm) {
              // 超标选中
              siteMarkerItem = new AMap.Marker({
                map: this.maps,
                icon: heavyCorruptOverMarker,
                position: [item.longitude, item.latitude],
                offset:
                  item.companyId == this.currentSelectedCompanyId
                    ? new AMap.Pixel(-15.5, -15.5)
                    : new AMap.Pixel(-15.5, -15.5),
                data: item,
                zIndex: 19,
              });
              siteMarkerItem.setLabel({
                offset: new AMap.Pixel(3, 22), //设置文本标注偏移量
                content: `<div class='${
                  item.isAlarm
                    ? "site-info-pollutantState"
                    : item.online
                    ? "site-info-online"
                    : "site-info-offline"
                } site-info'></div>`, //设置文本标注内容
              });
            } else {
              // 未超标选中
              siteMarkerItem = new AMap.Marker({
                map: this.maps,
                icon: heavyCorruptNormalMarker,
                position: [item.longitude, item.latitude],
                offset:
                  item.companyId == this.currentSelectedCompanyId
                    ? new AMap.Pixel(-11.9, -14.7)
                    : new AMap.Pixel(-15.5, -15.5),
                data: item,
                zIndex: 19,
              });
              siteMarkerItem.setLabel({
                offset: new AMap.Pixel(3, 22), //设置文本标注偏移量
                content: `<div class='${
                  item.isAlarm
                    ? "site-info-pollutantState"
                    : item.online
                    ? "site-info-online"
                    : "site-info-offline"
                } site-info'></div>`, //设置文本标注内容
              });
            }
            this.currItem = siteMarkerItem;
            this.openHeavyCorruptSite(item);
          } else {
            if (item.isAlarm) {
              // 超标
              siteMarkerItem = new AMap.Marker({
                map: this.maps,
                icon: heavyCorruptOverMarker,
                position: [item.longitude, item.latitude],
                offset:
                  item.companyId == this.currentSelectedCompanyId
                    ? new AMap.Pixel(-15.5, -15.5)
                    : new AMap.Pixel(-15.5, -15.5),
                data: item,
                zIndex: 19,
              });
            } else {
              // 未超标
              siteMarkerItem = new AMap.Marker({
                map: this.maps,
                icon: heavyCorruptNormalMarker,
                position: [item.longitude, item.latitude],
                offset:
                  item.companyId == this.currentSelectedCompanyId
                    ? new AMap.Pixel(-15.5, -15.5)
                    : new AMap.Pixel(-15.5, -15.5),
                data: item,
                zIndex: 19,
              });
            }
          }
        } else {
          if (item.companyId == this.currentSelectedCompanyId) {
            // 掉线选中
            siteMarkerItem = new AMap.Marker({
              map: this.maps,
              icon: heavyCorruptOfflineMarker,
              position: [item.longitude, item.latitude],
              offset:
                item.companyId == this.currentSelectedCompanyId
                  ? new AMap.Pixel(-15.5, -15.5)
                  : new AMap.Pixel(-15.5, -15.5),
              data: item,
              zIndex: 19,
            });
            siteMarkerItem.setLabel({
              offset: new AMap.Pixel(3, 22), //设置文本标注偏移量
              content: `<div class='${
                item.isAlarm
                  ? "site-info-pollutantState"
                  : item.online
                  ? "site-info-online"
                  : "site-info-offline"
              } site-info'></div>`, //设置文本标注内容
            });
            this.currItem = siteMarkerItem;
            this.openHeavyCorruptSite(item);
          } else {
            // 掉线
            siteMarkerItem = new AMap.Marker({
              map: this.maps,
              icon: heavyCorruptOfflineMarker,
              position: [item.longitude, item.latitude],
              offset:
                item.companyId == this.currentSelectedCompanyId
                  ? new AMap.Pixel(-15.5, -15.5)
                  : new AMap.Pixel(-15.5, -15.5),
              data: item,
              zIndex: 19,
            });
          }
        }
        // if(!index){
        //   this.maps.setCenter([item.longitude, item.latitude])
        // }
        AMap.event.addListener(
          siteMarkerItem,
          "click",
          this.siteHeavyCorruptClick
        );
        this.siteMarkerList.push(siteMarkerItem);
      });
    },
    openHeavyCorruptSite(marker) {
      let infoWindow;
      let str = "";
      if (marker) {
        const labelArr = marker.pointList || [];
        if (!this.HPLabelId) {
          this.HPLabelId =
            marker.pointList &&
            marker.pointList.length &&
            marker.pointList[0].pointId;
        }
        const labelDiv = labelArr.map((v, i) => {
          let flg = v.pointId == this.HPLabelId;
          return `
            <div style="background:${flg ? "#114278" : "#0a274d"};color:${
            flg ? "#fff" : "#416b92"
          };border-top:1px solid #0fb9ed;flex:1;cursor: pointer;" onclick="HPClick('${
            v.pointId
          }','${marker.companyId}')">${v.pointName}</div>
          `;
        });
        const iDiv = labelArr.filter((o) => o.pointId === this.HPLabelId);
        const itemDiv =
          (iDiv.length &&
            iDiv[0].pointLastList.map((v, i) => {
              return `
            <div style="width:50%;color:#56A7F1;font-size:0.12rem;margin-top:0.05rem;">
              <span>${v.monitorName}:</span>
              <span style="color:${
                v.isAlarm ? "#ff3030" : "#E7EFF6"
              };font-family: YouSheBiaoTiHei;font-size:0.14rem">${v.avgValue ||
                "--"}</span>
              <span>${v.monitorUnit || ""}</span>
            </div>
          `;
            })) ||
          [];
        const empty = `
          <div style="text-align: center;width:100%;">
            <div>
              <img src="${heavyCorruptKong}" style="width:0.77rem;height:0.74rem;" />
            </div>
            <div style="font-size: 0.15rem;font-family: PingFang SC;font-weight: 300;color: #7694B3;line-height: 0.24rem;">当前站点暂无监测数据</div>
          </div>
        `;
        const timeArr =
          iDiv.length && iDiv[0].pointLastList.length
            ? iDiv[0].pointLastList.length
              ? iDiv[0].pointLastList.filter((v) => v.monitorTime)
              : []
            : "--";
        str = `
          <div style="pointer-events: auto;width:4.64rem; height:${
            itemDiv.length > 6
              ? "3.7rem"
              : itemDiv.length > 8
              ? "3.9rem"
              : "3.42rem"
          }; background: url('${electricityBg}') no-repeat; background-size: 100% 100%; padding:0.15rem 0.25rem;transform: translateX(0.06rem);">
            <span style="position: absolute;top:0.45rem;left:0.65rem;font-size: 0.18rem;font-family: YouSheBiaoTiHei;font-weight: 400;color: #FFFFFF;background: linear-gradient(0deg, #56D1ED 0%, #CAF6FF 100%);-webkit-background-clip: text;-webkit-text-fill-color: transparent;">排污监测</span>
            <img src="${noiseClose}" style="position: absolute;width:0.22rem; height:0.22rem;top:0.63rem;right:0.3rem;" onclick="closeInfoWindow()"/>
            <div style="display:${
              labelArr.length ? "flex" : "none"
            };margin-top:0.78rem;padding:0 0.3rem;height:0.32rem;font-size:0.16rem;line-height:0.32rem;text-align: center;">
              ${labelDiv.join("")}
            </div>
            <div style="height:0.82rem; margin-top:${
              labelArr.length ? "0.05rem" : "0.7rem"
            };display:flex;padding-left:0.3rem;padding-top:0.15rem;">
              <img src="${solidWasteIcon}" style="width:0.6rem;height:0.7rem" />
              <div style="padding-left:0.08rem;">
                <div style="font-size: 0.16rem;font-family: PingFang SC;font-weight: 400;color: #DCF0FF;line-height: 0.32rem;width:2.3rem;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;" title="${
                  marker.heavilyPollutingEnterpriseName
                }">${marker.heavilyPollutingEnterpriseName}</div>
                <span style="font-size: 0.14rem;font-family: PingFang SC;font-weight: 400;color: #7798B9;margin-top:0.12rem;">监测时间：${
                  timeArr.length
                    ? timeArr[0].monitorTime
                      ? timeArr[0].monitorTime.slice(0, 16)
                      : "--"
                    : "--"
                }</span>
              </div>
            </div>
            <div style="margin-top:0.1rem;display:flex;flex-wrap: wrap; margin-left:0.3rem; align-content: flex-start;">
              ${itemDiv.length ? itemDiv.join("") : empty}
            </div>
          </div>
          `;
      }
      infoWindow = new AMap.InfoWindow({
        // anchor: "bottom-center",
        isCustom: true,
        content: str, //使用默认信息窗体框样式，显示信息内容
        offset: new AMap.Pixel(-8, 8),
      });
      this.infoWindow = infoWindow;
      if (this.isOpen) {
        infoWindow.open(this.maps, [marker.longitude, marker.latitude]);
      }
    },
    siteHeavyCorruptClick(marker) {
      this.HPLabelId = "";
      const data = marker.target.w.data;
      this.siteMarkerList.forEach((items) => {
        const item = items.w.data;
        if (item.companyId === data.companyId) {
          if (this.currItem) {
            // this.clearCarSelected(this.currItem)
          }
          this.currItem = items;
          items.setLabel({
            offset: new AMap.Pixel(3, 22), //设置文本标注偏移量
            content: `<div class='${
              item.isAlarm
                ? "site-info-pollutantState"
                : item.online
                ? "site-info-online"
                : "site-info-offline"
            } site-info'></div>`, //设置文本标注内容
          });
        } else {
          items.setLabel({
            offset: new AMap.Pixel(3, 22), //设置文本标注偏移量
            content: "<div></div>", //设置文本标注内容
          });
        }
      });
      this.curMarkerClick = data.companyId;
      this.openHeavyCorruptSite(marker.target.w.data);
      this.maps.setCenter([data.longitude, data.latitude]);
      this.$emit("curSiteChange", this.curMarkerClick);
    },
    // 雷达图弹窗
    openRandaSite(marker, flg = false) {
      findPointData({
        groupFileName: this.groupFileName,
        lng: marker.lng,
        lat: marker.lat,
      }).then((res) => {
        const { data } = res.data;
        let morUnit =
          this.randaType == "消光系数"
            ? "KM-1"
            : this.randaType == "退偏振比"
            ? ""
            : "ug/m³";
        let infoWindow;
        let str = "";
        if (data) {
          this.randarDataInfo = data;
        }
        // const data = {
        //   xiaoguang: 12,
        //   absorbanceThreshold: 10,
        //   distance: 133
        // }
        if (data) {
          let morValue =
            this.randaType == "消光系数"
              ? data.xiaoguang
              : this.randaType == "PM₁₀"
              ? data.pm10
              : this.randaType == "退偏振比"
              ? data.tuipian
              : data.pm25;
          let morValueTh =
            this.randaType == "消光系数"
              ? data.absorbanceThreshold
              : this.randaType == "PM₁₀"
              ? data.inhalableParticlesThreshold
              : data.fineParticulateMatterThreshold;
          str = `
            <div style="width:3.44rem; height:3.3rem; background: url('${electricityBg}') no-repeat; background-size: 100% 100%; padding:0.15rem 0.25rem;transform: translateX(0.06rem);">
              <span style="position: absolute;top:0.4rem;left:0.45rem;font-size: 0.18rem;font-family: YouSheBiaoTiHei;font-weight: 400;color: #FFFFFF;background: linear-gradient(0deg, #56D1ED 0%, #CAF6FF 100%);-webkit-background-clip: text;-webkit-text-fill-color: transparent;">污染信息</span>
              <img src="${noiseClose}" style="position: absolute;width:0.22rem; height:0.22rem;top:0.58rem;right:0.25rem;" onclick="closeInfoWindow()"/>
              <div style="margin-top:0.6rem;display:flex;padding-left:0.2rem;">
                <span style="font-size: 0.14rem;font-family: PingFang SC;font-weight: 400;color: #7798B9;">监测时间：${moment(
                  data.date
                ).format("YYYY-MM-DD HH:mm")}</span>
              </div>
              <div style="margin-top:0.15rem;margin-left:0.2rem; color:#56A7F1;">
                <div style="display:flex;margin-top:0.05rem;">
                  <span style="flex:1;">经度：<span style="color:#DCF0FF;">${
                    marker.lng
                  }</span></span>
                </div>
                <div style="display:flex;margin-top:0.05rem;">
                  <span style="flex:1;">纬度：<span style="color:#DCF0FF;">${
                    marker.lat
                  }</span></span>
                </div>
                <div style="display:flex;margin-top:0.05rem;">
                  <span style="flex:1;">中心距离：<span style="color:#DCF0FF;">${data.distance.toFixed(
                    3
                  )} km</span></span>
                </div>
                <div style="margin-top:0.02rem;"> ${
                  this.randaType
                }：<span style="font-family: YouSheBiaoTiHei;color:${
            morValueTh != null &&
            morValueTh < morValue &&
            this.randaType != "退偏振比"
              ? "#ff3030"
              : "#34E583"
          };font-size:0.18rem;">${morValue ||
            "--"}</span> <span style="color:#DCF0FF;">${morUnit}</span></div>
                <div style="margin-top:0.05rem;display:flex;" title="${
                  data.address
                }"> <span style="width: 70px;">详细地址：</span><span style="color:#DCF0FF;flex:1;display:-webkit-box;-webkit-line-clamp:1;overflow:hidden;-webkit-box-orient:vertical;text-overflow: ellipsis;">${data.address ||
            "暂无"}</span></div>
              </div>
              <div  style="display: flex; justify-content: center; align-items: center;margin-top: 10px;">
               <div  onclick="handleClickTask()" style="width: 100px;height: 32px;border: 1px solid #206FC8;font-weight: 400;font-size: 14px;color: #00CCFF;display: flex; justify-content: center; align-items: center; cursor: pointer;">任务调度</div>
              </div>
            </div>
          `;
        } else {
          str = `
          <div style="width:3.44rem; height:2.9rem; background: url('${electricityBg}') no-repeat; background-size: 100% 100%; padding:0.15rem 0.25rem;transform: translateX(0.06rem);">
            <span style="position: absolute;top:0.44rem;left:0.45rem;font-size: 0.18rem;font-family: YouSheBiaoTiHei;font-weight: 400;color: #FFFFFF;background: linear-gradient(0deg, #56D1ED 0%, #CAF6FF 100%);-webkit-background-clip: text;-webkit-text-fill-color: transparent;">污染信息</span>
            <img src="${noiseClose}" style="position: absolute;width:0.22rem; height:0.22rem;top:0.64rem;right:0.28rem;" onclick="closeInfoWindow()"/>
            <div style="text-align: center;width:100%;margin-top:1.1rem;">
              <div>
                <img src="${heavyCorruptKong}" style="width:0.77rem;height:0.74rem;" />
              </div>
              <div style="font-size: 0.15rem;font-family: PingFang SC;font-weight: 300;color: #7694B3;line-height: 0.24rem;">当前点位暂无监测数据</div>
            </div>
          </div>
          `;
        }
        infoWindow = new AMap.InfoWindow({
          // anchor: "bottom-center",
          isCustom: true,
          content: str, //使用默认信息窗体框样式，显示信息内容
          offset: new AMap.Pixel(0, 20),
        });
        this.infoWindow = infoWindow;
        // console.log([marker.lng, marker.lat]);
        // if(!flg){
        this.maps.setCenter([marker.lng, marker.lat]);
        // }
        infoWindow.open(this.maps, [marker.lng, marker.lat]);
      });
    },
    // 黑烟车地图图标以及弹窗
    createExhaustGasTruckMarker() {
      if (this.siteMarkerList.length !== 0) {
        this.maps.remove(this.siteMarkerList);
        this.siteMarkerList = [];
      }
      // 超标
      const noiseOverMarker = new AMap.Icon({
        size: new AMap.Size(34, 42),
        image: truckOver,
        imageSize: new AMap.Size(25.5, 31.5),
        imageOffset: new AMap.Pixel(0, 0),
      });
      // 未超标
      const noiseNormalMarker = new AMap.Icon({
        size: new AMap.Size(34, 42),
        image: truckNormal,
        imageSize: new AMap.Size(25.5, 31.5),
        imageOffset: new AMap.Pixel(0, 0),
      });
      // 掉线
      const noiseOfflineMarker = new AMap.Icon({
        size: new AMap.Size(34, 42),
        image: truckOffLine,
        imageSize: new AMap.Size(25.5, 31.5),
        imageOffset: new AMap.Pixel(0, 0),
      });
      this.markerList.forEach((item, index) => {
        let siteMarkerItem;
        if (item.online) {
          // 判断是否掉线
          if (item.companyId == this.currentSelectedCompanyId) {
            if (item.isAlarm) {
              // 超标选中
              siteMarkerItem = new AMap.Marker({
                map: this.maps,
                icon: noiseOverMarker,
                position: [item.lng, item.lat],
                offset:
                  item.companyId == this.currentSelectedCompanyId
                    ? new AMap.Pixel(-15.5, -15.5)
                    : new AMap.Pixel(-15.5, -15.5),
                data: item,
                zIndex: 19,
              });
              siteMarkerItem.setLabel({
                offset: new AMap.Pixel(3, 22), //设置文本标注偏移量
                content: `<div class='${
                  item.isAlarm
                    ? "site-info-pollutantState"
                    : item.online
                    ? "site-info-online"
                    : "site-info-offline"
                } site-info'></div>`, //设置文本标注内容
              });
            } else {
              // 未超标选中
              siteMarkerItem = new AMap.Marker({
                map: this.maps,
                icon: noiseNormalMarker,
                position: [item.lng, item.lat],
                offset:
                  item.companyId == this.currentSelectedCompanyId
                    ? new AMap.Pixel(-11.9, -14.7)
                    : new AMap.Pixel(-15.5, -15.5),
                data: item,
                zIndex: 19,
              });
              siteMarkerItem.setLabel({
                offset: new AMap.Pixel(3, 22), //设置文本标注偏移量
                content: `<div class='${
                  item.isAlarm
                    ? "site-info-pollutantState"
                    : item.online
                    ? "site-info-online"
                    : "site-info-offline"
                } site-info'></div>`, //设置文本标注内容
              });
            }
            this.currItem = siteMarkerItem;
            this.openExhaustGasTruckSite(item);
          } else {
            if (item.isAlarm) {
              // 超标
              siteMarkerItem = new AMap.Marker({
                map: this.maps,
                icon: noiseOverMarker,
                position: [item.lng, item.lat],
                offset:
                  item.companyId == this.currentSelectedCompanyId
                    ? new AMap.Pixel(-15.5, -15.5)
                    : new AMap.Pixel(-15.5, -15.5),
                data: item,
                zIndex: 19,
              });
            } else {
              // 未超标
              siteMarkerItem = new AMap.Marker({
                map: this.maps,
                icon: noiseNormalMarker,
                position: [item.lng, item.lat],
                offset:
                  item.companyId == this.currentSelectedCompanyId
                    ? new AMap.Pixel(-15.5, -15.5)
                    : new AMap.Pixel(-15.5, -15.5),
                data: item,
                zIndex: 19,
              });
            }
          }
        } else {
          if (item.companyId == this.currentSelectedCompanyId) {
            // 掉线选中
            siteMarkerItem = new AMap.Marker({
              map: this.maps,
              icon: noiseOfflineMarker,
              position: [item.lng, item.lat],
              offset:
                item.companyId == this.currentSelectedCompanyId
                  ? new AMap.Pixel(-15.5, -15.5)
                  : new AMap.Pixel(-15.5, -15.5),
              data: item,
              zIndex: 19,
            });
            siteMarkerItem.setLabel({
              offset: new AMap.Pixel(3, 22), //设置文本标注偏移量
              content: `<div class='${
                item.isAlarm
                  ? "site-info-pollutantState"
                  : item.online
                  ? "site-info-online"
                  : "site-info-offline"
              } site-info'></div>`, //设置文本标注内容
            });
            this.currItem = siteMarkerItem;
            this.openExhaustGasTruckSite(item);
          } else {
            // 掉线
            siteMarkerItem = new AMap.Marker({
              map: this.maps,
              icon: noiseOfflineMarker,
              position: [item.lng, item.lat],
              offset:
                item.companyId == this.currentSelectedCompanyId
                  ? new AMap.Pixel(-15.5, -15.5)
                  : new AMap.Pixel(-15.5, -15.5),
              data: item,
              zIndex: 19,
            });
          }
        }
        // if(!index){
        //   this.maps.setCenter([item.longitude, item.latitude])
        // }
        AMap.event.addListener(
          siteMarkerItem,
          "click",
          this.siteExhaustGasTruckClick
        );
        this.siteMarkerList.push(siteMarkerItem);
      });
    },
    openExhaustGasTruckSite(marker) {
      const markerData = marker;
      let infoWindow;
      let str = "";
      if (marker.deviceNo) {
        const time = markerData.dataTime
          ? markerData.dataTime.slice(0, 16)
          : "--";
        str = `
          <div style="pointer-events: auto;width:3.4rem; height:2.5rem; box-sizing:border-box;background: url('${noiseBg}') no-repeat; background-size: 100% 100%; padding:0.15rem 0.25rem;position: relative;">
            <span style="position: absolute;top:0.4rem;left:0.35rem;font-size: 0.18rem;font-family: YouSheBiaoTiHei;font-weight: 400;color: #FFFFFF;background: linear-gradient(0deg, #56D1ED 0%, #CAF6FF 100%);-webkit-background-clip: text;-webkit-text-fill-color: transparent;">黑烟车监测</span>
            <img src="${noiseClose}" style="width:0.22rem; height:0.22rem;position: absolute;top:0.45rem;right:0.25rem;" onclick="closeInfoWindow()"/>
            <div style="height:0.82rem; margin-top:0.5rem;display:flex;padding-left:0.3rem;padding:0.15rem; border-bottom: 1px dashed #409BDB;">
              <img src="${truckIcon}" style="width:0.6rem;height:0.7rem" />
              <div style="padding-left:0.08rem;">
                <div style="font-size: 0.16rem;font-family: PingFang SC;font-weight: 400;color: #DCF0FF;line-height: 0.32rem;">${
                  marker.deviceName
                }</div>
                <span style="font-size: 0.14rem;font-family: PingFang SC;font-weight: 400;color: #7798B9;margin-top:0.12rem;">监测时间：${time}</span>
              </div>
            </div>
            <div style="padding:0 0.15rem; margin-top:0.1rem; display:flex; justify-content: space-around;flex-wrap: wrap;font-size: 12px;font-family: PingFang SC;font-weight: 400;color: #56A7F1;">
              <div style="width:50%;">
                车流量：<span style="font-size: 16px;font-family: YouSheBiaoTiHei;font-weight: 400;color: #E7EFF6;">${marker.trafficFlow ||
                  "--"}</span> 辆
              </div>
              <div style="width:50%;">
                黑烟车：<span style="font-size: 16px;font-family: YouSheBiaoTiHei;font-weight: 400;color: ${
                  marker.isAlarm ? "#FF3030" : "#E7EFF6"
                };">${marker.smokyCarCount || "--"}</span> 辆
              </div>
              <div style="width:50%;">
                汽油车：<span style="font-size: 16px;font-family: YouSheBiaoTiHei;font-weight: 400;color: #E7EFF6;">--</span> 辆
              </div>
              <div style="width:50%;">
                柴油车：<span style="font-size: 16px;font-family: YouSheBiaoTiHei;font-weight: 400;color: #E7EFF6;">${marker.dieselFuelCount ||
                  "--"}</span> 辆
              </div>
            </div>
          </div>
          `;
      } else {
        str = `
          <div style="pointer-events: auto;width:3.74rem; height:2.68rem; box-sizing:border-box;background: url('${noiseBg}') no-repeat; background-size: 100% 100%; padding:0.15rem 0.25rem;position: relative;">
            <span style="position: absolute;top:0.4rem;left:0.51rem;font-size: 0.18rem;font-family: YouSheBiaoTiHei;font-weight: 400;color: #FFFFFF;background: linear-gradient(0deg, #56D1ED 0%, #CAF6FF 100%);-webkit-background-clip: text;-webkit-text-fill-color: transparent;">噪声监测</span>
            <img src="${noiseClose}" style="width:0.22rem; height:0.22rem;position: absolute;top:0.57rem;right:0.25rem;" onclick="closeInfoWindow()"/>
            <div style="height:0.82rem; margin-top:0.5rem;display:flex;padding-left:0.3rem;padding:0.15rem 0; border-bottom: 1px dashed #409BDB;">
              <img src="${truckIcon}" style="width:0.6rem;height:0.7rem" />
              <div style="padding-left:0.08rem;">
                <div style="font-size: 0.16rem;font-family: PingFang SC;font-weight: 400;color: #DCF0FF;line-height: 0.32rem;">${marker.stationName}</div>
                <span style="font-size: 0.14rem;font-family: PingFang SC;font-weight: 400;color: #7798B9;margin-top:0.12rem;">监测时间：--</span>
              </div>
            </div>
            <div style="padding:0 0.1rem; margin-top:0.05rem; display:flex; justify-content: space-around;flex-wrap: wrap;font-size: 14px;font-family: PingFang SC;font-weight: 400;color: #56A7F1;">
              <div style="width:50%;">
                车流量：<span>--</span> 辆
              </div>
              <div style="width:50%;">
                黑烟车：<span>--</span> 辆
              </div>
              <div style="width:50%;">
                汽油车：<span>--</span> 辆
              </div>
              <div style="width:50%;">
                采油车：<span>--</span> 辆
              </div>
            </div>
          </div>
          `;
      }
      infoWindow = new AMap.InfoWindow({
        // anchor: "bottom-center",
        isCustom: true,
        content: str, //使用默认信息窗体框样式，显示信息内容
        offset: new AMap.Pixel(0, 5),
      });
      this.infoWindow = infoWindow;
      if (this.isOpen) {
        infoWindow.open(this.maps, [marker.lng, marker.lat]);
      }
    },
    siteExhaustGasTruckClick(marker) {
      const data = marker.target.w.data;
      this.siteMarkerList.forEach((items) => {
        const item = items.w.data;
        if (item.companyId === data.companyId) {
          if (this.currItem) {
            // this.clearCarSelected(this.currItem)
          }
          this.currItem = items;
          items.setLabel({
            offset: new AMap.Pixel(3, 22), //设置文本标注偏移量
            content: `<div class='${
              item.pollutantState
                ? "site-info-pollutantState"
                : item.online
                ? "site-info-online"
                : "site-info-offline"
            } site-info'></div>`, //设置文本标注内容
          });
        } else {
          items.setLabel({
            offset: new AMap.Pixel(3, 22), //设置文本标注偏移量
            content: "<div></div>", //设置文本标注内容
          });
        }
      });
      this.curMarkerClick = data.companyId;
      this.openExhaustGasTruckSite(marker.target.w.data);
      this.maps.setCenter([data.lng, data.lat]);
      this.maps.setZoom(13.6);
      this.$emit("curSiteChange", this.curMarkerClick);
    },
  },
};
</script>

<style scoped lang="less">
.relationship_box {
  position: absolute;
  z-index: 1;
  right: 40px;
  top: 100px;
  width: 390px;

  .header_box {
    position: relative;
    width: 390px;
    height: 38px;
    background: url("~@/assets/heavily-polluted-enterprise/rader/<EMAIL>")
      no-repeat;
    background-size: contain;
    display: flex;
    justify-content: end;
    align-items: center;
  }

  .content {
    width: 391px;
    height: 450px;
    background: url("~@/assets/heavily-polluted-enterprise/rader/<EMAIL>")
      no-repeat;
    background-size: contain;
    overflow-y: auto;
    // 隐藏滚动条
    &::-webkit-scrollbar {
      display: none;
    }
  }

  .content_item {
    display: flex;
    height: 80px;
    background-image: linear-gradient(
      to right,
      rgba(24, 124, 223, 0.3),
      rgba(24, 124, 223, 0)
    );
    box-sizing: border-box;
    padding: 20px 18px 20px 64px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;

    .title {
      font-family: PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #c7d4e6;
      position: relative;

      span {
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;

        width: 250px;
      }

      &::before {
        content: "";
        width: 17px;
        height: 17px;
        background: url("~@/assets/heavily-polluted-enterprise/rader/<EMAIL>")
          no-repeat;
        background-size: contain;
        position: absolute;
        left: -35px;
        top: 50%;
        transform: translateY(-50%);
      }
    }

    .item_list {
      display: flex;
      align-items: center;

      .item_item {
        display: flex;
        align-items: center;
        margin-right: 20px;
      }

      .label {
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 12px;
        color: #d7dbe5;
      }

      .value {
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 12px;
        color: #d7dbe5;
      }

      .number {
        font-size: 18px;
      }

      .unit {
        font-size: 12px;
      }
    }

    .right_box {
      .number {
        font-family: DIN;
        font-weight: 500;
        font-size: 20px;
        color: #c7d4e6;
      }

      .unit {
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 12px;
        color: #c7d4e6;
      }
    }

    &.active {
      background-image: linear-gradient(
        to right,
        rgba(125, 8, 8, 0.3),
        rgba(24, 124, 223, 0)
      );
    }
  }
}

.amap-ranging-label /deep/ span {
  color: #000 !important;
}

/deep/ .amap-ranging-label {
  color: #000 !important;
}

.mapContainer {
  width: 100%;
  height: 100%;
}

.siteMsgWindow {
  width: 3.5rem;
  height: 1.8rem;
  background: url("../../assets/<EMAIL>") no-repeat;
  background-size: 100% 100%;
  box-sizing: border-box;
  padding: 0.1rem 0.2rem 0.2rem;

  > div:not(:last-child) {
    display: flex;
    align-items: center;
  }

  > div:first-child {
    justify-content: space-between;
    align-items: flex-end;
    margin-top: 0.05rem;

    > img {
      width: 0.24rem;
      cursor: pointer;
    }
  }

  > div:nth-child(2) {
    font-size: 0.16rem;
    // margin: 0.1rem 0;
    margin-top: 0.24rem;
    margin-bottom: 0.15rem;

    img {
      width: 0.16rem;
      height: 0.13rem;
    }

    > span:nth-child(2) {
      margin: 0 0.1rem 0 0.1rem;
    }

    > span:nth-child(3) {
      font-size: 0.2rem;
      color: rgba(232, 14, 14, 1);
    }

    > span:nth-child(4) {
      margin: 0 0.2rem 0 0.1rem;
    }

    > span:last-child {
      background-color: #18e04a;
      padding: 0 0.1rem;
    }

    > .exceedStandard {
      background-color: rgba(232, 14, 14, 1);
    }
  }

  > div:nth-child(3) {
    font-size: 0.16rem;
  }

  .title-site {
    font-size: 0.17rem;
    font-family: Source Han Sans CN;
    font-weight: 500;
    color: rgba(0, 234, 255, 1);
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    flex: auto;
    width: 2rem;
  }

  .title-time {
    width: 0.9rem;
    font-size: 0.15rem;
  }

  .sign-out {
    width: 0.24rem;
    height: 0.24rem;
  }
}
</style>
