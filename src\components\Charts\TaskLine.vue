<template>
  <div :id="id" :style="{ height: height, width: width }" />
</template>

<script lang="ts">
import * as echarts from 'echarts'
import { Component, Prop, Watch } from "vue-property-decorator";
import { mixins } from "vue-class-component";
import ResizeMixin from "./mixins/resize";
@Component({
  name: "TaskLine"
})
export default class extends mixins(ResizeMixin) {
  @Prop({ default: "chart" }) private id!: string;
  @Prop({ default: "200px" }) private width!: string;
  @Prop({ default: "200px" }) private height!: string;
  @Prop({ required: true }) private propData!: any;
  @Watch("propData", { immediate: true, deep: true })
  public onMsgChanged(newValue: any, oldValue: any) {
    if (this.chart) {
      this.chart.clear();
      this.$nextTick(() => {
        this.initChart();
      });
    }
  }
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
  }
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.clear();
    this.chart.dispose();
    this.chart = null;
  }
  private initChart() {

    if(this.chart === null || this.chart === undefined) {
      this.chart = echarts.init(
        document.getElementById(this.id) as HTMLDivElement
      );
    }
    this.chart.setOption({
      backgroundColor: "transparent",
      grid: {
        bottom: 50,
        left: 40,
        right: 20,
        top: 40
      },
      legend: {
        data: ["任务总数", "完成总数"],
        textStyle: {
          color: "#fff"
        },
        right: 10
      },
      xAxis: {
        type: "category",
        boundaryGap: false,
        nameTextStyle: {
          color: "#fff"
        },
        data: this.propData.bottomList,
        axisLabel: {
          textStyle: {
            fontSize: 12,
            color: "white"
          },
          rotate: 45
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        splitLine: {
          show: false
        }
      },
      yAxis: {
        type: "value",
        // data: ["0%", "20%", "40%", "60%", "80%", "100%"],
        nameTextStyle: {
          color: "#fff",
          fontSize: 14
        },
        axisLabel: {
          textStyle: {
            fontSize: 12,
            color: "white"
          }
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: "#5D6A78"
          }
        }
      },
      tooltip: {
        trigger: "axis",
        axisPointer: {
          // type: "none"
          // lineStyle: {
          //   color: this.lineColor
          // }
        }
      },
      series: [
        {
          name: "任务总数",
          data: this.propData.dataList,
          type: "line",
          lineStyle: {
            color: "#026AFF" //改变折线颜色
          },
          symbol: "circle",
          symbolSize: 10,
          // smooth: true,
          itemStyle: {
            color: "#fff", //改变折线点的颜色
            borderColor: "#026AFF",
            borderWidth: 2
          }
        },
        {
          name: "完成总数",
          data: this.propData.dataList1,
          type: "line",
          lineStyle: {
            color: "#FE8449" //改变折线颜色
          },
          symbol: "circle",
          symbolSize: 10,
          // smooth: true,
          itemStyle: {
            color: "#fff", //改变折线点的颜色
            borderColor: "#FE8449",
            borderWidth: 2
          }
        }
      ]
    } as any);
  }
}
</script>
