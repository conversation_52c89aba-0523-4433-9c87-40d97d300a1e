<template>
  <div class="dialogPollutionMonitorItem-container flex flex-col items-center">
    <div class="icon-box base-bg-img" :class="[data.type]"></div>
    <div class="content-box flex flex-col items-center">
      <div class="value">
        {{ data.value === 0 ? 0 : data.value ? data.value : '—' }}
      </div>
      <div class="name">{{ data.name || '—' }}</div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'dialogPollutionMonitorItem',
  props: {
    data: {
      type: Object,
      default: () => ({
        type: 'fs',
        value: 0,
        name: '模块名称',
      }),
    },
  },
}
</script>

<style lang="less" scoped>
.dialogPollutionMonitorItem-container {
  width: 100%;

  .icon-box {
    width: 82px;
    height: 82px;
    margin-bottom: 25px;

    &.fs {
      background-image: url('../../../../../assets/images/<EMAIL>');
      background-repeat: no-repeat;
    }
    &.fq {
      background-image: url('../../../../../assets/images/<EMAIL>');
      background-repeat: no-repeat;
    }
    &.fl {
      background-image: url('../../../../../assets/images/<EMAIL>');
      background-repeat: no-repeat;
    }
  }

  .content-box {
    width: 100%;
    .value {
      width: 100%;
      padding: 15px 15px 6px 15px;
      font-size: 18px;
      color: #ffffff;
      border-bottom: 1px solid #37d3ff;
      margin-bottom: 10px;
      background: linear-gradient(
        0deg,
        rgba(51, 198, 241, 0.4) 0%,
        rgba(51, 198, 241, 0) 100%
      );
      text-align: center;
    }

    .name {
      font-size: 14px;
      color: #ffffff;
    }
  }
}
.flex{
  display: flex;
}
.items-center {
  align-items: center;
}
.flex-col {
  flex-direction: column;
}
</style>
