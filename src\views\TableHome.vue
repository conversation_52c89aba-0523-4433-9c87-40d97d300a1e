<style lang="less">
.home-main-two {
  .swiper-main {
    .swiper-container-horizontal
      > .swiper-pagination-bullets
      .swiper-pagination-bullet {
      margin: 0 4px;
    }
    .swiper-pagination-bullets {
      z-index: 100;
      width: 4rem;
      // left: 6.025rem;
      // bottom: 0.05rem;
    }
    .swiper-pagination-bullet {
      width: 0.6rem;
      height: 0.1rem;
      opacity: 1;
      background: #cccccc;
      border-radius: 0;
    }
    .swiper-pagination-bullet-active {
      background: #00fcf9;
    }
    .swiper-slide {
      padding: 0 0.2rem 0.7rem;
      height: 918px;
    }
    .title-video-select {
      display: flex;
      justify-content: space-between;
      margin-right: 0.1rem;
      width: 1rem;
      height: 0.3rem;
      .ant-select-selection {
        width: 1rem;
        height: 0.3rem;
        border: none;
        border-radius: unset;
        background: url(../assets/<EMAIL>);
        background-size: 100% 100%;
        background-repeat: no-repeat;
        display: flex;
        justify-content: center;
      }
      .ant-select-selection-selected-value {
        color: rgba(0, 234, 255, 1);
        font-size: 0.2rem;
      }
      .ant-select-focused .ant-select-selection,
      .ant-select-selection:focus,
      .ant-select-selection:active {
        border-color: #40a9ff;
        border-right-width: 0 !important;
        outline: 0;
        box-shadow: none;
      }
    }
  }
}
</style>
<style lang="less" scoped>
.home-main-two {
  .swiper-container {
    height: 100%;
  }
  .swiper-main {
    width: 100%;
    height: calc(100% - 1rem);
    box-sizing: border-box;
    padding: 0 1.5rem;
    position: absolute;
    top: 1rem;
    left: 0;
    background: url("../assets/home_shadow.png") no-repeat;
    background-color: rgba(0, 0, 0, 0.3);
    .title {
      text-align: center;
      font-size: 0.32rem;
      color: #00eaff;
      letter-spacing: 1px;
      padding: 0.4rem 0;
    }
    .table-container {
      width: 100%;
      text-align: center;
      border-color: rgba(11, 140, 209, 1);
      tr:nth-child(2) {
        /*color: #a9f5ff;*/
      }
      tr:first-child {
        /*color: #00eaff;*/
      }
      tr:first-child,
      tr:nth-child(2) {
        background-color: #0f38ab;
      }
      tr {
        td:first-child {
          background-color: #0f38ab;
          color: #a9f5ff;
        }
        .even {
          background: #0f245e;
        }
        .odd {
          background: #021446;
        }
      }
    }
    .check-div {
      display: flex;
      justify-content: center;
      align-items: flex-end;
      position: absolute;
      left: 50%;
      bottom: 0.45rem;
      transform: translateX(-50%);
      // z-index: 99;
      div {
        width: 1.86rem;
        height: 0.5rem;
        line-height: 0.5rem;
        border: 1px solid rgba(78, 112, 141, 1);
        font-size: 0.24rem;
        text-align: center;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: rgba(255, 255, 255, 1);
        cursor: pointer;
      }
      .check-div-active {
        background-color: #193e71;
        border: 1px solid #5d93c5;
        width: 1.86rem;
        height: 0.52rem;
        font-size: 0.26rem;
        /*line-height: normal;*/
      }
      > :nth-of-type(1) {
        margin-right: 0.3rem;
      }
    }
    .water-table {
      padding: 0 0.2rem 0.7rem;
      height: 857px;
      > :nth-of-type(1) {
        display: flex;
        justify-content: center;
        align-items: center;
      }
      .water-title {
        text-align: center;
        font-size: 0.32rem;
        color: #00eaff;
        letter-spacing: 1px;
        padding: 0.4rem 0;
        margin-left: 0.17rem;
      }
    }
  }
  .left-arrow {
    position: absolute;
    top: 40%;
    left: 0.7rem;
    cursor: pointer;
    outline: none;
    img {
      width: 0.6rem;
      height: 0.6rem;
    }
  }
  .right-arrow {
    position: absolute;
    top: 40%;
    right: 0.7rem;
    cursor: pointer;
    outline: none;
    img {
      width: 0.6rem;
      height: 0.6rem;
    }
  }
  .air-btn {
    cursor: pointer;
    position: absolute;
    bottom: 0.45rem;
    z-index: 99;
    width: 1.86rem;
    height: 0.5rem;
    left: 7.6rem;
  }
  .water-btn {
    cursor: pointer;
    position: absolute;
    bottom: 0.45rem;
    z-index: 99;
    width: 1.86rem;
    height: 0.5rem;
    left: 9.75rem;
  }
  .back_btn{
    position: absolute;
    top: 100px;
    left: 100px;
    z-index: 100;
    cursor: pointer;
  }
}
</style>
<template>
  <div class="home-main-two">
    <!-- <gao-de-map :mapZoom="13" :mapStyle="mapStyle" :enterType="5"></gao-de-map> -->
    <department-bg-map :mapZoom="13" :mapStyle="mapStyle" :enterType="5" :heat="false"></department-bg-map>
    <!-- <div @click="back" class="back_btn">返回</div> -->
    <div class="swiper-main">
      <Swiper :options="outSwiperOption" v-if="tableType == 'air'">
        <SwiperSlide>
          <div class="title">{{ yearStreetTitle }}</div>
          <table
            class="table-container"
            border="1px"
            cellpadding="5"
            cellspacing="0"
            align="center"
          >
            <tr>
              <th rowspan="2">街道</th>
              <th colspan="2">PM₁₀</th>
              <th colspan="2">O₃</th>
              <th colspan="2">PM₂.₅</th>
              <th colspan="2">NO₂</th>
              <th colspan="2">SO₂</th>
              <th colspan="2">CO</th>
              <th colspan="2">综合指数排名</th>
            </tr>
            <tr>
              <td>
                <div>浓度</div>
                <div>ug/m³</div>
              </td>
              <td>排名</td>
              <td>
                <div>浓度</div>
                <div>ug/m³</div>
              </td>
              <td>排名</td>
              <td>
                <div>浓度</div>
                <div>ug/m³</div>
              </td>
              <td>排名</td>
              <td>
                <div>浓度</div>
                <div>ug/m³</div>
              </td>
              <td>排名</td>
              <td>
                <div>浓度</div>
                <div>ug/m³</div>
              </td>
              <td>排名</td>
              <td>
                <div>浓度</div>
                <div>mg/m³</div>
              </td>
              <td>排名</td>
              <td>
                数值
              </td>
              <td>排名</td>
            </tr>
            <tr v-for="(item, index) in dataArr[0]" :key="index">
              <td>{{ item.stationName }}</td>
              <td class="even">{{ item.inhalableParticles || "-" }}</td>
              <td class="odd">{{ item.inhalableParticlesSort || "-" }}</td>
              <td class="even">{{ item.ozoneEight || "-" }}</td>
              <td class="odd">{{ item.ozoneEightSort || "-" }}</td>
              <td class="even">{{ item.fineParticulateMatter || "-" }}</td>
              <td class="odd">{{ item.fineParticulateMatterSort || "-" }}</td>
              <td class="even">{{ item.nitrogenDioxide || "-" }}</td>
              <td class="odd">{{ item.nitrogenDioxideSort || "-" }}</td>
              <td class="even">{{ item.sulfurDioxide || "-" }}</td>
              <td class="odd">{{ item.sulfurDioxideSort || "-" }}</td>
              <td class="even">{{ item.carbonMonoxide || "-" }}</td>
              <td class="odd">{{ item.carbonMonoxideSort || "-" }}</td>
              <td class="even">{{ item.aqci || "-" }}</td>
              <td class="odd">{{ item.aqciSort || "-" }}</td>
            </tr>
          </table>
        </SwiperSlide>
        <SwiperSlide>
          <div class="title">{{ monthStreetTitle }}</div>
          <table
            class="table-container"
            border="1px"
            cellpadding="5"
            cellspacing="0"
            align="center"
          >
            <tr>
              <th rowspan="2">街道</th>
              <th colspan="2">PM₁₀</th>
              <th colspan="2">O₃</th>
              <th colspan="2">PM₂.₅</th>
              <th colspan="2">NO₂</th>
              <th colspan="2">SO₂</th>
              <th colspan="2">CO</th>
              <th colspan="2">综合指数排名</th>
            </tr>
            <tr>
              <td>
                <div>浓度</div>
                <div>ug/m³</div>
              </td>
              <td>排名</td>
              <td>
                <div>浓度</div>
                <div>ug/m³</div>
              </td>
              <td>排名</td>
              <td>
                <div>浓度</div>
                <div>ug/m³</div>
              </td>
              <td>排名</td>
              <td>
                <div>浓度</div>
                <div>ug/m³</div>
              </td>
              <td>排名</td>
              <td>
                <div>浓度</div>
                <div>ug/m³</div>
              </td>
              <td>排名</td>
              <td>
                <div>浓度</div>
                <div>mg/m³</div>
              </td>
              <td>排名</td>
              <td>
                数值
              </td>
              <td>排名</td>
            </tr>
            <tr v-for="(item, index) in dataArr[1]" :key="index">
              <td>{{ item.stationName }}</td>
              <td class="even">{{ item.inhalableParticles || "-" }}</td>
              <td class="odd">{{ item.inhalableParticlesSort || "-" }}</td>
              <td class="even">{{ item.ozoneEight || "-" }}</td>
              <td class="odd">{{ item.ozoneEightSort || "-" }}</td>
              <td class="even">{{ item.fineParticulateMatter || "-" }}</td>
              <td class="odd">{{ item.fineParticulateMatterSort || "-" }}</td>
              <td class="even">{{ item.nitrogenDioxide || "-" }}</td>
              <td class="odd">{{ item.nitrogenDioxideSort || "-" }}</td>
              <td class="even">{{ item.sulfurDioxide || "-" }}</td>
              <td class="odd">{{ item.sulfurDioxideSort || "-" }}</td>
              <td class="even">{{ item.carbonMonoxide || "-" }}</td>
              <td class="odd">{{ item.carbonMonoxideSort || "-" }}</td>
              <td class="even">{{ item.aqci || "-" }}</td>
              <td class="odd">{{ item.aqciSort || "-" }}</td>
            </tr>
          </table>
        </SwiperSlide>
        <SwiperSlide>
          <div class="title">{{ yearSiteTitle }}</div>
          <table
            class="table-container"
            border="1px"
            cellpadding="5"
            cellspacing="0"
            align="center"
          >
            <tr>
              <th rowspan="2">站点</th>
              <th colspan="2">PM₁₀</th>
              <th colspan="2">O₃</th>
              <th colspan="2">PM₂.₅</th>
              <th colspan="2">NO₂</th>
              <th colspan="2">SO₂</th>
              <th colspan="2">CO</th>
              <th colspan="2">综合指数排名</th>
            </tr>
            <tr>
              <td>
                <div>浓度</div>
                <div>ug/m³</div>
              </td>
              <td>排名</td>
              <td>
                <div>浓度</div>
                <div>ug/m³</div>
              </td>
              <td>排名</td>
              <td>
                <div>浓度</div>
                <div>ug/m³</div>
              </td>
              <td>排名</td>
              <td>
                <div>浓度</div>
                <div>ug/m³</div>
              </td>
              <td>排名</td>
              <td>
                <div>浓度</div>
                <div>ug/m³</div>
              </td>
              <td>排名</td>
              <td>
                <div>浓度</div>
                <div>mg/m³</div>
              </td>
              <td>排名</td>
              <td>
                数值
              </td>
              <td>排名</td>
            </tr>
            <tr v-for="(item, index) in dataArr[2]" :key="index">
              <td>{{ item.stationName }}</td>
              <td class="even">{{ item.inhalableParticles || "-" }}</td>
              <td class="odd">{{ item.inhalableParticlesSort || "-" }}</td>
              <td class="even">{{ item.ozoneEight || "-" }}</td>
              <td class="odd">{{ item.ozoneEightSort || "-" }}</td>
              <td class="even">{{ item.fineParticulateMatter || "-" }}</td>
              <td class="odd">{{ item.fineParticulateMatterSort || "-" }}</td>
              <td class="even">{{ item.nitrogenDioxide || "-" }}</td>
              <td class="odd">{{ item.nitrogenDioxideSort || "-" }}</td>
              <td class="even">{{ item.sulfurDioxide || "-" }}</td>
              <td class="odd">{{ item.sulfurDioxideSort || "-" }}</td>
              <td class="even">{{ item.carbonMonoxide || "-" }}</td>
              <td class="odd">{{ item.carbonMonoxideSort || "-" }}</td>
              <td class="even">{{ item.aqci || "-" }}</td>
              <td class="odd">{{ item.aqciSort || "-" }}</td>
            </tr>
          </table>
        </SwiperSlide>
        <SwiperSlide>
          <div class="title">{{ monthSiteTitle }}</div>
          <table
            class="table-container"
            border="1px"
            cellpadding="5"
            cellspacing="0"
            align="center"
          >
            <tr>
              <th rowspan="2">站点</th>
              <th colspan="2">PM₁₀</th>
              <th colspan="2">O₃</th>
              <th colspan="2">PM₂.₅</th>
              <th colspan="2">NO₂</th>
              <th colspan="2">SO₂</th>
              <th colspan="2">CO</th>
              <th colspan="2">综合指数排名</th>
            </tr>
            <tr>
              <td>
                <div>浓度</div>
                <div>ug/m³</div>
              </td>
              <td>排名</td>
              <td>
                <div>浓度</div>
                <div>ug/m³</div>
              </td>
              <td>排名</td>
              <td>
                <div>浓度</div>
                <div>ug/m³</div>
              </td>
              <td>排名</td>
              <td>
                <div>浓度</div>
                <div>ug/m³</div>
              </td>
              <td>排名</td>
              <td>
                <div>浓度</div>
                <div>ug/m³</div>
              </td>
              <td>排名</td>
              <td>
                <div>浓度</div>
                <div>mg/m³</div>
              </td>
              <td>排名</td>
              <td>
                数值
              </td>
              <td>排名</td>
            </tr>
            <tr v-for="(item, index) in dataArr[3]" :key="index">
              <td>{{ item.stationName }}</td>
              <td class="even">{{ item.inhalableParticles || "-" }}</td>
              <td class="odd">{{ item.inhalableParticlesSort || "-" }}</td>
              <td class="even">{{ item.ozoneEight || "-" }}</td>
              <td class="odd">{{ item.ozoneEightSort || "-" }}</td>
              <td class="even">{{ item.fineParticulateMatter || "-" }}</td>
              <td class="odd">{{ item.fineParticulateMatterSort || "-" }}</td>
              <td class="even">{{ item.nitrogenDioxide || "-" }}</td>
              <td class="odd">{{ item.nitrogenDioxideSort || "-" }}</td>
              <td class="even">{{ item.sulfurDioxide || "-" }}</td>
              <td class="odd">{{ item.sulfurDioxideSort || "-" }}</td>
              <td class="even">{{ item.carbonMonoxide || "-" }}</td>
              <td class="odd">{{ item.carbonMonoxideSort || "-" }}</td>
              <td class="even">{{ item.aqci || "-" }}</td>
              <td class="odd">{{ item.aqciSort || "-" }}</td>
            </tr>
          </table>
        </SwiperSlide>
        <div
          class="swiper-pagination"
          style="left: 6.025rem;bottom: 1.2rem;"
          slot="pagination"
        ></div>
      </Swiper>
      <Swiper :options="outSwiperOption1" v-if="tableType == 'water'">
        <SwiperSlide>
          <div>
            <div class="title">金牛区水质自动监测数据日报</div>
          </div>
          <div>
            <table
              class="table-container"
              border="1px"
              cellpadding="5"
              cellspacing="0"
              align="center"
            >
              <thead>
                <tr style="height: 0.45rem;">
                  <th style="color:#fff;">站点</th>
                  <th style="color:#fff;">断面性质</th>
                  <th style="color:#fff;">所在河流</th>
                  <th style="color:#fff;">日均水质类别</th>
                  <th style="color:#fff;">超标项目及超标倍数</th>
                  <th style="color:#fff;">日期</th>
                </tr>
              </thead>
              <tbody v-if="waterDayTable.length != 0">
                <tr v-for="(item, index) in waterDayTable" :key="index">
                  <td style="color:#fff;">
                    {{ item.stationName ? item.stationName : "-" }}
                  </td>
                  <td class="even">
                    {{
                      item.crossSectionalNature
                        ? item.crossSectionalNature
                        : "-"
                    }}
                  </td>
                  <td class="odd" style="color:#fff;">
                    {{ item.river ? item.river : "-" }}
                  </td>
                  <td class="even" style="color:#fff;">
                    {{ item.category ? item.category : "-" }}
                  </td>
                  <td class="odd" style="color:#fff;">
                    {{
                      item.detectList
                        ? item.detectList.item + " " + item.detectList.multiple
                        : "-"
                    }}
                  </td>
                  <td class="even" style="color:#fff;">
                    {{ item.time ? item.time : "-" }}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </SwiperSlide>
        <SwiperSlide>
          <div>
            <div class="title">金牛区水质自动监测数据月报</div>
          </div>
          <div>
            <table
              class="table-container"
              border="1px"
              cellpadding="5"
              cellspacing="0"
              align="center"
            >
              <thead>
                <tr style="height: 0.45rem;">
                  <th style="color:#fff;">站点</th>
                  <th style="color:#fff;">断面性质</th>
                  <th style="color:#fff;">所在河流</th>
                  <th style="color:#fff;">日均水质类别</th>
                  <th style="color:#fff;">超标项目及超标倍数</th>
                  <th style="color:#fff;">日期</th>
                </tr>
              </thead>
              <tbody v-if="waterMonthTable.length != 0">
                <tr v-for="(item, index) in waterMonthTable" :key="index">
                  <td style="color:#fff;">
                    {{ item.stationName ? item.stationName : "-" }}
                  </td>
                  <td class="even" style="color:#fff;">
                    {{
                      item.crossSectionalNature
                        ? item.crossSectionalNature
                        : "-"
                    }}
                  </td>
                  <td class="odd" style="color:#fff;">
                    {{ item.river ? item.river : "-" }}
                  </td>
                  <td class="even" style="color:#fff;">
                    {{ item.category ? item.category : "-" }}
                  </td>
                  <td class="odd" style="color:#fff;">
                    {{
                      item.detectList
                        ? item.detectList.item + " " + item.detectList.multiple
                        : "-"
                    }}
                  </td>
                  <td class="even" style="color:#fff;">
                    {{ item.time ? item.time : "-" }}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </SwiperSlide>
        <div
          class="swiper-pagination"
          style="left: 6.025rem;bottom: 1.2rem;"
          slot="pagination"
        ></div>
      </Swiper>
      <!-- 空气 -->
      <!-- 左侧箭头 -->
      <div class="left-arrow left-arrow-air" v-if="tableType == 'air'">
        <img src="@/assets/zuo-jiantou.png" alt="" />
      </div>
      <!-- 右侧箭头 -->
      <div class="right-arrow right-arrow-air" v-if="tableType == 'air'">
        <img src="@/assets/you-jiantou.png" alt="" />
      </div>
      <!-- 水质 -->
      <!-- 左侧箭头 -->
      <div class="left-arrow left-arrow-water" v-if="tableType == 'water'">
        <img src="@/assets/zuo-jiantou.png" alt="" />
      </div>
      <!-- 右侧箭头 -->
      <div class="right-arrow right-arrow-water" v-if="tableType == 'water'">
        <img src="@/assets/you-jiantou.png" alt="" />
      </div>
      <div class="check-div">
        <div
          @click="tableType = 'air'"
          :class="{ 'check-div-active': tableType == 'air' }"
        >
          大气环境
        </div>
        <div
          @click="tableType = 'water'"
          :class="{ 'check-div-active': tableType == 'water' }"
        >
          水环境
        </div>
      </div>
      <div
        class="air-btn"
        @click="tableType = 'air'"
        :style="{ 'z-index': tableType == 'air' ? 0 : 99 }"
      ></div>
      <div
        class="water-btn"
        @click="tableType = 'water'"
        :style="{ 'z-index': tableType == 'water' ? 0 : 99 }"
      ></div>
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Vue, Watch } from "vue-property-decorator";
import { Swiper, SwiperSlide } from "vue-awesome-swiper";
import {
  yearStreet,
  monthStreet,
  yearSite,
  monthSite,
  getMonitorReport
} from "@/api/homeTable";
import { Select, Icon } from "ant-design-vue";
import moment from "moment";
import GaoDeMap from "@/components/GaoDeMap/index.vue";
import DepartmentBgMap from "@/components/GaoDeMap/departmentBgMap.vue";
@Component({
  name: "tableHome",
  components: {
    Swiper,
    SwiperSlide,
    GaoDeMap,
    DepartmentBgMap,
    ASelect: Select,
    AIcon: Icon,
    ASelectOption: Select.Option
  }
})
export default class extends Vue {
  private mapStyle = "amap://styles/0739a6632d849ae1b5e6f1c52f7d12c3";
  @Watch("waterType", { immediate: false, deep: true })
  private onWaterType(newValue: any, oldValue: any) {
    if (newValue) {
      this.getWaterTable();
    }
  }
  private outSwiperOption = {
    loop: true,
    speed: 1000,
    initialSlide: 0,
    pagination: {
      el: ".swiper-pagination",
      clickable: true
    },
    autoplay: {
      delay: 30 * 1000,
      disableOnInteraction: false
    },
    navigation: {
      nextEl: ".right-arrow-air",
      prevEl: ".left-arrow-air"
    }
  };
  private outSwiperOption1 = {
    loop: true,
    speed: 1000,
    initialSlide: 0,
    pagination: {
      el: ".swiper-pagination",
      clickable: true
    },
    autoplay: {
      delay: 30 * 1000,
      disableOnInteraction: false
    },
    navigation: {
      nextEl: ".right-arrow-water",
      prevEl: ".left-arrow-water"
    }
  };
  private yearStreetTitle = "";
  private monthStreetTitle = "";
  private yearSiteTitle = "";
  private monthSiteTitle = "";
  private dataArr: any[] = [];
  private tableType: any = "air"; // air  water
  private waterType: any = "日报";
  private waterDayTable: any = [];
  private waterMonthTable: any = [];
  mounted() {
    Promise.all([yearStreet(), monthStreet(), yearSite(), monthSite()]).then(
      res => {
        console.log(res, 'res')
        const dataArr = res.map(item => item.data.data);
        this.dataArr = dataArr;
      }
    );
    const startYear = moment()
      .startOf("year")
      .format("YYYY年MM月DD日");
    const startMonth = moment()
      .startOf("month")
      .format("YYYY年MM月DD日");
    const today = moment().format("MM月DD日");
    this.yearStreetTitle =
      startYear + "-" + today + "各街道主要污染物浓度及综合指数排名表";
    this.monthStreetTitle =
      startMonth + "-" + today + "各街道主要污染物浓度及综合指数排名表";
    this.yearSiteTitle =
      startYear + "-" + today + "各站点主要污染物浓度及综合指数排名表";
    this.monthSiteTitle =
      startMonth + "-" + today + "各站点主要污染物浓度及综合指数排名表";
    this.getWaterTable();
  }
  private back(){
    this.$router.push('/homeTable')
  }
  private waterTableChange(val: any) {
    this.waterType = val;
  }
  private getWaterTable() {
    getMonitorReport(1).then((res: any) => {
      for (const item of res.data.data) {
        if (item.category == 0) {
          item.category = "未知";
        } else if (item.category == 1) {
          item.category = "一类";
        } else if (item.category == 2) {
          item.category = "二类";
        } else if (item.category == 3) {
          item.category = "三类";
        } else if (item.category == 4) {
          item.category = "四类";
        } else if (item.category == 5) {
          item.category = "五类";
        }
      }
      this.waterDayTable = res.data.data;
    });
    getMonitorReport(2).then((res: any) => {
      for (const item of res.data.data) {
        if (item.category == 0) {
          item.category = "未知";
        } else if (item.category == 1) {
          item.category = "一类";
        } else if (item.category == 2) {
          item.category = "二类";
        } else if (item.category == 3) {
          item.category = "三类";
        } else if (item.category == 4) {
          item.category = "四类";
        } else if (item.category == 5) {
          item.category = "五类";
        }
      }
      this.waterMonthTable = res.data.data;
    });
  }
}
</script>
