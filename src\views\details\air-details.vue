<style lang="less" scoped>
@font-face {
  font-family: "DS-DIGII";
  src: url("../../assets/font/DS-DIGII.ttf");
  font-display: swap;
}
.details-main-warp {
  padding: 0.2rem 0.2rem 0.2rem;
  position: relative;
  .details-main {
    height: 100%;
    background-image: url("../../assets/kqbk.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    padding: 0.65rem 0.5rem 0.35rem;
    display: flex;
    justify-content: space-around;
    flex-direction: column;
    .details-region {
      padding: 0 0.7rem;
      margin-bottom: 0.15rem;
      .region-box {
        padding: 0.05rem 0.25rem;
        box-sizing: border-box;
        background: rgba(6, 30, 101, 1);
        border: 1px solid rgba(2, 174, 210, 1);
        display: flex;
        justify-content: space-between;
        align-items: center;
        .street {
          width: 2.6rem;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .site-type {
          width: 2.6rem;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .site-name {
          width: 3rem;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .select-time {
          width: 3.8rem;
          font-family: fontnameRegular;
          font-size: 0.36rem;
          margin-left: 0.35rem;
        }
        .line {
          width: 0.02rem;
          height: 0.7rem;
          background-image: url("../../assets/xgx.png");
          background-repeat: no-repeat;
          background-size: 100% 100%;
        }
        .time {
          width: 4rem;
          font-size: 0.32rem;
          font-family: "DS-DIGII";
          font-weight: 400;
          text-align: center;
          // display: none;
        }
        .name,
        .type {
          width: 3rem;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .type {
          > div {
            width: 1.6rem;
            height: 0.44rem;
            background: #0084ff;
            border-radius: 0.44rem;
            line-height: 0.44rem;
            font-size: 0.24rem;
            display: flex;
            justify-content: center;
          }
        }
        .name {
          // padding-left: 0.3rem;
          // width: 3.5rem;
        }
        .address {
          width: 6rem;
          font-size: 0.2rem;
          display: flex;
          align-items: center;
          // padding-left: 0.5rem;
          .address-text {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            flex: 1;
          }
          .icon {
            width: 0.3rem;
            height: 0.32rem;
            margin-right: 0.28rem;
            background-image: url("../../assets/<EMAIL>");
            background-size: 100% 100%;
          }
        }
      }
    }
    .details-top {
      // height: 2.8rem;
      margin-bottom: 0.2rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .left-arrow {
        cursor: pointer;
        width: 0.4rem;
        height: 0.4rem;
        background: rgba(7, 56, 141, 0.6);
        border: 1px solid rgba(54, 218, 234, 0.42);
      }
      .middle-wrap {
        // flex: 1;
        // height: 100%;
        min-height: 2.8rem;
        width: 17rem;
        // width: 100%;
        padding: 0 0.3rem;
        .middle-carousel {
          display: flex;
          // justify-content: space-between;
          align-items: center;
          height: calc(100% - 0.5rem);
          > div {
            width: 2rem;
            height: 2.35rem;
          }
        }
      }
      .right-arrow {
        cursor: pointer;
        width: 0.4rem;
        height: 0.4rem;
        background: rgba(7, 56, 141, 0.6);
        border: 1px solid rgba(54, 218, 234, 0.42);
      }
    }
    .details-bottom {
      height: 3.5rem;
      padding: 0 0.7rem;
      .details-bottom-chart {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .bottom-chart {
          height: 2.5rem;
        }
      }
      .bottom-btn {
        height: 0.8rem;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 8.2rem;
      }
    }
    .middle-carousel {
      .middle-carousel-title {
        height: 0.3rem;
        text-align: center;
        font-size: 0.18rem;
        font-family: PingFang SC;
        font-weight: bold;
        color: rgba(255, 255, 255, 1);
      }
      .middle-carousel-content {
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        height: calc(100% - 0.3rem);
        background: rgba(1, 41, 104, 1);
        padding: 0.1rem 0.2rem;
        > div {
          font-size: 16px;
          font-family: Source Han Sans CN;
          font-weight: 400;
          color: rgba(255, 255, 255, 1);
        }
      }
    }
    .common-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 0.2rem;
      font-family: Source Han Sans CN;
      font-weight: 500;
      color: #fff;
      text-shadow: 0 0 0.1rem #2e73d6, 0 0 0.1rem #2e73d6;
      margin-bottom: 0.1rem;
      > div {
        display: flex;
        align-items: center;
        > span:nth-child(1) {
          width: 0.05rem;
          display: inline-block;
          height: 0.25rem;
          background: #fff;
          margin-right: 0.2rem;
        }
      }
    }
    .svg-arrow {
      width: 0.35rem;
      height: 0.35rem;
    }
  }
  label {
    font-size: 0.16rem;
  }
  .ant-radio-group-solid
    .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
    color: #fff;
    background: #0084ff;
    border-color: #0084ff;
  }
  .ant-radio-button-wrapper-checked {
    z-index: 1;
    color: #0084ff;
    background: #fff;
    border-color: #0084ff;
    -webkit-box-shadow: -1px 0 0 0 #0084ff;
    box-shadow: -1px 0 0 0 #0084ff;
  }
  .ant-radio-button-wrapper:hover {
    position: relative;
    color: #0084ff;
  }
  .ant-radio-button-wrapper {
    position: relative;
    display: inline-block;
    height: 32px;
    margin: 0;
    padding: 0 30px;
    color: rgb(255, 255, 255);
    line-height: 30px;
    background: rgba(10, 45, 107, 1);
    margin-right: 0.1rem;
    border: 1px solid rgba(255, 255, 255, 0);
    border-top-width: 1.02px;
    border-left: 0;
    cursor: pointer;
    -webkit-transition: color 0.3s, background 0.3s, border-color 0.3s;
    transition: color 0.3s, background 0.3s, border-color 0.3s;
  }
  .ant-radio-button-wrapper:not(:first-child)::before {
    position: absolute;
    top: 0;
    left: -1px;
    display: block;
    width: 0px;
    height: 100%;
    background-color: #d9d9d9;
    content: "";
  }
}
.title-flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
  > div {
    display: flex;
    font-size: 0.14rem;
    font-weight: 400;

    > div {
      width: 0.8rem;
      height: 0.28rem;
      line-height: 0.3rem;
      color: #0e8bff;
      // background: rgba(14, 139, 255, 0.32);
      // border: 1px solid rgba(14, 139, 255, 1);
      text-align: center;
      // clip-path: polygon(100% 0, 100% 100%, 12% 100%, 0% 70%, 0 0);
      background: url(../../assets/<EMAIL>);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      cursor: pointer;
    }
    div.type-active {
      background: url(../../assets/<EMAIL>);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      color: rgba(0, 234, 255, 1);
    }
    :nth-of-type(2) {
      margin-left: 0.1rem;
    }
  }
}
.site-type-name {
  font-size: 0.2rem;
  color: #ffffff;
}
</style>

<style lang="less">
.title-selects {
  align-items: flex-end;
  .ant-select-selection {
    width: 1.8rem;
    height: 0.3rem;
    font-size: 0.2rem;
    // background: rgba(14, 139, 255, 0.32);
    outline: none !important;
    border: none;
    border-radius: unset;
    // clip-path: polygon(100% 0, 100% 100%, 12% 100%, 0% 70%, 0 0);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-color: transparent;
  }
  .ant-select-selection__placeholder,
  .ant-select-search__field__placeholder {
    height: 0.3rem;
    text-align: center;
    line-height: 0.3rem;
  }
  .ant-select-selection-selected-value {
    color: #00eaff;
  }
  .ant-select-focused .ant-select-selection,
  .ant-select-selection:focus,
  .ant-select-selection:active {
    border-color: transparent !important;
    border-right-width: 0 !important;
    outline: none !important;
    box-shadow: none !important;
    border: none !important;
  }
  .ant-select-selection__rendered {
    display: flex;
    // justify-content: center;
  }
}
.title-selects1 {
  .ant-select-selection {
    width: 1.1rem;
  }
}
.title-selects2 {
  .ant-select-selection {
    width: 1.5rem !important;
  }
}
.title-selects2 {
  .ant-select-selection {
    width: 1.8rem;
  }
}
.middle-carousel {
  .swiper-slide {
    height: 2.35rem !important;
  }
}
</style>

<template>
  <section class="details-main-warp">
    <div class="details-main">
      <!-- 头部区域选择 -->
      <div class="details-region">
        <div class="region-box">
          <!-- 站点类型筛选 -->
          <div class="site-type">
            <div class="site-type-name">站点类型选择：</div>
            <a-select
              v-model="siteTypeName"
              class="title-selects title-selects1"
              @change="siteTypeNameChange"
              placeholder="请选择站点类型"
            >
              <a-icon
                slot="suffixIcon"
                type="caret-down"
                style="color:#1FFFFC;font-size:0.2rem"
              />
              <a-select-option
                :value="String(index)"
                v-for="(item, index) in siteList"
                :key="index"
                >{{ item.stationType }}</a-select-option
              >
            </a-select>
          </div>
          <div class="line"></div>
          <!-- 街道筛选 -->
          <div class="street">
            <div class="site-type-name">街道筛选：</div>
            <a-select
              v-model="streetName"
              class="title-selects title-selects2"
              @change="streetNameChange"
              placeholder="请选择街道"
            >
              <a-icon
                slot="suffixIcon"
                type="caret-down"
                style="color:#1FFFFC;font-size:0.2rem"
              />
              <a-select-option
                :value="String(index)"
                v-for="(item, index) in siteList[siteTypeName].streetVOList"
                :key="index"
                >{{ item.streetName }}</a-select-option
              >
            </a-select>
          </div>
          <div class="line"></div>
          <!-- 站点名称筛选 -->
          <div class="site-name">
            <div class="site-type-name">站点名称：</div>
            <a-select
              v-model="sitetName"
              class="title-selects title-selects3"
              @change="sitetNameChange"
              placeholder="请选择站点"
            >
              <a-icon
                slot="suffixIcon"
                type="caret-down"
                style="color:#1FFFFC;font-size:0.2rem"
              />
              <a-select-option
                :value="String(index)"
                v-for="(item, index) in siteList[siteTypeName].streetVOList[
                  streetName
                ].stationList"
                :key="index"
                >{{ item.positionName }}</a-select-option
              >
            </a-select>
          </div>
          <div class="line"></div>
          <div class="address">
            <!-- <div class="icon"></div> -->
            <div class="address-text">
              站点位置：<span style="font-size:0.16rem">{{
                stationdDetails.address
              }}</span>
            </div>
          </div>
        </div>
      </div>
      <!-- 上半边部分 -->
      <div class="details-top">
        <!-- 左侧箭头 -->
        <div class="left-arrow">
          <svgicon name="left" color="#00D9D5" class="svg-arrow"></svgicon>
        </div>
        <!-- 中间部分 -->
        <div class="middle-wrap">
          <!-- 通用标题 -->
          <div class="common-title">
            <div>
              <span></span>
              <span>24小时空气质量监测</span>
            </div>
            <div style="text-shadow: none;font-size: 0.18rem;">
              （单位：μg/m³）
            </div>
          </div>
          <!-- 内容滚动部分 -->
          <swiper
            class="middle-carousel"
            :options="swiperOption"
            v-if="trendDataList.length != 0 && trendDataList.length > 8"
          >
            <swiper-slide v-for="(item, i) in trendDataList" :key="i">
              <!-- 空气 -->
              <div
                v-if="$route.query.type === 1"
                class="middle-carousel-title"
                :style="{
                  backgroundColor: item.bgColor ? item.bgColor : '#0072ff',
                }"
              >
                {{ `${item.createTime}:00` }}
              </div>
              <!-- 空气质量 -->
              <div class="middle-carousel-content">
                <div style="margin-left: 0.15rem;">
                  NO₂:
                  <span
                    :style="{ 'font-size': '0.2rem', color: item.fontColor }"
                    >{{ item.nitrogenDioxide }}</span
                  >
                </div>
                <div style="margin-left: 0.15rem;">
                  PM₂.₅:
                  <span
                    :style="{ 'font-size': '0.2rem', color: item.fontColor }"
                    >{{ item.fineParticulateMatter }}</span
                  >
                </div>
                <div style="margin-left: 0.15rem;">
                  PM₁₀:
                  <span
                    :style="{ 'font-size': '0.2rem', color: item.fontColor }"
                    >{{ item.inhalableParticles }}</span
                  >
                </div>
                <div style="margin-left: 0.15rem;">
                  O₃:
                  <span
                    :style="{ 'font-size': '0.2rem', color: item.fontColor }"
                    >{{ item.ozone }}</span
                  >
                </div>
              </div>
            </swiper-slide>
          </swiper>
          <!-- 内容滚动部分 -->
          <div
            class="middle-carousel"
            v-if="trendDataList.length != 0 && trendDataList.length <= 8"
          >
            <div
              v-for="(item, i) in trendDataList"
              :key="i"
              style="margin-right: 0.15rem;"
            >
              <!-- 空气 -->
              <div
                class="middle-carousel-title"
                :style="{
                  backgroundColor: item.bgColor ? item.bgColor : '#0072ff',
                }"
              >
                {{ `${item.createTime}:00` }}
              </div>
              <!-- 空气质量 -->
              <div class="middle-carousel-content">
                <div style="margin-left: 0.15rem;">
                  NO2:
                  <span
                    :style="{ 'font-size': '0.2rem', color: item.fontColor }"
                    >{{ item.nitrogenDioxide }}</span
                  >
                </div>
                <div style="margin-left: 0.15rem;">
                  PM2.5:
                  <span
                    :style="{ 'font-size': '0.2rem', color: item.fontColor }"
                    >{{ item.fineParticulateMatter }}</span
                  >
                </div>
                <div style="margin-left: 0.15rem;">
                  PM10:
                  <span
                    :style="{ 'font-size': '0.2rem', color: item.fontColor }"
                    >{{ item.inhalableParticles }}</span
                  >
                </div>
                <div style="margin-left: 0.15rem;">
                  O3:
                  <span
                    :style="{ 'font-size': '0.2rem', color: item.fontColor }"
                    >{{ item.ozone }}</span
                  >
                </div>
              </div>
            </div>
            <!-- <div class="swiper-pagination" slot="pagination"></div> -->
          </div>
          <div
            v-if="trendDataList.length == 0"
            class="middle-carousel"
            style="display: flex;justify-content: center;align-items: center;font-size: 0.2rem;height: 2.35rem"
          >
            设备离线
          </div>
        </div>
        <!-- 右侧箭头 -->
        <div class="right-arrow">
          <svgicon name="right" color="#00D9D5" class="svg-arrow"></svgicon>
        </div>
      </div>
      <!-- 下半边部分 -->
      <div class="details-bottom">
        <div class="details-bottom-chart">
          <div>
            <!-- 通用标题 -->
            <div class="common-title">
              <div>
                <span></span>
                <span>24小时空气质量趋势</span>
              </div>
            </div>
            <div class="bottom-chart">
              <MultiLineChart
                :id="'multi-line-trend'"
                :width="'8.2rem'"
                :height="'2.5rem'"
                :type="1"
                :airDataProp="airDataProp"
                :airContrast="airContrast"
              ></MultiLineChart>
            </div>
          </div>
          <div>
            <!-- 通用标题 -->
            <div class="common-title">
              <div style="width: 100%;justify-content: space-between;">
                <div style="display: flex;align-items: center;">
                  <span
                    style="width: 0.05rem;display: inline-block;height: 0.25rem;background: #fff;margin-right: 0.2rem;"
                  ></span>
                  <span>空气质量对比</span>
                </div>
                <div class="title-flex">
                  <div>
                    <div
                      :class="{ 'type-active': airQualityType === '同比' }"
                      @click="airQualityType = '同比'"
                    >
                      同比
                    </div>
                    <div
                      :class="{ 'type-active': airQualityType === '环比' }"
                      @click="airQualityType = '环比'"
                    >
                      环比
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="bottom-chart">
              <!-- <MultiLineChart
                :id="'multi-line-contrast'"
                :width="'8.2rem'"
                :height="'2.5rem'"
                :type="2"
                :airDataProp="airDataProp"
                :airContrast="airContrast"
              ></MultiLineChart> -->
              <LineAndBarChart
                v-if="airQualityType == '同比'"
                :id="'air-proportion'"
                :width="'8.2rem'"
                :height="'2.5rem'"
                :LineAndBarData="airChangeChartData"
              />
              <LineAndBarChartOne
                v-if="airQualityType == '环比'"
                :id="'LineAndBarChartOne'"
                :width="'8.2rem'"
                :height="'2.5rem'"
                :propData="airChangeChartData"
              />
            </div>
          </div>
        </div>
        <div class="bottom-btn">
          <a-radio-group
            v-model="elementType"
            buttonStyle="solid"
            size="large"
            @change="elementTypeChange"
          >
            <a-radio-button
              v-for="btn in ButtonGroup"
              :key="btn.value"
              :value="btn.value"
              >{{ airTypeNamePub[btn.name] }}
            </a-radio-button>
          </a-radio-group>
        </div>
      </div>
    </div>
  </section>
</template>

<script lang="ts">
/* eslint-disable @typescript-eslint/ban-ts-ignore */
import { Component, Vue, Watch } from "vue-property-decorator";
import MultiLineChart from "@/components/Charts/MultiLineChart.vue";
import LineChartDashed from "@/components/Charts/LineChartDashed.vue";
import LineAndBarChart from "@/components/Charts/LineAndBarChart.vue";
import LineAndBarChartOne from "@/components/Charts/LineAndBarChartOne.vue";
import { Radio, Select, Icon } from "ant-design-vue";
import { Swiper, SwiperSlide } from "vue-awesome-swiper";
import moment from "moment";

import {
  getAllAqiInfo,
  aqidetails,
  airStationdDetails,
  aqiTrend,
  aqcistation,
  airStationOptionList,
  stationRingAir,
} from "@/api/air";
import { getAirTypes } from "@/api/homeTable";
interface DataList {
  name: string;
  value: number;
}

interface ButtonProperty {
  name: string | number;
  value: string | number;
}

interface AirDataProp {
  airTrendYesterday: string | number[];
  airToday1: string | number[];
  airToday2: string | number[];
  airAverage: string | number[];
  unit: string;
}

interface AirContrast {
  monthList: string | number[];
  thisYear: string | number[];
  lastYear: string | number[];
  thisYearName?: string;
  lastYearName?: string;
}

interface LineChartData {
  bottomList: string[];
  dataList: string[];
  unit?: string;
}

interface WaterChageData {
  bottomList: string[];
  currentYearName: string;
  currentYearDataList: string[];
  beforeOneYearName: string;
  beforeOneYearDataList: string[];
  unit?: string;
}

enum EnterType {
  AIR = 1,
  WATER = 2,
  VEHICLE = 3,
  HOME = 4,
}

@Component({
  name: "AirDetails",
  components: {
    MultiLineChart,
    LineChartDashed,
    LineAndBarChart,
    ARadioGroup: Radio.Group,
    ARadioButton: Radio.Button,
    AIcon: Icon,
    ASelect: Select,
    ASelectOption: Select.Option,
    Swiper,
    SwiperSlide,
    LineAndBarChartOne,
  },
})
export default class extends Vue {
  @Watch("stationCode")
  public async onStationCode(newValue: string, oldValue: string) {
    // @ts-ignore
    this.getAqidetails();
    this.getAqiTrend();
    this.getYearOnYear();
    this.getAirStationdDetails();
    this.getAirTypes();
  }
  @Watch("elementType", { immediate: true, deep: true })
  public onSirTypeChange(newValue: string, oldValue: string) {
    // @ts-ignore
    this.getAqiTrend();
    // this.getYearOnYear();
  }
  // 空气质量对比
  @Watch("airQualityType", { immediate: true, deep: true })
  public onAirQualityType(newValue: any, oldValue: any) {
    if (newValue) {
      this.getYearOnYear();
    }
  }
  readonly colors: Array<string> = [
    "#1D9DFF",
    "#FF9F7F",
    "#FB7293",
    "#E7BCF3",
    "#8378EA",
    "#32C5E9",
    "#9FE6B8",
    "#FFDB5C",
  ];
  private airTypeName = {
    NO2: "μg/m³",
    O3: "μg/m³",
    PM10: "μg/m³",
    "PM2.5": "μg/m³",
    NOx: "μg/m³",
    NO: "μg/m³",
  };
  private airTypeNamePub = {
    "NO₂": "NO₂",
    "O₃": "O₃",
    "PM₁₀": "PM₁₀",
    "PM₂.₅": "PM₂.₅",
    NOx: "NOx",
    NO: "NO",
    "SO₂": "SO₂",
    CO: "CO",
  };
  private dataList!: Array<any>;

  private displayDataList: Array<DataList> = [
    {
      name: "二氧化硫",
      value: 0.13,
    },
    {
      name: "二氧化氮",
      value: 0.4,
    },
    {
      name: "PM2.5",
      value: 0.139,
    },
    {
      name: "PM10",
      value: 0.246,
    },
    {
      name: "一氧化硫",
      value: 0.145,
    },
    {
      name: "臭氧",
      value: 0.317,
    },
  ];

  private ButtonGroup: Array<ButtonProperty> = [
    // {
    //   name: "AQI",
    //   value: "aqi"
    // },
    {
      name: "NO₂",
      value: "101",
    },
    {
      name: "PM₂.₅",
      value: "105",
    },
    {
      name: "PM₁₀",
      value: "104",
    },
    {
      name: "O₃",
      value: "102",
    },
    {
      name: "SO₂",
      value: "100",
    },
    {
      name: "CO",
      value: "103",
    },
  ];

  private swiperOption: any = {
    slidesPerView: 8,
    spaceBetween: 15,
    slidesPerGroup: 1,
    loop: false,
    loopFillGroupWithBlank: true,
    pagination: {
      el: ".swiper-pagination",
      clickable: true,
    },
    autoplay: {
      delay: 30000,
      disableOnInteraction: false,
    },
    navigation: {
      nextEl: ".right-arrow",
      prevEl: ".left-arrow",
    },
  };

  readonly AirColors: Array<any> = [
    {
      color: "rgb(0,255,0)",
      min: 0,
      max: 50,
    },
    {
      color: "rgb(255,255,0)",
      min: 51,
      max: 100,
    },
    {
      color: "rgb(255,126,0)",
      min: 101,
      max: 150,
    },
    {
      color: "rgb(255,0,0)",
      min: 151,
      max: 200,
    },
    {
      color: "rgb(153,0,76)",
      min: 201,
      max: 300,
    },
    {
      color: "rgb(126,0,35)",
      min: 300,
      max: 999,
    },
  ];
  readonly NO2Colors: number = 700;
  readonly O3Colors: number = 300;
  readonly PM10Colors: number = 250;
  readonly PM25Colors: number = 115;
  private stationList = [];
  private stationCode = "";
  private elementType = "";
  private trendDataList = [];
  private airDataProp: AirDataProp = {
    airTrendYesterday: [],
    airToday1: [],
    airToday2: [],
    airAverage: [],
    unit: "",
  };
  private airContrast: AirContrast = {
    thisYear: [], // 今年
    lastYear: [], // 上年
    monthList: [],
    thisYearName: "",
    lastYearName: "",
  };
  private airQualityType: any = "同比";
  private airChangeChartData: any = {};
  private selectTimer: any;
  private airTimer: any;
  private stationdDetails: any = {};
  private siteList: any[] = [
    {
      stationType: "区控站",
      streetVOList: [
        {
          stationList: [
            {
              address: "四川省成都市金牛区百寿路9号",
              online: 1,
              positionName: "西安路",
              stationCode: "1013A",
              stationPic: null,
              stationTypeId: 1,
            },
          ],
        },
      ],
    },
  ];
  private siteTypeName: any = "0"; //  = "sk"
  private streetName: any = "0";
  private sitetName: any = "0"; // = "0"
  created() {
    // @ts-ignore
    // this.stationCode = this.$route.query.stationCode;
    this.stationCode = this.getQueryString("stationCode");
    // @ts-ignore
    this.getAirStationOptionList();
    this.getAllAirAqiInfo();
  }
  mounted() {
    // 空气质量
    this.getAqidetails();
    // this.getAqiTrend();
    this.getYearOnYear();
    this.autoTime1();
    // @ts-ignore
  }
  beforeDestroy() {
    // clearInterval(this.selectTimer);
    clearTimeout(this.airTimer);
  }
  private getQueryString(name: string) {
    const reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
    const r = location.href
      .slice(location.href.indexOf("?"))
      .substr(1)
      .match(reg);
    if (r != null) {
      return decodeURIComponent(r[2]);
    }
    return "";
  }
  // 下拉框定时器
  private autoTime() {
    const autoTimer = () => {
      for (let index = 0; index < this.stationList.length; index++) {
        this.selectTimer = setTimeout(() => {
          this.stationCode = (this.stationList[index] as any).stationCode;
          if (index === this.stationList.length - 1) {
            clearTimeout(this.selectTimer);
            setTimeout(autoTimer, 90000);
          }
        }, index * 90000);
      }
    };
    autoTimer();
  }
  private i: any = 0;
  // 污染物类型定时器
  private autoTime1() {
    // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
    // @ts-ignore
    this.elementType = this.ButtonGroup[this.i].value;
    const autoTimer = () => {
      this.airTimer = setInterval(() => {
        // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
        // @ts-ignore
        this.elementType = this.ButtonGroup[this.i].value;
        if (this.i === this.ButtonGroup.length - 1) {
          setTimeout(() => {
            clearInterval(this.airTimer);
            this.i = 0;
            // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
            // @ts-ignore
            this.elementType = this.ButtonGroup[this.i].value;
            autoTimer();
          }, 15000);
        }
        if (this.i < this.ButtonGroup.length - 1) {
          this.i = this.i + 1;
        }
      }, 15000);
    };
    autoTimer();
  }

  // 街道类型站点层级列表选择
  private getAirStationOptionList() {
    airStationOptionList().then((res: any) => {
      console.log(res, "res1");
      this.siteList = res.data.data;
      res.data.data.forEach((item1: any, index1: number) => {
        item1.streetVOList.forEach((item2: any, index2: any) => {
          item2.stationList.forEach((item3: any, index3: any) => {
            if (item3.stationCode == this.stationCode) {
              this.siteTypeName = String(index1);
              this.streetName = String(index2);
              this.sitetName = String(index3);
            }
          });
        });
      });
    });
  }

  // 站点类型
  private siteTypeNameChange(val: any) {
    this.siteTypeName = val;
    this.streetName = "0";
    this.sitetName = "0";
    this.stationCode = this.siteList[val].streetVOList[
      this.streetName
    ].stationList[this.sitetName].stationCode;
  }

  // 街道名称
  private streetNameChange(val: any) {
    this.streetName = val;
    this.sitetName = "0";
    this.stationCode = this.siteList[this.siteTypeName].streetVOList[
      val
    ].stationList[this.sitetName].stationCode;
  }

  // 站点名称
  private sitetNameChange(val: any) {
    this.sitetName = val;
    this.stationCode = this.siteList[this.siteTypeName].streetVOList[
      this.streetName
    ].stationList[val].stationCode;
  }

  // 根据站点code获取污染物列表
  private getAirTypes() {
    getAirTypes({
      stationCode: this.stationCode,
    }).then((res: any) => {
      const ButtonGroup: Array<ButtonProperty> = [];
      for (const key in res.data.data) {
        ButtonGroup.push({
          name: key,
          value: res.data.data[key],
        });
      }
      this.ButtonGroup = ButtonGroup;
    });
  }

  // 通过区代码获取全部站点aqi信息
  private getAllAirAqiInfo() {
    getAllAqiInfo({ areaCode: "510106" }).then((res: any) => {
      this.stationList = res.data.data;
      // this.autoTime();
      this.getAqiTrend();
      this.getAirStationdDetails();
    });
  }
  // 站点下拉框
  private stationChange(val: any) {
    this.stationCode = val;
  }
  // 详情页顶部站点信息
  private getAirStationdDetails() {
    airStationdDetails(this.stationCode).then((res: any) => {
      this.stationdDetails = res.data.data;
    });
  }
  // 24小时空气质量检测
  private getAqidetails() {
    aqidetails({
      stationCode: this.stationCode,
    }).then((res: any) => {
      for (const item in res.data.data) {
        if (res.data.data[item].length == 0) {
          delete res.data.data[item];
        }
      }
      for (const item in res.data.data) {
        for (const item1 in res.data.data[item]) {
          // NO2
          if (item1 == "nitrogenDioxide") {
            if (res.data.data[item].monValue > this.NO2Colors) {
              res.data.data[item].fontColor = "rgb(255,0,0)";
              res.data.data[item].bgColor = "rgb(255,0,0)";
            } else {
              res.data.data[item].fontColor = "#fff";
            }
          }
          // O3
          if (item1 == "ozone") {
            if (res.data.data[item].monValue > this.O3Colors) {
              res.data.data[item].fontColor = "rgb(255,0,0)";
              res.data.data[item].bgColor = "rgb(255,0,0)";
            } else {
              res.data.data[item].fontColor = "#fff";
            }
          }
          // PM10
          if (item1 == "inhalableParticles") {
            if (res.data.data[item].monValue > this.PM10Colors) {
              res.data.data[item].fontColor = "rgb(255,0,0)";
              res.data.data[item].bgColor = "rgb(255,0,0)";
            } else {
              res.data.data[item].fontColor = "#fff";
            }
          }
          // PM2.5
          if (item1 == "fineParticulateMatter") {
            if (res.data.data[item].monValue > this.PM25Colors) {
              res.data.data[item].fontColor = "rgb(255,0,0)";
              res.data.data[item].bgColor = "rgb(255,0,0)";
            } else {
              res.data.data[item].fontColor = "#fff";
            }
          }
        }
      }
      const dataList: any = [];
      for (const key in res.data.data) {
        dataList.push(res.data.data[key]);
        res.data.data[key].createTime = moment(
          res.data.data[key].createTime
        ).format("HH");
        console.log(res.data.data[key].createTime);
      }
      this.trendDataList = dataList;
      this.swiperOption.initialSlide = dataList.length - 7;
    });
  }
  // 24小时空气质趋势
  private getAqiTrend() {
    if (this.elementType != "" && this.stationCode != "") {
      aqiTrend({
        pollutantCode: this.elementType,
        stationCode: this.stationCode,
      }).then((res: any) => {
        if (res.data.data) {
          const airTrendYesterday = [];
          const airToday1: any[] = [];
          const airToday2: any[] = [];
          const airAverage: any[] = [];
          if (
            res.data.data.yesterday &&
            res.data.data.todayList &&
            res.data.data.todayAverage
          ) {
            for (const item of res.data.data.yesterday) {
              airTrendYesterday.push(item.monValue);
            }
            res.data.data.todayList.forEach((item: any, index: number) => {
              if (index <= new Date().getHours()) {
                airToday1.push(item.monValue);
              } else {
                airToday1.push("-");
              }
            });
            res.data.data.todayList.forEach((item: any, index: number) => {
              if (index <= new Date().getHours()) {
                airToday2.push("-");
              } else {
                airToday2.push(item.monValue);
              }
            });
            airToday2[airToday2.lastIndexOf("-")] =
              airToday1[airToday2.lastIndexOf("-")];
            for (let index = 0; index <= 24; index++) {
              airAverage.push(res.data.data.todayAverage);
            }
          }
          if (this.elementType == "103") {
            this.airDataProp.unit = "mg/m³";
          } else {
            this.airDataProp.unit = "μg/m³";
          }
          this.airDataProp.airTrendYesterday = airTrendYesterday;
          this.airDataProp.airToday1 = airToday1;
          this.airDataProp.airToday2 = airToday2;
          this.airDataProp.airAverage = airAverage;
        }
      });
    }
  }
  // 切换按钮组标签
  private elementTypeChange(e: any) {
    this.elementType = e.target.value;
    // @ts-ignore
    this.ButtonGroup.find((item: any, index: number) => {
      if (item.value == e.target.value) {
        this.i = index;
        return;
      }
    });
  }
  private currYearName: number = new Date().getFullYear();
  // 空气质量对比
  private getYearOnYear() {
    if (this.airQualityType == "同比") {
      aqcistation(this.stationCode).then((res: any) => {
        // const airContrast: AirContrast = {
        //   thisYear: [], // 今年
        //   lastYear: [], // 上年
        //   monthList: [],
        //   thisYearName: "今年",
        //   lastYearName: "去年"
        // };
        // for (const item of res.data.data.thisYear) {
        //   (airContrast.thisYear as any).push(item.aqci);
        // }
        // for (const item of res.data.data.lastYear) {
        //   (airContrast.lastYear as any).push(item.aqci);
        // }
        // const leng = 12 - airContrast.lastYear.length;
        // for (let index = 0; index < leng; index++) {
        //   (airContrast.lastYear as any).unshift("-");
        // }
        // for (let index = 1; index <= 12; index++) {
        //   (airContrast.monthList as any).push(index + "月");
        // }
        // this.airContrast = airContrast;
        const airChangeChartData: any = {
          bottomList: [
            "1",
            "2",
            "3",
            "4",
            "5",
            "6",
            "7",
            "8",
            "9",
            "10",
            "11",
            "12",
          ],
          currentYearName: `${this.currYearName}年`,
          currentYearDataList: [],
          beforeOneYearName: `${this.currYearName - 1}年`,
          beforeOneYearDataList: [],
          sameRatioName: "同比率",
          sameRatio: [],
          unit: "指数",
          unit1: "同比率(%)",
        };
        if (res.data.data.lastYear.length > 0) {
          console.log(
            res.data.data.lastYear[0].createDate,
            Number(res.data.data.lastYear[0].createDate.split("-")[1])
          );
          if (Number(res.data.data.lastYear[0].createDate.split("-")[1]) != 1) {
            for (
              let index = 0;
              index <
              Number(res.data.data.lastYear[0].createDate.split("-")[1]);
              index++
            ) {
              (airChangeChartData.beforeOneYearDataList as any).push("-");
            }
          }
          for (const item of res.data.data.lastYear) {
            (airChangeChartData.beforeOneYearDataList as any).push(item.aqci);
          }
        } else {
          for (let index = 0; index < 12; index++) {
            (airChangeChartData.beforeOneYearDataList as any).push("-");
          }
          for (const item of res.data.data.lastYear) {
            (airChangeChartData.beforeOneYearDataList as any).push(item.aqci);
          }
        }
        // for (const item of res.data.data.lastYear) {
        //   (airChangeChartData.beforeOneYearDataList as any).push(item.aqci);
        // }
        // const leng = 12 - res.data.data.thisYear.length;
        // for (const item of res.data.data.thisYear) {
        //   (airChangeChartData.currentYearDataList as any).push(item.aqci);
        // }
        // for (let index = 0; index < leng; index++) {
        //   (airChangeChartData.currentYearDataList as any).push("-");
        // }

        airChangeChartData.currentYearDataList = [
          "1",
          "2",
          "3",
          "4",
          "5",
          "6",
          "7",
          "8",
          "9",
          "10",
          "11",
          "12",
        ].map((item: any) => {
          const curr = res.data.data.thisYear.findIndex(
            (items: any) => items.month == item
          );
          return curr > -1 ? res.data.data.thisYear[curr].aqci : "-";
        });
        for (let index = 0; index < res.data.data.sameRatio.length; index++) {
          if (res.data.data.sameRatio[index].value != 0) {
            (airChangeChartData.sameRatio as any).push(
              res.data.data.sameRatio[index].value
            );
          } else {
            (airChangeChartData.sameRatio as any).push("-");
          }
        }
        // res.data.data.sameRatio.forEach((item: any, index: number) => {
        //   if (new Date().getMonth() >= index) {
        //     (airChangeChartData.sameRatio as any).push(item.value);
        //   }
        // });
        this.airChangeChartData = airChangeChartData;
      });
    } else {
      stationRingAir(this.stationCode).then((res: any) => {
        const airChangeChartData: any = {
          bottomList: [],
          monthName: "",
          monthList: [],
          ringRatioName: "环比率",
          ringRatioList: [],
          unit: "指数",
          unit1: "环比率(%)",
        };
        if (res.data.data.ringRatio.length !== res.data.data.aqci.length) {
          const leng =
            res.data.data.aqci.length - res.data.data.ringRatio.length;
          for (let index = 0; index < leng; index++) {
            airChangeChartData.ringRatioList.push("-");
          }
        }
        for (const item of res.data.data.ringRatio) {
          airChangeChartData.ringRatioList.push(item.value);
        }
        for (const item of res.data.data.aqci) {
          airChangeChartData.monthList.push(item.aqci);
          airChangeChartData.bottomList.push(item.createDate);
        }
        this.airChangeChartData = airChangeChartData;
      });
    }
  }
}
</script>
