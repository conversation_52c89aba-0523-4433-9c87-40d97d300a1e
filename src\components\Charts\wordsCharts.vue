<template>
  <div :id="id" :style="{ height: height, width: width }" />
</template>
<script lang="ts">
import * as echarts from 'echarts'
import { Component, Prop, Watch } from "vue-property-decorator";
import { mixins } from "vue-class-component";
import ResizeMixin from "./mixins/resize";
@Component({
  name: "wordsCharts"
})
export default class extends mixins(ResizeMixin) {
  @Prop({ default: "chart" }) private id!: string;
  @Prop({ default: "200px" }) private width!: string;
  @Prop({ default: "200px" }) private height!: string;
  @Prop({ required: true }) private propData!: any;
  @Watch("propData", { immediate: true, deep: true })
  public onMsgChanged(newValue: any, oldValue: any) {
    this.propData = newValue;
    if (this.chart) {
      this.chart.clear();
    }
    this.$nextTick(() => {
      this.initChart();
    });
  }
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
  }
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.clear();
    this.chart.dispose();
    this.chart = null;
  }
  private initChart() {

    if(this.chart === null || this.chart === undefined) {
      this.chart = echarts.init(
        document.getElementById(this.id) as HTMLDivElement
      );
    }
    this.chart.setOption({
      animationDuration: 3000,
      // 'backgroundColor': '#031739',
      tooltip: {
        show: true,
        textStyle: {
          fontSize: "16",
          color: "#3c3c3c"
        },
        transitionDuration: 0,
        backgroundColor: "#fff",
        borderColor: "#ddd",
        borderWidth: 1
      },
      series: [
        {
          // 'name': '积分排行',
          type: "wordCloud",
          gridSize: 20,
          sizeRange: [12, 30],
          rotationRange: [0, 0],
          shape: "circle",
          autoSize: {
            enable: true,
            minSize: 18
          },
          data: this.propData
        }
      ]
    } as {});
  }
}
</script>
