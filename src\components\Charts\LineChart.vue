<template>
  <div
    :id="id"
    :style="{ height: height, width: width }"
    v-if="propData.dataList.length != 0"
  />
  <div v-else>
    <img
      style="width:1.7rem;position:relative;left:50%;top:0.3rem;transform: translateX(-50%)"
      src="@/assets/pollution-not.png"
      alt=""
    />
  </div>
</template>

<script lang="ts">
import * as echarts from 'echarts'
import {Component, Prop, Watch} from "vue-property-decorator";
import {mixins} from "vue-class-component";
import ResizeMixin from "./mixins/resize";

import {AirData} from "@/types";

@Component({
  name: "LineChart"
})
export default class extends mixins(ResizeMixin) {
  @Prop({ default: "chart" }) private id!: string;
  @Prop({ default: "200px" }) private width!: string;
  @Prop({ default: "200px" }) private height!: string;
  @Prop({ required: true }) private propData!: AirData;
  @Prop({ required: true }) private lineColor!: string;
  // private chart: any = null;
  private option: any = {};
  @Watch("propData", { immediate: true, deep: true }) public onMsgChanged(
    newValue: AirData,
    oldValue: AirData
  ) {
    this.propData = newValue;
    if (newValue.dataList.length) {
      if (this.chart) {
        this.chart.clear();
      }
      this.$nextTick(() => {
        this.initChart();
      });
    }
  }
  mounted() {
    setTimeout(() => {
      this.initChart();
    }, 2000);
  }
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.clear();
    this.chart.dispose();
    this.chart = null;
  }
  private initChart() {

    if(this.chart === null || this.chart === undefined) {
      this.chart = echarts.init(
        document.getElementById(this.id) as HTMLDivElement
      );
    }
    const itemStyleColor = {
      SO2: {
        color: (params: any) => {
          if (params.data >= 0 && params.data <= 50) {
            return "rgb(0,255,0)";
          } else if (params.data > 50 && params.data <= 150) {
            return "rgb(255,255,0)";
          } else if (params.data > 150 && params.data <= 475) {
            return "rgb(255,126,0)";
          } else if (params.data > 475 && params.data <= 800) {
            return "rgb(255,0,0)";
          } else if (params.data > 800 && params.data <= 1600) {
            return "rgb(153,0,76)";
          } else {
            return "rgb(126,0,35)";
          }
        }
      },
      NO2: {
        color: (params: any) => {
          if (params.data >= 0 && params.data <= 40) {
            return "rgb(0,255,0)";
          } else if (params.data > 40 && params.data <= 80) {
            return "rgb(255,255,0)";
          } else if (params.data > 80 && params.data <= 180) {
            return "rgb(255,126,0)";
          } else if (params.data > 180 && params.data <= 280) {
            return "rgb(255,0,0)";
          } else if (params.data > 280 && params.data <= 565) {
            return "rgb(153,0,76)";
          } else {
            return "rgb(126,0,35)";
          }
        }
      },
      O3: {
        color: (params: any) => {
          if (params.data >= 0 && params.data <= 160) {
            return "rgb(0,255,0)";
          } else if (params.data > 160 && params.data <= 200) {
            return "rgb(255,255,0)";
          } else if (params.data > 200 && params.data <= 300) {
            return "rgb(255,126,0)";
          } else if (params.data > 300 && params.data <= 400) {
            return "rgb(255,0,0)";
          } else if (params.data > 400 && params.data <= 800) {
            return "rgb(153,0,76)";
          } else {
            return "rgb(126,0,35)";
          }
        }
      },
      CO: {
        color: (params: any) => {
          if (params.data >= 0 && params.data <= 2) {
            return "rgb(0,255,0)";
          } else if (params.data > 2 && params.data <= 4) {
            return "rgb(255,255,0)";
          } else if (params.data > 4 && params.data <= 14) {
            return "rgb(255,126,0)";
          } else if (params.data > 14 && params.data <= 24) {
            return "rgb(255,0,0)";
          } else if (params.data > 24 && params.data <= 36) {
            return "rgb(153,0,76)";
          } else {
            return "rgb(126,0,35)";
          }
        }
      },
      PM25: {
        color: (params: any) => {
          if (params.data >= 0 && params.data <= 35) {
            return "rgb(0,255,0)";
          } else if (params.data > 35 && params.data <= 75) {
            return "rgb(255,255,0)";
          } else if (params.data > 75 && params.data <= 115) {
            return "rgb(255,126,0)";
          } else if (params.data > 115 && params.data <= 150) {
            return "rgb(255,0,0)";
          } else if (params.data > 150 && params.data <= 250) {
            return "rgb(153,0,76)";
          } else {
            return "rgb(126,0,35)";
          }
        }
      },
      PM10: {
        color: (params: any) => {
          if (params.data >= 0 && params.data <= 50) {
            return "rgb(0,255,0)";
          } else if (params.data > 50 && params.data <= 150) {
            return "rgb(255,255,0)";
          } else if (params.data > 150 && params.data <= 250) {
            return "rgb(255,126,0)";
          } else if (params.data > 250 && params.data <= 350) {
            return "rgb(255,0,0)";
          } else if (params.data > 350 && params.data <= 420) {
            return "rgb(153,0,76)";
          } else {
            return "rgb(126,0,35)";
          }
        }
      },
      n: {
        color: "white", //改变折线点的颜色
        borderColor: "#05C1A6",
        borderWidth: 2
      }
    };
    this.chart.setOption({
      backgroundColor: "transparent",
      grid: {
        height: 120,
        bottom: 20,
        left: 40,
        right: 20
      },
      xAxis: {
        type: "category",
        boundaryGap: false,
        nameTextStyle: {
          color: "#fff"
        },
        data: this.propData.bottomList,
        axisLabel: {
          textStyle: {
            fontSize: 12,
            color: "white"
          }
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        splitLine: {
          show: false
        }
      },
      yAxis: {
        type: "value",
        name: this.propData.unit,
        nameTextStyle: {
          color: "#fff",
          fontSize: 14
        },
        axisLabel: {
          textStyle: {
            fontSize: 12,
            color: "white"
          }
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        splitLine: {
          show: false
        }
      },
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "none",
          lineStyle: {
            color: this.lineColor
          }
        }
      },
      series: [
        {
          // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
          // @ts-ignore
          name: "数值",
          data: this.propData.dataList,
          type: "line",
          lineStyle: {
            color: this.lineColor //改变折线颜色
          },
          symbol: "circle",
          symbolSize: 0,
          smooth: true,
          itemStyle: this.propData.colorType
            ? itemStyleColor[this.propData.colorType as string]
            : {
                color: this.lineColor, //改变折线点的颜色
                borderColor: this.lineColor
                // borderWidth: 2
              },
          areaStyle: {
            color: this.lineColor,
            opacity: 0.3
          }
        },
        {
          // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
          // @ts-ignore
          type: "bar",
          tooltip: {
            show: false
          },
          barWidth: 1,
          hoverAnimation: false,
          data: this.propData.dataList,
          itemStyle: {
            normal: {
              color: this.lineColor,
              opacity: 0.6,
              label: {
                show: false
              }
            }
          }
        }
      ]
    } as EChartOption<EChartOption>);
  }
}
</script>
