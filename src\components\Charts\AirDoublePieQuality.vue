<template>
  <div
    :id="id"
    :class="className"
    :style="{ height: height, width: width }"
  ></div>
</template>

<script lang="ts">
import * as echarts from 'echarts'
import { Component, Prop, Watch } from "vue-property-decorator";
import { mixins } from "vue-class-component";
import ResizeMixin from "./mixins/resize";
@Component({
  name: "AirDoublePieQuality"
})
export default class extends mixins(ResizeMixin) {
  @Prop({ default: "chart" }) private className!: string;
  @Prop({ default: "chart" }) private id!: string;
  @Prop({ default: "200px" }) private width!: string;
  @Prop({ default: "200px" }) private height!: string;
  @Prop({ required: true, default: () => [] }) private propData!: any;
  @Watch("propData", { immediate: true, deep: true })
  public onAirTypeNum(newValue: any, oldValue: any) {
    this.$nextTick(() => {
      if (newValue) {
        if (newValue.dataList && newValue.dataList.length !== 0) {
          this.initChart();
        }
      }
    });
  }
  mounted() {
    this.$nextTick(() => {
      // this.initChart();
    });
  }
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.clear();
    this.chart.dispose();
    this.chart = null;
  }

  private initChart() {
    if(this.chart === null || this.chart === undefined) {
      this.chart = echarts.init(
        document.getElementById(this.id) as HTMLDivElement
      );
    }
    console.log(this.propData);
    const series: any = [
      {
        name: "Line 1",
        type: "pie",
        // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
        // @ts-ignore
        clockWise: true,
        hoverAnimation: false,
        radius: ["70%", "80%"],
        center: ["50%", "60%"],
        labelLine: {
          show: false
        },
        label: {
          show: false
        },
        data: [
          {
            value: this.propData.dataList[0]
              ? 100 - Number(this.propData.dataList[0].value)
              : 0,
            itemStyle: {
              color: "transparent"
            }
          },
          {
            value: this.propData.dataList[0]
              ? this.propData.dataList[0].value
              : 0,
            name: this.propData.dataList[0]
              ? this.propData.dataList[0].name
              : ""
          }
        ]
      },
      {
        name: "Line 2",
        type: "pie",
        clockWise: true,
        radius: ["59%", "68%"],
        center: ["50%", "60%"],
        hoverAnimation: false,
        labelLine: {
          show: false
        },
        label: {
          show: false
        },
        data: [
          {
            value: this.propData.dataList[1]
              ? 100 - Number(this.propData.dataList[1].value)
              : 0,
            itemStyle: {
              color: "transparent"
            }
          },
          {
            value: this.propData.dataList[1]
              ? this.propData.dataList[1].value
              : 0,
            name: this.propData.dataList[1]
              ? this.propData.dataList[1].name
              : ""
          }
        ]
      },
      {
        name: "Line 3",
        type: "pie",
        clockWise: true,
        hoverAnimation: false,
        radius: ["49%", "57%"],
        center: ["50%", "60%"],
        labelLine: {
          show: false
        },
        label: {
          show: false
        },
        data: [
          {
            value: this.propData.dataList[2]
              ? 100 - Number(this.propData.dataList[2].value)
              : 0,
            itemStyle: {
              color: "transparent"
            }
          },
          {
            value: this.propData.dataList[2]
              ? this.propData.dataList[2].value
              : 0,
            name: this.propData.dataList[2]
              ? this.propData.dataList[2].name
              : ""
          }
        ]
      },
      {
        name: "Line 4",
        type: "pie",
        clockWise: true,
        hoverAnimation: false,
        radius: ["40%", "47%"],
        center: ["50%", "60%"],
        labelLine: {
          show: false
        },
        label: {
          show: false
        },
        data: [
          {
            value: this.propData.dataList[3]
              ? 100 - Number(this.propData.dataList[3].value)
              : 0,
            itemStyle: {
              color: "transparent"
            }
          },
          {
            value: this.propData.dataList[3]
              ? this.propData.dataList[3].value
              : 0,
            name: this.propData.dataList[3]
              ? this.propData.dataList[3].name
              : ""
          }
        ]
      },
      {
        name: "Line 5",
        type: "pie",
        clockWise: true,
        hoverAnimation: false,
        radius: ["32%", "38%"],
        center: ["50%", "60%"],
        labelLine: {
          show: false
        },
        label: {
          show: false
        },
        data: [
          {
            value: this.propData.dataList[4]
              ? 100 - Number(this.propData.dataList[4].value)
              : 0,
            itemStyle: {
              color: "transparent"
            }
          },
          {
            value: this.propData.dataList[4]
              ? this.propData.dataList[4].value
              : 0,
            name: this.propData.dataList[4]
              ? this.propData.dataList[4].name
              : ""
          }
        ]
      },
      {
        name: "Line 6",
        type: "pie",
        clockWise: true,
        hoverAnimation: false,
        radius: ["25%", "30%"],
        center: ["50%", "60%"],
        labelLine: {
          show: false
        },
        label: {
          show: false
        },
        data: [
          {
            value: this.propData.dataList[5]
              ? 100 - Number(this.propData.dataList[5].value)
              : 0,
            itemStyle: {
              color: "transparent"
            }
          },
          {
            value: this.propData.dataList[5]
              ? this.propData.dataList[5].value
              : 0,
            name: this.propData.dataList[5]
              ? this.propData.dataList[5].name
              : ""
          }
        ]
      }
    ];
    series.length = this.propData.dataList.length;
    this.chart.setOption({
      color: ["#D1D1D1", "#A5EEE7", "#37BAF0", "#00CCCC", "#67FF9A", "#6875F5"],
      grid: [
        {
          top: 20,
          // height: 60,
          left: 30
        }
      ],
      yAxis: [
        {
          type: "category",
          inverse: true,
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            interval: 0,
            inside: true,
            margin: 42,
            // lineHeight: 60,
            textStyle: {
              color: "#00EAFF",
              fontSize: 8
            }
          },
          // data: ["优", "良", "轻度污染", "中度污染", "重度污染", "严重污染"]
          data: this.propData.bottomList
        }
      ],
      xAxis: [
        {
          show: false
        }
      ],
      series: series
    } as EChartOption);
  }
}
</script>
