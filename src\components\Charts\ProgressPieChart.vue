<template>
  <div>
    <div
      :id="id"
      :class="className"
      :style="{ height: height, width: width }"
    ></div>
  </div>
</template>

<script lang="ts">
import * as echarts from 'echarts'
import { Component, Prop, Watch } from "vue-property-decorator";
import { mixins } from "vue-class-component";
import ResizeMixin from "./mixins/resize";

@Component({
  name: "ProgressPieChart"
})
export default class extends mixins(ResizeMixin) {
  @Prop({ default: "chart" }) private className!: string;
  @Prop({ default: "chart" }) private id!: string;
  @Prop({ default: "200px" }) private width!: string;
  @Prop({ default: "200px" }) private height!: string;
  @Prop({ required: true }) private propData!: any;
  @Watch("propData", { immediate: true, deep: true })
  public onMsgChanged(newValue: string, oldValue: string) {
    this.propData = newValue;
    this.$nextTick(() => {
      this.initChart();
    });
  }
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
  }

  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.clear();
    this.chart.dispose();
    this.chart = null;
  }

  private initChart() {

    if(this.chart === null || this.chart === undefined) {
      this.chart = echarts.init(
        document.getElementById(this.id) as HTMLDivElement
      );
    }
    this.chart.setOption({
      title: [
        // {
        //   text: `${this.propData.now}截止`,
        //   textStyle: {
        //     color: "#fff",
        //     fontSize: 14
        //   },
        //   right: 0
        // }
        // {
        //   text: "主要污染物:PM10、NO2",
        //   textStyle: {
        //     color: "#fff",
        //     fontSize: 14
        //   },
        //   right: 0,
        //   bottom: 15
        // }
      ],
      series: [
        {
          // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
          // @ts-ignore
          name: "AQI优良率",
          type: "pie",
          center: ["50%", "48%"],
          radius: ["75%", "80%"],
          label: {
            show: false
          },
          hoverAnimation: false,
          clockwise: false,
          data: [
            {
              value: (1 - Number(this.propData.aqiGoodRatio)) * 100,
              name: "invisible",
              itemStyle: {
                borderWidth: 7, // 控制圆环端点圆弧
                borderColor: "#14417D"
              }
            },
            {
              value: Number(this.propData.aqiGoodRatio) * 100,
              label: {
                normal: {
                  rich: {
                    a: {
                      color: "#fff",
                      align: "center",
                      fontSize: 22,
                      fontWeight: "bold"
                    },
                    b: {
                      color: "#fff",
                      align: "center",
                      fontSize: 12
                    }
                  },
                  formatter: function(params: any) {
                    return (
                      `{b|${params.seriesName}}\n` + `\n{a|${params.value}%}`
                    );
                  },
                  position: "center",
                  show: true,
                  textStyle: {
                    fontSize: "14",
                    fontWeight: "normal",
                    color: "#fff"
                  }
                }
              },
              itemStyle: {
                borderWidth: 7, // 控制圆环端点圆弧
                borderColor: "#19BBFE"
              }
            }
          ]
        }
        // {
        //   name: "主要污染物:PM10、NO2",
        //   type: "pie",
        //   center: ["75%", "48%"],
        //   radius: [0, 60],
        //   selectedMode: "single",
        //   label: {
        //     position: "inner",
        //     formatter: "{b}\n{d}"
        //   },
        //   markPoint: {
        //     label: {
        //       show: true,
        //       position: "bottom",
        //       color: "#fff",
        //       formatter: "{a}"
        //     }
        //   },
        //   data: [
        //     {
        //       value: this.propData.pollutantCount.nitrogenDioxide,
        //       name: "NO2",
        //       itemStyle: {
        //         color: "#0E6DE9"
        //       }
        //     },
        //     {
        //       value: this.propData.pollutantCount.inhalableParticles,
        //       name: "PM10",
        //       itemStyle: {
        //         color: "#AC4ED3"
        //       }
        //     },
        //     {
        //       value: this.propData.pollutantCount.ozoneEight,
        //       name: "O3",
        //       itemStyle: {
        //         color: "#E6AF08"
        //       }
        //     }
        //   ]
        // }
      ],
      animationEasing: "elasticOut",
      animationEasingUpdate: "elasticOut",
      animationDelay(idx: number) {
        return idx * 20;
      },
      animationDelayUpdate(idx: number) {
        return idx * 20;
      }
    } as EChartOption<EChartOption>);
  }
}
</script>
