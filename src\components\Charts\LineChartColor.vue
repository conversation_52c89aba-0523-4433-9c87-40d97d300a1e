<template>
  <div :id="id" :style="{ height: height, width: width }" />
</template>
<script lang="ts">
import * as echarts from 'echarts'
import { Component, Prop, Watch } from "vue-property-decorator";
import { mixins } from "vue-class-component";
import ResizeMixin from "./mixins/resize";
interface AirData {
  bottomList: string[];
  dataList: string[];
}
@Component({
  name: "LineChartColor"
})
export default class extends mixins(ResizeMixin) {
  // @Prop({ default: "chart" }) private className!: string;
  @Prop({ default: "chart" }) private id!: string;
  @Prop({ default: "200px" }) private width!: string;
  @Prop({ default: "200px" }) private height!: string;
  @Prop({ required: true }) private propData!: AirData;
  private dataList1: string[] = [];
  private dataList2: string[] = [];
  @Watch("propData", { immediate: true, deep: true })
  public onMsgChanged(newValue: AirData, oldValue: AirData) {
    this.propData = newValue;
    if (this.chart) {
      this.chart.clear();
    }
    this.$nextTick(() => {
      this.dataList1 = this.propData.dataList.map((item, index) => {
        return index < 4 ? item : "-";
      });
      this.dataList2 = this.propData.dataList.map((item, index) => {
        return index > 2 ? item : "-";
      });
      this.initChart();
    });
  }
  // AQI
  readonly AQIAirColors: Array<any> = [
    {
      color: "rgb(0,255,0)",
      min: 0,
      max: 50,
      text: "优"
    },
    {
      color: "rgb(255,255,0)",
      min: 51,
      max: 100,
      text: "良"
    },
    {
      color: "rgb(255,126,0)",
      min: 101,
      max: 150,
      text: "轻度污染"
    },
    {
      color: "rgb(255,0,0)",
      min: 151,
      max: 200,
      text: "中度污染"
    },
    {
      color: "rgb(153,0,76)",
      min: 201,
      max: 300,
      text: "重度污染"
    },
    {
      color: "rgb(126,0,35)",
      min: 300,
      max: 999,
      text: "严重污染"
    }
  ];
  mounted() {
    this.$nextTick(() => {
      this.dataList1 = this.propData.dataList.map((item, index) => {
        return index < 4 ? item : "-";
      });
      this.dataList2 = this.propData.dataList.map((item, index) => {
        return index > 2 ? item : "-";
      });
      this.initChart();
    });
  }

  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.clear();
    this.chart.dispose();
    this.chart = null;
  }

  private initChart() {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const _this = this;

    if(this.chart === null || this.chart === undefined) {
      this.chart = echarts.init(
        document.getElementById(this.id) as HTMLDivElement
      );
    }
    this.chart.setOption({
      backgroundColor: "transparent",
      grid: {
        height: "80%",
        bottom: 20,
        left: "left",
        containLabel: true
      },
      xAxis: {
        show: true,
        type: "category",
        data: this.propData.bottomList,
        axisLabel: {
          show: true,
          textStyle: {
            fontSize: 12,
            color: "white"
          }
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        }
      },
      yAxis: [
        {
          type: "value",
          max: "300",
          axisLabel: {
            textStyle: {
              fontSize: 12,
              color: "white"
            }
          },
          axisLine: {
            lineStyle: {
              width: 7,
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: "rgb(126,0,35)"
                  },
                  {
                    offset: 0.2,
                    color: "rgb(153,0,76)"
                  },
                  {
                    offset: 0.4,
                    color: "rgb(255,0,0)"
                  },
                  {
                    offset: 0.6,
                    color: "rgb(255,126,0)"
                  },
                  {
                    offset: 0.8,
                    color: "rgb(255,255,0)"
                  },
                  {
                    offset: 1,
                    color: "rgb(0,255,0)"
                  }
                ],
                global: false // 缺省为 false
              }
            }
          },
          axisTick: {
            show: false
          },
          splitLine: {
            show: false
          }
        }
        // {
        //   axisLabel: {
        //     textStyle: {
        //       fontSize: 12,
        //       color: "white",
        //       align: "center"
        //     }
        //   },
        //   axisLine: {
        //     show: false
        //   },
        //   axisTick: {
        //     show: false
        //   },
        //   offset: 20,
        //   data: ["优", "良", "轻度污染", "中度污染", "重度污染", "严重污染"]
        // }
      ],
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "line",
          label: {
            backgroundColor: "#6a7985"
          }
        },
        formatter: function(params: Array<any>): string {
          let ret = `${params[0].name}`;

          params.forEach((series: any) => {
            let AQIText = "";
            for (const item1 of _this.AQIAirColors) {
              if (series.value >= item1.min && series.value <= item1.max) {
                AQIText = item1.text;
              }
            }
            if (new RegExp(`${series.seriesName}`, "g").test(ret)) return;
            if (series.value !== "-") {
              ret += `<br/>${series.seriesName}: ${series.value}<br/>AQI等级: ${AQIText}`;
            }
          });
          return ret;
        }
      },
      series: [
        {
          type: "line",
          name: "指数",
          data: this.dataList1,
          lineStyle: {
            color: "white" //改变折线颜色
          },
          label: {
            show: false,
            position: "top",
            distance: 2,
            color: "white",
            fontSize: 12,
            formatter: "{b}"
          },
          symbolSize: 8,
          symbol: "circle",
          color: "yellow",
          itemStyle: {
            color: (params: any) => {
              if (params.data >= 0 && params.data < 50) {
                return "rgb(0,255,0)";
              } else if (params.data >= 50 && params.data < 100) {
                return "rgb(255,255,0)";
              } else if (params.data >= 100 && params.data < 150) {
                return "rgb(255,126,0)";
              } else if (params.data >= 150 && params.data < 200) {
                return "rgb(255,0,0)";
              } else if (params.data >= 200 && params.data < 250) {
                return "rgb(153,0,76)";
              } else {
                return "rgb(126,0,35)";
              }
            }
          }
        },
        {
          name: "指数",
          data: this.dataList2,
          type: "line",
          lineStyle: {
            color: "white", //改变折线颜色
            type: "dashed"
          },
          label: {
            show: false,
            position: "top",
            distance: 2,
            color: "white",
            fontSize: 12,
            formatter: "{b}"
          },
          symbolSize: 8,
          symbol: "circle",
          color: "yellow",
          itemStyle: {
            color: (params: any) => {
              if (params.data >= 0 && params.data < 50) {
                return "#0EE790";
              } else if (params.data >= 50 && params.data < 100) {
                return "#F6DB3E";
              } else if (params.data >= 100 && params.data < 150) {
                return "#FB6634";
              } else if (params.data >= 150 && params.data < 200) {
                return "#F54133";
              } else if (params.data >= 200 && params.data < 250) {
                return "#CE1A79";
              } else {
                return "#A81CF6";
              }
            }
          }
        }
      ]
    } as {});
  }
}
</script>
