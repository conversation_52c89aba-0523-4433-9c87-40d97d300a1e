<template>
  <div id="statEachrts"></div>
</template>


<script>
import * as echarts from 'echarts'
import 'echarts-gl'
import dayjs from 'dayjs'
export default {
  name: 'cardBox',
  props: {
    statisticsData: {
      type: Array,
      default: [],
    },
  },
  data() {
    return {
      thisYear: dayjs().format('YYYY') + '年',
      lastYear: Number(dayjs().format('YYYY') - 1) + '年',
      types: ['1类', '2类', '3类', '4a类', '4b类'],
    }
  },
  watch: {
    statisticsData: {
      handler(newVal) {
        let types = []
        let thisY = []
        let lastY = []
        newVal.forEach((v) => {
          types.push(v.type)
          thisY.push(v.thisYear)
          lastY.push(v.lastYear)
        })
        this.creatEharts(thisY, lastY, types)
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {
    let types = []
    let thisY = []
    let lastY = []
    this.statisticsData.forEach((v) => {
      types.push(v.type)
      thisY.push(v.thisYear)
      lastY.push(v.lastYear)
    })
    this.creatEharts(thisY, lastY, types)
  },
  methods: {
    creatEharts(thisY, lastY, types) {
      let option = {}
      let myChart = echarts.init(document.getElementById('statEachrts'))
      option = {
        backgroundColor: '',
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
          formatter: '{b}<br/>{a0}: {c0}%<br/>{a1}: {c1}% ',
        },
        legend: {
          top: '-2%',
          right: '6%',
          itemWidth: 12,
          itemHeight: 5,
          // itemGap: 343,
          icon: 'horizontal',
          zlevel: 999,
          z: 9999,
          textStyle: {
            color: '#B6D0D8',
            fontSize: 13,
          },
          data: [this.lastYear, this.thisYear],
        },
        grid: {
          left: '3%',
          right: '-1%',
          bottom: '1%',
          top: '14%',
          containLabel: true,
        },
        xAxis: [
          {
            type: 'category',
            data: types || this.types,
            axisLine: {
              show: true,
              lineStyle: {
                color: '#385982',
                width: 1,
                type: 'dashed',
              },
            },

            axisTick: {
              show: true,
            },
            axisLabel: {
              show: true,
              fontSize: 14,
              color: '#B6D0D8',
              textStyle: {
                color: '#a4a7aa',
              },
            },
          },
        ],
        yAxis: [
          {
            name: '',
            axisLine: {
              show: false,
              lineStyle: {
                color: '#494e54',
              },
            },
            axisTick: {
              //y轴刻度线
              show: false,
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: '#385982',
                type: 'dashed',
              },
            },
            axisLabel: {
              color: '#B6D0D8',
              formatter: '{value}%',
            },
          },
          {
            name: '',
            show: false,
            splitLine: { show: false },
            axisTick: {
              //y轴刻度线
              show: false,
            },
            axisLine: {
              lineStyle: {
                color: '#494e54',
              },
            },
            axisLabel: {
              formatter: '{value} ',
            },
          },
        ],
        series: [
          {
            name: this.lastYear,
            type: 'bar',
            yAxisIndex: 1, // 对应y轴第二个维度
            data: lastY || [0, 0, 0, 0, 0],
            barWidth: 11,
            barGap: 1,
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: '#41A7FD',
                  },
                  // {
                  //   offset: 0.8,
                  //   color: 'rgba(226,184,71,.8)',
                  // },
                  {
                    offset: 1,
                    color: '#0F2140',
                  },
                ]),
                opacity: 1,
              },
            },
          },
          {
            name: this.thisYear,
            type: 'bar',
            data: thisY || [0, 0, 0, 0, 0],
            barWidth: 11, //柱子宽度
            barGap: 1, //柱子之间间距
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: '#94E7D3',
                  },
                  {
                    offset: 1,
                    color: '#082641 ',
                  },
                ]),
                opacity: 1,
              },
            },
          },
        ],
      }

      if (option) {
        myChart.setOption(option)
        this.lunboEcharts(myChart, lastY.length)
      }
    },
     lunboEcharts(echartsId, dataLen,currentIndex=-1) {
      console.log('echartsId, dataLen', echartsId, dataLen)
      this.timer = setInterval(() => {
          echartsId.dispatchAction({
              type: 'downplay',
              seriesIndex: 0,
              dataIndex: currentIndex
          });
          currentIndex = (currentIndex + 1) % dataLen;
          echartsId.dispatchAction({
              type: 'highlight',
              seriesIndex: 0,
              dataIndex: currentIndex,
          });
          echartsId.dispatchAction({
              type: 'showTip',
              seriesIndex: 0,
              dataIndex: currentIndex
          });
      }, 3000)
    }
  },
}
</script>

<style lang="less">
#statEachrts {
  height: 240px;
  width: 100%;
}
</style>
