<style lang="less" scoped>
.no-data {
  font-size: 0.2rem;
  position: relative;
  .no-datas {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}
</style>
<template>
  <div>
    <div v-if="
    (
      LineAndBarData.currentYearDataList ||
      LineAndBarData.beforeOneYearDataList
    ) &&
    (
      LineAndBarData.currentYearDataList.length ||
      LineAndBarData.beforeOneYearDataList.length
    )" :id="id" :style="{ height: height, width: width }" />
    <div v-else class="no-data" :style="{ height: height, width: width }">
      <div class="no-datas">暂无数据</div>
    </div>
  </div>
</template>

<script lang="ts">
import * as echarts from 'echarts'
import { Component, Prop, Watch } from "vue-property-decorator";
import { mixins } from "vue-class-component";
import ResizeMixin from "./mixins/resize";
interface WaterChageData {
  bottomList: string[];
  currentYearName: string;
  currentYearDataList: string[];
  beforeOneYearName: string;
  beforeOneYearDataList: string[];
  unit?: string;
}
@Component({
  name: "LineAndBarChart"
})
export default class extends mixins(ResizeMixin) {
  // @Prop({ default: "chart" }) private className!: string;
  @Prop({ default: "chart" }) private id!: string;
  @Prop({ default: "200px" }) private width!: string;
  @Prop({ default: "200px" }) private height!: string;
  @Prop({ required: true }) private LineAndBarData!: any;
  @Watch("LineAndBarData", { immediate: true, deep: true })
  private onChartDataChange(newValue: any, oldValue: any): void {
    if (this.LineAndBarData && newValue) {
      if (this.chart) {
        this.chart.clear();
      }
      this.$nextTick(() => {
        this.initChart();
      });
    }
  }
  mounted() {
    // this.$nextTick(() => {
    //   this.initChart();
    // });
  }

  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.clear();
    this.chart.dispose();
    this.chart = null;
  }

  private initChart() {

    if(this.chart === null || this.chart === undefined) {
      const chartDom = document.getElementById(this.id) as HTMLDivElement
      if(!chartDom) return;
      this.chart = echarts.init(chartDom);
    }
    this.chart.setOption({
      backgroundColor: "transparent",
      grid: {
        // height: 130,
        top: 60,
        bottom: 20,
        left: "left",
        right: 26,
        containLabel: true
      },
      legend: [
        {
          show: true,
          data: [
            this.LineAndBarData.beforeOneYearName,
            this.LineAndBarData.currentYearName ? this.LineAndBarData.currentYearName : '2021',
            this.LineAndBarData.sameRatioName
          ],
          right: 0,
          // top: -20,
          textStyle: {
            color: "#fff"
          }
        }
      ],
      xAxis: {
        name: "月",
        nameTextStyle: {
          color: "#fff",
          lineHeight: -30,
          align: "center",
          verticalAlign: "bottom"
        },
        type: "category",
        data: this.LineAndBarData.bottomList,
        axisLabel: {
          interval: 0,
          textStyle: {
            fontSize: 12,
            color: "white"
          }
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        }
      },
      yAxis: [
        {
          name: this.LineAndBarData.unit,
          nameTextStyle: {
            color: "white",
            align: "left"
          },
          type: "value",
          axisLabel: {
            // align: "left",
            textStyle: {
              fontSize: 12,
              color: "white"
            }
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          splitLine: {
            lineStyle: {
              type: "dashed"
            }
          }
        },
        {
          name: this.LineAndBarData.unit1,
          nameTextStyle: {
            color: "white"
          },
          type: "value",
          axisLabel: {
            // align: "left",
            textStyle: {
              fontSize: 12,
              color: "white"
            }
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          splitLine: {
            show: false,
            lineStyle: {
              type: "dashed"
            }
          }
        }
      ],
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "cross",
          label: {
            backgroundColor: "#6a7985"
          }
        },
        // formatter: `{b}月<br />{a0}：{c0}<br />{a1}：{c1}<br />{a2}：{c2}`
        formatter: (params: any) => {
          if (params.length == 3) {
            return `${params[0].name}月<br />${params[0].seriesName}：${params[0].value}<br />${params[1].seriesName}：${params[1].value}<br />${params[2].seriesName}：${params[2].value}`;
          } else if (params.length == 2) {
            return `${params[0].name}月<br />${params[0].seriesName}：${params[0].value}<br />${params[1].seriesName}：${params[1].value}`;
          } else if (params.length == 1) {
            return `${params[0].name}月<br />${params[0].seriesName}：${params[0].value}`;
          }
        }
      },
      color: [
        "#9FE6B8",
        "#428AFF",
        "#1D9DFF",
        "#9FE6B8",
        "#FB7293",
        "#8378EA",
        "#E7BCF3",
        "#FF9F7F",
        "#32C5E9",
        "#FFDB5C"
      ],
      series: [
        {
          name: this.LineAndBarData.beforeOneYearName,
          type: "bar",
          barWidth: "25%",
          data: this.LineAndBarData.beforeOneYearDataList
          // itemStyle: {
          //     color: "rgba(14,156,255,1)"
          // }
        },
        {
          name: this.LineAndBarData.currentYearName,
          type: "bar",
          // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
          // @ts-ignore
          barWidth: "25%",
          data: this.LineAndBarData.currentYearDataList
        },
        {
          name: this.LineAndBarData.sameRatioName,
          type: "line",
          yAxisIndex: 1,
          data: this.LineAndBarData.sameRatio,
          lineStyle: {
            color: "#FFBF35" //改变折线颜色
          },
          symbol: "circle",
          symbolSize: 6,
          itemStyle: {
            color: "#FFBF35", //改变折线点的颜色
            borderColor: "#FFBF35"
            // borderWidth: 2
          }
        }
      ]
    } as EChartOption<EChartOption.SeriesLine>);
  }
}
</script>
