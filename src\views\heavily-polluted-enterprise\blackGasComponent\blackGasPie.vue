<template>
  <div class="GT_pie2">
    <div id="GT_pie"></div>
    <div class="center">
      <div style="font-size: 12px;font-family: PingFang SC;font-weight: 400;color: #C5CECD;">总数(辆)</div>
      <div style="font-size: 23px;font-family: DIN;font-weight: 500;color: #E3F4F3;">{{total}}</div>
    </div>
  </div>
</template>

<script>
import * as echarts from "echarts";
export default {
  name: "carHCRank",
  props: {
    echartData:{
      type: Object|Array,
      default:()=>({})
    }
  },
  components: {},
  data() {
    return {
      colorArr: ["#59F7CA", "#53D0F0", "#F2B949"],
      myChart1: "",
      echartsData: [
        {name:'本市',value:15, color:'#59F7CA'},
        {name:'外省',value:15, color:'#53D0F0'},
        {name:'本省非本市',value:15, color:'#F2B949'},
      ],
      total:0,
    };
  },
  watch: {
    echartData: {
      handler(nval, oval) {
        if (nval) {
          if (this.myChart1) {
            this.myChart1.dispose();
          }
          this.echartsData.forEach((v,i)=>{
            if(i==0){
              v.value = nval.isLocalCount||0
              v.percent = nval.isLocalCountRatio||0
            }else if(i==1){
              v.value = nval.notProvinceCount||0
              v.percent = nval.notProvinceCountRatio||0
            }else if(i==2){
              v.value = nval.thisProvinceNotLocalCount||0
              v.percent = nval.thisProvinceNotLocalCountRatio||0
            }
          })
          this.$nextTick(()=>{
            this.initEchart();
          })
        }
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {
    
  },
  beforeUnmount() {
    
  },
  methods: {
    initEchart() {
      const chartMonth = echarts.init(document.getElementById("GT_pie"));
      this.myChart1 = chartMonth;
      chartMonth.clear();
      chartMonth.showLoading();
      this.total = this.echartData.total||0
      let option = {
        color: this.colorArr,
        tooltip: {
          show: true,
          trigger: "item",
          formatter:(params)=>{
            const str = `
              <div style="padding:0.02rem;">
                <div>${params.name}</div>
                <div>
                  ${params.marker} ${params.value}辆 <span style="margin-left:0.07rem;">${params.percent}%</span>
                </div>
              </div>
            `
            return str
          }
        },
        legend: {
          show: true,
          top: 49,
          right: 0,
          textStyle: {
            color: "#86BED1",
            width:'100%',
            height: '5',
            lineHeight: '5',
            rich:{
              name:{
                width: 75,
              },
              percent:{
                width: 50,
              },
              value:{
                width: 50,
              }
            }
          },
          icon: "circle",
          itemWidth: 7,
          itemHeight: 7,
          itemGap: 53,
          orient: 'vertical',
          formatter:(name)=>{
            const obj = this.echartsData.filter(o=>o.name===name)[0]
            return `{name|${name}}{percent|${obj.percent}%}{value|${obj.value}辆 }`
          }
        },
        grid: {
          containLabel: true,
          left: "8%",
          top: "5%",
          right: "0%",
          bottom: "0%",
        },
        xAxis: {
          axisTick: {
            show: false,
          },
          axisLabel: {
            color: "#80B6C8",
            interval: "auto",
            // rotate: -35,
            fontSize: 12,
          },
          axisLine: {
            show: false,
            lineStyle: {
              color: "rgba(56,89,130,1)",
              type: [5, 5],
            },
          },
        },
        yAxis: {
          // name:'单位:'+'',
          nameTextStyle: {
            color: "#80B6C8",
            padding: [0, 40, 0, 0],
          },
          splitLine: {
            lineStyle: {
              color: "rgba(56,89,130,0.7)",
              type: [5, 5],
            },
          },
          axisLine: {
            show: false,
            lineStyle: {
              color: "#1B5BBA",
            },
          },
          axisLabel: {
            color: "#80B6C8",
            interval: 0,
          },
        },
        series: [
          {
            name: "固废13",
            type: "pie",
            // radius: ["55%", "65%"],
            radius: ["58%", "70%"],
            center: ['25.5%', '47%'],
            avoidLabelOverlap: false,
            selectedOffset: 0,
            itemStyle: {
              // borderRadius: 10,
              borderColor: "#010614",
              borderWidth: 3,
            },
            label: {
              show: false,
              position: "center",
            },
            emphasis: {
              scale: false,
              scaleSize: 2,
              label: {
                show: false,
                fontSize: "30",
                fontWeight: "bold",
                formatter: ["{a1|{b}}", "{b1|{c}}"].join("\n"),
                rich: {
                  a1: {
                    color: "#C5CECD",
                    fontSize: 14,
                    lineHeight: 25,
                  },
                  b1: {
                    color: "#E3F4F3",
                    fontSize: 22,
                  },
                },
              },
            },
            labelLine: {
              show: false,
            },
            data: this.echartsData,
          },
          {
            name: "固废2",
            type: "pie",
            radius: ["44%", "60%"],
            center: ['25.5%', '47%'],
            avoidLabelOverlap: false,
            selectedOffset: 7,
            silent: true,
            itemStyle: {
              color: (val) => {
                // const rgba = this.toRgba(this.colorArr[val.dataIndex])
                const rgba = this.toRgba(val.data.color);
                // console.log(rgba,val,'颜色');
                return rgba + ",0.3)";
              },
              // borderRadius: 10,
              borderColor: "#010614",
              borderWidth: 3,
            },
            label: {
              show: false,
              position: "center",
            },
            emphasis: {
              scale: false,
              scaleSize: 3,
              label: {
                show: false,
                fontSize: "30",
                fontWeight: "bold",
                formatter: ["{a1|{b}}", "{b1|{c}}"].join("\n"),
                rich: {
                  a1: {
                    color: "#C5CECD",
                    fontSize: 14,
                    lineHeight: 25,
                  },
                  b1: {
                    color: "#E3F4F3",
                    fontSize: 22,
                  },
                },
              },
            },
            labelLine: {
              show: false,
            },
            data: this.echartsData,
          },
        ],
      };
      chartMonth.setOption(option);
      chartMonth.hideLoading();
      const _this=this
      chartMonth.on('legendselectchanged', function(obj) {
          const {selected,name} = obj
          const arr  = _this.echartsData.filter(v=>(selected[v.name])).map(o=>o.value)
          _this.total = arr.reduce( (prev,cur) => prev+cur )
      });
      this.myChart1.on("highlight", { seriesIndex: 1 }, function(params) {
        // console.log('触发高亮');
      });
      if (!this.myChart1) {
        return;
      }
    },
    toRgba(colors) {
      // 16进制颜色值的正则
      let reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/;
      // 把颜色值变成小写
      let color = colors.toLowerCase();
      if (reg.test(color)) {
        // 如果只有三位的值，需变成六位，如：#fff => #ffffff
        if (color.length === 4) {
          let colorNew = "#";
          for (let i = 1; i < 4; i += 1) {
            colorNew += color.slice(i, i + 1).concat(color.slice(i, i + 1));
          }
          color = colorNew;
        }
        // 处理六位的颜色值，转为RGB
        let colorChange = [];
        for (let i = 1; i < 7; i += 2) {
          colorChange.push(parseInt("0x" + color.slice(i, i + 2)));
        }
        return "rgba(" + colorChange.join(",");
      } else {
        return color;
      }
    },
  },
};
</script>
<style scoped>
#GT_pie {
  width: 100%;
  height: 2.5rem;
  background-image: url('../../../assets/exhaustGasTruck/<EMAIL>');
  background-position: 0% 32.5%;
  background-size: 3.97rem 2.1rem;
  background-repeat: no-repeat;
}
.GT_pie2{
  position: relative;
}
.center{
  position: absolute;
  text-align: center;
  pointer-events: none;
  left: 3px;
  width: 2.05rem;
  top: 1rem;
}
</style>
