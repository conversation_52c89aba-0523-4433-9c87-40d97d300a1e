<style lang="less" scoped>
.water-video {
  width: 100%;
  height: 100%;
  position: relative;
  .content-bg {
    /*overflow: hidden;*/
    /*position: absolute;*/
    /*pointer-events: none;*/
    width: 100%;
    height: 100%;
    /*width: 100vw;*/
    background-image: url('../../assets/<EMAIL>');
    background-size: 100% 100%;
    overflow: hidden;
    padding: 0.18rem 0.33rem 0.43rem 0.38rem;
    .top {
      display: flex;
      justify-content: space-between;
      .left-bg {
        width: 68.25%;
        height: 6.6rem;
        background-image: url('../../assets/<EMAIL>');
        background-size: 100% 100%;
        position: relative;
        .title {
          font-size: 30px;
          font-weight: 400;
          color: #ffffff;
          text-align: center;
          text-shadow: 0px 1px 3px #0c1f39;
        }
        .video-area {
          width: 10rem;
          height: 4.9rem;
          position: absolute;
          top: 0.96rem;
          left: 1.2rem;
        }
      }
      .right-bg {
        width: 28.66%;
        height: 6.6rem;
        background-image: url('../../assets/<EMAIL>');
        background-size: 100% 100%;
        padding: 0.59rem 0.7rem 0.66rem;
        .title {
          width: 3.28rem;
          height: 0.45rem;
          background-image: url('../../assets/<EMAIL>');
          background-size: 100% 100%;
        }
        .station,
        .address {
          margin-top: 0.3rem;
          display: flex;
          align-content: center;
          span {
            line-height: 0.17rem;
            &:nth-child(1) {
              font-size: 18px;
              font-weight: 500;
              color: #5abefe;
              display: inline-block;
              width: 1rem;
            }
            &:nth-child(2) {
              font-size: 18px;
              font-weight: 400;
              color: #dcf0ff;
              display: inline-block;
              width: calc(100% - 1rem);
              overflow: hidden; /*超出部分隐藏*/
              white-space: nowrap; /*不换行*/
              text-overflow: ellipsis; /*超出部分文字以...显示*/
            }
          }
        }
        .address {
          margin-top: 0.2rem;
        }
        .weather {
          margin-top: 0.25rem;
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 0.14rem 0;
          height: 1.13rem;
          background-image: url('../../assets/<EMAIL>');
          background-size: 100% 100%;
          .lists {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            align-items: center;
            position: relative;
            width: 33.3%;
            height: 100%;
            img {
              width: 0.3rem;
              height: 0.3rem;
            }
            .line {
              width: 1px;
              height: 0.81rem;
              position: absolute;
              right: 0;
            }
            span {
              &:nth-child(1) {
                font-size: 18px;
                /*font-weight: bold;*/
                color: #ffffff;
              }
              &:nth-child(2) {
                font-size: 15px;
                font-weight: 500;
                color: #b8d3f1;
              }
            }
          }
        }
        .water-catch {
          margin-top: 0.1rem;
          display: flex;
          background-image: url('../../assets/<EMAIL>');
          background-size: 100% 100%;
          height: 0.3rem;
          padding-left: 0.2rem;
          justify-content: space-between;
          span {
            &:nth-child(1) {
              font-size: 16px;
              font-weight: 500;
              font-style: italic;
              color: #60e4ff;
            }
            &:nth-child(2) {
              font-size: 14px;
              font-weight: 400;
              color: #b8d3f1;
              opacity: 0.8;
            }
          }
        }
        .water-list {
          height: 0.87rem;
          background-image: url('../../assets/<EMAIL>');
          background-size: 100% 100%;
          margin-top: 0.1rem;
          display: flex;
          align-items: center;
          .pai-list {
            width: 33.3%;
            height: 100%;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            span {
              &:nth-child(1) {
                font-size: 15px;
                font-weight: 500;
                color: #00c7ff;
              }
              &:nth-child(2) {
                margin-top: 0.1rem;
                font-size: 17px;
                font-weight: 500;
                color: #f9fcfd;
              }
            }
            .water-err {
              color: #fc4b4b !important;
            }
          }
        }
      }
    }
    .bottom {
      margin-top: 0.3rem;
      height: 2.2rem;
      .list {
        height: 2.1rem;
        margin-bottom: 0.63rem;
        padding: 0;
        box-sizing: border-box;
      }
      .thead {
        display: flex;
        align-items: center;
        background-color: #0f245e;
        opacity: 0.8;
        div {
          display: inline-block;
          width: 14.28%;
          text-align: center;
          line-height: 0.4rem;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
      .t-body {
        height: 1.6rem;
      }
      .task-list {
        display: flex;
        align-items: center;
        cursor: pointer;
        color: #97b6e4;
        font-size: 0.14rem;
        &:nth-child(2n) {
          background-color: #0f245e;
          opacity: 0.8;
        }
        &:nth-child(2n + 1) {
          background-color: #04173d;
          opacity: 0.8;
        }
        div {
          width: 14.28%;
          text-align: center;
          line-height: 0.4rem;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          padding: 0 0.1rem;
          box-sizing: border-box;
        }
        .state {
          display: flex;
          justify-content: center;
          .image {
            width: 0.7rem;
            height: 0.3rem;
            background-size: 100% 100% !important;
            line-height: 0.3rem !important;
            color: #ffe27f;
          }
          .active {
            color: #4bfbff;
          }
        }
      }
      .thead {
        color: #40deff;
        font-size: 0.14rem;
      }
    }
  }
}
</style>

<style lang="less">
.ant-modal-waters {
    top: 2.05rem !important;
    right: calc(50% - 600px) !important;
    .ant-modal-content {
      width: 12rem !important;
      height: 6.7rem !important;
      padding: 0.7rem 0.95rem 0.84rem;
      box-sizing: border-box;
      background-color: transparent !important;
      background-image: url('../../assets/<EMAIL>') !important;
      background-size: 100% 100% !important;
    }
    .ant-modal-footer {
      border: none !important;
    }
    .ant-modal-close {
      top: 0.4rem !important;
      right: 0.8rem !important;
      svg {
        font-size: 0.3rem !important;
        color: #fff;
      }
    }
    .ant-modal-close-x {
      background: RGBA(12, 39, 94, 1);
      border-radius: 50%;
      display: flex;
      align-items: center;
      width: 0.42rem;
      height: 0.42rem;
      justify-content: center;
      border: 0.015rem solid #01dbef;
    }
    .ant-modal-body {
      padding: 0;
      .title {
        display: flex;
        justify-content: center;
        .bg {
          width: 5.91rem;
          height: 0.39rem;
          background-image: url('../../assets/<EMAIL>');
          background-size: 100% 100%;
        }
      }
      .table-container {
        margin-top: 0.2rem;
        &:last-child {
          margin-top: 0.3rem;
        }
        color: #c4dbfb;
        font-size: 0.16rem;
        width: 100%;
        text-align: center;
        border-color: rgba(11, 156, 229, 0.2);
        tr {
          th {
            background: rgba(14, 45, 126, 0.7);
          }
        }
        tr:nth-child(2n) {
          td {
            background-color: rgba(14, 45, 126, 0.2);
          }
        }
      }
    }
    .content {
      height: 5rem;
      margin-top: 0.3rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .left-arrow {
        cursor: pointer;
        width: 0.4rem;
        height: 0.4rem;
        background: rgba(7, 56, 141, 0.6);
        border: 1px solid rgba(54, 218, 234, 0.42);
      }
      .middle-wrap {
        width: 9rem;
        height: 5rem;
        .middle-carousels {
          height: 100%;
          background-size: 100% 100%;
          background-image: url('../../assets/heavily/<EMAIL>');
          .swiper-img {
            display: flex;
            align-items: center;
            justify-content: center;
          }
          .item {
            width: 8.4rem;
            height: 4.4rem;
          }
        }
      }
      .right-arrow {
        cursor: pointer;
        width: 0.4rem;
        height: 0.4rem;
        background: rgba(7, 56, 141, 0.6);
        border: 1px solid rgba(54, 218, 234, 0.42);
      }
      .svg-arrow {
        width: 0.35rem;
        height: 0.35rem;
      }
    }
  }
.video-areas {
  .video-wrapper {
    height: 4.9rem !important;
    padding-bottom: 49% !important;
  }
}
.check-images {
  width: 0.82rem;
  display: inline-block;
  height: 0.31rem;
  background-image: url('../../assets/<EMAIL>');
  background-size: 100% 100%;
  font-size: 13px;
  font-weight: 500;
  line-height: 0.31rem;
  color: #4bfbff;
}
</style>

<template>
  <section class="water-video">
    <section class="content-bg">
      <div class="top">
        <div class="left-bg">
          <div class="title">实时监控</div>
          <div class="video-area video-areas">
            <LivePlayer
              :video-url="videoUrl"
              fluent
              autoplay
              live
              :stretch="true"
              style="width: 10rem; height: 4.9rem"
            />
          </div>
        </div>
        <div class="right-bg">
          <div class="title"></div>
          <div class="station">
            <span>站点名称：</span>
            <span :title="drainCamera.stationName">{{
              drainCamera.stationName
            }}</span>
          </div>
          <div class="address">
            <span>站点地址：</span>
            <span :title="drainCamera.stationAddress">{{
              drainCamera.stationAddress
            }}</span>
          </div>
          <div class="weather">
            <div class="lists">
              <img src="../../assets/<EMAIL>" alt="" />
              <span style="color: white">{{ meteorological.weather }}</span>
              <span style="color: #b8d3f1">天气</span>
              <img src="../../assets/<EMAIL>" alt="" class="line" />
            </div>
            <div class="lists">
              <img src="../../assets/<EMAIL>" alt="" />
              <span
                ><span style="font-size: 0.24rem">{{
                  meteorological.windpower
                }}</span
                >级</span
              >
              <span style="color: #b8d3f1"
                >{{ meteorological.winddirection }}风</span
              >
              <img src="../../assets/<EMAIL>" alt="" class="line" />
            </div>
            <div class="lists">
              <img src="../../assets/<EMAIL>" alt="" />
              <span
                ><span style="font-size: 0.24rem">{{
                  meteorological.humidity
                }}</span
                >%</span
              >
              <span style="color: #b8d3f1">相对湿度</span>
            </div>
          </div>
          <div class="water-catch">
            <span>排水口1</span>
            <span>禁止时间段：{{ drainCamera.drainOneProhibitTime }}</span>
          </div>
          <div class="water-list">
            <div class="pai-list">
              <span>排口类型</span>
              <span>{{ drainCamera.drainOneTypeStr }}</span>
            </div>
            <div class="pai-list">
              <span>是否排水</span>
              <span
                :class="{
                  'water-err': drainCamera.drainOneIsDrain ? 'water-err' : ''
                }"
                >{{ drainCamera.drainOneIsDrain ? '是' : '否' }}</span
              >
            </div>
            <div class="pai-list">
              <span>是否排污</span>
              <span
                :class="{
                  'water-err': drainCamera.drainOneIsSewage ? 'water-err' : ''
                }"
                >{{ drainCamera.drainOneIsSewage ? '是' : '否' }}</span
              >
            </div>
          </div>
          <div class="water-catch">
            <span>排水口2</span>
            <span>禁止时间段：{{ drainCamera.drainTwoProhibitTime }}</span>
          </div>
          <div class="water-list">
            <div class="pai-list">
              <span>排口类型</span>
              <span>{{ drainCamera.drainTwoTypeStr }}</span>
            </div>
            <div class="pai-list">
              <span>是否排水</span>
              <span
                :class="{
                  'water-err': drainCamera.drainTwoIsDrain ? 'water-err' : ''
                }"
                >{{ drainCamera.drainTwoIsDrain ? '是' : '否' }}</span
              >
            </div>
            <div class="pai-list">
              <span>是否排污</span>
              <span
                :class="{
                  'water-err': drainCamera.drainTwoIsSewage ? 'water-err' : ''
                }"
                >{{ drainCamera.drainTwoIsSewage ? '是' : '否' }}</span
              >
            </div>
          </div>
          <!--                <div class="water-pai">-->
          <!--                    <div class="left">-->
          <!--                        <span>是否排水</span>-->
          <!--                        <span>{{( activeIndex === 1 ? drainCamera.drainOneIsDrain: drainCamera.drainTwoIsDrain) ? '是' : '否' }}</span>-->
          <!--                    </div>-->
          <!--                    <div class="right">-->
          <!--                        <span>是否排污</span>-->
          <!--                        <span>{{( activeIndex === 1 ? drainCamera.drainOneIsSewage: drainCamera.drainTwoIsSewage) ? '是' : '否' }}</span>-->
          <!--                    </div>-->
          <!--                </div>-->
          <!--                <div class="water-kou">-->
          <!--                    <div class="pai" @click="activeIndex=1">-->
          <!--                        <img :src="activeIndex === 1 ? image1 : image2" alt="">-->
          <!--                        <span :class="{'active-sapn': activeIndex === 1 ? 'active-sapn' : '' }">排水口1</span>-->
          <!--                    </div>-->
          <!--                   <div class="pai" @click="activeIndex=2">-->
          <!--                       <img :src="activeIndex === 1 ? image2 : image1" alt="">-->
          <!--                       <span :class="{'active-sapn': activeIndex === 2 ? 'active-sapn' : '' }">排水口2</span>-->
          <!--                   </div>-->
          <!--                    <div class="bottom"></div>-->
          <!--                </div>-->
        </div>
      </div>
      <div class="bottom">
        <div v-if="list.length" class="list">
          <div class="thead">
            <div>告警类型</div>
            <div>排口类型</div>
            <div>排口号</div>
            <div>天气类型</div>
            <div>告警时间</div>
            <div>智能分析</div>
            <div>告警截图</div>
          </div>
          <swiper
            class="t-body"
            :options="list.length > 4 ? swiperOption : swiperOption1"
            ref="mySwiper"
          >
            <swiper-slide
              class="task-list"
              v-for="(item, index) in list"
              :key="index"
            >
              <div>{{ item.alarmTypeStr }}</div>
              <div>{{ item.drainTypeStr }}</div>
              <div>{{ item.drainNumber }}</div>
              <div>{{ item.weatherTypeStr }}</div>
              <div>{{ item.alarmTime }}</div>
              <div>{{ item.alarmRemark }}</div>
              <div :data-href="JSON.stringify(item)" :data-index="index">
                <span
                  :data-href="JSON.stringify(item)"
                  :data-index="index"
                  class="check-images"
                  >查看图片</span
                >
              </div>
            </swiper-slide>
          </swiper>
        </div>
      </div>
    </section>
    <a-modal
      :visible="visible"
      class="ant-modal-waters"
      :footer="null"
      :destroyOnClose="true"
      @cancel="handleCancel"
    >
      <div class="content">
        <!-- 左侧箭头 -->
        <div class="left-arrow">
          <svgicon name="left" color="#00D9D5" class="svg-arrow"></svgicon>
        </div>
        <!-- 中间部分 -->
        <div class="middle-wrap">
          <!-- 内容滚动部分 -->
          <swiper class="middle-carousels" :options="swiperOptions">
            <swiper-slide
              v-for="(item, i) in imageList"
              :key="i"
              class="swiper-img"
            >
              <img :src="item" alt="" class="item" />
            </swiper-slide>
            <!-- <div class="swiper-pagination" slot="pagination"></div> -->
          </swiper>
        </div>
        <!-- 右侧箭头 -->
        <div class="right-arrow">
          <svgicon name="right" color="#00D9D5" class="svg-arrow"></svgicon>
        </div>
      </div>
    </a-modal>
  </section>
</template>

<script>
import LivePlayer from '@liveqing/liveplayer'
import { Swiper, SwiperSlide } from 'vue-awesome-swiper'
import { socketUrl3 } from '../../utils'
import { Modal } from 'ant-design-vue'
import { fetchCameraUrl } from '@/api/water'
export default {
  name: 'WaterRealTime',
  components: {
    Swiper,
    SwiperSlide,
    LivePlayer,
    AModal: Modal
  },
  data() {
    const _this = this
    return {
      list: [],
      visible: false,
      videoUrl: '',
      image1: require('../../assets/<EMAIL>'),
      image2: require('../../assets/<EMAIL>'),
      swiperOption: {
        direction: 'vertical',
        slidesPerView: 4,
        slidesPerGroup: 1,
        // loop: true,
        // autoplay: {
        //     delay: 2800,
        //     disableOnInteraction: false
        // },
        on: {
          click: function (e) {
            _this.currObj = JSON.parse(e.target.getAttribute('data-href'))
          }
        }
      },
      currObj: {},
      swiperOption1: {
        direction: 'vertical',
        slidesPerView: 4,
        slidesPerGroup: 1,
        // loop: false,
        // autoplay: {
        //     delay: 2800,
        //     disableOnInteraction: false
        // },
        on: {
          click: function (e) {
            _this.currObj = JSON.parse(e.target.getAttribute('data-href'))
          }
        }
      },
      drainCamera: {},
      meteorological: {},
      swiperOptions: {
        slidesPerView: 1,
        slidesPerGroup: 1,
        loop: false,
        loopFillGroupWithBlank: true,
        pagination: {
          el: '.swiper-pagination',
          clickable: true
        },
        autoplay: {
          delay: 30000,
          disableOnInteraction: false
        },
        navigation: {
          nextEl: '.right-arrow',
          prevEl: '.left-arrow'
        }
      },
      imageList: [
        // 'https://oss-chengdu.vankeytech.com:9000/vankeytech-ep//task/annex/2f50da5344244aceb86f342461b71aa1.jpg',
        // 'https://oss-chengdu.vankeytech.com:9000/vankeytech-ep//task/annex/ec30c6f93f5043eb9e0a0d5be9a8f6a8.jpg',
        // 'https://oss-chengdu.vankeytech.com:9000/vankeytech-ep//task/annex/f55eaeaaeaaf4e6b886437bc113a5da6.jpg'
      ],
      socketTimer: null
    }
  },
  watch: {
    currObj(val) {
      if (val) {
        console.log(val, 'val')
        if (val.alarmImage) {
          this.visible = true
          this.$nextTick(() => {
            this.imageList = val.alarmImage.split(',')
          })
        }
      }
    }
  },
  mounted() {
    this.fetchCameraUrl()
    this.connect()
    this.initSocketTime()
  },
  beforeDestroy() {
    clearInterval(this.socketTimer)
  },
  methods: {
    initSocketTime() {
      this.socketTimer = setInterval(() => {
        if (this.socket) {
          this.socket.send(JSON.stringify({ code: 0 }))
        }
      }, 20000)
    },
    handleCancel() {
      this.visible = false
    },
    fetchCameraUrl() {
      fetchCameraUrl('ae624d40adc64936949f9713a61acc10').then((res) => {
        this.videoUrl = res.data.data.flvHttps
      })
    },
    connect() {
      this.socket = new WebSocket(socketUrl3())
      // 监听socket连接
      this.socket.onopen = this.open
      // 监听socket错误信息
      this.socket.onerror = this.error
      // 监听socket消息
      this.socket.onmessage = this.getMessage
      this.$once('hook:beforeDestroy', () => {
        this.socket.onopen = () => {}
        this.socket.onerror = () => {}
        this.socket.onmessage = () => {}
      })
    },
    open() {
      this.send()
    },
    send() {
      this.socket.send(JSON.stringify({ code: 1, token: 'xxxxx' }))
    },
    error() {
      console.error('系统连接错误')
    },
    getMessage(msg) {
      const data = JSON.parse(msg.data)
      if (data.code === -1 && data.success) {
        this.socket.send(JSON.stringify({ code: 50 }))
      }
      if (data.code == -50) {
        this.list = data.drainAlarmList
        this.drainCamera = data.drainCamera
        this.meteorological = data.meteorological
      }
    }
  }
}
</script>
