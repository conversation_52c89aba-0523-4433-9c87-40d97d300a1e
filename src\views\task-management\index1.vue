<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  padding: 0;
  box-sizing: border-box;
  .center-map {
    position: absolute;
    height: calc(1080px - 0.94rem);
    width: 100%;
    > div {
      width: 100%;
      height: 100%;
    }
  }
  .container-bg {
    overflow: hidden;
    position: absolute;
    pointer-events: none;
    z-index: 2;
    width: 100%;
    height: 100%;
    width: 1920px;
    background-size: 100% 100%;
    /*background-image: url("../../assets/department/marker.png");*/
    .left-part {
      position: absolute;
      pointer-events: auto;
      z-index: 2;
      top: 0;
      left: 0;
      height: calc(1080px - 0.94rem);
      width: 3rem;
      padding: 0.3rem 0.3rem 0;
      .list {
        cursor: pointer;
        margin-top: 0.7rem;
        padding-left: 0.52rem;
        .left-list {
          width: 1.81rem;
          height: 0.66rem;
          padding-left: 0.65rem;
          box-sizing: border-box;
          background-size: 100% 100% !important;
          line-height: 0.66rem;
          margin-bottom: 0.36rem;
          span {
            font-size: 12px;
            color: white;
            &:nth-child(2) {
              margin-left: 0.05rem;
              font-size: 15px;
            }
          }
        }
      }
    }
    .list-remark {
      padding-left: 0.52rem;
      font-size: 0.14rem;
      font-weight: 400;
      color: #86c6ff;
    }
    .right-part-copy {
      position: absolute;
      pointer-events: auto;
      z-index: 2;
      top: 0;
      right: -5rem;
      opacity: 0.4;
      height: calc(1080px - 0.94rem);
      width: 4.6rem;
      padding: 0.3rem 0.6rem 0.43rem 0.3rem;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      transition: all 1.5s ease-out;
      .depart-select {
        display: flex;
        justify-content: flex-end;
      }
      .right-part-one {
        margin-top: 0.2rem;
        height: calc(2.5rem - (1080px - 1080px) / 3);
        .title {
          font-size: 0.2rem;
          font-family: Source Han Sans CN;
          font-weight: 500;
          text-shadow: 0 0 5px blue, 0 0 5px blue;
        }
        .real-time {
          width: 4rem;
          height: 1.75rem;
        }
        .button-list {
          height: 0.24rem;
          background-color: #0f245e;
          margin: 0.1rem 0;
          /*padding: 0 0.1rem;*/
          display: flex;
          align-items: center;
          box-sizing: border-box;
          color: white;
          line-height: 0.24rem;
          font-size: 0.12rem;
          span {
            flex: 1;
            padding: 0 0.05rem;
            cursor: pointer;
            display: inline-block;
            height: 100%;
            text-align: center;
          }
          .active {
            background-color: #0084ff;
          }
        }
      }
      .right-part-two {
        height: calc(2.5rem - (1080px - 1080px) / 3);
        .title {
          font-size: 0.2rem;
          font-family: Source Han Sans CN;
          font-weight: 500;
          text-shadow: 0 0 5px blue, 0 0 5px blue;
        }
        .executive-department {
          width: 100%;
          height: 2.1rem;
          position: relative;
          .major-ndoe {
            position: absolute;
            text-align: center;
            font-size: 0.14rem;
            font-weight: 500;
            color: #f9f9f9;
            line-height: 0.5rem;
            top: 1.2rem;
            left: 1.56rem;
          }
          .node {
            position: absolute;
            height: 0.76rem;
            width: 1.46rem;
            text-align: center;
            font-size: 0.14rem;
            font-weight: 500;
            color: #f9f9f9;
            line-height: 0.5rem;
            background-size: 100% 100%;
            &:nth-child(1) {
              /*background-image: url('../../assets/department/<EMAIL>');*/
              top: 30px;
              left: -10px;
            }
            &:nth-child(2) {
              /*background-image: url('../../assets/department/<EMAIL>');*/
              top: 30px;
              left: 110px;
            }
            &:nth-child(3) {
              /*background-image: url('../../assets/department/<EMAIL>');*/
              top: 30px;
              left: 240px;
            }
          }
          .node2 {
            position: absolute;
            height: 0.76rem;
            width: 1.46rem;
            text-align: center;
            font-size: 0.14rem;
            font-weight: 500;
            color: #f9f9f9;
            line-height: 0.5rem;
            background-size: 100% 100%;
            &:nth-child(1) {
              /*background-image: url('../../assets/department/<EMAIL>');*/
              top: 30px;
              left: -10px;
            }
            &:nth-child(2) {
              /*background-image: url('../../assets/department/<EMAIL>');*/
              top: 30px;
              left: 230px;
            }
          }
        }
      }
      .right-part-three {
        height: calc(2.8rem - (1080px - 1080px) / 3);
        /*overflow: hidden;*/
        .title {
          font-size: 0.2rem;
          font-family: Source Han Sans CN;
          font-weight: 500;
          text-shadow: 0 0 5px blue, 0 0 5px blue;
        }
        .reback {
          width: 100%;
          height: 2.82rem;
          margin-top: 0.35rem;
          box-sizing: border-box;
          .reback-list {
            display: flex;
            .left {
              padding-top: 0.05rem;
              box-sizing: border-box;
              display: flex;
              flex-direction: column;
              align-items: flex-end;
              span {
                color: #c9dbeb;
                font-size: 0.13rem;
              }
            }
            .center {
              width: 0.16rem;
              height: 0.14rem;
              margin: 0.05rem;
              background-size: 100% 100%;
              background-image: url('../../assets/department/<EMAIL>');
              position: relative;
            }
            .after {
              &::after {
                width: 0.01rem;
                height: 1.29rem;
                position: absolute;
                content: '';
                top: 0.12rem;
                left: 0.07rem;
                background-color: #1293f2;
              }
            }
            .right {
              width: 2.8rem;
              height: 1.12rem;
              padding: 0.28rem;
              box-sizing: border-box;
              background-size: 100% 100%;
              background-image: url('../../assets/department/<EMAIL>');
              .title {
                color: white;
                font-size: 0.15rem;
              }
              .content {
                color: #9bc8f1;
                font-size: 0.14rem;
                width: 100%;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: normal;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 2; /*规定最多显示两行*/
              }
              .check {
                width: 0.72rem;
                height: 0.2rem;
                text-align: center;
                line-height: 0.2rem;
                background: rgba(1, 55, 166, 0.41);
                border-radius: 2px;
                color: #00bde5;
                font-size: 0.11rem;
                cursor: pointer;
              }
            }
          }
        }
      }
    }
    .right-part {
      position: absolute;
      pointer-events: auto;
      z-index: 2;
      top: 0;
      right: 0;
      opacity: 1;
      height: calc(1080px - 0.94rem);
      width: 4.6rem;
      padding: 0.3rem 0.6rem 0.43rem 0.3rem;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      transition: all 1.5s ease-out;
      .depart-select {
        display: flex;
        justify-content: flex-end;
      }
      .right-part-one {
        margin-top: 0.2rem;
        height: calc(2.5rem - (1080px - 1080px) / 3);
        .title {
          font-size: 0.2rem;
          font-family: Source Han Sans CN;
          font-weight: 500;
          text-shadow: 0 0 5px blue, 0 0 5px blue;
        }
        .real-time {
          width: 4rem;
          height: 1.75rem;
        }
        .button-list {
          height: 0.24rem;
          background-color: #0f245e;
          margin: 0.1rem 0 0.05rem;
          padding: 0 0.1rem;
          /*display: flex;*/
          box-sizing: border-box;
          color: white;
          line-height: 0.24rem;
          font-size: 0.12rem;
          span {
            padding: 0 0.1rem;
            cursor: pointer;
            display: inline-block;
            height: 100%;
          }
          .active {
            background-color: #0084ff;
          }
        }
      }
      .right-part-two {
        height: calc(2.5rem - (1080px - 1080px) / 3);
        .title {
          font-size: 0.2rem;
          font-family: Source Han Sans CN;
          font-weight: 500;
          text-shadow: 0 0 5px blue, 0 0 5px blue;
        }
        .executive-department {
          width: 100%;
          height: 2.1rem;
        }
      }
      .right-part-three {
        height: calc(2.8rem - (1080px - 1080px) / 3);
        /*overflow: hidden;*/
        .title {
          font-size: 0.2rem;
          font-family: Source Han Sans CN;
          font-weight: 500;
          text-shadow: 0 0 5px blue, 0 0 5px blue;
        }
        .reback {
          width: 100%;
          height: 2.82rem;
          margin-top: 0.35rem;
          box-sizing: border-box;
          .reback-list {
            display: flex;
            .left {
              padding-top: 0.05rem;
              box-sizing: border-box;
              display: flex;
              flex-direction: column;
              align-items: flex-end;
              span {
                color: #c9dbeb;
                font-size: 0.13rem;
              }
            }
            .center {
              width: 0.16rem;
              height: 0.14rem;
              margin: 0.05rem;
              background-size: 100% 100%;
              background-image: url('../../assets/department/<EMAIL>');
              position: relative;
            }
            .after {
              &::after {
                width: 0.01rem;
                height: 1.29rem;
                position: absolute;
                content: '';
                top: 0.12rem;
                left: 0.07rem;
                background-color: #1293f2;
              }
            }
            .right {
              width: 2.99rem;
              height: 1.12rem;
              padding: 0.28rem;
              box-sizing: border-box;
              background-size: 100% 100%;
              background-image: url('../../assets/department/<EMAIL>');
              .title {
                color: white;
                font-size: 0.15rem;
              }
              .content {
                color: #9bc8f1;
                font-size: 0.14rem;
                width: 100%;
                overflow: hidden; /*超出部分隐藏*/
                white-space: nowrap; /*不换行*/
                text-overflow: ellipsis; /*超出部分文字以...显示*/
              }
              .check {
                width: 0.72rem;
                height: 0.2rem;
                text-align: center;
                line-height: 0.2rem;
                background: rgba(1, 55, 166, 0.41);
                border-radius: 2px;
                color: #00bde5;
                font-size: 0.11rem;
                cursor: pointer;
              }
            }
          }
        }
      }
    }
    .no-feed {
      width: 100%;
      height: 2rem;
      margin-top: 0.35rem;
      box-sizing: border-box;
      color: #ffffff;
      font-size: 0.18rem;
      text-align: center;
      line-height: 2rem;
    }
    .right-part-to-right {
      right: -5rem;
      opacity: 0.4;
    }
    .right-part-to-left {
      right: 0;
      opacity: 1;
    }
    .bottom-part {
      position: absolute;
      pointer-events: auto;
      z-index: 2;
      bottom: 0;
      height: 2.48rem;
      width: calc(100% - 4.5rem);
      .no-text {
        height: 2.1rem;
        line-height: 2.1rem;
        text-align: center;
        font-size: 0.18rem;
        color: #ffffff;
        margin-bottom: 0.63rem;
        padding: 0 0.4rem 0 0.82rem;
        box-sizing: border-box;
      }
      .list {
        height: 2.1rem;
        margin-bottom: 0.63rem;
        padding: 0 0.4rem 0 0.82rem;
        box-sizing: border-box;
      }
      .thead {
        display: flex;
        align-items: center;
        background-color: #0f245e;
        opacity: 0.8;
        div {
          display: inline-block;
          width: 12.5%;
          text-align: center;
          line-height: 0.4rem;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
      .t-body {
        height: 1.6rem;
      }
      .task-list {
        display: flex;
        align-items: center;
        cursor: pointer;
        color: #97b6e4;
        font-size: 0.14rem;
        &:nth-child(2n) {
          background-color: #0f245e;
          opacity: 0.8;
        }
        &:nth-child(2n + 1) {
          background-color: #04173d;
          opacity: 0.8;
        }
        div {
          width: 12.5%;
          text-align: center;
          line-height: 0.4rem;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          padding: 0 0.1rem;
          box-sizing: border-box;
        }
        .state {
          display: flex;
          justify-content: center;
          .image {
            width: 0.7rem;
            height: 0.3rem;
            background-size: 100% 100% !important;
            line-height: 0.3rem !important;
            color: #ffe27f;
          }
          .active {
            color: #4bfbff;
          }
        }
      }
      .thead {
        color: #40deff;
        font-size: 0.14rem;
      }
    }
  }
}
.quanpin {
  position: absolute;
  top: -0.88rem;
  right: 0.5rem;
  transition: 1.5s;
  z-index: 99;
}
.fullscreen {
  width: 100%;
  height: 100%;
}
</style>
<style lang="less">
.ant-select-dropdown-menu-item-active:not(.ant-select-dropdown-menu-item-disabled) {
  background-color: #0061C6!important;
  color: white!important;
}
.depart-select {
  .select-main {
    width: 2.77rem !important;
    height: 0.47rem !important;
    display: flex;
    justify-content: space-between;
    .ant-select-selection__rendered {
      width: 100%;
      height: 100%;
      line-height: 0.47rem;
      .ant-select-selection-selected-value {
        width: 100%;
        height: 100%;
        padding-left: 0.56rem;
        color: #a9d3ff;
        font-size: 0.16rem !important;
      }
    }
    .ant-select-arrow {
      right: 0.3rem;
    }
    .ant-select-selection,
    .ant-select-focused {
      width: 100%;
      height: 100%;
      border: none !important;
      outline: none !important;
      border-radius: unset;
      background: url(../../assets/department/<EMAIL>);
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
    .ant-select-selection-selected-value {
      color: rgba(0, 234, 255, 1);
    }
    .ant-select-focused .ant-select-selection,
    .ant-select-selection:focus,
    .ant-select-selection:active {
      border-color: transparent !important;
      border-right-width: 0 !important;
      outline: 0 !important;
      box-shadow: none !important;
      border: none !important;
    }
    .ant-select-selection__rendered {
      outline: none;
    }
  }
}
.select-dispatch {
  width: 100%;
  .ant-select-selection {
    background: rgba(9, 21, 42, 0.6);
    box-shadow: 0px 0px 6px 1px rgba(18, 143, 239, 0.8) inset;
    border: none;
    color: white;
  }
}
.select-plan {
  width: 100%;
  .ant-select-selection {
    display: flex;
    background: rgba(9, 21, 42, 0.6);
    box-shadow: 0px 0px 6px 1px rgba(18, 143, 239, 0.8) inset;
    border: none;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    color: white;
  }
  .ant-select-selection__rendered {
    width: 100%;
    height: 100%;
  }
  .ant-select-selection-selected-value {
    color: rgba(0, 234, 255, 1);
    font-size: 0.17rem;
    width: 90%;
  }

  .ant-select-focused .ant-select-selection,
  .ant-select-selection:focus,
  .ant-select-selection:active {
    border-color: #40a9ff;
    border-right-width: 0 !important;
    outline: 0;
    box-shadow: none;
  }

}
.date-dispatch {
  .ant-input {
    border: none !important;
    margin-left: 0 !important;
    background: rgba(9, 21, 42, 0.6) !important;
    box-shadow: 0px 0px 6px 1px rgba(18, 143, 239, 0.8) inset !important;
    color: white;
  }
}
.ant-modal-alarm {
  top: 1.8rem !important;
  .ant-modal-content {
    width: 5.09rem !important;
    min-height: 4.4rem!important;
    background-color: transparent !important;
    background-image: url('../../assets/department/<EMAIL>') !important;
    background-size: 100% 100% !important;
  }
  .ant-modal-footer {
    border: none !important;
    display: none !important;
  }
  .ant-modal-close-x {
    padding-top: 20px !important;
    color: #42adfb !important;
  }
  .ant-modal-body {
    padding: 0.16rem 0.54rem 0.28rem!important;
    min-height: 4.4rem;
    box-sizing: border-box;
    .title {
      color: #dcf0ff;
      font-size: 0.16rem;
      width: 3.25rem;
      height: 0.3rem;
      line-height: 0.3rem;
      background-image: url("../../assets/department/<EMAIL>");
      background-size: 100% 100%;
      margin-bottom: 0.24rem;
      margin-left: -0.36rem;
      padding-left: 0.3rem;
    }
    .content {
      .list {
        display: flex;
        align-items: center;
        margin-bottom: 0.16rem;
        > span {
          color: #5abefe;
          display: inline-block;
          font-size: 0.14rem;
          &:nth-child(1) {
            width: 0.8rem;
          }
          &:nth-child(2) {
            color: white;
            display: inline-block;
            width: calc(100% - 0.8rem);
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
        .radio-group {
          margin-left: 0.12rem;
          display: inline-block;
          span {
            color: white !important;
          }
        }
        .ant-input {
          background: rgba(9, 21, 42, 0.6);
          box-shadow: 0px 0px 6px 1px rgba(18, 143, 239, 0.8);
          opacity: 0.77;
          border-color: #128fef;
          margin-left: 0.16rem;
        }
      }
      .textarea {
        align-items: flex-start;
      }
      .parmas {
        margin-bottom: 0.012rem;
      }
      .table {
        margin-bottom: 0.16rem;
        color: white;
        .table-header {
          background-color: rgba(15, 36, 94, 0.6);
          span {
            width: 25%;
            display: inline-block;
            height: 0.3rem;
            text-align: center;
            line-height: 0.3rem;
            color: #5abefe;
            font-size: 0.14rem;
          }
        }
        .table-list {
          &:nth-child(2n) {
            background-color: rgba(3, 21, 61, 0.6);
          }
          &:nth-child(2n + 1) {
            background-color: rgba(15, 36, 94, 0.6);
          }
          span {
            width: 25%;
            display: inline-block;
            height: 0.3rem;
            text-align: center;
            line-height: 0.3rem;
            color: #bccede;
            font-size: 0.14rem;
          }
          .active {
            color: #ff5f58;
          }
        }
      }
    }
  }
  .footer {
    display: flex;
    align-items: center;
    padding-left: 0.8rem;
    padding-top: 0.2rem;
    padding-bottom: 0.3rem;
    color: #ffffff;
    .cancel {
      text-align: center;
      cursor: pointer;
      line-height: 0.31rem;
      width: 0.9rem;
      height: 0.31rem;
      background-image: url('../../assets/department/<EMAIL>');
      background-size: 100% 100%;
    }
    .confirm {
      text-align: center;
      cursor: pointer;
      line-height: 0.31rem;
      width: 0.9rem;
      height: 0.31rem;
      background-image: url('../../assets/department/<EMAIL>');
      background-size: 100% 100%;
      margin-left: 0.22rem;
    }
  }
  .ant-modal-footer {
    padding: 0.28rem 0.54rem;
    .footer {
      display: flex;
      align-items: center;
      padding-left: 0.8rem;
      color: #ffffff;
      .cancel {
        text-align: center;
        cursor: pointer;
        line-height: 0.31rem;
        width: 0.9rem;
        height: 0.31rem;
        background-image: url('../../assets/department/<EMAIL>');
        background-size: 100% 100%;
      }
      .confirm {
        text-align: center;
        cursor: pointer;
        line-height: 0.31rem;
        width: 0.9rem;
        height: 0.31rem;
        background-image: url('../../assets/department/<EMAIL>');
        background-size: 100% 100%;
        margin-left: 0.22rem;
      }
    }
  }
}
.ant-modal-alarmss {
  .ant-modal-body {
    padding-top: 0.2rem!important;
  }
}
.law-vehicle {
  width: 0.9rem;
  height: 0.91rem;
  position: absolute;
  pointer-events: auto;
  z-index: 99;
  top: 0.4rem;
  right: 4.5rem;
  cursor: pointer;
  background-image: url('../../assets/department/<EMAIL>');
  background-size: 100% 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  .vehicle-text {
    font-size: 0.14rem;
    font-weight: bold;
    color: #ffffff;
    top: 0;
  }
  .within-vehicle {
    position: absolute;
    width: 100%;
    height: 100%;
    background-image: url('../../assets/department/<EMAIL>');
  }
  @-webkit-keyframes rotation {
    from {
      -webkit-transform: rotate(0deg);
    }
    to {
      -webkit-transform: rotate(360deg);
    }
  }
  .Rotation {
    -webkit-transform: rotate(360deg);
    animation: rotation 2s linear infinite;
    -moz-animation: rotation 2s linear infinite;
    -webkit-animation: rotation 2s linear infinite;
    -o-animation: rotation 2s linear infinite;
  }
}
.ant-select-dropdown-menu-item {
  font-size: 0.16rem !important;
}
</style>
<template>
  <div class="container">
    <div
      class="container-bg"
      ref="containerBg"
      style="transition: all 2s"
      :style="{ backgroundImage: displayState ? 'url(' + bgImage + ')' : '' }"
    >
      <section
        class="left-part"
        style="transition: all 1s"
        :style="{ opacity: displayState ? '1' : '0' }"
      >
        <div class="list">
          <div
            v-for="(item, index) in list"
            :key="index"
            class="left-list"
            :style="{
              backgroundImage:
                'url(' + (active === index ? item.icon2 : item.icon1) + ')'
            }"
            @click="handleChangeType(index)"
          >
            <span>{{
              item.type === 0
                ? '全部'
                : item.type === 1
                ? '水环境'
                : item.type === 2
                ? '大气环境'
                : item.type === 3
                ? '重点污染源'
                : '巡岗报警'
            }}</span>
            <span>{{ enentTypeList[index] }}</span>
          </div>
        </div>
<!--        <div class="list-remark">注：一周任务</div>-->
      </section>
      <div class="law-vehicle" @click="toDetail('/vehicleManage')">
        <div class="within-vehicle Rotation"></div>
        <div class="vehicle-text">执法</div>
        <div class="vehicle-text">车辆</div>
      </div>
      <section
        class="right-part-copy"
        :class="{ 'right-part-to-left': isToRight ? 'right-part-to-left' : '' }"
        style="transition: all 1s"
        :style="{ opacity: displayState ? '1' : '0' }"
      >
        <div class="select-area depart-select">
          <a-select
            v-model="defaultpartMent"
            style="width: 1rem"
            class="select-main"
            @change="changeDepartment"
          >
            <a-icon
              slot="suffixIcon"
              type="caret-down"
              style="
                color: rgba(0, 234, 255, 1);
                width: 0.23rem;
                height: 0.11rem;
              "
            />
            <a-select-option
              :value="item.departmentId"
              v-for="(item, index) in departMentList"
              :key="index"
              class="select-option"
            >
              {{ item.departmentName }}</a-select-option
            >
          </a-select>
        </div>
        <div class="right-part-one">
          <div class="title">
            <span>实时监测</span>
          </div>
          <div class="sub-title">
            <img src="@/assets/biaoti.png" alt />
          </div>
          <div v-if="pollutNameList.length" class="button-list">
            <span
              v-for="(item, index) in pollutNameList"
              :key="index"
              :class="{ active: activeIndex == index ? 'active' : '' }"
              :style="{ 'flex-grow': item.length > 4 ? 2 : 1 }"
              @click="handleClick(index)"
              >{{ item }}</span
            >
          </div>
          <real-time-monitor
            :id="'realTimeMonitor' + new Date().getTime()"
            :width="'4rem'"
            :height="'1.75rem'"
            :propData="realTimeData"
            :smooth="true"
          />
        </div>
        <div class="right-part-two">
          <div class="title">
            <span>执行部门</span>
          </div>
          <div class="sub-title">
            <img src="@/assets/biaoti.png" alt />
          </div>
          <div class="executive-department">
            <div
              class="node"
              v-for="(item, index) in executiveDepartmentList1"
              :key="index"
              :style="{
                backgroundImage:
                  'url(' +
                  (item.completeStatus
                    ? defaultComplateImage
                    : defaultUnComplateImage) +
                  ')'
              }"
              :class="{
                node2: executiveDepartmentList1.length == 2 ? 'node2' : ''
              }"
            >
              {{ item.taskNodeName }}
            </div>
            <div class="major-ndoe" v-if="executiveDepartmentList2.length">
              {{ executiveDepartmentList2[0].taskNodeName }}
            </div>
            <div id="1" v-show="executiveDepartmentList1.length == 3">
              <lottie
                :id="'quanke' + new Date().getTime() + 1"
                :options="defaultOptions"
                :width="370"
                :height="310"
              />
            </div>
            <div id="2" v-show="executiveDepartmentList1.length == 2">
              <lottie
                :id="'quanke' + new Date().getTime() + 2"
                :options="defaultOptions1"
                :width="370"
                :height="310"
              />
            </div>
          </div>
        </div>
        <div class="right-part-three" style="overflow: hidden">
          <div class="title">
            <span>部门反馈</span>
          </div>
          <div class="sub-title">
            <img src="@/assets/biaoti.png" alt />
          </div>
          <swiper
            v-if="feedbackList.length"
            :options="
              feedbackList.length > 1 ? swiperRebackOption : swiperRebackOption1
            "
            class="reback"
          >
            <swiper-slide
              class="reback-list"
              v-for="(item, index) in feedbackList"
              :key="index"
            >
              <div class="left">
                <span>{{ item.time2 }}</span>
                <span>{{ item.time1 }}</span>
              </div>
              <!--                      :class="{'after': index !==2 ? 'after' : ''}"-->
              <div
                class="center"
                :style="{
                  visibility: feedbackList.length == 1 ? 'hidden' : ''
                }"
              >
                <div class="after"></div>
              </div>
              <div class="right">
                <div class="right-bg">
                  <div class="title" style="display: flex; align-items: center">
                    <img
                      :src="
                        item.type == 1
                          ? zhzfjIcon
                          : item.type == 2
                          ? zjjIcon
                          : item.type == 3
                          ? sthjjIcon
                          : shwIcon
                      "
                      alt=""
                      style="width: 24px; height: 24px; margin-right: 10px"
                    />{{ item.departmentName ? item.departmentName : '--' }}
                  </div>
                  <div class="content" :title="item.remark">
                    任务反馈：{{ item.remark ? item.remark : '--' }}
                  </div>
                  <div class="check" v-if="false">查看图片</div>
                </div>
              </div>
            </swiper-slide>
          </swiper>
          <div v-else class="no-feed">暂无反馈</div>
        </div>
      </section>
      <section
        class="right-part"
        :class="{
          'right-part-to-right ': isToRight ? 'right-part-to-right ' : ''
        }"
        style="transition: all 1s"
        :style="{ opacity: displayState ? '1' : '0' }"
      >
        <div class="select-area depart-select">
          <a-select
            v-model="defaultpartMent"
            style="width: 1rem"
            class="select-main"
            @change="changeDepartment"
          >
            <a-icon
              slot="suffixIcon"
              type="caret-down"
              style="
                color: rgba(0, 234, 255, 1);
                width: 0.23rem;
                height: 0.11rem;
              "
            />
            <a-select-option
              :value="item.departmentId"
              v-for="(item, index) in departMentList"
              :key="index"
              class="select-option"
            >
              {{ item.departmentName }}</a-select-option
            >
          </a-select>
        </div>
        <div class="right-part-one">
          <div class="title">
            <span>任务完成情况</span>
          </div>
          <div class="sub-title">
            <img src="@/assets/biaoti.png" alt />
          </div>
          <taskProcessing
              :id="'taskProcessing1' + new Date().getTime()"
              :width="'4rem'"
              :height="'1.75rem'"
              :propData="completeData"
              :smooth="true"
          />
<!--          <mission-accomplished-->
<!--            :id="'accomplished' + new Date().getTime()"-->
<!--            :width="'4rem'"-->
<!--            :height="'1.75rem'"-->
<!--            :propData="completeData"-->
<!--            :smooth="true"-->
<!--          />-->
        </div>
        <div class="right-part-two">
          <div
            class="title"
            style="
              display: flex;
              justify-content: space-between;
              align-items: flex-end;
            "
          >
            <span>任务分类统计</span>
<!--            <span style="color: #86c6ff; font-size: 0.14rem"-->
<!--              >数据统计：一周</span-->
<!--            >-->
          </div>
          <div class="sub-title">
            <img src="@/assets/biaoti.png" alt />
          </div>
          <task-type
            :id="'taskType' + new Date().getTime()"
            :width="'4rem'"
            :height="'1.75rem'"
            :propData="taskTypeData"
            :smooth="true"
          />
        </div>
        <div class="right-part-three">
          <div class="title">
            <span>每日任务处理</span>
          </div>
          <div class="sub-title">
            <img src="@/assets/biaoti.png" alt />
          </div>
          <dayTask
            :id="'dayTask' + new Date().getTime()"
            :width="'4rem'"
            :height="'2.2rem'"
            :propData="taskHandleData"
            :smooth="true"
          />
        </div>
        <div></div>
      </section>
      <section
        class="bottom-part"
        style="transition: all 1s"
        :style="{ opacity: displayState ? '1' : '0' }"
      >
        <div v-if="taskData.length" class="list">
          <div class="thead">
            <div>任务标题</div>
            <div>告警类型</div>
            <div>事件类型</div>
            <div>上报时间</div>
            <div>告警内容</div>
            <div>发生站点</div>
            <div>发生地址</div>
            <div>反馈</div>
          </div>
          <swiper
            class="t-body"
            :options="taskData.length > 4 ? swiperOption : swiperOption1"
            ref="mySwiper"
          >
            <swiper-slide
              class="task-list"
              v-for="(item, index) in taskData"
              :key="index"
              :data-href="JSON.stringify(item)"
              :data-index="index"
            >
              <div :data-href="JSON.stringify(item)" :data-index="index">
                {{ item.title }}
              </div>
              <div :data-href="JSON.stringify(item)" :data-index="index">
                {{
                  item.typeId == 2
                    ? '大气环境'
                    : item.typeId == 1
                    ? '水环境'
                    : item.typeId == 3
                    ? '重点污染源'
                    : '巡岗报警'
                }}
              </div>
              <div :data-href="JSON.stringify(item)" :data-index="index">
                {{ item.eventTypeId ? '自动监测' : '巡岗上报' }}
              </div>
              <div :data-href="JSON.stringify(item)" :data-index="index">
                {{ item.createTime }}
              </div>
              <div
                :title="item.content"
                :data-href="JSON.stringify(item)"
                :data-index="index"
              >
                {{ item.content }}
              </div>
              <div :data-href="JSON.stringify(item)" :data-index="index">
                {{ item.stationName ? item.stationName : '--' }}
              </div>
              <div :data-href="JSON.stringify(item)" :data-index="index">
                {{ item.address ? item.address : '--' }}
              </div>
              <div
                :data-href="JSON.stringify(item)"
                :data-index="index"
                class="state"
              >
                <div
                  class="image"
                  :data-href="JSON.stringify(item)"
                  :data-index="index"
                  :style="{
                    backgroundImage:
                      'url(' +
                      (item.completeStatus === 1 ? statusIcon2 : statusIcon1) +
                      ')'
                  }"
                  :class="{ active: item.completeStatus === 1 ? 'active' : '' }"
                >
                  {{ item.completeStatus === 1 ? '已完成' : '进行中' }}
                </div>
              </div>
            </swiper-slide>
          </swiper>
        </div>
        <!--              <div v-else class="list no-text">暂无数据</div>-->
      </section>
    </div>
    <section class="center-map">
      <center-map
        :mapMarkerList="taskData"
        :activeIndex="active"
        :taskIndex="taskIndex"
        @markerClick="markersClick"
        :currObj="currObj"
        @change="changeMarker"
      />
      <section class="quanpin" @click="fullScreen">
        <img src="@/assets/quanping.png" alt />
      </section>
    </section>
    <a-modal
      :visible.sync="visible"
      class="ant-modal-alarm"
      :class="{'ant-modal-alarmss': (taskDetail.dispatch || taskDetail.myCompleteStatus==0) ? 'ant-modal-alarmss' : ''}"
      @cancel="handleCancel"
    >
      <div class="title">告警详情</div>
      <div class="content">
        <div class="list">
          <span>任务描述：</span>
          <span v-if="taskDetail">{{ taskDetail.content }}</span>
        </div>
        <div class="list">
          <span>告警类型：</span>
          <span v-if="taskDetail">{{
            taskDetail.stationTypeId == 2
              ? '大气环境'
              : taskDetail.stationTypeId == 1
              ? '水环境'
              : taskDetail.stationTypeId == 3
              ? '重点污染源'
              : '巡岗报警'
          }}</span>
        </div>
        <div class="list">
          <span>上报时间：</span>
          <span v-if="taskDetail">{{ taskDetail.createTime }}</span>
        </div>
        <div class="list">
          <span>告警地点：</span>
          <span v-if="taskDetail">{{ taskDetail.address }}</span>
        </div>
        <div class="list" :class="{'parmas': taskAlarmList.length ? 'parmas' : ''}">
          <span>告警参数：</span>
          <span
            v-if="
              !taskAlarmList.length
            "
            >暂无告警项</span
          >
        </div>
        <div
          v-show="taskAlarmList.length"
          class="table"
        >
          <div class="table-header">
            <span>指标项</span>
            <span>超标项</span>
            <span>标准</span>
            <span>告警时间</span>
          </div>
          <div
            v-for="(item, index) in taskAlarmList"
            :key="index"
            class="table-list"
          >
            <span>{{ item.alarmItemName }}</span>
            <span class="active">{{ item.alarmValue }}<span style="color: #bccede;display: inline">{{ item.alarmItemUnit }}</span></span>
            <span>{{ item.alarmValueType == 0 ? item.alarmThreshold : item.alarmValueType == 1 ? '>'+item.alarmMinValue+',<'+item.alarmMaxValue : '<'+item.alarmMinValue+',>'+item.alarmMaxValue }}{{ item.alarmItemUnit }}</span>
            <span>{{ item.alarmTime }}</span>
          </div>
        </div>
        <div v-if="taskDetail.dispatch || taskDetail.myCompleteStatus==0" class="list">
          <span>选择类型：</span>
          <div class="radio-group">
            <a-radio-group v-model="type">
              <a-radio :value="1" v-if="taskDetail.dispatch"> 派遣 </a-radio>
              <a-radio :value="2" v-if="taskDetail.myCompleteStatus==0"> 处置 </a-radio>
            </a-radio-group>
          </div>
        </div>
        <div
          v-if="taskDetail.dispatch || taskDetail.myCompleteStatus==0"
          class="list date-dispatch"
          :class="{ textarea: type === 2 ? 'textarea' : '' }"
        >
          <span>任务处置：</span>
          <span
            v-if="type === 2"
            style="display: inline-block; width: calc(100% - 0.8rem)"
          >
            <a-textarea
              class="select-dispatch"
              style="color: white"
              v-model="text"
              placeholder="请输入任务处置详情"
              :maxLength="250"
              :auto-size="{ minRows: 3, maxRows: 5 }"
            />
          </span>
          <span
            v-if="type === 1"
            style="display: inline-block; width: calc(100% - 0.8rem)"
          >
            <a-select
              v-model="planId"
              class="select-area select-plan"
              size="large"
            >
              <a-icon
                slot="suffixIcon"
                type="caret-down"
                style="color: #41abf9; width: 0.14rem; height: 0.07rem"
              />
              <a-select-option
                :value="item.planId"
                v-for="(item, index) in planList"
                :key="index"
                class="select-option"
              >
                {{ item.planName }}</a-select-option
              >
            </a-select>
          </span>
        </div>
        <div v-if="type === 1 && (taskDetail.dispatch || taskDetail.myCompleteStatus==0)" class="list date-dispatch">
          <span>结束时间：</span>
          <span style="display: inline-block; width: calc(100% - 0.8rem)">
            <a-date-picker
              size="large"
              class="select-dispatch"
              placeholder="选择结束时间"
              allowClear
              :disabled-date="disabledDate"
              :showTime="{format: 'HH:mm' }"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              format="YYYY-MM-DD HH:mm"
              v-model="endTime"
            />
          </span>
        </div>
        <div v-if="taskDetail.dispatch || taskDetail.myCompleteStatus==0" class="footer">
          <div class="cancel" @click="handleCancel">取消</div>
          <div class="confirm" @click="handDispatch">{{type == 1 ? '派遣' : '处置'}}</div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script>
import lottie from '@/components/lottie'
import * as animationData from '../../../public/images/data'
import * as animationData1 from '../../../public/images1/data'
import centerMap from './map'
import {Icon, Select, Modal, Radio, Input, DatePicker, message} from "ant-design-vue"
import realTimeMonitor from '@/components/Charts/realTimeMonitor'
import missionAccomplished from '@/components/Charts/missionAccomplished'
import taskProcessing from '@/components/Charts/taskProcessing'
import dayTask from '@/components/Charts/dayTask'
import taskType from '@/components/Charts/taskType'
import { Swiper, SwiperSlide } from "vue-awesome-swiper"
import { getDepartment } from '@/api/login'
import { socketUrl2 } from "../../utils"
import moment from 'moment'
export default {
  name: 'index1',
  components: {
    centerMap,
    AIcon: Icon,
    ASelect: Select,
    ASelectOption: Select.Option,
    realTimeMonitor,
    Swiper,
    SwiperSlide,
    AModal: Modal,
    ARadio: Radio,
    ARadioGroup: Radio.Group,
    ATextarea: Input.TextArea,
    missionAccomplished,
    taskProcessing,
    dayTask,
    taskType,
    lottie,
    ADatePicker: DatePicker
  },
  data() {
    const _this = this
    return {
      sthjjIcon: require('../../assets/<EMAIL>'),
      zhzfjIcon: require('../../assets/<EMAIL>'),
      zjjIcon: require('../../assets/<EMAIL>'),
      shwIcon: require('../../assets/<EMAIL>'),
      defaultComplateImage: require('../../assets/department/<EMAIL>'),
      defaultUnComplateImage: require('../../assets/department/<EMAIL>'),
      defaultOptions: { animationData: animationData },
      defaultOptions1: { animationData: animationData1 },
      bgImage: require('../../assets/department/<EMAIL>'),
      swiperOption: {
        direction: 'vertical',
        slidesPerView: 4,
        slidesPerGroup: 1,
        loop: true,
        autoplay: {
          delay: 2800,
          disableOnInteraction: false
        },
        on: {
          click: function (e) {
            _this.currObj = JSON.parse(e.target.getAttribute('data-href'))
          }
        }
      },
      swiperOption1: {
        direction: 'vertical',
        slidesPerView: 4,
        slidesPerGroup: 1,
        loop: false,
        autoplay: {
          delay: 2800,
          disableOnInteraction: false
        },
        on: {
          click: function (e) {
            _this.currObj = JSON.parse(e.target.getAttribute('data-href'))
          }
        }
      },
      swiperRebackOption: {
        direction: 'vertical',
        slidesPerView: 2,
        slidesPerGroup: 1,
        loop: true,
        autoplay: {
          delay: 2800,
          disableOnInteraction: false
        }
      },
      swiperRebackOption1: {
        direction: 'vertical',
        slidesPerView: 2,
        slidesPerGroup: 1,
        loop: false,
        autoplay: {
          delay: 2800,
          disableOnInteraction: false
        }
      },
      statusIcon1: require('../../assets/department/<EMAIL>'),
      statusIcon2: require('../../assets/department/<EMAIL>'),
      active: 0,
      visible: false,
      taskDetail: {},
      type: 1,
      planId: undefined,
      isToRight: false,
      text: '',
      moment,
      list: [
        {
          type: 0,
          icon1: require('../../assets/department/<EMAIL>'),
          icon2: require('../../assets/department/<EMAIL>')
        },
        {
          type: 1,
          icon1: require('../../assets/department/<EMAIL>'),
          icon2: require('../../assets/department/<EMAIL>')
        },
        {
          type: 2,
          icon1: require('../../assets/department/<EMAIL>'),
          icon2: require('../../assets/department/<EMAIL>')
        },
        {
          type: 3,
          icon1: require('../../assets/department/<EMAIL>'),
          icon2: require('../../assets/department/<EMAIL>')
        },
        {
          type: 4,
          icon1: require('../../assets/department/<EMAIL>'),
          icon2: require('../../assets/department/<EMAIL>')
        }
      ],
      currObj: {},
      taskData: [],
      taskIndex: -1,
      enentTypeList: [0, 0, 0, 0, 0],
      defaultpartMent: undefined,
      feedbackList: [],
      departMentList: [],
      executiveDepartmentList1: [],
      executiveDepartmentList2: [],
      realTimeData: {
        bottomList: [],
        dataList: [],
        standard: 0,
        max: 0,
        unit: '',
        name: ''
      },
      pollutNameList: [],
      pollutList: [],
      planList: [],
      taskAlarmList: [],
      taskHandleData: {
        bottomList: [],
        dataList: []
      },
      activeIndex: 0,
      completeData: {
        bottomList: [],
        dataList: [],
        dataList1: [],
        dataList2: [],
      },
      taskTypeData: {
        bottomList: [],
        dataList: []
      },
      rebackList: [],
      socket: null,
      displayState: true,
      isFirst: false,
      endTime: undefined,
      userId: undefined,
      departmentId: undefined
    }
  },
  mounted() {
    const UserInfor = localStorage.getItem('UserInfor')
    this.userId = UserInfor ? JSON.parse(UserInfor).user.userId : undefined
    this.departmentId = UserInfor ? JSON.parse(UserInfor).department.departmentId : undefined
    this.getDepartment()
  },
  methods: {
    disabledDate(current) {
      return current < moment().startOf('day')
    },
    fullScreen() {
      this.displayState = !this.displayState
    },
    changeDepartment() {
      this.isToRight = false
      this.isFirst = false
      this.send()
    },
    // 二级页面
    toDetail(url) {
      this.$router.push(url)
    },
    changeMarker(args) {
      this.activeIndex = 0
      if (args) {
        this.isToRight = true
        this.handleTaskDetail(args)
      } else {
        this.isToRight = false
        this.taskDetail = null
      }
    },
    markersClick(args) {
      this.activeIndex = 0
      if (args) {
        const data = args.w.data
        this.isToRight = true
        this.visible = true
        if (data.dispatch) {
          this.type = 1
        }
        if (Number(data.myCompleteStatus) === 0) {
          this.type = 2
        }
        this.handleTaskDetail(args)
      } else {
        this.isToRight = false
        this.visible = false
        this.taskDetail = {}
      }
    },
    handleTaskDetail(args) {
      const data = args.w.data
      this.taskDetail = data
      this.socket.send(JSON.stringify({ code: 4, taskId: data.taskId }))
      this.socket.send(JSON.stringify({ code: 6, taskId: data.taskId }))
      this.socket.send(JSON.stringify({ code: 9, taskId: data.taskId }))
      this.socket.send(JSON.stringify({ code: 12, userId: this.userId, stationId: data.stationId, stationTypeId: data.stationTypeId }))
    },
    handleChangeType(index) {
      this.active = index
      this.isToRight = false
      this.taskData = []
      this.taskIndex = -1
      this.socket.send(
        JSON.stringify({
          code: 3,
          departmentId: this.defaultpartMent,
          stationTypeId: index ? index : undefined,
          userId: this.userId
        })
      )
    },
    getDepartment() {
      getDepartment()
        .then((res) => {
          this.departMentList = res.data.data || []
          this.defaultpartMent = this.departMentList.length
            ? this.departMentList[0].departmentId
            : undefined
        })
        .finally(() => {
          this.connect()
        })
    },
    handleCancel(e) {
      this.visible = false
      this.endTime = undefined
      this.planId = undefined
      this.text = ''
    },
    handleClick(index) {
      this.activeIndex = index
      this.realTimeData = this.pollutList[index]
    },
    handDispatch() {
      if (this.type == 1) {
        if (!this.planId) {
          message.warning('请选择任务处置')
          return
        }
        if (!this.endTime) {
          message.warning('请选择结束时间')
          return
        }
        this.socket.send(JSON.stringify({ code: 11, departmentId: this.departmentId, planId: this.planId, parentId: this.taskDetail.taskId, userId: this.userId, endTime: this.endTime }))
      }
      if (this.type == 2) {
        if (!this.text) {
          message.warning('请输入任务处置详情')
          return
        }
        this.socket.send(JSON.stringify({ code: 10, taskId: this.taskDetail.taskId, userId: this.userId, completeRemark: this.text }))
      }
    },
    connect() {
      this.socket = new WebSocket(socketUrl2())
      // 监听socket连接
      this.socket.onopen = this.open
      // 监听socket错误信息
      this.socket.onerror = this.error
      // 监听socket消息
      this.socket.onmessage = this.getMessage

      this.$once('hook:beforeDestroy', () => {
        this.socket.onopen = () => {}
        this.socket.onerror = () => {}
        this.socket.onmessage = () => {}
      })
    },
    open() {
      this.send()
    },
    send() {
      this.taskData = []
      this.isFirst = false
      this.active = 0
      this.socket.send(
        JSON.stringify({ code: 1, departmentId: this.defaultpartMent })
      )
      this.socket.send(
        JSON.stringify({ code: 2, departmentId: this.defaultpartMent })
      )
      this.socket.send(
        JSON.stringify({ code: 3, departmentId: this.defaultpartMent, userId: this.userId })
      )
    },
    error() {
      console.error('系统连接错误')
    },
    getMessage(msg) {
      const data = JSON.parse(msg.data)
      if (data.code == -1) {
        this.completeData = {
          bottomList: data.taskCompletion.map((item) =>
            item.create_date.substr(5)
          ),
          dataList: data.taskCompletion.map((item) => item.completed),
          dataList1: data.taskCompletion.map((item) => item.uncompleted + item.completed),
          dataList2: data.taskCompletion.map((item) => item.rate.substr(0, item.rate.indexOf('%')))
        }
        console.log(this.completeData, 'completeData')
        this.taskHandleData = {
          bottomList: data.taskCompletion.map((item) =>
            item.create_date.substr(5)
          ),
          dataList: data.taskCompletion.map((item) => item.node_completed)
        }
      }
      if (data.code == -2) {
        this.taskTypeData = {
          bottomList: [],
          total: data.count,
          dataList: data.typeStatistics.map((item) => {
            item.value = item.count
            return item
          })
        }
      }
      if (data.code == -3) {
        this.taskData = data.linkageTaskList || []
        if (!this.isFirst) {
          this.isFirst = true
          this.enentTypeList = [0, 0, 0, 0, 0]
          this.enentTypeList[0] = data.linkageTaskList.length
          const data1 = (data.linkageTaskList || []).forEach((item) => {
            switch (item.typeId) {
              case 1:
                this.enentTypeList[1] = this.enentTypeList[1] + 1
                break
              case 2:
                this.enentTypeList[2] = this.enentTypeList[2] + 1
                break
              case 3:
                this.enentTypeList[3] = this.enentTypeList[3] + 1
                break
              case 4:
                this.enentTypeList[4] = this.enentTypeList[4] + 1
                break
            }
          })
        }
      }
      if (data.code == -4) {
        this.executiveDepartmentList1 = data.executiveDepartmentList.filter(
          (item) => item.parentId
        )
        this.executiveDepartmentList2 = data.executiveDepartmentList
          .filter((item) => !item.parentId)
          .map((item) => {
            item.taskNodeName =
              this.taskDetail.stationTypeId === 1
                ? '水质告警'
                : this.taskDetail.stationTypeId === 2
                ? '空气告警'
                : this.taskDetail.stationTypeId === 3
                ? '重污告警'
                : '巡岗告警'
            return item
          })
        console.log(
          this.executiveDepartmentList1,
          this.executiveDepartmentList2,
          'executiveDepartmentList1'
        )
        this.feedbackList = (data.feedbackList || []).map((item) => {
          item.time1 = item.time.substr(0, item.time.indexOf(' '))
          item.time1 = item.time1.replace(/-/g, '.')
          item.time2 = item.time.substr(
            item.time.indexOf(' ') + 1,
            item.time.length - item.time.indexOf(' ')
          )
          item.type =
            (item.departmentName.indexOf('综合') ||
              item.departmentName.indexOf('执法')) > -1
              ? 1
              : item.departmentName.indexOf('住建') > -1
              ? 2
              : (item.departmentName.indexOf('生态') ||
                  item.departmentName.indexOf('环境')) > -1
              ? 3
              : 4
          return item
        })
      }
      if (data.code == -6) {
        const data1 = data.itemVOList || []
        this.pollutNameList = data1.map((item) => item.pollutantName)
        this.pollutList = data1.map((item) => {
          const bottomList = item.mapList.map((item) => item.name)
          const dataList = item.mapList.map((item) => item.value || 0)
          const data1 = dataList.sort((a, b) => a - b)
          return {
            bottomList: bottomList,
            dataList: dataList,
            standard: item.alarmStandard,
            max:
              (data1.length ? data1[data1.length - 1] : 0) < item.alarmStandard
                ? item.alarmStandard
                : data1.length
                ? data1[data1.length - 1]
                : 0,
            unit: item.unit,
            name: item.pollutantName
          }
        })
        this.realTimeData = this.pollutList[0]
      }
      if (data.code == -9) {
        this.taskAlarmList = (data.taskAlarmList || []).map(item => {
          item.alarmTime = item.alarmTime.replace(/T/, ' ')
          item.alarmTime = item.alarmTime.substr(5, 11)
          return item
        })
      }
      if (data.code == -12) {
        this.planList = data.planList || []
      }
      if (data.code == -11 && data.success) {
        message.success('派遣成功')
        this.handleCancel()
        this.taskIndex = this.taskData.findIndex(item => item.taskId === this.taskDetail.taskId)
        this.socket.send(
                JSON.stringify({
                  code: 3,
                  departmentId: this.defaultpartMent,
                  stationTypeId: this.active ? this.active : undefined,
                  userId: this.userId
                })
        )
      }
      if (data.code == -10 && data.success) {
        message.success('处置成功')
        this.handleCancel()
        this.taskIndex = this.taskData.findIndex(item => item.taskId === this.taskDetail.taskId)
        this.socket.send(
          JSON.stringify({
            code: 3,
            departmentId: this.defaultpartMent,
            stationTypeId: this.active ? this.active : undefined,
            userId: this.userId
          })
        )
      }
    }
  }
}
</script>
