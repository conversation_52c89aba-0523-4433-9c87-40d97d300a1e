<template>
  <div class="patrol-kanban-container">
    <div class="bg_box"></div>
    <PatrolMap :patrolList="patrolList" :currentTaskId="currentTaskId" @clickMarker="clickMarker"></PatrolMap>
    <div class="left-box-wrapper">
      <div class="left-first-box">
        <CommonTitle title="巡岗异常上报统计"></CommonTitle>
        <LeftFirstBox :LeftFirstBoxData="LeftFirstBoxData"></LeftFirstBox>
      </div>
      <div class="left-second-box" style="margin-top: 20px">
        <CommonTitle title="问题发现率与整改率">
          <template #title>
            <div class="tool_list">
              <span
                class="tool_item"
                @click="type = 'month'"
                :class="{ active: type === 'month' }"
                >月度</span
              >
              <span
                class="tool_item"
                @click="type = 'year'"
                :class="{ active: type === 'year' }"
                >年度</span
              >
            </div>
          </template>
        </CommonTitle>
        <LeftSecondBox :leftSecondData="leftSecondData"></LeftSecondBox>
      </div>
      <div class="left-thrid-box" style="margin-top: 20px">
        <CommonTitle title="巡岗列表">
          <template #title>
            <div class="picker_box">
              <i class="el-icon-date rili_icon"></i>
              <el-date-picker
                v-model="yearAndMonth"
                value-format="yyyy-MM-dd"
                format="yyyy年MM月"
                type="month"
                size="small"
                popper-class="picker_class"
                prefix-icon="null"
                :clearable="false"
                :editable="false"
                :picker-options="pickerOptions"
                @change="onDateChange"
                placeholder="选择日期"
              ></el-date-picker>
            </div>
          </template>
        </CommonTitle>
        <LeftThridBox :patrolList="patrolList" @clickMarker="clickMarker" :taskId="currentTaskId"></LeftThridBox>
      </div>
    </div>
    <div class="right-box-wrpper">
      <div class="right-first-box">
        <CommonTitle title="巡岗情况"></CommonTitle>
        <RightFirstBox :detail="detail"></RightFirstBox>
      </div>
      <div class="right-second-box">
        <CommonTitle title="执行反馈">
          <template #title>
            <div class="detail_box" @click="handleClickProcessDetail">
              查看处理流程
            </div>
          </template>
        </CommonTitle>
        <RightSecondBox :feedbackList="feedbackList"></RightSecondBox>
      </div>
    </div>
    <!-- 处置流程 -->
    <a-modal
      :visible.sync="processVisible"
      class="ant-modal-process"
      :footer="null"
      :maskClosable="true"
      :centered="true"
    >
      <div class="title">
        <span>处置流程</span>
        <div class="close" @click="processVisible = false">
          <img src="@/assets/guanbi.png" alt="" class="close" />
        </div>
      </div>
      <div class="content">
        <orgTree
          v-if="processVisible"
          :orgTreeList="orgTreeList"
          :typeId="typeId"
        ></orgTree>
      </div>
    </a-modal>
  </div>
</template>

<script>
import CommonTitle from "./components/commonTitle";
import PatrolMap from "./components/map";
import LeftFirstBox from "./components/left-first-box";
import LeftSecondBox from "./components/left-second-box";
import LeftThridBox from "./components/left-thrid-box";

import RightFirstBox from "./components/right-first-box";
import RightSecondBox from "./components/right-second-box";

import { patrolCount, patrolList, patrolDetail } from "@/api/patrolKanban";

import orgTree from "../task-management/components/org-tree/index.vue";
import dayjs from "dayjs";

export default {
  name: "",
  data() {
    return {
      type: "month",
      LeftFirstBoxData: {
        thisMonth: 0,
        thisYear: 0,
        total: 0,
      },
      leftSecondData: {
        issues: [],
        rectification: [],
      },
      patrolCountObj: {},
      yearAndMonth: new Date(),
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
      patrolList: [],
      processVisible: false,
      orgTreeList: [],
      typeId: 0,
      feedbackList: [],
      timer: null,
      detail: {},
      index:0,
      currentTaskId: "",
    };
  },
  created() {
    this.handleGetPatrolCount();
    this.handleGetPatrolList();
    // this.handlegetPatrolDetail();
  },
  methods: {
    // 点击查看处置流程
    handleClickProcessDetail() {
      if(!this.orgTreeList || !this.orgTreeList.length) {
        return this.$message.warning('暂无数据')
      }
      this.processVisible = true
    },
    // 获取巡查统计
    handleGetPatrolCount() {
      patrolCount().then((res) => {
        const { thisMonth, thisYear, total } = res.data.data || {};
        this.LeftFirstBoxData = {
          thisMonth: thisMonth || 0,
          thisYear: thisYear || 0,
          total: total || 0,
        };
        this.patrolCountObj = res.data.data || {};
        this.leftSecondData = {
          issues: this.patrolCountObj.issuesMonth || [],
          rectification: this.patrolCountObj.rectificationMoth || [],
        };
      });
    },
    // 日期选择
    onDateChange(val) {
      this.yearAndMonth = val;
      this.handleGetPatrolList();
    },
    // 获取巡查列表
    handleGetPatrolList() {
      const params = {
        yearAndMonth: dayjs(this.yearAndMonth).format("YYYY-MM"),
      };
      console.log("params", params)
      patrolList(params).then((res) => {
        this.patrolList = res.data.data || [];
        if (this.patrolList.length > 0) {
          this.timerAnimation()
          this.currentTaskId = this.patrolList[0].taskId;
          this.handlegetPatrolDetail(this.currentTaskId);
        }
      });
    },
    // 获取详情
    handlegetPatrolDetail(taskId) {
      const params = {
        taskId: taskId,
      };
      patrolDetail(params).then((res) => {
        
        this.orgTreeList = res?.data?.data?.task?.taskNodeList ?? [];
        this.feedbackList = res?.data?.data?.task?.taskLogList ?? [];
        this.detail = res?.data?.data?.task ?? {};
        this.typeId = res?.data?.data?.task?.typeId ?? 0;
      });
    },
    clickMarker(taskId) {
      this.currentTaskId = taskId;
      this.handlegetPatrolDetail(taskId);
      this.timerAnimation()
    },
    // 定时器动画
    timerAnimation() {
      if(this.timer) clearInterval(this.timer);
      if(this.patrolList.length < 2) return
      this.timer = setInterval(() => {
        this.startAnimation()
      }, 1000 * 10  );
    },
    startAnimation() {
      this.index++;
      if (this.index >= this.patrolList.length) {
        this.index = 0;
      }
      this.currentTaskId = this.patrolList[this.index].taskId;
       this.handlegetPatrolDetail(this.currentTaskId);
    }
  },
  computed: {},
  watch: {
    type(val) {
      if (val === "month") {
        this.leftSecondData = {
          issues: this.patrolCountObj.issuesMonth || [],
          rectification: this.patrolCountObj.rectificationMoth || [],
        };
      } else {
        this.leftSecondData = {
          issues: this.patrolCountObj.issuesYear || [],
          rectification: this.patrolCountObj.rectificationYear || [],
        };
      }
    },
  },
  beforeDestroy() {
    if(this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }
  },
  components: {
    PatrolMap,
    CommonTitle,
    LeftFirstBox,
    LeftSecondBox,
    LeftThridBox,
    RightFirstBox,
    RightSecondBox,
    orgTree,
  },
};
</script>

<style lang="less" scoped>
.patrol-kanban-container {
  width: 100%;
  height: 100%;
  position: relative;
  .bg_box {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    background: url("~@/assets/department/<EMAIL>") no-repeat center;
    z-index: 1;
    pointer-events: none;
  }
  .left-box-wrapper {
    position: absolute;
    left: 40px;
    top: 40px;
    z-index: 8;
  }
  .right-box-wrpper {
    position: absolute;
    right: 40px;
    top: 40px;
    z-index: 8;
  }
  .tool_list {
    height: 100%;
    display: flex;
    align-items: center;
    .tool_item {
      width: 75px;
      height: 25px;
      background: url("~@/assets/patrolKanban/wxz.png") no-repeat center;
      background-size: contain;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 14px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #0e8aff;
      cursor: pointer;
    }
    .active {
      background: url("~@/assets/patrolKanban/xz.png") no-repeat center;
      background-size: contain;
      color: #00eaff;
    }
  }
  .picker_box {
    width: 110px;
    height: 26px;
    // background: rgba(0, 0, 0, 0.5);
    background: url("~@/assets/patrolKanban/date_picker_bg.png") no-repeat
      center;
    background-size: contain;
    // text-align: center;
    // line-height: 26px;
    // position: relative;
    display: flex;
    align-items: center;
    justify-content: space-around;
    box-sizing: border-box;

    .rili_icon {
      // position: absolute;
      // top: 8px;
      // left: 44%;
      font-size: 14px;
      margin-left: 10px;
      margin-right: 5px;
    }
    ::v-deep .el-date-editor {
      flex: 1;
    }
    /deep/.el-input__inner {
      background-color: transparent;
      border: 1px solid transparent;
      color: #cddfff;
      font-size: 14px;
      cursor: pointer;
      padding: 0;
    }
  }
  .detail_box {
    width: 110px;
    height: 30px;
    background: url("~@/assets/patrolKanban/<EMAIL>") no-repeat center;
    background-size: contain;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: 400;
    font-size: 12px;
    color: #3ccaff;
    cursor: pointer;
  }
}
</style>
<style lang="less">
.ant-modal-process {
  width: 991.5px !important;
  height: 667.5px !important;
  .title {
    height: 37px;
    background-image: url("~@/assets/department/dptitle.png") !important;
    background-repeat: no-repeat;
    font-size: 16px;
    font-family: PingFang SC;
    font-weight: 400;
    color: #dcf0ff;
    padding-left: 30px;
    padding-right: 10px;
    line-height: 37px;
    display: flex;
    justify-content: space-between;
  }
  .close {
    widows: 16px;
    height: 16px;
    cursor: pointer;
  }
  .content {
    overflow: hidden;
    width: 904px;
    height: 533px;
  }
  .ant-modal-content {
    overflow: hidden;
    height: 667.5px !important;
    position: relative;
    padding: 20px !important;
    background-color: transparent !important;
    background-image: url("~@/assets/department/dpbg.png") !important;
    background-size: 100% 100% !important;
  }
}
.picker_class {
  background: #052f61;
  border: none;
  color: #cddfff;
  .el-date-picker__header-label,
  .el-picker-panel__icon-btn,
  .el-month-table td .cell,
  .el-year-table td .cell {
    color: #cddfff;
  }
  .popper__arrow {
    border-top-color: #052f61 !important;
    &::after {
      border-top-color: #052f61 !important;
    }
  }
  .el-date-table th {
    border: none;
    color: #cddfff;
  }
  .el-date-picker__header--bordered {
    border: none;
  }
  .el-date-table td.disabled div,
  .el-month-table td.disabled .cell,
  .el-year-table td.disabled .cell {
    background-color: #000a38;
  }
}
</style>
