.time-enter-active,
.time-leave-active {
  transition: all 0.3s ease-out;
}

.time-enter {
  transform: translateY(1rem);
  opacity: 0;
}

.time-leave-to {
  transform: translateY(-1rem);
  opacity: 0;
}

.sign-enter-active,
.sign-leave-active {
  transition: all 0.3s ease-out;
}

.sign-enter {
  transform: translateY(1rem);
  opacity: 0;
}

.sign-leave-to {
  transform: translateY(-1rem);
  opacity: 0;
}


.shift-enter-active,
.shift-leave-active {
  transition: all 0.3s ease-out;
}

.shift-enter {
  transform: scale(0.8);
  opacity: 0;
}

.shift-leave-to {
  transform: scale(0.9);
  opacity: 0;
}

.swift-panel-enter-active,
.swift-panel-leave-active {
  transition: all 0.3s ease-out;
}

.swift-panel-enter {
  transform: scale(0);
  opacity: 0;
}

.swift-panel-leave-to {
  transform: scale(0);
  opacity: 0;
}