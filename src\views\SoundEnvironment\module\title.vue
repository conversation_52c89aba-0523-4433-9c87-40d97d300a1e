<template>
  <div class="titlebox">
    <div class="box-title"><slot name="title" /></div>
    <div class="box-right" v-if="isRight"><slot name="right" /></div>
  </div>
</template>
<script>
export default {
  name: 'cardBox',
  props: {
    isRight: {
      type: Boolean,
      default: false,
    },
  },
}
</script>

<style lang="less">
.titlebox {
  width: 354px;
  height: 42px;
  background-image: url('../../../assets/topTitleImg.png');
  background-size: 100% 10px;
  background-position: center bottom;
  background-repeat: no-repeat;
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
  align-items: start;
  .box-title {
    font-size: 20px;
    font-family: Source Han Sans CN;
    font-weight: 500;
    color: #ffffff;
    text-shadow: 0 0 5px blue, 0 0 5px blue;
  }
  .box-right {
    width: 42%;
    height: 100%;
    display: flex;
    justify-content: space-between;
    // align-items: center;
  }
}
</style>
