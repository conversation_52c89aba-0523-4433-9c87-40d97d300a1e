<template>
  <div :id="id" :style="{ height: height, width: width }" />
</template>

<script lang="ts">
import * as echarts from 'echarts'
import { Component, Prop, Watch } from "vue-property-decorator";
import { mixins } from "vue-class-component";
import ResizeMixin from "./mixins/resize";
interface AirData {
  bottomList: string[];
  dataList: string[];
  unit: string;
  name?: string;
  colorType?: string;
}
@Component({
  name: "LineChartDouble"
})
export default class extends mixins(ResizeMixin) {
  @Prop({ default: "chart" }) private id!: string;
  @Prop({ default: "200px" }) private width!: string;
  @Prop({ default: "200px" }) private height!: string;
  @Prop({ default: null }) private propData!: AirData;
  @Prop({ default: null }) private lineColor!: string;
  // private chart: any = null;
  private option: any = {};
  // @Watch("propData", { immediate: true, deep: true }) public onMsgChanged(
  //   newValue: AirData,
  //   oldValue: AirData
  // ) {
  //   /* eslint-disable @typescript-eslint/ban-ts-ignore */
  //   //@ts-ignore
  //   this.chart.clear();
  //   this.getOption();
  //   /* eslint-disable @typescript-eslint/ban-ts-ignore */
  //   //@ts-ignore
  //   this.chart.setOption(this.option as EChartOption<EChartOption>);
  // }
  mounted() {
    this.initChart();
  }
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.clear();
    this.chart.dispose();
    this.chart = null;
  }
  private initChart() {

    if(this.chart === null || this.chart === undefined) {
      this.chart = echarts.init(
        document.getElementById(this.id) as HTMLDivElement
      );
    }
    this.getOption();
    this.chart.setOption(this.option as EChartOption<EChartOption>);
  }
  private getOption(): void {
    this.option = {
      tooltip: {
        trigger: "axis",
        axisPointer: {
          lineStyle: {
            color: "#57617B"
          }
        }
      },
      grid: {
        top: "15%",
        left: "0",
        right: "10%",
        bottom: "15%",
        containLabel: true
      },
      xAxis: [
        {
          boundaryGap: false,
          type: "category",
          data: ["化学需氧", "六价铬", "PH", "挥发酚", "高猛酸盐", "悬浮物"],
          axisLine: {
            lineStyle: {
              color: "rgba(255,255,255,0.12)"
            }
          },
          axisLabel: {
            color: "#CCCCCC",
            textStyle: {
              fontSize: 14
            }
          }
        }
      ],
      yAxis: [
        {
          axisLabel: {
            formatter: "{value}",
            color: "#CCCCCC"
          },
          nameTextStyle: {
            color: "#CCCCCC",
            align: "right"
          },
          nameLocation: "end",
          name: "ug/m³",
          axisLine: {
            show: false
          },
          splitLine: {
            lineStyle: {
              color: "rgba(255,255,255,0.12)"
            }
          }
        }
      ],
      series: [
        {
          symbolSize: 8,
          type: "line",
          smooth: true,
          lineStyle: {
            normal: {
              width: 3,
              color: "#3158FF"
            }
          },
          areaStyle: {
            normal: {
              color: new echarts.graphic.LinearGradient(
                0,
                0,
                0,
                1,
                [
                  {
                    offset: 0,
                    color: "rgba(137, 189, 27, 0.3)"
                  },
                  {
                    offset: 0.8,
                    color: "rgba(137, 189, 27, 0)"
                  }
                ],
                false
              ),
              shadowColor: "rgba(0, 0, 0, 0.1)",
              shadowBlur: 10
            }
          },
          itemStyle: {
            normal: {
              color: "rgb(137,189,27)",
              borderWidth: 3,
              borderColor: "#3158FF"
            }
          },
          data: [96.3, 96.4, 97.5, 60, 50, 40]
        },
        {
          symbolSize: 8,
          type: "line",
          smooth: true,
          lineStyle: {
            normal: {
              width: 3,
              color: "#41FDFF"
            }
          },
          areaStyle: {
            normal: {
              color: new echarts.graphic.LinearGradient(
                0,
                0,
                0,
                1,
                [
                  {
                    offset: 0,
                    color: "rgba(0, 136, 212, 0.3)"
                  },
                  {
                    offset: 0.8,
                    color: "rgba(0, 136, 212, 0)"
                  }
                ],
                false
              ),
              shadowColor: "rgba(0, 0, 0, 0.1)",
              shadowBlur: 10
            }
          },
          itemStyle: {
            normal: {
              width: 3,
              color: "rgb(0,136,212)",
              borderWidth: 3,
              borderColor: "#41FDFF"
            }
          },
          data: [97.3, 99.2, 99.3, 100.0, 99.6, 90.6]
        }
      ]
    };
  }
}
</script>
