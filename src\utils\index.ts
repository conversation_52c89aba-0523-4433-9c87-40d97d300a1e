/**
 * @description 防抖动函数
 * @param {Function} func
 * @param {number} wait
 * @param {boolean} immediate
 * @return {*}
 */
 export function debounce(
  func: Function,
  wait: number,
  immediate: boolean
): Function {
  let timeout: NodeJS.Timeout | null,
    args: any,
    context: any | null,
    timestamp: number,
    result: Function;

  const later = function() {
    // 据上一次触发时间间隔
    const last = +new Date() - timestamp;

    // 上次被包装函数被调用时间间隔 last 小于设定时间间隔 wait
    if (last < wait && last > 0) {
      timeout = setTimeout(later, wait - last);
    } else {
      timeout = null;
      // 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用
      if (!immediate) {
        result = func.apply(context, args);
        if (!timeout) context = args = null;
      }
    }
  };

  return (...args: any[]) => {
    timestamp = +new Date();
    const callNow = immediate && !timeout;
    // 如果延时不存在，重新设定延时
    if (!timeout) timeout = setTimeout(later, wait);
    if (callNow) {
      result = func.apply(context, args);
      context = null;
      args = [];
    }

    return result;
  };
}

/**
 * @description 节流函数
 * @param {function} func
 * @param {number} delay
 */
export function throttle(func: Function, delay: number) {
  let timer: NodeJS.Timeout | null | number = null;
  // 闭包记录开始时间
  let startTime = Date.now();
  return (...args: any[]) => {
    const curTime = Date.now();
    const remaining = delay - (curTime - startTime);
    clearTimeout(timer as NodeJS.Timeout);
    // 到时执行一次则重置
    if (remaining <= 0) {
      func.apply(null, ...args);
      startTime = Date.now();
    } else {
      // 未到时则继续等待
      timer = setTimeout(func, remaining);
    }
  };
}

export function getNowTime() {
  const date = new Date();
  const year = date.getFullYear();
  let month: any = date.getMonth() + 1;
  let day: any = date.getDate();
  let hour: any = date.getHours();
  let minute: any = date.getMinutes();
  let second: any = date.getSeconds();
  if (month < 10) {
    month = "0" + month;
  }
  if (day < 10) {
    day = "0" + day;
  }
  if (hour < 10) {
    hour = "0" + hour;
  }
  if (minute < 10) {
    minute = "0" + minute;
  }
  if (second < 10) {
    second = "0" + second;
  }
  // return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
  return `${year}-${month}-${day} ${hour}:${minute}`;
}

export function conversionDate(date: any) {
  const year = date.getFullYear();
  let month: any = date.getMonth() + 1;
  let day: any = date.getDate();
  if (month < 10) {
    month = "0" + month;
  }
  if (day < 10) {
    day = "0" + day;
  }
  return `${year}-${month}-${day}`;
}
// 获取当前环境的 VUE_WS_API_1
export function socketUrl() {
  const baseUrl: any = process.env.VUE_APP_WS_API;
  console.log("baseUrl", process.env);
  // const baseUrl:any = "ws://192.168.0.87:23000/ws" // 本地
  // const baseUrl: any = "ws://ep.vankeytech.com:8835/ws"; // 测试
  // const baseUrl:any = "ws://www.jinnq.com:8834/ws" // 正式
  // const baseUrl:any = "wss://221.237.107.77:8834/ws" // 正式 http
  // const baseUrl:any = "wss://www.jinnq.com/ws" // 正式 https
  return baseUrl;
}
export function socketUrl1() {
  const baseUrl: any = process.env.VUE_APP_WS_API_1;
  // const baseUrl:any = "ws://192.168.0.87:23000/ws" // 本地
  // const baseUrl: any = "ws://ep.vankeytech.com:23001/ws"; // 测试
  // const baseUrl:any = "wss://www.jinnq.com:27712/ws" // 正式 http
  // const baseUrl:any = "wss://www.jinnq.com:27712/ws" // 正式https
  return baseUrl;
}
export function socketUrl2() {
  const baseUrl: any = process.env.VUE_APP_WS_API_2;
  // const baseUrl: any = "ws://192.168.0.30:19999/ws"; // 本地
  // const baseUrl: any = "ws://10.10.10.6:29999/ws"; // 测试(内网地址)
  // const baseUrl: any = "ws://111.9.60.160:29999/ws"; // 测试
  // const baseUrl:any = "wss://www.jinnq.com:9880/ws" // 正式 http
  // const baseUrl:any = "wss://www.jinnq.com:9880/ws" // 正式 https
  return baseUrl;
}
export function socketUrl3() {
  const baseUrl: any = process.env.VUE_APP_WS_API_3;
  // const baseUrl:any = "ws://192.168.0.30:23000/ws" // 本地
  // const baseUrl: any = "ws://ep.vankeytech.com:23001/ws"; // 测试
  // const baseUrl:any = "wss://www.jinnq.com:27712/ws" // 正式 http
  // const baseUrl:any = "wss://www.jinnq.com:27712/ws" // 正式 https
  return baseUrl;
}

/**
 * 指定时间执行任务
 * @param config
 * {interval: 1, //间隔天数，间隔为整数
    runNow: false, //是否立即运行
    time: "14:00:00" //执行的时间点 时在0~23之间
 * }
 * @param func 回调函数
 */
export function timeoutFunc(config: any, func: Function) {
  config.runNow && func();

  let nowTime = new Date().getTime();

  let timePoints: Array<any> = config.time
    .split(":")
    .map((i: any) => parseInt(i));

  // @ts-ignore
  let recent = new Date().setHours(...timePoints);

  recent >= nowTime || (recent += 24 * 3600000);

  setTimeout(() => {
    func();
    if (!config.interval) config.interval = 1;
    setInterval(func, config.interval * 3600000);
  }, recent - nowTime);
}

/**
 * 处理webgl上下文丢失事件
 * @param {String} domStr dom选择器参数 id 或者 class
 */
export function webglcontextlostHandle(domStr: string = ''): void {

  // @ts-ignore
  const canvas: any = document.querySelector(domStr || ".amap-layer");
  if(!canvas || !canvas.restoreContext) return;

  const handle = canvas.restoreContext;
  // @ts-ignore
  canvas && canvas.addEventListener("webglcontextlost",handle);

  // @ts-ignore
  this.$once('hook:beforeDestroy',() => {
    canvas.removeEventListener("webglcontextlost", handle)
  })
}
