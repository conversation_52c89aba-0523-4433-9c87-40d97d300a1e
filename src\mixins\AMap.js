import AMapLoader from '@amap/amap-jsapi-loader'

const AMapLoderMix = {
  created() {
    AMapLoader.reset()
    // // load 加载
    AMapLoader.load({
      "key": "777fec7ef3cc29281d60ae900fa33925",              // 申请好的Web端开发者Key，首次调用 load 时必填
      "version": "1.4.15",   // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
      "plugins": ['AMap.DistrictSearch','AMap.Heatmap','AMap.ControlBar','AMap.Object3DLayer','Map3D','AMap.Geocoder','AMap.CircleMarker','AMap.MouseTool'],           // 需要使用的的插件列表，如比例尺'AMap.Scale'等
      "AMapUI": {             // 是否加载 AMapUI，缺省不加载
        "version": '1.0',   // AMapUI 版本
        // "plugins":['overlay/SimpleMarker'],       // 需要加载的 AMapUI ui插件
      },
      "Loca":{                // 是否加载 Loca， 缺省不加载
        "version": '1.3.2'  // Loca 版本
      },
    }).then((AMap)=>{
      
    }).catch(e => {
      console.log(e);
    })
  }
}
export default AMapLoderMix
