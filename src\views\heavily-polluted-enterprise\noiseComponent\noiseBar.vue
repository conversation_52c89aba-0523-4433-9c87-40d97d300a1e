<style lang="less" scoped>
.no-data {
  font-size: 0.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
<template>
  <div
    :id="id"
    v-if="show"
    :style="{ height: height, width: width }"
  />
  <div v-else class="no-data" :style="{ height: height, width: width }">
    暂无数据
  </div>
</template>

<script lang="ts">
import * as echarts from 'echarts'
import { Component, Prop, Watch } from 'vue-property-decorator'
import { mixins } from 'vue-class-component'
import ResizeMixin from '@/components/Charts/mixins/resize'
interface AirData {
  bottomList: string[]
  dataList: string[]
  dataList1: string[]
  standard: number | string
  max: number | string
  unit: string
  name: string
}
@Component({
  name: 'LineChartDashed'
})
export default class extends mixins(ResizeMixin) {
  // @Prop({ default: "chart" }) private className!: string;
  @Prop({ default: 'chart' }) private id!: string
  @Prop({ default: '200px' }) private width!: string
  @Prop({ default: '200px' }) private height!: string
  @Prop({ required: true }) private propData!: AirData
  @Prop({ required: false, default: 'rgba(14,156,255,1)' })
  private bgColor!: string
  @Prop({ required: false, default: false }) private smooth!: boolean
  @Prop({ required: false, default: true }) private bgColorState!: boolean
  @Prop({ required: false, default: '' }) private xText!: string
  @Watch('propData', { immediate: true, deep: true }) public onMsgChanged(
    newValue: AirData,
    oldValue: AirData
  ) {
    if (this.propData) {
      if (this.chart) {
        this.chart.clear()
        this.chart.dispose()
        this.chart = null
      }
      this.$nextTick(() => {
        this.show = true
        this.initChart()
      })
    }
  }
  private show = false
  mounted() {
    if (this.propData) {
      this.initChart()
    }
  }
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.clear()
    this.chart.dispose()
    this.chart = null
  }

  private initChart() {
    if (this.chart === null || this.chart === undefined) {
      const chartDom = document.getElementById(this.id) as HTMLDivElement
      if (!chartDom) return
      this.chart = echarts.init(chartDom)
    }
    this.chart.setOption({
      backgroundColor: 'transparent',
      legend: {
        //图例组件，颜色和名字
        right: '10%',
        top: '2%',
        itemGap: 16,
        itemWidth: 12,
        itemHeight: 3,
        textStyle: {
          color: '#C5E8F4',
          fontSize: 12
        },
        selectedMode: true
      },
      grid: {
        height: '80%',
        left: '0%',
        right: '0%',
        top: '15%',
        bottom: '5%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        name: '',
        nameTextStyle: {
          color: '#fff',
          lineHeight: -30,
          align: 'center',
          verticalAlign: 'bottom'
        },
        data: this.propData.bottomList,
        axisLabel: {
          textStyle: {
            fontSize: 12,
            color: 'white'
          }
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        }
      },
      yAxis: {
        type: 'value',
        name: '',
        min: 0,
        max: 100,
        show: this.propData.dataList[0]['data']&&this.propData.dataList[0]['data'].length,
        nameTextStyle: {
          color: '#fff',
          shadowOffsetX: 50
          // align: "center"
        },
        axisLabel: {
          formatter: '{value} %',
          textStyle: {
            fontSize: 12,
            color: 'white'
          }
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        splitNumber: 4,
        maxInterval:25,
        splitLine: {
          lineStyle: {
            type: "dashed",
            color: 'RGBA(2, 39, 75, 1)'
          }
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
          label: {
            backgroundColor: '#6a7985'
          }
        },
        confine: true,
        backgroundColor: "rgba(11, 11, 11, .4)",
        borderColor: "rgba(11, 11, 11, .4)",
        formatter:(params)=>{
          const arr:any = params
          const nameL = arr.map((v:any)=>v.seriesName)
          const time:string = this.propData.bottomList[params[0].dataIndex]
          const year = new Date().getFullYear()
          const dayData = this.propData.dataList[0]['data'][params[0].dataIndex]
          const nightData = this.propData.dataList[1]['data'][params[0].dataIndex]
          const dayRate = dayData.rate
          const nightRate = nightData.rate
          const dayLd = dayData.dB
          const nightLd = nightData.dB
          // const dayTotal = dayData.total
          // const dayCount = dayData.count
          // const nightTotal = nightData.total
          // const nightCount = nightData.count
          const str = `
            <div style="border-radius: 0.06rem;padding: 0.1rem 0.13rem; background-color: rgba(11, 11, 11, .4);color: #fff;">
              <div style="font-size: 0.14rem;"><i class="el-icon-time"></i>  监测时间:  ${year}-${time}</div>
              <div style="font-size: 0.14rem;margin-top: 0.12rem;">
                <div>
                  <i class="el-icon-stopwatch"></i>  昼间达标率：<span style="color:${dayRate!=100?'#FF4D46':''}">${dayRate}</span> %
                  <span style="margin-left: 0.15rem;">Ld：<span style="color:${dayRate!=100?'#FF4D46':''}">${dayLd}</span> dB(A)</span>
                </div>
                <div style="margin-top: 0.12rem;">
                  <i class="el-icon-stopwatch"></i>  夜间达标率：<span style="color:${nightRate!=100?'#FF4D46':''}">${nightRate}</span> %
                  <span style="margin-left: 0.15rem;">Ln：<span style="color:${nightRate!=100?'#FF4D46':''}">${nightLd}</span> dB(A)</span>
                </div>
              </div>
            </div>
          `
          if(nameL.length==2){
            return str
          }else if(nameL.includes('昼间')){
            return `
              <div style="border-radius: 0.06rem;padding: 0.1rem 0.13rem; background-color: rgba(11, 11, 11, .4);color: #fff;">
                <div style="font-size: 0.14rem;"><i class="el-icon-time"></i>  监测时间:  ${year}-${time}</div>
                <div style="font-size: 0.14rem;margin-top: 0.12rem;">
                  <div>
                    <i class="el-icon-stopwatch"></i>  昼间达标率：<span style="color:${dayRate!=100?'#FF4D46':''}">${dayRate}</span> %
                    <span style="margin-left: 0.15rem;">Ld：<span style="color:${dayRate!=100?'#FF4D46':''}">${dayLd}</span> dB(A)</span>
                  </div>
                </div>
              </div>
            `
          }else if(nameL.includes('夜间')){
            return `
              <div style="border-radius: 0.06rem;padding: 0.1rem 0.13rem; background-color: rgba(11, 11, 11, .4);color: #fff;">
                <div style="font-size: 0.14rem;"><i class="el-icon-time"></i>  监测时间:  ${year}-${time}</div>
                <div style="font-size: 0.14rem;margin-top: 0.12rem;">
                  <div>
                    <i class="el-icon-stopwatch"></i>  夜间达标率：<span style="color:${nightRate!=100?'#FF4D46':''}">${nightRate}</span> %
                    <span style="margin-left: 0.15rem;">Ln：<span style="color:${nightRate!=100?'#FF4D46':''}">${nightLd}</span> dB(A)</span>
                  </div>
                </div>
              </div>
            `
          }else{
            return str
          }
        }
      },
      series: this.propData.dataList.map(item => {
        return {
          name: item['name'],
          type: 'bar',
          data: item['data']&&item['data'].map((v:any) => v.rate),
          barWidth: 10,
          barGap: 0.5, //柱间距离
          itemStyle: {
            normal: {
              show: true,
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: item['colors']?item['colors'][0]:'',
                },
                {
                  offset: 1,
                  color: item['colors']?item['colors'][1]:'',
                },
              ]),
              opacity: 0.8,
            },
          },
        }
    }),
    } as EChartOption<EChartOption>)
  }
}
</script>



