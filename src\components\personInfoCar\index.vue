<template>
  <div class="personInfoCarBox">
    <div class="personInfoCar">
      <div class="icon-box">
        <img src="@/assets/images/<EMAIL>" alt="" />
      </div>
      <div class="right-box">
        <div class="top">
          <div class="top-title">{{ dataObj.departmentName || '--' }}</div>
          <div class="click" @click="moreClick()" v-if="moreFlg">
            {{ isMore ? '收起' : '更多' }}
          </div>
        </div>
        <div class="bottom">
          <div class="block">
            <div class="block-text">总人数</div>
            <div class="block-num">
              {{
                dataObj.departmentExecutor
                  ? dataObj.departmentExecutor.length
                  : 0
              }}
            </div>
          </div>
          <div class="block">
            <div class="block-text">进行中</div>
            <div class="block-num" style="color: #db9c15">
              {{ onProcess.length }}
            </div>
          </div>
          <div class="block">
            <div class="block-text">已完成</div>
            <div class="block-num" style="color: #02e6cf">
              {{ done.length }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="list-box" :class="isMore ? '' : 'box-height0'">
      <peopleList :persiondata="dataObj.departmentExecutor || []"></peopleList>
    </div>
  </div>
</template>
<script>
  import peopleList from '@/components/peopleInfo/peopleList.vue'
export default {
  name: 'personInfoCar',
    props: {
      thind: {
        type: Number,
        default: 0,
      },
      cardata: {
        type: Object,
        default: () => ({}),
      },
      moreFlg:{
        type: Boolean,
        default: true
      }
    },
    components: {
      peopleList,
    },
    data() {
      return {
        isMore: false,
        dataObj: {},
        onProcess: [],
        done: [],
      }
    },
    watch: {
      cardata: {
        handler(nval, oval) {
          // console.log(oval, nval, '人员的数据改变22')
          if (nval) {
            this.dataObj = JSON.parse(JSON.stringify(nval))
            this.onProcess = nval.departmentExecutor.filter(
              (e) => e.completeStatus == 0,
            )
            this.done = nval.departmentExecutor.filter(
              (e) => e.completeStatus == 1,
            )
          } else {
            this.dataObj = {}
            this.onProcess = []
            this.done = []
          }
        },
        deep: true,
        immediate: true,
      },
    },
    methods: {
      moreClick() {
        this.isMore = !this.isMore
        // this.$emit('getclick', this.thind)
      },
    },
}
</script>

<style lang="less" scoped>
.personInfoCarBox {
  margin-bottom: 30px;
  &:last-child {
    margin-bottom: 0;
  }
}
.personInfoCar {
  display: flex;
  align-items: center;
  background-image: url('../../assets/images/<EMAIL>');
  background-repeat: no-repeat;
  background-position: bottom;
  background-size: 100%;
  padding-bottom: 15px;

  .icon-box {
    width: 120px;
    height: 120px;
    box-sizing: border-box;
    // padding: 12px;
    // background-color: aquamarine;
    // background-image: linear-gradient(to top,rgba(4,111,161,0.3),rgba(4,111,161,0.5),rgba(4,111,161,0.1));
    img {
      width: 100%;
      height: 100%;
      background-size: 93px 80px;
      background-repeat: no-repeat;
      background-position: center center;
      // background-image: linear-gradient(to top,rgba(4,111,161,0.1),rgba(4,111,161,0.5),rgba(4,111,161,0.01));
      animation: huxi 4s infinite;
      transition-timing-function: linear;
      opacity: 0.9;
    }
    @keyframes huxi {
      0% {
        opacity: 0.9;
      }
      50% {
        opacity: 0.7;
      }
      100% {
        opacity: 0.9;
      }
    }
  }
  .right-box {
    flex: 1;
    .top {
      padding: 5px 10px;
      background-image: url('../../assets/images/<EMAIL>');
      background-size: 100% 100%;
      font-size: 15px;
      display: flex;
      margin-bottom: 10px;
      .top-title {
        transform: skewX(5deg);
        flex: 1;
        text-align: center;
        font-weight: bolder;
        color: #fff;
      }
      .click {
        padding-left: 15px;
        font-size: 15px;
        color: #378ad1;
        cursor: pointer;
      }
    }
    .bottom {
      display: flex;
      flex-wrap: wrap;
      .block {
        flex: 1;
        text-align: center;
        overflow: hidden;
        .block-text {
          font-size: 14px;
          color: #7190ab;
        }
        .block-num {
          font-size: 20px;
          // font-weight: bold;
          color: #10c9ff;
          // padding-right: 15px;
        }
      }
    }
  }
}
.list-box {
  // height: 160px;
  overflow: hidden;
  transition-duration: 0.5s;
}
.box-height0 {
  height: 0;
}
</style>
