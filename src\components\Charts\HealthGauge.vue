<template>
  <div
    :id="id"
    :class="className"
    :style="{ height: height, width: width }"
  ></div>
</template>

<script lang="ts">
import * as echarts from 'echarts'
import { Component, Prop, Watch } from "vue-property-decorator";
import { mixins } from "vue-class-component";
import ResizeMixin from "./mixins/resize";
import { fontFamily } from "html2canvas/dist/types/css/property-descriptors/font-family";
interface TaskData {
  type: number;
  showNumber: number;
  name: string;
  number: number;
}
@Component({
  name: "DashboardChart",
})
export default class extends mixins(ResizeMixin) {
  @Prop({ default: "chart" }) private className!: string;
  @Prop({ default: "chart" }) private id!: string;
  @Prop({ default: "200px" }) private width!: string;
  @Prop({ default: "200px" }) private height!: string;
  // @Prop({ required: true }) private propData!: TaskData;

  @Watch("propData", { immediate: false, deep: true })
  public onMsgChanged(newValue: InData, oldValue: InData) {
    this.initChart();
  }

  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.clear();
    this.chart.dispose();
    this.chart = null;
  }
  mounted(): void {
    this.initChart();
  }

  private initChart() {
    if (this.chart === null || this.chart === undefined) {
      this.chart = echarts.init(
        document.getElementById(this.id) as HTMLDivElement
      );
    }
    const option = {
      title: {
        text: "AQHI综合指数",
        x: "center",
        y: "58%",
        textStyle: {
          color: "#D0FDFE",
          fontSize: 12,
        },
      },
      tooltip: {
        formatter: "{a} <br/>值 : {c}",
      },

      grid: {
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
      },
      series: [
        {
          name: "",
          type: "gauge",
          center: ["50%", "50%"],
          radius: "100%",
          min: 0, //最小刻度
          max: 100, //最大刻度
          startAngle: 222,
          endAngle: -42,
          axisLine: {
            lineStyle: {
              color: [
                [0.25, "#7DDBA5"],
                [0.5, "#4F82CD"],
                [0.75, "#E8B368"],
                [1, "#D13B42"],
              ],
              width: 10,
            },
          },
          axisLabel: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            show: true,
            length: 10,
            lineStyle: {
              width: 3,
              color: "#04264C",
            },
          },
          splitNumber: 20,
          detail: {
            show: false,
          },
          animationDuration: 4000,
        },
      ],
    };
    this.chart.setOption(option as EChartOption);
  }
}
</script>
