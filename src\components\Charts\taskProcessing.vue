<style lang="less" scoped>
.no-data {
  font-size: 0.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
<template>
  <div
    v-if="propData.dataList.length > 0"
    :id="id"
    :style="{ height: height, width: width }"
  />
  <div v-else class="no-data" :style="{ height: height, width: width }">
    暂无联动任务
  </div>
</template>

<script lang="ts">
import * as echarts from 'echarts'
import { Component, Prop, Watch } from "vue-property-decorator";
import { mixins } from "vue-class-component";
import ResizeMixin from "./mixins/resize";
interface AirData {
  bottomList: string[];
  dataList: string[];
  dataList1: string[];
  dataList2: string[];
}
@Component({
  name: "LineChartDashed",
})
export default class extends mixins(ResizeMixin) {
  // @Prop({ default: "chart" }) private className!: string;
  @Prop({ default: "chart" }) private id!: string;
  @Prop({ default: "200px" }) private width!: string;
  @Prop({ default: "200px" }) private height!: string;
  @Prop({ required: true }) private propData!: AirData;
  @Prop({ required: false, default: "rgba(14,156,255,1)" })
  private bgColor!: string;
  @Prop({ required: false, default: false }) private smooth!: boolean;
  @Prop({ required: false, default: true }) private bgColorState!: boolean;
  @Prop({ required: false, default: "" }) private xText!: string;
  @Watch("propData", { immediate: true, deep: true }) public onMsgChanged(
    newValue: AirData,
    oldValue: AirData
  ) {
    if (this.propData) {
      if (this.chart) {
        this.chart.clear();
        this.chart.dispose();
        this.chart = null;
      }
      this.$nextTick(() => {
        this.initChart();
      });
    }
  }
  mounted() {
    // if (this.propData) {
    //     this.initChart();
    // }
  }
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.clear();
    this.chart.dispose();
    this.chart = null;
  }

  private initChart() {
    if (this.chart === null || this.chart === undefined) {
      const chartDom = document.getElementById(this.id) as HTMLDivElement;
      if (!chartDom) return;
      this.chart = echarts.init(chartDom);
    }
    // SO2:1 NO2:1 O3:1 CO:1 PM10:1 PM:2.5
    this.chart.setOption({
      backgroundColor: "transparent",
      legend: {
        textStyle: {
          color: "#fff",
          fontSize: 14,
        },
        itemWidth: 15,
        itemHeight: 7,
        right: "5%",
        // textStyle: {
        //   color: '#7BB7ED',
        //   fontSize: 12
        // },
        selectedMode: false,
        // icon: 'none'
      },
      grid: {
        height: "80%",
        left: "5%",
        top: "20%",
        bottom: "5%",
        right: "5%",
        containLabel: true,
      },
      xAxis: {
        type: "category",
        name: "",
        nameTextStyle: {
          color: "#fff",
          lineHeight: -30,
          align: "center",
          verticalAlign: "bottom",
        },
        data: this.propData.bottomList,
        axisLabel: {
          textStyle: {
            fontSize: 12,
            color: "white",
          },
        },
        axisLine: {
          lineStyle: {
            color: "#0E92E4",
          },
        },
        axisTick: {
          show: false,
        },
      },
      yAxis: [
        {
          type: "value",
          name: "单位：件",
          minInterval:1,
          // min: 1,
          max: Math.max(...[...this.propData.dataList, ...this.propData.dataList1].map((item: any) => +item)) > 0 ? null : 100,
          nameTextStyle: {
            color: "#86C6FF",
          },
          axisLabel: {
            textStyle: {
              fontSize: 12,
              color: "#86C6FF",
            },
          },
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            lineStyle: {
              type: "dashed",
              color: "rgba(30,34,117, 0.3)",
            },
          },
        },
      ],
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "cross",
          label: {
            backgroundColor: "#6a7985",
          },
        },
        formatter: function (params: any) {
          var html = "";
          if (params.length > 0) {
            // that.xIndex = params[0].dataIndex;
            for (var int = 0; int < params.length; int++) {
              html +=
                params[int].seriesName +
                ":" +
                params[int].value +
                "<br>";
                // (int == 1
                //   ? params[int].value - params[int - 1].value
                //   : int == 2
                //   ? params[int].value + "%"
                //   : params[int].value) +
                // "<br>";
            }
          }
          return html;
        },
      },
      animation: true,
      animationDuration: 2000,
      series: [
        {
          // @ts-ignore
          name: "已完成",
          type: "bar",
          barWidth: "12px",
          itemStyle: {
            normal: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: "rgba(66, 168, 255, 1)",
                },
                {
                  offset: 1,
                  color: "rgba(28, 75, 124, 1)",
                },
              ]),
              barBorderRadius: [12,12,0,0],
            },
          },
          data: this.propData.dataList,
          z: 1,
        },
        {
          // @ts-ignore
          name: "未完成",
          type: "bar",
          barWidth: "12px",
          itemStyle: {
            normal: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: "rgba(89, 247, 202, 1)",
                },
                {
                  offset: 1,
                  color: "rgba(10, 107, 106, 1)",
                },
              ]),
              barBorderRadius: [12,12,0,0],
            },
          },
          data: this.propData.dataList1,
          z: 1,
        },
        {
          // @ts-ignore
          name: "已关闭",
          type: "bar",
          barWidth: "12px",
          itemStyle: {
            normal: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: "rgba(232, 176, 114, 1)",
                },
                {
                  offset: 1,
                  color: "rgba(232, 176, 114, 0.4)",
                },
              ]),
              barBorderRadius: [12,12,0,0],
            },
          },
          data: this.propData.dataList2,
          z: 1,
        },
      ],
    } as EChartOption<EChartOption>);
  }
}
</script>



