<template>
  <div class="period-time-select">
    <a-select
      class="title-select-period"
      @change="changePeriod"
      v-model="periodValue"
      style="width: 60px"
    >
      <a-icon
        slot="suffixIcon"
        type="caret-down"
        style="color: rgba(0, 234, 255, 1); width: 14px; height: 8px"
      />
      <a-select-option
        :value="item.value"
        v-for="item in periodList"
        :key="item.value"
      >
        {{ item.label }}</a-select-option
      >
    </a-select>
    <a-select
      style="width: 70px"
      v-if="[3, 4].includes(periodValue)"
      class="title-select-date"
      v-model="year"
      @change="getPeriodData"
    >
      <a-icon
        slot="suffixIcon"
        type="caret-down"
        style="color: rgba(0, 234, 255, 1); width: 14px; height: 8px"
      />
      <a-select-option
        :value="item.value"
        v-for="item in yearList"
        :key="item.value"
      >
        {{ item.label }}</a-select-option
      >
    </a-select>
    <a-select
      style="width: 60px"
      v-if="periodValue !== 0"
      class="title-select-date"
      v-model="date"
      @change="getPeriodData"
    >
      <a-icon
        slot="suffixIcon"
        type="caret-down"
        style="color: rgba(0, 234, 255, 1); width: 14px; height: 8px"
      />
      <a-select-option
        :value="item.value"
        v-for="item in dateList"
        :key="item.value"
      >
        {{ item.label }}</a-select-option
      >
    </a-select>
  </div>
</template>
<script>
import dayjs from "dayjs";

export default {
  name: "PeriodTimeSelect",
  props: {
    useRealTime: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    dateList() {
      return this.periodValue === 5
        ? this.yearList
        : this.periodValue === 4
        ? this.quarterList
        : this.monthList;
    },
    yearList() {
      const arr = [];
      const startYear = new Date().getFullYear();
      const endYear = new Date().getFullYear() - 3;
      for (let i = startYear; i >= endYear; i--) {
        arr.push({ label: i, value: i });
      }
      return arr;
    },
    monthList() {
      const arr = [];
      for (let i = 1; i <= 12; i++) {
        arr.push({ label: `${i}月`, value: i > 9 ? i : `0${i}` });
      }
      return arr;
    },
    startDate() {
      if (this.periodValue === 0) {
        return dayjs().format("YYYY-MM-DD HH:00:00");
      }
      if (this.periodValue === 5) {
        return `${this.date}-01-01`;
      }
      if (this.periodValue === 4) {
        return `${this.year}-${this.date.split(",")[0]}`;
      }
      if (this.periodValue === 3) {
        return `${this.year}-${this.date}-01`;
      }
    },
    endDate() {
      if (this.periodValue === 0) {
        return dayjs().format("YYYY-MM-DD HH:00:00");
      }
      if (this.periodValue === 5) {
        return `${this.date}-12-31`;
      }
      if (this.periodValue === 4) {
        return `${this.year}-${this.date.split(",")[1]}`;
      }
      if (this.periodValue === 3) {
        return `${this.year}-${this.date}-${new Date(
          this.year,
          this.date,
          0
        ).getDate()}`;
      }
    },
  },
  data() {
    return {
      periodValue: 3,
      periodList: [
        { label: "年度", value: 5 },
        { label: "季度", value: 4 },
        { label: "月度", value: 3 },
      ],
      date: "",
      year: "",
      quarterList: [
        { label: "一季度", value: "01-01,03-31" },
        { label: "二季度", value: "04-01,06-30" },
        { label: "三季度", value: "07-01,09-30" },
        { label: "四季度", value: "10-01,12-31" },
      ],
    };
  },
  created() {
    if (this.useRealTime) {
      this.periodList.unshift({ label: "实时", value: 0 });
      this.periodValue = 0;
    }
    this.date = this.dateList[0].value;
    this.year = this.yearList[0].value;
    this.getPeriodData();
  },
  methods: {
    changePeriod() {
      this.$nextTick(() => {
        this.date = this.dateList[0].value;
        if ([3, 4].includes(this.periodValue)) {
          this.year = this.yearList[0].value;
        }
        this.getPeriodData();
      });
    },
    getPeriodData() {
      this.$emit("getPeriodData", {
        startDate: this.startDate,
        endDate: this.endDate,
        periodValue: this.periodValue,
      });
    },
  },
};
</script>
<style lang="less" scoped>
.period-time-select {
  width: 160px;
  display: flex;
  justify-content: flex-end;
}
</style>
