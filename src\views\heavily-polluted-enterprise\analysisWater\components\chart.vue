<style lang="less" scoped>
.no-data {
  font-size: 0.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
<template>
  <div
    v-if="propData.thisStation.length > 0"
    :id="id"
    :style="{ height: height, width: width }"
  />
  <div v-else class="no-data" :style="{ height: height, width: width }">
    <template>
      <div
        style="
          width: 100%;
          height: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
        "
      >
        <div style="width: 100%; text-align: center">
          <img src="@/assets/<EMAIL>" alt="" />
          <p
            style="
              height: 15px;
              font-size: 14px;
              font-family: PingFang SC;
              font-weight: 500;
              color: #d8e8fe;
            "
          >
            暂无数据
          </p>
        </div>
      </div>
    </template>
  </div>
</template>

<script lang="ts">
import * as echarts from 'echarts'
import { Component, Prop, Watch } from "vue-property-decorator";
import { mixins } from "vue-class-component";
import ResizeMixin from "@/components/Charts/mixins/resize";
interface AirData {
  thisStation: any[];
  upStation: any[];
  downStation: any[];
  unit: string | null;
}
@Component({
  name: "analysisAirLineChartDashed",
})
export default class extends mixins(ResizeMixin) {
  // @Prop({ default: "chart" }) private className!: string;
  @Prop({ default: "chart" }) private id!: string;
  @Prop({ default: "200px" }) private width!: string;
  @Prop({ default: "200px" }) private height!: string;
  @Prop({ required: true }) private propData!: AirData;
  @Prop({ required: false, default: "rgba(14,156,255,1)" })
  private bgColor!: string;
  @Prop({ required: false, default: false }) private smooth!: boolean;
  @Prop({ required: false, default: true }) private bgColorState!: boolean;
  @Prop({ required: false, default: "" }) private xText!: string;
  @Watch("propData", { immediate: true, deep: true }) public onMsgChanged(
    newValue: AirData,
    oldValue: AirData
  ) {
    if (this.propData) {
      if (this.chart) {
        this.chart.clear();
        this.chart.dispose();
        this.chart = null;
      }
      this.$nextTick(() => {
        this.initChart();
      });
    }
  }
  mounted() {
    if (this.propData) {
      this.initChart();
    }
  }
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.clear();
    this.chart.dispose();
    this.chart = null;
  }
  private bottomList: string[] = [];
  private initChart() {
    // 当前站点
    let thisStation = {};
    if (this.propData.thisStation && this.propData.thisStation.length) {
      thisStation = {
        name: "当前站点",
        data: this.propData.thisStation.map((item: any) => item.concentration),
        type: "line",
        smooth: this.smooth,
        lineStyle: {
          color: "#15B4FE", //改变折线颜色
        },
        symbol: "circle",
        symbolSize: 5,
        areaStyle: {
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: this.bgColorState
                  ? "RGBA(21,180,254, 0.5)"
                  : "transparent", // 0% 处的颜色
              },
              {
                offset: 1,
                color: "RGBA(25,252,255, 0)", // 100% 处的颜色
              },
            ],
            global: false,
          },
          shadowColor: "rgba(0,85,250,0)",
          shadowBlur: 20,
        },
      };
    }
    // 上游站点
    let upStation = {};
    if (this.propData.upStation && this.propData.upStation.length) {
      upStation = {
        name: "上游站点",
        data: this.propData.upStation.map((item: any) => item.concentration),
        type: "line",
        smooth: this.smooth,
        lineStyle: {
          color: "#15B4FE", //改变折线颜色
        },
        symbol: "circle",
        symbolSize: 5,
        areaStyle: {
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: this.bgColorState
                  ? "RGBA(21,180,254, 0.5)"
                  : "transparent", // 0% 处的颜色
              },
              {
                offset: 1,
                color: "RGBA(25,252,255, 0)", // 100% 处的颜色
              },
            ],
            global: false,
          },
          shadowColor: "rgba(0,85,250,0)",
          shadowBlur: 20,
        },
      };
    }
    // 下游站点
    let downStation = {};
    if (this.propData.downStation && this.propData.downStation.length) {
      downStation = {
        name: "下游站点",
        data: this.propData.downStation.map((item: any) => item.concentration),
        type: "line",
        smooth: this.smooth,
        lineStyle: {
          color: "#15B4FE", //改变折线颜色
        },
        symbol: "circle",
        symbolSize: 5,
        areaStyle: {
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: this.bgColorState
                  ? "RGBA(21,180,254, 0.5)"
                  : "transparent", // 0% 处的颜色
              },
              {
                offset: 1,
                color: "RGBA(25,252,255, 0)", // 100% 处的颜色
              },
            ],
            global: false,
          },
          shadowColor: "rgba(0,85,250,0)",
          shadowBlur: 20,
        },
      };
    }
    if (this.propData.thisStation && this.propData.thisStation.length) {
      this.bottomList = this.propData.thisStation.map((it) =>
        it.monitorTime.substring(5, 16)
      );
    }
    if (this.propData.upStation && this.propData.upStation.length) {
      this.bottomList = this.propData.upStation.map((it) =>
        it.monitorTime.substring(5, 16)
      );
    }
    if (this.propData.downStation && this.propData.downStation.length) {
      this.bottomList = this.propData.downStation.map((it) =>
        it.monitorTime.substring(5, 16)
      );
    }
    let series = [];
    JSON.stringify(thisStation) !== "{}" && series.push(thisStation);
    JSON.stringify(upStation) !== "{}" && series.push(upStation);
    JSON.stringify(downStation) !== "{}" && series.push(downStation);

    if (this.chart === null || this.chart === undefined) {
      const chartDom = document.getElementById(this.id) as HTMLDivElement;
      if (!chartDom) return;
      this.chart = echarts.init(chartDom);
    }
    this.chart.setOption({
      backgroundColor: "transparent",
      color: ["#40A3F8", "#48EBE7", "#10C9FF"],
      legend: {
        right: 30,
        textStyle: {
          color: "#7BB7ED",
          fontSize: 12,
        },
        itemHeight: 12,
      },
      grid: {
        // height: '80%',
        left: "5%",
        top: "20%",
        bottom: "5%",
        containLabel: true,
      },
      xAxis: {
        type: "category",
        name: "",
        nameTextStyle: {
          color: "#fff",
          lineHeight: -30,
          align: "center",
          verticalAlign: "bottom",
        },
        data: this.bottomList,
        axisLabel: {
          textStyle: {
            fontSize: 12,
            color: "white",
          },
        },
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
      },
      yAxis: {
        type: "value",
        name: this.propData.unit,
        min: 0,
        nameTextStyle: {
          color: "#fff",
          shadowOffsetX: 50,
          // align: "center"
        },
        axisLabel: {
          textStyle: {
            fontSize: 12,
            color: "white",
          },
        },
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          lineStyle: {
            // type: "dashed"
            color: "RGBA(2, 39, 75, 1)",
          },
        },
      },
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "cross",
          label: {
            backgroundColor: "#6a7985",
          },
        },
      },
      series: series,
    } as EChartOption<EChartOption>);
  }
}
</script>



