function initScriptAMap() {
  return new Promise((resolve, reject) => {
    if (window.AMap) {
      resolve(window.AMap)
    } else {
      let url = 'https://webapi.amap.com/maps?v=1.4.15&key=777fec7ef3cc29281d60ae900fa33925&plugin=AMap.DistrictSearch&plugin=AMap.Heatmap&AMap.ControlBar&plugin=AMap.Object3DLayer&plugin=Map3D&plugin=AMap.Geocoder&plugin=AMap.CircleMarker&plugin=AMap.MouseTool'
      let script = document.createElement('script')
      script.charset = 'utf-8'
      script.src = url
      script.onerror = reject
      document.head.appendChild(script)
    }
    window.onLoad = () => {
      resolve(window.AMap)
    }
  })
}
function initScriptLoca() {
  let script = document.getElementById("gaodeMapLocaScript");
  script = document.createElement("script");
  script.id = "gaodeMapLocaScript";
  script.src =
    "//webapi.amap.com/loca?v=1.3.2&key=777fec7ef3cc29281d60ae900fa33925";
  document.body.append(script);
}
function initScriptMapUi() {
  let script = document.getElementById("gaodeMapUIScript");
  script = document.createElement("script");
  script.id = "gaodeMapUIScript";
  script.src = "https://webapi.amap.com/ui/1.0/main.js?v=1.0.11";
  document.body.append(script);
}
export { initScriptAMap, initScriptLoca, initScriptMapUi}
