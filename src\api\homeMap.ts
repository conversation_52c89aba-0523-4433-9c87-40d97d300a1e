import request from "@/utils/request";
import { AxiosPromise } from "axios";

/**
 * @method functionName
 * @param {type} data 说明
 * @description
 */
export function getHpeSum(): AxiosPromise<any> {
  return request({
    url: "/water/sewage/enterprise/getHpeSum",
    method: "get"
  });
}

/**
 * @method functionName
 * @param {type} data 获取空气报警站点数量 今天
 * @description
 */
export function countThisAirDay(): AxiosPromise<any> {
  return request({
    url: "/air/air-alarm/countThisDay",
    method: "get"
  });
}

/**
 * @method functionName
 * @param {type} data 获取水的 告警站点数量
 * @description
 */
export function countThisWaterDay(): AxiosPromise<any> {
  return request({
    url: "/water/alarm/countThisDay",
    method: "get"
  });
}
/**
 * @method functionName
 * @param {type} data 说明
 * @description
 */
export function functionName(data: any): AxiosPromise<any> {
  return request({
    url: "url",
    method: "post",
    data: data
  });
}
