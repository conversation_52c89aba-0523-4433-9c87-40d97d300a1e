import { emergencyStat } from '@/api/emergencyDetails/emergencyEvent'

import { ref, onMounted, onActivated } from 'vue'

/**
 * 获取应急任务分类统计
 * @returns emergencyEventData 获取到的数据 结构如下
 * {
    事故灾害: 0,
    自然灾害: 0,
    公共安全: 0,
    社区安全: 0,
  }
 */
export default function getEmergencyEventData() {
  // 应急事件统计数据
  const emergencyEventData = ref({
    事故灾害: 0,
    自然灾害: 0,
    公共安全: 0,
    社区安全: 0,
  })

  const emergencyEventDataTypeMap = {
    accident: '社区安全',
    community: '事故灾害',
    nature: '公共安全',
    publicSafety: '自然灾害',
  }

  /**
   * 获取应急任务分类统计
   */
  function getData() {
    emergencyStat()
      .then((res) => {
        if (!res) throw new Error('请求失败')

        const { code, data } = res
        if (code === 200) {
          for (const key in data) {
            if (Object.hasOwnProperty.call(data, key)) {
              const val = data[key]
              emergencyEventData.value[
                emergencyEventDataTypeMap[key]
              ] = val
            }
          }
        }
      })
      .catch((err) => {
        console.log('获取数据失败', err)
      })
      .finally(() => {
        console.log('获取数据请求完成---1')
      })
  }

  onMounted(() => {
    getData()
  })

  onActivated(() => {
    getData()
  })

  return {
    emergencyEventData,
  }
}
