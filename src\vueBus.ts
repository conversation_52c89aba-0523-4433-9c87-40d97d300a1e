const install = (Vue: any) => {
  const Bus = new Vue({
    methods: {
      emit(event: Event, ...args: any[]) {
        (this as any).$emit(event, args);
      },
      on(event: Event, callback: Function) {
        (this as any).$on(event, callback);
      },
      off(event: Event, callback: Function) {
        (this as any).$off(event, callback);
      }
    }
  });
  Vue.prototype.$bus = Bus;
};
export default install;
