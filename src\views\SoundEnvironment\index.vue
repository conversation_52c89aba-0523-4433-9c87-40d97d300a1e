<style lang="less" scoped>
::v-deep .box-title {
  width: 100%;
}
.home-main-one {
  width: 100%;
  position: relative;

  .left_contair {
    width: 380px;
    position: absolute;
    top: 30px;
    left: 46px;
    .marker_info {
      height: 280px;
      .table-data {
        width: 100%;
        height: 200px;
        .table-data-thead {
          .tr {
            display: flex;
            justify-content: space-between;
            .th {
              background: transparent !important;
              color: #48aeee;
              padding: 0;
              text-align: center;
              border: none;
              // line-height: 0.43rem;
              font-size: 0.15rem;
              // height: 0.43rem;
            }
            > :nth-of-type(1) {
              width: 19%;
            }
            > :nth-of-type(2) {
              width: 31%;
            }
            > :nth-of-type(3) {
              width: 14%;
            }
            > :nth-of-type(4) {
              width: 18%;
            }
            > :nth-of-type(5) {
              width: 18%;
            }
          }
        }
        .table-data-body {
          height: 180px;
          margin-top: 6px;
          .tr {
            width: 370px;
            height: 34px;
            background: rgba(13, 44, 84, 0.801);
            display: flex;
            align-items: center;
            justify-content: space-between;
            .th {
              font-size: 13px;
              font-family: PingFang SC;
              font-weight: 400;
              color: #ffffff;
              line-height: 34px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              text-align: center;
            }
            > :nth-of-type(1) {
              width: 19%;
            }
            > :nth-of-type(2) {
              width: 31%;
            }
            > :nth-of-type(3) {
              width: 13%;
            }
            > :nth-of-type(4) {
              width: 16%;
              font-family: YouSheBiaoTiHei;
              font-size: 16px;
            }
            > :nth-of-type(5) {
              width: 16%;
              font-family: YouSheBiaoTiHei;
              font-size: 16px;
            }
            .sup {
              color: red !important;
            }
          }
          .tr:nth-child(even) {
            background-color: rgba(0, 20, 55, 0.603);
          }
        }
        .rek_analyse_nodata {
          text-align: center;
          img {
            width: 260px;
            height: 130px;
          }
          .no_data_text {
            font-size: 14px;
            font-family: PingFang SC;
            font-weight: 500;
            color: #d8e8fe;
            line-height: 30px;
            text-shadow:
              0 0 5px rgb(63, 63, 221),
              0 0 5px rgb(13, 13, 218);
          }
        }
      }
    }

    .marker_info1 {
      height: 290px;
      margin-top: 20px;
    }
    .marker_info2 {
      height: 340px;
      margin-top: 20px;
      .tips {
        width: 100%;
        display: flex;
        justify-content: flex-end;
        > div {
          font-size: 15px;
          font-family: PingFang SC;
          font-weight: 400;
          color: #e6f0ff;
          margin-left: 40px;
          position: relative;
        }
        > div::after {
          display: block;
          content: '▬';
          position: absolute;
          left: -25px;
          top: 6px;
          width: 18px;
          height: 11px;
          border: 1px solid #1cdca6;
          font-weight: 300;
          font-size: 12px;
          text-align: center;
          color: #1cdca6;
          line-height: 8px;
          border-radius: 3px;
        }
        > div:nth-child(2):after {
          border: 1px solid #f3432c;
          color: #f3432c;
        }
      }
    }
    .date {
      margin-top: 10px;
      height: 1.9rem;
      box-sizing: border-box;
      .week {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-around;
        padding: 0 0.06rem;
        height: 33px;
        box-sizing: border-box;
        background-color: #053465;
        box-shadow: 0px 0px 10px 8px #0a4074 inset;
        span {
          font-size: 0.14rem;
          font-weight: 500;
          color: #ffffff;
          height: 0.33rem;
          box-sizing: border-box;
          text-align: center;
          line-height: 0.33rem;
        }
      }
      .days {
        width: 100%;
        background-color: rgba(4, 21, 51, 0.7);
        box-shadow: 0px 0px 16px 12px #0d3357 inset;
        padding: 10px 10px 10px 10px;
        > div {
          display: flex;
          align-items: center;
          // justify-content: space-around;
        }
        span {
          display: inline-block;
          // justify-content: space-around;
          font-size: 0.16rem;
          width: 0.52rem;
          height: 0.25rem;
          margin-bottom: 7px;
          box-sizing: border-box;
          text-align: center;
          line-height: 0.29rem;
          font-weight: 500;
          color: #ffffff;
        }
      }
    }
    .fengbu {
      display: flex;
      align-items: center;
      height: 230px;
      flex-wrap: wrap;
      .fengbu1 {
        width: 50%;
        display: flex;
        align-items: center;
        img {
          width: 62px;
          height: 62px;
          margin-right: 19px;
        }
        > div {
          div:nth-child(1) {
            font-size: 15px;
            font-family: Source Han Sans CN;
            font-weight: 400;
            color: #dcf0ff;
            margin-top: 10px;
          }
          div:nth-child(2) {
            font-size: 26px;
            font-family: YouSheBiaoTiHei;
            font-weight: 400;
          }
        }
      }
    }
  }
  .right_contair {
    width: 380px;
    position: absolute;
    top: 30px;
    right: 46px;
    .overall_evaluation {
      height: 245px;
      .evaluation {
        display: flex;
        justify-content: space-between;
        > img {
          width: 70px;
          height: 70px;
        }
        .right_info {
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          padding: 8px 0 10px 0;
          > div:nth-child(1) {
            font-size: 14px;
            font-family: PingFang SC;
            font-weight: 400;
            color: #87b3ca;
          }
          .info {
            width: 317px;
            height: 27px;
            background: rgba(16, 46, 80, 0.801);
            display: flex;
            justify-content: space-between;
            padding: 0 10px;
            .sup {
              color: red !important;
            }
            > div {
              font-size: 22px;
              font-family: YouSheBiaoTiHei;
              font-weight: 400;
              color: #ffffff;
              line-height: 27px;
              > span {
                font-size: 14px !important;
                font-family: PingFang SC;
                font-weight: 400;
                color: #87b3ca;
                line-height: 27px;
              }
            }
          }
        }
      }
      .jianxi {
        width: 100%;
        border: 1px solid #0b3759;
        margin: 8px 0;
      }
    }
    .Noise_class {
      // height: 298px;
    }
    .statisticals {
      width: 100%;
      margin-top: 30px;
      // height: 298px;
    }
  }
  .left-entry {
    animation: left-entry 1s linear;
  }
  .right-entry {
    animation: right-entry 1s linear;
  }

  @keyframes left-entry {
    0% {
      transform: translate(-100%, 0);
      opacity: 0;
    }
    66% {
      transform: translate(-100%, 0);
      opacity: 0;
    }
    100% {
      transform: translate(0, 0);
      opacity: 1;
    }
  }
  @keyframes right-entry {
    0% {
      transform: translate(100%, 0);
      opacity: 0;
    }
    66% {
      transform: translate(100%, 0);
      opacity: 0;
    }
    100% {
      transform: translate(0, 0);
      opacity: 1;
    }
  }
  @keyframes bottom-entry {
    0% {
      transform: translate(0, 100%);
      opacity: 0;
    }
    66% {
      transform: translate(0, 100%);
      opacity: 0;
    }
    100% {
      transform: translate(0, 0);
      opacity: 1;
    }
  }
  .RHSCC {
    position: absolute;
    right: 460px;
    bottom: 40px;
    // width: 74px;
    height: 210px;
    background: rgba(1, 10, 41, 0.5);
    // display: flex;
    padding: 20px 10px;
    animation: bottom-entry 1s linear;
    border: 1px solid #1a77aa;
    .tuli {
      width: 100%;
      position: absolute;
      left: 0;
      top: 0;
      font-size: 11px;
      font-family: PingFang SC;
      font-weight: 500;
      color: #50a9ff;
      text-align: center;
      padding-top: 4px;
    }
    .RHSCC1 {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      font-size: 12px;
      font-family: PingFang SC;
      font-weight: 500;
      color: #ffffff;
      height: 200px;
      text-align: center;
      flex: 1;
      > span {
        position: relative;
        height: 20%;
        line-height: 40px;
      }
      // > span::after {
      //   content: '';
      //   display: block;
      //   position: absolute;
      //   width: 10px;
      //   height: 22px;
      //   right: -10px;
      //   top: 8px;
      //   border-top: 1px solid #fff;
      // }
      // > span:nth-child(1)::after {
      //   position: absolute;
      //   top: 0;
      //   right: -10px;
      // }
      // > span:nth-child(2)::after {
      //   position: absolute;
      //   top: 2px;
      //   right: -10px;
      // }
      // > span:nth-child(3)::after {
      //   position: absolute;
      //   top: 3px;
      //   right: -10px;
      // }
      // > span:nth-child(6)::after {
      //   position: absolute;
      //   top: 12px !important;
      //   right: -10px;
      // }
    }
    .RHSCC2 {
      width: 23px;
      height: 200px;
      background: linear-gradient(
        0deg,
        #feff99 0%,
        #feff99 20%,
        #cdffcc 20%,
        #cdffcc 40%,
        #3366ff 40%,
        #3366ff 60%,
        #993401 60%,
        #993401 80%,
        #fe0101 80%,
        #fe0101 100%,
        #81017f 100%
      );
    }
    img {
      position: absolute;
      bottom: -20px;
      right: 0;
      width: 230px;
      height: 250px;
    }
    .title {
      width: 200px;
      height: 30px;
      line-height: 30px;
      background: rgba(11, 52, 114, 0.5);
      color: #329afa;
      font-size: 13px;
      font-weight: bold;
      display: flex;
      div {
        &:nth-child(1) {
          width: 50%;
          text-align: center;
        }
        &:nth-child(2) {
          width: 25%;
          text-align: center;
        }
        &:nth-child(3) {
          width: 25%;
          text-align: center;
        }
      }
    }
    .content {
      width: 200px;
      .content_item {
        width: 100%;
        height: 30px;
        line-height: 30px;
        display: flex;
        font-size: 13px;
        color: rgb(207, 207, 207);
        .name {
          display: flex;
          width: 50%;
          padding-left: 10px;
          justify-content: flex-start;
          align-items: center;
          .color {
            width: 15px;
            height: 10px;
            margin-right: 7px;
          }
        }
        .number {
          width: 25%;
          text-align: center;
        }
      }
    }
  }
  .right-select-type {
    pointer-events: auto;
    position: absolute;
    z-index: 2;
    bottom: 0.2rem;
    right: 7.2rem;
    cursor: pointer;
    animation: bottom-entry 1s linear;

    .list {
      display: flex;
      align-items: center;
      margin-bottom: 0.2rem;
      div:nth-of-type(1) {
        margin: 0 0.1rem;
      }
    }
  }
  .DandN {
    position: absolute;
    top: 40px;
    right: 470px;
    width: 190px;
    height: 42px;
    background: #00162b;
    border: 2px solid #194fa8;
    border-radius: 21px;
    box-shadow: 0 0 6px 3px #4073c47a;
    display: flex;
    .sun {
      width: 50%;
      height: 100%;
      border-radius: 95px;
      > img {
        display: block;
        width: 33px;
        height: 33px;
        margin: 0 auto;
        margin-top: 3px;
      }
    }
    .moon {
      background-color: #2599f5;
    }
  }

  .type-choose {
    width: 0.7rem;
    height: 0.28rem;
    line-height: 0.3rem;
    color: #0e8bff;
    // background: rgba(14, 139, 255, 0.32);
    // border: 1px solid rgba(14, 139, 255, 1);
    text-align: center;
    // clip-path: polygon(100% 0, 100% 100%, 12% 100%, 0% 70%, 0 0);
    background: url(../../assets/<EMAIL>);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    cursor: pointer;
  }
  .type-active {
    background: url(../../assets/<EMAIL>);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    color: rgba(0, 234, 255, 1);
  }
}
.thisyear {
  font-size: 15px;
  font-family: Source Han Sans CN;
  font-weight: 500;
  color: #00eaff;
  text-align: right;
  width: 100%;
  line-height: 40px;
}
</style>
<style lang="less"></style>
<template>
  <section class="home-main-one depMap_bg">
    <!-- 地图 -->
    <department-bg-map :mapZoom="12.5" :marKertype="marKertype" @clickMarker="clickMarker"></department-bg-map>
    <!-- 地图 -->

    <!-- 左侧列表 -->
    <!-- 按功能区 -->
    <div v-if="changeType === '按功能区'" key="按功能区" class="left_contair">
      <!-- 功能区监测点位信息 -->
      <div class="marker_info left-entry">
        <topTitle :isRight="true">
          <template v-slot:title>功能区监测点位信息</template>
          <template v-slot:right>
            <div class="type-choose" @click="changeType = '按站点'">按站点</div>
            <div class="type-choose type-active">按功能区</div>
          </template>
        </topTitle>
        <!-- 轮播表 -->
        <div class="table-data">
          <div class="table-data-thead">
            <div class="tr">
              <div class="th">街道</div>
              <div class="th">站点名称</div>
              <div class="th">功能区</div>
              <div class="th">昼间(dB(A))</div>
              <div class="th">夜间(dB(A))</div>
            </div>
          </div>
          <swiper v-if="tableList.length" class="table-data-body" :options="swiperOptionWarn">
            <swiper-slide class="tr" v-for="(item, index) in tableList" :key="index">
              <div class="th">{{ item.streetName || '' }}</div>
              <div class="th">{{ item.stationName || '' }}</div>
              <div class="th">{{ item.functionTypeName || '' }}</div>
              <div class="th" :class="{ sup: item.isLdAlarm }">
                {{ item.ld || '' }}
              </div>
              <div class="th" :class="{ sup: item.isLnAlarm }">
                {{ item.ln || '' }}
              </div>
            </swiper-slide>
          </swiper>

          <div v-else class="table-data-body rek_analyse_nodata">
            <img src="../../assets/recheck/<EMAIL>" alt="" />
            <div class="no_data_text">暂无数据</div>
          </div>
        </div>
      </div>
      <!-- 功能区监测点位信息 -->

      <!-- 噪声分布统计 -->
      <div class="marker_info1 right-entry">
        <topTitle :isRight="true">
          <template v-slot:title>噪声分布统计</template>
          <template v-slot:right>
            <div class="thisyear">{{ butionCount.monitorDate }}</div>
          </template>
        </topTitle>

        <!-- echarts -->
        <distributionEcharts :butionCount="butionCount" />
      </div>
      <!-- 噪声分布统计 -->

      <!-- 功能区布设统计 -->
      <div class="left-entry">
        <topTitle>
          <template v-slot:title>功能区布设统计</template>
        </topTitle>
        <!-- 气泡球 -->
        <Drum :stationTypeCount="stationTypeCount" />
      </div>
      <!-- 功能区布设统计 -->
    </div>
    <!-- 按站点 -->
    <div v-if="changeType === '按站点'" key="按站点" class="left_contair">
      <!-- 噪声监测趋势 -->
      <div class="marker_info left-entry">
        <topTitle :isRight="true">
          <template v-slot:title>噪声监测趋势</template>
          <template v-slot:right>
            <div class="type-choose type-active">按站点</div>
            <div class="type-choose" @click="changeType = '按功能区'">按功能区</div>
          </template>
        </topTitle>
        <!-- 图表 -->
        <Trend v-if="AnalyzeNew.x && AnalyzeNew.x.length > 0" :AnalyzeNew="AnalyzeNew" :AnalyzeNewLast="AnalyzeNewLast" :isDay="isDay" />
        <div v-else style="text-align: center; line-height: 2.4rem; width: 100%">暂无数据</div>
      </div>
      <!-- 噪声监测趋势 -->

      <!-- 本月监测日历 -->
      <div class="marker_info2 right-entry">
        <topTitle>
          <template v-slot:title>本月监测日历</template>
        </topTitle>
        <!-- 图例 -->
        <div class="tips">
          <div>正常</div>
          <div>超标</div>
        </div>
        <!-- 日历 -->
        <div class="date">
          <div class="week">
            <span>日</span>
            <span>一</span>
            <span>二</span>
            <span>三</span>
            <span>四</span>
            <span>五</span>
            <span>六</span>
          </div>
          <div class="days">
            <div v-for="(item, index) in dateDataFilter" :key="index">
              <div v-for="(items, indexs) in item" :key="indexs">
                <a-tooltip
                  v-if="items.level === 2"
                  :title="`超标值: ${isDay ? items.ld : items.ln}`"
                  :style="{
                    color: items.curr ? '#FFFFFF' : '#767D96',
                    backgroundColor: items.level === 1 ? ' rgba(89,247,202,0.5)' : items.level === 2 ? '#7D3633' : '',
                  }"
                  style="cursor: pointer"
                >
                  <span>{{ items.date }}</span>
                </a-tooltip>
                <span
                  v-else
                  :style="{
                    color: items.curr ? '#FFFFFF' : '#767D96',
                    backgroundColor: items.level === 1 ? ' rgba(89,247,202,0.5)' : items.level === 2 ? '#7D3633' : '',
                  }"
                  style="cursor: pointer"
                >
                  <span>{{ items.date }}</span>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 本月监测日历 -->

      <!-- 功能区布设统计 -->
      <div class="left-entry">
        <topTitle>
          <template v-slot:title>噪声来源情况</template>
        </topTitle>
        <!-- 气泡球 -->
        <div class="fengbu">
          <div class="fengbu1" v-for="(item, index) in stationPointList" :key="index">
            <img :src="item.url" :alt="item.typneName" />
            <div>
              <div>{{ item.typneName }}</div>
              <div :style="{ color: item.color }">{{ item.total }}</div>
            </div>
          </div>
        </div>
      </div>
      <!-- 功能区布设统计 -->
    </div>
    <!-- 左侧列表 -->

    <!-- 右边列表 -->
    <div class="right_contair">
      <!-- 城市区域环境噪声总体水平等级 -->
      <div class="left-entry Noise_class">
        <topTitle :isRight="true">
          <template v-slot:title>功能区单站点达标率</template>
        </topTitle>
        <!-- 可视化图表 -->
        <grid3DUp v-if="noiseDataComputedUp.length > 0" :chartData="noiseDataComputedUp" />
        <div v-else style="text-align: center; line-height: 2.4rem; width: 100%">暂无数据</div>
      </div>
      <!-- 总体评价 -->

      <!-- 噪声分级占比 -->
      <div class="left-entry Noise_class">
        <topTitle :isRight="true">
          <template v-slot:title>功能区达标率统计</template>
        </topTitle>
        <!-- 可视化图表 -->
        <grid3D v-if="noiseDataComputed.length > 0" :chartData="noiseDataComputed" />
        <div v-else style="text-align: center; line-height: 2.4rem; width: 100%">暂无数据</div>
      </div>
      <!-- 噪声分级占比 -->

      <!-- 功能区站点达标率统计 -->
      <div class="statisticals right-entry">
        <topTitle>
          <template v-slot:title>
            <div style="display: flex; justify-content: space-between; width: 100%; align-items: center">
              <span>功能区国控站点达标率</span>
              <span style="font-size: 14px; color: #a6cbe2">截止{{ thisYear }}</span>
            </div>
          </template>
        </topTitle>
        <!-- 可视化图表 -->
        <statEachrtsNew v-if="statisticsData.thisYearRate" :statisticsData="statisticsData" />
        <div v-else style="text-align: center; line-height: 2.4rem; width: 100%">暂无数据</div>
      </div>
      <!-- 功能区站点达标率统计 -->
    </div>
    <!-- 右边列表 -->

    <!-- 比色卡 -->
    <div class="RHSCC">
      <!-- <div class="tuli">功能区图例</div>
      <div class="RHSCC1">
        <span>4b类</span>
        <span>4a类</span>
        <span>3类</span>
        <span>2类</span>
        <span>1类</span>
      </div>
      <div class="RHSCC2"></div> -->
      <!-- <img src="@/assets/<EMAIL>" alt=""> -->
      <div class="title">
        <div>功能区图例</div>
        <div>昼间</div>
        <div>夜间</div>
      </div>
      <div class="content">
        <div class="content_item">
          <div class="name">
            <div class="color" style="background: rgb(219, 236, 171)"></div>
            <div class="text">1类功能区</div>
          </div>
          <div class="number">55</div>
          <div class="number">45</div>
        </div>
        <div class="content_item">
          <div class="name">
            <div class="color" style="background: rgb(39, 127, 222)"></div>
            <div class="text">2类功能区</div>
          </div>
          <div class="number">60</div>
          <div class="number">50</div>
        </div>
        <div class="content_item">
          <div class="name">
            <div class="color" style="background: rgb(115, 44, 32)"></div>
            <div class="text">3类功能区</div>
          </div>
          <div class="number">65</div>
          <div class="number">55</div>
        </div>
        <div class="content_item">
          <div class="name">
            <div class="color" style="background: rgb(218, 5, 25)"></div>
            <div class="text">4a类功能区</div>
          </div>
          <div class="number">70</div>
          <div class="number">55</div>
        </div>
        <div class="content_item">
          <div class="name">
            <div class="color" style="background: rgb(88, 11, 98)"></div>
            <div class="text">4b类功能区</div>
          </div>
          <div class="number">70</div>
          <div class="number">60</div>
        </div>
      </div>
    </div>
    <!-- 比色卡 -->

    <!-- 赛选 -->
    <section class="right-select-type">
      <div class="list" @click="Choose(stationType[0].id, 1)">
        <img src="@/assets/heavily/<EMAIL>" alt="" class="gou" v-if="!isShowWater" />
        <img src="@/assets/gou_active.png" alt="" class="gou" v-if="isShowWater" />
        <div>功能区</div>
        <div>{{ stationType ? stationType[0].typeCount : 0 }}个</div>
        <img style="margin-left: 10px" :src="gnqbs" alt="" />
      </div>
      <div class="list" @click="Choose(stationType[1].id, 2)">
        <img src="@/assets/heavily/<EMAIL>" alt="" class="gou" v-if="!isShowDrain" />
        <img src="@/assets/gou_active.png" alt="" class="gou" v-if="isShowDrain" />
        <div>微 站</div>
        <div>{{ stationType ? stationType[1].typeCount : 0 }}个</div>
        <img style="margin-left: 21px" :src="wzbs" alt="" />
      </div>
    </section>

    <!-- 昼夜切换按钮 -->
    <div class="DandN" @click="changeDay">
      <div class="sun" :class="{ moon: isDay }">
        <img :src="zjImg" alt="" />
      </div>
      <div class="sun" :class="{ moon: !isDay }">
        <img :src="yjImg" alt="" />
      </div>
    </div>
    <!-- 昼夜切换按钮 -->
  </section>
</template>

<script lang="ts">
interface EchartData {
  bottomList: string[]
  dataList: string[]
  unit?: string | number
  colorType?: string
}
interface InData {
  name: string
  dataList: any[]
  colorList: string[]
}
import moment from 'moment'
import { Component, Vue, Watch } from 'vue-property-decorator'
import { Swiper, SwiperSlide } from 'vue-awesome-swiper'
import DepartmentBgMap from '@/components/GaoDeMap/soundMap.vue'
import topTitle from './module/title.vue'
import grid3D from './module/grid3DCopy.vue'
import grid3DUp from './module/grid3DCopyUp.vue'
import statEachrts from './module/statEachrts.vue'
import statEachrtsNew from './module/statEachrtsNew.vue'
import distributionEcharts from './module/distributionEcharts.vue'
import Drum from './module/Drum.vue'
import Trend from './module/trend.vue'
import {
  getOne,
  listFunction,
  noiseDistributionCount,
  bigDataDaysAnalyzeNew,
  calendar,
  noiseLevelCountNew,
  complianceRateByStation,
  complianceRateByNationalStation,
} from '@/api/sound-mnviront'
import dayjs from 'dayjs'

let vm: any = null

@Component({
  name: 'swiperHome',
  components: {
    topTitle,
    grid3D,
    grid3DUp,
    statEachrts,
    statEachrtsNew,
    distributionEcharts,
    Drum,
    Trend,
    Swiper,
    SwiperSlide,
    DepartmentBgMap,
  },
})
export default class extends Vue {
  private showViewer: any = false

  @Watch('curMainIndex', { immediate: true, deep: true }) public onMsgChanged(newValue: number, oldValue: number) {}

  swiperOptionWarn = {
    direction: 'vertical',
    slidesPerView: 5,
    slidesPerGroup: 1,
    loop: true,
    autoplay: {
      delay: 5000,
      disableOnInteraction: false,
    },
  }

  zjImg: string = require('@/assets/<EMAIL>')
  yjImg: string = require('@/assets/<EMAIL>')
  isDay: boolean = true
  changeType: string = '按站点'
  mounted() {
    this.getNoise()
    this.getDistribution()
    this.getOneP()
    this.getCompType()
    this.getList()
    this.output(new Date().getFullYear() + '-' + (new Date().getMonth() + 1))
  }

  thisYear = dayjs().format('YYYY-MM-DD')

  statistics: any = {}
  dateData: any = {}
  deviceId: any = ''
  date: string = ''
  isShowWater: boolean = true
  isShowDrain: boolean = true
  cercle1: string = require('@/assets/<EMAIL>')
  cercle2: string = require('@/assets/<EMAIL>')
  cercle3: string = require('@/assets/<EMAIL>')
  cercle4: string = require('@/assets/<EMAIL>')
  cercle11: string = require('@/assets/<EMAIL>')
  cercle22: string = require('@/assets/<EMAIL>')
  cercle33: string = require('@/assets/<EMAIL>')
  cercle44: string = require('@/assets/<EMAIL>')
  wzbs: string = require('@/assets/noise/<EMAIL>')
  gnqbs: string = require('@/assets/noise/<EMAIL>')
  get dateDataFilter() {
    const dateArr: any = []

    if (!this.dateData || this.dateData.length === 0 || !this.dateData.filter) return dateArr

    for (let index: any = 0; index < 6; index++) {
      // @ts-ignore
      dateArr.push(this.dateData.filter((e: any, i: any) => parseInt((i / 7).toString()) === index))
    }
    return dateArr
  }

  // 上个月要显示的天数
  // 求出本月第一天是星期几
  // 求出上个月最大的天数 把日期设为0
  private getPrevDays(dates: any) {
    var date = new Date(dates)
    // 把日期设为第一天，为了获取第一天是星期几
    date.setDate(1)
    var week = date.getDay()
    // 把日期设为0，为了得到上个月的最后一天
    date.setDate(0)
    var maxDay = date.getDate()
    var list = []
    // 遍历红色日期的范围 push进数组
    for (var i = maxDay - week + 1; i <= maxDay; i++) {
      list.push(i)
    }
    return list
  }

  // 求本月的天数
  // 月份推到下个月
  // 日期设为0
  private getNowDays(dates: any) {
    var date = new Date(dates)
    date.setMonth(date.getMonth() + 1)
    date.setDate(0)
    var maxDay = date.getDate()
    var list = []
    //
    for (var i = 1; i <= maxDay; i++) {
      list.push(i)
    }
    return list
  }

  // 下个月要显示的天数
  private getNextDays(prevDays: any, nowDays: any) {
    var list = []
    // 一页日历42天，42 - 上月天数 - 这个月天数 = 最后显示剩余的下个月天数
    for (var i = 1; i <= 35 - prevDays - nowDays; i++) {
      list.push(i)
    }
    return list
  }
  // 生成日历
  private output(x: any) {
    let arr1 = this.getPrevDays(x).map((item: any) => {
      return {
        date: item,
        curr: 0,
      }
    })
    let arr2 = this.getNowDays(x).map((item: any) => {
      return {
        date: item,
        curr: 1,
      }
    })
    let arr3 = this.getNextDays(arr1.length, arr2.length).map((item: any) => {
      return {
        date: item,
        curr: 0,
      }
    })

    this.dateData = arr1.concat(arr2).concat(arr3)
  }

  // 计算属性：根据isDay返回相应的噪声数据
  get statisticsData() {
    if (!this.statistics?.ld && !this.statistics?.ln) {
      return []
    }
    return this.isDay ? this.statistics.ld || {} : this.statistics.ln || {}
  }

  // 功能区国控站点达标率
  getCompType() {
    complianceRateByNationalStation().then((res) => {
      this.statistics = res.data.data
    })
  }

  evaluate: any = {}
  /* 噪声总体评价 */
  getOneP() {
    getOne().then((res) => {
      this.evaluate = res.data.data
    })
  }
  // noiseData: any = {}
  tableList: any = []
  stationTypeCount: object = {}
  stationPointList: any = []
  butionCount: object = {}
  AnalyzeNew: any = {}
  AnalyzeNewLast: any = {}
  AnalyzeNewDatea: any = {}
  stationType: any = []
  marKertype: any = ''
  noiseDataAll: any = {} // 存储完整的噪声数据
  noiseDataAllUp: any = {} // 存储完整的噪声数据

  // 计算属性：根据isDay返回相应的噪声数据
  get noiseDataComputed() {
    if (!this.noiseDataAll.ld && !this.noiseDataAll.ln) {
      return []
    }
    return this.isDay ? this.noiseDataAll.ld || [] : this.noiseDataAll.ln || []
  }

  get noiseDataComputedUp() {
    if (!this.noiseDataAllUp.ld && !this.noiseDataAllUp.ln) {
      return []
    }
    return this.isDay ? this.noiseDataAllUp.ld || [] : this.noiseDataAllUp.ln || []
  }

  /* 噪声分级占比 */
  getNoise() {
    noiseLevelCountNew().then((res) => {
      this.noiseDataAll = res.data.data || {} // 存储完整数据
    })
    // 功能区单站点达标率
    complianceRateByStation().then((res) => {
      this.noiseDataAllUp = res.data.data || {}
    })
  }
  /* 功能区点位信息 */
  getList() {
    listFunction().then((res) => {
      this.tableList = res.data.data.stationList
      this.stationType = res.data.data.stationType
      this.stationTypeCount = res.data.data.stationTypeCount
      this.stationPointList = res.data.data.stationPointList.map((v: any) => {
        switch (v.typneName) {
          case '交通噪声源':
            v.url = this.cercle1
            v.color = '#2ddcff'
            break
          case '工业噪声源':
            v.url = this.cercle2
            v.color = '#70f1ce'
            break
          case '生活噪声源':
            v.url = this.cercle3
            v.color = '#dfc17a'
            break
          case '其他声源':
            v.url = this.cercle4
            v.color = '#3aa0f6'
            break
          default:
            break
        }
        return v
      })
    })
  }

  //赛选
  Choose(id: any, type: any) {
    if (type === 1) {
      this.isShowWater = !this.isShowWater
    } else {
      this.isShowDrain = !this.isShowDrain
    }
    if (this.isShowDrain && this.isShowWater) {
      this.marKertype = ''
    } else if (!this.isShowDrain && !this.isShowWater) {
      this.marKertype = -1
    } else {
      this.marKertype = this.isShowWater ? this.stationType[0].id : this.stationType[1].id
    }
  }

  /* 噪声分布统计 */
  getDistribution() {
    noiseDistributionCount().then((res) => {
      // this.butionCount = res.data.data
      // this.$forceUpdate()
      this.$set(this, 'butionCount', res.data.data)
    })
  }
  /* 天监测趋势 */
  getAnalyzeNew(stationId: any) {
    bigDataDaysAnalyzeNew({ stationId: stationId }).then((res) => {
      this.AnalyzeNewDatea = res.data.data
      if (this.isDay) {
        this.AnalyzeNew = res.data.data.thisYear?.Ld || {}
        this.AnalyzeNewLast = res.data.data.lastYear?.Ld || {}
        return
      }
      this.AnalyzeNew = res.data.data.thisYear?.Ln || {}
      this.AnalyzeNewLast = res.data.data.lastYear?.Ln || {}
    })
  }

  /* 日历 */
  getCalendar(deviceId: any, date: any) {
    const data = {
      deviceId,
      date,
      type: this.isDay ? 1 : 2,
    }
    calendar(data).then((res) => {
      this.output(new Date().getFullYear() + '-' + (new Date().getMonth() + 1))
      this.dateData.forEach((v: any) => {
        if (v.curr === 1) {
          res.data.data.forEach((x: any) => {
            let num = dayjs(x.date).format('DD')
            const date = Number(num)[0] === 0 ? Number(num)[1] : Number(num)
            if (date === v.date && x.functionTypeName) {
              v.level = 1
              if (x.abnormal) {
                v.level = 2
                v.ld = x.ld
                v.ln = x.ln
              }
            }
          })
        }
      })
    })
  }
  /* 点击marker */
  clickMarker(stationId: any, deviceId: any, date: any, type: any) {
    this.deviceId = deviceId
    this.date = date
    this.getAnalyzeNew(stationId)
    this.getCalendar(deviceId, date)
    this.changeUrl(type)
  }
  //改变类型图标
  changeUrl(type: number) {
    this.stationPointList.forEach((v: any) => {
      switch (v.type) {
        case 1:
          v.url = this.cercle1
          break
        case 2:
          v.url = this.cercle2
          break
        case 3:
          v.url = this.cercle3
          break
        case 9:
          v.url = this.cercle4
          break
        default:
          break
      }
      if (type === v.type) {
        switch (type) {
          case 1:
            v.url = this.cercle11
            break
          case 2:
            v.url = this.cercle22
            break
          case 3:
            v.url = this.cercle33
            break
          case 9:
            v.url = this.cercle44
            break
          default:
            break
        }
      }
      return v
    })
  }

  changeDay() {
    this.isDay = !this.isDay
    if (this.isDay) {
      // 切换到白天数据
      this.AnalyzeNew = this.AnalyzeNewDatea.thisYear?.Ld || {}
      this.AnalyzeNewLast = this.AnalyzeNewDatea.lastYear?.Ld || {}
      this.zjImg = require('@/assets/<EMAIL>')
      this.yjImg = require('@/assets/<EMAIL>')
    } else {
      // 切换到夜间数据
      this.AnalyzeNew = this.AnalyzeNewDatea.thisYear?.Ln || {}
      this.AnalyzeNewLast = this.AnalyzeNewDatea.lastYear?.Ln || {}
      this.zjImg = require('@/assets/<EMAIL>')
      this.yjImg = require('@/assets/<EMAIL>')
    }
    this.getCalendar(this.deviceId, this.date)
  }
}
</script>
