<style lang="less" scoped>
.no-data {
  font-size: 0.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
<template>
  <div
    v-if="propData.value&&propData.value.length > 0"
    :id="id"
    :style="{ height: height, width: width }"
  />
  <div v-else class="no-data" :style="{ height: height, width: width }">
    暂无数据
  </div>
</template>

<script lang="ts">
import * as echarts from 'echarts'
import { Component, Prop, Watch } from 'vue-property-decorator'
import { mixins } from 'vue-class-component'
import ResizeMixin from '@/components/Charts/mixins/resize'
import moment from 'moment'
interface AirData {
  x: string[]
  value: string[]
  standard: number | string
  threshold: number | string
  max: number | string
  unit: string
  name: string
}
@Component({
  name: 'LineChartDashed'
})
export default class extends mixins(ResizeMixin) {
  // @Prop({ default: "chart" }) private className!: string;
  @Prop({ default: '' }) private subTabName!: string
  @Prop({ default: 0 }) private dataType!: string|number
  @Prop({ default: 0 }) private standard!: Number|string
  @Prop({ default: 'kg' }) private unit!: Number|string
  @Prop({ default: 'chart' }) private id!: string
  @Prop({ default: '200px' }) private width!: string
  @Prop({ default: '200px' }) private height!: string
  @Prop({ required: true,default:()=>({value:[]}) }) private propData!: AirData
  @Prop({ required: false, default: 'rgba(14,156,255,1)' })
  private bgColor!: string
  @Prop({ required: false, default: false }) private smooth!: boolean
  @Prop({ required: false, default: true }) private bgColorState!: boolean
  @Prop({ required: false, default: '' }) private xText!: string
  @Watch('propData', { deep: true }) public onMsgChanged(
    newValue: AirData,
    oldValue: AirData
  ) {
    if (this.propData) {
      if (this.chart) {
        this.chart.clear()
        this.chart.dispose()
        this.chart = null
      }
      this.$nextTick(() => {
        this.initChart()
      })
    }
  }
  mounted() {
    if (this.propData) {
      this.initChart()
    }
  }
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.clear()
    this.chart.dispose()
    this.chart = null
  }

  private initChart() {
    if (this.chart === null || this.chart === undefined) {
      const chartDom = document.getElementById(this.id) as HTMLDivElement
      if (!chartDom) return
      this.chart = echarts.init(chartDom)
    }
    // SO2:1 NO2:1 O3:1 CO:1 PM10:1 PM:2.5
    const option:any ={
      backgroundColor: 'transparent',
      color:['#1DCCFF','#15FEFE'],
      grid: {
        height: '80%',
        left: '0%',
        right: '3%',
        top: '20%',
        bottom: '5%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        name: '',
        nameTextStyle: {
          color: '#fff',
          lineHeight: -30,
          align: 'center',
          verticalAlign: 'bottom'
        },
        data: this.propData.x.map(v=>v.slice(5,10)),
        axisLabel: {
          textStyle: {
            fontSize: 12,
            color: 'white'
          }
        },
        axisLine: {
          show: false,
          lineStyle:{
            type: 'dotted'
          }
        },
        axisTick: {
          show: false
        }
      },
      yAxis: {
        type: 'value',
        name: '单位：' + this.unit,
        min: 0,
        // max: this.propData.max,
        nameTextStyle: {
          color: '#fff',
          shadowOffsetX: 50
          // align: "center"
        },
        axisLabel: {
          textStyle: {
            fontSize: 12,
            color: 'white'
          }
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        splitNumber: 5,
        splitLine: {
          lineStyle: {
            // type: "dashed"
            color: 'RGBA(2, 39, 75, 1)'
          }
        }
      },
      tooltip: {
        trigger: 'axis',
        confine: true,
        axisPointer: {
          type: 'cross',
          label: {
            show:false,
            backgroundColor: '#6a7985'
            // formatter:(params:any)=>{
            //   if(params.axisDimension=='x'){
            //     return params.value
            //   }else{
            //     return ''
            //   }
            // },
          }
        },
        formatter:(params:any)=>{
          const time:string = this.propData.x[params[0].dataIndex]
          let str = `
            <div style="border-radius: 0.06rem;padding: 0.1rem 0.13rem; background-color: rgba(11, 11, 11, .4);color: #fff;">
              <div style="font-size: 0.14rem;"><i class="el-icon-time"></i>  监测时间：  ${time}</div>
              <div style="font-size: 0.14rem;margin-top: 0.12rem;">
                ${params[0].marker}  今日${this.subTabName}： <span>${params[0].value}Kg</span>
              </div>
            </div>
          `
          return str
        }
      },
      series: [
        {
          // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
          // @ts-ignore
          name: this.propData.name,
          data: this.propData.value || [],
          type: 'line',
          smooth: this.smooth,
          lineStyle: {
            color: '#15B4FE' //改变折线颜色
          },
          symbol: 'circle',
          symbolSize: 5,
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: this.bgColorState
                    ? 'RGBA(21,180,254, 0.7)'
                    : 'transparent' // 0% 处的颜色
                },
                {
                  offset: 1,
                  color: 'RGBA(25,252,255, 0)' // 100% 处的颜色
                }
              ],
              global: false
            },
            shadowColor: 'rgba(0,85,250,0)',
            shadowBlur: 20
          },
          itemStyle: {
            color: (params:any) => {
              if(this.subTabName=='利用量'){
                return '#1DCCFF'
              }else{
                return params.value <= this.propData.threshold||this.propData.threshold==null ? '#1DCCFF'  : 'red'
              }
              // return params.value < this.propData.standard ? '#1DCCFF'  : '#1DCCFF'
            }, //改变折线点的颜色 '#1DCCFF'
            borderColor: '#fff'
          }
        },
      ]
    }
    if(this.subTabName=='Ld'||this.subTabName=='Ln'){
      const obj:any = {
        name: '告警阈值',
        type: 'line',
        markLine: {
          symbol: 'none',
          label: {
            show: true,
            // formatter:()=>{
            //   return '告警阈值'
            // },
            // distance: [-13,0]
          },
          data: [
            {
              silent: false, //鼠标悬停事件  true没有，false有
              lineStyle: {
                //警戒线的样式  ，虚实  颜色
                type: 'dotted',
                color: 'red'
              },
              yAxis: this.standard
            }
          ]
        }
      }
      option.series=[obj, ...option.series]
    }
    this.chart.setOption(option as EChartOption<EChartOption>)
  }
}
</script>



