<template>
    <div
            :id="id"
            :style="{ height: height, width: width }"
            v-if="propData.dataList.length != 0"
    />
    <div v-else>暂无数据</div>
</template>

<script lang="ts">
import * as echarts from 'echarts'
import { Component, Prop, Watch } from "vue-property-decorator";
import { mixins } from "vue-class-component";
import ResizeMixin from "./mixins/resize";
interface LineData {
    bottomList: string[];
    dataList: string[];
    unit: '',
    name: ''
}
@Component({
    name: "LineChart"
})
export default class extends mixins(ResizeMixin) {
    @Prop({ default: "chart" }) private id!: string;
    @Prop({ default: "200px" }) private width!: string;
    @Prop({ default: "200px" }) private height!: string;
    @Prop({ required: true }) private propData!: LineData;
    // private chart: any = null;
    private option: any = {};
    @Watch("propData", { immediate: true, deep: true }) public onMsgChanged(
        newValue: LineData,
        oldValue: LineData
    ) {
        this.propData = newValue;
        if (newValue.dataList.length) {
            if (this.chart) {
                this.chart.clear();
                this.chart.dispose();
                this.chart = null;
            }
            this.$nextTick(() => {
                this.initChart();
            });
        }
    }
    mounted() {
        setTimeout(() => {
            this.initChart();
        }, 2000);
    }
    beforeDestroy() {
        if (!this.chart) {
            return;
        }
        this.chart.clear();
        this.chart.dispose();
        this.chart = null;
    }
    private initChart() {
        if(this.chart === null || this.chart === undefined) {
            this.chart = echarts.init(
                document.getElementById(this.id) as HTMLDivElement
            );
        }
        const option = {
            grid: [{
                top: '15%',
                left: '8%',
                right: '0%',
                bottom: '18%'
            }],
            tooltip: {
                trigger: 'axis'
            },
            xAxis: [
                {
                    type: 'category',
                    axisLine: {
                        lineStyle: {
                            color: '#FFFFFF',
                        }
                    },
                    boundaryGap: true,
                    axisTick: {
                        show: false
                    },
                    axisLabel: {
                        show: true,
                        margin: 10,
                        color: '#86C6FF',
                        fontSize: 12,
                        formatter: function (item:any) {
                            let ret = ''
                            const temp1 = item.substr(11, 5) + '\n'
                            const temp2 = item.substr(5, 5)
                            ret = temp1 + temp2 // 凭借最终的字符串
                            return ret
                        }
                    },
                    data: this.propData.bottomList,
                }
            ],
            yAxis: [
                {
                    name: this.propData.unit ? ('单位：'+ this.propData.unit) : '',
                    type: 'value',
                    axisLine: {
                        show: false,
                    },
                    axisTick: {
                        show: false
                    },
                    axisLabel: {
                        show: true,
                        margin: 5,
                        color: '#86C6FF',
                        fontSize: 12
                    },
                    splitLine: {
                        show: true,
                        lineStyle: {
                            type: 'dashed',
                            color: 'rgba(255,255,255,.08)'
                        }
                    },
                    nameTextStyle: {
                        color: '#86C6FF',
                        fontSize: 12
                    }
                },
            ],
            series: [
                {
                    name: this.propData.name,
                    type: 'line',
                    symbolSize: 1,
                    smooth: true,
                    lineStyle: {
                        color: '#40DEFF'
                    },
                    symbol: "circle",
                    itemStyle: {
                        color: '#40DEFF',
                        // borderColor: "#fff",
                        // borderWidth: 1,
                    },
                    emphasis: {
                        scale: true,
                        itemStyle: {
                            color: '#40DEFF',
                            borderColor: "#fff",
                            borderWidth: 1,
                        },
                    },
                    areaStyle: {
                        color: {
                            type: "linear",
                            x: 0,
                            y: 0,
                            x2: 0,
                            y2: 1,
                            colorStops: [
                                {
                                    offset: 0,
                                    color: '#40DEFF'
                                },
                                {
                                    offset: 1,
                                    color: 'transparent' // 100% 处的颜色
                                }
                            ],
                            global: false
                        },
                    },
                    data: this.propData.dataList
                }
            ]
        }
        // @ts-ignore
        this.chart.setOption(option as EChartOption<EChartOption>);
    }
}
</script>

