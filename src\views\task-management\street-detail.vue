<style lang="less" scoped>
p {
  margin-bottom: 0;
}

.monitor {
  display: flex;
  color: #ffffff;
  padding: 0.12rem;
  align-items: center;
  .spot {
    width: 0.2rem;
    height: 0.2rem;
    background-image: url('../../assets/rwcztjc-jb.png');
    background-size: 100% 100%;
    margin-right: 0.12rem;
  }
  .line {
    width: 0.02rem;
    height: 0.24rem;
    background-image: url('../../assets/<EMAIL>');
    background-size: 100% 100%;
    margin: 0 0.2rem;
  }
  .position {
  }
}
.monitor:nth-child(odd) {
  background: #052f61;
}
.monitor:nth-child(even) {
  background: transparent;
}
.task {
  box-sizing: border-box;
  .task-top {
    padding: 0.15rem 0.25rem;
    box-sizing: border-box;
    background-image: url('../../assets/task2.png');
    background-size: 100% 100%;
    > div:nth-child(2) {
      display: flex;
      > :nth-of-type(1) {
        img {
          object-fit: cover;
          width: 100%;
          height: 100%;
        }
      }
      > :nth-of-type(2) {
        display: flex;
        flex-direction: column;
        padding-left: 0.1rem;
        justify-content: space-between;
        flex: 1;
        p {
          margin-bottom: 0;
        }
      }
      .task-top-name {
        color: rgba(0, 252, 249, 1);
        font-size: 0.16rem;
        // margin-left: 0.15rem;
      }
      .task-top-text {
        font-size: 0.16rem;
        color: #ffffff;
      }
      .task-top-text-big {
        display: inline-block;
        font-size: 0.16rem;
        background: rgba(18, 75, 156, 1);
        border-radius: 0.04rem;
        padding: 0.04rem;
      }
    }
  }
  .task-bottom {
    box-sizing: border-box;
    background-image: url('../../assets/task1.png');
    background-size: 100% 180%;
    padding: 0.15rem 0.25rem;
    .task-bottom-top {
      border-bottom: 1px solid rgba(255, 255, 255, 0.35);
      padding-bottom: 0.05rem;
      > :nth-of-type(1) {
        width: 0.4rem;
        height: 0.4rem;
        border-radius: 50%;
        margin-right: 0.1rem;
        img {
          width: 100%;
          height: 100%;
          border-radius: 50%;
        }
      }
      display: flex;
      align-items: center;
      .task-bottom-top-name {
        color: rgba(0, 234, 255, 1);
        font-size: 0.16rem;
      }
      .task-bottom-top-time {
        color: rgba(191, 208, 210, 1);
        font-size: 0.13rem;
      }
    }
    .task-bottom-bottom {
      font-size: 0.16rem;
      color: #ffffff;
      padding-top: 0.05rem;
    }
  }
  .lines {
    width: 2.66rem;
    height: 0.02rem;
    background: url(../../assets/<EMAIL>) no-repeat;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    margin: 0.15rem auto;
  }
  // .task-charts {
  //   padding: 0.15rem;
  //   box-sizing: border-box;
  //   background-image: url("../../assets/task3.png");
  //   background-size: 100% 100%;
  //   display: flex;
  //   justify-content: space-between;
  // }
}
.playback-form {
  color: #fff;
  transition: all 1s linear;
  opacity: 0;
  pointer-events: none;
  > div:nth-child(odd) {
    margin-bottom: 0.03rem;
  }
  > div:nth-child(even) {
    margin-bottom: 0.15rem;
  }
  .date-picker {
    margin-right: 0.2rem;
  }
  .time-picker {
    width: 1.28rem;
  }
}
.show-playback {
  opacity: 1;
  pointer-events: auto;
}
.query-button {
  width: 100%;
  margin-top: 0.05rem;
  display: flex;
  justify-content: center;
  > button {
    width: 1.62rem;
    // background:linear-gradient(0deg,rgba(26,154,255,1),rgba(48,120,253,1));
    // outline: none;
    // border: none;
    // color: #fff
  }
}
.multiple-speed,
.play-ctr {
  width: 100%;
  display: flex;
  justify-content: space-around;
  margin-top: 0.1rem;
  > button {
    width: 0.8rem;
  }
}
.play-ctr {
  padding: 0 0.65rem;
  box-sizing: border-box;
  > button {
    width: 1rem;
  }
}
.playback-form {
  padding: 0.2rem 0.3rem 0.2rem 0.3rem !important;
}
.ant-form {
  color: #fff;
}
.form-item {
  margin-bottom: 0.1rem;
}
.ant-calendar-picker {
  width: 152px !important;
  min-width: 152px !important;
}
.ant-form-item {
  margin: 0 5px !important;
}
.button {
  width: 0.79rem;
  height: 0.3rem;
  line-height: 0.3rem;
  text-align: center;
  padding: 0 0.15rem;
  box-sizing: border-box;
  border: none;
  cursor: pointer;
  border-radius: 0.05rem;
}
.button-selected {
  border: 1px solid #0084ff;
}
.f-color {
  color: rgba(255, 255, 255, 1) !important;
  font-size: 0.18rem !important;
  text-shadow: none;
}
.type-center {
  position: relative;
  width: 9.5rem;
  .type-center-top {
    margin-top: 0.57rem;
    display: flex;
    justify-content: space-between;
    .car-normal {
      p {
        margin-bottom: 0;
      }
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      width: 1.6rem;
      height: 1rem;
      background: url(../../assets/<EMAIL>);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      > :nth-of-type(1) {
        font-size: 0.19rem;
        color: rgba(255, 255, 255, 1);
      }
      > :nth-of-type(2) {
        width: 0.34rem;
        height: 0.3rem;
        margin-top: 0.11rem;
        img {
          width: 100%;
          height: 100%;
        }
      }
    }
    .car-active {
      p {
        margin-bottom: 0;
      }
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      width: 1.6rem;
      height: 1rem;
      background: url(../../assets/<EMAIL>);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      > :nth-of-type(1) {
        font-size: 0.19rem;
        color: rgba(255, 255, 255, 1);
      }
      > :nth-of-type(2) {
        width: 0.34rem;
        height: 0.3rem;
        margin-top: 0.11rem;
        img {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
  .center-map {
    position: absolute;
    width: 10.5rem;
    left: -0.5rem;
    height: calc(1080px - 2.6rem);
    margin-top: 0.15rem;
    > div {
      width: 100%;
      height: 100%;
    }
  }
}
.main-content {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  > div {
    width: 100%;
    height: 0.31rem;
  }
  .legend {
    display: flex;
    justify-content: flex-end;
    .on {
      display: flex;
      align-items: center;
      .block {
        width: 0.08rem;
        height: 0.08rem;
        background: #56ccff;
        margin: 0 0.1rem 0 0.3rem;
      }
    }
    .off {
      display: flex;
      align-items: center;
      .block {
        width: 0.08rem;
        height: 0.08rem;
        background: #719eef;
        margin: 0 0.1rem 0 0.3rem;
      }
    }
    .error {
      display: flex;
      align-items: center;
      .block {
        width: 0.08rem;
        height: 0.08rem;
        background: #f27678;
        margin: 0 0.1rem 0 0.3rem;
      }
    }
  }
  .line-one {
    display: flex;
    justify-content: space-between;
    font-size: 0.14rem;
    font-family: PingFang SC;
    font-weight: 500;
    color: rgba(255, 255, 255, 1);
    > :nth-of-type(2) {
      color: rgba(0, 222, 255, 1);
    }
  }
  .line-two {
    height: 0.06rem;
    background-color: #f27678;
    // border-radius: 0.12rem;
    display: flex;
    align-items: center;
    > div {
      height: 0.06rem;
      // border-radius: 0.12rem;
      // border-top-right-radius: 0;
      // border-bottom-right-radius: 0;
    }
  }
}
.common-content-title {
  display: flex;
  align-items: center;
  justify-content: center;
  // width: 76px;
  height: 0.3rem;
  font-size: 0.16rem;
  background: rgba(18, 75, 156, 1);
  text-align: center;
  margin-bottom: 0.3rem;
  // > span {
  //   display: inline-block;
  // }
  // span.circle {
  //   margin-right: 0.1rem;
  //   width: 0.06rem;
  //   height: 0.06rem;
  //   background: rgba(0, 234, 255, 1);
  //   border-radius: 50%;
  // }
}
.title-top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  > div {
    display: flex;
    font-size: 0.14rem;
    font-weight: 400;

    > div {
      width: 0.8rem;
      height: 0.28rem;
      line-height: 0.3rem;
      color: #0e8bff;
      // background: rgba(14, 139, 255, 0.32);
      // border: 1px solid rgba(14, 139, 255, 1);
      text-align: center;
      // clip-path: polygon(100% 0, 100% 100%, 12% 100%, 0% 70%, 0 0);
      background: url(../../assets/<EMAIL>);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      cursor: pointer;
    }
    div.type-active {
      background: url(../../assets/<EMAIL>);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      color: rgba(0, 234, 255, 1);
    }
    :nth-of-type(2) {
      margin-left: 0.1rem;
    }
  }
}
.table-data {
  width: 100%;
  .table-data-thead {
    width: 100%;
    .tr {
      display: flex;
      justify-content: space-between;
      .th {
        background: transparent !important;
        color: #ffffff;
        padding: 0;
        text-align: center;
        border: none;
        line-height: 0.34rem;
        font-size: 0.14rem;
        height: 0.34rem;
      }
      > :nth-of-type(1) {
        width: 20%;
      }
      > :nth-of-type(2) {
        width: 25%;
      }
      > :nth-of-type(3) {
        width: 20%;
      }
      > :nth-of-type(4) {
        width: 25%;
      }
    }
  }
  .table-data-tbody {
    width: 4.6rem;
    height: calc(2rem - 0.35rem);
    .tr {
      display: flex;
      justify-content: space-between;
      .td {
        color: #ffffff;
        padding: 0;
        font-size: 0.14rem;
        text-align: center;
        height: 0.34rem;
        line-height: 0.34rem;
        border: none;
      }
      > :nth-of-type(1) {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 20%;
      }
      > :nth-of-type(2) {
        width: 25%;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
      > :nth-of-type(3) {
        width: 20%;
      }
      > :nth-of-type(4) {
        width: 25%;
      }
    }
  }
  .table-data-tbody .tr:nth-child(odd) {
    background: rgba(5, 47, 97, 1);
  }
  .table-data-tbody .tr:nth-child(even) {
    background: transparent;
  }
  .tdBefore {
    //前三
    width: 0.26rem;
    display: block;
    text-align: center;
    height: 0.2rem;
    line-height: 0.2rem;
    background: rgba(255, 133, 9, 1);
    border-radius: 0.06rem;
    margin: 0 auto;
  }
  .tdAfter {
    //前三外
    width: 0.26rem;
    display: block;
    text-align: center;
    height: 0.2rem;
    line-height: 0.2rem;
    background: rgba(18, 136, 226, 1);
    border-radius: 0.06rem;
    margin: 0 auto;
  }
}
.common-main {
  padding: 0;
}
.left-region {
  width: 100%;
  height: 100%;
  position: relative;
  .left-region-bottom {
    position: absolute;
    bottom: 0;
    left: 0;
    padding: 0 0.2rem 0 0.2rem;
    display: flex;
    justify-content: space-between;
    height: 3.8rem;
    // padding-top: 0.5rem;
    width: 1920px;
    background-image: url('../../assets/bottomBg.png');
    background-size: 100% 100%;
    // box-shadow: 0 0 10px 10px #888888;

    > div {
      position: relative;
      padding: 0.16rem;
      margin-bottom: 0.1rem;
      // width: 4.46rem;
    }
    .left-part-one {
      // width: 4rem;
    }
  }
  .left-region-top {
    height: 100%;
    width: 100%;
    position: relative;
    // margin-bottom: 0.35rem;
    // margin-top: 0.4rem;
    .conner-left-top {
      position: absolute;
      top: -2px;
      left: -2px;
      width: 0.1rem;
      height: 0.1rem;
      border-top: 2px solid #00eaff;
      border-left: 2px solid #00eaff;
    }
    .conner-right-top {
      position: absolute;
      top: -2px;
      right: -2px;
      width: 0.1rem;
      height: 0.1rem;
      border-top: 2px solid #00eaff;
      border-right: 2px solid #00eaff;
    }
    .conner-left-bot {
      position: absolute;
      left: -2px;
      bottom: -2px;
      width: 0.1rem;
      height: 0.1rem;
      border-bottom: 2px solid #00eaff;
      border-left: 2px solid #00eaff;
    }
    .conner-right-bot {
      position: absolute;
      right: -2px;
      bottom: -2px;
      width: 0.1rem;
      height: 0.1rem;
      border-right: 2px solid #00eaff;
      border-bottom: 2px solid #00eaff;
    }
    .center-map {
      width: 1920px;
      height: 1080px;
      position: relative;
      .type-car {
        margin-top: 0.1rem;
        position: absolute;
        left: 0;
        top: 0;
        box-sizing: border-box;
        padding: 0.2rem 0.15rem 0.1rem 0.5rem;
        width: 2.5rem;
        .type-car-text {
          font-size: 0.22rem;
        }
        .count {
          font-size: 0.24rem;
          font-weight: bold;
        }
        .line {
          font-size: 0.24rem;
        }
        > div {
          display: flex;
          align-items: center;
          justify-content: left;
          margin-bottom: 0.2rem;
          width: 2rem;
          > div {
            height: 0.5rem;
          }
          > div:nth-child(1) {
            width: 0.6rem;
            display: flex;
            align-items: center;
            justify-content: center;
            // background-image: url("../../assets/carbox.png");
            background-size: 100% 100%;
          }
          .circle {
            align-self: center;
            // border-radius: 50%;
            margin-right: 0.24rem;
            img {
              width: 0.6rem;
              height: 0.6rem;
            }
          }
        }
        .legend {
          font-size: 0.15rem;
          color: #19f1f9;
        }
      }
      .type-department {
        margin-top: 0.1rem;
        position: absolute;
        right: 0;
        top: 0;
        box-sizing: border-box;
        padding: 0.2rem 0.5rem 0.1rem 0.15rem;
        width: 2.5rem;
        img {
          width: 0.54rem;
          height: 0.6rem;
          margin: 0.09rem 0;
          cursor: pointer;
        }
        > div {
          display: flex;
          flex-direction: column;
          align-items: flex-end;
        }
      }
    }
    .img-top-right {
      position: absolute;
      background: url(../../assets/<EMAIL>);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      width: 3rem;
      height: 0.08rem;
      top: -0.2rem;
      right: 0;
    }
    .img-top-left {
      position: absolute;
      background: url(../../assets/<EMAIL>);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      width: 3rem;
      height: 0.08rem;
      top: -0.2rem;
      left: 0;
    }
    .img-bottom-right {
      position: absolute;
      background: url(../../assets/<EMAIL>);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      width: 1.73rem;
      height: 0.08rem;
      bottom: -0.2rem;
      right: 0;
    }
    .img-bottom-left {
      position: absolute;
      background: url(../../assets/<EMAIL>);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      width: 1.73rem;
      height: 0.08rem;
      bottom: -0.2rem;
      left: 0;
    }
    .img-left-top {
      position: absolute;
      background: url(../../assets/<EMAIL>);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      width: 0.08rem;
      height: 0.83rem;
      top: 0;
      left: -0.2rem;
    }
    .img-left-bottom {
      position: absolute;
      background: url(../../assets/<EMAIL>);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      width: 0.08rem;
      height: 0.83rem;
      bottom: 0;
      left: -0.2rem;
    }
    .img-right-top {
      position: absolute;
      background: url(../../assets/<EMAIL>);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      width: 0.08rem;
      height: 0.83rem;
      top: 0;
      right: -0.2rem;
    }
    .img-right-bottom {
      position: absolute;
      background: url(../../assets/<EMAIL>);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      width: 0.08rem;
      height: 0.83rem;
      bottom: 0;
      right: -0.2rem;
    }
  }
}
.display-state {
  display: none !important;
  transform: scale(0);
  transition: 1s;
}
.display-state-map {
  height: 100%;
  width: 1843px;
}
.conner-left-top {
  position: absolute;
  top: -2px;
  left: -2px;
  width: 0.1rem;
  height: 0.1rem;
  border-top: 2px solid #00eaff;
  border-left: 2px solid #00eaff;
}
.conner-right-top {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 0.1rem;
  height: 0.1rem;
  border-top: 2px solid #00eaff;
  border-right: 2px solid #00eaff;
}
.conner-left-bot {
  position: absolute;
  left: -2px;
  bottom: -2px;
  width: 0.1rem;
  height: 0.1rem;
  border-bottom: 2px solid #00eaff;
  border-left: 2px solid #00eaff;
}
.conner-right-bot {
  position: absolute;
  right: -2px;
  bottom: -2px;
  width: 0.1rem;
  height: 0.1rem;
  border-right: 2px solid #00eaff;
  border-bottom: 2px solid #00eaff;
}
.sub-title {
  margin-bottom: 0.1rem !important;
}
.map-box {
  overflow: hidden;
  width: 100%;
  height: 100%;
}
.quanpin {
  position: absolute;
  top: -0.88rem;
  right: 0.5rem;
  transition: 1.5s;
  z-index: 99;
}
.fade-enter-active,
.fade-leave-active {
  transition: all 0.5s;
  position: absolute;
}
.fade-enter, .fade-leave-to /* .fade-leave-active below version 2.1.8 */ {
  left: -5rem;
}
.common-title .title {
  text-shadow: 0 0 5px blue, 0 0 5px blue;
  font-size: 0.22rem;
}
.mile,
.mile2 {
  background: url('../../assets/lcs-k1.png');
  background-size: 100% 100%;
  width: 0.31rem;
  height: 0.45rem;
  line-height: 0.45rem;
  font-size: 0.26rem;
  text-align: center;
  margin-right: 0.01rem;
}
.mile2 {
  background: url('../../assets/lcs-k2.png');
  background-size: 100% 100%;
}
.mileage {
  display: flex;
  align-items: center;
}
.mile-cycle {
  cursor: pointer;
  width: 0.25rem;
  height: 0.25rem;
  line-height: 0.25rem;
  text-align: center;
  border-radius: 5px;
}
.cycle-of-day {
  margin: 0 0.1rem 0 0.15rem;
}
.mile-cycle-selected {
  background: #03112d;
  border: 1px solid rgba(0, 132, 255, 1);
}
.playback {
  width: 1.63rem;
  height: 0.35rem;
  background: linear-gradient(
    0deg,
    rgba(77, 161, 248, 1) 0%,
    rgba(54, 116, 217, 1) 100%
  );
  cursor: pointer;
  border-radius: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.playback-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  img {
    margin: 0.1rem 0;
  }
}
.playback-form {
  padding: 0.2rem 0.3rem !important;
}
.ant-form {
  color: #fff;
}
.form-item {
  margin-bottom: 0.1rem;
}
.common-content-one {
  box-sizing: border-box;
  width: 3.9rem;
  height: 2.87rem;
  background: rgba(8, 45, 120, 0.53);
  padding: 0.18rem 0 0.1rem 0;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  .one-top {
    padding: 0 0.14rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: 0.11rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.35);
    .one-top-div {
      margin-left: 0.12rem;
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      > :nth-of-type(1) {
        font-size: 0.2rem;
        font-family: Source Han Sans SC;
        font-weight: 400;
        color: rgba(0, 234, 255, 1);
      }
      > :nth-of-type(2) {
        font-size: 0.14rem;
        font-family: Adobe Heiti Std;
        font-weight: normal;
        color: rgba(191, 208, 210, 1);
      }
    }
  }
  .one-bottom {
    padding: 0 0.14rem;
    flex: 1;
    .one-bottom-one {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 0.15rem;
      > :nth-of-type(1) {
        background: rgba(253, 21, 21, 1);
        border-radius: 0.04rem;
        padding: 0 0.13rem;
        height: 0.3rem;
        line-height: 0.3rem;
        display: inline-block;
        font-size: 0.2rem;
        font-family: Source Han Sans SC;
        font-weight: 400;
        color: rgba(255, 255, 255, 1);
      }
      > :nth-of-type(2) {
        margin-left: 0.08rem;
        flex: 1;
        font-size: 0.24rem;
        font-family: Source Han Sans SC;
        font-weight: 400;
        color: rgba(0, 222, 255, 1);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    .one-bottom-two {
      margin-top: 0.14rem;
      font-size: 0.18rem;
      font-family: Source Han Sans SC;
      font-weight: 400;
      color: rgba(255, 255, 255, 1);
      line-height: 0.25rem;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
    }
    .one-bottom-three {
      font-size: 0.2rem;
      font-family: Source Han Sans SC;
      font-weight: 400;
      color: rgba(0, 234, 255, 1);
      margin: 0.05rem 0;
    }
  }
  .one-bottom-four {
    width: 100%;
    font-size: 0.18rem;
    padding: 0.05rem 0;
    color: #ffffff;
    text-align: center;
    background-image: radial-gradient(
      #1c3da8,
      rgba(28, 61, 168, 0.7),
      rgba(28, 61, 168, 0.1)
    );
  }
}
.common-content-two {
  box-sizing: border-box;
  width: 5.18rem;
  height: 2.87rem;
  background: rgba(8, 45, 120, 0.53);
  padding: 0.21rem 0.06rem 0.1rem 0.06rem;
  .two-table {
    display: block !important;
    width: 100%;
    font-size: 0.16rem;
    .two-thead {
      padding: 0 0.3rem;
      height: 0.5rem;
      line-height: 0.5rem;
      background: rgba(12, 54, 140, 1);
      display: flex;
      justify-content: space-between;
      align-items: center;
      .td {
        width: 25%;
      }
    }
    .two-tbody {
      .tr {
        padding: 0 0.3rem;
        height: 0.4rem;
        line-height: 0.4rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .td {
          width: 25%;
        }
      }
      .tr:nth-child(odd) {
        background: rgba(29, 68, 148, 0.2);
      }
      .tr:nth-child(even) {
        background: rgba(29, 68, 148, 0);
      }
    }
  }
  .two-bottom {
    width: 5.18rem;
    height: 2.87rem;
    background: #001865;
    position: relative;
    img {
      width: 5.18rem;
      height: 2.87rem;
    }
    .right-arrow1 {
      position: absolute;
      cursor: pointer;
      width: 0.33rem;
      height: 0.33rem;
      background: rgba(3, 17, 45, 0.5);
      right: 0.1rem;
      top: 50%;
      transform: translateY(-50%);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 99;
      outline: none !important;
    }
    .left-arrow1 {
      position: absolute;
      cursor: pointer;
      width: 0.33rem;
      height: 0.33rem;
      background: rgba(3, 17, 45, 0.5);
      left: 0.1rem;
      top: 50%;
      transform: translateY(-50%);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 99;
      outline: none !important;
    }
    .svg-arrow {
      width: 0.25rem;
      height: 0.25rem;
    }
  }
}
.common-content-three {
  box-sizing: border-box;
  width: 7.62rem;
  height: 2.87rem;
  background: rgba(8, 45, 120, 0.53);
  display: flex;
  justify-content: space-between;
  .three-left {
    width: 3.8rem;
    padding: 0.1rem 0.11rem;
    .three-left-top {
      display: flex;
      justify-content: space-between;
      padding-bottom: 0.11rem;
      border-bottom: 1px solid rgba(255, 255, 255, 0.35);
      > :nth-child(1) {
        flex: 1;
        padding-right: 0.14rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .one-top-div {
          margin-left: 0.12rem;
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          > :nth-of-type(1) {
            font-size: 0.2rem;
            font-family: Source Han Sans SC;
            font-weight: 400;
            color: rgba(0, 234, 255, 1);
          }
          > :nth-of-type(2) {
            font-size: 0.14rem;
            font-family: Adobe Heiti Std;
            font-weight: normal;
            color: rgba(191, 208, 210, 1);
          }
        }
      }
      > :nth-child(2) {
        width: 0.72rem;
        height: 0.59rem;
        margin-right: 0.3rem;
      }
    }
    .three-left-bottom {
      padding: 0.1rem 0;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      box-sizing: border-box;
      > :nth-of-type(1) {
        font-size: 0.2rem;
        font-family: Source Han Sans SC;
        font-weight: 500;
        color: rgba(31, 255, 252, 1);
        margin-bottom: 0.05rem;
      }
      > :nth-of-type(2) {
        box-sizing: border-box;
        // flex: 1;
        font-size: 0.18rem;
        font-family: Source Han Sans SC;
        font-weight: 400;
        color: rgba(255, 255, 255, 1);
        line-height: 0.26rem;
        overflow-y: scroll;
        height: 1.5rem;
        white-space: pre-wrap;
        word-wrap: break-word;
      }
    }
  }
  .three-lines {
    width: 0.02rem;
    height: 100%;
    padding: 0.15rem 0;
    padding-bottom: 0.1rem;
    div {
      width: 0.02rem;
      height: 100%;
      background: #0e6390;
    }
  }
  .three-right {
    padding: 0.18rem 0.17rem;
    width: 3.8rem;
    .three-right-top {
      font-size: 0.2rem;
      font-family: Source Han Sans SC;
      font-weight: 500;
      color: rgba(31, 255, 252, 1);
      margin-bottom: 0.1rem;
    }
    .three-right-bottom {
      width: 100%;
      height: 2.2rem;
      // background: #001865;
      position: relative;
      img {
        width: 100%;
        height: 2.2rem;
      }
      .right-arrow {
        position: absolute;
        cursor: pointer;
        width: 0.33rem;
        height: 0.33rem;
        background: rgba(3, 17, 45, 0.5);
        right: 0.1rem;
        top: 50%;
        transform: translateY(-50%);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 99;
        outline: none !important;
      }
      .left-arrow {
        position: absolute;
        cursor: pointer;
        width: 0.33rem;
        height: 0.33rem;
        background: rgba(3, 17, 45, 0.5);
        left: 0.1rem;
        top: 50%;
        transform: translateY(-50%);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 99;
        outline: none !important;
      }
      .svg-arrow {
        width: 0.25rem;
        height: 0.25rem;
      }
    }
  }
}
.common-content-car-one {
  box-sizing: border-box;
  // width: 7.5rem;
  height: 2.87rem;
  // background: rgba(8, 45, 120, 0.53);
  padding: 0.18rem 0 0.1rem 0;
  display: flex;
  justify-content: space-between;
  .task-top {
    padding: 0.15rem 0.25rem;
    box-sizing: border-box;
    background-image: url('../../assets/task2.png');
    background-size: 100% 100%;
    > div:nth-child(2) {
      display: flex;
      > :nth-of-type(1) {
        img {
          object-fit: cover;
          width: 100%;
          height: 100%;
        }
      }
      > :nth-of-type(2) {
        display: flex;
        flex-direction: column;
        padding-left: 0.1rem;
        justify-content: space-between;
        flex: 1;
        p {
          margin-bottom: 0;
        }
      }
      .task-top-name {
        color: rgba(0, 252, 249, 1);
        font-size: 0.16rem;
        // margin-left: 0.15rem;
      }
      .task-top-text {
        font-size: 0.16rem;
        color: #ffffff;
      }
      .task-top-text-big {
        display: inline-block;
        font-size: 0.16rem;
        background: rgba(18, 75, 156, 1);
        border-radius: 0.04rem;
        padding: 0.04rem;
      }
    }
  }
  .task-bottom {
    box-sizing: border-box;
    background-image: url('../../assets/task1.png');
    background-size: 100% 180%;
    padding: 0.15rem 0.25rem;
    .task-bottom-top {
      border-bottom: 1px solid rgba(255, 255, 255, 0.35);
      padding-bottom: 0.05rem;
      > :nth-of-type(1) {
        width: 0.4rem;
        height: 0.4rem;
        border-radius: 50%;
        margin-right: 0.1rem;
        img {
          width: 100%;
          height: 100%;
          border-radius: 50%;
        }
      }
      display: flex;
      align-items: center;
      .task-bottom-top-name {
        color: rgba(0, 234, 255, 1);
        font-size: 0.16rem;
      }
      .task-bottom-top-time {
        color: rgba(191, 208, 210, 1);
        font-size: 0.13rem;
      }
    }
    .task-bottom-bottom {
      font-size: 0.16rem;
      color: #ffffff;
      padding-top: 0.05rem;
    }
  }
  .lines {
    width: 2.66rem;
    height: 0.02rem;
    background: url(../../assets/<EMAIL>) no-repeat;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    margin: 0.15rem auto;
  }
  .playback-form {
    color: #fff;
    transition: all 1s linear;
    opacity: 0;
    pointer-events: none;
    > div:nth-child(odd) {
      margin-bottom: 0.03rem;
    }
    > div:nth-child(even) {
      margin-bottom: 0.15rem;
    }
    .playback-button {
      display: inline-block;
      margin-right: 0.1rem;
    }
    .date-picker {
      margin-right: 0.2rem;
    }
    .time-picker {
      width: 1.28rem;
    }
  }
  .show-playback {
    opacity: 1;
    pointer-events: auto;
    width: 4rem;
  }
}
.common-content-car-two {
  box-sizing: border-box;
  width: 4.6rem;
  height: 2.87rem;
  // background: rgba(8, 45, 120, 0.53);
  padding: 0.18rem 0 0.1rem 0;
}
.content-right {
  background-image: url(../../assets/air_bg.png) !important;
  height: calc(1080px - 1rem);
  background-size: 100% 100% !important;
  position: absolute;
  right: 0;
  top: 0;
  padding-right: 0.25rem;
  .right-content {
    margin-top: 0.25rem;
    height: 2.5rem;
  }
}
.content-left {
  background-image: url(../../assets/air_bg_left.png) !important;
  height: calc(1080px - 1rem);
  background-size: 100% 100% !important;
  position: absolute;
  left: 0;
  top: 0;
  padding-left: 0.5rem;
  .left-content {
    margin-top: 0.25rem;
    height: 2.5rem;
    .common-content-left-one {
      > div {
        height: 0.7rem;
      }
      .analysis-name {
        font-size: 0.2rem;
        margin-bottom: 0.1rem;
        span {
          margin: 0 0.05rem;
          color: #009cff;
          font-weight: bold;
        }
      }
      > div:nth-of-type(1) {
        .analysis-line-two {
          > div {
            background: rgba(0, 156, 255, 1);
            border: 1px solid rgba(6, 28, 59, 1);
          }
        }
      }
      > div:nth-of-type(2) {
        .analysis-name {
          span {
            color: #09d2d4;
          }
        }
        .analysis-line-one {
          > div {
            background: rgba(7, 59, 60, 1);
          }
        }
        .analysis-line-two {
          > div {
            background: rgba(9, 210, 212, 1);
            border: 1px solid rgba(9, 35, 30, 1);
          }
        }
      }
      > div:nth-of-type(3) {
        .analysis-line-one {
          > div {
            background: rgba(46, 39, 115, 1);
          }
        }
        .analysis-line-two {
          > div {
            background: rgba(106, 104, 255, 1);
            border: 1px solid rgba(6, 28, 59, 1);
          }
        }
      }
      .analysis-line {
        position: relative;
        .analysis-line-one {
          position: absolute;
          display: flex;
          > div {
            width: 0.15rem;
            height: 0.22rem;
            background: rgba(19, 58, 113, 1);
            border: 1px solid rgba(9, 35, 30, 1);
          }
        }
        .analysis-line-two {
          position: absolute;
          display: flex;
          > div {
            width: 0.15rem;
            height: 0.22rem;
          }
        }
        .analysis-line-three {
          position: absolute;
          color: rgba(255, 255, 255, 1);
          font-size: 0.14rem;
          padding-left: 0.05rem;
          text-shadow: 0px 2px 2px rgba(0, 55, 90, 1);
        }
      }
    }
    .common-content-left-two {
      width: 4rem;
      .taskList-item {
        display: flex;
        align-items: center;
        width: 4rem;
        height: 0.38rem;
        // background: rgba(8, 47, 97, 0.8);
        // margin-bottom: 0.1rem;
        padding: 0 0.1rem;
        font-size: 0.14rem;
        .name-btn {
          padding: 0 0.1rem;
          margin-right: 0.1rem;
          height: 0.26rem;
          line-height: 0.26rem;
          background-image: url(../../assets/<EMAIL>) !important;
          background-size: 100% 100% !important;
        }
        .content {
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .line {
          width: 0.02rem;
          height: 0.25rem;
          background: rgba(0, 234, 255, 1);
          margin: 0 0.05rem;
        }
      }
      .taskList-item:nth-child(odd) {
        background: rgba(8, 47, 97, 0.8);
      }
      .taskList-item:nth-child(even) {
        background: transparent;
      }
    }
  }
}
.content-text {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}
.content-text1 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.streets {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  top: 0;
}
.change-camera {
  position: absolute;
  top: 1rem;
  right: 0.2rem;
  width: 0.8rem;
  height: 0.24rem;
  background: rgba(13, 29, 74, 0.7);
  border: 1px solid rgba(32, 103, 224, 1);
  border-radius: 0.04rem;
  display: flex;
  align-items: center;
  color: #ccc;
  justify-content: space-around;
  cursor: pointer;
}
.change-camera-selected {
  text-shadow: 0 0 5px blue, 0 0 5px blue;
  color: #fff;
}
</style>
<style lang="less">
.department-detail {
  .video-js.vjs-fluid {
    z-index: 999;
  }
  .common-content-two-content1 {
    .water-monitor-tab {
      height: 0.24rem;
      font-size: 0.12rem;
      color: rgba(255, 255, 255, 1);
      background: rgba(12, 39, 92, 1);
      border-radius: 0;
      padding: 0 0.1rem;
      border: none;
    }
    .ant-radio-group {
      width: 100%;
      display: flex;
      justify-content: space-around;
      background-color: #0f245e;
    }
    .ant-radio-group-solid
      .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
      background: #0084ff;
      border: none;
      padding: 0 0.15rem;
    }
    .ant-radio-button-wrapper:not(:first-child)::before {
      background: transparent;
    }
    .ant-radio-button-wrapper-checked::before {
      background: transparent !important;
    }
    .ant-radio-button-wrapper-checked {
      z-index: 1;
      border-color: #0084ff !important;
      -webkit-box-shadow: -1px 0 0 0 #0084ff;
      box-shadow: -1px 0 0 0 #0084ff;
    }
    .ant-radio-group-small .ant-radio-button-wrapper {
      // width: 14.28%;
      // width: 16.66%;
      text-align: center;
    }
    .ant-radio-group {
      display: flex;
    }
    .ant-radio-group-small .ant-radio-button-wrapper {
      padding: 0;
    }
  }
  .streets {
    .street-select {
      display: flex;
      justify-content: space-between;
      margin-right: 0.1rem;
      .ant-select-selection {
        width: 5.18rem;
        height: 1.3rem;
        border: none;
        border-radius: unset;
        background: url(../../assets/<EMAIL>);
        background-size: 100% 100%;
        background-repeat: no-repeat;
      }
      .ant-select-selection-selected-value {
        color: rgba(0, 234, 255, 1);
        font-size: 0.26rem;
      }
      .ant-select-focused .ant-select-selection,
      .ant-select-selection:focus,
      .ant-select-selection:active {
        border-color: #40a9ff;
        border-right-width: 0 !important;
        outline: 0;
        box-shadow: none;
        font-size: 0.26rem;
      }
      .ant-select-selection--single .ant-select-selection__rendered {
        margin-top: 0.63rem !important;
        margin-right: 0.4rem !important;
        margin-left: 0.4rem !important;
      }
      .ant-select-arrow {
        margin-top: 0.02rem;
        margin-right: 0.2rem;
      }
    }
  }
  ::-webkit-scrollbar {
    width: 0 !important;
  }
  .select-main {
    width: 1.2rem;
    display: flex;
    justify-content: space-between;
    .ant-select-selection {
      height: 0.3rem;
      width: 100%;
      border: none;
      outline: none;
      border-radius: unset;
      background: url(../../assets/<EMAIL>);
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
    .ant-select-selection-selected-value {
      color: rgba(0, 234, 255, 1);
    }
    .ant-select-focused .ant-select-selection,
    .ant-select-selection:focus,
    .ant-select-selection:active {
      border: none;
      border-right-width: 0 !important;
      outline: none;
      box-shadow: none;
    }
  }
}
</style>
<template>
  <!-- 车辆管理 -->
  <section class="common-main department-detail">
    <!-- 左侧部分 -->
    <section class="left-region">
      <section
        :class="{ 'left-region-top': true, 'display-state-map': displayState }"
      >
        <div class="map-box">
          <section class="center-map">
            <!-- <keep-alive> -->
            <gao-de-map
              ref="map"
              :mapStyle="mapStyle"
              :queryResultCar="queryResultCar"
              :mapZoom="13"
              :enterType="7"
              :viewMode="'2D'"
              :locationRecordList="locationRecordList"
              :oldCarPoint="oldCarPoint"
              :newCarPoint="newCarPoint"
              :mapMarker="mapMarker"
              @showTaskDetail="showTaskDetail"
              :jointEnforcementMarker="jointEnforcementMarker"
            ></gao-de-map>
            <!-- </keep-alive> -->
          </section>
        </div>
        <section class="quanpin" @click="fullScreen">
          <img src="@/assets/quanping.png" alt />
        </section>
        <section class="streets">
          <a-select
            v-model="defaultStreetValue"
            class="street-select"
            @change="streetChange"
          >
            <a-icon
              slot="suffixIcon"
              type="caret-down"
              style="color: rgba(0, 234, 255, 1); font-size: 0.26rem"
            />
            <a-select-option
              :value="String(index)"
              v-for="(item, index) in streetList"
              :key="index"
              >{{ item.departmentName }}</a-select-option
            >
          </a-select>
        </section>
      </section>
      <section
        class="content-left"
        style="transition: all 1s"
        :style="{
          opacity: !displayState ? '1' : '0',
          visibility: !displayState ? '' : 'hidden'
        }"
      >
        <div class="left-content">
          <div class="common-title">
            <div class="title title-top">
              <span>{{ `执法任务状态分析` }}</span>
            </div>
            <div class="sub-title">
              <img
                src="@/assets/biaoti.png"
                style="width: 4rem"
                alt
                class="title-img"
              />
            </div>
          </div>
          <div class="common-content common-content-left-one">
            <div
              v-if="
                taskStatusList.water.count && taskStatusList.water.count > 0
              "
            >
              <div class="analysis-name">
                水环境<span>{{
                  taskStatusList.water.count ? taskStatusList.water.count : 0
                }}</span
                >件:
              </div>
              <div class="analysis-line">
                <div class="analysis-line-one">
                  <div v-for="i in 26" :key="i"></div>
                </div>
                <div class="analysis-line-two">
                  <div v-for="i in oneLine" :key="i"></div>
                </div>
                <div class="analysis-line-three">
                  已完成{{
                    taskStatusList.water.completed
                      ? taskStatusList.water.completed
                      : 0
                  }}件
                </div>
              </div>
            </div>
            <div
              v-if="taskStatusList.air.count && taskStatusList.air.count > 0"
            >
              <div class="analysis-name">
                大气环境<span>{{
                  taskStatusList.air.count ? taskStatusList.air.count : 0
                }}</span
                >件:
              </div>
              <div class="analysis-line">
                <div class="analysis-line-one">
                  <div v-for="i in 26" :key="i"></div>
                </div>
                <div class="analysis-line-two">
                  <div v-for="i in twoLine" :key="i"></div>
                </div>
                <div class="analysis-line-three">
                  已完成{{
                    taskStatusList.air.completed
                      ? taskStatusList.air.completed
                      : 0
                  }}件
                </div>
              </div>
            </div>
            <div
              v-if="
                taskStatusList.pollution.count &&
                taskStatusList.pollution.count > 0
              "
            >
              <div class="analysis-name">
                重点污染源<span>{{
                  taskStatusList.pollution.count
                    ? taskStatusList.pollution.count
                    : 0
                }}</span
                >件:
              </div>
              <div class="analysis-line">
                <div class="analysis-line-one">
                  <div v-for="i in 26" :key="i"></div>
                </div>
                <div class="analysis-line-two">
                  <div v-for="i in threeLine" :key="i"></div>
                </div>
                <div class="analysis-line-three">
                  已完成{{
                    taskStatusList.pollution.completed
                      ? taskStatusList.pollution.completed
                      : 0
                  }}件
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="left-content">
          <div class="common-title">
            <div class="title title-top">
              <span>{{ `本周任务` }}</span>
              <div>
                <div
                  :class="{ 'type-active': taskTypes === '完成' }"
                  @click="taskTypes = '完成'"
                >
                  完成
                </div>
                <div
                  :class="{ 'type-active': taskTypes === '未完成' }"
                  @click="taskTypes = '未完成'"
                >
                  未完成
                </div>
              </div>
            </div>
            <div class="sub-title">
              <img
                src="@/assets/biaoti.png"
                style="width: 4rem"
                class="title-img"
              />
            </div>
          </div>
          <div
            class="common-content common-content-left-two"
            v-if="taskTypes === '完成'"
          >
            <template v-if="taskList.completedList.length <= 5">
              <div
                v-for="(item, index) in taskList.completedList"
                :key="index"
                class="taskList-item"
              >
                <div class="name-btn">
                  {{
                    item.pollutantType == 1
                      ? '水环境'
                      : item.pollutantType == 2
                      ? '大气环境'
                      : '重点污染源'
                  }}
                </div>
                <div class="content" :title="item.title">{{ item.title }}</div>
                <div class="line"></div>
                <div class="times">{{ item.createTime }}</div>
              </div>
            </template>
            <swiper
              :options="swiperOption"
              class="common-content common-content-left-two"
              v-if="taskList.completedList.length > 5"
            >
              <swiper-slide
                v-for="(item, index) in taskList.completedList"
                :key="index"
                class="taskList-item"
              >
                <div class="name-btn">
                  {{
                    item.pollutantType == 1
                      ? '水环境'
                      : item.pollutantType == 2
                      ? '大气环境'
                      : '重点污染源'
                  }}
                </div>
                <div class="content" :title="item.title">{{ item.title }}</div>
                <div class="line"></div>
                <div class="times">{{ item.createTime }}</div>
              </swiper-slide>
            </swiper>
          </div>
          <div
            class="common-content common-content-left-two"
            v-if="taskTypes === '未完成'"
          >
            <template v-if="taskList.undoneList.length <= 5">
              <div
                v-for="(item, index) in taskList.undoneList"
                :key="index"
                class="taskList-item"
              >
                <div class="name-btn">
                  {{
                    item.type == 1
                      ? '水环境'
                      : item.type == 2
                      ? '大气环境'
                      : '重点污染源'
                  }}
                </div>
                <div class="content" :title="item.title">{{ item.title }}</div>
                <div class="line"></div>
                <div class="times">{{ item.createTime }}</div>
              </div>
            </template>
            <swiper
              :options="swiperOption"
              class="common-content common-content-left-two"
              v-if="taskList.undoneList.length > 5"
            >
              <swiper-slide
                v-for="(item, index) in taskList.undoneList"
                :key="index"
                class="taskList-item"
              >
                <div class="name-btn">
                  {{
                    item.type == 1
                      ? '水环境'
                      : item.type == 2
                      ? '大气环境'
                      : '重点污染源'
                  }}
                </div>
                <div class="content" :title="item.title">{{ item.title }}</div>
                <div class="line"></div>
                <div class="times">{{ item.createTime }}</div>
              </swiper-slide>
            </swiper>
          </div>
        </div>
      </section>
      <section
        class="content-right"
        style="transition: all 1s"
        :style="{
          opacity: !displayState ? '1' : '0',
          visibility: !displayState ? '' : 'hidden'
        }"
      >
        <div class="right-content">
          <div class="common-title">
            <div class="title title-top">
              <span>{{ `任务完成情况分析` }}</span>
              <!-- <div>
                <div
                  :class="{ 'type-active': dayType === '7天' }"
                  @click="dayType = '7天'"
                >
                  7天
                </div>
                <div
                  :class="{ 'type-active': dayType === '30天' }"
                  @click="dayType = '30天'"
                >
                  30天
                </div>
              </div> -->
            </div>
            <div class="sub-title">
              <img
                src="@/assets/biaoti.png"
                style="width: 4rem"
                alt
                class="title-img"
              />
            </div>
          </div>
          <div
            class="common-content common-content-right-one"
            style="border: none"
          >
            <TaskLine
              :id="'department-TaskLine'"
              :width="'4rem'"
              :height="'2rem'"
              :propData="complete"
            />
          </div>
        </div>
        <div class="right-content">
          <div class="common-title">
            <div class="title">{{ `事件处理率分析` }}</div>
            <div class="sub-title">
              <img
                src="@/assets/biaoti.png"
                style="width: 4rem"
                alt
                class="title-img"
              />
            </div>
          </div>
          <div
            class="common-content common-content-right-two"
            style="border: none"
          >
            <TaskLineSmooth
              :id="'department-TaskLineSmooth'"
              :width="'4rem'"
              :height="'2rem'"
              :propData="taskHandling"
            />
          </div>
        </div>
      </section>
      <!-- 空气、水、巡岗 -->
      <section
        :class="{ 'left-region-bottom': true }"
        style="transition: all 1s"
        :style="{
          opacity: !displayBottomState ? '1' : '0',
          visibility: !displayBottomState ? '' : 'hidden'
        }"
        v-if="bottomDivType == 'site'"
      >
        <!-- 下面第一栏(空气、水、巡岗) -->
        <div class="common-title left-part-one">
          <div class="title title-top">
            <span>{{ `任务信息` }}</span>
          </div>
          <div class="sub-title">
            <img
              src="@/assets/biaoti.png"
              alt
              class="title-img"
              style="width: 3.9rem"
            />
          </div>
          <div class="common-content common-content-one">
            <div class="one-top">
              <img
                src="@/assets/touxiang.png"
                alt=""
                style="width: 0.42rem; height: 0.42rem"
              />
              <div class="one-top-div">
                <div>{{ taskDetails.taskInfo.departmentName }}</div>
                <div>{{ taskDetails.taskInfo.createTime }}</div>
              </div>
            </div>
            <div class="one-bottom">
              <div class="one-bottom-one">
                <div>{{ TASKTYPE[taskDetails.taskInfo.pollutantType] }}</div>
                <div :title="taskDetails.taskInfo.title">
                  {{ taskDetails.taskInfo.title }}
                </div>
              </div>
              <div class="one-bottom-two" :title="taskDetails.taskInfo.content">
                {{ taskDetails.taskInfo.content }}
              </div>
              <div class="one-bottom-three">
                任务类型：{{ TASKTYPELIST[taskDetails.taskInfo.pollutantType] }}
              </div>
            </div>
            <div class="one-bottom-four">
              位置：{{ taskDetails.taskInfo.address }}
            </div>
          </div>
        </div>
        <!-- 下面第二栏(空气、水) -->
        <div class="common-title left-part-one" v-if="divType == 'site'">
          <div class="title title-top">
            <span>{{ `告警参数` }}</span>
          </div>
          <div class="sub-title">
            <img
              src="@/assets/biaoti.png"
              alt
              class="title-img"
              style="width: 5.18rem"
            />
          </div>
          <div class="common-content common-content-two">
            <div class="two-table" style="">
              <div class="two-thead">
                <div class="td">指标项</div>
                <div class="td">数值</div>
                <div class="td">标准</div>
                <div class="td">单位</div>
                <div class="td">时间</div>
              </div>
              <div class="two-tbody">
                <swiper
                  :options="swiperOption"
                  style="height: 2rem"
                  v-if="
                    taskDetails.enforcementTaskPojo.alarmList &&
                    taskDetails.enforcementTaskPojo.alarmList.length > 5
                  "
                >
                  <swiper-slide
                    v-for="(item, index) in taskDetails.enforcementTaskPojo
                      .alarmList"
                    :key="index"
                    class="tr"
                  >
                    <div class="td" v-if="airTypeNamePub[item.keyWord]">
                      {{
                        item.keyWord
                          ? airTypeNamePub[item.keyWord]
                          : airTypeNamePub[item.keyWord]
                      }}
                    </div>
                    <div class="td" v-else>
                      {{ item.keyWord }}
                    </div>
                    <div class="td">
                      {{ item.alarmValue ? item.alarmValue : '-' }}
                    </div>
                    <div class="td">
                      {{ item.alarmStandard ? item.alarmStandard : '-' }}
                    </div>
                    <div class="td">{{ item.unit ? item.unit : '-' }}</div>
                    <div class="td">
                      {{ item.alarmTime ? item.alarmTime.slice(5, 16) : '-' }}
                    </div>
                  </swiper-slide>
                </swiper>
                <template
                  v-if="
                    taskDetails.enforcementTaskPojo.alarmList &&
                    taskDetails.enforcementTaskPojo.alarmList.length <= 5
                  "
                >
                  <div
                    class="tr"
                    v-for="(item, index) in taskDetails.enforcementTaskPojo
                      .alarmList"
                    :key="index"
                  >
                    <div class="td" v-if="airTypeNamePub[item.keyWord]">
                      {{ item.keyWord ? airTypeNamePub[item.keyWord] : '' }}
                    </div>
                    <div class="td" v-else>
                      {{ item.keyWord }}
                    </div>
                    <div class="td">
                      {{ item.alarmValue ? item.alarmValue : '-' }}
                    </div>
                    <div class="td">
                      {{ item.alarmStandard ? item.alarmStandard : '-' }}
                    </div>
                    <div class="td">{{ item.unit ? item.unit : '-' }}</div>
                    <div class="td">
                      {{ item.alarmTime ? item.alarmTime.slice(5, 16) : '-' }}
                    </div>
                  </div>
                </template>
              </div>
            </div>
          </div>
        </div>
        <!-- 下面第二栏(巡岗) -->
        <div class="common-title left-part-one" v-if="divType == 'patrol'">
          <div class="title title-top">
            <span>{{ `任务附件` }}</span>
          </div>
          <div class="sub-title">
            <img
              src="@/assets/biaoti.png"
              alt
              class="title-img"
              style="width: 5.18rem"
            />
          </div>
          <div class="common-content common-content-two" style="padding: 0">
            <div
              class="two-bottom"
              v-if="
                taskDetails.enforcementTaskPojo.imageUrls &&
                taskDetails.enforcementTaskPojo.imageUrls.length !== 0
              "
            >
              <!-- 左侧箭头 -->
              <div class="left-arrow1">
                <svgicon
                  name="left"
                  color="#FFFFFF"
                  class="svg-arrow"
                ></svgicon>
              </div>
              <swiper :options="swiperImgOption1">
                <swiper-slide
                  v-for="(item, index) in taskDetails.enforcementTaskPojo
                    .imageUrls"
                  :key="index"
                >
                  <img :src="item" alt="" />
                </swiper-slide>
                <div class="swiper-pagination" slot="pagination"></div>
              </swiper>
              <!-- 右侧箭头 -->
              <div class="right-arrow1">
                <svgicon
                  name="right"
                  color="#FFFFFF"
                  class="svg-arrow"
                ></svgicon>
              </div>
            </div>
            <div class="two-bottom" v-else>
              <img src="@/assets/<EMAIL>" alt="" />
            </div>
          </div>
        </div>
        <!-- 下面第三栏(空气、水、巡岗) -->
        <div class="common-title left-part-one">
          <div class="title title-top">
            <span>{{ `任务反馈` }}</span>
          </div>
          <div class="sub-title">
            <img
              src="@/assets/biaoti.png"
              alt
              class="title-img"
              style="width: 7.62rem"
            />
          </div>
          <div class="common-content common-content-three">
            <div class="three-left">
              <div class="three-left-top">
                <div>
                  <img
                    src="@/assets/touxiang.png"
                    alt=""
                    style="width: 0.42rem; height: 0.42rem"
                  />
                  <div class="one-top-div">
                    <div>{{ taskDetails.taskUserRel.departmentName }}</div>
                    <div>{{ taskDetails.taskUserRel.createTime }}</div>
                  </div>
                </div>
                <img
                  src="@/assets/<EMAIL>"
                  alt=""
                  v-if="taskDetails.taskUserRel.status == 2"
                />
                <img src="@/assets/jxz.png" alt="" v-else />
              </div>
              <div class="three-left-bottom">
                <div class="">描述：</div>
                <div>
                  {{ taskDetails.taskUserRel.remark }}
                </div>
              </div>
            </div>
            <div class="three-lines">
              <div></div>
            </div>
            <div class="three-right">
              <div class="three-right-top">附件：</div>
              <div
                class="three-right-bottom"
                v-if="
                  taskDetails.taskUserRel.annexUrlList &&
                  taskDetails.taskUserRel.annexUrlList.length !== 0
                "
              >
                <!-- 左侧箭头 -->
                <div class="left-arrow">
                  <svgicon
                    name="left"
                    color="#FFFFFF"
                    class="svg-arrow"
                  ></svgicon>
                </div>
                <swiper :options="swiperImgOption">
                  <swiper-slide
                    v-for="(item, index) in taskDetails.taskUserRel
                      .annexUrlList"
                    :key="index"
                  >
                    <img :src="item" alt="" />
                  </swiper-slide>
                  <div class="swiper-pagination" slot="pagination"></div>
                </swiper>
                <!-- 右侧箭头 -->
                <div class="right-arrow">
                  <svgicon
                    name="right"
                    color="#FFFFFF"
                    class="svg-arrow"
                  ></svgicon>
                </div>
              </div>
              <div class="three-right-bottom" v-else>
                <img src="@/assets/<EMAIL>" alt="" />
              </div>
            </div>
          </div>
        </div>
      </section>
      <!-- 车辆 -->
      <section
        :class="{ 'left-region-bottom': true }"
        style="transition: all 1s"
        :style="{
          opacity: !displayBottomState ? '1' : '0',
          visibility: !displayBottomState ? '' : 'hidden',
          'justify-content': 'center'
        }"
        v-show="bottomDivType == 'car'"
      >
        <!-- 下面第一栏(车辆) -->
        <div class="common-title left-part-one">
          <div class="title title-top">
            <span>{{ `作业信息` }}</span>
          </div>
          <div class="sub-title">
            <img
              src="@/assets/biaoti.png"
              alt
              class="title-img"
              style="width: 7.5rem"
            />
          </div>
          <div class="common-content-car-one">
            <div class="task-top">
              <div
                style="
                  border-bottom: 1px solid rgba(255, 255, 255, 0.35);
                  padding-bottom: 0.05rem;
                  margin-bottom: 0.08rem;
                "
              >
                <div style="display: flex; align-items: center">
                  <img src="@/assets/zycl.png" style="width: 0.3rem" />
                  <div style="color: #00eaff; margin-left: 0.2rem">
                    作业车辆
                  </div>
                </div>
              </div>
              <div style="white-space: nowrap">
                <div>
                  <p>
                    <span class="task-top-name">车辆类型：</span>
                    <span class="task-top-text">
                      {{ carDetails.typeName ? carDetails.typeName : '' }}
                    </span>
                  </p>
                  <p>
                    <span class="task-top-name">车牌号码：</span>
                    <span class="task-top-text">
                      {{ carDetails.license ? carDetails.license : '' }}
                    </span>
                  </p>
                  <p>
                    <span class="task-top-name">所属公司/街道：</span>
                    <span class="task-top-text">
                      {{ carDetails.deptName ? carDetails.deptName : '' }}
                    </span>
                  </p>
                  <p>
                    <span class="task-top-name">当前车速：</span>
                    <span class="task-top-text">{{
                      carDetails.speed + ' km/h'
                    }}</span>
                  </p>
                  <p style="display: flex; align-items: center">
                    <span class="task-top-name">车辆里程：</span>
                    <span class="mileage">
                      <div
                        v-if="!mileLoadingSuccess"
                        class="task-top-text"
                        style="margin-right: 0.1rem"
                      >
                        <a-icon type="loading" /> 获取数据中...
                      </div>
                      <div
                        v-else
                        v-for="(item, index) in mileCycle == '日'
                          ? carDetails.todayMileStringList
                          : carDetails.totalMileStringList"
                        :key="index + 'num'"
                        :class="index === 4 ? 'mile2' : 'mile'"
                      >
                        {{ item }}
                      </div>
                      <div>km</div>
                      <div
                        class="mile-cycle cycle-of-day"
                        :class="{ 'mile-cycle-selected': mileCycle == '日' }"
                        @click="mileCycle = '日'"
                      >
                        日
                      </div>
                      <div
                        class="mile-cycle"
                        :class="{ 'mile-cycle-selected': mileCycle == '总' }"
                        @click="mileCycle = '总'"
                      >
                        总
                      </div>
                    </span>
                  </p>
                </div>
              </div>
              <div class="playback-box">
                <img src="@/assets/<EMAIL>" alt />
                <div class="playback">
                  <div
                    @click="
                      showPlayBack = true
                      displayState = true
                    "
                  >
                    <img src="@/assets/gjhf.png" alt />
                    <span>轨迹回放</span>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="task-bottom playback-form"
              :class="showPlayBack ? 'show-playback' : ''"
            >
              <div>回放时间：</div>
              <a-form-model
                layout="inline"
                :model="form"
                ref="form"
                style="display: flex; align-items: center; flex-wrap: wrap"
              >
                <a-form-model-item class="form-item" prop="startTime">
                  <a-date-picker
                    placeholder="默认今日0点"
                    :disabled-date="disabledDate"
                    show-time
                    :allowClear="false"
                    v-model="form.startTime"
                    valueFormat="YYYY-MM-DD HH:mm"
                    format="YYYY-MM-DD HH:mm"
                  />
                </a-form-model-item>
                <span>至</span>
                <a-form-model-item class="form-item" prop="endTime">
                  <a-date-picker
                    placeholder="默认当前时间"
                    v-model="form.endTime"
                    :allowClear="false"
                    show-time
                    :disabled-date="disabledDate2"
                    valueFormat="YYYY-MM-DD HH:mm"
                    format="YYYY-MM-DD HH:mm"
                  />
                </a-form-model-item>
                <div class="query-button">
                  <a-button
                    type="primary"
                    :loading="loading"
                    @click="getPlaybackTrajectory"
                  >
                    查询
                  </a-button>
                </div>
                <div class="multiple-speed">
                  <div
                    class="button"
                    :class="{ 'button-selected': disabledButton === 0.5 }"
                    @click="multipleSpeed(0.5)"
                  >
                    0.5x
                  </div>
                  <div
                    class="button"
                    :class="{ 'button-selected': disabledButton === 1 }"
                    @click="multipleSpeed(1)"
                  >
                    1x
                  </div>
                  <div
                    class="button"
                    :class="{ 'button-selected': disabledButton === 2 }"
                    @click="multipleSpeed(2)"
                  >
                    2x
                  </div>
                  <div
                    class="button"
                    :class="{ 'button-selected': disabledButton === 4 }"
                    @click="multipleSpeed(4)"
                  >
                    4x
                  </div>
                </div>
                <div class="play-ctr">
                  <a-button type="primary" @click="playbackControl">{{
                    buttonText
                  }}</a-button>
                  <a-button type="primary" @click="stopPlayback">结束</a-button>
                </div>
              </a-form-model>
            </div>
          </div>
        </div>
        <!-- 下面第二栏(车辆) -->
        <div class="common-title left-part-one">
          <div class="title title-top">
            <span @click="showMoreVideo" style="cursor: pointer">
              {{ `任务车实况巡查>>` }}
            </span>
            <div>
              <a-select
                v-model="carStreet"
                v-show="hasVideo"
                class="select-main"
                @change="changeStreet"
                style="width: 1.05rem"
              >
                <a-icon
                  slot="suffixIcon"
                  type="caret-down"
                  style="color: rgba(0, 234, 255, 1); width: 14px; height: 8px"
                />
                <a-select-option
                  :value="item.deptId"
                  v-for="item in deptStreetList"
                  :key="item.deptId"
                >
                  {{ item.deptName }}</a-select-option
                >
              </a-select>
              <a-select
                v-model="carId"
                v-show="hasVideo"
                @change="changeCar"
                class="select-main"
                style="margin-left: 0.2rem; width: 1.05rem"
              >
                <a-icon
                  slot="suffixIcon"
                  type="caret-down"
                  style="color: rgba(0, 234, 255, 1); width: 14px; height: 8px"
                />
                <a-select-option
                  :value="item.carId"
                  v-for="item in deptCarList"
                  :key="item.carId"
                >
                  {{ item.license }}</a-select-option
                >
              </a-select>
            </div>
          </div>
          <div class="sub-title">
            <img
              src="@/assets/biaoti.png"
              alt
              class="title-img"
              style="width: 3.9rem"
            />
          </div>
          <div class="common-content common-content-car-two">
            <div
              v-show="flvPlayer === null"
              style="
                background: rgba(8, 45, 120, 0.5);
                width: 4.6rem;
                height: 2.5rem;
                font-size: 0.2rem;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
              "
            >
              <img
                src="@/assets/shipin.png"
                alt=""
                style="width: 1.11rem; height: 1.04rem; margin-bottom: 0.3rem"
              />
              该摄像头不在线
            </div>
            <div
              v-show="liveTimeOut"
              style="
                background: rgba(8, 45, 120, 0.5);
                display: flex;
                width: 4.6rem;
                height: 2.5rem;
                font-size: 0.2rem;
                flex-direction: column;
                align-items: center;
                justify-content: center;
              "
            >
              <img
                src="@/assets/noCamera.png"
                alt=""
                style="width: 1.11rem; height: 1.04rem; margin-bottom: 0.3rem"
              />
              <br />
              观看已超时，请切换摄像头或刷新
            </div>
            <video
              v-show="flvPlayer !== null && !liveTimeOut"
              id="videoElement"
              style="width: 4.6rem; height: 2.5rem; background: #000"
              controls="true"
            ></video>
            <div class="change-camera" v-show="hasVideo">
              <span
                :class="cameraDirection == 0 ? 'change-camera-selected' : ''"
                @click="changeCameraDirection(0)"
                >前</span
              >
              <img src="@/assets/<EMAIL>" width="2" height="25" />
              <span
                :class="cameraDirection == 1 ? 'change-camera-selected' : ''"
                @click="changeCameraDirection(1)"
                >后</span
              >
            </div>
          </div>
        </div>
      </section>
    </section>
  </section>
</template>

<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator'
import flvjs from 'flv.js'
import GaoDeMap from '@/components/GaoDeMap/index.vue'
import { Swiper, SwiperSlide } from 'vue-awesome-swiper'
import TaskLineSmooth from '@/components/Charts/TaskLineSmooth.vue'
import TaskLine from '@/components/Charts/TaskLine.vue'
import LineChartDashed from '@/components/Charts/LineChartDashed.vue'
import { getStreetOfficeList } from '@/api/task-management'
import { keepAlive } from '@/api/vehicles'
import moment from 'moment'
import { aqiQualityTrend } from '@/api/air'
import 'video.js/dist/video-js.css'
import 'videojs-contrib-hls'
import 'videojs-flash'
import {
  Table,
  Icon,
  Empty,
  Avatar,
  Select,
  DatePicker,
  TimePicker,
  Input,
  Button,
  message,
  FormModel,
  Radio
} from 'ant-design-vue'
import {
  getEnforcement,
  getEnforcementTaskId,
  getAqiQuality,
  getTwentyFourHour,
  getTaskCountList,
  getTaskInfo
} from '@/api/task-management'
import { getMonitorItemDetailRecord } from '@/api/water'
import { socketUrl } from '@/utils/index'
@Component({
  name: 'StreetDetail',
  components: {
    GaoDeMap,
    Swiper,
    SwiperSlide,
    TaskLineSmooth,
    ATable: Table,
    AIcon: Icon,
    AEmpty: Empty,
    AAvatar: Avatar,
    ASelect: Select,
    ADatePicker: DatePicker,
    ATimePicker: TimePicker,
    AInput: Input,
    AButton: Button,
    AFormModel: FormModel,
    AFormModelItem: FormModel.Item,
    ARadioGroup: Radio.Group,
    ARadioButton: Radio.Button,
    TaskLine,
    LineChartDashed,
    ASelectOption: Select.Option
  }
})
export default class extends Vue {
  // 地图样式
  private mapStyle = 'amap://styles/0739a6632d849ae1b5e6f1c52f7d12c3'
  private oneLine: any = Number((26 * (34 / 62)).toFixed(0))
  private twoLine: any = Number((26 * (64 / 81)).toFixed(0))
  private threeLine: any = Number((26 * (14 / 49)).toFixed(0))
  private swiperOption = {
    direction: 'vertical',
    slidesPerView: 5,
    slidesPerGroup: 1,
    loop: true,
    autoplay: {
      delay: 3500,
      disableOnInteraction: false
    }
  }
  private airTypeNamePub = {
    NO2: 'NO₂',
    O3: 'O₃',
    PM10: 'PM₁₀',
    'PM2.5': 'PM₂.₅',
    NOx: 'NOx',
    NO: 'NO',
    SO2: 'SO₂',
    CO: 'CO'
  }
  private cameraDirection = 0
  // car---车辆   site---站点
  private bottomDivType: any = 'site'
  private displayState = false
  private displayBottomState = true
  private queryResultCar: any = ''
  private fullScreen() {
    this.displayState = !this.displayState
    if (!this.displayBottomState) {
      this.displayBottomState = true
    }
  }
  private swiperImgOption = {
    pagination: {
      el: '.swiper-pagination',
      clickable: true // 允许点击小圆点跳转
    },
    direction: 'horizontal',
    loop: true,
    autoplay: {
      delay: 2500,
      disableOnInteraction: true
    },
    navigation: {
      nextEl: '.right-arrow',
      prevEl: '.left-arrow'
    }
  }
  private swiperImgOption1 = {
    pagination: {
      el: '.swiper-pagination',
      clickable: true // 允许点击小圆点跳转
    },
    direction: 'horizontal',
    loop: true,
    autoplay: {
      delay: 3500,
      disableOnInteraction: true
    },
    navigation: {
      nextEl: '.right-arrow1',
      prevEl: '.left-arrow1'
    }
  }
  // site:空气、水、重污    patrol:应急    car:车辆
  private divType: any = 'site'
  // 执法任务状态分析
  private taskStatusList: any = {
    water: {},
    air: {},
    pollution: {}
  }
  // 本周任务
  private taskList: any = {
    completedList: [],
    undoneList: []
  }
  // 任务完成情况分析
  private complete: any = {
    bottomList: [],
    dataList: [],
    dataList1: []
  }
  // 事件处理率分析
  private taskHandling: any = {
    bottomList: [],
    dataList: []
  }
  // 本周任务类型
  private taskTypes: any = '未完成'
  // 任务完成情况分析类型
  private dayType: any = '7天'
  private TASKTYPE: any = {
    '1': '水',
    '2': '空气',
    '3': '重污',
    '4': '巡岗',
    '5': '重污'
  }
  private TASKTYPELIST: any = {
    '1': '水环境',
    '2': '大气环境',
    '3': '重点污染源',
    '4': '巡岗报警',
    '5': '重点污染源'
  }
  private jointEnforcementMarker: any[] = []
  private taskId: any = null
  private enforcementTaskId: any = null
  private mileCycle = '日'
  private disabledDate(current: any) {
    return current >= moment().endOf('day')
  }
  private disabledDate2(current: any) {
    return (
      current >= moment().endOf('day') ||
      current > moment(this.form.startTime).add(2, 'days') ||
      current < moment(this.form.startTime)
    )
  }
  mounted() {
    console.log('routeQuery----->', this.$route.query.type)
    this.getStreetOfficeList()
    this.getTaskStatistics()
    // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
    //@ts-ignore
    // eslint-disable-next-line no-undef
    this.$bus.on('checkType', (res: any) => {
      this.divType = res[0].type
      this.bottomDivType = res[0].bottomDivType
      this.enforcementTaskId = res[0].id
      this.taskId = res[0].taskId
      this.getEnforcementTaskId(this.taskId, this.enforcementTaskId)
      this.displayBottomState = false
      this.displayState = false
      // this.closeCarTaskDetail();
    })
    // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
    //@ts-ignore
    // eslint-disable-next-line no-undef
    this.$bus.on('checkTypeCar', (res: any) => {
      this.bottomDivType = res[0].bottomDivType
      this.displayBottomState = false
      this.displayState = true
    })
    this.connect()
  }
  beforeDestroy() {
    clearInterval(this.requestTime)
    if (this.flvPlayer) {
      this.flvPlayer.pause()
      this.flvPlayer.unload()
      this.flvPlayer.detachMediaElement()
      this.flvPlayer.destroy()
      this.flvPlayer = null
    }
    // @ts-ignore
    this.$bus.off('checkType')
    // @ts-ignore
    this.$bus.off('checkTypeCar')
  }
  // 获取任务图表信息
  private getTaskStatistics() {
    getTaskCountList(this.departmentId).then((res) => {
      console.log(res.data.data)
      const data = res.data.data
      // 地图站点
      this.jointEnforcementMarker = data.enforcementTaskPojoList
      // 执法任务状态分析
      for (const item of res.data.data.taskStatuses) {
        if (item.type == 1) {
          this.taskStatusList.water = {}
          this.taskStatusList.water.count = item.count
          this.taskStatusList.water.completed = item.completed
          this.oneLine = Number((26 * (item.completed / item.count)).toFixed(0))
        } else if (item.type == 2) {
          this.taskStatusList.air = {}
          this.taskStatusList.air.count = item.count
          this.taskStatusList.air.completed = item.completed
          this.twoLine = Number((26 * (item.completed / item.count)).toFixed(0))
        } else if (item.type == 5) {
          this.taskStatusList.pollution = {}
          this.taskStatusList.pollution.count = item.count
          this.taskStatusList.pollution.completed = item.completed
          this.threeLine = Number(
            (26 * (item.completed / item.count)).toFixed(0)
          )
        }
      }
      // 本周任务
      this.taskList = {
        completedList: res.data.data.completeTask,
        undoneList: res.data.data.noCompleteTask
      }
      // 本周任务（完成）
      if (this.taskList.completedList.length > 0) {
        for (const item of this.taskList.completedList) {
          item.createTime = item.createTime
            ? moment(new Date(item.createTime)).format('MM月DD日 HH:mm')
            : ''
        }
      }
      // 本周任务（未完成）
      if (this.taskList.undoneList.length > 0) {
        for (const item of this.taskList.undoneList) {
          item.createTime = item.createTime
            ? moment(new Date(item.createTime)).format('MM月DD日 HH:mm')
            : ''
        }
      }

      // 任务完成情况分析
      this.complete = {
        bottomList: [],
        dataList: [],
        dataList1: []
      }
      // 事件处理率分析
      this.taskHandling = {
        bottomList: [],
        dataList: []
      }
      for (const item of res.data.data.taskCompletionList) {
        this.complete.bottomList.push(item.date.slice(5, 10))
        this.complete.dataList.push(item.undone ? item.undone : 0)
        this.complete.dataList1.push(item.completed ? item.completed : 0)
        this.taskHandling.bottomList.push(item.date.slice(5, 10))
        this.taskHandling.dataList.push(
          item.processingRate ? item.processingRate : 0
        )
      }
    })
  }
  // 切换div类型
  private checkDivType(text: any) {
    this.divType = text
  }
  // 任务详情
  private taskDetails: any = {
    taskInfo: {},
    taskUserRel: {},
    enforcementTaskPojo: {}
  }
  // 获取任务详情
  private getEnforcementTaskId(taskId: any, enforcementTaskId: any) {
    getTaskInfo(taskId, enforcementTaskId).then((res: any) => {
      if (res.data.data) {
        console.log(res.data.data)
        const reg = /png|jpg|jpeg|bmp|gif/i
        const list = []
        if (res.data.data.taskUserRel.annexUrlList) {
          for (const item of res.data.data.taskUserRel.annexUrlList) {
            if (reg.test(item)) {
              list.push(item)
            }
          }
          res.data.data.taskUserRel.annexUrlList = list
        } else {
          res.data.data.taskUserRel.annexUrlList = []
        }
        this.taskDetails = res.data.data
      }
    })
  }
  // 街道下拉框
  private defaultStreetValue: any = '0'
  private streetList: any = []
  private departmentId: any = undefined
  private streetChange(val: any) {
    this.defaultStreetValue = val
    if (this.streetList[val].departmentId == '') {
      this.departmentId = undefined
      this.socket.send(JSON.stringify({ code: 2 }))
      this.getTaskStatistics()
      return
    }
    this.departmentId = this.streetList[val].departmentId
    this.socket.send(
      JSON.stringify({
        code: 2,
        departmentId: this.streetList[val].departmentId
      })
    )
    this.getTaskStatistics()
  }
  private getStreetOfficeList() {
    getStreetOfficeList().then((res: any) => {
      console.log(res.data.data)
      res.data.data.unshift({
        departmentId: '',
        departmentName: '全部'
      })
      this.streetList = res.data.data
    })
  }
  // 车辆
  private hasVideo: any = true
  private socket: any = null
  private newCarPoint: any = {}
  private oldCarPoint: any = {}
  private locationRecordList: any = []
  private mapMarker: any = []
  private license = ''
  private carList: any[] = []
  private carDetails: any = {}
  private mileLoadingSuccess = false
  private showTaskDetail(data: any) {
    // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
    // @ts-ignore
    console.log(this.$refs.map.mList)
    this.mileLoadingSuccess = false
    this.carList.find((item: any, index: number) => {
      if (data.license == item.license) {
        this.carDetails = item
      }
    })
    // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
    // @ts-ignore
    this.carDetails.time = moment().format('YYYY-MM-DD HH:mm')
    this.license = data.license
    this.carId = data.carId
    // 获取车辆直播
    this.getCarCamera()
    let needBreak = false
    for (const i in this.deptStreetList) {
      if (needBreak) {
        break
      }
      for (const j in this.deptStreetList[i].carList) {
        if (this.deptStreetList[i].carList[j].license === data.license) {
          this.carStreet = this.deptStreetList[i].deptId
          this.deptCarList = this.deptStreetList[i].carList
          this.carId = this.deptStreetList[i].carList[j].carId
          console.log(this.carStreet, this.deptCarList, this.carId, data)
          this.hasVideo = true
          needBreak = true
          break
        } else {
          this.hasVideo = false
        }
      }
    }
    this.socket.send(
      JSON.stringify({
        code: 8,
        license: data.license,
        type: 1,
        mileageDate:
          new Date().getFullYear() +
          '-' +
          (new Date().getMonth() + 1 < 10
            ? '0' + (new Date().getMonth() + 1)
            : new Date().getMonth() + 1) +
          '-' +
          (new Date().getDate() < 10
            ? '0' + new Date().getDate()
            : new Date().getDate())
      })
    )
    // this.hasTaskDetail = true;
    // 获取任务
    this.socket.send(JSON.stringify({ code: 5, license: data.license }))
  }
  // 切换车辆视频的街道
  private changeStreet() {
    this.deptCarList = this.deptStreetList.filter((item: any) => {
      return item.deptId === this.carStreet
      // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
      // @ts-ignore
    })[0].carList
    this.carId = this.deptCarList[0].carId
    this.changeCar()
  }
  private carTypeText: any = '所有车辆'
  // 连选切换车辆
  changeCar() {
    this.liveTimeOut = false
    this.count = 0
    for (const item of this.mapMarker) {
      if (item.carId === this.carId) {
        this.queryResultCar = item
        this.carTypeText = '所有车辆'
      }
    }
  }
  // 获取车辆视频地址
  private getCarCamera() {
    this.cameraSerial = []
    for (const item of this.cameraList) {
      if (item.carId === this.carId && item.positionType === 0) {
        this.cameraSerial[0] = item.cameraSerial
      }
      if (item.carId === this.carId && item.positionType === 1) {
        this.cameraSerial[1] = item.cameraSerial
      }
    }
    if (this.cameraSerial.length > 0) {
      this.socket.send(
        JSON.stringify({
          code: 4,
          cameraSerial: this.cameraSerial[this.cameraDirection]
        })
      )
      this.videoCameraSerial = this.cameraSerial[this.cameraDirection]
    } else {
      if (this.flvPlayer) {
        this.flvPlayer.pause()
        this.flvPlayer.unload()
        this.flvPlayer.detachMediaElement()
        this.flvPlayer.destroy()
        this.flvPlayer = null
      }
    }
  }
  // 建立连接
  private connect() {
    // this.socket = new WebSocket("ws://192.168.0.135:22000/ws");
    // this.socket = new WebSocket("ws://ep.vankeytech.com:8835/ws");
    // this.socket = new WebSocket("ws://www.jinnq.com:8834/ws");
    this.socket = new WebSocket(socketUrl())
    // 监听socket连接
    this.socket.onopen = this.open
    // 监听socket错误信息
    this.socket.onerror = this.error
    // 监听socket消息
    this.socket.onmessage = this.getMessage
    window.onbeforeunload = () => {
            this.socket.onopen = () => {}
            this.socket.onerror = () => {}
            this.socket.onmessage = () => {}
            this.socket.close();
        };
  }
  private open() {
    console.log('socket连接成功')
    this.send()
  }
  private error() {
    message.error('系统连接错误')
  }
  private carMarker: any = []
  private carTypeList: any = []
  private flvPlayer: any = null
  private deptStreetList: any = []
  private deptCarList: any = []
  private cameraList: any = []
  private carId: any = ''
  private carStreet: any = ''
  private carLicense: any = ''
  private cameraSerial: any = []
  private requestTime: any = ''
  private liveTimeOut = false
  private count = 0
  private videoCameraSerial: any = ''
  // 获得数据
  private getMessage(msg: any) {
    const res: any = JSON.parse(msg.data)
    if (res.code == -2) {
      const mapMarker: any = []
      this.carList = JSON.parse(msg.data).carList
      for (const item of JSON.parse(msg.data).carList) {
        if (item.typeId) {
          mapMarker.push({
            type: item.typeId,
            longitude: item.gps.lng,
            latitude: item.gps.lat,
            license: item.license,
            carId: item.carId,
            deptId: item.deptId
          })
        }
      }
      this.mapMarker = mapMarker
      this.carMarker = mapMarker
    }
    if (res.code == -4) {
      this.liveTimeOut = false
      this.count = 0
      if (this.flvPlayer) {
        this.flvPlayer.pause()
        this.flvPlayer.unload()
        this.flvPlayer.detachMediaElement()
        this.flvPlayer.destroy()
        this.flvPlayer = null
      }
      if (!res.liveAddress.online) {
        return
      }
      if (flvjs.isSupported()) {
        const videoElement = document.getElementById('videoElement')
        this.flvPlayer = flvjs.createPlayer({
          type: 'flv',
          isLive: true,
          hasVideo: true,
          // hasAudio: true,
          url: res.liveAddress.flv
        })
        this.flvPlayer.attachMediaElement(videoElement)
        this.flvPlayer.load()
        this.flvPlayer.play()
        this.flvPlayer.on('error', (err: any) => {
          console.log('err', err)
          message.error('播放失败请重试或切换其他视频')
          this.flvPlayer.pause()
          this.flvPlayer.unload()
          this.flvPlayer.detachMediaElement()
          this.flvPlayer.destroy()
          this.flvPlayer = null
        })
        // this.flvPlayer.on(flvjs.ErrorDetails.NETWORK_EXCEPTION, (err: any) => {
        //   console.log("err", err);
        //   message.error("超市");
        //   this.flvPlayer.pause();
        //   this.flvPlayer.unload();
        //   this.flvPlayer.detachMediaElement();
        //   this.flvPlayer.destroy();
        //   this.flvPlayer = null;
        // });
      }
      clearInterval(this.requestTime)
      this.requestTime = setInterval(() => {
        // this.count++;
        // if (this.count > 8) {
        //   clearInterval(this.requestTime);
        //   this.liveTimeOut = true;
        //   this.flvPlayer.pause();
        //   this.flvPlayer.unload();
        //   this.flvPlayer.detachMediaElement();
        //   this.flvPlayer.destroy();
        //   return;
        // }
        console.log(this.count, 66666666)
        keepAlive(this.videoCameraSerial)
      }, 10 * 1000)
    }
    if (res.code == 3) {
      // 车辆移动
      for (const i in this.mapMarker) {
        const marker = this.mapMarker[i]
        // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
        // @ts-ignore
        if (marker.carId == res.car.carId && marker.type) {
          this.oldCarPoint = marker
          this.newCarPoint = {
            type: res.car.typeId,
            longitude: res.car.gps.lng,
            latitude: res.car.gps.lat,
            license: res.car.license,
            carId: res.car.carId,
            deptId: res.car.deptId
          }
        }
      }
    }
    if (res.code == -7) {
      if (res.locationRecordList.length !== 0) {
        this.locationRecordList = res.locationRecordList
        this.loading = false
      } else {
        this.loading = false
        message.warning('该车辆该时段暂无轨迹信息')
      }
    }
    if (res.code == -8) {
      // 里程处理
      const totalMileStringList = res.countMileage.toString().split('')
      const todayMileStringList = res.mileage.toString().split('')
      const numberOfLoop1 = 5 - totalMileStringList.length
      const numberOfLoop2 = 5 - todayMileStringList.length
      for (let i = 0; i < numberOfLoop1; i++) {
        totalMileStringList.unshift('0')
      }
      for (let i = 0; i < numberOfLoop2; i++) {
        todayMileStringList.unshift('0')
      }
      this.carDetails.totalMileStringList = totalMileStringList
      this.carDetails.todayMileStringList = todayMileStringList
      setTimeout(() => {
        this.mileLoadingSuccess = true
      }, 500)
    }
    if (res.code === -11) {
      // 车辆监控列表
      this.deptStreetList = res.cameraMap.carList
      this.cameraList = res.cameraMap.cameraList
      for (const i in this.deptStreetList) {
        for (const j in this.deptStreetList[i].carList) {
          if (this.deptStreetList[i].carList[j].license == '川AAH355') {
            this.carStreet = this.deptStreetList[i].deptId
            this.deptCarList = this.deptStreetList[i].carList
            this.carId = this.deptCarList[j].carId
            this.cameraSerial.push(this.deptStreetList[0].cameraSerial)
          }
        }
      }
      for (const item of this.cameraList) {
        console.log(item.carId, this.carId, 66666)
        if (item.carId === this.carId && item.positionType === 0) {
          this.cameraSerial[0] = item.cameraSerial
        }
        if (item.carId === this.carId && item.positionType === 1) {
          this.cameraSerial[1] = item.cameraSerial
        }
      }
      console.log(this.cameraSerial)
      this.socket.send(
        JSON.stringify({
          code: 4,
          cameraSerial: this.cameraSerial[this.cameraDirection]
        })
      )
      this.videoCameraSerial = this.cameraSerial[this.cameraDirection]
    }
  }
  private send() {
    this.socket.send(JSON.stringify({ code: 1, token: 'hello123' }))
    this.socket.send(JSON.stringify({ code: 2 }))
    this.socket.send(JSON.stringify({ code: 6 }))
    this.socket.send(JSON.stringify({ code: 11 }))
    this.socket.send(
      JSON.stringify({
        code: 9,
        typeId: 6,
        cycle: 1
      })
    )
    // this.socket.send(JSON.stringify({ code: 5, license: "川A200Z3" }));
  }
  private close() {
    console.log('socket已经关闭')
  }
  // 查看更多视频
  showMoreVideo() {
    this.$router.push('/vehiclesVideo')
  }
  // 切换摄像头前后方向
  changeCameraDirection(direction: number) {
    this.count = 0
    this.liveTimeOut = false
    if (this.cameraSerial[direction]) {
      this.cameraDirection = direction
      this.socket.send(
        JSON.stringify({
          code: 4,
          // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
          // @ts-ignore
          cameraSerial: this.cameraSerial[direction]
        })
      )
      this.videoCameraSerial = this.cameraSerial[direction]
    } else {
      message.error('暂无该方向摄像头')
    }
  }
  private loading = false
  private form = {
    startTime: moment(new Date()).format('YYYY-MM-DD') + ' 00:00:00',
    endTime: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
    animationTime: 30
  }
  private startPlayState: any = false
  private buttonText = '开始'
  // 查询回放轨迹
  private getPlaybackTrajectory(): void {
    const timeDifference =
      +new Date(this.form.endTime) - +new Date(this.form.startTime)
    if (timeDifference <= 1800000) {
      // 结束时间比开始时间少30分钟
      message.error('回放结束时间应大于开始时间30分钟以上')
      return
    }
    this.loading = true
    this.startPlayState = true
    this.socket.send(
      JSON.stringify({
        code: 7,
        license: this.license,
        startTime: this.form.startTime,
        endTime: this.form.endTime
      })
    )
  }
  // 回放状态控制
  playbackControl() {
    if (this.buttonText === '开始') {
      // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
      // @ts-ignore
      this.$refs.map.startPlayback()
      this.buttonText = '暂停'
    } else if (this.buttonText === '暂停') {
      console.log('暂停')
      // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
      // @ts-ignore
      this.$refs.map.suspendPlayback()
      this.buttonText = '继续'
    } else if (this.buttonText === '继续') {
      // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
      // @ts-ignore
      this.$refs.map.resumePlayback()
      this.buttonText = '暂停'
    }
  }
  private disabledButton = 1
  // 倍速播放
  multipleSpeed(multiple: number) {
    // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
    // @ts-ignore
    this.$refs.map.multipleSpeed(multiple)
    this.disabledButton = multiple
    console.log(this.disabledButton)
  }
  // 停止轨迹回放
  private stopPlayback(): void {
    this.loading = false
    this.buttonText = '开始'
    this.startPlayState = false
    this.showPlayBack = true
    this.displayState = false
    for (const i in this.deptStreetList) {
      for (const j in this.deptStreetList[i].carList) {
        if (this.deptStreetList[i].carList[j].license == '川AAH355') {
          this.carStreet = this.deptStreetList[i].deptId
          this.deptCarList = this.deptStreetList[i].carList
          this.carId = this.deptCarList[j].carId
          this.cameraSerial.push(this.deptStreetList[0].cameraSerial)
        }
      }
    }
    for (const item of this.cameraList) {
      console.log(item.carId, this.carId, 66666)
      if (item.carId === this.carId && item.positionType === 0) {
        this.cameraSerial[0] = item.cameraSerial
      }
      if (item.carId === this.carId && item.positionType === 1) {
        this.cameraSerial[1] = item.cameraSerial
      }
    }
    this.hasVideo = true
    // this.bottomDivType = "";
    // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
    // @ts-ignore
    this.$refs.map.stopPlayback()
    this.disabledButton = 1
  }
  private showPlayBack: any = true
  // 关闭车辆详情
  private closeCarTaskDetail() {
    this.buttonText = '开始'
    // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
    // @ts-ignore
    this.$refs.map.stopPlayback()
    this.disabledButton = 1
    this.startPlayState = false
    this.showPlayBack = false
    this.displayState = false
    console.log(this.hasVideo, 66666666666)
    if (!this.hasVideo) {
      this.carStreet = this.deptStreetList[0].deptId
      this.deptCarList = this.deptStreetList.filter((item: any) => {
        return item.deptId === this.carStreet
        // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
        // @ts-ignore
      })[0].carList
      this.carId = this.deptCarList[0].carId
      this.liveTimeOut = false
      this.count = 0
      this.hasVideo = true
      this.getCarCamera()
    }
    /* eslint-disable @typescript-eslint/ban-ts-ignore */
    //@ts-ignore
    this.stopPlayback()
    /* eslint-disable @typescript-eslint/ban-ts-ignore */
    //@ts-ignore
    if (this.$refs.map.carItem) {
      /* eslint-disable @typescript-eslint/ban-ts-ignore */
      //@ts-ignore
      this.$refs.map.clearCarSelected()
    }
    /* eslint-disable @typescript-eslint/ban-ts-ignore */
    //@ts-ignore
    const map = this.$refs.map.maps
    /* eslint-disable @typescript-eslint/ban-ts-ignore */
    //@ts-ignore
    if (this.$refs.map.Polyline) {
      /* eslint-disable @typescript-eslint/ban-ts-ignore */
      //@ts-ignore
      map.remove(this.$refs.map.Polyline)
    }
    /* eslint-disable @typescript-eslint/ban-ts-ignore */
    //@ts-ignore
    if (this.$refs.map.startPoint) {
      /* eslint-disable @typescript-eslint/ban-ts-ignore */
      //@ts-ignore
      map.remove(this.$refs.map.startPoint)
    }
    /* eslint-disable @typescript-eslint/ban-ts-ignore */
    //@ts-ignore
    if (this.$refs.map.endPoint) {
      /* eslint-disable @typescript-eslint/ban-ts-ignore */
      //@ts-ignore
      map.remove(this.$refs.map.endPoint)
    }
  }
}
</script>
