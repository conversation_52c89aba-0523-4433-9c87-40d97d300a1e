<style lang="less" scoped>
.no-data {
  font-size: 0.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
<template>
  <div
    v-if="propData.dataList&&propData.dataList.length"
    :id="id"
    :style="{ height: height, width: width }"
  />
  <div v-else class="no-data" :style="{ height: height, width: width }">
    暂无数据
  </div>
</template>

<script lang="ts">
import * as echarts from 'echarts'
import { Component, Prop, Watch } from 'vue-property-decorator'
import { mixins } from 'vue-class-component'
import ResizeMixin from '@/components/Charts/mixins/resize'
interface AirData {
  bottomList: string[]
  dataList: string[]
  dataList1: string[]
  standard: number | string
  max: number | string
  unit: string
  name: string
}
@Component({
  name: 'LineChartDashed'
})
export default class extends mixins(ResizeMixin) {
  // @Prop({ default: "chart" }) private className!: string;
  @Prop({ default: 'chart' }) private id!: string
  @Prop({ default: '200px' }) private width!: string
  @Prop({ default: '200px' }) private height!: string
  @Prop({ required: true }) private propData!: AirData
  @Prop({ required: false, default: 'rgba(14,156,255,1)' })
  private bgColor!: string
  @Prop({ required: false, default: false }) private smooth!: boolean
  @Prop({ required: false, default: true }) private bgColorState!: boolean
  @Prop({ required: false, default: '' }) private xText!: string
  @Watch('propData', { immediate: true, deep: true }) public onMsgChanged(
    newValue: AirData,
    oldValue: AirData
  ) {
    if (this.propData) {
      if (this.chart) {
        this.chart.clear()
        this.chart.dispose()
        this.chart = null
      }
      this.$nextTick(() => {
        this.initChart()
      })
    }
  }
  mounted() {
    if (this.propData) {
      this.initChart()
    }
  }
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.clear()
    this.chart.dispose()
    this.chart = null
  }

  private initChart() {
    if (this.chart === null || this.chart === undefined) {
      const chartDom = document.getElementById(this.id) as HTMLDivElement
      if (!chartDom) return
      this.chart = echarts.init(chartDom)
    }
    const option = {
      backgroundColor: 'transparent',
      legend: {
        //图例组件，颜色和名字
        right: '10%',
        top: '2%',
        itemGap: 16,
        itemWidth: 12,
        itemHeight: 3,
        textStyle: {
          color: '#C5E8F4',
          fontSize: 12
        },
        show: false,
        selectedMode: false
      },
      grid: {
        height: '80%',
        left: '0%',
        right:'0%',
        top: '10%',
        bottom: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        name: '',
        nameTextStyle: {
          color: '#fff',
          lineHeight: -30,
          align: 'center',
          verticalAlign: 'bottom'
        },
        data: this.propData.bottomList.map(v=>v.slice(5,10)),
        axisLabel: {
          textStyle: {
            fontSize: 12,
            color: 'white'
          }
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        }
      },
      yAxis: [
        {
          type: 'value',
          name: '',
          min: 0,
          max: 100,
          show: true,
          nameTextStyle: {
            color: '#fff',
            shadowOffsetX: 50
            // align: "center"
          },
          axisLabel: {
            formatter: '{value} %',
            textStyle: {
              fontSize: 12,
              color: 'white'
            }
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          splitNumber: 5,
          splitLine: {
            lineStyle: {
              type: "dashed",
              color: 'RGBA(2, 39, 75, 1)'
            }
          }
        },
        {
          type: 'value',
          name: '',
          min: 0,
          // max: Math.max(...this.propData.dataList.map((v:any)=>v.useElectricity)),
          show: false,
          nameTextStyle: {
            color: '#fff',
            shadowOffsetX: 50
            // align: "center"
          },
          axisLabel: {
            formatter: '{value}',
            textStyle: {
              fontSize: 12,
              color: 'white'
            }
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          splitNumber: 5,
          splitLine: {
            lineStyle: {
              type: "dashed",
              color: 'RGBA(2, 39, 75, 1)'
            }
          }
        }
      ],
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
          label: {
            backgroundColor: '#6a7985'
          }
        },
        confine: true,
        backgroundColor: "rgba(11, 11, 11, .4)",
        borderColor: "rgba(11, 11, 11, .4)",
        formatter:(params:any)=>{
          const i = params[0].dataIndex
          const data = this.propData.dataList[i]
          // style="color:${data['complianceRate']!=100?'#FF4D46':''}"
          return `
            <div style="border-radius: 0.06rem;padding: 0.1rem 0.13rem; background-color: rgba(11, 11, 11, .4);color: #fff;">
              <div style="font-size: 0.14rem;"><i class="el-icon-time"></i>  监测时间：${data['monitorTime']}</div>
              <div style="font-size: 0.14rem;margin-top: 0.12rem;">
                <div>
                  <i class="el-icon-stopwatch"></i>  今日达标率： <span>${data['complianceRate']}</span> %
                </div>
                <div style="margin-top: 0.12rem;">
                  <i class="el-icon-stopwatch"></i>  今日电量： <span>${data['useElectricity']} kwh</span>
                </div>
              </div>
            </div>
          `
        }
      },
      series: [
        {
          name: '',
          type: 'bar',
          data: this.propData.dataList.map((v:any)=>v.complianceRate),
          barWidth: 10,
          barGap: 0.5, //柱间距离
          itemStyle: {
            normal: {
              show: true,
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: '#42A8FF',
                },
                {
                  offset: 1,
                  color: '#082641',
                },
              ]),
              opacity: 0.8,
            },
          },
        },
        // {
        //   name: '告警阈值',
        //   type: 'line',
        //   yAxisIndex: 1,
        //   markLine: {
        //     symbol: 'none',
        //     label: {
        //       show: true,
        //       // formatter:()=>{
        //       //   return '告警阈值'
        //       // },
        //       // distance: [-13,0]
        //     },
        //     data: [
        //       {
        //         silent: false, //鼠标悬停事件  true没有，false有
        //         lineStyle: {
        //           //警戒线的样式  ，虚实  颜色
        //           type: 'dotted',
        //           color: 'red'
        //         },
        //         yAxis: this.propData.standard
        //       }
        //     ]
        //   }
        // },
      ],
    }
    this.chart.setOption(option as EChartOption<EChartOption>)
  }
}
</script>



