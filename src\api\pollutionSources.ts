import request from "@/utils/request";
import { AxiosPromise } from "axios";

/////////////////////////// 污染源 //////////////////////////////////////

/**
  污染源调查清单详情(基础详情)
 */
export function pollutionSourcesDetail(params: any): AxiosPromise<any> {
  return request({
    url: `/system/bigData/pollutionSourceSurvey/doDetail`,
    method: "get",
    params
  });
}

/**
  根据污染源类型获取监测项列表
 */
export function pollutionMonitorItem(params: any): AxiosPromise<any> {
  return request({
    url: `/system/bigData/pollutionSourceSurvey/pollutionMonitorItem`,
    method: "get",
    params
  });
}

/**
  根据污染源获取监测数据
 */
export function pollutionMonitorData(params: any): AxiosPromise<any> {
  return request({
    url: `/system/bigData/pollutionSourceSurvey/pollutionMonitorData`,
    method: "post",
    data: params
  });
}
/**
  根据污染源类型获取监控列表分页
 */
export function pollutionCameraList(params: any): AxiosPromise<any> {
  return request({
    url: `/system/bigData/pollutionSourceSurvey/pollutionCameraList`,
    method: "get",
    params
  });
}
/**
  根据监控地址
     channelId  通道id
     pollutionType 污染源类型 0 汽修源 1 工地源 2 工业源 3 加油站 4 停车场
 */
export function pollutionCameraLive(params: any): AxiosPromise<any> {
  return request({
    url: `/system/bigData/pollutionSourceSurvey/pollutionCameraLive`,
    method: "get",
    params
  });
}

/**
  分页获取问题清单列表（督办情况）
 */
export function supervisionSituationList(params: any): AxiosPromise<any> {
  return request({
    url: `/task/bigData/issues_manifest/doPage`,
    method: "get",
    params
  });
}


// 获取活性炭信息 enterpriseName 企业名称
export function getByEnterpriseName(params: any): AxiosPromise<any> {
  return request({
    url: `/system/bigData/activated_carbon/getByEnterpriseName`,
    method: "get",
    params
  });
}


