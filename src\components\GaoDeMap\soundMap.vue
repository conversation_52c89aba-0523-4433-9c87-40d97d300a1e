<template>
  <div id="depmap" class="container" ref="container">
    <!--蒙板 -->
    <div class="all_men"></div>
    <!--蒙板 -->
  </div>
</template>

<script lang="ts">
//@ts-ignore
// import AMap from 'AMap'
//@ts-ignore
import jinniuStreet from '@/assets/map-geojson/jinniu_street'
import { Vue, Component, Prop, Watch } from 'vue-property-decorator'
import { Icon } from 'ant-design-vue'
import { webglcontextlostHandle } from '@/utils/index'
import { airPraCheck, waterPraCheck } from '@/api/practiceCheck'
import moment from 'moment'
import AMapLoader from '@amap/amap-jsapi-loader'
import { AnyARecord } from 'dns'
import areLngLat from './areLngLat.js'
import noiseNormal1 from '@/assets/noise/<EMAIL>' // 噪声地图图标-正常
import noiseOver1 from '@/assets/noise/<EMAIL>' // 噪声地图图标-超标
import noiseOffline1 from '@/assets/noise/<EMAIL>' // 噪声地图图标-离线
import noiseNormal2 from '@/assets/noise/<EMAIL>' // 噪声地图图标-正常
import noiseOver2 from '@/assets/noise/<EMAIL>' // 噪声地图图标-超标
import noiseOffline2 from '@/assets/noise/<EMAIL>' // 噪声地图图标-离线
import biaoge from '@/assets/<EMAIL>' // 噪声地图图标-正常
import noiseBg from '@/assets/noise/tanchuang.png' // 噪声弹窗背景
import noiseLx from '@/assets/noise/<EMAIL>' // 噪声弹窗类型
import noiseIcon from '@/assets/noise/<EMAIL>' // 噪声弹窗图标
import noiseClose from '@/assets/noise/<EMAIL>' // 噪声弹窗关闭
import noiseTime from '@/assets/noise/<EMAIL>' // 噪声弹窗时间
import { getNoiseMapList } from '@/api/noise'
import dayjs from 'dayjs'
import { log } from 'console'

let AMap: any

@Component({
  name: 'DepartmentBgMap',
  components: {
    AIcon: Icon,
  },
})
export default class extends Vue {
  // private this.maps: any = "";
  @Prop({
    required: false,
    type: Number,
    default: 12.5,
  })
  district: any = ''
  polygons: any = []
  siteMarkerList: any = []
  markerList: any = []
  infoWindow: any = ''
  areLngLat: any = ''
  maps: any = ''
  polygonPlayer: any = []

  dingwei: string = require('@/assets/<EMAIL>')

  @Prop({ required: false }) marKertype!: any

  @Watch('mapZoom', { immediate: true, deep: false })
  private onmapZoomChange(newValue: number, oldValue: number) {
    if (newValue && this.maps) {
      this.maps.setZoom(newValue)
    }
  }

  @Watch('maps', { immediate: true, deep: false })
  private onmaps(newValue: any, oldValue: any) {
    if (newValue) {
      this.createOverlay()
      this.addPolygon(this.areLngLat['九里堤'])
      this.addPolygon(this.areLngLat['人工智能产业园'])
      this.creatWays()
      // this.creatAreMarker(this.areLngLat['九里堤'])
      // this.creatAreMarker(this.areLngLat['人工智能产业园'])
      this.paGetNoiseMapList()
    }
  }
  @Watch('marKertype', { immediate: true, deep: false })
  private onmarKertype(newValue: number, oldValue: number) {
    this.paGetNoiseMapList(newValue)
  }
  private polygonList: any = []
  beforeDestroy() {
    this.areLngLat = []
    if (!this.maps) return
    this.maps.clearMap()
    // 销毁地图
    this.maps.destroy()
    this.maps = null
    // 销毁前清空方法 防止内存泄漏
    ;(window as any).closeInfoWindowRck = null
    ;(window as any).AMap = null
    AMap = null
    //@ts-ignore
    window.closeInfoWindow = null
    this.polygonPlayer = []
  }
  closeInfoWindowRck() {
    if (this.maps) {
      this.maps.clearInfoWindow()
    }
  }
  created() {
    ;(window as any).closeInfoWindowRck = this.closeInfoWindowRck // 解决字符串模板@click无效的问题
    AMapLoader['reset']()
  }

  mounted() {
    this.areLngLat=JSON.parse(JSON.stringify(areLngLat))
    // // load 加载
    AMapLoader.load({
      key: '777fec7ef3cc29281d60ae900fa33925', // 申请好的Web端开发者Key，首次调用 load 时必填
      version: '1.4.15', // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
      plugins: [
        'AMap.DistrictSearch',
        'AMap.Heatmap',
        'AMap.ControlBar',
        'AMap.Object3DLayer',
        'Map3D',
        'AMap.Geocoder',
        'AMap.CircleMarker',
        'AMap.MouseTool',
      ], // 需要使用的的插件列表，如比例尺'AMap.Scale'等
      AMapUI: {
        // 是否加载 AMapUI，缺省不加载
        version: '1.0', // AMapUI 版本
        // "plugins":['overlay/SimpleMarker'],       // 需要加载的 AMapUI ui插件
      },
      Loca: {
        // 是否加载 Loca， 缺省不加载
        version: '1.3.2', // Loca 版本
      },
    })
      .then((amaps) => {
        setTimeout((_) => {
          AMap = amaps
          ;(window as any).AMap = amaps
          this.map()

          this.maps.on('complete', () => {
            //@ts-ignore
            const waterCenterPosition = new AMap.LngLat(104.066, 30.725)
            this.maps.setCenter(waterCenterPosition)
          })
        }, 0)
        //@ts-ignore
        window.closeInfoWindow = this.closeInfoWindow // 解决字符串模板@click无效的问题
      })
      .catch((e) => {
        console.log(e)
      })
  }
  // 高德地图
  private map(): void {
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const _this = this
    // 初始化地图
    const map = new AMap.Map(this.$refs.container, {
      center: [104.04, 30.71],
      position: [104.04, 30.71],
      zoom: 13,
      viewMode: '3D',
      pitch: 0,
      zoomEnable: true,
      dragEnable: true,
      zooms: [11.5, 18],
    })

    // 处理webgl上下文丢失事件
    webglcontextlostHandle.call(this)

    // 设置地图样式
    map.setMapStyle('amap://styles/0739a6632d849ae1b5e6f1c52f7d12c3')

    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const that = this
    // map.on('click', (ev:any)=> {
    //   // console.log(ev,'---------ev-----136')
    //   // 触发事件的地理坐标，AMap.LngLat 类型
    //   let lnglat = ev.lnglat;
    //   console.log([lnglat.lng, lnglat.lat]);
    // });
    this.maps = map
  }

  /* 绘制主要道路 */
  creatWays() {
    this.areLngLat.wayLine.forEach((v: any) => {
      let polyline = new AMap.Polyline({
        path: v.path,
        isOutline: true,
        outlineColor: 'rgb(209, 1, 20)',
        borderWeight: 1,
        strokeColor: 'rgb(209, 1, 20)',
        strokeOpacity: 1,
        strokeWeight: v.width * 1.6,
        strokeStyle: 'solid',
        lineJoin: 'bevel',
        zIndex: 150,
      })

      if (v.railway === 1) {
        polyline.setOptions({ outlineColor: 'rgb(128, 18, 145)' })
        polyline.setOptions({ strokeColor: 'rgb(50, 8, 56)' })
        polyline.setOptions({ borderWeight: 3 })
      } else if (v.railway === 2) {
        polyline.setOptions({ isOutline: false })
        polyline.setOptions({ strokeColor: '#fff' })
        polyline.setOptions({ borderWeight: 0 })
        polyline.setOptions({ strokeStyle: 'dashed' })
        polyline.setOptions({ strokeDasharray: [30, 30] })
      }
      this.maps.add(polyline)
    })
  }
  /**
   * 绘制金牛区区域
   */
  createOverlay() {
    const map = this.maps
    // 加载行政区划插件
    if (!this.district) {
      // 实例化DistrictSearch
      const opts = {
        subdistrict: 0, // 获取边界不需要返回下级行政区
        extensions: 'all', // 返回行政区边界坐标组等具体信息
        level: 'district', // 查询行政级别为 市
      }
      this.district = new AMap.DistrictSearch(opts)
    }
    // 行政区查询
    this.district.setLevel('district')
    this.district.search('金牛区', (status: any, result: any) => {
      // this.map.remove(this.polygons)// 清除上次结果
      this.polygons = []
      const bounds = result.districtList[0].boundaries
      if (bounds) {
        for (let i = 0, l = bounds.length; i < l; i++) {
          // 生成行政区划polygon
          const polygon = new AMap.Polygon({
            path: bounds[i],
            // fillOpacity: 0.4,
            fillColor: 'rgb(41, 126, 222)',
            cursor: 'default',
            bubble: true,
            strokeColor: '#1A77AA', // 线颜色
            strokeOpacity: 1, // 线透明度
            strokeWeight: 3, // 线宽
          })
          this.polygons.push(polygon)
        }
      }
      map.add(this.polygons)
    })
  }

  // 添加街道划分区域地图数据
  private creatGeojson() {
    const map = this.maps
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const that = this
    // 添加金牛区地理信息数据 1
    const geojson = new AMap.GeoJSON({
      geoJSON: jinniuStreet,
      getPolygon: function (geojson: any, lnglats: any) {
        AMap.convertFrom(
          geojson.geometry.coordinates[0],
          'gps',
          function (status: any, result: any) {
            if (result.info === 'ok' && geojson.properties.name !== '金牛区') {
              const polygon = new AMap.Polygon({
                path: result.locations,
                strokeColor: '#2266AB',
                strokeWeight: 1,
                strokeOpacity: 1,
                // strokeStyle: 'dashed',
                // strokeDasharray: [10,10],
                // fillOpacity: 0.5, // 多边形填充透明度
                fillColor: 'rgba(3,70,109,0)',
                zIndex: 1,
              })
              // polygon.on('click', (en: any) => {
              //   const lnglat = en.lnglat
              //   that.openRandaSite({ lng: lnglat.lng, lat: lnglat.lat })
              // })
              map.add(polygon)
              that.polygonList.push(polygon)
            }
          }
        )
      },
    })
    // 添加金牛区地理信息数据 2
    geojson.setMap(map)
  }

  //绘制指定区域
  addPolygon(data: any) {
    this.polygonPlayer = []
    const polygon = new AMap.Polygon({
      path: data.path,
      fillColor: data.color,
      strokeOpacity: 1,
      fillOpacity: 0.7,
      strokeColor: data.color,
      strokeWeight: 2,
      strokeStyle: 'solid',
      zIndex: 99,
    })
    polygon.on('mouseover', () => {
      polygon.setOptions({
        fillOpacity: 0.9,
      })
    })
    polygon.on('mouseout', () => {
      polygon.setOptions({
        fillOpacity: 0.7,
      })
    })
    // const polygonA = JSON.parse(JSON.stringify(polygon))
    this.polygonPlayer.push(polygon)
    this.maps.add(this.polygonPlayer)
  }

  //绘制指定区域点位
  creatAreMarker(data: any) {
    let icon = new AMap.Icon({
      size: new AMap.Size(26, 59),
      image: this.dingwei,
      imageSize: new AMap.Size(26, 59),
    })
    var marker = new AMap.Marker({
      position: data.center,
      icon,
      offset: new AMap.Pixel(0, -60),
    })

    marker.setMap(this.maps)

    // 设置label标签
    marker.setLabel({
      offset: new AMap.Pixel(0, 0), //设置文本标注偏移量
      content: `<div class='dep-site-infos'>
        ${data.name}
        <div class='dep-site-infos0'></div>
        </div>`, //设置文本标注内容
      direction: 'top', //设置文本标注方位
    })
    // //点位的小细干子
    // marker.setLabel({
    //   offset: new AMap.Pixel(20, 20), //设置文本标注偏移量
    //   content: `<div class='dep-site-info0'></div>`, //设置文本标注内容
    //   direction: 'center', //设置文本标注方位
    // })
  }
  /* 噪声地图列表 */
  paGetNoiseMapList(monitorType: any = '') {
    getNoiseMapList({
      status: '',
      streetCode: '',
      functionType: '',
      monitorType,
    }).then((res) => {
      const data = res.data.data || []
      this.markerList = data
      this.createNoiseMarker()
      this.$emit(
        'clickMarker',
        data[0].stationId,
        data[0].deviceId,
        dayjs(data[0].dataTime).format('YYYY-MM-DD')
      )
    })
  }
  // 噪声地图图标以及弹窗
  createNoiseMarker() {
    if (this.siteMarkerList.length !== 0) {
      this.maps.remove(this.siteMarkerList)
      this.siteMarkerList = []
    }

    this.markerList.forEach((item: any, index: any) => {
      let siteMarkerItem
      const noiseOverMarker = item.monitorType === 1 ? noiseOver1 : noiseOver2
      const noiseNormalMarker =
        item.monitorType === 1 ? noiseNormal1 : noiseNormal2
      const noiseOfflineMarker =
        item.monitorType === 1 ? noiseOffline1 : noiseOffline2
      const icons = !item.online
        ? noiseOfflineMarker
        : item.isAlarm
        ? noiseOverMarker
        : noiseNormalMarker

      siteMarkerItem = new AMap.Marker({
        map: this.maps,
        content: `<div style="width:1.1rem;height:0.46rem;color:#fff;transform: translate(-55px,-40px);"></div>`, //为取消默认marker所用的假marker
        position: [item.longitude, item.latitude],
        offset: new AMap.Pixel(0, 0),
        data: item,
        zIndex: 19,
      })

      if (index === 0) {
        siteMarkerItem.setLabel({
          offset: new AMap.Pixel(-55, -46), //设置文本标注偏移量
          content: `<div style="position: relative;background: url('${icons}') no-repeat; background-size: 100% 100%;width:1.2rem;height:0.54rem;color:#fff;padding:0.08rem 0 0 0.26rem;font-size: 12px;">
            ${item.stationName}

            <div  class='${
              item.isAlarm
                ? 'site-info-pollutantState'
                : item.online
                ? 'site-info-online'
                : 'site-info-offline'
            } site-infosoud'></div>
            </div>`, //设置文本标注内容
        })
      } else {
        siteMarkerItem.setLabel({
          offset: new AMap.Pixel(-55, -46), //设置文本标注偏移量
          content: `<div style="background: url('${icons}') no-repeat; background-size: 100% 100%;width:1.2rem;height:0.54rem;color:#fff;padding:0.08rem 0 0 0.26rem;font-size: 12px;">
            ${item.stationName}
            </div>`, //设置文本标注内容
        })
      }

      AMap.event.addListener(siteMarkerItem, 'click', this.siteNoiseClick)
      this.siteMarkerList.push(siteMarkerItem)
    })
  }

  openNoiseSite(marker: any) {
    const markerData = marker.relData
    let infoWindow
    let str = ''
    if (marker.relData) {
      const time = markerData.monitorTime
        ? markerData.monitorTime.slice(0, 16)
        : '--'
      str = `
          <div style="pointer-events: auto;width:3.7rem; height:2.3rem; box-sizing:border-box;background: url('${noiseBg}') no-repeat; background-size: 100% 100%; padding:0.15rem 0.25rem;position: relative;">
            <span style="position: absolute;top:0.4rem;left:0.51rem;font-size: 0.18rem;font-family: YouSheBiaoTiHei;font-weight: 400;color: #FFFFFF;background: linear-gradient(0deg, #56D1ED 0%, #CAF6FF 100%);-webkit-background-clip: text;-webkit-text-fill-color: transparent;">噪声监测</span>
            <img src="${noiseClose}" style="width:0.22rem; height:0.22rem;position: absolute;top:0.47rem;right:0.45rem;" onclick="closeInfoWindow()"/>
            <div style="height:0.82rem; margin-top:0.5rem;display:flex;padding-left:0.3rem;padding-top:0.15rem;">
              <img src="${noiseIcon}" style="width:0.56rem;height:0.65rem" />
              <div style="padding-left:0.08rem;">
                <div style="font-size: 0.16rem;font-family: PingFang SC;font-weight: 400;color: #DCF0FF;line-height: 0.32rem;">${
                  marker.stationName
                }</div>
                <span style="font-size: 0.14rem;font-family: PingFang SC;font-weight: 400;color: #7798B9;margin-top:0.12rem;">监测时间：${time}</span>
              </div>
            </div>
           <div style="background: url('${biaoge}') no-repeat; background-size: 100% 100%;width:100%;height:0.3rem;margin-top:0.0rem;">
            <div style="0.13rem;color:#60ADF4;width:100%;text-align:center;padding-top:0.03rem;">Leq：<span style="font-size: 0.16rem; font-family: YouSheBiaoTiHei; color: ${
              !marker.isAlarm ? '#fff' : '#EE505F'
            };">${markerData.leq}</span style="#D3E6F5"> dB(A)</div>
            </div>
          </div>
          `

      // <div style="padding:0 0.1rem; margin-top:0.05rem; display:flex; justify-content: space-around;">
      //  <div style="width:1.26rem; height:1.13rem; background: url('${noiseLx}') no-repeat; background-size: 100% 100%;    padding:0.18rem 0rem;">
      //       <div style="font-size: 0.2rem; font-family: YouSheBiaoTiHei; font-weight: 400; color: #FFFFFF; width:100%;  text-align: center;">${
      //         marker.functionTypeName
      //       }</div>
      //       <div style="font-size: 0.1rem; font-family: PingFang SC; font-weight: 400; color: #FFFFFF; width:100%;  text-align: center;">功能区类型</div>
      //     </div>
      //     <div style="font-size: 0.12rem; font-family: YouSheBiaoTiHei; color: #D1DDF7;">
      //       <div>Leq：<span style="font-size: 0.16rem; font-family: YouSheBiaoTiHei; color: ${
      //         !marker.isAlarm ? '#D1DDF7' : '#EE505F'
      //       };">${markerData.leq}</span> dB</div>
      //       <div>Lmax：<span style="font-size: 0.16rem; font-family: YouSheBiaoTiHei; color: ${
      //         !marker.isAlarm ? '#D1DDF7' : '#EE505F'
      //       };">${markerData.lmax}</span> dB</div>
      //       <div>Lmin：<span style="font-size: 0.16rem; font-family: YouSheBiaoTiHei; color: ${
      //         !marker.isAlarm ? '#D1DDF7' : '#EE505F'
      //       };">${markerData.lmin}</span> dB</div>
      //       <div>标准差：<span style="font-size: 0.16rem; font-family: YouSheBiaoTiHei; color: ${
      //         !marker.isAlarm ? '#D1DDF7' : '#EE505F'
      //       };">${markerData.sd}</span></div>
      //       <div>有效采集率：<span style="font-size: 0.16rem; font-family: YouSheBiaoTiHei; color: ${
      //         !marker.isAlarm ? '#D1DDF7' : '#EE505F'
      //       };">${markerData.rate}</span> %</div>
      //     </div>
      //   </div>
    } else {
      str = `<div style="pointer-events: auto;width:3.7rem; height:2.3rem; box-sizing:border-box;background: url('${noiseBg}') no-repeat; background-size: 100% 100%; padding:0.15rem 0.25rem;position: relative;">
            <span style="position: absolute;top:0.4rem;left:0.51rem;font-size: 0.18rem;font-family: YouSheBiaoTiHei;font-weight: 400;color: #FFFFFF;background: linear-gradient(0deg, #56D1ED 0%, #CAF6FF 100%);-webkit-background-clip: text;-webkit-text-fill-color: transparent;">噪声监测</span>
            <img src="${noiseClose}" style="width:0.22rem; height:0.22rem;position: absolute;top:0.47rem;right:0.45rem;" onclick="closeInfoWindow()"/>
            <div style="height:0.82rem; margin-top:0.5rem;display:flex;padding-left:0.3rem;padding-top:0.15rem;">
              <img src="${noiseIcon}" style="width:0.56rem;height:0.65rem" />
              <div style="padding-left:0.08rem;">
                <div style="font-size: 0.16rem;font-family: PingFang SC;font-weight: 400;color: #DCF0FF;line-height: 0.32rem;">${marker.stationName}</div>
                <span style="font-size: 0.14rem;font-family: PingFang SC;font-weight: 400;color: #7798B9;margin-top:0.12rem;">监测时间：--</span>
              </div>
            </div>
           <div style="background: url('${biaoge}') no-repeat; background-size: 100% 100%;width:100%;height:0.3rem;margin-top:0.0rem;">
            <div style="0.13rem;color:#60ADF4;width:100%;text-align:center;padding-top:0.03rem;">Leq：<span style="font-size: 0.16rem; font-family: YouSheBiaoTiHei; color:#fff;">--</span style="#D3E6F5"> dB(A)</div>
            </div>
          </div>
          `
    }
    infoWindow = new AMap.InfoWindow({
      // anchor: "bottom-center",
      isCustom: true,
      content: str, //使用默认信息窗体框样式，显示信息内容
      offset: new AMap.Pixel(0, 5),
    })
    this.infoWindow = infoWindow
    infoWindow.open(this.maps, [marker.longitude, marker.latitude])
  }

  siteNoiseClick(marker: any) {
    const data = marker.target.w.data

    this.siteMarkerList.forEach((items: any) => {
      const item = items.w.data
      const noiseOverMarker = item.monitorType === 1 ? noiseOver1 : noiseOver2
      const noiseNormalMarker =
        item.monitorType === 1 ? noiseNormal1 : noiseNormal2
      const noiseOfflineMarker =
        item.monitorType === 1 ? noiseOffline1 : noiseOffline2
      const icons = !item.online
        ? noiseOfflineMarker
        : item.isAlarm
        ? noiseOverMarker
        : noiseNormalMarker
      if (item.stationId === data.stationId) {
        items.setLabel({
          offset: new AMap.Pixel(-55, -46), //设置文本标注偏移量
          content: `<div style="position: relative;background: url('${icons}') no-repeat; background-size: 100% 100%;width:1.2rem;height:0.54rem;color:#fff;padding:0.08rem 0 0 0.26rem;font-size: 12px;">
            ${item.stationName}

            <div  class='${
              item.isAlarm
                ? 'site-info-pollutantState'
                : item.online
                ? 'site-info-online'
                : 'site-info-offline'
            } site-infosoud'></div>
            </div>`, //设置文本标注内容
        })
      } else {
        items.setLabel({
          offset: new AMap.Pixel(-55, -46), //设置文本标注偏移量
          content: `<div style="background: url('${icons}') no-repeat; background-size: 100% 100%;width:1.2rem;height:0.54rem;color:#fff;padding:0.08rem 0 0 0.26rem;font-size: 12px;">
            ${item.stationName}
            </div>`, //设置文本标注内容
        })
      }
    })
    this.maps.clearInfoWindow()
    this.openNoiseSite(marker.target.w.data)
    // this.this.maps.setCenter([data.longitude,data.latitude])
    this.$emit(
      'clickMarker',
      data.stationId,
      data.deviceId,
      dayjs(data.dataTime).format('YYYY-MM-DD'),
      data.stationPoint
    )
  }

  closeInfoWindow() {
    this.maps.clearInfoWindow()
  }
}
</script>

<style lang="less">
@import url(./map.less);
.dep-site-infos {
  position: relative;
  width: 129px;
  height: 22px;
  text-align: center;
  line-height: 22px;
  font-size: 12px;
  font-family: Adobe Heiti Std;
  font-weight: normal;
  color: #ffffff;
  background: linear-gradient(
    to right,
    #ac680f00 4%,
    #ac680f 50%,
    #ac680f00 96%
  );
  border: 1px solid;
  border-image: linear-gradient(
      to right,
      #f8bb6c00 0%,
      #f8bc6c 50%,
      #f8bb6c00 100%
    )
    10;
  border-left: none;
  border-right: none;
}
.dep-site-infos0 {
  position: absolute;
  bottom: -62px;
  left: 50px;
  width: 28px;
  height: 20px;
  border: 2px solid #f0f0f0;
  opacity: 0.6;
  border-radius: 50%;
  transform: scale(0.2);
  animation: depripples 1.5s ease-out infinite;
}
@keyframes depripples {
  0% {
    transform: scale(0.2);
    opacity: 1;
  }
  100% {
    transform: scale(1.2);
    opacity: 0;
  }
}
.dep-site-info-sel1 {
  background-color: rgba(0, 228, 0, 0.7);
}
.dep-site-info-sel2 {
  background-color: rgba(255, 255, 0, 0.7);
}
.dep-site-info-sel3 {
  background-color: rgba(255, 126, 0, 0.7);
}
.dep-site-info-sel4 {
  background-color: rgba(255, 0, 0, 0.7);
}
.dep-site-info-sel5 {
  background-color: rgba(153, 0, 76, 0.7);
}
.dep-site-info-sel6 {
  background-color: rgba(126, 0, 35, 0.7);
}

.amap-marker-label {
  pointer-events: none !important;
}
.amap-icon {
  overflow: hidden !important;
  img {
    pointer-events: none !important;
  }
}

.site-info-pollutantState {
  background-color: #ff5053;
}
.site-info-offline {
  background-color: #ccc4c6;
}
.site-info-online {
  background-color: #78fffd;
}
.site-infosoud {
  position: absolute;
  width: 10px;
  height: 10px;
  left: 50px;
  bottom: 0;
  border-radius: 50%;
  /*background-color: red;*/
  transform: rotateX(120deg);
  /*-moz-animation-name: ripple;*/
  /*-webkit-animation-name: ripple;*/
  animation-name: ripplesoud;
  animation-delay: 0s;
  animation-duration: 1s;
  -moz-animation-iteration-count: infinite;
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
  transform-origin: center center;
}
@keyframes ripplesoud {
  from {
    opacity: 1;
    left: 50px;
    bottom: 0;
  }
  to {
    width: 40px;
    height: 40px;
    opacity: 0;
    left: 35px;
    bottom: -13px;
  }
}
</style>
<style lang="less" scoped>
.container {
  width: 100%;
  height: 100%;
  color: rgb(252, 134, 134);
  position: relative;
  z-index: 0 !important;
  .all_men {
    width: 100%;
    height: 100%;
    z-index: 1;
    position: absolute;
    pointer-events: none;
    background: url('../../assets/recheck/ylfp_bg.png') no-repeat;
    background-size: 100% 100%;
  }
}
</style>
