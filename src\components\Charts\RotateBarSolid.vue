<template>
  <div :id="id" :style="{ height: height, width: width }" />
</template>

<script lang="ts">
import * as echarts from 'echarts'
import { Component, Prop, Watch } from "vue-property-decorator";
import { mixins } from "vue-class-component";
import ResizeMixin from "./mixins/resize";
interface AirDatas {
  bottomList: string[];
  dataList: string[];
  colorList?: string[];
}

let chart: any = null;
@Component({
  name: "RotateBarSolid"
})
export default class extends mixins(ResizeMixin) {
  // @Prop({ default: "chart" }) private className!: string;
  @Prop({ default: "chart" }) private id!: string;
  @Prop({ default: "200px" }) private width!: string;
  @Prop({ default: "200px" }) private height!: string;
  @Prop({ required: true }) private airData!: AirDatas;
  @Watch("airData", { immediate: true, deep: true })
  public onAirTypeNum(newValue: AirDatas, oldValue: AirDatas) {
    this.$nextTick(() => {
      this.initChart();
    });
  }
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
  }

  beforeDestroy() {
    if (!chart) {
      return;
    }
    chart.clear();
    chart.dispose();
    chart = null;
  }

  private initChart() {

    if(chart === null || chart === undefined) {
      chart = echarts.init(
        document.getElementById(this.id) as HTMLDivElement
      );
    }
    chart.setOption({
      title: {
        left: 25,
        top: 0,
        text: "首要污染物天数",
        textStyle: {
          color: "#fff",
          fontSize: 12
        }
      },
      // backgroundColor: "transparent",
      grid: {
        bottom: 10,
        left: "left",
        containLabel: true,
        top: 35
      },
      yAxis: {
        type: "value",
        // name: "天",
        axisLabel: {
          textStyle: {
            fontSize: 12,
            color: "white"
          }
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: "#fff",
            width: 2
          }
        },
        axisTick: {
          show: false
        },
        splitLine: {
          lineStyle: {
            type: "dashed"
          }
        }
      },
      xAxis: {
        type: "category",
        nameTextStyle: {
          color: "#fff"
        },
        data: this.airData.bottomList,
        axisLabel: {
          textStyle: {
            fontSize: 12,
            color: "white"
          },
          interval: 0
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: "#fff",
            width: 2
          }
        },
        axisTick: {
          show: false
        },
        splitLine: {
          show: false,
          lineStyle: {
            type: "solid"
          }
        }
      },
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "cross",
          label: {
            backgroundColor: "#6a7985"
          }
        }
      },
      series: [
        {
          type: "bar",
          // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
          // @ts-ignore
          barWidth: "35%",
          data: this.airData.dataList,
          itemStyle: {
            // color: "rgb(255,255,0)",
            normal: {
              // barBorderRadius: [0, 5, 5, 0],
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: "#0263FF"
                },
                {
                  offset: 1,
                  color: "#00A2FF"
                }
              ]),
              label: {
                show: true, //开启显示
                position: "top", //在上方显示
                textStyle: {
                  //数值样式
                  color: "#fff",
                  fontSize: 12
                }
              }
              // color: function(data: any) {
              //   if (data.value > 70) {
              //     return "rgb(153,0,76)";
              //   } else if (data.value <= 70 && data.value > 50) {
              //     return "rgb(255,0,0)";
              //   } else if (data.value <= 50 && data.value > 35) {
              //     return "rgb(255,126,0)";
              //   } else if (data.value <= 35 && data.value > 10) {
              //     return "rgb(255,255,0)";
              //   } else if (data.value <= 10) {
              //     return "rgb(0,255,0)";
              //   }
              // }
            }
          }
        }
      ]
    } as {});

    // 鼠标移出之后，恢复自动高亮
    /* chart.on("mouseout", (param: any) => {
      chart.clear();
      chart.dispose();
      this.initChart()
    }); */

  }
}
</script>
