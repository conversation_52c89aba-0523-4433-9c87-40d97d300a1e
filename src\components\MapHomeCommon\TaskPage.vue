<style lang="less" scoped>
.home-map-top {
  .home-map-left {
    .title {
      color: #cccccc;
      font-size: 0.2rem;
      margin-bottom: 0.2rem;
    }
    .sub-title-content {
      height: 2.5rem;
    }
    .task-total {
    }
    .task-complete {
      height: 1.2rem;
      width: 100%;
      .task-strip {
        width: 3.6rem;
        > :nth-of-type(2) {
          display: flex;
          justify-content: space-between;
          margin-top: 0.14rem;
        }
        .line-two {
          // margin-top: 0.5rem;
          // height: 0.1rem;
          // background-color: rgba(44, 60, 164, 1);
          // border-radius: 0.12rem;
          // position: relative;
          // .progress {
          //   height: 0.27rem;
          //   width: 0.47rem;
          //   background-image: url("../../assets/<EMAIL>");
          //   background-size: 100% 100%;
          //   position: absolute;
          //   bottom: 0.2rem;
          //   text-align: center;
          // }
          // > div {
          //   height: 0.1rem;
          //   border-radius: 0.12rem;
          // }
        }
      }
    }
  }
}
.task-page {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: calc(100% - 1rem);
  .task-proportion {
    display: flex;
    flex-wrap: wrap;
    > div:nth-child(odd) {
      margin-right: 1rem;
    }
    .chart-title {
      margin: 0.1rem 0;
      font-size: 0.17rem;
      text-align: center;
    }
  }
  .task-proportions {
    height:2.2rem;
    width: 3.5rem;
  }
  height: 100%;
}
</style>
<template>
  <section class="task-page">
    <div class="comment-content">
      <div class="title">
        本月巡查任务总数<span style="font-size:0.14rem;">（总数 {{count}}个）</span>
      </div>
      <div class="sub-title-content task-total">
        <TaskLineChart
          :id="'taskTotal'"
          :width="'3.5rem'"
          :height="'2.3rem'"
          :propData="taskTotal"
        />
      </div>
    </div>
    <div class="comment-content">
      <div class="title">
        本月日常任务总数<span style="font-size:0.14rem;">（总数 {{count}}个）</span>
      </div>
      <div class="task-proportions">
        <taskPieChart
                :id="'taskPieChart'"
                :width="'3.5rem'"
                :height="'2.3rem'"
                :PieChartData="PieChartData"
        ></taskPieChart>
      </div>
<!--      <div class="task-proportion">-->
<!--        <div>-->
<!--          <TaskPie-->
<!--            :id="'taskTotalDay'"-->
<!--            :valueColor="'#4AE5D2'"-->
<!--            :count="20"-->
<!--            :total="427"-->
<!--          />-->
<!--          <div class="chart-title">未开始</div>-->
<!--        </div>-->
<!--        <div>-->
<!--          <TaskPie-->
<!--            :id="'taskInHand'"-->
<!--            :count="368"-->
<!--            :total="427"-->
<!--            :valueColor="'#DDE67B'"-->
<!--          />-->
<!--          <div class="chart-title">进行中</div>-->
<!--        </div>-->
<!--        <div>-->
<!--          <TaskPie-->
<!--            :id="'taskCompleted'"-->
<!--            :count="15"-->
<!--            :total="427"-->
<!--            :valueColor="'#5297FF'"-->
<!--          />-->
<!--          <div class="chart-title">已完成</div>-->
<!--        </div>-->
<!--        <div>-->
<!--          <TaskPie-->
<!--            :id="'taskOverdue'"-->
<!--            :count="24"-->
<!--            :total="427"-->
<!--            :valueColor="'#EF9B17'"-->
<!--          />-->
<!--          <div class="chart-title">逾期</div>-->
<!--        </div>-->
<!--      </div>-->
    </div>
    <div class="comment-content">
      <div class="title">本月任务完成率（总数 {{count}}件）</div>
      <div class="sub-title-content task-complete">
        <div class="task-strip">
          <div class="line-two">
            <!-- <div
              :style="{
                width:
                  parseInt(
                    (taskComplete.hasTaskCount / taskComplete.value) * 100
                  ) + '%',
                background:
                  'linear-gradient(90deg,rgba(56,102,230,1) 0%,rgba(101,180,239,1) 100%)'
              }"
            ></div>
            <div
              :style="{
                left:
                  parseInt(
                    ((taskComplete.hasTaskCount - 15) / taskComplete.value) *
                      100
                  ) + '%'
              }"
              class="progress"
            >
              {{
                parseInt(
                  (taskComplete.hasTaskCount / taskComplete.value) * 100
                ) + " %"
              }}
            </div> -->
            <progressBar
              :id="'progressBars'"
              :propData="percentData"
              :width="'4rem'"
              :height="'0.4rem'"
            />
          </div>
          <div>
            <span>已完成：{{monthlyCompleteRate.completedRate ? monthlyCompleteRate.completedRate.count : 0}}</span>
            <span>未完成：{{monthlyCompleteRate.uncompletedRate ? monthlyCompleteRate.uncompletedRate.count : 0}}</span>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>
<script lang="ts">
interface TaskData {
  bottomList: string[];
  dataList: any[];
  typeList: string[];
}
import { Component, Vue, Watch } from "vue-property-decorator";
import TaskLineChart from "@/components/Charts/TaskLineChart.vue";
import TaskPie from "@/components/Charts/TaskPie.vue";
import progressBar from "@/components/Charts/progressBar.vue";
import { getTotalMonth } from '@/api/task-management.ts'
import taskPieChart from '@/components/Charts/taskPieChart.vue'
@Component({
  name: "VehiclesPage",
  components: {
    TaskLineChart,
    TaskPie,
    progressBar,
    taskPieChart
  }
})
export default class extends Vue {
  private taskComplete: any = {
    name: "666",
    hasTaskCount: 157,
    value: 194
  };
  private PieChartData:any = {
    name: '本月日常任务',
    dataList: [],
    colorList: ["#01D0FE", "#357ADD", "#5D17DB"]
  }
  private taskTotal: TaskData = {
    bottomList: [],
    dataList: [[], [], []],
    typeList: ["未完成", "已关闭", "已完成"]
  };
  private percentData:any = 0
  private taskMarkerList: any[] = [
    {
      lng: 104.1045570373535,
      lat: 30.770159115784214
    },
    {
      lng: 104.08687591552734,
      lat: 30.733720709532616
    },
    {
      lng: 104.08567428588867,
      lat: 30.68265426111617
    },
    {
      lng: 104.07073974609375,
      lat: 30.688559278566338
    },
    {
      lng: 104.05494689941406,
      lat: 30.733130500446673
    },
    {
      lng: 104.06301498413086,
      lat: 30.707010130100215
    },
    {
      lng: 104.04636383056639,
      lat: 30.677929987145504
    },
    {
      lng: 104.04275894165039,
      lat: 30.68974023871805
    },
    {
      lng: 104.04602050781249,
      lat: 30.70745290718712
    },
    {
      lng: 104.03263092041016,
      lat: 30.703172643553152
    },
    {
      lng: 104.0185546875,
      lat: 30.693135418679468
    },
    {
      lng: 104.01924133300781,
      lat: 30.727228210785487
    },
    {
      lng: 103.99057388305664,
      lat: 30.714094319607913
    }
  ];
  mounted() {
    this.getTotalMonth()
    // this.taskTotalNum();
    /* eslint-disable @typescript-eslint/ban-ts-ignore */
    //@ts-ignore
    this.$bus.emit("sendTaskMarkerList", this.taskMarkerList);
  }
  private count:number = 0
  private monthlyCompleteRate:any = {}
  private getTotalMonth() {
    getTotalMonth().then(res => {
      const data = res.data.data
      this.count = data.count
      data.dailyStatistics.forEach((item:any, index:any) => {
        this.taskTotal.bottomList.push(index + "日");
        this.taskTotal.dataList[0].push(item.uncompleted)
        this.taskTotal.dataList[1].push(item.closed)
        this.taskTotal.dataList[2].push(item.completed)
      })
      this.percentData = parseInt(data.monthlyCompleteRate.completedRate.rate.substr(0, data.monthlyCompleteRate.completedRate.rate.indexOf('%')))
      this.monthlyCompleteRate = data.monthlyCompleteRate
      data.monthlyCompleteStatus.forEach((item:any) => {
        this.PieChartData.dataList.push(item.count)
      })
      console.log(this.percentData, this.PieChartData, 'percentData')
    })
  }
  private taskTotalNum() {
    for (let index = 1; index <= 30; index++) {
      this.taskTotal.bottomList.push(index + "日");
    }
    for (let index1 = 1; index1 <= 30; index1++) {
      this.taskTotal.dataList[0].push(
        Math.floor(Math.random() * (80 - 40)) + 40
      );
    }
    for (let index1 = 1; index1 <= 30; index1++) {
      this.taskTotal.dataList[1].push(
        Math.floor(Math.random() * (35 - 20)) + 20
      );
    }
    for (let index1 = 1; index1 <= 30; index1++) {
      this.taskTotal.dataList[2].push(
        Math.floor(Math.random() * (30 - 15)) + 15
      );
    }
    for (let index1 = 1; index1 <= 30; index1++) {
      this.taskTotal.dataList[3].push(
        Math.floor(Math.random() * (20 - 10)) + 10
      );
    }
    console.log(this.taskTotal);
  }
}
</script>
