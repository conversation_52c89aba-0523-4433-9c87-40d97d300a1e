<template>
  <div class="fingerprint-library-more-container" v-if="visibles">
    <!-- 涉污企业指纹库 -->
    <a-modal
      dialogClass="fingerprint-library-more"
      :visible="visibles"
      class="fingerprint-library-more"
      :footer="null"
      :closable="false"
      :destroyOnClose="true"
      @cancel="handleClose"
      :centered="true"
    >
      <div class="content">
        <div class="header">
          <div class="title">
            <img src="@/assets/analysis/<EMAIL>" alt="">
          </div>
          <div class="name">{{ currentStation.stationName || "--" }}</div>
          <div class="close" @click="handleClose">
            <img src="@/assets/<EMAIL>" alt="" />
          </div>
        </div>
        <div class="title_select">
          <div
            class="title_select_item"
            :class="timeType === 1 ? 'title_select_item_select' : ''"
            style="margin-right: 0.05rem"
            @click="timeType = 1"
          >
            分钟数据
          </div>
          <div
            class="title_select_item"
            :class="timeType === 2 ? 'title_select_item_select' : ''"
            @click="timeType = 2"
          >
            小时数据
          </div>
        </div>
        <div class="chart-box">
          <dialogChart
            v-if="visibles"
            :propData="propData"
            :id="'DialogChart'"
            :width="'100%'"
            :height="'100%'"
            :smooth="true"
          ></dialogChart>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script>
import { Modal } from "ant-design-vue"
import dialogChart from './dialogChart.vue'
import { listSewageMonitorTrendHour } from '@/api/analysisAir'
export default {
  name: '',
  props: {
    visibles: {
        type: Boolean,
        default: false
    },
    currentStation: {
        type: Object,
        default: () => {}
    }
  },
  data() {
    return {
        timeType: 1,
        propData:{
            threshold: null,
            list: []
        }
    }
  },
  created() {
    // console.log('timeType', this.timeType)
  },
  methods: {
    handleClose() {
        this.$emit('handleClose')
    },
    getListSewageMonitorTrendHour() {
        const params = {
            sewageType: this.currentStation.type,
            stationId: this.currentStation.stationCode,
            timeType: this.timeType
        }
        listSewageMonitorTrendHour(params).then(res=> {
            if(res.data.code === 200) {
                this.propData = res.data.data || []
            }else{
                this.propData = []
            }
        }).catch((_) => {
            this.propData = []
        })
    }
  },
  computed: {},
  watch: {
    currentStation:{
        handler(newVal) {
            this.timeType = 1
            if(newVal && JSON.stringify(newVal) !== '{}'){
                this.getListSewageMonitorTrendHour()
            }
        },
        deep: true,
        immediate: true
    },
    timeType() {
        if(this.currentStation && JSON.stringify(this.currentStation) !== '{}'){
            this.getListSewageMonitorTrendHour()
        }
    }
  },
  components: {
    AModal: Modal,
    dialogChart
  },
}
</script>

<style lang="less" scoped>
.fingerprint-library-more {
  background: transparent !important;
  width: 1560px !important;
  height: 850px;
  .ant-modal-close {
    display: none;
  }
  .ant-modal-content {
    background: transparent;
    width: 1560px !important;
    height: 850px;
    background: url("~@/assets/heavily-polluted-enterprise/chartDialigBg.png");
    background-size: 100% 100%;
    position: relative;
    .content {
      padding: 20px 30px;
      color: #fff;
      box-sizing: border-box;
      .header {
        width: 100%;
        height: 180px;
        display: flex;
        justify-content: space-between;
        // align-items: top;
        align-items: flex-start;
        position: relative;
        background: url('~@/assets/analysis/<EMAIL>');
        background-size: 100% 100%;
        .title {
          font-size: 20px;
        }
        .name {
          font-size: 24px;
          font-family: YouSheBiaoTiHei;
          font-weight: 400;
          color: #FFEEEE;
          position: absolute;
          left: 50%;
          top: 40%;
          transform: translate(-50%, -50%);
        }
        .close {
          cursor: pointer;
        }
      }
      .title_select {
        display: flex;
        margin-top: -60px;
        position: relative;
        z-index: 10;
        .title_select_item {
          width: 116px;
          height: 26px;
          background: url("~@/assets/analysis/<EMAIL>") no-repeat;
          background-size: 100%;
          text-align: center;
          line-height: 26px;
          font-size: 0.14rem;
          font-family: Source Han Sans CN;
          font-weight: 500;
          color: #E2F8FFCC;
          cursor: pointer;
        }
        .title_select_item_select {
        background: url("~@/assets/analysis/<EMAIL>") no-repeat;
         color: #E2F8FF;
        }
      }
      .chart-box {
        // background-color: #fff;
        margin-top: 20px;
        width: 100%;
        height: 615px;
      }
    }
  }
}
</style>
