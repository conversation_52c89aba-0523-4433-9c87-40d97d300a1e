import request from "@/utils/request";
import { AxiosPromise } from "axios";

/**
 * @method functionName
 * @param {type} data 说明
 * @description 获取当前工地摄像头列表
 */
export function getSiteVideoList(companyId:any): AxiosPromise<any> {
  return request({
    url: `/air/company/camera/${companyId}`,
    method: "get",
  });
}
/**
 * @method functionName
 * @param {type} data 说明
 * @description 获取单个摄像头url
 */
export function getCameraUrl(channeId:any): AxiosPromise<any> {
  return request({
    url: `/air/company/camera/live/${channeId}`,
    method: "get",
  });
}
/**
 * @method functionName
 * @param {type} data 说明
 * @description 获取当前工地PM10信息
 */
export function getCurSitePM10(companyId:any): AxiosPromise<any> {
  return request({
    url: `/air/company/data/${companyId}`,
    method: "get",
  });
}
/**
 * @method functionName
 * @param {type} data 说明
 * @description 获取重污工地列表
 */
export function getCompanyList(params:any): AxiosPromise<any> {
  return request({
    url: "/air/company/list",
    method: "get",
    params
  });
}
/**
 * @method functionName
 * @param {type} data 说明
 * @description 在建工地数量
 */
export function getSiteList(): AxiosPromise<any> {
  return request({
    url: "/air/company/count",
    method: "get",
  });
}
/**
 * @method functionName
 * @param {type} data 说明
 * @description 在建工地数量
 */
export function findByKeyWord(keyWord:any): AxiosPromise<any> {
  return request({
    url: "/air/heavilyPollutingEnterprise/findByKeyWord",
    method: "get",
    params:{
      keyWord
    }
  });
}
/**
 * @method functionName
 * @param {type} data 说明
 * @description 获取水七厂最新数据
 */
export function getSevenWaterList(): AxiosPromise<any> {
  return request({
    url: "/air/heavilyPollutingEnterprise/getSevenWaterList",
    method: "get"
  });
}
/**
 * @method functionName
 * @param {type} data 说明
 * @description 获取水五厂最新数据
 */
export function getFiveWaterList(): AxiosPromise<any> {
  return request({
    url: "/water/heavilyPollutingEnterprise/getFiveWaterList",
    method: "get"
  });
}
/**
 * @method functionName
 * @param {type} data 说明
 * @description 获取工地报警最新记录
 */
export function getcompanyAlarmList(): AxiosPromise<any> {
  return request({
    url: "/air/company/alarm/recent/list",
    method: "get"
  });
}
/**
 * @method functionName
 * @param {type} data 说明
 * @description 污染总数量
 */
export function getPollutionCount(heavilyPollutingEnterpriseId:number|string): AxiosPromise<any> {
  return request({
    url: `/air/heavilyPollutingEnterprise/getAllDischarge?heavilyPollutingEnterpriseId=${heavilyPollutingEnterpriseId}`,
    method: "get",
    headers: { 'Content-Type':'application/x-www-form-urlencoded'},
    data: { heavilyPollutingEnterpriseId}
  });
}
/**
 * @method functionName
 * @param {type} data 说明
 * @description 污染物列表
 */
export function getPollutionEnterprise(heavilyPollutingEnterpriseId:number|string): AxiosPromise<any> {
  return request({
    url: `/air/heavilyPollutingEnterprise/getWastewaterDischargeList?heavilyPollutingEnterpriseId=${heavilyPollutingEnterpriseId}`,
    method: "get",
    headers: { 'Content-Type':'application/x-www-form-urlencoded'},
    // data: { heavilyPollutingEnterpriseId}
  });
}
/**
 * @method getTwentyFourHour
 * @params {companyId} 公司id
 * @description 获取监测工地的24小时监测值
 */
export function getTwentyFourHour(companyId:any):AxiosPromise<any> {
  return request({
    url: '/air/company-hour-data/big-data/twentyFourHour/' + companyId,
    method: 'get'
  })
}

/**
 * @method getStreet
 * @description 获取街道列表
 */
export function getStreet():AxiosPromise<any> {
  return request({
    url: '/air/area/street',
    method: 'get'
  })
}

/**
 * @method getSiteCount
 * @description 获取工地数量
 *
 */
export function getSiteCount(params: any):AxiosPromise<any> {
  return request({
    url: '/air/company/count',
    params
  })
}

/**
 * @method getHeavilyPollutingEnterpriseList
 * @param {streetCode}街道code
 * @param {keywords} 关键词
 */
export function getHeavilyPollutingEnterpriseList(params:any):AxiosPromise<any> {
  return request({
    url: '/water/heavilyPollutingEnterprise/getEnterpriseList',
    method: 'get',
    params
  })
}

/**
 * @method getHeavyCount
 * @description 获取企业数量
 */
export function getHeavyCount():AxiosPromise<any> {
  return request({
    url: '/water/heavilyPollutingEnterprise/count',
    method: 'get'
  })
}

/**
 * @description 获取重污站点统计
 * @method getHeavyTotal
 */
export function getHeavyTotal():AxiosPromise<any> {
  return request({
    url: '/water/sewage/enterprise/heavyPollutionOnlineCount',
    method: 'get'
  })
}

/**
 * @description 获取重污在线站点信息
 * @method getHeavilyData
 */
export function getHeavilyData() {
  return request({
    url: '/water/sewage/enterprise/heavyPollutionOnlineList',
    method: 'get'
  })
}
/**
 * @description 获取当前年每月数量
 * @method getCurrYearTotal
 */
export function getCurrYearTotal() {
  return request({
    url: '/water/sewage/enterprise/alarm/alarmMonthCount',
    method: 'get'
  })
}
/**
 * @description 获取当前月 重污类型告警统计
 * @method getCurrMonthTotal
 */
export function getCurrMonthTotal() {
  return request({
    url: '/water/sewage/enterprise/alarm/alarmThisMonthTypeCount',
    method: 'get'
  })
}

/**
 * @description 获取在线车辆列表
 * @method getOnlieCar
 */
export function getOnlieCar() {
  return request({
    url: '/car/big-data/onlineList',
    method: 'get'
  })
}
/**
 * @description 获取辆类型 的包含的车辆总数和 里程信息
 * @method getCarData
 */
export function getCarData() {
  return request({
    url: '/car/big-data/carTypeList',
    method: 'get'
  })
}

/**
 * @description 今日违规统计
 * @method thisDayCount
 */
export function thisDayCount(data:any) {
  return request({
    url: '/air/company-video-alarm/thisDayCount',
    method: 'get',
    params:data
  })
}

/**
 * @description 违规历史记录查询
 * @method pageList
 */
export function pageList(data:any) {
  return request({
    url: '/air/company-video-alarm/pageList',
    method: 'get',
    params:data
  })
}
/**
 * @description 获取 黑烟车证据链列表
 * @method pageList1
 */
export function pageList1(data:any) {
  return request({
    url: '/water/traffic/evidence/pageList',
    method: 'get',
    params:data
  })
}


/**
 * @description 获取 工地视频监控
 * @method listGroup
 */
export function listGroup(data:any) {
  return request({
    url: '/air/company/camera/listGroup',
    method: 'get',
    params:data
  })
}