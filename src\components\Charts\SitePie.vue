<template>
  <div :id="id" :style="{ height: height, width: width }"></div>
</template>

<script lang="ts">
import * as echarts from 'echarts'
import { Component, Prop } from "vue-property-decorator";
import { mixins } from "vue-class-component";
import ResizeMixin from "./mixins/resize";
interface InData {
  name: string;
  number: number;
  total: number;
  startColor: string;
  endColor: string;
}
@Component({
  name: "SitePie"
})
export default class extends mixins(ResizeMixin) {
  @Prop({ default: "chart" }) private id!: string;
  @Prop({ default: "200px" }) private width!: string;
  @Prop({ default: "200px" }) private height!: string;
  @Prop({ required: true }) private propData!: InData;
  mounted() {
    this.$nextTick(() => {
      setTimeout(() => {
        this.initChart();
      }, 2000);
    });
  }

  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.clear();
    this.chart.dispose();
    this.chart = null;
  }

  private initChart() {

    if(this.chart === null || this.chart === undefined) {
      this.chart = echarts.init(
        document.getElementById(this.id) as HTMLDivElement
      );
    }
    this.chart.setOption({
      title: {
        text: this.propData.name,
        left: "center",
        bottom: 0,
        textStyle: {
          color: "rgba(255,255,255,1)",
          fontSize: 16
        }
      },
      series: [
        {
          radius: ["78%", "80%"],
          center: ["50%", "41%"],
          hoverAnimation: false,
          // clockwise: false,
          type: "pie",
          itemStyle: {
            borderColor:
              this.propData.total == 0 && this.propData.number == 0
                ? "#B3B3B3"
                : new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: this.propData.startColor
                    },
                    {
                      offset: 1,
                      color: this.propData.endColor
                    }
                  ]),
            borderWidth:
              this.propData.total == 0 && this.propData.number == 0 ? 0 : 5
          },
          label: {
            show: true,
            rich: {
              a: {
                // color: "rgba(204, 204, 204, 1)",
                color: "rgba(255,255,255,1)",
                fontSize: 40
              },
              b: {
                // color: "rgba(204, 204, 204, 1)",
                color: "rgba(255,255,255,1)",
                fontSize: 16
              }
            },
            position: "center",
            formatter: (params: any) => {
              // return [`{a|${params.value}}`, "{b|个}"];
              return [`{a|${params.value}}`];
            }
          },
          data: [
            {
              value: this.propData.number
            },
            {
              value: this.propData.total - this.propData.number,
              itemStyle: {
                color: "#B3B3B3",
                borderWidth: 0
              },
              label: {
                show: false
              }
            }
          ]
        }
      ]
    } as EChartOption);
  }
}
</script>
