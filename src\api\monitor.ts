import request from '@/utils/request'

export function getGarageList(data) {
  return request({
    url: `/water/garage/record/listRecordByStation`,
    method: 'post',
    data,
  })
}

export function getGarageCamera(data) {
  return request({
    url: '/water/garage-camera/list',
    method: 'post',
    data,
  })
}

export function playbackListGarage(params) {
  return request({
    url: '/water/garage-camera/playbackPageList',
    method: 'get',
    params,
  })
}

export function playbackStartGarage(params) {
  return request({
    url: '/water/garage-camera/playbackStart',
    method: 'get',
    params,
  })
}

// 获取摄像头直播流的接口
export function getCarLive(cameSerial) {
  return request({
    url: `/water/garage-camera/getLiveAddress/${cameSerial}`,
    method: 'get',
  })
}

// 获取监控数据的接口
export function getMonitorData(params) {
  return request({
    url: '/water/monitor/data',
    method: 'get',
    params,
  })
}
