<template>
  <div class="taskFeedbackContent">
    <div class="back-text" :class="{ 'show-text': data.shuowText }">
      <span :class="content.length > 16 ? 'til' : ''">回复内容: </span>
      <div :class="{ oneline: !data.shuowText }">{{ content }}</div>
      <div v-if="content.length > 32" class="more-text" @click="showMoreText">
        {{ !data.shuowText ? '更多' : '收起' }}
      </div>
    </div>
    <div class="imh-box">
      <div
        class="img"
        :class="imgarr.length == 1 ? 'one_pic' : ''"
        v-for="(item, index) in imgarr"
        :key="index + 'taskimg'"
        :style="{
          display: data.imgFlag ? '' : index > 2 ? 'none' : '',
        }"
      >
        <div
          class="more-image"
          v-if="index == 2 && !data.imgFlag && imgarr.length > 2"
        >
          +{{ imgarr.length - 3 }}
        </div>
        <ComImage
          style="width: 120px; height: 120px"
          :src="item" 
          fit="cover"
          :preview-src-list="imgarr">
        </ComImage>
      </div>
    </div>
  </div>
</template>
<script>
// import Image from "ant-design-vue/lib/image/index";
// import InternalPreviewGroup from "ant-design-vue/lib/image/PreviewGroup";
import ComImage from '@/components/image/src/main.vue'
export default {
  // components:{
  //   AImage:Image,
  //   AImagePreviewGroup:InternalPreviewGroup
  // },
  props: {
    content: {
      type: String,
      default: ''
    },
    imgarr:{
      type: Array,
      default:() => []
    },
  },
  components:{
    ComImage
  },
  watch:{
    imgarr:{
      handler(){
        // console.log(this.imgarr,'---------imgarr------52');
      },
      immediate: true,
      deep: true
    }
  },
  data() {
    return {
      data: {
        imgFlag: false,
        shuowText: false,
      },
    }
  },
  methods: {
    moreImageClick() {
      this.data.imgFlag = true
    },
    showMoreText() {
      this.data.shuowText = !this.data.shuowText
    },
  },
}
</script>

<style lang="less" scoped>
.taskFeedbackContent {
  padding: 15px;
  padding-left: 20px;
  background-image: linear-gradient(
    to right,
    rgba(10, 66, 74, 0.5),
    rgba(10, 66, 74, 0)
  );
  .back-text {
    display: flex;
    margin-bottom: 15px;
    font-size: 14px;
    color: #d6f4ff;
    // overflow: hidden;
    // text-overflow: ellipsis;
    // -webkit-box-orient: vertical;
    // -webkit-line-clamp: 3;
    cursor: pointer;
    align-items: center;
    .til {
      margin-top: -22px;
    }
    .oneline {
      width: 16em;
      display: -webkit-box;
      -webkit-line-clamp: 2; /*多少行数之后显示为省略...*/
      word-wrap: break-word;
      word-break: break-all;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-box-orient: vertical;
    }

    .more-text {
      font-size: 14px;
      color: #00aaff;
      margin-bottom: -24px;
    }
  }
  .show-text {
    display: block;
  }
  .imh-box {
    display: flex;
    flex-wrap: wrap;
    overflow-x: scroll;
    margin-top: 24px;
    // height:80px;
    &::-webkit-scrollbar {
      display: none;
    }
    .img {
      width: 90px;
      height: 90px;
      overflow: hidden;
      // max-width: 130px;
      // max-height: 80px;
      margin-right: 10px;
      margin-bottom: 10px;
      position: relative;
      .more-image {
        position: absolute;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        color: #fff;
        font-size: 42px;
        z-index: 2;
        cursor: pointer;
        pointer-events: none;
      }
    }
    .one_pic {
      width: 130px;
      height: 86px;
    }
  }
}
</style>
<style lang="less">
.ant-image-preview-switch-left {
  left: 40px !important;
  .anticon-left {
    svg {
      width: 36px !important;
      height: 36px !important;
    }
  }
}
.ant-image-preview-switch-right {
  right: 40px !important;
  .anticon-right {
    svg {
      width: 36px !important;
      height: 36px !important;
    }
  }
}
</style>