<template>
  <div class="lineBox">
    <!-- <div class="text-box">
      <div @click="clickName" class="line-text" :class="choseText?'chose':''">{{dataObj.text}}</div>
    </div> -->
    <div class="line-box">
      <div
        class="line-point-box"
        @click.stop="pointClick(item)"
        v-for="(item, index) in dataObj.pointArr"
        :key="index + 'point'"
        :style="{ right: item.percent || '0' }"
      >
        <div
          class="line-point"
          :class="item.chose ? 'chose-point' : ''"
          :title="item.time || ''"
        >
          <div class="point"></div>
        </div>
        <div v-if="item.chose && item.content" class="conten-box">
          {{ item.userName }}：{{ item.content }}
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    dataObj: {
      type: Object,
      default: () => ({
        text: '',
        choseText: false,
        pointArr: [],
      }),
    },
    choseText: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      isMore: false,
      clickData: undefined,
    }
  },
  mounted() {
    // console.log('dataObj-------------49', this.dataObj)
  },
  methods: {
    clickName() {
      // this.isMore = !this.isMore
      // console.log('点击');
      this.$emit('getclick', this.dataObj.text)
    },
    pointClick(val) {
      console.log(val,'事件-----60');
      if (val.time) {
        this.clickData = val
        this.$emit('pointclick', val)
      }
    },
  },
}
</script>

<style lang="less" scoped>
.lineBox {
  width: 100%;
  display: flex;
  align-items: center;
  padding: 5px 0;
  .text-box {
    width: 195px;
    display: flex;
    justify-content: flex-end;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    color: #cee1f0;
    font-size: 13px;
    .line-text {
      padding: 2px 13px;
      padding-bottom: 0;
      border-radius: 4px;
      margin-right: 2px;
      text-align: center;
      cursor: pointer;
    }
    .chose {
      background-color: #1e6197;
    }
  }

  .line-box {
    position: relative;
    height: 2px;
    flex: 1;
    background-image: linear-gradient(to right, #3894ff, #041943);
    margin: 15px 0;
    .line-point-box {
      position: absolute;
      bottom: calc(50% - 10px);
      .conten-box {
        position: absolute;
        bottom: 0px;
        left: 15px;
        padding: 2px 5px;
        background-color: rgba(17, 79, 121, 0.5);
        border: 1px solid #3894ff;
        font-size: 12px;
        color: #cee1f0;
        width: 120px;
      }
    }
    .line-point {
      // position: absolute;
      cursor: pointer;
      width: 20px;
      height: 20px;
      // bottom: calc(50% - 10px);
      // left: 50%;
      border-radius: 50%;
      transition-property: border-color;
      transition-duration: 0.4s;
      border: 1px dashed #1487fd;
      animation: xunzhuan 2s linear infinite;
      &:hover {
        border-color: #39d0b1;
        .point {
          background-color: #39d0b1;
          box-shadow: 0px 0px 7px 2px #65e8e6;
        }
      }
      .point {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        margin: auto;
        height: 6px;
        width: 6px;
        border-radius: 50%;
        transition-property: background-color, box-shadow;
        transition-duration: 0.4s;
        background-color: #1487fd;
        animation: faguang 2s linear infinite;
      }
      @keyframes xunzhuan {
        0% {
          transform: rotate(0);
        }
        100% {
          transform: rotate(360deg);
        }
      }
      @keyframes faguang {
        0% {
        }
        50% {
          background-color: #39d0b1;
          box-shadow: 0px 0px 7px 1px #65e8e6;
        }
        100% {
        }
      }
    }
    .chose-point {
      width: 22px;
      height: 22px;
      border-color: #39d0b1;
      bottom: calc(50% - 11px);
      .point {
        background-color: #39d0b1;
        box-shadow: 0px 0px 7px 2px #65e8e6;
      }
      // transform: scale(1.5, 1.5);
    }
  }
}
</style>