<template>
  <div class="hello-ezuikit-js" ref="ezuikitBox">
    <div :id="'video-container_' + id"></div>
    <!--    <div v-if="!monitorDeviceState" style="width:2.92rem;height:2.12rem;background-color: black;text-align: center;line-height: 2.12rem">设备离线</div>-->
  </div>
</template>

<script>
  import EZUIKit from 'ezuikit-js'

  export default {
    name: 'ezuikitPlayer',
    props: {
      accessToken: {
        type: String,
        default: '',
      },
      url: {
        type: String,
        default: '',
      },
      width: {
        type: Number,
        default: 0,
      },
      height: {
        type: Number,
        default: 0,
      },
      id: {
        type: [String, Number],
        default: '',
      },
      monitorDeviceState: {
        type: Number,
        default: 0,
      },
    },
    data() {
      return {
        player: null,
      }
    },
    watch: {
      url(val) {
        if (val) {
          this.initVideo()
        }
      },
    },
    mounted() {
      this.initVideo()
    },
    beforeUnmount() {
      try {
        window.EZUIKitPlayer.stop()
        delete window['EZUIKitPlayer']
      } catch (error) {
        
      }
      // 销毁全局挂载的容器
      delete window['EZUIKit']['video-container']
      // 销毁全局挂载的js播放插件
      delete window['JSPlugin']
    },
    methods: {
      initVideo() {
        const ezuikitBox = this.$refs.ezuikitBox

        ezuikitBox.style.height = `${ezuikitBox.clientWidth * 0.5}px`

        try {
          window.EZUIKitPlayer = new EZUIKit.EZUIKitPlayer({
            autoplay: true,
            id: 'video-container_' + this.id,
            accessToken: this.accessToken,
            url: this.url,
            template: 'simple', // simple - 极简版;standard-标准版;security - 安防版(预览回放);voice-语音版；
            // 视频上方头部控件
            //header: ["capturePicture", "save", "zoom"], // 如果templete参数不为simple,该字段将被覆盖
            //plugin: ['talk'],                       // 加载插件，talk-对讲
            // 视频下方底部控件
            // footer: ["talk", "broadcast", "hd", "fullScreen"], // 如果template参数不为simple,该字段将被覆盖
            // audio: 1, // 是否默认开启声音 0 - 关闭 1 - 开启
            // openSoundCallBack: data => console.log("开启声音回调", data),
            // closeSoundCallBack: data => console.log("关闭声音回调", data),
            // startSaveCallBack: data => console.log("开始录像回调", data),
            // stopSaveCallBack: data => console.log("录像回调", data),
            // capturePictureCallBack: data => console.log("截图成功回调", data),
            // fullScreenCallBack: data => console.log("全屏回调", data),
            // getOSDTimeCallBack: data => console.log("获取OSDTime回调", data),
            width: ezuikitBox.clientWidth,
            height: ezuikitBox.clientWidth * 0.5,
          })

          // this.player.play()
        } catch (error) {
          console.log('初始化播放错误', error)
        }
      },
    },
  }
</script>

<style lang="less" scoped>
  .hello-ezuikit-js {
    width: 100%;
  }
</style>
