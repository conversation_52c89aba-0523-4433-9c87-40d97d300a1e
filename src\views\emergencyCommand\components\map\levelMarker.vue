<template>
  <div class="level-box" ref="marker">
    <img :src="levelObj[level]" />
  </div>
</template>
<script>
  import yj_marker_yiji from '@/assets/images/<EMAIL>';
  import yj_marker_erji from '@/assets/images/<EMAIL>';
  import yj_marker_sanji from '@/assets/images/<EMAIL>';
  import yj_marker_siji from '@/assets/images/<EMAIL>';
  export default {
    name:"levelMark",
    props:{
      level: {
        type: Number,
        default: 1,
      },
    },
    data(){
      return{
        levelObj:{
          1: yj_marker_yiji,
          2: yj_marker_erji,
          3: yj_marker_sanji,
          4: yj_marker_siji,
        }
      }
    },
  }
</script>

<style lang="less" scoped>
  .level-box {
    width: 151px;
    height: 115px;

    img {
      width: 100%;
      height: 100%;
    }
  }
</style>
