<template>
  <div :class="excutor.highlight ? 'highlight' : ''">
    <bigBox
      :hiddenbtom="hiddenbtom"
      :person="person"
      :time="excutor.completeTime || ''"
    >
      <contentT :content="excutor.content" :imgarr="imgarr"></contentT>
    </bigBox>
  </div>
</template>

<script>
import bigBox from './bigBox.vue'
import contentT from './content.vue'
export default {
  name: 'taskFeedbackItem',
  props: {
    hiddenbtom: {
      type: Boolean,
      default: false,
    },
    excutor: {
      type: Object,
      default: () => ({
        userName: '',
        departmentName: '',
        completeTime: '',
        content: '',
        taskAnnexList: [],
      }),
    },
  },
  components: {
    bigBox,
    contentT,
  },
  computed: {
    person() {
      if (this.excutor.departmentName) {
        return `${this.excutor.departmentName}-${this.excutor.userName}`
      } else {
        return `暂无任务反馈`
      }
    },
    imgarr() {
      if (this.excutor.taskAnnexList) {
        const arr = this.excutor.taskAnnexList.map((e) => e.annexUrl)
        return arr
      }
      return []
    },
  },
}
</script>

<style scoped>
.highlight {
  background-color: rgba(4, 30, 66, 0.5);
}
</style>
