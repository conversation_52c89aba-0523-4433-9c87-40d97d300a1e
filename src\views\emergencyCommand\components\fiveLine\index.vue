<template>
  <div class="fiveLine-box">
    <img
      class="fiveLine-icon"
      :class="showFlag ? 'icon-xuan' : ''"
      src="@/assets/images/<EMAIL>"
      alt=""
      @click="showClick"
    />
    <div class="line-box" :class="showFlag ? 'show-line' : ''">
      <div class="line-block">
        <!-- 部门名称 -->
        <div :class="list.length < 4 ? 'gcenter' : ''">
          <div class="text-box">
            <div v-for="(i, n) in list" :key="n + 'tex'" class="text-box-item">
              <div
                class="item"
                :class="i.choseText ? 'choseI' : ''"
                @click="nameClick(i)"
              >
                {{ i.text }}
              </div>
            </div>
          </div>
        </div>
        <!-- 线条 -->
        <div
          :class="list.length < 4 ? 'gcenter' : ''"
          style="width: calc(100% - 200px)"
        >
          <div class="liney-box">
            <div
              class="liney-block"
              :style="{
                width: !chosetime ? offsetWidth + 'px' : '0',
                height: !chosetime ? '' : '0',
              }"
            >
              <LineItem
                @pointclick="pointClick"
                v-for="(i, n) in list"
                :key="n + 'lin'"
                :choseText="i.choseText"
                :dataObj="i"
              ></LineItem>
            </div>
            <div
              class="liney-block"
              v-if="chosetime"
              :style="{
                width: chosetime ? offsetWidth2 + 'px' : '0',
                height: chosetime ? '' : '0',
              }"
            >
              <LineItem
                @pointclick="pointClick"
                v-for="(i, n) in daylist"
                :key="n + 'day'"
                :choseText="i.choseText"
                :dataObj="i"
              ></LineItem>
            </div>
          </div>
        </div>
      </div>
      <!-- 时间刻度 -->
      <div class="tiem-ke">
        <div v-show="!chosetime" class="tiem-ke-box" style="display: flex">
          <span
            class="ke-item"
            v-for="(ite, ind) in timeList"
            :key="ind + 'ti'"
            >{{ ite }}</span
          >
        </div>
        <div
          v-show="chosetime"
          class="day-ke-box tiem-ke-box"
          style="display: flex"
        >
          <span
            class="day-key"
            v-for="(ite, ind) in dayArr"
            :key="ind + 'dy'"
            >{{ ite }}</span
          >
        </div>
      </div>
      <!-- 改变时间 -->
      <div class="time-change">
        <div
          class="time-hour"
          :class="chosetime ? '' : 'time-active'"
          @click="timeChange('hour')"
        >
          按小时
        </div>
        <div
          class="time-tian"
          :class="chosetime ? 'time-active' : ''"
          @click="timeChange('day')"
        >
          &ensp;按天&ensp;
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import LineItem from '@/components/fiveLine/index.vue'
import moment from 'moment'
const timeL = [
  '00:00',
  '01:00',
  '02:00',
  '03:00',
  '04:00',
  '05:00',
  '06:00',
  '07:00',
  '08:00',
  '09:00',
  '10:00',
  '11:00',
  '12:00',
  '13:00',
  '14:00',
  '15:00',
  '16:00',
  '17:00',
  '18:00',
  '19:00',
  '20:00',
  '21:00',
  '22:00',
  '23:00',
  // '23:59'
]
export default {
  name: 'fiveLine',
  components: {
    LineItem,
  },
  props: {
    detailData: {
      type: Object,
      default: () => ({
        data: [],
      }),
    },
  },
  data() {
    return {
      choseData: {},
      detailDatas: [], // 线谱总数据
      detailDataTime: [], // 起止时间
      dayArr: [],
      sumHours: 0, // 总小时数
      showFlag: false,
      timeList: [],
      list: [],
      daylist: [],
      chosetime: false,
      offsetWidth: 570,
      offsetWidth2: 570,
      gundom: {},
      lineDom: {},
      dayObj: {
        percent: '0px',
      },
    }
  },
  watch: {
    detailData: {
      handler(oval, nval) {
        // console.log(nval, '线谱的数据改变----------------185')
        if (nval && nval.data && nval.data.length) {
          this.detailDatas = nval.data
          this.detailDataTime = nval.time
          // console.log('nval.time======================189',nval);

          const { sumHour, hourArr } = this.extendTime(
            nval.time[0],
            nval.time[1]
          )
          this.sumHours = sumHour
          this.timeList = hourArr || []
          console.log('小时数据1', hourArr)

          this.getLinePoint()
          this.getdayArr()
        } else {
          this.detailDatas = []
          this.detailDataTime = []
          this.timeList = timeL.slice(0, 6)
          this.list = [
            { pointArr: [{}], text: '金牛生态环境局' },
            { pointArr: [{}], text: '金牛综合执法局' },
            { pointArr: [{}], text: '金牛住建交局' },
            { pointArr: [{}], text: '金牛农业和水务局' },
          ]
          // console.log('小时数据2', this.timeList, this.list)
          ;(this.daylist = [
            { pointArr: [{}], text: '金牛生态环境局' },
            { pointArr: [{}], text: '金牛综合执法局' },
            { pointArr: [{}], text: '金牛住建交局' },
            { pointArr: [{}], text: '金牛农业和水务局' },
          ]),
            (this.dayArr = [])
          for (let i = 0; i < 6; i++) {
            this.dayArr.push(
              moment()
                .subtract(5 - i, 'day')
                .format('YYYY-MM-DD')
            )
          }
        }
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {
    this.gundom = document.querySelector('.tiem-ke')
    this.lineDom = document.querySelector('.liney-box')
    this.timeScoll(this.gundom, 'time')
    this.timeScoll(this.lineDom, 'line')
    // const obj = this.extendTime('2022-03-17 03:21','2022-03-18 21:53')
    // this.timeList = obj.hourArr
    const dom1 = document.querySelector('.layout-container')
    // console.log(dom1,'dom1');
    if (dom1) {
      dom1.addEventListener('click', () => {
        this.choseData.chose = false
      })
    }
  },
  methods: {
    showClick() {
      this.showFlag = !this.showFlag
    },
    /**
     * 按天、小时切换
     */
    timeChange(val) {
      this.gundom.scrollLeft = 0
      this.lineDom.scrollLeft = 0
      if (val == 'day') {
        this.chosetime = true
        this.initPonitLocal(this.list, 'day')
      } else {
        this.chosetime = false
        this.initPonitLocal(this.daylist)
      }
      if (!this.offsetWidth) {
        this.offsetWidth = document.querySelector('.tiem-ke-box').offsetWidth
      }
      if (!this.offsetWidth2) {
        this.offsetWidth2 = document.querySelector('.day-ke-box').offsetWidth
      }
    },
    /**
     * 获取天的点数据 及 刻度
     */
    getdayArr() {
      const that = this
      let dayNum =
        Math.ceil(
          (new Date(this.detailDataTime[1]).getTime() -
            new Date(this.detailDataTime[0]).getTime()) /
            (1000 * 60 * 60 * 24)
        ) + 1
      console.log(dayNum, '天数')
      const arr = []
      dayNum = dayNum >= 7 ? dayNum : 7
      for (let i = 0; i < dayNum; i++) {
        arr.push(
          moment(this.detailDataTime[0]).add(i, 'day').format('YYYY-MM-DD')
        )
      }
      console.log(arr, '日期')
      // 按天的刻度数组 天倒序
      this.dayArr = arr.reverse()

      // 获取按天的点数据
      const arr2 = []
      this.detailDatas.forEach((e) => {
        let obj = {
          text: e.departmentName,
          choseText: false,
          // pointArr: [] as any,
          departmentId: e.departmentId,
          dayArr: [],
          pointArr: [],
        }
        if (e.departmentExecutor) {
          // 获取不重复的天数组
          e.departmentExecutor.forEach((e2) => {
            const day = moment(e2.completeTime).format('YYYY-MM-DD')
            const ind = obj.dayArr.findIndex((i) => i == day)
            if (ind == -1) {
              obj.dayArr.push(day)
            }
          })
          // 获取天数组的某项在刻度数组的下标
          obj.dayArr.forEach((d) => {
            const num = that.dayArr.findIndex((d2) => d2 == d)
            if (num !== -1) {
              const juli = (num + 1) * 103 - 51.5 - 10 + 'px'
              let objSon = {
                time: d,
                departmentId: e.departmentId,
                chose: false,
                departmentName: e.departmentName,
                percent: juli,
              }
              obj.pointArr.push(objSon)
            }
          })
        }
        arr2.push(obj)
      })
      this.dayArr = arr.reverse()
      // this.$nextTick(()=>{
      this.offsetWidth2 = this.dayArr.length * 103
      // })
      // console.log(this.offsetWidth2, '长度')

      this.daylist = arr2
      console.log(arr2, '按天点数据')
    },

    // 同时滚动
    timeScoll(dom, type) {
      // const dom = this.gundom
      let flag = false
      let downX = 0
      let scrollLeft = 0
      const that = this

      dom.addEventListener('mousedown', function (event) {
        flag = true
        downX = event.clientX
        scrollLeft = dom.scrollLeft
        console.log('mousedown', scrollLeft)
      })
      // 鼠标移动
      dom.addEventListener('mousemove', function (event) {
        //  console.log('move')
        if (flag) {
          let moveX = event.clientX
          let scrollX = moveX - downX
          // console.log("moveX" + moveX);
          // console.log("scrollX" + scrollX);
          // if (scrollX < 0 && scrollLeft > 0) {
          //   dom.scrollLeft = scrollLeft - scrollX
          // } else {
          dom.scrollLeft = scrollLeft - scrollX
          // }
          // 是线还是时间拖动
          if (type == 'time') {
            that.lineDom.scrollLeft = scrollLeft - scrollX
          } else {
            that.gundom.scrollLeft = scrollLeft - scrollX
          }
        }
      })
      // 鼠标释放
      dom.addEventListener('mouseup', function () {
        // console.log('mouseup')
        // this.gundom.removeEventListener('mousemove',)
        flag = false
      })
      dom.addEventListener('mouseleave', function (event) {
        // console.log(
        //   'mouseleave',
        //   event.pageX,
        //   document.body.offsetWidth,
        // )

        flag = false
        if (event.pageX < 0 || event.pageX > document.body.offsetWidth) {
          // console.log('在元素上移出')
          flag = false
        }
      })
    },
    /**
     * 按小时线谱上的点数据
     */
    getLinePoint() {
      const star1 = this.timeList[0]
      const starTime = moment(this.detailDataTime[0]).format(
        `YYYY-MM-DD ${star1}`
      ) // 开始时刻

      const that = this
      const arr = this.detailDatas.map((e) => {
        let obj = {
          text: e.departmentName,
          choseText: false,
          pointArr: [],
        }
        if (e.departmentExecutor) {
          e.departmentExecutor.forEach((e2) => {
            if (e2.completeTime) {
              let objSon = {
                time: e2.completeTime,
                taskLogId: e2.taskLogId,
                logTypeId: e2.logTypeId,
                userName: e2.userName,
                content: e2.content,
                chose: false,
                percent: that.timePosion(
                  that.sumHours,
                  starTime,
                  e2.completeTime
                ),
              }
              console.log('点位', objSon)

              obj.pointArr.push(objSon)
            }
          })
        }
        return obj
      })
      this.list = arr
      this.initPonitLocal(arr)
      console.log('小時-------------------447', arr)
    },
    // 点击天定位
    dayLocal(name, time) {
      this.dayObj = { percent: '' }
      this.list.forEach((item) => {
        if (item.text == name) {
          this.dayObj = item.pointArr.find((point) => point.time.includes(time))
        }
      })
      const str = this.dayObj.percent
      console.log(
        +str.slice(0, str.length - 2),
        this.offsetWidth - +str.slice(0, str.length - 2) - 103,
        '-------------------461'
      )
      this.lineDom.scrollLeft =
        this.offsetWidth - +str.slice(0, str.length - 2) - 103
      this.gundom.scrollLeft =
        this.offsetWidth - +str.slice(0, str.length - 2) - 103
    },
    // 定位点位置
    initPonitLocal(list, type = 'time') {
      let initarr = []
      list.forEach((it) => {
        it.pointArr.forEach((point) => {
          initarr.push(+point.percent.slice(0, point.percent.length - 2))
        })
      })
      initarr = initarr.sort((a, b) => b - a)
      console.log(initarr, 'initarr-----------------477')

      if (type == 'time') {
        setTimeout(() => {
          this.lineDom.scrollLeft = this.offsetWidth - initarr[0] - 103
          this.gundom.scrollLeft = this.offsetWidth - initarr[0] - 103
        }, 1500)
        console.log(
          this.offsetWidth - initarr[0] - 103,
          '---------------------459'
        )
      } else {
        setTimeout(() => {
          this.lineDom.scrollLeft = this.offsetWidth2 - initarr[0] - 103
          this.gundom.scrollLeft = this.offsetWidth2 - initarr[0] - 103
        }, 1500)
        console.log(
          this.offsetWidth2 - initarr[0] - 103,
          '---------------------459'
        )
      }
    },
    /**
     * 线上时间点击
     */
    pointClick(val) {
      console.log(val, '线上时间点击')
      if (val.departmentId) {
        this.chosetime = false
        this.lineDom.scrollLeft = 0
        this.gundom.scrollLeft = 0
        setTimeout(() => {
          this.dayLocal(val.departmentName, val.time)
        }, 1500)
      }
      // 选中效果点
      const list = val.departmentId ? this.daylist : this.list
      list.forEach((e) => {
        e.pointArr.forEach((e2) => {
          const flag = val.departmentId
            ? e2.departmentId == val.departmentId
            : e2.taskLogId == val.taskLogId
          if (flag) {
            this.choseData = e2
            e2.chose = true
          } else {
            e2.chose = false
          }
        })
      })
      // 不是完成节点，不继续向外传递事件 // 操作类型logTypeId: 4完成节点 6已读 7指派任务
      // if (val.logTypeId !== 4 && val.content) {
      //   setTimeout(() => {
      //     val.chose = false
      //   }, 5000)
      //   return false
      // }

      // 重置部门选中状态
      if (Object.keys(val).length == 1 && Object.keys(val)[0] == 'name') {
        //由部门名点击触发
      } else {
        this.list.forEach((e) => {
          e.choseText = false
        })
      }
      this.$emit('pointclicks', val)
    },
    /**
     * 部门名点击
     */
    nameClick(item) {
      this.list=this.list.map((e) => {
        if (e.text !== item.text) {
          return{
            ...e,
            choseText: false
          }
        }else{
          return{
            ...e,
            choseText: !e.choseText
          }
        }
      })
      if (item.choseText) {
        this.pointClick({ name: item.text })
      } else {
        this.pointClick({ name: '' })
      }
    },
    /**
     * 获取一个时间区间小时数
     * @param startTime 开始日期 2022-03-17 03:21
     * @param endTime 结束日期
     */
    extendTime(startTime, endTime) {
      // const date = new Date()
      let hourArr = []
      let startHour1 = moment(startTime).format('HH:00')
      let startHour = moment(startTime).format('YYYY-MM-DD HH:00')
      let endHour1 = moment(endTime).add(1, 'hour').format('HH:00')
      let endHour = moment(endTime).add(1, 'hour').format('YYYY-MM-DD HH:00')
      let sumHour = Math.ceil(
        (new Date(endHour).getTime() - new Date(startHour).getTime()) /
          (1000 * 60 * 60)
      )
      console.log('总的小时', sumHour)
      let sumHour2 = sumHour > 7 ? sumHour : 7

      // 开始时刻在24小时内的下标
      let strIndex = timeL.findIndex((e) => e === startHour1)
      if (strIndex == -1) {
        console.log('获取开始时间失败', startHour)
        return {}
      }

      // if (sumHour<=12) {
      //   if(strIndex<=12){
      //     hourArr = timeL.filter((e,i)=>i<=12&&i%2==0)||[]
      //   }else{
      //     hourArr = timeL.filter((e,i)=>i>=12&&i%2==0)||[]
      //   }
      //   sumHour = 12
      // }else if (sumHour<=24){
      //    hourArr = timeL.filter((e,i)=>i%2==0)||[]
      //    sumHour = 24
      // }else{
      // 生成间隔两小时的时刻数组
      // for (let i = 0; i <= Math.ceil(sumHour/2); i++) {
      //   hourArr.push(timeL[strIndex])
      //   if (strIndex<timeL.length-2) {
      //     strIndex = strIndex+2
      //   }else{
      //     strIndex = strIndex/2==0?0:1
      //   }
      // }

      for (let i = 0; i < Math.ceil(sumHour2); i++) {
        hourArr.push(timeL[strIndex])
        if (strIndex < timeL.length - 1) {
          strIndex = strIndex + 1
        } else {
          strIndex = 0
        }
      }
      sumHour >= 7 ? hourArr.push(endHour1) : ''
      // hourArr.push(endHour1)
      // 倒序
      // hourArr.reverse()
      this.offsetWidth = hourArr.length * 103
      // }

      return {
        sumHour,
        startHour1,
        endHour1,
        hourArr,
      }
    },
    /**
     * 返回 按小时查看某个点位置
     * @parms sumHour 时间区间的小时数量
     * @parms stTime 时间区间开始时刻
     * @parms atime 时间区间内某个时刻
     */
    timePosion(sumHour, stTime, atime) {
      // console.log(sumHour, stTime, atime, this.timeList)
      const hour =
        (new Date(atime).getTime() - new Date(stTime).getTime()) /
        (1000 * 60 * 60)
      // console.log(hour, '相差小时')

      // const num = Number((hour/(sumHour)).toFixed(4))
      // return `calc(${ (Math.ceil(num*1000)/10)-1+'%'} - '10px')`
      // console.log('sumHour-----------------------588',sumHour,hour);

      // 103px单个刻度div宽度，10px线普上点的半径
      // console.log('atimeatimeatime-----589',sumHour,'-y-',stTime,'-y-',atime);
      // console.log('((sumHour - hour + 1) * 103 - 51.5 - 10).toFixed(2)----590',(sumHour - hour + 1),((sumHour - hour + 1) * 103 - 51.5 - 10).toFixed(2) + 'px');

      // return ((sumHour - hour + 1) * 103 - 51.5 - 10).toFixed(2) + 'px'
      return ((this.timeList.length - hour) * 103 - 51.5 - 10).toFixed(2) + 'px'
    },
  },
}
</script>

<style lang="less" scoped>
.fiveLine-box {
  height: 50px;
  width: 100%;
  // background-color: aquamarine;
  background-image: url('../../../../assets/images/<EMAIL>');
  position: relative;
  .fiveLine-icon {
    width: 37px;
    height: 13px;
    position: absolute;
    top: 13px;
    left: calc(50% - 18px);
    cursor: pointer;
    transition-duration: 0.3s;
  }
  .icon-xuan {
    transform: rotate(180deg);
  }
  .line-box {
    // height: 160px;
    position: absolute;
    bottom: -180px;
    width: 900px;
    left: calc(50% - 450px);
    transition-property: bottom;
    transition: all 0.5s;
    opacity: 0;
    .line-block {
      display: flex;
      overflow-y: scroll;
      height: 170px;
      &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
        background-color: #a6d5eb;
        border-radius: 6px;
        display: none;
      }
      &::-webkit-scrollbar-thumb {
        border-radius: 10px;
        background-color: #094886;
        cursor: pointer;
      }
      .gcenter {
        display: flex;
        align-items: center;
      }
    }
    .text-box {
      width: 200px;
      .text-box-item {
        height: 42px;
        //  line-height: 42px;
        text-align: right;
        color: #fff;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        .item {
          margin: 0 15px;
          padding: 2px 10px;
          cursor: pointer;
        }
        .choseI {
          background-color: #094886;
        }
      }
    }
    .liney-box {
      width: 100%;
      display: inline-block;
      overflow-x: scroll;
      &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
        background-color: #a6d5eb;
        border-radius: 6px;
        display: none;
      }
      &::-webkit-scrollbar-thumb {
        border-radius: 10px;
        background-color: #094886;
        cursor: pointer;
      }
      .liney-block {
        // transition-property: ;
        transition: 1.5s;
      }
    }
  }
  .show-line {
    bottom: 80px;
    opacity: 1;
  }
  .tiem-ke {
    width: 700px;
    // position: absolute;
    // bottom: -10px;
    // left: calc(50% - 450px);
    display: flex;
    box-sizing: border-box;
    // z-index: 99;
    margin-left: 200px;
    overflow-x: scroll;
    cursor: move;
    // transition-property: bottom;
    // transition:all 0.5s;
    // opacity: 0;
    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
      background-color: #a6d5eb;
      border-radius: 6px;
      display: none;
    }
    &::-webkit-scrollbar-thumb {
      border-radius: 10px;
      background-color: #094886;
      cursor: pointer;
    }
    .ke-item {
      user-select: none;
      // position: absolute;
      display: inline-block;
      font-size: 13px;
      color: #fff;
      margin: 0 35px;
      // margin-right: 70px;
      // padding-left: 50px;
      width: 33px;
      &:last-child {
        // margin-right: 0;
      }
    }
    .day-key {
      display: inline-block;
      font-size: 13px;
      color: #fff;
      margin: 0;
      width: 103px;
      text-align: center;
      user-select: none;
    }
  }
  .show-time {
    bottom: 70px;
    opacity: 1;
  }
  .time-change {
    position: absolute;
    top: -15px;
    right: 0;
    display: flex;
    color: #fff;
    font-size: 12px;
    & > div {
      background-color: rgba(25, 74, 125, 0.5);
      &:hover {
        background: #194a7d;
      }
      transition-duration: 0.3s;
      cursor: pointer;
    }
    .time-active {
      background-color: #18589c;
    }
    .time-tian {
      padding: 2px 8px;
      border-right: 1px solid #a6d5eb;
    }
    .time-hour {
      padding: 2px 8px;
    }
  }
}
</style>
