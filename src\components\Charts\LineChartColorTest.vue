<template>
  <div :id="id" :style="{ height: height, width: width }" />
</template>
<script lang="ts">
import * as echarts from 'echarts'
import { Component, Prop, Watch } from "vue-property-decorator";
import { mixins } from "vue-class-component";
import ResizeMixin from "./mixins/resize";
interface AirData {
  bottomList: string[];
  dataList: string[];
}
@Component({
  name: "LineChartColor"
})
export default class extends mixins(ResizeMixin) {
  // @Prop({ default: "chart" }) private className!: string;
  @Prop({ default: "chart" }) private id!: string;
  @Prop({ default: "200px" }) private width!: string;
  @Prop({ default: "200px" }) private height!: string;
  @Prop({ required: true }) private propData!: any;
  private dataList1: any[] = [];
  private dataList2: any[] = [];
  private dataList3: any[] = [];
  private dateList: any[] = [];
  private descList: any[] = [];

  @Watch("propData", { immediate: true, deep: true })
  public onMsgChanged(newValue: AirData, oldValue: AirData) {
    this.propData = newValue;
    if (this.chart) {
      this.chart.clear();
    }
    this.$nextTick(() => {
      this.dateList = this.propData.map((item: { x: any; }) => item.x)
      // this.dateList = this.propData.map((item: any) => item.x.split('月')[1])
      this.dataList1 = this.propData.map((item: { min: any; })=>item.min)
      this.dataList2 = this.propData.map((item: { max: any; })=>item.max)
      this.dataList3 = this.propData.map((item: { max: any; min: any; })=>{
        return Number(item.max) - Number(item.min)
      })
      this.descList = this.propData.map((item: { des: any; })=>item.des)
      this.initChart();
    });
  }
  // AQI
  readonly AQIAirColors: Array<any> = [
    {
      color: "rgb(0,255,0)",
      min: 0,
      max: 50,
      text: "优"
    },
    {
      color: "rgb(255,255,0)",
      min: 51,
      max: 100,
      text: "良"
    },
    {
      color: "rgb(255,126,0)",
      min: 101,
      max: 150,
      text: "轻度污染"
    },
    {
      color: "rgb(255,0,0)",
      min: 151,
      max: 200,
      text: "中度污染"
    },
    {
      color: "rgb(153,0,76)",
      min: 201,
      max: 300,
      text: "重度污染"
    },
    {
      color: "rgb(126,0,35)",
      min: 300,
      max: 999,
      text: "严重污染"
    }
  ];

  mounted() {
    this.$nextTick(() => {
      this.dateList = this.propData.map((item: { x: any; }) => item.x)
      // this.dateList = this.propData.map((item: any) => item.x.split('月')[1])
      this.dataList1 = this.propData.map((item: { min: any; })=>item.min)
      this.dataList2 = this.propData.map((item: { max: any; })=>item.max)
      this.dataList3 = this.propData.map((item: { max: any; min: any; })=>{
        return Number(item.max) - Number(item.min)
      })
      this.descList = this.propData.map((item: { des: any; })=>item.des)
      this.initChart();
    });
  }

  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.clear();
    this.chart.dispose();
    this.chart = null;
  }

  private initChart() {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const _this = this;

    if(this.chart === null || this.chart === undefined) {
      this.chart = echarts.init(
        document.getElementById(this.id) as HTMLDivElement
      );
    }
    this.chart.setOption({
      backgroundColor: "transparent",
      grid: {
        height: "80%",
        bottom: 20,
        left: "left",
        containLabel: true
      },
      xAxis: {
        show: true,
        type: "category",
        data: this.dateList.map(item => item.replace(/\n/g, '')),
        axisLabel: {
          show: true,
          // interval: 0, rotate: 30,
          textStyle: {
            fontSize: 12,
            color: "white"
          }
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        }
      },
      yAxis: [
        {
          type: "value",
          max: "300",
          axisLabel: {
            textStyle: {
              fontSize: 12,
              color: "white"
            }
          },
          axisLine: {
            lineStyle: {
              width: 7,
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: "rgb(126,0,35)"
                  },
                  {
                    offset: 0.2,
                    color: "rgb(153,0,76)"
                  },
                  {
                    offset: 0.4,
                    color: "rgb(255,0,0)"
                  },
                  {
                    offset: 0.6,
                    color: "rgb(255,126,0)"
                  },
                  {
                    offset: 0.8,
                    color: "rgb(255,255,0)"
                  },
                  {
                    offset: 1,
                    color: "rgb(0,255,0)"
                  }
                ],
                global: false // 缺省为 false
              }
            }
          },
          axisTick: {
            show: false
          },
          splitLine: {
            show: false
          }
        }
        // {
        //   axisLabel: {
        //     textStyle: {
        //       fontSize: 12,
        //       color: "white",
        //       align: "center"
        //     }
        //   },
        //   axisLine: {
        //     show: false
        //   },
        //   axisTick: {
        //     show: false
        //   },
        //   offset: 20,
        //   data: ["优", "良", "轻度污染", "中度污染", "重度污染", "严重污染"]
        // }
      ],

      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "line",
          label: {
            backgroundColor: "#6a7985"
          }
        },
        formatter: (params: Array<any>) => {
          let index = `${params[0].dataIndex}`;
          console.log(this.descList[index])
          return this.descList[index].replace(/\n/g, '')
        },
        extraCssText:'width:180px; white-space:pre-wrap;text-align:left'
      },
      series: [
        {
          type: "bar",
          barWidth: 18, // 柱子宽度
          name: "指数",
          data: this.dataList1.map(item=>{return 300}),
          // stack: 'Total',
          barGap: "-100%", /*这里设置包含关系，只需要这一句话*/
          itemStyle:{
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 1, color: "rgb(0,255,0)" },
              { offset: 0.8, color: "rgb(255,255,0)"},
              { offset: 0.6, color: "rgb(255,126,0)" },
              { offset: 0.4, color: "rgb(255,0,0)" },
              { offset: 0.2, color: "rgb(153,0,76)" },
              { offset: 0, color: "rgb(126,0,35)" }
            ])
          }
        },
        {
          type: "bar",
          name: "指数",
          barWidth: 18, // 柱子宽度
          data: this.dataList1,
          stack: 'Total',
          itemStyle: {
             borderColor: 'rgb(3,9,35)',
             color: 'rgb(3,9,35)'
          }
        },
        {
          type: "bar",
          name: "指数",
          barWidth: 18, // 柱子宽度
          data: this.dataList3,
          stack: 'Total',
          itemStyle: {
            borderColor: 'transparent',
            color: 'transparent'
          },
        },
        {
          type: "bar",
          name: "指数",
          barWidth: 18, // 柱子宽度
          data: this.dataList2.map( item => {
            return 300 - item
          }),
          stack: 'Total',
          itemStyle: {
             borderColor: 'rgb(3,9,35)',
             color: 'rgb(3,9,35)'
          }
        },
      ]
    } as {});
  }
}
</script>
