<style lang="less" scoped>
.main {
  height: 100%;
  width: 4.5rem;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  .panel-top {
    > div {
      display: flex;
      align-items: center;
      margin-bottom: 0.2rem;
      > .icon {
        width: 0.55rem;
        height: 0.55rem;
      }
      > * {
        margin-right: 0.5rem;
      }
      > div {
        color: #b3b3b3;
        font-size: 0.18rem;
        .count {
          color: #4abcff;
          font-size: 0.3rem;
        }
      }
    }
  }
  .panel-center {
  }
  .panel-bottom {
  }
  .chart-title {
    color: #cccccc;
    font-size: 0.2rem;
    margin-bottom: 0.05rem;
  }
}
.left-parts {
  > div {
    width: 4rem;
  }
  .title {
    font-size: 0.2rem;
    cursor: pointer;
  }
  .flex-number {
    cursor: pointer;
    display: flex;
    margin-top: 0.18rem;
    > div {
      border: 1px solid RGBA(33, 109, 253, 1);
      background-color: RGBA(35, 211, 255, 0.6);
      font-size: 0.4rem;
      margin-right: 0.08rem;
      text-align: center;
      padding: 0rem 0.12rem;
      font-family: "300-CAI978";
    }
  }
  .video-main {
    margin-top: 3rem;
    .sub-title {
      > img {
        width: 50%;
        height: 0.1rem;
      }
    }
    .box-title {
      justify-content: start;
      .title {
        margin-right: 0.5rem;
      }
    }
    .title-video {
      display: flex;
      align-items: center;
    }
    img {
      width: 1.2rem;
    }
  }
}
.left-part-one, .left-part-two, .left-part-three {
  width: 4rem;
  .monitoring-statistics {
    display: flex;
    flex-wrap: wrap;
    margin-top: 0.4rem;
    .list {
      width: 50%;
      display: flex;
      align-items: center;
      height: 0.67rem;
      margin-bottom: 0.37rem;
      .right {
        margin-left: 0.15rem;
        display: flex;
        flex-direction: column;
        height: 100%;
        justify-content: space-between;
        .title {
          color: #B3B3B3;
          font-size: 0.19rem;
          font-weight: 500;
        }
        .amount {
          font-size: 0.16rem;
          color: #B3B3B3;
          .number {
            color: #32CEFF;
            font-size: 0.32rem;
          }
        }
      }
    }
  }
  .image {
    width: 0.67rem;
    height: 0.67rem;
  }
  .title {
    font-size: 0.24rem;
    color: #cccccc;
  }
  .sub-title {
    color: #BBD3FF;
    font-size: 12px;
    text-align: right;
  }
}
</style>

<template>
  <section class="main">
    <section class="left-part-one">
      <div class="title">污染源监测统计</div>
      <div class="monitoring-statistics">
        <div class="list" v-for="(item, index) in amountData" :key="index">
          <img :src="item.type === 1 ? icon1 : item.type === 2 ? icon2 : item.type === 3 ? icon3 : icon4" class="image" alt="">
          <div class="right">
            <div class="title">{{ item.type === 1 ? '在建工地' : item.type === 2 ? '汽修印刷' : item.type === 3 ? '餐饮油烟' : '加油站' }}</div>
            <div class="amount"><span class="number">{{ item.number }}</span>个</div>
          </div>
        </div>
      </div>
    </section>
    <section class="left-part-two">
      <div class="title">污染源告警趋势</div>
      <alarmTrend
        :width="'4rem'"
        :height="'1.75rem'"
        :id="'alarmTrend'"
        :propData="alarmData"
        :smooth="true"
      />
    </section>
    <section class="left-part-three">
      <div class="title">污染源告警统计</div>
      <div class="sub-title">数据统计:本月</div>
      <alarmStatistics
        :width="'4rem'"
        :height="'1.75rem'"
        :id="'alarmStatistics'"
        :propData="alarmStatisticsData"
        :smooth="true"
      />
    </section>
    <!--    <section class="left-parts">-->
    <!--      <div>-->
    <!--        <div class="title" style="letter-spacing:2px">企事业单位总数</div>-->
    <!--        <div class="flex-number">-->
    <!--          <div>{{ heavilyPollutionCount[0] }}</div>-->
    <!--          <div>{{ heavilyPollutionCount[1] }}</div>-->
    <!--          <div>{{ heavilyPollutionCount[2] }}</div>-->
    <!--        </div>-->
    <!--        <div class="title" style="margin-top:0.3rem;letter-spacing:7px">-->
    <!--          在建工地总数-->
    <!--        </div>-->
    <!--        <div class="flex-number">-->
    <!--          <div>{{ siteCount[0] }}</div>-->
    <!--          <div>{{ siteCount[1] }}</div>-->
    <!--          <div>{{ siteCount[2] }}</div>-->
    <!--        </div>-->
    <!--      </div>-->
    <!--    </section>-->
    <!--    <section class="panel-top">-->
    <!--      <div class="title" style="font-size: 0.2rem;">污染源排放监测</div>-->
    <!--      <div>-->
    <!--        <img class="icon" src="@/assets/<EMAIL>" alt="" />-->
    <!--        <img src="@/assets/<EMAIL>" alt="" />-->
    <!--        <div>-->
    <!--          <div>废气总排放总量</div>-->
    <!--          <div>-->
    <!--            <span class="count">-->
    <!--              <countTo-->
    <!--                :startVal="0"-->
    <!--                :endVal="hpeSum.gasDischargeNum"-->
    <!--                :duration="3000"-->
    <!--              ></countTo>-->
    <!--            </span>-->
    <!--            m³-->
    <!--          </div>-->
    <!--        </div>-->
    <!--      </div>-->
    <!--      <div>-->
    <!--        <img class="icon" src="@/assets/<EMAIL>" alt="" />-->
    <!--        <img src="@/assets/<EMAIL>" alt="" />-->
    <!--        <div>-->
    <!--          <div>废水总排放总量</div>-->
    <!--          <div>-->
    <!--            <span class="count">-->
    <!--              <countTo-->
    <!--                :startVal="0"-->
    <!--                :endVal="hpeSum.wasteWaterNum"-->
    <!--                :duration="3000"-->
    <!--              ></countTo>-->
    <!--            </span>-->
    <!--            千克-->
    <!--          </div>-->
    <!--        </div>-->
    <!--      </div>-->
    <!--      <div>-->
    <!--        <img class="icon" src="@/assets/<EMAIL>" alt="" />-->
    <!--        <img src="@/assets/<EMAIL>" alt="" />-->
    <!--        <div>-->
    <!--          <div>废料总排放总量</div>-->
    <!--          <div>-->
    <!--            <span class="count">-->
    <!--              <countTo-->
    <!--                :startVal="0"-->
    <!--                :endVal="hpeSum.wasteNum"-->
    <!--                :duration="3000"-->
    <!--              ></countTo>-->
    <!--            </span>-->
    <!--            吨-->
    <!--          </div>-->
    <!--        </div>-->
    <!--      </div>-->
    <!--    </section>-->
    <!-- <section class="panel-center">
      <div class="chart-title">废气污染物监测</div>
      <component
        :is="'LineChartCylinder'"
        :height="'2.5rem'"
        :id="'LineChartCylinder'"
        :width="'4rem'"
      ></component>
    </section>
    <section class="panel-bottom">
      <div class="chart-title">废水污染物监测</div>
      <component
        :is="'LineChartDouble'"
        :height="'2.5rem'"
        :id="'LineChartDouble'"
        width="'4rem'"
      ></component>
    </section> -->
  </section>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import SitePie from "@/components/Charts/SitePie.vue";
import MapHomeAqi from "@/components/Charts/MapHomeAqi.vue";
import AirDoublePie from "@/components/Charts/AirDoublePie.vue";
import RotateBarSolid from "@/components/Charts/RotateBarSolid.vue";
import LineChartCylinder from "@/components/Charts/LineChartCylinder.vue";
import LineChartDouble from "@/components/Charts/LineChartDouble.vue";
import countTo from "vue-count-to";
import { getHeavilyPollutingEnterpriseList } from "@/api/water";
import { getCompanyList, getHeavyTotal, getHeavilyData, getCurrYearTotal, getCurrMonthTotal } from "@/api/headvily-pollution";
import { getHpeSum } from "@/api/homeMap";
import alarmTrend from "@/components/Charts/alarmTrend.vue"
import alarmStatistics from "@/components/Charts/alarmStatistics.vue"
interface EchartData {
  bottomList: string[];
  dataList: string | number[];
}
@Component({
  name: "PollutionPage",
  components: {
    SitePie,
    MapHomeAqi,
    AirDoublePie,
    RotateBarSolid,
    LineChartCylinder,
    LineChartDouble,
    countTo,
    alarmTrend,
    alarmStatistics
  }
})
export default class extends Vue {
  private heavyPollutionEnterpriseList: any[] = [];
  private alarmData: EchartData = {
    bottomList: [],
    dataList: []
  };
  private icon1 = require('../../assets/<EMAIL>')
  private icon2 = require('../../assets/<EMAIL>')
  private icon3 = require('../../assets/<EMAIL>')
  private icon4 = require('../../assets/<EMAIL>')
  private alarmStatisticsData: EchartData = {
    bottomList: [],
    dataList: []
  };
  private amountData = [
    {
      type: 1,
      number: 36
    },
    {
      type: 2,
      number: 23
    },
    {
      type: 3,
      number: 17
    },
    {
      type: 4,
      number: 14
    }
  ]
  mounted() {
    // this.fetchHeavilyPollutingEnterpriseList();
    // this.fetchcompanyList();
    this.getCurrYearTotal()
    this.getHpeSum();
    this.getHeavyTotal()
    this.getHeavilyData()
    this.getCurrMonthTotal()
  }
  // 获取当前每月统计
  getCurrMonthTotal() {
    getCurrMonthTotal().then(res => {
      const data = res.data.data || []
      this.alarmStatisticsData = {
        bottomList: data.map((item:any) => item.name),
        dataList: data.map((item:any) => item.value)
      }
    })
  }
  // 获取当前年每月统计
  getCurrYearTotal() {
    getCurrYearTotal().then(res => {
      const data = res.data.data || []
      this.alarmData = {
        bottomList: data.map((item:any) => item.name).reverse(),
        dataList: data.map((item:any) => item.value).reverse()
      }
    })
  }
  // 获取重污站点统计
  private getHeavyTotal() {
    getHeavyTotal().then(res => {
      const data = res.data.data
      this.amountData[0].number = data.constructionSiteCount
      this.amountData[1].number = data.garageAndPrintCount
      this.amountData[2].number = data.restaurantCount
      this.amountData[3].number = data.gasCount
    })
  }
  // 获取重污统计
  getHeavilyData() {
    getHeavilyData().then(res => {
      const data = res.data.data
      data.constructionSiteList = data.constructionSiteList.map((item:any) => {
        item.gcLng = item.lng
        item.gcLat = item.lat
        item.online = 1
        item.pollutantState = item.isAlarm
        return item
      })
      data.WaterSource = data.WaterSource.map((item:any) => {
        item.value = item.stationId
        return item
      })
      /* eslint-disable @typescript-eslint/ban-ts-ignore */
      //@ts-ignore
      this.$bus.emit("sendCompanyList", data.constructionSiteList);
      /* eslint-disable @typescript-eslint/ban-ts-ignore */
      //@ts-ignore
      this.$bus.emit("sendPollutingMapMarker", data.WaterSource);
      /* eslint-disable @typescript-eslint/ban-ts-ignore */
      //@ts-ignore
      this.$bus.emit('sendHeavilyDtaa', data)
    })
  }
  private heavilyPollutionCount: number | string = 0; //在建工地数量
  // 获取重污染企业信息 deprecated
  private fetchHeavilyPollutingEnterpriseList(): void {
    getHeavilyPollutingEnterpriseList().then(res => {
      if (res.data.data && res.data.data.length > 0) {
        this.heavilyPollutionCount =
          res.data.data.length > 99
            ? String(res.data.data.length)
            : "0" + res.data.data.length;
        this.heavyPollutionEnterpriseList = res.data.data.map(
          (enterprise: any) => {
            return {
              name: enterprise.heavilyPollutingEnterpriseNickname,
              value: enterprise.heavilyPollutingEnterpriseId,
              lng: enterprise.lng,
              lat: enterprise.lat
            };
          }
        );
        /* eslint-disable @typescript-eslint/ban-ts-ignore */
        //@ts-ignore
        this.$bus.emit(
          "sendPollutingMapMarker",
          this.heavyPollutionEnterpriseList
        );
      }
    });
  }
  private siteCount: number | string = 0; //在建工地数量
  private companyList: any[] = [];
  // 获取重污染工地列表
  private fetchcompanyList(): void {
    getCompanyList({}).then((res: any) => {
      const data = res.data.data;
      this.siteCount =
        data.length > 99 ? String(data.length) : "0" + data.length;
      this.companyList = data;
      /* eslint-disable @typescript-eslint/ban-ts-ignore */
      //@ts-ignore
      // this.$bus.emit("sendCompanyList", this.companyList);
    });
  }
  private hpeSum: any = {};
  private getHpeSum() {
    getHpeSum().then((res: any) => {
      res.data.data.wasteWaterNum = Number(
        Number(res.data.data.wasteWaterNum).toFixed(0)
      );
      res.data.data.gasDischargeNum = Number(
        Number(res.data.data.gasDischargeNum).toFixed(0)
      );
      res.data.data.wasteNum = Number(
        Number(res.data.data.wasteNum).toFixed(0)
      );
      console.log(res.data.data);
      this.hpeSum = res.data.data;
    });
  }
}
</script>
