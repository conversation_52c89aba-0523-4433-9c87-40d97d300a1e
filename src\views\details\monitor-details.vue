<style lang="less">
.ant-modal-root {
  .ant-modal-content {
    background-color: #061e65;
    border: 1px solid #02aed2;
    box-shadow: 0 0 20px rgba(0, 217, 213, 0.3);
  }

  .ant-modal-header {
    background-color: rgba(7, 56, 141, 0.8);
    border-bottom: 1px solid #02aed2;
    padding: 12px 20px;
  }

  .ant-modal-title {
    color: #00d9d5;
    font-weight: bold;
    text-align: center;
    font-size: 0.22rem;
  }

  .ant-table-placeholder {
    background-color: transparent;
  }

  .ant-modal-close {
    color: #00d9d5;

    &:hover {
      color: #ffffff;
    }
  }

  .ant-modal-body {
    background-color: #061e65;
    color: #ffffff;
    padding: 20px;
  }

  .ant-modal-footer {
    border-top: 1px solid #02aed2;
    background-color: rgba(7, 56, 141, 0.8);
    padding: 10px 20px;

    .ant-btn {
      background-color: rgba(0, 217, 213, 0.1);
      border: 1px solid #00d9d5;
      color: #00d9d5;
      font-size: 0.16rem;

      &:hover {
        background-color: #00d9d5;
        color: #061e65;
      }
    }
  }
}
</style>
<style lang="less" scoped>
::v-deep .ant-select-selection {
  background: transparent;
  color: #02c7e6;
}

.type {
  display: flex;
  align-items: center;
  margin-left: 10px;
}

.list {
  display: flex;
  align-items: center;
  margin-left: 10px;
  font-size: 14px;
  cursor: pointer;
}

.list span:last-child {
  margin-left: 5px;
}

.monitor-detail-wrap {
  padding: 0.2rem 0.2rem 0.2rem;
  position: relative;
  .monitor-detail-main {
    height: 100%;
    background-image: url('../../assets/kqbk.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    padding: 0.6rem 1.1rem;
    /*display: flex;*/
    /*justify-content: space-around;*/
    /*flex-direction: column;*/
    .monitor-select {
      height: 0.84rem;
      background: #061e65;
      border: 1px solid #02aed2;
      display: flex;
      align-items: center;
      .monitor-title {
        color: #ffffff;
        font-size: 0.28rem;
        font-weight: bold;
        width: 4.28rem;
        text-align: center;
      }
      .monitor-select-middle {
        width: 5.06rem;
        height: 0.84rem;
        .title-select-monitor {
          width: 100%;
          height: 0.84rem;
        }
      }
      .monitor-address {
        color: #ffffff;
        font-size: 0.28rem;
        font-weight: bold;
        width: 6.6rem;
        text-align: center;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    .monitor-time {
      margin-top: 0.5rem;
      position: relative;
      align-items: center;
      .monitor-time-title {
        font-size: 0.22rem;
        height: 0.24rem;
        font-weight: bold;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-left: 0.22rem;
        line-height: 0.24rem;
        &:before {
          top: 0;
          left: 0;
          position: absolute;
          width: 0.06rem;
          height: 0.24rem;
          content: '';
          background: #ffffff;
        }
      }
      .monitor-time-area {
        height: 1.38rem;
        position: relative;
        margin-top: 0.28rem;
        .left-arrow {
          cursor: pointer;
          width: 0.4rem;
          position: absolute;
          height: 0.4rem;
          left: -0.6rem;
          top: 0.49rem;
          background: rgba(7, 56, 141, 0.6);
          border: 1px solid rgba(54, 218, 234, 0.42);
        }
        .middle-wrap {
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          .middle-carousel {
            display: flex;
            align-items: center;
            .single {
              width: 1.8rem;
              height: 1.78rem;
              margin-right: 0.26rem;
              .single-title {
                background-color: #089708;
                height: 0.3rem;
                margin-bottom: 0;
                color: white;
                font-size: 0.18rem;
                text-align: center;
                line-height: 0.3rem;
                width: 100%;
              }
              .single-content {
                padding: 0.12rem 0.22rem;
                background-color: #022360;
                color: white;
                display: flex;
                flex-direction: column;
                font-size: 0.14rem;
              }
            }
          }
          .no-data {
            color: white;
            font-size: 0.18rem;
            width: 100%;
            text-align: center;
            height: 1.38rem;
            line-height: 1.38rem;
          }
        }
        .right-arrow {
          cursor: pointer;
          width: 0.4rem;
          height: 0.4rem;
          position: absolute;
          top: 0.49rem;
          right: -0.6rem;
          background: rgba(7, 56, 141, 0.6);
          border: 1px solid rgba(54, 218, 234, 0.42);
        }
      }
    }
    .history-area {
      margin-top: 0.58rem;
      .history-area-top {
        display: flex;
        position: relative;
        align-items: center;
        .history-area-title {
          font-size: 0.22rem;
          height: 0.24rem;
          font-weight: bold;
          padding-left: 0.22rem;
          line-height: 0.24rem;
          &:before {
            /*top: 0.07rem;*/
            left: 0;
            position: absolute;
            width: 0.06rem;
            height: 0.24rem;
            content: '';
            background: #ffffff;
          }
        }
        .history-area-select {
          margin-left: 0.84rem;
          display: flex;
          align-items: center;
          .division {
            display: inline-block;
            width: 0.22rem;
            margin: 0 0.31rem;
            height: 0.02rem;
            background: #19f1f9;
          }
        }
      }
      .monitor-container {
        display: flex;
        margin-bottom: 0.2rem;

        .monitor-section {
          flex: 1;
          margin-right: 0.2rem;
          background: transparent;
        }

        .monitor-section:last-child {
          margin-right: 0;
        }

        .chart-section {
          flex: 2; /* 图表占据更多空间 */
        }

        .camera-section {
          flex: 1; /* 监控占据相对较少空间 */
        }
      }
      .history-echarts {
        width: 100%;
        height: 3.3rem;
        margin-top: 0.25rem;
      }
    }
  }
}

/* 监控列表样式 */
.single-camera-container {
  width: 100%;
  margin: 0.19rem auto;
  border: 1px solid #02aed2;
}

.single-camera {
  width: 100%;
  background: transparent;
}

.card-header {
  position: relative;
  display: flex;
  justify-content: space-between;
  .name {
    font-size: 0.18rem;
    font-weight: bold;
    color: #ffffff;
  }
}

/* 监控标题栏样式 */
.camera-title-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 0.48rem;
  margin-bottom: 0.2rem;
  padding: 0 0.22rem;
  position: relative;
}

.camera-title {
  font-size: 0.22rem;
  font-weight: bold;
  height: 0.24rem;
  line-height: 0.24rem;
  padding-left: 0.22rem;
  position: relative;
}

.camera-title:before {
  left: 0;
  position: absolute;
  width: 0.06rem;
  height: 0.24rem;
  content: '';
  background: #ffffff;
}

.camera-select-wrapper {
  flex: 1;
  display: flex;
  justify-content: flex-end;
}

/* 监控下拉框样式 */
.camera-select-wrapper .ant-select {
  width: 2.2rem !important;
}

.camera-select-wrapper .ant-select-selection {
  background-color: transparent;
  border: 1px solid #00d9d5;
  height: 0.36rem;
  line-height: 0.36rem;
}

.camera-select-wrapper .ant-select-selection__rendered {
  line-height: 0.34rem;
}

.camera-select-wrapper .ant-select-selection-selected-value {
  color: #3adbff;
  font-size: 0.16rem;
}

.camera-select-wrapper .ant-select-arrow {
  color: #00d9d5;
}

.camera-select-wrapper .ant-select-dropdown {
  background-color: #061e65;
  border: 1px solid #00d9d5;
}

.camera-select-wrapper .ant-select-dropdown-menu-item {
  color: #ffffff;
  font-size: 0.16rem;
}

.camera-select-wrapper .ant-select-dropdown-menu-item:hover,
.camera-select-wrapper .ant-select-dropdown-menu-item-active {
  background-color: rgba(0, 217, 213, 0.2);
}

.video_btn {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 5;
  width: 1rem;
  height: 0.3rem;
  font-size: 0.14rem;
  line-height: 0.3rem;
  color: #ffffff;
  text-align: center;
  cursor: pointer;
  background-color: rgba(255, 255, 255, 0.1);
  border-bottom-left-radius: 0.05rem;
}

.video_btn:hover {
  background-color: rgba(0, 122, 204, 0.4);
}

.false-camera {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 270px;
  background-repeat: no-repeat;
  background-size: cover;
}

.false-camera .play {
  display: flex;
  align-content: center;
  justify-content: center;
  width: 0.8rem;
  height: 0.4rem;
  color: #777777;
  cursor: pointer;
  background: rgba(0, 0, 0, 0.5);
  border: 0.02rem solid #777777;
  border-radius: 0.05rem;
}

.false-camera .play .anticon-caret-right {
  position: relative;
  top: 0.03rem;
  font-size: 0.3rem;
  color: #ffffff;
}

.false-camera .play:hover {
  border-color: #00d9d5;
}

.no-data {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 1.5rem;
  color: #ffffff;
  border-radius: 0.05rem;
}

.camera-section .no-data {
  height: 1.8rem;
  flex-direction: column;
}

.camera-section .no-data img {
  width: 0.5rem;
  height: 0.5rem;
  margin-bottom: 0.1rem;
}

.camera-select {
  width: 100%;
  margin-top: 0.2rem;
}

/* 录像回放弹窗样式 */
.video-tape-modal {
  .tape-list {
    margin-top: 0.2rem;
  }
  .ant-range-picker {
    width: 100%;
    height: 0.38rem;
    background-color: transparent;
    border: 1px solid #00d9d5;
    color: #3adbff;
  }
  .ant-input {
    color: #3adbff !important;
    background-color: transparent;
  }
  .ant-calendar-picker-icon,
  .ant-calendar-range-picker-separator {
    color: #00d9d5;
  }

  .date-picker-container {
    display: flex;
    align-items: center;
    margin-bottom: 0.2rem;
  }

  .picker-label {
    color: #ffffff;
    font-size: 0.16rem;
    margin-right: 0.2rem;
    white-space: nowrap;
  }
}

/* 表格样式调整 */
.ant-modal .ant-table {
  background-color: transparent;
  color: #ffffff;

  &::v-deep .ant-table-thead > tr > th {
    background-color: rgba(7, 56, 141, 0.8);
    color: #00d9d5;
    border-bottom: 1px solid #02aed2;
  }

  &::v-deep .ant-table-tbody > tr > td {
    border-bottom: 1px solid rgba(2, 174, 210, 0.3);
    background-color: rgba(7, 56, 141, 0.4);
    color: #ffffff;
  }

  &::v-deep .ant-table-tbody > tr:hover > td {
    background-color: rgba(0, 217, 213, 0.1);
  }

  &::v-deep .ant-table-placeholder {
    background-color: rgba(7, 56, 141, 0.4);
    color: #ffffff;
    border-bottom: 1px solid #02aed2;
  }
}

/* 分页组件 */
.ant-modal .ant-pagination {
  &::v-deep .ant-pagination-item {
    background-color: rgba(7, 56, 141, 0.6);
    border: 1px solid #02aed2;

    a {
      color: #ffffff;
    }

    &-active {
      background-color: rgba(0, 217, 213, 0.2);
      border-color: #00d9d5;

      a {
        color: #00d9d5;
      }
    }
  }

  &::v-deep .ant-pagination-prev,
  &::v-deep .ant-pagination-next {
    .ant-pagination-item-link {
      background-color: rgba(7, 56, 141, 0.6);
      border: 1px solid #02aed2;
      color: #ffffff;
    }
  }

  &::v-deep .ant-pagination-disabled .ant-pagination-item-link {
    color: rgba(255, 255, 255, 0.3);
    background-color: rgba(7, 56, 141, 0.3);
    border-color: rgba(2, 174, 210, 0.3);
  }
}

/* 监控状态标志样式 */
.camera-status {
  position: absolute;
  top: 10px;
  left: 10px;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 0.14rem;
  z-index: 5;
  background-color: rgba(0, 0, 0, 0.5);
}

.camera-status.online {
  color: #29a95f;
}

.camera-status.offline {
  color: #f26e70;
}

/* 日期选择器深度样式 */
::v-deep .ant-calendar-picker-input,
::v-deep .ant-calendar-range-picker-input {
  color: #3adbff !important;
  background-color: transparent !important;
}

::v-deep .ant-calendar-range-picker-separator,
::v-deep .ant-calendar-picker-icon {
  color: #00d9d5 !important;
}

::v-deep .ant-calendar,
::v-deep .ant-calendar-time-picker-inner {
  background-color: #061e65;
  border-color: #02aed2;
  color: #ffffff;
}

::v-deep .ant-calendar-date {
  color: #ffffff;
}

::v-deep .ant-calendar-date:hover {
  background-color: #00d9d5;
  color: #061e65;
}

/* 自定义范围选择器 */
.custom-range-picker {
  width: 100%;
  border: 1px solid #00d9d5;
  border-radius: 4px;

  &::v-deep .ant-input,
  &::v-deep .ant-calendar-picker-input,
  &::v-deep .ant-calendar-range-picker-input {
    background-color: rgba(0, 217, 213, 0.1);
    color: #3adbff;
    font-weight: bold;
  }

  &::v-deep .ant-calendar-range-picker-separator,
  &::v-deep .ant-calendar-picker-icon {
    color: #00d9d5;
  }
}

/* 特定Modal样式 */
.dark-theme-modal {
  .video-tape-modal-wrapper,
  .video-player-modal-wrapper {
    &::v-deep .ant-modal-content {
      background-color: rgba(6, 30, 101, 0.95);
      box-shadow: 0 0 25px rgba(0, 217, 213, 0.4);
    }

    &::v-deep .ant-modal-header {
      position: relative;
      overflow: hidden;

      &:before {
        content: '';
        position: absolute;
        left: 0;
        bottom: 0;
        width: 100%;
        height: 2px;
        background: linear-gradient(90deg, transparent, #00d9d5, transparent);
      }
    }

    &::v-deep .ant-modal-title {
      text-align: center;
      color: #00d9d5;
      font-weight: bold;
      font-size: 0.24rem;
      text-shadow: 0 0 10px rgba(0, 217, 213, 0.5);
    }

    &::v-deep .ant-modal-close {
      color: #00d9d5;
      background-color: rgba(0, 217, 213, 0.1);
      border-radius: 50%;
      width: 32px;
      height: 32px;
      margin: 10px 10px 0 0;

      .ant-modal-close-x {
        width: 32px;
        height: 32px;
        line-height: 32px;
      }

      &:hover {
        background-color: rgba(0, 217, 213, 0.3);
        color: #ffffff;
      }
    }
  }
}

/* 视频播放器Modal特定样式 */
.video-player-modal-wrapper {
  &::v-deep .ant-modal-body {
    padding: 12px;
    background-color: rgba(6, 30, 101, 0.9);
  }

  &::v-deep .ant-modal-footer {
    padding: 10px;
    text-align: center;

    .ant-btn {
      min-width: 100px;
    }
  }
}

/* 录像列表表格样式 */
.video-tape-modal-wrapper {
  .tape-list {
    &::v-deep .ant-table {
      background-color: transparent;

      .ant-table-thead > tr > th {
        background-color: rgba(7, 56, 141, 0.9);
        color: #00d9d5;
        text-align: center;
        font-weight: bold;
      }

      .ant-table-tbody > tr > td {
        background-color: rgba(7, 56, 141, 0.6);
        color: #ffffff;
        border-color: rgba(2, 174, 210, 0.2);
      }

      .ant-table-tbody > tr:hover > td {
        background-color: rgba(0, 217, 213, 0.1);
      }

      .ant-btn-primary {
        background-color: rgba(0, 217, 213, 0.2);
        border-color: #00d9d5;
        color: #00d9d5;

        &:hover {
          background-color: #00d9d5;
          color: #061e65;
        }
      }
    }

    &::v-deep .ant-pagination {
      text-align: right;
      margin-top: 0.2rem;

      .ant-pagination-item {
        background-color: rgba(7, 56, 141, 0.6);
        border-color: rgba(2, 174, 210, 0.4);

        a {
          color: #ffffff;
        }

        &-active {
          background-color: rgba(0, 217, 213, 0.2);
          border-color: #00d9d5;

          a {
            color: #00d9d5;
          }
        }
      }

      .ant-pagination-prev,
      .ant-pagination-next {
        .ant-pagination-item-link {
          background-color: rgba(7, 56, 141, 0.6);
          border-color: rgba(2, 174, 210, 0.4);
          color: #ffffff;
        }
      }
    }
  }
}
</style>
<style lang="less">
.title-select-monitor {
  align-items: flex-end;
  .ant-select-selection {
    width: 5.06rem;
    height: 0.84rem;
    font-size: 0.28rem;
    // background: rgba(14, 139, 255, 0.32);
    border: none;
    border-radius: unset;
    // clip-path: polygon(100% 0, 100% 100%, 12% 100%, 0% 70%, 0 0);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-color: transparent;
  }
  .ant-select-selection__placeholder,
  .ant-select-search__field__placeholder {
    height: 0.84rem;
    text-align: center;
    line-height: 0.84rem;
  }
  .ant-select-selection-selected-value {
    color: #00eaff;
  }
  .ant-select-focused .ant-select-selection,
  .ant-select-selection:focus,
  .ant-select-selection:active {
    border-color: transparent !important;
    border-right-width: 0 !important;
    outline: 0 !important;
    box-shadow: none !important;
    border: none !important;
  }
  .ant-select-selection__rendered {
    display: flex;
    height: 0.84rem;
    text-align: center;
    line-height: 0.84rem;
    .ant-select-selection-selected-value {
      width: 100%;
    }
    // justify-content: center;
  }
}
.select-options {
  font-size: 0.28rem !important;
  height: 0.58rem !important;
  line-height: 0.58rem !important;
}
.svg-arrow {
  width: 0.4rem;
  height: 0.4rem;
}
.monitor-date-picker {
  width: 2.32rem;
  height: 0.38rem;
  background-size: 100% 100%;
  background-image: url(../../assets/<EMAIL>);
  background-repeat: no-repeat;
  .ant-input {
    background-color: transparent;
    border: none;
    outline: none;
    height: 0.38rem;
    line-height: 0.38rem;
    color: #3adbff;
    text-align: center;
    font-size: 0.19rem;
  }
}

/* 专门为日期选择器添加样式 */
.video-tape-modal {
  .ant-calendar-picker-input {
    color: #3adbff !important;
    background-color: transparent;
    border-color: #00d9d5;
  }

  .ant-calendar-range-picker-input {
    color: #3adbff !important;
    background-color: transparent;
  }

  .ant-calendar-range-picker-separator {
    color: #3adbff;
  }

  .ant-calendar-picker-icon {
    color: #00d9d5;
  }
}
</style>
<template>
  <div>
    <div class="monitor-detail-wrap">
      <div class="monitor-detail-main">
        <!--      // 头部-->
        <section class="monitor-select">
          <div class="monitor-title">{{ industryName }}</div>
          <img src="../../assets/<EMAIL>" alt="" />
          <div class="monitor-select-middle">
            <a-select v-model="defaultValue" @change="changeCompany" class="title-select-monitor">
              <a-icon slot="suffixIcon" type="caret-down" style="color: rgba(0, 234, 255, 1); width: 0.23rem; height: 0.11rem" />
              <a-select-option :value="getCompanyId(item)" v-for="(item, index) in printFactoryList" :key="index" class="select-options">
                {{ getCompanyName(item) }}
              </a-select-option>
            </a-select>
          </div>
          <img src="../../assets/<EMAIL>" alt="" />
          <div class="monitor-address">位置：{{ currAddress }}</div>
        </section>
        <!--      // 时间-->
        <section class="monitor-time">
          <div class="monitor-time-title">
            <span>24小时监测</span><span>{{ unitDescription }}</span>
          </div>
          <div class="monitor-time-area">
            <!-- 左侧箭头 -->
            <div class="left-arrow">
              <svgicon name="left" color="#00D9D5" class="svg-arrow"></svgicon>
            </div>
            <!-- 中间部分 -->
            <div class="middle-wrap">
              <swiper class="middle-carousel" :options="swiperOption" v-if="printRecordHourList.length != 0 && printRecordHourList.length > 8">
                <swiper-slide v-for="(item, i) in printRecordHourList" :key="i">
                  <div class="single">
                    <p v-if="type !== 3" class="single-title" :style="{ backgroundColor: item.morStandard1 || item.morStandard2 ? '#C71F09' : '#089708' }">
                      {{ type === 4 ? item.hours : item.time }}
                    </p>
                    <p v-if="type === 3" class="single-title" :style="{ backgroundColor: item.morStandard1 ? '#C71F09' : '#089708' }">{{ item.time }}</p>
                    <p class="single-content">
                      <span v-if="type !== 3"
                        >{{ type === 4 ? '油气浓度' : 'TVOC' }}：{{ type === 4 ? item.oilConcentration : item.vocAvgConcentration || 0 }}</span
                      >
                      <span v-if="type !== 3"
                        >{{ type === 4 ? '液压' : '排放速率' }}：{{ type === 4 ? item.hydraulicPressure : item.emissionAvgRate || 0 }}</span
                      >
                      <span v-if="type === 3">烟雾浓度：{{ item.concentration }}</span>
                      <span>湿度：{{ item.humidity || '0' }}%</span>
                      <span>大气压：{{ item.pressure || '0' }}pa</span>
                      <span>温度：{{ item.temperature || '0' }}℃</span>
                      <span>风速：{{ item.windPower || '0' }}m/s</span>
                    </p>
                  </div>
                </swiper-slide>
              </swiper>
              <div v-if="printRecordHourList.length != 0 && (printRecordHourList.length < 8 || printRecordHourList.length === 8)" class="middle-carousel">
                <div v-for="(item, i) in printRecordHourList" :key="i" class="single">
                  <p v-if="type !== 3" class="single-title" :style="{ backgroundColor: item.morStandard1 || item.morStandard2 ? '#C71F09' : '#089708' }">
                    {{ type === 4 ? item.hours : item.time }}
                  </p>
                  <p v-if="type === 3" class="single-title" :style="{ backgroundColor: item.morStandard1 ? '#C71F09' : '#089708' }">{{ item.time }}</p>
                  <p class="single-content">
                    <span v-if="type !== 3"
                      >{{ type === 4 ? '油气浓度' : 'TVOC' }}：{{ type === 4 ? item.oilConcentration : item.vocAvgConcentration || 0 }}</span
                    >
                    <span v-if="type !== 3">{{ type === 4 ? '液压' : '排放速率' }}：{{ type === 4 ? item.hydraulicPressure : item.emissionAvgRate || 0 }}</span>
                    <span v-if="type === 3">烟雾浓度：{{ item.concentration }}</span>
                    <span>湿度：{{ item.humidity || '0' }}%</span>
                    <span>大气压：{{ item.pressure || '0' }}pa</span>
                    <span>温度：{{ item.temperature || '0' }}℃</span>
                    <span>风速：{{ item.windPower || '0' }}m/s</span>
                  </p>
                </div>
              </div>
              <div v-if="!printRecordHourList.length" class="no-data">数据未更新</div>
            </div>
            <!-- 右侧箭头 -->
            <div class="right-arrow">
              <svgicon name="right" color="#00D9D5" class="svg-arrow"></svgicon>
            </div>
          </div>
        </section>
        <!--      历史曲线-->
        <section class="history-area">
          <div class="history-area-top">
            <div class="history-area-title">数据监测</div>
            <div class="type" style="margin-left: 20px">
              <div class="list" @click="changeType(4)">
                <img :src="dateType == 4 ? image2 : image1" alt="实时" />
                <span>实时</span>
              </div>

              <div class="list" @click="changeType(1)">
                <img :src="dateType == 1 ? image2 : image1" alt="未完成" />
                <span>分钟</span>
              </div>
              <div class="list" @click="changeType(2)">
                <img :src="dateType == 2 ? image2 : image1" alt="完成" />
                <span>小时</span>
              </div>
              <div class="list" @click="changeType(3)">
                <img :src="dateType == 3 ? image2 : image1" alt="完成" />
                <span>日</span>
              </div>
            </div>
          </div>
          <!-- 修改历史曲线部分，添加数据监测图表和监控列表 -->
          <div class="monitor-container">
            <div class="monitor-section chart-section">
              <div class="history-echarts">
                <ChartDual :prop-data="chartData" />
              </div>
            </div>

            <div class="monitor-section camera-section" style="margin-top: -37px">
              <!-- 添加监控标题和选择器 -->
              <div class="camera-title-bar">
                <div class="camera-title">监控列表</div>
                <div class="camera-select-wrapper">
                  <a-select
                    v-if="cameraList.length > 0"
                    v-model="selectedCamera"
                    placeholder="选择监控"
                    @change="handleCameraChange"
                    style="background: transparent"
                  >
                    <a-select-option v-for="(item, index) in cameraList" :key="index" :value="index">{{ item.cameraName }}</a-select-option>
                  </a-select>
                </div>
              </div>

              <div v-loading="cameraLoading" class="single-camera-container" style="margin-top: 57px">
                <div v-if="cameraList.length && selectedCamera !== null" class="single-camera" :bordered="false">
                  <div
                    v-if="!cameraList[selectedCamera].videoUrl"
                    class="false-camera"
                    :style="{
                      backgroundImage: 'url(' + cameraList[selectedCamera].snapUrl + ')',
                    }"
                  >
                    <div :class="['camera-status', cameraList[selectedCamera].online ? 'online' : 'offline']">
                      {{ cameraList[selectedCamera].online ? '在线' : '离线' }}
                    </div>
                    <div v-if="cameraList[selectedCamera].type === 2 || true" class="video_btn" @click="showVideoTapeList(cameraList[selectedCamera])">
                      回放录像
                    </div>

                    <div class="play" @click="play(selectedCamera)">
                      <a-icon v-if="!cameraList[selectedCamera].loading" type="caret-right"></a-icon>
                      <span v-else style="font-size: 14px; color: #ffffff">加载中...</span>
                    </div>
                  </div>
                  <div v-else style="position: relative; width: 100%; height: 270px">
                    <div :class="['camera-status', cameraList[selectedCamera].online ? 'online' : 'offline']">
                      {{ cameraList[selectedCamera].online ? '在线' : '离线' }}
                    </div>
                    <div style="position: relative; width: 100%; height: 100%">
                      <LivePlayer :video-url="cameraList[selectedCamera].videoUrl" aspect="fullscreen" fluent autoplay live stretch />
                    </div>
                  </div>
                </div>
                <div v-else class="no-data" style="height: 270px">
                  <div>
                    <p style="font-size: 0.18rem; color: #3adbff">暂无相关监控视频</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>

    <!-- 回放弹出框 -->
    <a-modal
      v-model="showVideoTapeWindow"
      title="查看录像"
      width="700px"
      :footer="null"
      @cancel="showVideoTapeWindow = false"
      class="video-tape-modal-wrapper"
      wrapClassName="dark-theme-modal"
    >
      <div class="video-tape-modal">
        <a-row>
          <a-col :span="24">
            <div class="date-picker-container">
              <div class="picker-label">选择时间范围：</div>
              <a-range-picker
                v-model="dateRange"
                :allowClear="false"
                showTime
                format="YYYY-MM-DD HH:mm:ss"
                @change="reloadTapeList(1)"
                :placeholder="['开始时间', '结束时间']"
                class="custom-range-picker"
              />
            </div>
          </a-col>
        </a-row>
        <div v-loading="loadingTapeList" class="tape-list">
          <a-table :dataSource="tapeList" :rowKey="(record, index) => index" :pagination="false" bordered>
            <a-table-column key="channelSerial" dataIndex="channelSerial" title="通道号" align="center" :width="155" />
            <a-table-column key="channelName" dataIndex="channelName" title="通道名" align="center" />
            <a-table-column key="startTime" title="开始时间" align="center" :width="135">
              <template slot-scope="data">
                {{ data.startTime ? formatTableDate(data.startTime) : '-' }}
              </template>
            </a-table-column>
            <a-table-column key="endTime" title="结束时间" align="center" :width="135">
              <template slot-scope="data">
                {{ data.endTime ? formatTableDate(data.endTime) : '-' }}
              </template>
            </a-table-column>
            <a-table-column key="action" title="操作" align="center">
              <template slot-scope="data">
                <a-button type="primary" size="small" @click="startPlayVideoTape(data)">播放</a-button>
              </template>
            </a-table-column>
          </a-table>
          <a-pagination
            :total="tapePageTotal"
            :pageSize="tapePageSize"
            :current="tapePageNum"
            @change="reloadTapeList"
            size="small"
            style="margin-top: 10px; text-align: right"
          />
        </div>
      </div>
    </a-modal>

    <!-- 播放录像弹窗 -->
    <a-modal
      v-model="playVideoTape"
      :title="tapeName"
      width="700px"
      :destroyOnClose="true"
      :maskClosable="false"
      @cancel="stopPlayTape"
      @close="closeAndStop"
      class="video-player-modal-wrapper"
      wrapClassName="dark-theme-modal"
    >
      <div v-loading="replayLoading" style="height: 100%">
        <Player
          v-if="videoUrl !== ''"
          :video-url="videoUrl"
          :has-audio="false"
          height="400px"
          :isFullResize="false"
          :isResize="false"
          :autoplay="true"
          :index="0"
          muted
        />
        <div v-else class="no-data" style="height: 400px; display: flex; align-items: center; justify-content: center">
          <span style="color: #3adbff; font-size: 0.18rem">加载中...</span>
        </div>
      </div>
      <template slot="footer">
        <a-button @click="stopPlayTape">关闭</a-button>
      </template>
    </a-modal>
  </div>
</template>

<script lang="ts">
import MultiLineChart from '@/components/Charts/MultiNewLineChart.vue'
import ChartDual from '@/components/Charts/ChartDual.vue'
import { Component, Vue, Watch } from 'vue-property-decorator'
import { Select, Icon, DatePicker, Card, Table, Button, Row, Col, Modal, Pagination } from 'ant-design-vue'
import { Swiper, SwiperSlide } from 'vue-awesome-swiper'
import { getToken } from '@/utils/authority'
import { getNowTime } from '@/utils/index'
import { getGarageCamera, getCarLive, playbackListGarage, playbackStartGarage, getGarageList } from '@/api/monitor'
import LivePlayer from '@liveqing/liveplayer'
import moment from 'moment'
import 'moment/locale/zh-cn'
import Player from '@/components/jessibucaPlayer/jessibuca.vue'

// 设置moment为中文
moment.locale('zh-cn')

interface AirDataProp {
  TVOC: any[]
  speed: any[]
  date: any[]
  unit: string
  type: number
}

interface ChartDataProp {
  data1: any[]
  data2: any[]
  xData: string[]
  name1: string
  name2: string
  unit1: string
  unit2: string
  max?: number
}

@Component({
  name: 'monitorDetails',
  components: {
    AIcon: Icon,
    ASelect: Select,
    ASelectOption: Select.Option,
    ACard: Card,
    Swiper,
    SwiperSlide,
    ADatePicker: DatePicker,
    ARangePicker: DatePicker.RangePicker,
    ATable: Table,
    ATableColumn: Table.Column,
    AButton: Button,
    ARow: Row,
    ACol: Col,
    APagination: Pagination,
    AModal: Modal,
    MultiLineChart,
    ChartDual,
    LivePlayer,
    Player,
  },
})
export default class extends Vue {
  private image1 = require('@/assets/images/<EMAIL>')
  private image2 = require('@/assets/images/<EMAIL>')

  private defaultValue = ''
  private type = 1
  private dateType = 1
  private currAddress = ''
  private socket: any
  private startTime = ''
  private airDataProp: AirDataProp = {
    date: [],
    TVOC: [],
    speed: [],
    unit: '单位：TVOC (mg/m³) 排放速率 (kg/h)',
    type: 1,
  }
  private printRecordHourList: any = []
  private printFactoryList: any[] = []
  private date = ''
  private chartData: ChartDataProp = {
    data1: [],
    data2: [],
    xData: [],
    name1: 'VOC',
    name2: '电流',
    unit1: '单位：mg/m³',
    unit2: '单位：A',
  }
  private cameraLoading = false
  private cameraList: any[] = []
  private selectedCamera: number | null = null
  private showVideoTapeWindow = false
  private tapeList: any[] = []
  private loadingTapeList = false
  private tapePageTotal = 0
  private tapePageSize = 10
  private tapePageNum = 1
  private playVideoTape = false
  private videoUrl = ''
  private tapeName = ''
  private replayLoading = false
  private streamId: any = undefined
  private channelId = ''
  private dateRange: any[] = [moment().subtract(1, 'days'), moment()]
  private swiperOption: any = {
    slidesPerView: 8,
    spaceBetween: 15,
    slidesPerGroup: 1,
    loop: false,
    loopFillGroupWithBlank: true,
    pagination: {
      el: '.swiper-pagination',
      clickable: true,
    },
    autoplay: {
      delay: 30000,
      disableOnInteraction: false,
    },
    navigation: {
      nextEl: '.right-arrow',
      prevEl: '.left-arrow',
    },
  }

  // 计算属性：当前行业名称
  get industryName(): string {
    const typeMap = {
      1: '印刷行业',
      2: '汽修行业',
      3: '餐饮油烟行业',
      4: '加油站行业',
    }
    return typeMap[this.type] || ''
  }

  // 计算属性：图表配置
  get chartConfig(): { name1: string; name2: string; unit1: string; unit2: string } {
    switch (this.type) {
      case 3:
        return {
          name1: '烟雾浓度',
          name2: '',
          unit1: '单位：mg/m³',
          unit2: '',
        }
      case 4:
        return {
          name1: '油气浓度',
          name2: '液阻压力',
          unit1: '单位：%/ppm',
          unit2: '单位：pa',
        }
      default:
        return {
          name1: 'VOC',
          name2: '电流',
          unit1: '单位：mg/m³',
          unit2: '单位：A',
        }
    }
  }

  // 计算属性：数据单位描述
  get unitDescription(): string {
    if (this.type === 3) {
      return '单位：烟雾浓度(mg/m³) 湿度(%) 大气压(pa) 温度(℃) 风速(m/s)'
    } else if (this.type === 4) {
      return '单位：卸油区油气浓度(%/ppm) 液阻压力(pa) 湿度(%) 大气压(pa) 温度(℃) 风速(m/s)'
    } else {
      return '单位：TVOC (mg/m³) 排放速率 (kg/h) 湿度(%) 大气压(pa) 温度(℃) 风速(m/s)'
    }
  }

  // 获取公司ID
  private getCompanyId(item: any): string {
    switch (this.type) {
      case 1:
        return item.printFactoryId || ''
      case 2:
        return item.garageId || ''
      case 3:
        return item.restaurantName || ''
      case 4:
        return item.gasId || ''
      default:
        return ''
    }
  }

  // 获取公司名称
  private getCompanyName(item: any): string {
    switch (this.type) {
      case 1:
        return item.printFactoryName || ''
      case 2:
        return item.garageName || ''
      case 3:
        return item.restaurantName || ''
      case 4:
        return item.gasName || ''
      default:
        return ''
    }
  }

  // 获取阈值
  private getThresholdValue(): number {
    if (!this.defaultValue || !this.printFactoryList.length) return 0

    const current = this.printFactoryList.find((item) => this.getCompanyId(item) === this.defaultValue)
    if (!current) return 0

    switch (this.type) {
      case 1:
      case 2:
        return current.vocConcentrationThreshold || 0
      case 3:
        return current.concentrationThreshold || 0
      case 4:
        return current.vocConcentrationThreshold || 0
      default:
        return 0
    }
  }

  mounted() {
    this.type = Number(this.$route.params.type)
    const data: any = this.$route.params.socketData
    console.log(data, 'data4')
    if (data.printFactoryList && data.printFactoryList.length && this.type === 1) {
      this.printFactoryList = data.printFactoryList || []
      this.defaultValue = data.printFactoryList[0].printFactoryId || ''
      this.currAddress = data.printFactoryList[0].printFactoryAddress || ''
    } else if (data.garageList && data.garageList.length && this.type === 2) {
      this.printFactoryList = data.garageList || []
      this.defaultValue = data.garageList[0].garageId || ''
      this.currAddress = data.garageList[0].garageAddress || ''
    } else if (data.gasList && data.gasList.length && this.type === 4) {
      this.printFactoryList = data.gasList || []
      this.defaultValue = data.gasList[0].gasId || ''
      this.currAddress = data.gasList[0].address || ''
    } else if (data.restaurantList && data.restaurantList.length && this.type === 3) {
      this.printFactoryList = data.restaurantList || []
      this.defaultValue = data.restaurantList[0].restaurantName || ''
      this.currAddress = data.restaurantList[0].restaurantAddress || ''
    }
  }

  changeType(params) {
    if (this.dateType === params) return
    this.dateType = params
    this.getGarageMonitorData(this.defaultValue)
  }

  // 处理WebSocket消息
  private getMessage(msg: any) {
    const data: any = JSON.parse(msg.data)
    // 使用消息处理器分发处理
    const codeHandlers = {
      // 印刷行业
      '-3': this.processPrintHourlyData,
      '-9': this.processPrintDailyData,
      // 汽修行业
      '-11': this.processGarageHourlyData,
      '-12': this.processGarageDailyData,
      // 加油站行业
      '-7': this.processGasHourlyData,
      '-18': this.processGasDailyData,
      // 餐饮油烟行业
      '-21': this.processRestaurantHourlyData,
      '-22': this.processRestaurantDailyData,
    }

    if (codeHandlers[data.code] && data.success) {
      codeHandlers[data.code](data)
    }
  }

  // 印刷行业小时数据处理
  private processPrintHourlyData(data: any): void {
    const curr = this.printFactoryList.find((item) => item.printFactoryId === this.defaultValue)
    if (!curr) return

    const vocConcentrationThreshold = curr.vocConcentrationThreshold
    const emissionRateThreshold = curr.emissionRateThreshold

    this.printRecordHourList = (data.printRecordHourList || []).map((item: any) => {
      return {
        ...item,
        morStandard1: item.vocAvgConcentration ? item.vocAvgConcentration > vocConcentrationThreshold : false,
        morStandard2: item.emissionAvgRate ? item.emissionAvgRate > emissionRateThreshold : false,
        time: item.hour + ':00',
      }
    })
  }

  // 印刷行业日数据处理
  private processPrintDailyData(data: any): void {
    if (data.dailyRecordList) {
      this.processChartData(data.dailyRecordList, 1)
    }
  }

  // 汽修行业小时数据处理
  private processGarageHourlyData(data: any): void {
    const curr = this.printFactoryList.find((item) => item.garageId === this.defaultValue)
    if (!curr) return

    const vocConcentrationThreshold = curr.vocConcentrationThreshold || 0
    const emissionRateThreshold = curr.emissionRateThreshold || 0

    this.printRecordHourList = (data.garageRecordHourList || []).map((item: any) => {
      return {
        ...item,
        morStandard1: item.vocAvgConcentration ? item.vocAvgConcentration > vocConcentrationThreshold : false,
        morStandard2: item.emissionAvgRate ? item.emissionAvgRate > emissionRateThreshold : false,
        time: item.hour + ':00',
      }
    })
  }

  // 汽修行业日数据处理
  private processGarageDailyData(data: any): void {
    if (data.dailyRecordList) {
      this.processChartData(data.dailyRecordList, 2)
    }
  }

  // 加油站行业小时数据处理
  private processGasHourlyData(data: any): void {
    const gasPojo = data.gasPojo
    if (!gasPojo || !gasPojo.environmentList || !gasPojo.environmentList.length) return

    const curr = this.printFactoryList.find((item) => item.gasId === this.defaultValue)
    if (!curr) return

    const vocConcentrationThreshold = curr.vocConcentrationThreshold || 0
    const emissionRateThreshold = curr.emissionRateThreshold || 0

    this.printRecordHourList = (gasPojo.environmentList || []).map((item: any) => {
      return {
        ...item,
        morStandard1: item.hydraulicPressure ? item.hydraulicPressure > vocConcentrationThreshold : false,
        morStandard2: item.oilConcentration ? item.oilConcentration > emissionRateThreshold : false,
        time: item.hours.substr(0, 2) + ':00',
      }
    })
  }

  // 加油站行业日数据处理
  private processGasDailyData(data: any): void {
    if (data.gasEnvironmentList) {
      this.processGasChartData(data.gasEnvironmentList)
    }
  }

  // 餐饮油烟行业小时数据处理
  private processRestaurantHourlyData(data: any): void {
    if (!data.restaurantRecordHourList || !data.restaurantRecordHourList.length) return

    const curr = this.printFactoryList.find((item) => item.restaurantName === this.defaultValue)
    if (!curr) return

    const concentrationThreshold = curr.concentrationThreshold || 0

    this.printRecordHourList = (data.restaurantRecordHourList || []).map((item: any) => {
      return {
        ...item,
        morStandard1: item.concentration ? item.concentration > concentrationThreshold : false,
        time: item.hour + ':00',
      }
    })
  }

  // 餐饮油烟行业日数据处理
  private processRestaurantDailyData(data: any): void {
    if (data.dailyRecordList) {
      this.processChartData(data.dailyRecordList, 3)
    }
  }

  // 统一处理图表数据
  private processChartData(dataList: any[], industryType: number): void {
    if (!dataList || !dataList.length) return

    // 初始化图表数据
    const config = this.chartConfig
    this.chartData = {
      data1: [],
      data2: [],
      xData: [],
      name1: config.name1,
      name2: config.name2,
      unit1: config.unit1,
      unit2: config.unit2,
      max: this.getThresholdValue(),
    }

    // 清空air数据
    this.airDataProp.date = []
    this.airDataProp.TVOC = []
    this.airDataProp.speed = []

    // 处理每一条数据
    dataList.forEach((item: any) => {
      const dateStr = item.date.substr(5, item.date.length)
      this.chartData.xData.push(dateStr)

      // 根据行业类型获取不同的数据字段
      const recordData = this.getRecordDataByType(item, industryType)
      if (recordData) {
        this.chartData.data1.push([dateStr, this.getFirstValue(recordData, industryType) || 0])
        this.chartData.data2.push([dateStr, this.getSecondValue(recordData, industryType) || 0])

        this.airDataProp.date.push(dateStr)
        this.airDataProp.TVOC.push(this.getFirstValue(recordData, industryType) || 0)
        this.airDataProp.speed.push(this.getSecondValue(recordData, industryType) || 0)
      } else {
        this.chartData.data1.push([dateStr, 0])
        this.chartData.data2.push([dateStr, 0])

        this.airDataProp.date.push(dateStr)
        this.airDataProp.TVOC.push(0)
        this.airDataProp.speed.push(0)
      }
    })
  }

  // 获取记录数据
  private getRecordDataByType(item: any, industryType: number): any {
    switch (industryType) {
      case 1:
        return item.printRecordDaily
      case 2:
        return item.garageRecordDaily
      case 3:
        return item.restaurantRecordDaily
      default:
        return null
    }
  }

  // 获取第一个值（浓度或相关数据）
  private getFirstValue(recordData: any, industryType: number): number {
    if (!recordData) return 0

    switch (industryType) {
      case 1:
      case 2:
        return recordData.vocAvgConcentration || 0
      case 3:
        return recordData.concentration || 0
      default:
        return 0
    }
  }

  // 获取第二个值（排放速率、电流等）
  private getSecondValue(recordData: any, industryType: number): number {
    if (!recordData) return 0

    switch (industryType) {
      case 1:
        return recordData.emissionAvgRate || 0
      case 2:
        return recordData.electronCurrent || 0
      case 3:
        return recordData.emissionAvgRate || 0
      default:
        return 0
    }
  }

  // 处理加油站图表数据
  private processGasChartData(dataList: any[]): void {
    if (!dataList || !dataList.length) return

    this.chartData = {
      data1: [],
      data2: [],
      xData: [],
      name1: '油气浓度',
      name2: '液阻压力',
      unit1: '单位：%/ppm',
      unit2: '单位：pa',
      max: this.getThresholdValue(),
    }

    // 清空air数据
    this.airDataProp.date = []
    this.airDataProp.TVOC = []
    this.airDataProp.speed = []

    dataList.forEach((item: any) => {
      const dateStr = item.date.substr(5, item.date.length)
      this.chartData.xData.push(dateStr)

      this.chartData.data1.push([dateStr, item.oilConcentration || 0])
      this.chartData.data2.push([dateStr, item.hydraulicPressure || 0])

      this.airDataProp.date.push(dateStr)
      this.airDataProp.TVOC.push(item.oilConcentration || 0)
      this.airDataProp.speed.push(item.hydraulicPressure || 0)
    })
  }

  // 公司切换
  private changeCompany(val: any): void {
    // 更新当前地址
    const company = this.printFactoryList.find((item) => this.getCompanyId(item) === val)
    if (company) {
      switch (this.type) {
        case 1:
          this.currAddress = company.printFactoryAddress || ''
          break
        case 2:
          this.currAddress = company.garageAddress || ''
          this.getGarageMonitorData(val)
          break
        case 3:
          this.currAddress = company.restaurantAddress || ''
          break
        case 4:
          this.currAddress = company.address || ''
          break
      }
    }

    // 获取监控列表
    this.getCameraList(val)
  }

  private async getCameraList(id: string) {
    this.cameraList = []
    this.selectedCamera = null
    this.cameraLoading = true

    try {
      const params: any = { pageNum: 1, pageSize: 1000 }

      if (this.type === 1) {
        params.printFactoryId = id
      } else if (this.type === 2) {
        params.garageId = id
      } else if (this.type === 3) {
        params.restaurantName = id
      } else if (this.type === 4) {
        params.gasId = id
      }

      const res = await getGarageCamera(params)
      if (res.data && res.data.data && res.data.data.records) {
        this.cameraList = res.data.data.records
        if (this.cameraList.length > 0) {
          this.selectedCamera = 0
        }
      }
    } catch (error) {
      console.error('获取监控列表失败', error)
    } finally {
      this.cameraLoading = false
    }
  }

  private async getGarageMonitorData(garageId: string) {
    try {
      const params = {
        garageId: garageId,
        type: this.dateType, // 默认获取日数据
      }

      const res = await getGarageList(params)
      if (res.data && res.data.data) {
        const { data } = res.data
        // 更新图表数据
        this.updateChartData(data)
      }
    } catch (error) {
      console.error('获取汽修行业数据失败', error)
    }
  }

  private updateChartData(data: any) {
    // 提取数据
    const vocData = data.list || []
    const currentData = data.weighingList || []
    const vocConcentrationThreshold = data.vocConcentrationThreshold || 0

    // 合并X轴数据，去除重复值
    const vocTimestamps = vocData.map((item) => item.monitorTime.substr(5, 11))
    const currentTimestamps = currentData.map((item) => item.monitorTime.substr(5, 11))
    const allTimestamps = [...vocTimestamps, ...currentTimestamps]
    const uniqueTimestamps = [...new Set(allTimestamps)].sort()

    // 更新图表数据格式
    this.chartData = {
      data1: vocData.map((item) => [item.monitorTime.substr(5, 11), item.vocAvgConcentration || 0]),
      data2: currentData.map((item) => [item.monitorTime.substr(5, 11), item.electronCurrent || 0]),
      xData: uniqueTimestamps,
      name1: 'VOC',
      name2: '电流',
      unit1: '单位：mg/m³',
      unit2: '单位：A',
      max: vocConcentrationThreshold,
    }
  }

  // 视频控制相关方法
  private playLiveVideo(index: number) {
    if (!this.cameraList[index]) return

    this.$set(this.cameraList[index], 'loading', true)

    getCarLive(this.cameraList[index].cameSerial)
      .then((res: any) => {
        if (res.data && res.data.data) {
          this.$set(this.cameraList[index], 'videoUrl', res.data.data.flvHttps)
        } else {
          this.$message.error('获取视频流失败')
        }
      })
      .finally(() => {
        this.$set(this.cameraList[index], 'loading', false)
      })
  }

  private stopPlayVideo(index: number) {
    if (this.cameraList[index]) {
      this.$set(this.cameraList[index], 'videoUrl', '')
    }
  }

  private play(index: number) {
    this.playLiveVideo(index)
  }

  private handleCameraChange(value: number) {
    this.selectedCamera = value
  }

  private async showVideoTapeList(camera: any) {
    if (!camera.online) {
      this.$message.warning('该监控点离线')
      return
    }

    // 重置日期范围为最近1天
    this.dateRange = [moment().subtract(1, 'days'), moment()]

    this.showVideoTapeWindow = true
    this.loadingTapeList = true
    this.channelId = camera.cameSerial

    await this.loadTapeList(1)
  }

  private async loadTapeList(pageNum: number) {
    this.tapePageNum = pageNum
    this.loadingTapeList = true

    const start = this.formatDate(this.dateRange[0], 'yyyy-MM-dd hh:mm:ss')
    const end = this.formatDate(this.dateRange[1], 'yyyy-MM-dd hh:mm:ss')
    this.tapeList = []

    try {
      const res = await playbackListGarage({
        channelId: this.channelId,
        startTime: start,
        endTime: end,
        pageNum: this.tapePageNum,
        pageSize: this.tapePageSize,
      })

      if (res.data && res.data.data) {
        const { data } = res.data
        // 确保每个记录都有所需的属性
        const records = (data.records || []).map((item: any) => {
          return {
            ...item,
            startTime: item.startTime || '',
            endTime: item.endTime || '',
            channelSerial: item.channelSerial || '',
            channelName: item.channelName || '',
          }
        })
        this.tapeList = records
        this.tapePageTotal = data.total || 0
      } else {
        this.tapeList = []
        this.tapePageTotal = 0
      }
    } catch (error) {
      console.error('获取录像列表失败', error)
      this.tapeList = []
      this.tapePageTotal = 0
    } finally {
      this.loadingTapeList = false
    }
  }

  // 统一的日期格式化方法
  private formatDate(date: moment.Moment | Date, format: string): string {
    // 处理moment对象
    if (moment.isMoment(date)) {
      return date.format(format.replace('yyyy', 'YYYY').replace('MM', 'MM').replace('dd', 'DD').replace('hh', 'HH').replace('mm', 'mm').replace('ss', 'ss'))
    }

    // 处理Date对象
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hour = String(date.getHours()).padStart(2, '0')
    const minute = String(date.getMinutes()).padStart(2, '0')
    const second = String(date.getSeconds()).padStart(2, '0')

    return format.replace('yyyy', year.toString()).replace('MM', month).replace('dd', day).replace('hh', hour).replace('mm', minute).replace('ss', second)
  }

  private formatTableDate(dateStr: string): string {
    if (!dateStr) return '-'
    return dateStr.replace('T', ' ')
  }

  @Watch('defaultValue', { immediate: true, deep: true })
  private onDefaultValue(newValue: any, oldValue: any) {
    if (newValue) {
      this.date = getNowTime().substr(0, 10)
      this.socket = this.$route.params.socket
      if (this.type === 1) {
        this.socket.send(JSON.stringify({ code: 3, printFactoryId: newValue, date: this.date, token: getToken() }))
        this.socket.send(JSON.stringify({ code: 9, printFactoryId: newValue, token: getToken() }))
      } else if (this.type === 2) {
        // 从接口获取汽修行业数据
        this.getGarageMonitorData(newValue)
        this.socket.send(JSON.stringify({ code: 11, garageId: newValue, token: getToken() }))
      } else if (this.type === 4) {
        this.socket.send(JSON.stringify({ code: 7, gasId: newValue, token: getToken() }))
        this.socket.send(JSON.stringify({ code: 18, gasId: newValue, token: getToken() }))
      } else if (this.type === 3) {
        this.socket.send(JSON.stringify({ code: 21, restaurantName: newValue, date: this.date, token: getToken() }))
        this.socket.send(JSON.stringify({ code: 22, restaurantName: newValue, token: getToken() }))
      }
      // 监听socket消息
      this.socket.onmessage = this.getMessage
      // 获取对应的监控列表
      this.getCameraList(newValue)
    }
  }

  private async reloadTapeList(currentPage: number) {
    await this.loadTapeList(currentPage)
  }

  private async startPlayVideoTape(camera: any) {
    if (!camera || !camera.channelName) {
      this.$message.error('录像信息不完整')
      return
    }

    this.showVideoTapeWindow = false
    this.replayLoading = true
    this.playVideoTape = true
    this.tapeName = `录像回放 ${camera.channelName}`
    this.videoUrl = '' // 初始化清空视频URL

    const start = camera.startTime.replace('T', ' ')
    const end = camera.endTime.replace('T', ' ')

    try {
      const response = await playbackStartGarage({
        channelId: this.channelId,
        startTime: start,
        endTime: end,
      })

      if (response && response.data && response.data.data) {
        const { data } = response.data
        // 使用flv或flvHttps地址，根据API返回结构选择
        this.videoUrl = data.flv || data.flvHttps || ''
        this.streamId = data.streamId

        if (!this.videoUrl) {
          this.$message.error('获取视频流地址失败')
          this.playVideoTape = false
        } else {
          console.log('视频回放地址:', this.videoUrl)
        }
      } else {
        this.$message.error('获取回放地址失败')
        this.playVideoTape = false
      }
    } catch (error) {
      console.error('开始回放失败', error)
      this.$message.error('开始回放失败，请稍后重试')
      this.playVideoTape = false
    } finally {
      this.replayLoading = false
    }
  }

  private stopPlayTape() {
    this.playVideoTape = false
    this.videoUrl = ''
    // 如果有streamId，可能需要调用停止回放的API
    if (this.streamId) {
      // 释放资源
      this.streamId = undefined
    }
  }

  private closeAndStop() {
    this.stopPlayTape()
  }
}
</script>
