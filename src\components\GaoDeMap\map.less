.amap-info-content,
.amap-info-outer {
  padding: 0;
}
.amap-info-content {
  background: transparent !important;
}
.amap-info-outer,
.amap-menu-outer {
  box-shadow: none !important;
}
.amap-info-sharp {
  display: none !important;
}
/* 空气 */
.marker-content {
  opacity: 0.89;
  box-sizing: border-box;
  color: #00eaff;
  overflow: -Scroll;
  overflow-x: hidden;
  overflow-y: hidden;
  position: relative;
  // clip-path: polygon(4% 0, 96% 0, 100% 7%, 100% 100%, 0 100%, 0 7%);
  width: 3.6rem;
  height: 2.3rem;
  background: url(../../assets/<EMAIL>);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  .closeInfoWindow {
    position: absolute;
    top: 0.2rem;
    right: 0.25rem;
    width: 0.25rem !important;
    cursor: pointer;
  }
}
.marker-top {
  width: 3rem;
  height: 0.54rem;
  // background: rgba(10, 56, 147, 1);
  margin-top: 0.18rem;
  // margin-left: 0.1rem;
}
.marker-top-top {
  display: flex;
  justify-content: space-between;
  box-sizing: border-box;
  padding: 0.1rem 0 0.01rem 0.27rem;
}
.update-time {
  width: 0.9rem;
  color: #fff;
  font-size: 0.16rem;
}
.marker-top-name {
  font-weight: 500;
  width: 1.9rem;
  display: flex;
  align-items: center;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
.marker-top-name1 {
  font-size: 0.16rem;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
.marker-top-state {
  width: 0.35rem;
  height: 0.16rem;
  background: #22b331;
  border-radius: 0.05rem;
  line-height: 0.16rem;
  color: #fff;
  font-size: 0.12rem;
  text-align: center;
  margin-left: 0.05rem;
  flex: none;
}
.marker-top-bottom {
  padding-left: 0.27rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 0.05rem;
}
.marker-top-left {
  font-size: 0.14rem;
  display: flex;
  align-items: center;
}
.dian {
  width: 0.06rem;
  height: 0.06rem;
  background: rgba(0, 234, 255, 1);
  border-radius: 50%;
  margin-right: 0.06rem;
}
.marker-top-right {
  display: flex;
  align-items: center;
  // margin-left: 0.28rem;
}
.marker-top-right img {
  width: 0.225rem;
  height: 0.225rem;
}
.marker-top-right div {
  font-size: 0.16rem;
  margin-left: 0.1rem;
}
.marker-bottom {
  padding-top: 0.2rem;
  width: 3.2rem;
  margin-left: 0.1rem;
  // padding-bottom: 0.12rem;
  p {
    margin-bottom: 0;
  }
  .marker-top-detail {
    font-size: 0.14rem;
    width: 0.7rem !important;
    height: 0.25rem;
    line-height: 0.25rem;
    text-align: center;
    background: rgba(9, 72, 171, 0.6);
    border: 1px solid rgba(0, 234, 255, 1);
    border-radius: 0.06rem;
    cursor: pointer;
    margin: 0 auto;
    display: flex;
    justify-content: center;
  }
}
.marker-bottom > div {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
}
.marker-bottom > div > div {
  width: 33.33%;
  text-align: center;
  margin-bottom: 0;
  // margin-top: 0.08rem;
}
.marker-bottom > div p:nth-of-type(1) {
  font-size: 0.14rem;
  color: #ffffff;
}
.marker-bottom > div p:nth-of-type(2) {
  font-size: 0.16rem;
  // color: #4adb4e;
  // display: none;
}

.amap-marker-label {
  position: absolute;
  z-index: 2;
  // width: 2rem;
  border: 1px solid transparent;
  // background-image: url("../../assets/szjcd.png");
  // background-repeat: no-repeat;
  // background-size: 100% 100%;
  background-color: transparent;
  white-space: nowrap;
  cursor: default;
  padding: 0.03rem;
  font-size: 0.12rem;
  line-height: 0.14rem;
  .info {
    font-size: 0.12rem;
    font-family: Source Han Sans CN;
    font-weight: 500;
    color: rgba(255, 255, 255, 1);
    text-align: center;
  }
}
.amap-controlbar-zoom {
  display: none;
}
.amap-luopan,
.amap-luopan-bg {
  background: url(../../assets/luopan.png) -44px -60px no-repeat;
}
.amap-compass {
  background: url(../../assets/luopan.png) -462px -52px no-repeat;
}
.amap-pointers {
  background: url(../../assets/luopan.png) -562px -52px no-repeat;
}
.amap-pitchDown,
.amap-pitchUp {
  background: url(../../assets/luopan.png) -605px -98px no-repeat;
}
.amap-rotateLeft,
.amap-rotateRight {
  background: url(../../assets/luopan.png) -603px -154px no-repeat;
}
.amap-rotateLeft,
.amap-rotateRight {
  background: url(../../assets/luopan.png) -603px -154px no-repeat;
}
.siteMsgWindow {
  width: 3.5rem;
  height: 1.8rem;
  background: url("../../assets/<EMAIL>") no-repeat;
  background-size: 100% 100%;
  box-sizing: border-box;
  padding: 0.1rem 0.2rem 0.2rem;
  > div:not(:last-child) {
    display: flex;
    align-items: center;
  }
  > div:first-child {
    justify-content: space-between;
    align-items: flex-end;
    margin-top: 0.05rem;
    > img {
      width: 0.24rem;
      cursor: pointer;
    }
  }
  > div:nth-child(2) {
    font-size: 0.16rem;
    // margin: 0.1rem 0;
    margin-top: 0.24rem;
    margin-bottom: 0.15rem;
    img {
      width: 0.16rem;
      height: 0.13rem;
    }
    > span:nth-child(2) {
      margin: 0 0.1rem 0 0.1rem;
    }
    > span:nth-child(3) {
      font-size: 0.2rem;
      color: rgba(232, 14, 14, 1);
    }
    > span:nth-child(4) {
      margin: 0 0.2rem 0 0.1rem;
    }
    > span:last-child {
      background-color: rgb(24, 224, 74);
      padding: 0 0.1rem;
    }
    > .exceedStandard {
      background-color: rgba(232, 14, 14, 1);
    }
  }
  > div:nth-child(3) {
    font-size: 0.16rem;
  }
  .title-site {
    font-size: 0.17rem;
    font-family: Source Han Sans CN;
    font-weight: 500;
    color: rgba(0, 234, 255, 1);
    flex: 1;
    /*width: 2rem;*/
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }
  .title-time {
    width: 0.9rem;
    font-size: 0.15rem;
  }
  .sign-out {
    width: 0.24rem;
    height: 0.24rem;
  }
}
