import request from "@/utils/request";
import { AxiosPromise } from "axios";

/**
 * @method functionName
 * @param {type} data 获取上报详情
 * @description
 */
export function getReportDetails(): AxiosPromise<any> {
  return request({
    url: "emergency_command_task/big_data/report_details",
    method: "get"
  });
}
/**
 * @method functionName
 * @param {type} data 获取任务概览
 * @description
 */
export function getTaskOverview(): AxiosPromise<any> {
  return request({
    url: "emergency_command_task/big_data/task_overview",
    method: "get"
  });
}
/**
 * @method functionName
 * @param {type} data 任务反馈时间轴显示
 * @description
 */
export function getTaskRemark(): AxiosPromise<any> {
  return request({
    url: "emergency_command_task/big_data/task_remark",
    method: "get"
  });
}

/**
 * @method functionName
 * @param {type} data 任务反馈时间轴显示
 * @description
 */
export function getEmergencyStationList(taskId: any, distance:any): AxiosPromise<any> {
  return request({
    url: "emergency_command_task/stationList",
    method: "get",
    params:{
      taskId,
      distance
    }
  });
}

/**
 * @method functionName
 * @param {type} data 获取水质数据
 * @description
 */
export function getMonitorItemDetailRecord(stationId: any, itemCode: any, time: any): AxiosPromise<any> {
  return request({
    url: "em/water",
    method: "get",
    params: {
      itemCode,
      stationId,
      time
    }
  });
}
/**
 * @method functionName
 * @param {type} data 获取水质数据
 * @description
 */
export function getAqiQuality(stationCode: any, pollutantCode: any, time: any): AxiosPromise<any> {
  return request({
    url: "em/air",
    method: "get",
    params: {
      stationCode,
      pollutantCode,
      time
    }
  });
}
/**
 * @method functionName
 * @param {type} data 获取水质数据
 * @description
 */
export function getTwentyFourHour(companyId: any, time: any): AxiosPromise<any> {
  return request({
    url: "em/company",
    method: "get",
    params: {
      companyId,
      time
    }
  });
}
