<template>
  <div class="app_rank">
    <div v-show="!showEmpty" id="waterMonitor"></div>

    <a-empty v-show="showEmpty" description="暂无趋势数据" />
  </div>
</template>

<script>
  import * as echarts from 'echarts'
  export default {
    name: 'dialogTrendLine',
    props: {
      echartData: {
        type: Array,
        default: () => [],
      },
      echartDataArray: {
        type: Array,
        default: () => [],
      },
      chartX: {
        type: Array,
        default: () => [],
      },
      dataType: {
        type: String,
        default: () => '',
      },
      name: {
        type: String,
        default: () => '',
      },
      nameArr: {
        type: Array,
        default: () => [],
      },
    },
    components: {},
    data() {
      return {
        echart: '',
        // echartData:[ 0,2,4,1,3],
        // echartBgData:[4,4,4,4,4],
        // chartX:[
        //   '氨氮',
        //   '总磷',
        //   'PH',
        //   '溶解氧',
        //   '高锰酸盐指数'
        // ],
        showEmpty: true,
      }
    },
    watch: {
      echartData: {
        handler(nval, oval) {
          if (nval) {
            if (this.echart) {
              this.echart.dispose()
            }
            this.initEchart()
          }
        },
        deep: true,
        // immediate: true,
      },
    },
    mounted() {
      const { echartData, echartDataArray } = this
      // console.log(echartData.length || echartDataArray.length, echartData,'-----------------------------72')
      if (echartData.length || echartDataArray.length)
        this.showEmpty = false
      if (echartDataArray.length) {
        this.showEmpty = echartDataArray.every((arr) => !arr.length)
      }
      this.$nextTick(() => {
        this.initEchart()
      })
    },
    methods: {
      initEchart() {
        const chartMonth = echarts.init(
          document.getElementById('waterMonitor'),
        )
        this.echart = chartMonth
        chartMonth.clear()
        chartMonth.showLoading()
        const colors = [
          {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: '#38E3FF', // 0% 处的颜色
              },
              {
                offset: 1,
                color: '#3B7BB9', // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
          {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: '#88712c', // 0% 处的颜色
              },
              {
                offset: 1,
                color: '#8e6230', // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
          {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: '#11c69a', // 0% 处的颜色
              },
              {
                offset: 1,
                color: '#06b8c4', // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
          {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: '#5e6642', // 0% 处的颜色
              },
              {
                offset: 1,
                color: '#6c4c42', // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
          {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: '#8f3ba4', // 0% 处的颜色
              },
              {
                offset: 1,
                color: '#8f3ba4', // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        ]
        const legendData =
          this.nameArr.length > 0 ? this.nameArr : [this.name]

        let defaultSeries = this.echartData.map((data,i)=>{
          return {
            // name: '平均时长',
            // symbolSize:15,
            name: this.name || this.nameArr[i],
            type: 'line',
            // stack: 'Total',
            smooth: true,
            lineStyle: {
              width: 2,
              color: colors[i]
            },
            symbol: 'circle',
            symbolSize: 7,
            // showSymbol: false,
            areaStyle: {
              opacity: 0.4,
              color: colors[i],
            },
            itemStyle: {
              // normal: {
              //   color: '#030B1B',
              //   borderWidth: 2,
              //   borderColor: '#00B3F6 ',
              //   shadowBlur: 7,
              //   shadowColor: 'rgba(136,213,241,0.7)',
              // },
            },
            emphasis: {
              focus: 'series',
            },
            data: data || [],
          }
        })

        let seriesArr = [...defaultSeries]

        if (this.echartDataArray.length) {
          seriesArr = []
          this.echartDataArray.forEach((arr, index) => {
            seriesArr.push({
              // name: '平均时长',
              // symbolSize:15,
              name: this.nameArr[index],
              type: 'line',
              // stack: 'Total',
              smooth: true,
              lineStyle: {
                width: 3,
                color: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: '#38E3FF', // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: '#3B7BB9', // 100% 处的颜色
                    },
                  ],
                  global: false, // 缺省为 false
                },
              },
              symbol: 'circle',
              symbolSize: 7,
              // showSymbol: false,
              areaStyle: {
                opacity: 0.8,
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: 'rgba(59,123,185,0.65)',
                    },
                    {
                      offset: 0.5,
                      color: 'rgba(59,123,185,0.2)',
                    },
                    {
                      offset: 1,
                      color: 'rgba(59,123,185,0.01)',
                    },
                  ],
                ),
              },
              itemStyle: {
                normal: {
                  color: '#030B1B',
                  borderWidth: 2,
                  borderColor: '#00B3F6 ',
                  shadowBlur: 7,
                  shadowColor: 'rgba(136,213,241,0.7)',
                },
              },
              emphasis: {
                focus: 'series',
              },
              data: arr || [],
            })
          })
        }

        let option = {
          // 图例颜色
          color: ['#83e3ff','#802b13','#fbd74c','#1e935d','#8f3ba4'],
          tooltip: {
            trigger: 'axis',
            // axisPointer: {
            //   // 坐标轴指示器，坐标轴触发有效
            //   type: 'line', // 默认为直线，可选为：'line' | 'shadow'
            // },
            // formatter: function (params) {
            //   return params[0].name + '<br/> ' + params[0].value
            // },
          },
          legend: {
            orient: 'horizontal', //水平展示，不写默认水平展示
            right: 10,
            top: 0,
            icon: "roundRect",
            itemHeight: 4,
            itemWidth: 15,
            itemGap: 15,
            itemStyle:{
              
            },
            textStyle: {
              // fontSize: 18,//字体大小
              color: '#ffffff', //字体颜色
            },
            data: legendData,
          },
          grid: {
            // containLabel: true,
            left: '8%',
            top: '25%',
            right: '2%',
            bottom: '15%',
          },
          xAxis: {
            data: this.chartX,
            axisTick: {
              show: false,
            },
            axisLabel: {
              color: '#80B6C8',
              interval: 'auto',
              // showMaxLabel: false,
              // rotate: -35,
              fontSize: 12,
              formatter(val) {
                return val.split(' ').join('\n')
              },
            },
            axisLine: {
              lineStyle: {
                color: 'rgba(56,89,130,0.7)',
                type: [5, 5],
              },
            },
          },
          yAxis: {
            name:
              '单位:' +
              (this.dataType == 'ph'
                ? '无'
                : this.dataType
                ? this.dataType
                : '(mg/L)'),
            nameTextStyle: {
              color: '#80B6C8',
              padding: [0, 0, 0, 10],
            },
            min:0,
            max: this.echartDataArray.length ? null : 10,
            // max: 10,
            splitLine: {
              lineStyle: {
                color: 'rgba(56,89,130,0.7)',
                type: [5, 5],
              },
            },
            axisLine: {
              lineStyle: {
                color: '#1B5BBA',
              },
            },
            axisLabel: {
              color: '#80B6C8',
              interval: 'auto',
            },
          },
          series: [...seriesArr],
        }
        chartMonth.setOption(option)
        chartMonth.hideLoading()
      },
    },
  }
</script>
<style scoped>
  .app_rank {
    width: 100%;
    height: 100%;
  }
  #waterMonitor {
    width: 100%;
    height: 250px;
  }
</style>
