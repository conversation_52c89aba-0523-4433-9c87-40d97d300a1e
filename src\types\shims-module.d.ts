declare module "ant-design-vue/lib/locale-provider/zh_CN" {
  // eslint-disable-next-line @typescript-eslint/camelcase
  const zh_CN: any;
  // eslint-disable-next-line @typescript-eslint/camelcase
  export default zh_CN;
}

// declare const BMap: any;

declare module "vue-video-player" {
  const VideoPlayer: any;
  export default VideoPlayer;
}

declare module "selectfx" {
  const SelectFx: any;
  export default SelectFx;
}

declare module "classie" {
  const Classie: any;
  export default Classie;
}

declare module "vue-count-to" {
  const countTo: any;
  export default countTo;
}

declare module "jquery" {
  const jquery: any;
  export default jquery;
}


declare module "flv.js" {
  const flvjs: any;
  export default flvjs;
}
declare module "vue-seamless-scroll" {
  const vueSeamlessScroll: any;
  export default flvjs;
}
declare module '*.js'
declare module 'vue2-org-tree';