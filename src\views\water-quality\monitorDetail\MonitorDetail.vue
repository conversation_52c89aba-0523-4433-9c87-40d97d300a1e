<template>
  <a-modal :visible="visibles" class="water-detail-dialog" :footer="null" centered destroy-on-close @cancel="cancel">
    <header class="header">
      <span class="title">监控信息</span>
      <a-icon type="close" style="color: #1598cf; font-size: 18px; cursor: pointer; margin-top: 10px" @click="cancel" />
    </header>
    <section class="detail-content">
      <header class="content-header">
        <div>
          <span class="label">监控名称：</span>
          <span class="value">{{ data.monitorName }}</span>
        </div>
      </header>
      <div class="tab-container">
        <button v-for="tab in tabs" :key="tab.value" class="tab-button" :class="{ active: activeTab === tab.value }" @click="changeTab(tab.value)">
          {{ tab.name }}
        </button>
      </div>
      <div class="tab-content">
        <div v-if="activeTab === 1" v-loading="monitorLoading && data.online" class="tab-pane" style="position: relative">
<!--          <LivePlayer v-if="monitorUrl && data.online" :video-url="monitorUrl" :hasaudio="false" autoplay live aspect="fullscreen" />-->
          <Jessibuca  v-if="monitorUrl && data.online" :video-url="monitorUrl" :hasaudio="false" autoplay live aspect="fullscreen"/>
          <div v-if="!data.online" class="empty" style="width: 100%;height: 100%;display: grid;place-items: center;user-select: none;color: grey">设备离线</div>
        </div>
        <div v-if="activeTab === 2" class="tab-pane">
          <AlarmRecord :data="data"/>
        </div>
        <div v-if="activeTab === 3" class="tab-pane" style="overflow-y: auto;padding-left: 20px;margin-left: -20px;">
          <LogList :data="data"/>
        </div>
      </div>
    </section>
  </a-modal>
</template>

<script>
import { Modal } from 'ant-design-vue'
import LivePlayer from '@liveqing/liveplayer'
import AlarmRecord from './AlarmRecord.vue'
import LogList from './LogList.vue'
import Jessibuca from '@/components/jessibucaPlayer/jessibuca.vue'


export default {
  name: 'MonitorDetail',
  props: {
    visibles: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
      default: () => ({}),
    },
    monitorUrl: {
      type: String,
      default: '',
    },
    monitorLoading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      activeTab: 1,
      tabs: [
        { name: '摄像头', value: 1 },
        { name: '告警记录', value: 2 },
        { name: '联动任务日志', value: 3 },
      ],
    }
  },
  created() {
  },
  methods: {
    cancel() {
      this.$emit('cancel')
    },
    changeTab(value) {
      this.activeTab = value
    },
  },
  computed: {},
  watch: {
    // monitorUrl(){
    //   console.log(this.monitorUrl, '11111111111111111')
    // },
  },
  components: {
    Jessibuca,
    AModal: Modal,
    LivePlayer,
    AlarmRecord,
    LogList,
  },
}
</script>

<style lang="less" scoped>
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-left: 30px;
  padding-top: 4px;
  padding-right: 8px;
  .title {
    font-size: 16px;
    color: #dcf0ff;
  }
}

.detail-content {
  margin-top: 35px;
  padding-left: 27px;
  padding-right: 20px;
  .label {
    color: #95aabe;
  }
  .value {
    color: #f4faff;
  }

  .content-header {
    padding-left: 13px;
    padding-bottom: 31px;
    border-bottom: 1px solid #093c68;
  }

  .tab-container {
    display: flex;
    margin-top: 20px;
    background: #051c37;
    border: 1px solid #09294e;
  }

  .tab-button {
    width: 157px;
    height: 32px;
    color: #5186ad;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s;
    background: #072140;
    border: none;
    border-right: 1px solid #09294e;

    &.active {
      background: linear-gradient(0deg, #074f96 0%, #09182d 100%);
      color: #dcf0ff;
    }

    &:hover {
      color: #dcf0ff;
    }
  }

  .tab-content {
    padding: 20px 0;
    .tab-pane {
      height: 504px;
    }
  }

  .tab-pane {
    color: #f4faff;
  }
}
</style>

<style lang="less">
.water-detail-dialog {
  background: transparent !important;
  width: fit-content !important;
  height: fit-content;
  .ant-modal-close {
    display: none;
  }
  .ant-modal-content {
    background: transparent;
    width: 886px !important;
    height: 767px;
    background: url(~@/assets/waterPng/<EMAIL>) center / 100% 100% no-repeat;
  }
}
</style>
