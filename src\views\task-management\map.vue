<style lang="less" scoped>
.container {
  width: 100%;
  height: 100%;
  .mapContainer {
    width: 100%;
    height: 100%;
  }
}
</style>
<style lang="less">
.amap-icon img {
  z-index: 999999 !important;
}
.amap-marker-label {
  border: 1px solid transparent !important;
  background-color: transparent !important;
}
.map-control {
  .amap-marker-label {
    position: absolute;
    z-index: 2;
    // width: 2rem;
    border: 1px solid transparent;
    // background-image: url("../../assets/szjcd.png");
    // background-repeat: no-repeat;
    // background-size: 100% 100%;
    background-color: transparent;
    white-space: nowrap;
    cursor: default;
    padding: 0.03rem;
    font-size: 0.12rem;
    line-height: 0.14rem;
    .info {
      font-size: 0.12rem;
      font-family: Source Han Sans CN;
      font-weight: 500;
      color: rgba(255, 255, 255, 1);
      text-align: center;
    }
  }
  .amap-controlbar-zoom {
    display: none;
  }
  .amap-luopan,
  .amap-luopan-bg {
    background: url(../../assets/luopan.png) -44px -60px no-repeat;
  }
  .amap-compass {
    background: url(../../assets/luopan.png) -462px -52px no-repeat;
  }
  .amap-pointers {
    background: url(../../assets/luopan.png) -562px -52px no-repeat;
  }
  .amap-pitchDown,
  .amap-pitchUp {
    background: url(../../assets/luopan.png) -605px -98px no-repeat;
  }
  .amap-rotateLeft,
  .amap-rotateRight {
    background: url(../../assets/luopan.png) -603px -154px no-repeat;
  }
  .amap-rotateLeft,
  .amap-rotateRight {
    background: url(../../assets/luopan.png) -603px -154px no-repeat;
  }
}
.task-info {
  position: relative;
  width: 10px;
  height: 10px;
  left: 0;
  top: 0;
  border-radius: 50%;
  /*background-color: red;*/
  transform: rotateX(70deg);
  /*-moz-animation-name: ripple;*/
  /*-webkit-animation-name: ripple;*/
  animation-name: ripple;
  animation-delay: 0s;
  animation-duration: 1s;
  -moz-animation-iteration-count: infinite;
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
}
.task-info-on {
  background-color: #47fabd;
}
.task-info-off {
  background-color: #ffdb1f;
}
@keyframes ripple {
  from {
    opacity: 1;
  }
  to {
    width: 40px;
    height: 40px;
    top: -15px;
    left: -15px;
    border-radius: 50%;
    opacity: 0;
  }
}
</style>
<template>
  <div class="container">
    <div ref="mapContainer" class="mapContainer"></div>
  </div>
</template>
<script>
// import AMap from 'AMap'
import AMapLoader from '@amap/amap-jsapi-loader'
import dqImage from '../../assets/department/<EMAIL>'
import dqActiveImage from '../../assets/department/<EMAIL>'
import szImage from '../../assets/department/<EMAIL>'
import szActiveImage from '../../assets/department/<EMAIL>'
import cyImage from '../../assets/department/<EMAIL>'
import cyActiveImage from '../../assets/department/<EMAIL>'
import xgImage from '../../assets/department/<EMAIL>'
import xgActiveImage from '../../assets/department/<EMAIL>'
import { webglcontextlostHandle } from '@/utils/index'
import jinniuStreet from '@/assets/map-geojson/jinniu_street'
let AMap
export default {
  name: 'department-map',
  props: {
    mapMarkerList: {
      type: Array,
      default: () => [],
    },
    activeIndex: {
      type: Number,
      default: 0,
    },
    currObj: {
      type: Object,
      default: () => {},
    },
    taskIndex: {
      type: Number,
      default: -1,
    },
  },
  watch: {
    mapMarkerList(val) {
      if (this.markerList.length) {
        this.maps.remove(this.markerList)
        this.firstMaker = null
        this.isfirst = false
        this.currMarker = null
        this.markerList = []
      }
      if(!AMap) return
      this.addMarkers()
    },
    activeIndex(val) {
      this.showMaker()
    },
    currObj: {
      handler(val) {
        this.changeMaker()
      },
      deep: true,
      immediate: true,
    },
  },
  data() {
    return {
      maps: null,
      mapStyle: 'amap://styles/0739a6632d849ae1b5e6f1c52f7d12c3',
      markerList: [],
      firstMaker: null,
      isfirst: false,
      currMarker: null,
    }
  },
  created() {
    console.log('--created---task---179');
    AMapLoader['reset']()
  },
  mounted() {
    // // load 加载
    AMapLoader.load({
      key: '777fec7ef3cc29281d60ae900fa33925', // 申请好的Web端开发者Key，首次调用 load 时必填
      version: '1.4.15', // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
      plugins: [
        'AMap.DistrictSearch',
        'AMap.Heatmap',
        'AMap.ControlBar',
        'AMap.Object3DLayer',
        'Map3D',
        'AMap.Geocoder',
        'AMap.CircleMarker',
        'AMap.MouseTool',
      ], // 需要使用的的插件列表，如比例尺'AMap.Scale'等
      AMapUI: {
        // 是否加载 AMapUI，缺省不加载
        version: '1.0', // AMapUI 版本
        // "plugins":['overlay/SimpleMarker'],       // 需要加载的 AMapUI ui插件
      },
      Loca: {
        // 是否加载 Loca， 缺省不加载
        version: '1.3.2', // Loca 版本
      },
    })
      .then((amaps) => {
        console.log('task---initMap--------207');
        AMap = amaps
        this.initMap()
        this.addMarkers()
      })
      .catch((e) => {
        console.log(e)
      })
  },
  beforeDestroy() {
    console.log('--beforeDestroy---task---218');
    if (this.maps) {
      this.maps.clearMap()
      this.maps.destroy()
      this.maps = null
    }
    this.markerList = []
    AMap = null
  },
  methods: {
    showMaker() {
      this.isfirst = false
      const curr = []
      this.markerList.forEach((item) => {
        const data = item.w.data
        if (this.activeIndex == 0) {
          if (!this.isfirst) {
            this.isfirst = true
            this.firstMaker = item
          }
          item.show()
        } else {
          if (data.typeId == this.activeIndex) {
            if (!this.isfirst) {
              this.isfirst = true
              this.firstMaker = item
            }
            item.show()
          } else {
            item.hide()
          }
        }
      })
      if (this.markerList.length) {
        this.maps.setCenter(
          new AMap.LngLat(
            Number(this.firstMaker.w.data.lng),
            Number(this.firstMaker.w.data.lat)
          )
        )
      }
    },
    addMarkers() {
      this.mapMarkerList.forEach(item => {
        if(!item.lng)return
        let markerImg
        if (item.typeId == 2) {
          markerImg = item.completeStatus ? dqImage : dqActiveImage
        } else if (item.typeId == 1 || item.typeId == 11) {
          markerImg = item.completeStatus ? szImage : szActiveImage
        } else if (item.typeId == 3) {
          markerImg = item.completeStatus ? cyImage : cyActiveImage
        } else if (item.typeId == 4) {
          markerImg = item.completeStatus ? xgImage : xgActiveImage
        }
        const icon = new AMap.Icon({
          // 图标尺寸
          size: new AMap.Size(24, 28),
          // 图标的取图地址
          image: markerImg,
          // 图标所用图片大小
          imageSize: new AMap.Size(24, 28),
          // 图标取图偏移量
          imageOffset: new AMap.Pixel(0, 0),
        })
        const marker = new AMap.Marker({
          map: this.maps,
          position: new AMap.LngLat(Number(item.lng), Number(item.lat)),
          icon: icon,
          offset: new AMap.Pixel(-15, -30),
          data: item,
        })
        this.markerList.push(marker)
        AMap.event.addListener(marker, 'click', (e) => {
          this.markerClick(marker)
        })
      });
      if (this.markerList.length) {
        this.firstMaker =
          this.taskIndex === -1
            ? this.markerList[0]
            : this.markerList[this.taskIndex]
        const data = this.firstMaker.w.data
        this.firstMaker.setLabel({
          offset: new AMap.Pixel(3, 22), //设置文本标注偏移量
          content: `<div class='${
            data.completeStatus ? 'task-info-on' : 'task-info-off'
          } task-info'></div>`, //设置文本标注内容
        })
        this.firstMaker.setzIndex(101)
        this.isfirst = true
        this.maps.setCenter(
          new AMap.LngLat(
            Number(this.firstMaker.w.data.lng),
            Number(this.firstMaker.w.data.lat)
          )
        )
      }
    },
    changeMaker() {
      let marker = ''
      this.markerList.forEach((item) => {
        const data = item.w.data
        if (JSON.stringify(this.currObj) === JSON.stringify(data)) {
          marker = item
          item.setzIndex(101)
          this.maps.setCenter(
            new AMap.LngLat(Number(data.lng), Number(data.lat))
          )
          // this.currMarker = marker
          // this.$emit('change', this.currMarker)
          item.setLabel({
            offset: new AMap.Pixel(3, 22), //设置文本标注偏移量
            content: `<div class='${
              data.completeStatus ? 'task-info-on' : 'task-info-off'
            } task-info'></div>`, //设置文本标注内容
          })
        } else {
          item.setzIndex(100)
          item.setLabel({
            offset: new AMap.Pixel(3, 22), //设置文本标注偏移量
            content: '<div></div>', //设置文本标注内容
          })
        }
      })
      // let markerImg = data.stationTypeId == 2 ? dqImage : data.stationTypeId == 1 ? szImage : data.stationTypeId == 3 ? cyImage : xgImage
      // let markerActiveImg = data.stationTypeId == 2 ? dqActiveImage : data.stationTypeId == 1 ? szActiveImage: data.stationTypeId == 3 ? cyActiveImage : xgActiveImage
      // if (this.currMarker == null) {
      //     this.currMarker = marker
      //     // marker.setIcon(markerActiveImg)
      // } else {
      //     // this.cleatSelected(this.currMarker)
      //     if (this.currMarker == marker) {
      //         this.currMarker = null
      //     } else {
      //         // marker.setIcon(markerActiveImg)
      //         this.currMarker = marker
      //     }
      // }
      // this.markerClick(marker)
    },
    markerClick(marker) {
      const data = marker.w.data
      this.markerList.forEach((item) => {
        if (item == marker) {
          item.setLabel({
            offset: new AMap.Pixel(3, 22), //设置文本标注偏移量
            content: `<div class='${
              data.completeStatus ? 'task-info-on' : 'task-info-off'
            } task-info'></div>`, //设置文本标注内容
          })
        } else {
          item.setLabel({
            offset: new AMap.Pixel(3, 22), //设置文本标注偏移量
            content: '<div></div>', //设置文本标注内容
          })
        }
      })
      this.maps.setCenter(new AMap.LngLat(Number(data.lng), Number(data.lat)))
      // let markerImg = data.stationTypeId == 2 ? dqImage : data.stationTypeId == 1 ? szImage : data.stationTypeId == 3 ? cyImage : xgImage
      // let markerActiveImg = data.stationTypeId == 2 ? dqActiveImage : data.stationTypeId == 1 ? szActiveImage: data.stationTypeId == 3 ? cyActiveImage : xgActiveImage
      this.currMarker = marker
      this.$emit('markerClick', this.currMarker)
      // if(this.currMarker != marker) {
      //     // marker.setIcon(markerActiveImg)
      //     // if (this.currMarker) {
      //     //     this.cleatSelected(this.currMarker)
      //     // }
      //     this.currMarker = marker
      //     this.$emit('markerClick', this.currMarker)
      // } else {
      //     // marker.setIcon(markerImg)
      //     // this.currMarker = null
      //     // this.$emit('markerClick', this.currMarker)
      // }
    },
    // cleatSelected(marker) {
    //     const data = marker.w.data
    //     let markerImg = data.stationTypeId == 2 ? dqImage : data.stationTypeId == 1 ? szImage : data.stationTypeId == 3 ? cyImage : xgImage
    //     marker.setIcon(markerImg)
    // },
    // 初始化地图
    initMap() {
      const map = new AMap.Map(this.$refs.mapContainer, {
        center: [104.05, 30.73],
        position: [104.05, 30.73],
        zoom: 18,
        zooms: [12, 18],
        viewMode: '3D',
        zoomEnable: true,
        dragEnable: true,
        pitch: 40,
      })
      // 设置地图样式
      map.setMapStyle(this.mapStyle)
      AMap.event.addListener(map, 'click', this.handleBackDepartment)
      this.maps = map
      // 处理webgl上下文丢失事件
      webglcontextlostHandle.call(this)
      // 添加街道划分区域地图数据
      this.creatGeojson(map)
    },
    // 添加街道划分区域地图数据
    creatGeojson(map) {
      // eslint-disable-next-line @typescript-eslint/no-this-alias
      const that = this
      // 添加金牛区地理信息数据 1
      const geojson = new AMap.GeoJSON({
        geoJSON: jinniuStreet,
        getPolygon: function (geojson, lnglats) {
          AMap.convertFrom(
            geojson.geometry.coordinates[0],
            'gps',
            function (status, result) {
              // const area: any = AMap.GeometryUtil.ringArea(lnglats[0]);
              if (geojson.properties.name !== '金牛区') {
                const text = new AMap.Text({
                  text: geojson.properties.name,
                  anchor: 'center', // 设置文本标记锚点
                  draggable: false,
                  cursor: 'pointer',
                  angle: 0,
                  style: {
                    padding: '.75rem 1.25rem',
                    'margin-bottom': '1rem',
                    'border-radius': '.25rem',
                    'background-color': 'transparent',
                    'border-width': 0,
                    'text-align': 'center',
                    'font-size': '14px',
                    color: '#36E9EF',
                  },
                  position: [
                    geojson.properties.center.lng,
                    geojson.properties.center.lat,
                  ],
                })
                text.setMap(map)
              }

              if (result.info === 'ok') {
                const polygon = new AMap.Polygon({
                  path: result.locations,
                  // strokeColor: "#0ea9f9",
                  strokeColor: '#2fbeb5',
                  strokeWeight: 2,
                  strokeOpacity: 1,
                  fillOpacity: 0.2, // 多边形填充透明度
                  fillColor: 'rgba(0,49,113, 0.15)',
                  // that.enterType === EnterType.AIR
                  //   ? that.mapColor
                  //   : "rgba(0,49,113, 0.15)",
                  zIndex: 10,
                })
                if (that.currMarker) {
                  AMap.event.addListener(
                    polygon,
                    'click',
                    that.handleBackDepartment
                  )
                }
                map.add(polygon)
              }
            }
          )
        },
      })

      // 添加金牛区地理信息数据 2
      geojson.setMap(map)
    },
    handleBackDepartment() {
      this.markerList.forEach((item) => {
        item.setLabel({
          offset: new AMap.Pixel(3, 22), //设置文本标注偏移量
          content: '<div></div>', //设置文本标注内容
        })
      })
      this.currMarker = null
      this.$emit('markerClick', this.currMarker)
    },
  },
}
</script>
