<style lang="less" scoped>
p {
    margin-bottom: 0 !important;
}
.container {
    position: relative;
    width: 100%;
    padding: 0;
    box-sizing: border-box;
    .center-map {
        position: absolute;
        height: calc(1080px - 0.94rem);
        width: 100%;
        > div {
            width: 100%;
            height: 100%;
        }
    }
    .container-bg {
        overflow: hidden;
        position: absolute;
        pointer-events: none;
        z-index: 2;
        height: 100%;
        width: 1920px;
        background-size: 100% 100%;
        background-image: url("../../assets/emergency/<EMAIL>");
        .title {
            font-size: 0.19rem;
            line-height: 0.19rem;
            font-family: Source Han Sans CN;
            font-weight: 500;
            text-shadow: 0 0 5px blue, 0 0 5px blue;
        }
        .emergency-command-bottom {
            // overflow: hidden;
            transition: 1.5s;
            position: absolute;
            bottom: 0.3rem;
            left: 0.43rem;
            z-index: 3;
            /*pointer-events: auto;*/
            // height: 5rem;
            /*height: 6.4rem;*/
            width: 14rem;
            // width: 100%;
            // background-image: url("../../assets/bottomBg.png");
            // background-size: 100% 100%;
            /*background: linear-gradient(rgba(5, 23, 54, 0.1), rgba(5, 23, 54, 1));*/
            /*padding-left: 0.71rem;*/
            .procress {
                .procress-name {
                    pointer-events: auto;
                    background-image: url("../../assets/emergency/anniubg.png");
                    background-size: 100% 100%;
                    width: 0.96rem;
                    height: 0.4rem;
                    font-size: 0.16rem;
                    font-weight: 400;
                    color: rgba(165, 248, 255, 1);
                    text-align: center;
                    line-height: 0.4rem;
                }
                .procress-area {
                    display: flex;
                    position: relative;
                    .taskAlert {
                        position: absolute;
                        pointer-events: auto;
                        width: 2.68rem;
                        height: 2.05rem;
                        /*background: rgba(8, 45, 120, 0.8);*/
                        background-image: url("../../assets/emergency/<EMAIL>");
                        background-size: 100% 100%;
                        top: -1.8rem;
                        z-index: 999999;
                        padding: .56rem 0.47rem 0.84rem 0.32rem;
                        .replyContent {
                            padding-left: 0.22rem;
                            width: 100%;
                            /*display: -webkit-box;*/
                            /*-webkit-line-clamp: 2;*/
                            /*-webkit-box-orient: vertical;*/
                            /*overflow: hidden;*/
                            color: #F2F9FF;
                            font-size: 0.14rem;
                            line-height: 0.17rem;
                            position: relative;
                            &::before {
                                width: 12px;
                                height: 12px;
                                content: '';
                                left: 0;
                                background: #00CFB9;
                                border-radius: 50%;
                                position: absolute;
                            }
                            .btn {
                                font-size: 0.14rem;
                                font-weight: 400;
                                color: #00EAFF;
                                float: right;
                                cursor: pointer;
                            }
                        }
                        .replyContent1 {
                            &::before {
                                background: #89C34A;
                            }
                        }
                        .replyContent2 {
                            &::before {
                                background: #04B9EE;
                            }
                        }
                        .replyContent3 {
                            &::before {
                                background: #E6A33B;
                            }
                        }
                        .replyContent4 {
                            &::before {
                                background: #153F9F;
                            }
                        }
                        .creatTime {
                            margin-top: 0.2rem;
                            padding-left: 0.22rem;
                            font-size: 0.14rem;
                            color: #DCF0FF;
                        }
                    }
                    .department-list {
                        pointer-events: auto;
                        width: 1.8rem;
                        display: flex;
                        flex-direction: column;
                        justify-content: flex-end;
                        >div {
                            cursor: pointer;
                            margin-top: 0.1rem;
                            display: flex;
                            align-items: center;
                            justify-content: flex-end;
                            span {
                                position: relative;
                                &:first-child {
                                    display: inline-block;
                                    font-size: 0.12rem;
                                    line-height: 0.12rem;
                                    font-weight: 500;
                                    color: #CEE1F0;
                                }
                                &:nth-child(2) {
                                    margin-left: 0.15rem;
                                    width: 13px;
                                    height: 13px;
                                }
                            }
                            &:nth-child(1) {
                                span {
                                    &:nth-child(2) {
                                        border: 1px solid #89C34A;
                                        border-radius: 50%;
                                    }
                                }
                                .department-active {
                                    background: rgba(72,97,128, .6);
                                    border-radius: 3px;
                                    padding: 0.08rem 0.12rem;
                                }
                                .depart-circle-active {
                                    &:after {
                                        position: absolute;
                                        content: '';
                                        width: 0.07rem;
                                        height: 0.07rem;
                                        background: #89C34A;
                                        border-radius: 50%;
                                        top: 0.02rem;
                                        left: 0.02rem;
                                    }
                                }
                            }
                            &:nth-child(2) {
                                span {
                                    &:nth-child(2) {
                                        border: 1px solid #2980FF;
                                        border-radius: 50%;
                                    }
                                }
                                .department-active {
                                    background: rgba(72,97,128, .6);
                                    border-radius: 3px;
                                    padding: 0.08rem 0.12rem;
                                }
                                .depart-circle-active {
                                    &:after {
                                        position: absolute;
                                        content: '';
                                        width: 0.07rem;
                                        height: 0.07rem;
                                        background: #2980FF;
                                        border-radius: 50%;
                                        top: 0.02rem;
                                        left: 0.02rem;
                                    }
                                }
                            }
                            &:nth-child(3) {
                                span {
                                    &:nth-child(2) {
                                        border: 1px solid #EAA03F;
                                        border-radius: 50%;
                                    }
                                }
                                .department-active {
                                    background: rgba(72,97,128, .6);
                                    border-radius: 3px;
                                    padding: 0.08rem 0.12rem;
                                }
                                .depart-circle-active {
                                    &:after {
                                        position: absolute;
                                        content: '';
                                        width: 0.07rem;
                                        height: 0.07rem;
                                        background: #EAA03F;
                                        border-radius: 50%;
                                        top: 0.02rem;
                                        left: 0.02rem;
                                    }
                                }
                            }
                            &:nth-child(4) {
                                span {
                                    &:nth-child(2) {
                                        border: 1px solid #083295;
                                        border-radius: 50%;
                                    }
                                }
                                .department-active {
                                    background: rgba(72,97,128, .6);
                                    border-radius: 3px;
                                    padding: 0.08rem 0.12rem;
                                }
                                .depart-circle-active {
                                    &:after {
                                        position: absolute;
                                        content: '';
                                        width: 0.07rem;
                                        height: 0.07rem;
                                        background: #083295;
                                        border-radius: 50%;
                                        top: 0.02rem;
                                        left: 0.02rem;
                                    }
                                }
                            }
                        }
                    }
                    .timeLine-text-line {
                        margin-left: 0.1rem;
                        width: calc(100% - 1.9rem);
                        overflow: hidden;
                        .line-one {
                            height: 2px;
                            background-color: rgba(255,255,255,.4);
                            width: 100%;
                            position: absolute;
                            top: 0rem;
                            &:nth-child(3) {
                              top: 0.38rem;
                              background: linear-gradient(to right, #ccc, #ccc 5px, transparent 5px, transparent);
                              background-size: 0.1rem 100%;
                            }
                            &:nth-child(4) {
                              top: 0.76rem;
                              background: linear-gradient(to right, #ccc, #ccc 5px, transparent 5px, transparent);
                              background-size: 0.1rem 100%;
                            }
                            &:nth-child(5) {
                              top: 1.12rem;
                              background: linear-gradient(to right, #ccc, #ccc 5px, transparent 5px, transparent);
                              background-size: 0.1rem 100%;
                            }
                        }

                        .line-scrool {
                            background: transparent;
                            width: 100%;
                            position: absolute;
                            top: 0;
                            height: 1.2rem;
                        }
                        ul {
                            padding: 0;
                        }
                        #times {
                            width: 100%;
                            height: 0rem;
                            font-size: 0.1rem;
                            color: #6aa1cb;
                            -moz-user-select: none;
                            /*火狐*/
                            -webkit-user-select: none;
                            /*webkit浏览器*/
                            -ms-user-select: none;
                            /*IE10*/
                            -khtml-user-select: none;
                            /*早期浏览器*/
                            user-select: none;
                        }
                        #time {
                            height: 0.02rem;
                            position: relative;
                            cursor: pointer;
                            top: 0rem;
                        }

                        #time li {
                            float: left;
                            background: #b4f7fd;
                            height: 6px;
                            width: 1px;
                            position: absolute;
                            list-style: none;
                        }
                    }
                }
            }
            .time-check {
                pointer-events: auto;
                .check-name {
                    background-image: url("../../assets/emergency/anniubg.png");
                    background-size: 100% 100%;
                    width: 0.96rem;
                    height: 0.4rem;
                    font-size: 0.16rem;
                    font-weight: 400;
                    color: rgba(165, 248, 255, 1);
                    text-align: center;
                    line-height: 0.4rem;
                }
                .check-bottom {
                    display: flex;
                    align-items: center;
                    height: 2rem;
                    .check-left {
                        width: 1.4rem;
                        display: flex;
                        flex-direction: column;
                        align-items: flex-end;
                        .check-list {
                            display: flex;
                            flex-direction: column;
                            justify-content: flex-end;
                            align-items: flex-end;
                            cursor: pointer;
                            opacity: 0.24;
                            height: 0.6rem;
                            width: 0.6rem;
                            img {
                                width: 0.41rem;
                                height: 0.41rem;
                            }
                            span {
                                font-size: 0.12rem;
                                font-weight: 500;
                                color: #CEE1F0;
                            }
                        }
                        .check-list-active {
                            opacity: 1;
                        }
                    }
                    .check-right {
                        position: relative;
                        flex: 1;
                        display: flex;
                        flex-direction: column;
                        height: 100%;
                        .right-select {
                            position: absolute;
                            right: 0;
                            top: 0;
                            z-index: 2;
                        }
                        .echarts-area {
                            width: 100%;
                            height: 1.5rem;
                            margin-top: 0.1rem;
                        }
                        .btn-area {
                            width: 100%;
                            height: 0.25rem;
                            margin-top: 0.05rem;
                            padding-left: 0.6rem;
                        }
                        .type-radios {
                            .water-tabs {
                                display: flex;
                                align-items: center;
                            }
                            .water-tab {
                                height: 0.24rem;
                                font-size: 0.12rem;
                                color: rgba(255, 255, 255, 1);
                                background: rgba(12, 39, 92, 1);
                                border-radius: 0;
                                padding: 0 0.1rem;
                                border: none;
                            }
                            .ant-radio-group-solid
                            .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
                                background: #0084ff;
                                border: none;
                            }
                            .ant-radio-button-wrapper:not(:first-child)::before {
                                background: transparent;
                            }
                            .ant-radio-button-wrapper-checked::before {
                                background: transparent !important;
                            }
                            .ant-radio-button-wrapper-checked {
                                z-index: 1;
                                border-color: #0084ff !important;
                                -webkit-box-shadow: -1px 0 0 0 #0084ff;
                                box-shadow: -1px 0 0 0 #0084ff;
                            }
                            .ant-radio-group-small .ant-radio-button-wrapper {
                                /*width: 25%;*/
                                flex: 1;
                                text-align: center;
                            }
                            .ant-radio-group {
                                display: flex;
                            }
                        }
                    }
                }
            }
            .content {
                width: 13.5rem;
                height: 1.3rem;
                .line-five {
                    height: 5.5rem;
                    background-color: #ffd200;
                    width: 0.02rem;
                    position: absolute;
                    top: 0rem;
                    z-index: 9999;
                    right: 0.33rem;
                }
                .taskAlert {
                    position: absolute;
                    width: 1.86rem;
                    height: auto;
                    background: rgba(8, 45, 120, 0.8);
                    top: 0.5rem;
                    pointer-events: auto;
                    z-index: 999999;
                    padding: 0.1rem;
                    > div:nth-of-type(2) {
                        display: flex;
                        span {
                            display: block;
                            width: 0.1rem;
                            height: 0.1rem;
                            background: rgba(255, 210, 0, 1);
                            border-radius: 50%;
                            margin-top: 0.05rem;
                            margin-right: 0.05rem;
                        }
                        div {
                            flex: 1;
                            // overflow: hidden;
                            // text-overflow: ellipsis;
                            // display: -webkit-box;
                            // -webkit-box-orient: vertical;
                            // -webkit-line-clamp: 2;
                            font-size: 0.16rem;
                        }
                    }
                    span {
                        display: block;
                        width: 0.1rem;
                        height: 0.1rem;
                        background: rgba(255, 210, 0, 1);
                        border-radius: 50%;
                        margin-top: 0.05rem;
                        margin-right: 0.05rem;
                    }
                    div {
                        flex: 1;
                        // overflow: hidden;
                        // text-overflow: ellipsis;
                        // display: -webkit-box;
                        // -webkit-box-orient: vertical;
                        // -webkit-line-clamp: 2;
                        font-size: 0.16rem;
                    }
                }
                // .taskAlert:after {
                //   position: absolute;
                //   content: "";
                //   right: 0;
                //   width: 0;
                //   height: 0;
                //   border-style: solid;
                //   border-width: 0.075rem 0 0.075rem 0.13rem;
                //   border-color: transparent transparent transparent #007bff;
                // }
            }
            .charts-div {
                overflow: hidden;
                display: flex;
                justify-content: space-between;
                width: 13.5rem;
                .charts-img {
                    width: 0.72rem;
                    height: 0.72rem;
                }
                .charts-btn {
                    background-image: url("../../assets/emergency/anniubg.png");
                    background-size: 100% 100%;
                    width: 1.04rem;
                    height: 0.46rem;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    font-size: 0.16rem;
                    font-family: Source Han Sans SC;
                    font-weight: 400;
                    color: rgba(165, 248, 255, 1);
                }
                .img-one {
                    margin-top: 0.15rem;
                }
                .img-two {
                    margin-top: 0.65rem;
                    margin-bottom: 0.5rem;
                }
                .charts-left {
                    > div {
                        text-align: center;
                    }
                }
                .charts-right {
                    flex: 1;
                    .charts-title {
                        width: 3.1rem;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        border-bottom: 1px solid rgba(62, 193, 239, 1);
                        padding-bottom: 0.05rem;
                        font-size: 0.16rem;
                        font-family: Source Han Sans SC;
                        font-weight: bold;
                        color: rgba(12, 235, 255, 1);
                        margin-left: 0.5rem;
                    }
                    .charts-content {
                        height: 1.15rem;
                    }
                }
            }
            .showImg {
                width: 0.52rem;
                height: 100%;
                position: absolute;
                right: 0;
                top: 0;
            }
        }
        .emergency-command-left {
            transition: 1.5s;
            position: absolute;
            z-index: 2;
            pointer-events: auto;
            left: 0.58rem;
            top: 0.4rem;
            width: 4.5rem;
            height: 3.64rem;
            background-size: 100% 100%;
            background-image: url("../../assets/emergency/tcbj.png");
            box-sizing: border-box;
            padding: 0.28rem 0.3rem 0.28rem 0.45rem;
            .left-title {
                font-size: 0.18rem;
                font-family: YouSheBiaoTiHei;
                font-weight: 400;
                color: #DCF0FF;
                line-height: 0.27rem;
                padding-left: 0.2rem;
                width: 2.5rem;
                overflow: hidden;
                text-overflow:ellipsis;
                white-space: nowrap;
            }
            .left-select {
                position: absolute;
                right: 0.1rem;
                top: .1rem;
            }
            .left-list {
                display: flex;
                align-items: center;
                margin-top: 0.16rem;
                span {
                    display: inline-block;
                    &:nth-child(1) {
                        width: 0.7rem;
                        font-size: 0.14rem;
                        line-height: 0.14rem;
                        font-weight: 500;
                        color: #5ABEFE;
                    }
                    &:nth-child(2) {
                        font-size: 0.14rem;
                        line-height: 0.14rem;
                        font-weight: 400;
                        color: #DCF0FF;
                        width: calc(100% - 0.7rem);
                        overflow: hidden;
                        text-overflow:ellipsis;
                        white-space: nowrap;
                    }
                }
            }
            .left-content {
                background: rgba(7, 24, 102, 0.6);
                .table {
                    width: 100%;
                    .tr {
                        height: 0.32rem;
                        font-size: 0.12rem;
                        font-family: Source Han Sans SC;
                        font-weight: 400;
                        color: rgba(255, 255, 255, 1);
                        display: flex;
                        align-items: center;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                        .table-name {
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            background: #0f245e;
                            width: 0.8rem;
                        }
                        .td {
                            border: 1px solid rgba(22, 179, 255, 1);
                            height: 0.32rem;
                            display: flex;
                            box-sizing: border-box;
                            align-items: center;
                        }
                        > .td:nth-of-type(2) {
                            padding: 0 0.18rem;
                            font-size: 0.14rem;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                            flex: 1;
                        }
                        .table-text {
                            font-size: 0.14rem;
                            color: #f82929;
                            padding: 0 0.18rem;
                            font-weight: bolder;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                        }
                    }
                }
            }
        }
        .emergency-command-right {
            transition: 1.5s;
            position: absolute;
            z-index: 2;
            pointer-events: auto;
            right: 0.44rem;
            top: 0.36rem;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            height: calc(100% - 0.6rem);
            .right-one-content,
            .right-two-content,
            .right-three-content {
                margin-top: 0.24rem;
                width: 3.87rem;
                height: 2.18rem;
                padding: 0.16rem 0.18rem;
                background-image: url("../../assets/heavily/<EMAIL>");
                background-size: 100% 100%;
            }
            .right-one-content {
            }
            .right-two-content {
                position: relative;
                .right-two-content-one {
                    z-index: 99;
                    position: absolute;
                    padding-left: 0.07rem;
                    left: 0.1rem;
                    top: 0.5rem;
                    width: 2.1rem;
                    height: 0.54rem;
                    background-image: url("../../assets/emergency/bgs1.png");
                    background-size: 100% 100%;
                    font-style: italic;
                    font-size: 0.14rem;
                    display: flex;
                    align-items: center;
                    justify-content: flex-start;
                    span {
                        font-size: 0.23rem;
                    }
                }
                .right-two-content-two {
                    z-index: 99;
                    position: absolute;
                    padding-right: 0.07rem;
                    right: 0.1rem;
                    top: 0.5rem;
                    width: 2.1rem;
                    height: 0.54rem;
                    background-image: url("../../assets/emergency/bgs.png");
                    background-size: 100% 100%;
                    font-style: italic;
                    font-size: 0.14rem;
                    display: flex;
                    align-items: center;
                    justify-content: flex-end;
                    span {
                        font-size: 0.23rem;
                    }
                }
            }
            .right-three-content {
                position: relative;
                .video-right {
                    position: absolute;
                    right: 0.25rem;
                    top: 0.2rem;
                    z-index: 9999;
                    .video-right-top {
                        width: 0.7rem;
                        height: 0.35rem;
                        border-radius: 2px;
                        background: rgba(255, 255, 255, 1);
                        border: 1px solid rgba(0, 234, 255, 1);
                    }
                    .swiper-video {
                        height: 0.8rem;
                    }
                }
            }
            .sub-title {
                width: 3.87rem;
                .title-img {
                    width: 100%;
                }
            }
        }
        .transform-left {
            transform: scale(0); // translateX(-4rem)
            transition: 1.5s;
            opacity: 0;
            visibility: hidden;
        }
        .transform-right {
            transform: scale(0); // translateX(4rem)
            transition: 1.5s;
            opacity: 0;
            visibility: hidden;
        }
        .transform-bottom {
            transform: scale(0); // translateY(4rem)
            transition: 1.5s;
            opacity: 0;
            visibility: hidden;
        }
        .transform-div {
            width: 5.8rem !important;
            transition: 1.5s;
        }
        .transform-div1 {
            width: 4.8rem !important;
            transition: 1.5s;
        }
        .title-top {
            display: flex;
            align-items: center;
            justify-content: space-between;
            > div {
                display: flex;
                align-items: center;
                > :nth-of-type(2) {
                    margin-left: 0.1rem;
                }
            }
        }
    }
    .change-camera {
        position: absolute;
        top: 0.1rem;
        right: 0.1rem;
        width: 0.8rem;
        height: 0.24rem;
        background: rgba(13, 29, 74, 0.7);
        border: 1px solid rgba(32, 103, 224, 1);
        border-radius: 0.04rem;
        display: flex;
        align-items: center;
        color: #ccc;
        justify-content: space-around;
        cursor: pointer;
    }
    .change-camera-selected {
        text-shadow: 0 0 5px blue, 0 0 5px blue;
        color: #fff;
    }
    .quanpin {
        position: absolute;
        top: -0.88rem;
        right: 0.5rem;
        transition: 1.5s;
        z-index: 99;
    }
    .fullscreen {
        width: 100%;
        height: 100%;
    }
}
</style>
<style lang="less">
    .emergency-command-right {
        .right-one-content {
            .video-js.vjs-fluid {
                padding-top: 1.86rem!important;
            }
        }
    }
    .emergency-command {
        .right-arrow,
        .left-arrow {
            text-align: center;
            outline: none;
            cursor: pointer;
            .icons {
                font-size: 0.16rem;
                color: #ffffff;
            }
        }
        .procress-area {
            display: flex;
            position: relative;
            .department-list {
                width: 1.8rem;
                display: flex;
                flex-direction: column;
                justify-content: flex-end;
                padding-bottom: 0.2rem;
                >div {
                    cursor: pointer;
                    margin-top: 0.1rem;
                    display: flex;
                    align-items: center;
                    justify-content: flex-end;
                    height: 0.28rem;
                    span {
                        position: relative;
                        &:first-child {
                            display: inline-block;
                            font-size: 0.12rem;
                            line-height: 0.12rem;
                            font-weight: 500;
                            color: #CEE1F0;
                        }
                        &:nth-child(2) {
                            margin-left: 0.15rem;
                            width: 13px;
                            height: 13px;
                        }
                    }
                    &:nth-child(1) {
                        span {
                            &:nth-child(2) {
                                border: 1px solid #89C34A;
                                border-radius: 50%;
                            }
                        }
                        .department-active {
                            background: rgba(72,97,128, .6);
                            border-radius: 3px;
                            padding: 0.05rem 0.12rem;
                        }
                        .depart-circle-active {
                            &:after {
                                position: absolute;
                                content: '';
                                width: 0.07rem;
                                height: 0.07rem;
                                background: #89C34A;
                                border-radius: 50%;
                                top: 0.02rem;
                                left: 0.02rem;
                            }
                        }
                    }
                    &:nth-child(2) {
                        span {
                            &:nth-child(2) {
                                border: 1px solid #2980FF;
                                border-radius: 50%;
                            }
                        }
                        .department-active {
                            background: rgba(72,97,128, .6);
                            border-radius: 3px;
                            padding: 0.05rem 0.12rem;
                        }
                        .depart-circle-active {
                            &:after {
                                position: absolute;
                                content: '';
                                width: 0.07rem;
                                height: 0.07rem;
                                background: #2980FF;
                                border-radius: 50%;
                                top: 0.02rem;
                                left: 0.02rem;
                            }
                        }
                    }
                    &:nth-child(3) {
                        span {
                            &:nth-child(2) {
                                border: 1px solid #EAA03F;
                                border-radius: 50%;
                            }
                        }
                        .department-active {
                            background: rgba(72,97,128, .6);
                            border-radius: 3px;
                            padding: 0.05rem 0.12rem;
                        }
                        .depart-circle-active {
                            &:after {
                                position: absolute;
                                content: '';
                                width: 0.07rem;
                                height: 0.07rem;
                                background: #E6A33B;
                                border-radius: 50%;
                                top: 0.02rem;
                                left: 0.02rem;
                            }
                        }
                    }
                    &:nth-child(4) {
                        span {
                            &:nth-child(2) {
                                border: 1px solid #083295;
                                border-radius: 50%;
                            }
                        }
                        .department-active {
                            background: rgba(72,97,128, .6);
                            border-radius: 3px;
                            padding: 0.05rem 0.12rem;
                        }
                        .depart-circle-active {
                            &:after {
                                position: absolute;
                                content: '';
                                width: 0.07rem;
                                height: 0.07rem;
                                background: #153F9F;
                                border-radius: 50%;
                                top: 0.02rem;
                                left: 0.02rem;
                            }
                        }
                    }
                }
            }
            .timeLine-text-line {
                margin-left: 0.1rem;
                width: calc(100% - 1.9rem);
                overflow: hidden;
                .line-one {
                    height: 2px;
                    background-color: rgba(255,255,255,.4);
                    width: 100%;
                    position: absolute;
                    top: 0.36rem;
                }

                .line-scrool {
                    height: 0.4rem;
                    background: transparent;
                    width: 100%;
                    position: absolute;
                }
                ul {
                    padding: 0;
                }
                .tttt {
                    height: 0rem;
                    cursor: move;
                    position: relative;
                    pointer-events: auto;
                }
                #times {
                    width: 100%;
                    height: 0rem;
                    margin-top: 1.36rem;
                    font-size: 0.1rem;
                    color: #6aa1cb;
                    -moz-user-select: none;
                    /*火狐*/
                    -webkit-user-select: none;
                    /*webkit浏览器*/
                    -ms-user-select: none;
                    /*IE10*/
                    -khtml-user-select: none;
                    /*早期浏览器*/
                    user-select: none;
                }
                #time {
                    height: 0.02rem;
                    position: relative;
                    cursor: pointer;
                    top:0rem;
                }

                #time li {
                    float: left;
                    position: absolute;
                    list-style: none;
                    /*background: rgba(255, 255, 255, 0.4);*/
                    /*height: 0.06rem;*/
                    /*width: 0.01rem;*/
                    top: 0.2rem;
                    text-indent: -0.12rem;
                }
                #time > .time1 {
                    width: 0.12rem;
                    height: 0.12rem;
                    background: linear-gradient(
                            0deg,
                            rgba(0, 150, 81, 1),
                            rgba(16, 162, 95, 1),
                            rgba(0, 208, 112, 1)
                    );
                    border-radius: 50%;
                    z-index: 10;
                    position: absolute;
                    z-index: 99999;
                }
                #time > .time2 {
                    background: #89C34A;
                }
                #time > .time3 {
                    background: #04B9EE;
                }
                #time > .time4 {
                    background: #E6A33B;
                }
                #time > .time5 {
                    background: #153F9F;
                }
                #time > .time1-active {
                    width: 0.14rem;
                    height: 0.14rem;
                    background: #00CFB9;
                    border-radius: 50%;
                    z-index: 9999991;
                    position: absolute;
                }
                .time-active1 .time2 {
                    background: #89C34A!important;
                    top: 0rem;
                }
                .time-active2 .time3 {
                    background: #04B9EE!important;
                }
                .time-active3 .time4{
                    background: #E6A33B!important;
                }
                .time-active4 .time5 {
                    background: #153F9F!important;
                }
            }
        }
        .content {
        }
        .video-js.vjs-fluid {
            height: 100% !important;
        }
        .vjs-poster {
            background-size: 100% 100%;
        }
        .vjs-big-play-button {
            border: none !important;
            background-color: transparent !important;
            border-radius: 0.3em;
            outline: none;
        }
        .title-select-water {
            display: flex;
            justify-content: space-between;
            width: 1.08rem;
            .ant-select-selection {
                display: flex;
                justify-content: center;
                width: 1.08rem;
                height: 0.25rem;
                font-size: 0.14rem;
                border: none;
                border-radius: unset;
                background: url(../../assets/<EMAIL>);
                background-size: 100% 100%;
                background-repeat: no-repeat;
            }
            .ant-select-selection-selected-value {
                color: rgba(0, 234, 255, 1);
                font-size: 0.14rem;
            }

            .ant-select-focused .ant-select-selection,
            .ant-select-selection:focus,
            .ant-select-selection:active {
                border-color: #40a9ff;
                border-right-width: 0 !important;
                outline: 0;
                box-shadow: none;
                font-size: 0.14rem;
            }
        }
        .title-select-range {
            width: 1.2rem;
            .ant-select-selection {
                width: 1.2rem;
                background-image: url("../../assets/emergency/jlsx.png") !important;
            }
            .ant-select-selection__rendered {
                margin-left: 0.25rem!important;
            }
        }
        .water-monitor-tab {
            height: 0.24rem;
            font-size: 0.12rem;
            color: rgba(255, 255, 255, 1);
            background: #0c275c;
            border-radius: 0;
            padding: 0 0.1rem;
            border: none;
        }
        .ant-radio-group-solid
        .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
            background: #0c275c;
            color: rgba(146, 145, 255, 1);
            border: none;
        }
        .ant-radio-button-wrapper:not(:first-child)::before {
            background: transparent;
        }
        .ant-radio-button-wrapper-checked::before {
            background: transparent !important;
        }
        .ant-radio-button-wrapper-checked {
            z-index: 1;
            border-color: #0c275c !important;
            box-shadow: -1px 0 0 0 #0c275c;
            color: rgba(146, 145, 255, 1);
        }
        .ant-radio-group {
            display: flex;
            justify-content: space-around;
            background: #0c275c;
        }
        .ant-radio-group-small .ant-radio-button-wrapper {
            padding: 0;
        }
        .ant-select-selection__rendered {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
        .ant-select-dropdown {
            margin: 0;
            padding: 0;
            color: #8eb3f6;
            font-variant: tabular-nums;
            line-height: 1.5;
            list-style: none;
            -webkit-font-feature-settings: "tnum";
            font-feature-settings: "tnum";
            position: absolute;
            top: -9999px;
            left: -9999px;
            z-index: 1050;
            -webkit-box-sizing: border-box;
            box-sizing: border-box;
            font-size: 0.12rem !important;
            font-variant: initial;
            background-color: #012474;
            border-radius: 4px;
            outline: none;
            -webkit-box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }
        .ant-select-dropdown-menu-item {
            position: relative;
            font-size: 0.12rem !important;
            display: block;
            padding: 5px 12px;
            overflow: hidden;
            color: #8eb3f6;
            font-weight: normal;
            line-height: 22px;
            white-space: nowrap;
            text-overflow: ellipsis;
            text-align: center;
            cursor: pointer;
            -webkit-transition: background 0.3s ease;
            transition: background 0.3s ease;
        }
        .select-main {
            width: 1.2rem;
            display: flex;
            justify-content: space-between;
            .ant-select-selection {
                height: 0.3rem;
                width: 100%;
                border: none;
                outline: none;
                border-radius: unset;
                background: url(../../assets/<EMAIL>);
                background-size: 100% 100%;
                background-repeat: no-repeat;
            }
            .ant-select-selection-selected-value {
                color: rgba(0, 234, 255, 1);
            }
            .ant-select-focused .ant-select-selection,
            .ant-select-selection:focus,
            .ant-select-selection:active {
                border: none;
                border-right-width: 0 !important;
                outline: none;
                box-shadow: none;
            }
        }
    }
    .ant-select-dropdown-menu-item {
        font-size: 0.14rem !important;
    }
    .ant-select-dropdown-custom-menu-item {
        font-size: 0.14rem !important;
    }
    .ant-modal-task {
        top: 1.6rem !important;
        width: 5.63rem !important;
        .ant-modal-content {
            width: 5.63rem;
            min-height: 5.8rem;
            background-size: 100% 100%;
            background-image: url("../../assets/emergency/<EMAIL>");
            background-color: transparent!important;
        }
        .ant-modal-body {
            height: 100%;
            min-height: 5.8rem;
            padding: 0.35rem 0.24rem;
            .content {
                padding-left: 0.16rem;
                padding-top: 25px;
                .title {
                    width: 3.04rem;
                    height: 0.36rem;
                    background: linear-gradient(90deg, rgba(9,70,148, .6), rgba(38, 47, 78, 0.6));
                    font-size: 0.16rem;
                    font-weight: 400;
                    color: #FAFDFF;
                    line-height: 0.36rem;
                    padding-left: 0.2rem;
                    padding-top: 2px;
                    border-left: 0.02rem solid #308EC1;
                }
                .close {
                    img {
                        position: absolute;
                        right: 53px !important;
                        cursor: pointer;
                        top: 76px !important;
                    }
                }
                .txt {
                    height: 100%;
                    width: 100%;
                    padding: 0.3rem 0.5rem 0.3rem 0.3rem;
                    .department-name {
                        font-size: 0.18rem;
                        font-weight: 500;
                        color: #5ABEFE;
                        line-height: 0.18rem;
                        padding-left: 0.2rem;
                        position: relative;
                        &::before {
                            content: '';
                            position: absolute;
                            width: 0.1rem;
                            height: 0.1rem;
                            left: 0;
                            top: 0.04rem;
                            background-color: #00CFB9;
                            border-radius: 50%;
                        }
                    }
                    .department-remark {
                        padding-top: 0.2rem;
                        font-size: 0.16rem;
                        font-weight: 400;
                        color: #DBEFFF;
                        line-height: 0.24rem;
                    }
                    .department-time {
                        display: flex;
                        justify-content: space-between;
                        padding-top: 0.2rem;
                        span {
                            color: #7294B0;
                            font-size: 0.14rem;
                        }
                    }
                    .department-image {
                        margin-top: 0.25rem;
                        height: 2.68rem;
                        width: 100%;
                        background-size: 100% 100%;
                        background-image: url("../../assets/emergency/spjk.png");
                        padding: 0.21rem 0.24rem 0.22rem 0.25rem;
                        box-sizing: border-box;
                        overflow: hidden;
                        .el-image {
                            width: 100%;
                            height: 100%;
                        }
                    }
                }
            }
        }
        .ant-modal-close {
            display: none !important;
        }
        .ant-modal-footer {
            border: none !important;
        }
    }
</style>
<template>
    <section class="container">
        <section class="container-bg emergency-command">
            <!-- 下面（时间轴、图表） -->
            <section
                    class="emergency-command-bottom"
                    :class="{
        'transform-bottom': displayState,
        'transform-div': showDiv
      }"
            >
                <section class="procress">
                    <div class="procress-name">任务进度</div>
                    <div class="procress-area">
                        <div class="taskAlert" v-show="taskDetailState">
                            <div :title="replyContent ? replyContent : ''" class="replyContent"
                                 :class="{'replyContent1': initIndex == 0 ? 'replyContent1' : '',
                                 'replyContent2': initIndex == 1 ? 'replyContent2' : '',
                                 'replyContent3': initIndex == 2 ? 'replyContent3' : '',
                                 'replyContent4': initIndex == 3 ? 'replyContent4' : ''}">
                                {{ replyContent ? (replyContent.length > 12 ? replyContent.substr(0, 17) + '...' : replyContent) : "" }}
                                <div v-show="(replyContent && replyContent.length > 12) || taskAnnexList.length > 0" class="btn" @click="hanldeDetail()">详情</div>
                            </div>
                            <div class="creatTime">{{ creatTime }}</div>
                        </div>
                        <div class="department-list">
                            <div v-for="(item, index) in departmentList" :key="index" @click="handleDepartment(item)">
                                <span class="depart-name" :class="{'department-active': departmentStatus[item.type] ? 'department-active' : ''}">{{item.name}}</span>
                                <span class="depart-circle" :class="{'depart-circle-active': departmentStatus[item.type] ? 'depart-circle-active' : ''}"></span>
                            </div>
                        </div>
                        <div class="timeLine-text-line">
                            <!-- <div id="times" :style="{marginTop: (0.24+ 0.38 * (departmentList.length -1)) + 'rem'}"> -->
                            <div id="times" style="margin-top: 0.24rem">
                                <div class="tttt">
                                    <ul id="time">
                                    </ul>
                                    <div class="line-one" v-for="(item, index) in departmentList" :key="index"></div>
                                    <div class="line-scrool"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
                <section class="time-check">
                    <div class="check-name">实时监测</div>
                    <section class="check-bottom">
                        <div class="check-left">
                            <div class="check-list" :class="{'check-list-active': isShow == 2 ? 'check-list-active' : ''}" @click="changeType(2)">
                                <img class="charts-img" src="../../assets/emergency/shui.png" />
                                <span>水质监测</span>
                            </div>
                            <div class="check-list" :class="{'check-list-active': isShow == 1 ? 'check-list-active' : ''}" @click="changeType(1)">
                                <img class="charts-img" src="../../assets/emergency/kongqi.png" />
                                <span>空气监测</span>
                            </div>
                            <div class="check-list" :class="{'check-list-active': isShow == 3 ? 'check-list-active' : ''}" @click="changeType(3)">
                                <img class="charts-img" src="../../assets/emergency/zhongwu.png" />
                                <span>重污监测</span>
                            </div>
                        </div>
                        <div v-if="isShow == 2" class="check-right">
                            <div class="right-select">
                                <a-select
                                        v-model="defaultWater"
                                        class="title-select-water"
                                        @change="waterChange"
                                >
                                    <a-icon
                                            slot="suffixIcon"
                                            type="caret-down"
                                            style="color:rgba(0, 234, 255, 1);width:14px;height:8px;"
                                    />
                                    <a-select-option
                                            :value="String(index)"
                                            v-for="(item, index) in waterStationList"
                                            :key="index"
                                    >
                                        {{ item.stationName }}</a-select-option
                                    >
                                </a-select>
                            </div>
                            <div class="echarts-area">
                                <LineEmergencyCommand
                                        :pageX="pageX"
                                        :id="'Command-emergency123'"
                                        :width="'100%'"
                                        :height="'1.5rem'"
                                        :end="end"
                                        :start="start"
                                        :propData="waterMonitor"
                                        :smooth="true"
                                />
                            </div>
                            <div class="btn-area type-radios">
                                <a-radio-group
                                        v-model="defaultwaterType"
                                        size="small"
                                        buttonStyle="solid"
                                        style="width:100%;"
                                        class="water-tabs"
                                        @change="waterTypeChange"
                                >
                                    <a-radio-button
                                            v-for="item in waterTypeList"
                                            :key="item.itemCode"
                                            :value="item.itemCode"
                                            class="water-tab"
                                    >{{ item.name }}</a-radio-button
                                    >
                                </a-radio-group>
                            </div>
                        </div>
                        <div v-if="isShow == 1" class="check-right">
                            <div class="right-select">
                                <a-select
                                        v-model="defaultAir"
                                        class="title-select-water"
                                        @change="airChange"
                                >
                                    <a-icon
                                            slot="suffixIcon"
                                            type="caret-down"
                                            style="color:rgba(0, 234, 255, 1);width:14px;height:8px;"
                                    />
                                    <a-select-option
                                            :value="String(index)"
                                            v-for="(item, index) in airStationList"
                                            :key="index"
                                    >
                                        {{ item.positionName }}</a-select-option
                                    >
                                </a-select>
                            </div>
                            <div class="echarts-area">
                                <LineEmergencyCommand
                                        :pageX="pageX"
                                        :id="'Command-emergency456'"
                                        :width="'100%'"
                                        :height="'1.5rem'"
                                        :propData="airData"
                                        :end="end"
                                        :start="start"
                                        :smooth="true"
                                />
                            </div>
                            <div class="btn-area type-radios">
                                <a-radio-group
                                        v-model="airType"
                                        size="small"
                                        buttonStyle="solid"
                                        class="water-tabs"
                                        style="width:100%;"
                                        @change="airTypeChange"
                                >
                                    <a-radio-button
                                            v-for="item in airTypeLIst"
                                            :key="item.code"
                                            :value="item.code"
                                            class="water-tab"
                                    >{{ item.name }}</a-radio-button
                                    >
                                </a-radio-group>
                            </div>
                        </div>
                        <div v-if="isShow == 3" class="check-right">
                            <div class="right-select">
                                <a-select
                                        v-model="defaultPollution"
                                        class="title-select-water"
                                        @change="pollutionChange"
                                >
                                    <a-icon
                                            slot="suffixIcon"
                                            type="caret-down"
                                            style="color:rgba(0, 234, 255, 1);width:14px;height:8px;"
                                    />
                                    <a-select-option
                                            :value="String(index)"
                                            v-for="(item, index) in pollutionList"
                                            :key="index"
                                    >
                                        {{ item.companyName }}</a-select-option
                                    >
                                </a-select>
                            </div>
                            <div class="echarts-area">
                                <LineEmergencyCommand
                                        :pageX="pageX"
                                        :id="'Command-emergency789'"
                                        :width="'100%'"
                                        :height="'1.5rem'"
                                        :end="end"
                                        :start="start"
                                        :propData="pollutionData"
                                        :smooth="true"
                                />
                            </div>
                            <div class="btn-area type-radios">
                                <a-radio-group
                                        default-value="'PM₁₀'"
                                        size="small"
                                        buttonStyle="solid"
                                        style="width:100%;"
                                        class="water-tabs"
                                        @change="airTypeChange"
                                >
                                    <a-radio-button value="'PM₁₀'"  class="water-tab"
                                    >PM₁₀</a-radio-button
                                    >
                                </a-radio-group>
                            </div>
                        </div>
                    </section>
                </section>
            </section>
            <!-- 左侧（事件概况） -->
            <section
                    class="emergency-command-left"
                    :class="{ 'transform-left': displayState }"
            >
                <div class="left-title" :title="reportDetails.title">{{reportDetails.title}}</div>
                <div class="left-select">
                    <a-select
                            v-model="defaultRange"
                            class="title-select-water title-select-range"
                            @change="rangeChange"
                    >
                        <a-icon
                                slot="suffixIcon"
                                type="caret-down"
                                style="color:rgba(0, 234, 255, 1);width:14px;height:8px;"
                        />
                        <a-select-option
                                :value="item.value"
                                v-for="(item, index) in rangeList"
                                :key="index"
                                class="ant-select-dropdown-custom-menu-item"
                        >
                            {{ item.name }}</a-select-option
                        >
                    </a-select>
                </div>
                <div class="left-list" style="margin-top: 0.3rem">
                    <span>上报内容：</span>
                    <span :title="reportDetails.content">{{ reportDetails.content ? reportDetails.content : "--" }}</span>
                </div>
                <div class="left-list">
                    <span>事件类型：</span>
                    <span>{{level[reportDetails.emergencyLevel]}}</span>
                </div>
                <div class="left-list">
                    <span>初报时间：</span>
                    <span>{{ reportDetails.createTime ? reportDetails.createTime.substr(0, reportDetails.createTime.length - 3) : "--" }}</span>
                </div>
                <div class="left-list">
                    <span>事件位置：</span>
                    <span :title="reportDetails.address">{{ reportDetails.address ? reportDetails.address : "" }}</span>
                </div>
                <div class="left-list">
                    <span>上报单位：</span>
                    <span :title="reportDetails.reportingDepartment">{{ reportDetails.reportingDepartment ? reportDetails.reportingDepartment : "--" }}</span>
                </div>
                <div class="left-list">
                    <span>协同单位：</span>
                    <span :title="reportDetails.assistDepartment">{{ reportDetails.assistDepartment ? reportDetails.assistDepartment : "--" }}</span>
                </div>
                <div class="left-list">
                    <span>指挥小组：</span>
                    <span :title="reportDetails.commandDepartment">{{ reportDetails.commandDepartment ? reportDetails.commandDepartment : "--" }}</span>
                </div>
                <div class="left-list">
                    <span style="width: 1.1rem">综合协调小组：</span>
                    <span :title="reportDetails.coordinationDepartment" style="width: calc(100% - 1.1rem)">{{ reportDetails.coordinationDepartment ? reportDetails.coordinationDepartment : "--" }}</span>
                </div>
            </section>
            <!-- 右侧（视频监控） -->
            <section
                    class="emergency-command-right"
                    :class="{ 'transform-right': displayState }"
            >
                <!-- 站点监控 -->
                <div class="right-one">
                    <div class="title title-top">
                        <div @click="toRouter('/waterVideo')" style="cursor: pointer;">
                            {{ `站点监控` }}
                        </div>
                        <div>
                            <a-select
                                    v-model="defaultCameraValue"
                                    class="title-select-water"
                                    @change="waterCameraChange"
                            >
                                <a-icon
                                        slot="suffixIcon"
                                        type="caret-down"
                                        style="color:rgba(0, 234, 255, 1);width:14px;height:8px;"
                                />
                                <a-select-option
                                        :value="item.monitorId"
                                        v-for="(item, index) in waterCameraList"
                                        :key="index"
                                >
                                    {{ item.monitorName }}</a-select-option
                                >
                            </a-select>
                        </div>
                    </div>
                    <div class="sub-title">
                        <img
                                src="@/assets/biaoti.png"
                                class="title-img"
                        />
                    </div>
                    <div class="right-one-content">
                        <video-player
                                class="vjs-custom-skin"
                                ref="livePlayer"
                                :playsinline="true"
                                @statechanged="playerStateChanged($event)"
                                :options="playerOptions1"
                        ></video-player>
                    </div>
                </div>
                <!-- 流动监控 -->
                <div class="right-two">
                    <div class="title title-top">
                        <div @click="toRouter('/vehiclesVideo')" style="cursor: pointer;">
                            {{ `流动监控` }}
                        </div>
                        <div>
                            <a-select
                                    v-model="carId"
                                    @change="getCarCamera"
                                    class="select-main"
                                    style="margin-left:0.2rem;width:1.05rem"
                            >
                                <a-icon
                                        slot="suffixIcon"
                                        type="caret-down"
                                        style="color:rgba(0, 234, 255, 1);width:14px;height:8px;"
                                />
                                <a-select-option
                                        :value="item.carId"
                                        v-for="item in carList"
                                        :key="item.carId"
                                >
                                    {{ item.license }}</a-select-option
                                >
                            </a-select>
                        </div>
                    </div>
                    <div class="sub-title">
                        <img
                                src="@/assets/biaoti.png"
                                class="title-img"
                        />
                    </div>
                    <div class="right-two-content">
                        <div
                                v-show="flvPlayer === null"
                                style="background:rgba(8,45,120,0.5);width: 3.51rem;height: 1.86rem;font-size:0.2rem;display: flex;flex-direction: column;align-items: center;justify-content: center;"
                        >
                            <img
                                    src="@/assets/shipin.png"
                                    alt=""
                                    style="width: 1.11rem;height: 1.04rem;margin-bottom:0.1rem;"
                            />
                            该摄像头不在线
                        </div>
                        <video
                                v-show="flvPlayer !== null"
                                id="videoElement"
                                style="width: 3.51rem;height: 1.86rem;background: #000;"
                                controls="true"
                        ></video>
                        <div class="change-camera" v-show="flvPlayer !== null">
            <span
                    :class="cameraDirection == 0 ? 'change-camera-selected' : ''"
                    @click="changeCameraDirection(0)"
            >前</span
            >
                            <img src="@/assets/<EMAIL>" width="2" height="25" />
                            <span
                                    :class="cameraDirection == 1 ? 'change-camera-selected' : ''"
                                    @click="changeCameraDirection(1)"
                            >后</span
                            >
                        </div>
                        <div class="right-two-content-one" v-show="flvPlayer !== null">
                            距离事发地：<span>{{melliageData.distance}}</span>{{melliageData.type ? 'KM' : 'M'}}
                        </div>
                        <div class="right-two-content-two" v-show="flvPlayer !== null">
                            预计<span>{{melliageData.time}}</span>分钟后到达
                        </div>
                    </div>
                </div>
                <!-- 在线指挥 -->
                <div class="right-three">
                    <div class="title">{{ `在线指挥` }}</div>
                    <div class="sub-title">
                        <img
                                src="@/assets/biaoti.png"
                                class="title-img"
                        />
                    </div>
                    <div class="right-three-content">
                       <video-player
                          class="vjs-custom-skin"
                          ref="livePlayers"
                          :playsinline="true"
                          @statechanged="playerStateChanged($event)"
                          :options="playerOptions"
                        ></video-player>
<!--                        <img :src="emActive" style="width:100%;height:100%;" />-->
                        <div class="video-right">
                            <img class="video-right-top" :src="emActive" />
                            <div>
                                <div class="right-arrow">
                                    <a-icon type="up" class="icons" />
                                </div>
                                <swiper :options="swiperVideoOption" class="swiper-video">
                                    <swiper-slide
                                            v-for="(item, index) in emList"
                                            :key="index"
                                            @click="checkEmImg(index)"
                                    >
                                        <img
                                                class="video-right-top"
                                                :src="item"
                                                style="cursor: pointer;"
                                                @click="checkEmImg(index)"
                                        />
                                    </swiper-slide>
                                </swiper>
                                <div class="left-arrow">
                                    <a-icon type="down" class="icons" />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </section>
        <section class="center-map">
            <!-- <keep-alive> -->
            <emergency-map
                    :map-style="mapStyle"
                    :enter-type="8"
                    :map-zoom="13.3"
                    :view-mode="'2D'"
                    :viewCenter="mapViewCenter"
                    :emergency-list="emergencyList"
                    :emergency-station-list="emergencyStationList"
                    :report-details="reportDetails"
                    :rangeObj="rangeObj"
                    :defaultCameraValue="defaultCameraValue"
                    :waterStationId="waterStationId"
                    :airStationId="airStationId"
                    :pollutionStationId="pollutionStationId"
                    :carId="carId"
                    :isShow="isShow"
                    :oldCarPoint="oldCarPoint"
                    :newCarPoint="newCarPoint"
            />
            <!-- </keep-alive> -->
        </section>
        <section class="quanpin" @click="fullScreen">
            <img src="@/assets/quanping.png" alt />
        </section>
        <a-modal
                :visible="visible"
                class="ant-modal-task"
                :footer="null"
                :destroyOnClose="true"
                @cancel="handleClose"
        >
            <div class="content">
                <div class="close" @click="handleClose">
                    <img
                        src="../../assets/guanbi.png"
                        alt=""
                        class="close"
                    />
                </div>
                <div class="title">部门反馈</div>
                <div class="txt">
                    <div class="department-name">{{  remarkDetail.departmentName }}</div>
                    <div class="department-remark">
                        {{ remarkDetail.remark }}
                    </div>
                    <div class="department-time">
                        <span>{{  remarkDetail.departmentName }}-{{ remarkDetail.userName }}</span>
                        <span>{{ remarkDetail.time ? remarkDetail.time.substr(0, remarkDetail.time.length - 3) : '--' }}</span>
                    </div>
                    <div class="department-image" v-show="remarkDetail.taskAnnexList && remarkDetail.taskAnnexList.length">
                    <ComImage
                        v-for="(pic, indexs) in remarkDetail.taskAnnexList"
                        v-show="indexs === 0"
                        :key="indexs"
                        :src="pic.annexUrl"
                        fit="cover"
                        lazy
                        :preview-src-list="remarkDetail.taskAnnexList.map(item => item.annexUrl)"
                      />
                        <!-- <Viewer :urlList="remarkDetail.taskAnnexList.map(item => item.annexUrl)"></Viewer> -->
                        <!-- <img v-show="indexs == 0" class="img" v-for="(item,indexs) in remarkDetail.taskAnnexList" :key="indexs" :src="item.annexUrl" :preview="'item'+0"> -->
                    </div>
                </div>
            </div>
        </a-modal>
    </section>
</template>

<script lang="ts">
/* eslint-disable @typescript-eslint/ban-ts-ignore */
//@ts-ignore
import AMap from "AMap";
import flvjs from "flv.js";
import { Component, Vue, Watch } from "vue-property-decorator";
import EmergencyMap from "@/components/GaoDeMap/emergencyMap.vue";
import ComImage from '@/components/image/src/main.vue'
import {
    Table,
    Icon,
    Empty,
    Avatar,
    Radio,
    Select,
    message,
    Tooltip,
    Modal
} from "ant-design-vue";
import "video.js/dist/video-js.css";
import "videojs-contrib-hls";
import "videojs-flash";
import jq from "jquery";
import { getStationList, fetchCameraUrl, fetchCameraList } from "@/api/water";
import moment from "moment";
import {
    getReportDetails,
    getTaskOverview
} from "@/api/emergency";
import LineEmergencyCommand from "@/components/Charts/LineEmergencyCommand.vue";
import { Swiper, SwiperSlide } from "vue-awesome-swiper";
import { clearInterval, setInterval } from "timers";
import em1 from "@/assets/emergency/em-1.png";
import em11 from "@/assets/emergency/em-1-1.png";
import em2 from "@/assets/emergency/em-2.png";
import em21 from "@/assets/emergency/em-2-1.png";
import em3 from "@/assets/emergency/em-3.png";
import em31 from "@/assets/emergency/em-3-1.png";
import em4 from "@/assets/emergency/em-4.png";
import em41 from "@/assets/emergency/em-4-1.png";
import video1 from '../../assets/video/video1.png'
import video2 from '../../assets/video/video2.png'
import video3 from '../../assets/video/video3.png'
import { socketUrl, socketUrl2 } from "@/utils/index";
@Component({
    name: "EmergencyCommand",
    components: {
        EmergencyMap,
        ATable: Table,
        AIcon: Icon,
        AEmpty: Empty,
        AAvatar: Avatar,
        LineEmergencyCommand,
        ARadioGroup: Radio.Group,
        ARadioButton: Radio.Button,
        Swiper,
        SwiperSlide,
        ASelect: Select,
        ASelectOption: Select.Option,
        ATooltip: Tooltip,
        AModal: Modal,
        ComImage
    }
})
export default class extends Vue {
    @Watch("swiperIndex", { immediate: true, deep: true })
    public onSwiperIndex(newValue: any, oldValue: any) {
        if (newValue && this.emList.length - 1 == newValue) {
            // this.autoSwiperTimer();
        }
    }
    @Watch("left", { immediate: true, deep: true })
    public onLeft(newValue: any, oldValue: any) {
        if (newValue || newValue == 0) {
            let xLength = 0
            if (this.isShow === 1) {
                xLength = this.airData.bottomList.length
            } else if (this.isShow === 2) {
                xLength = this.waterMonitor.bottomList.length
            } else if (this.isShow === 3) {
                xLength = this.pollutionData.bottomList.length
            }
            if (this.initEnd !== 100 && xLength !== 0) {
                const z = (100 / xLength)
                const a = newValue / 60
                this.start = a * z + 0
                this.end = a * z + this.initEnd
            }
            let first = this.totalDistance.findIndex((item:any) => item - newValue > 0)
            let last = this.totalDistance.findIndex((item:any) => item - newValue - jq('#times').width() > 0)
            if ((this.next < first || this.next > last) && first !== -1 && this.move) {
                this.next =  first - 1
            }
        }
    }
    private visible:any = false
    private defaultRange: any = "3";
    private rangeList: any = [
        {
            name: "监测范围:0.5公里",
            value: "0.5"
        },
        {
            name: "监测范围:1公里",
            value: "1"
        },
        {
            name: "监测范围:2公里",
            value: "2"
        },
        {
            name: "监测范围:3公里",
            value: "3"
        },
        {
            name: "监测范围:4公里",
            value: "4"
        }
    ];
    private level:any ={
        1: '一级',
        2: '二级',
        3: '二级',
        4: '二级',
        5: '二级',
    }
    private rangeObj: any = {
        lng: "",
        lat: "",
        number: 0.5
    };
    private rangeChange(val: any) {
        this.defaultRange = val;
        if (val == 0.5) {
            this.rangeObj = {
                lng: this.reportDetails.longitude,
                lat: this.reportDetails.latitude,
                number: 500
            };
        } else if (val == 1) {
            this.rangeObj = {
                lng: this.reportDetails.longitude,
                lat: this.reportDetails.latitude,
                number: 1000
            };
        } else if (val == 2) {
            this.rangeObj = {
                lng: this.reportDetails.longitude,
                lat: this.reportDetails.latitude,
                number: 2000
            };
        } else if (val == 3) {
            this.rangeObj = {
                lng: this.reportDetails.longitude,
                lat: this.reportDetails.latitude,
                number: 3000
            };
        } else if (val == 4) {
            this.rangeObj = {
                lng: this.reportDetails.longitude,
                lat: this.reportDetails.latitude,
                number: 4000
            };
        }
        this.socket1.send(JSON.stringify({ code: 7, lng: this.reportDetails.lng, lat: this.reportDetails.lat, distance: this.defaultRange  }));
    }
    private showDiv: any = false;
    // 水质站点列表
    private waterStationList: Array<any> = [];
    private waterCameraList: Array<any> = [];
    private carList: Array<any> = [];
    // 水质类型列表
    private waterTypeList: any = [];
    // 默认水质类型
    private defaultwaterType: any = "";
    // 水质站点id
    private waterStationId: any = '';
    // 水质下拉框
    private defaultWater: any = '';
    // 获取站点列表
    private getStationList(): void {
        getStationList('').then(res => {
            if (res.data.data) {
                this.waterStationVideoList = res.data.data;
                this.defaultWaterStationValue = res.data.data[0].stationId;
                this.fetchCameraList(this.defaultWaterStationValue);
                // this.defaultwaterType = this.waterTypeList[0].itemCode;
                // this.waterStationId = res.data.data[0].stationId;
                // this.getMonitorItemDetailRecord();
            }
        })
    }
    // 部门选中状态
    private departmentStatus:any = {
        st: 1,
        zf: 1,
        ns: 1,
        jj: 1
    }
    // 水质radio切换
    private waterTypeChange(val: any) {
        this.defaultwaterType = val.target.value;
        this.getMonitorItemDetailRecord();
    }
    // 水质下拉框
    private waterChange(val: any) {
        this.defaultWater = val;
        this.waterStationId = this.waterStationList[val].stationId;
        this.waterTypeList = this.waterStationList[val].monitorItems;
        if (this.waterTypeList && this.waterTypeList.length) {
            this.defaultwaterType = this.waterTypeList[0].itemCode
        }
        this.getMonitorItemDetailRecord();
    }
    // 空气站点列表
    private airStationList: any = [];
    // 空气站点id
    private airStationId: any = "";
    // 空气下拉框默认
    private defaultAir: any = "0";
    // 空气下拉框
    private airChange(val: any) {
        this.defaultAir = val;
        this.airStationId = this.airStationList[val].stationCode;
        this.getAqiTrend();
    }
    // 重污列表
    private pollutionList: any = [];
    // 重污下拉框默认
    private defaultPollution: any = "0";
    // 重污id
    private pollutionStationId: any = "";
    private pollutionChange(val: any) {
        this.defaultPollution = val;
        this.pollutionStationId = this.pollutionList[val].companyId;
        this.getTwentyFourHour();
    }
    private swiperVideoOption: any = {
        direction: "vertical",
        slidesPerView: 2,
        slidesPerGroup: 1,
        loop: false,
        autoplay: {
            delay: 3500,
            disableOnInteraction: false
        },
        navigation: {
            nextEl: ".right-arrow",
            prevEl: ".left-arrow"
        }
    };
    private swiperTimer: any = null;
    private emList: any = [video1, video2, video3];
    private emListActive: any = [video1, video2, video3];
    private emActive: any = video1;
    private videoList:any = []
    private checkEmImg(index: any) {
        this.playerOptions.sources = []
        this.emActive = this.emListActive[index];
        if (index == 0) {
            this.playerOptions.sources.push({
                type: "video/mp4",
                src: 'http://**************:9000/vankeytech-ep/waterVidel/DJI_0031.mp4'
            })
        } else if (index == 1) {
            this.playerOptions.sources.push({
                type: "video/mp4",
                src: 'https://oss-chengdu.vankeytech.com:9000/vankeytech-smart-community/aerial_photography/xinqiao.MP4'
            })
        } else {
            this.playerOptions.sources.push({
                type: "video/mp4",
                src: 'https://oss-chengdu.vankeytech.com:9000/vankeytech-ep/video/1.mp4'
            })
        }
        // clearInterval(this.swiperTimer);
        this.swiperIndex = index;
        // this.autoSwiperTimer();
    }
    private swiperIndex: any = 0;
    autoSwiperTimer() {
        this.swiperTimer = setInterval(() => {
            if (this.swiperIndex > this.emList.length - 1) {
                clearInterval(this.swiperTimer);
                this.swiperIndex = 0;
                this.checkEmImg(this.swiperIndex);
            }
            this.checkEmImg(this.swiperIndex);
            this.swiperIndex++;
        }, 3500);
    }
    private playerOptions: any = {
        autoplay: true, //如果true,浏览器准备好时开始回放。
        muted: false, // 默认情况下将会消除任何音频。
        loop: true, // 导致视频一结束就重新开始。
        preload: "auto", // 建议浏览器在<video>加载元素后是否应该开始下载视频数据。auto浏览器选择最佳行为,立即开始加载视频（如果浏览器支持）
        language: "zh-CN",
        languages: {
            "zh-CN": {
                Play: "播放",
                Pause: "暂停",
                "Current Time": "当前时间",
                Duration: "时长",
                "Remaining Time": "剩余时间",
                "Stream Type": "媒体流类型",
                LIVE: "直播",
                Loaded: "加载完毕",
                Progress: "进度",
                Fullscreen: "全屏",
                "Non-Fullscreen": "退出全屏",
                Mute: "静音",
                Unmute: "取消静音",
                "Playback Rate": "播放速度",
                Subtitles: "字幕",
                "subtitles off": "关闭字幕",
                Captions: "内嵌字幕",
                "captions off": "关闭内嵌字幕",
                Chapters: "节目段落",
                "Close Modal Dialog": "关闭弹窗",
                Descriptions: "描述",
                "descriptions off": "关闭描述",
                "Audio Track": "音轨",
                "You aborted the media playback": "视频播放被终止",
                "A network error caused the media download to fail part-way.":
                    "网络错误导致视频下载中途失败。",
                "The media could not be loaded, either because the server or network failed or because the format is not supported.":
                    "视频因格式不支持或者服务器或网络的问题无法加载。",
                "The media playback was aborted due to a corruption problem or because the media used features your browser did not support.":
                    "由于视频文件损坏或是该视频使用了你的浏览器不支持的功能，播放终止。",
                "No compatible source was found for this media.":
                    "无法找到此视频兼容的源。",
                "The media is encrypted and we do not have the keys to decrypt it.":
                    "视频已加密，无法解密。",
                // "Play Video": "播放视频",
                Close: "关闭",
                "Modal Window": "弹窗",
                "This is a modal window": "这是一个弹窗",
                "This modal can be closed by pressing the Escape key or activating the close button.":
                    "可以按ESC按键或启用关闭按钮来关闭此弹窗。",
                ", opens captions settings dialog": ", 开启标题设置弹窗",
                ", opens subtitles settings dialog": ", 开启字幕设置弹窗",
                ", opens descriptions settings dialog": ", 开启描述设置弹窗",
                ", selected": ", 选择",
                "captions settings": "字幕设定",
                "Audio Player": "音频播放器",
                "Video Player": "视频播放器",
                Replay: "重播",
                "Progress Bar": "进度小节",
                "Volume Level": "音量",
                "subtitles settings": "字幕设定",
                "descriptions settings": "描述设定",
                Text: "文字",
                White: "白",
                Black: "黑",
                Red: "红",
                Green: "绿",
                Blue: "蓝",
                Yellow: "黄",
                Magenta: "紫红",
                Cyan: "青",
                Background: "背景",
                Window: "视窗",
                Transparent: "透明",
                "Semi-Transparent": "半透明",
                Opaque: "不透明",
                "Font Size": "字体尺寸",
                "Text Edge Style": "字体边缘样式",
                None: "无",
                Raised: "浮雕",
                Depressed: "压低",
                Uniform: "均匀",
                Dropshadow: "下阴影",
                "Font Family": "字体库",
                "Proportional Sans-Serif": "比例无细体",
                "Monospace Sans-Serif": "单间隔无细体",
                "Proportional Serif": "比例细体",
                "Monospace Serif": "单间隔细体",
                Casual: "舒适",
                Script: "手写体",
                "Small Caps": "小型大写字体",
                Reset: "重启",
                "restore all settings to the default values": "恢复全部设定至预设值",
                Done: "完成",
                "Caption Settings Dialog": "字幕设定视窗",
                "Beginning of dialog window. Escape will cancel and close the window.":
                    "开始对话视窗。离开会取消及关闭视窗",
                "End of dialog window.": "结束对话视窗"
            }
        },
        aspectRatio: "16:9", // 将播放器置于流畅模式，并在计算播放器的动态大小时使用该值。值应该代表一个比例 - 用冒号分隔的两个数字（例如"16:9"或"4:3"）
        fluid: true, // 当true时，Video.js player将拥有流体大小。换句话说，它将按比例缩放以适应其容器。
        //application/x-mpegURL-m3u8 video/mp4-mp4 rtmp/mp4-rtmp flv-application/octet-stream-flv
        sources: [
            // {
            //     type: "video/mp4",
            //     src: require('../../../public/static/1.mp4')
            // },
        ],
        poster: this.emActive,
        // poster: '',
        width: document.documentElement.clientWidth,
        // notSupportedMessage: "该站点暂未设置监控，请查看其它站点" //允许覆盖Video.js无法播放媒体源时显示的默认信息。
        notSupportedMessage: " " //允许覆盖Video.js无法播放媒体源时显示的默认信息。
    };
    private playerOptions1: any = {
        autoplay: true, //如果true,浏览器准备好时开始回放。
        muted: true, // 默认情况下将会消除任何音频。
        loop: false, // 导致视频一结束就重新开始。
        preload: "auto", // 建议浏览器在<video>加载元素后是否应该开始下载视频数据。auto浏览器选择最佳行为,立即开始加载视频（如果浏览器支持）
        language: "zh-CN",
        languages: {
            "zh-CN": {
                Play: "播放",
                Pause: "暂停",
                "Current Time": "当前时间",
                Duration: "时长",
                "Remaining Time": "剩余时间",
                "Stream Type": "媒体流类型",
                LIVE: "直播",
                Loaded: "加载完毕",
                Progress: "进度",
                Fullscreen: "全屏",
                "Non-Fullscreen": "退出全屏",
                Mute: "静音",
                Unmute: "取消静音",
                "Playback Rate": "播放速度",
                Subtitles: "字幕",
                "subtitles off": "关闭字幕",
                Captions: "内嵌字幕",
                "captions off": "关闭内嵌字幕",
                Chapters: "节目段落",
                "Close Modal Dialog": "关闭弹窗",
                Descriptions: "描述",
                "descriptions off": "关闭描述",
                "Audio Track": "音轨",
                "You aborted the media playback": "视频播放被终止",
                "A network error caused the media download to fail part-way.":
                    "网络错误导致视频下载中途失败。",
                "The media could not be loaded, either because the server or network failed or because the format is not supported.":
                    "视频因格式不支持或者服务器或网络的问题无法加载。",
                "The media playback was aborted due to a corruption problem or because the media used features your browser did not support.":
                    "由于视频文件损坏或是该视频使用了你的浏览器不支持的功能，播放终止。",
                "No compatible source was found for this media.":
                    "无法找到此视频兼容的源。",
                "The media is encrypted and we do not have the keys to decrypt it.":
                    "视频已加密，无法解密。",
                // "Play Video": "播放视频",
                Close: "关闭",
                "Modal Window": "弹窗",
                "This is a modal window": "这是一个弹窗",
                "This modal can be closed by pressing the Escape key or activating the close button.":
                    "可以按ESC按键或启用关闭按钮来关闭此弹窗。",
                ", opens captions settings dialog": ", 开启标题设置弹窗",
                ", opens subtitles settings dialog": ", 开启字幕设置弹窗",
                ", opens descriptions settings dialog": ", 开启描述设置弹窗",
                ", selected": ", 选择",
                "captions settings": "字幕设定",
                "Audio Player": "音频播放器",
                "Video Player": "视频播放器",
                Replay: "重播",
                "Progress Bar": "进度小节",
                "Volume Level": "音量",
                "subtitles settings": "字幕设定",
                "descriptions settings": "描述设定",
                Text: "文字",
                White: "白",
                Black: "黑",
                Red: "红",
                Green: "绿",
                Blue: "蓝",
                Yellow: "黄",
                Magenta: "紫红",
                Cyan: "青",
                Background: "背景",
                Window: "视窗",
                Transparent: "透明",
                "Semi-Transparent": "半透明",
                Opaque: "不透明",
                "Font Size": "字体尺寸",
                "Text Edge Style": "字体边缘样式",
                None: "无",
                Raised: "浮雕",
                Depressed: "压低",
                Uniform: "均匀",
                Dropshadow: "下阴影",
                "Font Family": "字体库",
                "Proportional Sans-Serif": "比例无细体",
                "Monospace Sans-Serif": "单间隔无细体",
                "Proportional Serif": "比例细体",
                "Monospace Serif": "单间隔细体",
                Casual: "舒适",
                Script: "手写体",
                "Small Caps": "小型大写字体",
                Reset: "重启",
                "restore all settings to the default values": "恢复全部设定至预设值",
                Done: "完成",
                "Caption Settings Dialog": "字幕设定视窗",
                "Beginning of dialog window. Escape will cancel and close the window.":
                    "开始对话视窗。离开会取消及关闭视窗",
                "End of dialog window.": "结束对话视窗"
            }
        },
        aspectRatio: "16:9", // 将播放器置于流畅模式，并在计算播放器的动态大小时使用该值。值应该代表一个比例 - 用冒号分隔的两个数字（例如"16:9"或"4:3"）
        fluid: true, // 当true时，Video.js player将拥有流体大小。换句话说，它将按比例缩放以适应其容器。
        //application/x-mpegURL-m3u8 video/mp4-mp4 rtmp/mp4-rtmp flv-application/octet-stream-flv
        sources: [
            {
                type: "flv-application/octet-stream",
                src: ""
            },
            {
                type: "rtmp/mp4",
                src: ""
            },
            {
                type: "application/x-mpegURL-m3u8",
                src: ""
            }
        ],
        poster: "",
        width: document.documentElement.clientWidth,
        notSupportedMessage: "该站点暂未设置监控，请查看其它站点" //允许覆盖Video.js无法播放媒体源时显示的默认信息。
    };
    private mapStyle = "amap://styles/0739a6632d849ae1b5e6f1c52f7d12c3";
    private emergencyList: any = [];
    private taskList: any = [
        {
            timec: 1564503166555,
            time: 1564533166555,
            top: "-1.235rem"
        },
        {
            timec: 1564503166555,
            time: 1564538366555,
            top: "-0.985rem"
        },
        {
            timec: 1564503166555,
            time: 1564536166555,
            top: "-0.48rem"
        },
        {
            timec: 1564503166555,
            time: 1564535166555,
            top: "-0.735rem"
        },
    ];
    mounted() {
        this.playerOptions.sources.push({
            type: "video/mp4",
            src: 'http://**************:9000/vankeytech-ep/waterVidel/DJI_0031.mp4'
        })
        // this.getStationList();
        //@ts-ignore
        // eslint-disable-next-line no-undef
        const currentRoute = JSON.parse(localStorage.getItem('currentRoute'));
        if (currentRoute.name == '应急指挥') {
            this.connect();
            this.connect1();
        }
        (window as any)._that = this;
        //@ts-ignore
        // eslint-disable-next-line no-undef
        this.$bus.on("stationType", (res: any) => {
            if (res[0].type == 1) {
                this.isShow = 1
                jq("#time").css({
                    left: 0
                });
                this.start = 0
                this.end = 0
                this.initEnd = 0
                this.left = 0
                // 空气
                this.airStationId = res[0].stationId;
                this.getAqiTrend();
                this.defaultAir = String(
                    this.airStationList.findIndex((item: any, index: any) => {
                        return res[0].stationId == item.stationCode;
                    })
                );
                this.next = -1
                if (this.fetchTimer) {
                    clearInterval(this.fetchTimer)
                    this.fetchTimer = setInterval(() => {
                        this.nextOpen()
                    }, 5 * 1000)
                }
            } else if (res[0].type == 2) {
                this.isShow = 2
                jq("#time").css({
                    left: 0
                });
                this.start = 0
                this.end = 0
                this.initEnd = 0
                this.left = 0
                // 水
                this.waterStationId = res[0].stationId;
                this.getMonitorItemDetailRecord();
                this.defaultWater = String(
                    this.waterStationList.findIndex((item: any, index: any) => {
                        return res[0].stationId == item.stationId;
                    })
                );
                this.next = -1
                if (this.fetchTimer) {
                    clearInterval(this.fetchTimer)
                    this.fetchTimer = setInterval(() => {
                        this.nextOpen()
                    }, 5 * 1000)
                }
            } else if (res[0].type == 3) {
                this.isShow = 3
                jq("#time").css({
                    left: 0
                });
                this.start = 0
                this.end = 0
                this.initEnd = 0
                this.left = 0
                // 重污
                this.pollutionStationId = res[0].stationId;
                this.getTwentyFourHour();
                this.defaultPollution = String(
                    this.pollutionList.findIndex((item: any, index: any) => {
                        return res[0].stationId == item.companyId;
                    })
                );
                this.next = -1
                if (this.fetchTimer) {
                    clearInterval(this.fetchTimer)
                    this.fetchTimer = setInterval(() => {
                        this.nextOpen()
                    }, 5 * 1000)
                }
            } else if (res[0].type == 4) {
                // 监控
                this.defaultCameraValue = res[0].stationId
                console.log(this.defaultCameraValue, 'this.defaultCameraValue')
                const camera = this.waterCameraList.find((item: any, index: any) => {
                    if (item.monitorId == this.defaultCameraValue) {
                        return item;
                    }
                });
                this.fetchCameraUrl(this.defaultCameraValue, camera.snapUrl);
            } else if (res[0].type == 5) {
                this.carId = res[0].stationId
                this.getCarCamera()
            }
        });
        // eslint-disable-next-line @typescript-eslint/no-this-alias
        const _this = this;
    }
    beforeDestroy() {
        //@ts-ignore
        // eslint-disable-next-line no-undef
        window.clearInterval(this.timer);
        window.clearInterval(this.requestTime);
        window.clearInterval(this.swiperTimer);
        clearInterval(this.fetchTimer)
        // localStorage.setItem('emercy', '0')
        this.socket.close();
        this.socket = null

        if(this.$refs.livePlayer) {
            // @ts-ignore
            this.$refs.livePlayer.dispose()
        }
        if (this.flvPlayer) {
            this.flvPlayer.pause();
            this.flvPlayer.unload();
            this.flvPlayer.detachMediaElement();
            this.flvPlayer.destroy();
            this.flvPlayer = null;
        }
        // @ts-ignore
        this.$bus.off("stationType")
        (window as any)._that = null;
    }
    private timer: any = null;
    private siteType: any = 1;
    private taskDetailState: any = false;
    private pageX = 2000;
    private initIndex = 0;
    private timeLineDataTime: any = {
        startTime: "",
        endTime: ""
    };
    private left:any = 0
    private move:any = false
    private timeLineTimes: any = moment(
        new Date(new Date().valueOf() - 86400000)
    ).format("YYYY-MM-DD HH:00:00");
    private htmlLoad(startTime: any, endTime: any) {
        // eslint-disable-next-line @typescript-eslint/no-this-alias
        const _this = this;
        let _move = false;
        let _x: any;
        let dataTime: any = "";
        jq(".tttt")
            .click(function() {
                // 1;
            })
            .mousedown(function(e: any) {
                if (_this.fetchTimer) {
                    console.log(12321312321)
                    clearInterval(_this.fetchTimer)
                }
                _move = true;
                _this.move = true
                _x = e.pageX - parseInt(jq("#time").css("left"));
                jq("#time").fadeTo(20, 1);
            });
        jq(".tttt")
            .mousemove(function(e: any) {
                // _this.pageX = e.pageX;
                _this.move = true
                if (_move) {
                    _this.taskDetailState = false;
                    // for (const item of document.querySelectorAll(".time1-active")) {
                    //     item.className = "time1";
                    // }
                    const x = e.pageX - _x;
                    jq("#time").css({
                        left: x
                    });
                    if (x > 0) {
                        jq("#time").css({
                            left: 0
                        });
                        dataTime = moment(
                            new Date(Number(_this.timeLineDataTime.startTime) + 86400000)
                        ).format("YYYY-MM-DD HH:00:00");
                        // _this.timeLineTimes = dataTime;
                    } else if (jq("#time").width() - jq("#times").width() + x < 0) {
                        if (_this.timeLength <= 20) {
                            jq("#time").css({
                                left: 0
                            });
                        } else {
                            jq("#time").css({
                                left: -(jq("#time").width() - jq("#times").width())
                            });
                        }
                    } else if (x < 0) {
                        const number =
                            (Math.ceil(Math.abs(x) / (jq("#times").width() / 24)) + 24 - 1) *
                            3600000;
                        dataTime = moment(
                            new Date(Number(_this.timeLineDataTime.startTime) + number)
                        ).format("YYYY-MM-DD HH:00:00");
                        // _this.timeLineTimes = dataTime;
                    }
                    _this.left = Math.abs(Number(jq("#time").css('left').substr(0, jq("#time").css('left').length -2)))
                }
            })
            .mouseup(function() {
                console.log(_this.left, '_this.left')
                // // 水质
                // _this.getMonitorItemDetailRecord();
                // // 空气
                // _this.getAqiTrend();
                // // 重污
                // _this.getTwentyFourHour();
                if (_this.fetchTimer && _move) {
                    clearInterval(_this.fetchTimer)
                    _this.fetchTimer = setInterval(() => {
                        _this.nextOpen()
                    }, 5 * 1000)
                }
                console.log(_move, '_move')
                jq(".tttt").fadeTo("fast", 1);
                _move = false;
                _this.move = false
            })
            .mouseleave(function () {
                if (_this.fetchTimer && _move) {
                    clearInterval(_this.fetchTimer)
                    _this.fetchTimer = setInterval(() => {
                        _this.nextOpen()
                    }, 5 * 1000)
                }
                _move = false
                _this.move = false
            });
        this.time(startTime, endTime);
    }
    private timeLength:any = 0
    private time(hour: any, endTime: any) {
        const date = endTime;
        let timeLength = 0
        let length = this.departmentList.length - 1
        const timess = ((date - hour) / 1000 / 60) + 80;
        for (let i = 0; i < Math.floor(timess); i++) {
            let time1: any = "";
            const house: any =
                new Date(hour + i * 60000).getHours() >= 10
                    ? new Date(hour + i * 60000).getHours()
                    : "0" + new Date(hour + i * 60000).getHours();
            const minutes: any =
                new Date(hour + i * 60000).getMinutes() >= 10
                    ? new Date(hour + i * 60000).getMinutes()
                    : "0" + new Date(hour + i * 60000).getMinutes();
            const sxsx = house + ":" + minutes;
            if (minutes == "00") {
                timeLength++;
                time1 = `<li style='left:${i / 6 *
                0.06+0.4}rem;top: ${0.1+0.36*length}rem'>${sxsx}</li>`;
            }
            this.timeLength = timeLength
            jq("#time").append(time1);
        }
        jq("#time").css({
            width: Math.floor(timess) * 0.01 + "rem"
        });
    }
    private task1NodeIndex:any = []
    private task2NodeIndex:any = []
    private task3NodeIndex:any = []
    private task4NodeIndex:any = []
    private addtimes(data: any, endTime: any, startTime: any, index:any) {
        console.log(data.initIndex, 'data.initIndex')
        if (data.type == 1) {
            this.task1NodeIndex.push(index)
        } else if (data.type == 2) {
            this.task2NodeIndex.push(index)
        } else if (data.type == 3) {
            this.task3NodeIndex.push(index)
        } else if (data.type == 4) {
            this.task4NodeIndex.push(index)
        }
        const replyContent = data.replyContent.replace(/\s/g, "");
        console.log('aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa', data)
        this.taskAnnexList = data.taskAnnexList
        const content = data.content;
        const taskLogId = data.taskLogId
        const str: any = `'${data.departmentName}---${data.times}---${content}---${replyContent}---${taskLogId}---${data.type}---${index}---${data.initIndex}'`;
        const timess: any = Math.floor((data.times - data.timec) / 1000 / 60);
        const activeClass = data.initIndex == 0 ? 'time2' : data.initIndex == 1 ? 'time3' : data.initIndex == 2 ? 'time4' : 'time5'
        const time1: any = `<li class='time1 ${activeClass}'" style='left:${timess *
        0.01+0.4}rem;top:${data.top}' onclick=taskDetail(${str})></li>`;
        jq("#time").append(time1);
        // if (endTime - startTime > 90000000) {
        //     jq("#time").css({
        //         left: "-" + (timess * 0.01 - 11.5) + "rem"
        //     });
        // }
    }
    // 空气质量趋势
    private airData: any = {
        bottomList: [],
        dataList: [],
        unit: "",
        colorType: ""
    };
    // 空气质量趋势type
    private airTypeLIst: any[] = [
        {
            code: "104",
            name: "PM₁₀"
        },
        {
            code: "102",
            name: "O₃"
        },
        {
            code: "101",
            name: "NO₂"
        },
        {
            code: "105",
            name: "PM₂.₅"
        },
        {
            code: "100",
            name: "SO₂"
        },
        {
            code: "103",
            name: "CO"
        }
    ];
    private airType = "104";
    // 24小时空气质趋势
    private getAqiTrend() {
        this.socket1.send(JSON.stringify({
            code: 14, stationCode: this.airStationId, pollutantCode: this.airType,
            startTime: moment(new Date(Number(this.timeLineDataTime.startTime))).format(
                "YYYY-MM-DD HH:00:00"
            ),
            endTime: moment(new Date(Number(this.timeLineDataTime.endTime))).format(
                "YYYY-MM-DD HH:00:00"
            )}));
    }
    // 切换标签
    private airTypeChange(e: any) {
        this.airType = e.target.value;
        this.getAqiTrend();
    }
    private displayState = false;
    private isShow = 2;
    // 地图视图中心点
    private mapViewCenter: any = {
        lng: 104.04,
        lat: 30.685
    };
    private fullScreen() {
        this.displayState = !this.displayState;
        this.mapViewCenter = this.displayState
            ? {
                lng: 104.04,
                lat: 30.725
            }
            : {
                lng: 104.04,
                lat: 30.685
            };
    }
    private taskId: any = "";
    // 上报详情
    private reportDetails: any = {};
    private getReportDetails() {
        getReportDetails().then((res: any) => {
            if (res.data.data.levelId == 1) {
                res.data.data.levelId = "一";
            } else if (res.data.data.levelId == 2) {
                res.data.data.levelId = "二";
            } else if (res.data.data.levelId == 3) {
                res.data.data.levelId = "三";
            } else if (res.data.data.levelId == 4) {
                res.data.data.levelId = "四";
            }
            this.taskId = res.data.data.id;
            const geocoder = new AMap.Geocoder({
                city: "全国"
            });
            geocoder.getAddress(
                [res.data.data.longitude, res.data.data.latitude],
                (status: any, result: any) => {
                    if (status === "complete" && result.regeocode) {
                        const address = result.regeocode.formattedAddress;
                        res.data.data.address = address;
                        this.reportDetails = res.data.data;
                        this.rangeObj = {
                            lng: this.reportDetails.longitude,
                            lat: this.reportDetails.latitude,
                            number: 3000
                        };
                    }
                }
            );
        });
    }
    // 任务概览
    private taskOverview: any = {};
    private getTaskOverview() {
        getTaskOverview().then((res: any) => {
            this.taskOverview = res.data.data;
        });
    }
    // 任务反馈时间轴显示
    private taskRemark: any = {};
    private departmentList:any = []
    private creatTime: any = "";
    private departmentName: any = "";
    private content: any = "";
    private replyContent: any = "";
    private taskAnnexList: any = ""
    private taskLogId: any = "";
    // 水质监测数据
    private waterMonitor: any = {
        bottomList: [],
        dataList: [],
        unit: "mg/L"
    };
    // 水质实时监测数据btn选择
    private waterMonitorSelectChange(e: any): void {
        this.airType = e.target.value;
    }
    // 改变污染类型
    private changeType(params:any) {
        this.isShow = params
        jq("#time").css({
            left: 0
        });
        this.start = 0
        this.end = 0
        this.initEnd = 0
        this.left = 0
        this.next = -1
        // let currLeft = jq('#time .time1')[this.next].style.left
        // currLeft = currLeft.replace(/rem/g, '')
        // jq('#time .time1')[this.next].click()
        // if (Number(currLeft) >= Number(this.totalLength)) {
        //     const left = Number(this.totalLength) - Number(currLeft) + 1.35 + 'px'
        //     jq('#time').css({left: -left })
        // }
        if (this.fetchTimer) {
            clearInterval(this.fetchTimer)
            this.fetchTimer = setInterval(() => {
                this.nextOpen()
            }, 5 * 1000)
        }
        if (this.waterStationList.length > 0 && this.isShow === 2) {
            this.waterStationId = this.waterStationList[0].stationId;
            this.getMonitorItemDetailRecord()
        }
        if (this.airStationList.length > 0  && this.isShow === 1) {
            this.airStationId = this.airStationList[0].stationCode;
            this.getAqiTrend();
        }
        if (this.pollutionList.length > 0  && this.isShow === 3) {
            this.pollutionStationId = this.pollutionList[0].companyId;
            this.getTwentyFourHour();
        }
    }
    // 获取水质监测项详情记录
    private getMonitorItemDetailRecord(): void {
        this.socket1.send(JSON.stringify({
            code: 13, stationId: this.waterStationId, itemCode: this.defaultwaterType,
            startTime: moment(new Date(Number(this.timeLineDataTime.startTime))).format(
                "YYYY-MM-DD HH:00:00"
            ),
            endTime: moment(new Date(Number(this.timeLineDataTime.endTime))).format(
                "YYYY-MM-DD HH:00:00"
            )}));
    }
    // 在建工地趋势
    private pollutionData: any = {
        bottomList: [],
        dataList: [],
        unit: "",
        colorType: ""
    };
    // 获取重点污染源
    private getTwentyFourHour() {
        this.socket1.send(JSON.stringify({
            code: 15, projectId: this.pollutionStationId,
            startTime: moment(new Date(Number(this.timeLineDataTime.startTime))).format(
                "YYYY-MM-DD HH:00:00"
            ),
            endTime: moment(new Date(Number(this.timeLineDataTime.endTime))).format(
                "YYYY-MM-DD HH:00:00"
            )}));
    }
    // 获取地图的站点信息
    private emergencyStationList: any[] = [];
    // 水站点列表(摄像头)
    private waterStationVideoList: any = [];
    // 水站点摄像头列表
    private currentCameraList: Array<any> = [];
    // 默认选中水摄像头
    private defaultCameraValue = "";
    private defaultWaterStationValue: any = "";
    // 水质监控站点切换
    private waterStationChange(value: string): void {
        this.fetchCameraList(value);
        this.defaultWaterStationValue = value + "";
        // 重置一次摄像头数据
        this.currentCameraList = [];
        this.defaultCameraValue = "";
        this.playerOptions1.sources = [
            {
                src: null,
                type: null
            }
        ];
    }
    // 获取摄像头列表
    private fetchCameraList(station: string): void {
        fetchCameraList(station).then(res => {
            if (res.data.data && res.data.data.length > 0) {
                this.currentCameraList = res.data.data;
                this.defaultCameraValue = this.currentCameraList[0].monitorId;
                console.log(this.defaultCameraValue , 12345)
                this.fetchCameraUrl(
                    this.defaultCameraValue,
                    this.currentCameraList[0].snapUrl
                );
            } else {
                this.currentCameraList = [];
                this.defaultCameraValue = "";
                this.playerOptions1.sources = [
                    {
                        src: "",
                        type: "application/x-mpegURL"
                    }
                ];
                this.playerOptions1.poster = "";
            }
        });
    }
    // 水质监控站点摄像头切换
    private waterCameraChange(value: any): void {
        const camera = this.waterCameraList.find((item: any, index: any) => {
            if (item.monitorId == value) {
                return item;
            }
        });
        this.fetchCameraUrl(value, camera.snapUrl);
    }
    // 获取摄像头播放地址
    private fetchCameraUrl(channelId: string, snapUrl: any): void {
        fetchCameraUrl(channelId).then(res => {
            // 清除上一次地址
            this.playerOptions1.sources = [
                {
                    src: "",
                    type: "application/x-mpegURL"
                }
            ];
            if (res.data.data.hlsHttps) {
                this.playerOptions1.sources.push({
                    type: "application/x-mpegURL",
                    src: res.data.data.hlsHttps
                });
            } else if (res.data.data.flvHttps) {
                this.playerOptions1.sources.push({
                    type: "flv-application/octet-stream",
                    src: res.data.data.flvHttps
                });
            } else if (res.data.data.rtmp) {
                this.playerOptions1.sources.push({
                    type: "rtmp/mp4",
                    src: res.data.data.rtmp
                });
            } else {
                this.playerOptions1.sources = [
                    {
                        src: "",
                        type: "application/x-mpegURL"
                    }
                ];
            }
            if (snapUrl) {
                this.playerOptions1.poster = snapUrl;
            } else {
                this.playerOptions1.poster = "";
            }
        });
    }
    // 更多视频
    toRouter(router: any) {
        this.$router.push(router);
    }
    private playerStateChanged(playerCurrentState: any) {
        // if (playerCurrentState.error) {
        // }
    }
    private taskType1:any = true
    private taskType2:any = true
    private taskType3:any = true
    private taskType4:any = true
    handleDepartment(item:any) {
        this.departmentStatus[item.type] = !this.departmentStatus[item.type]
        if (item.type == 'st') {
            this.taskType1 = this.departmentStatus[item.type]
            jq('.time2').css('display', this.departmentStatus[item.type] ? 'block' : 'none')
        } else if (item.type == 'zf') {
            this.taskType2 = this.departmentStatus[item.type]
            jq('.time3').css('display', this.departmentStatus[item.type] ? 'block' : 'none')
        } else if (item.type == 'ns') {
            this.taskType3 = this.departmentStatus[item.type]
            jq('.time4').css('display', this.departmentStatus[item.type] ? 'block' : 'none')
        } else if (item.type == 'jj') {
            this.taskType4 = this.departmentStatus[item.type]
            jq('.time5').css('display', this.departmentStatus[item.type] ? 'block' : 'none')
        }
    }
    private remarkDetail:any = {}
    private hanldeDetail() {
        this.remarkDetail = this.taskList.find((item:any) => item.taskLogId === this.taskLogId)
        console.log(this.remarkDetail, 'this.remarkDetail')
        clearInterval(this.fetchTimer)
        this.visible = true
    }
    private handleClose() {
        this.visible = false
        this.fetchTimer = setInterval(() => {
            this.nextOpen()
        }, 5 * 1000)
    }
    private socket: any = null;
    private socket1: any = null;
    // 建立连接
    private connect() {
        // this.socket = new WebSocket("ws://192.168.0.135:22000/ws");
        // this.socket = new WebSocket("ws://ep.vankeytech.com:8835/ws");
        // this.socket = new WebSocket("ws://www.jinnq.com:8834/ws");
        this.socket = new WebSocket(socketUrl());
        // 监听socket连接
        this.socket.onopen = this.open;
        // 监听socket错误信息
        this.socket.onerror = this.error;
        // 监听socket消息
        this.socket.onmessage = this.getMessage;
        window.onbeforeunload = () => {
            this.socket.onopen = () => {}
            this.socket.onerror = () => {}
            this.socket.onmessage = () => {}
            this.socket.close();
        };
    }
    private open() {
        this.send();
    }
    private error() {
        if(location.hash.includes('emergencyCommand')){
            message.error("系统连接错误");
        }
    }
    private send() {
        this.socket.send(JSON.stringify({ code: 1, token: "hello1234" }));
    }
    // 建立连接
    private connect1() {
        this.socket1 = new WebSocket(socketUrl2());
        // 监听socket连接
        this.socket1.onopen = this.open1;
        // 监听socket错误信息
        this.socket1.onerror = this.error1;
        // 监听socket消息
        this.socket1.onmessage = this.getMessage1;
        window.onbeforeunload = () => {
            this.socket1.onopen = () => {}
            this.socket1.onerror = () => {}
            this.socket1.onmessage = () => {}
            this.socket1.close();
        };
    }
    private open1() {
        this.send1();
    }
    private error1() {
        if(location.hash.includes('emergencyCommand')){
            message.error("系统连接错误");
        }
    }
    private send1() {
        this.socket1.send(JSON.stringify({ code: 5 }));
    }
    private melliageData:any= {}
    private startTimes:any = null
    private endTimes:any = null
    private nextTime:any = 15
    private fetchTimer:any = null
    private next:any = 0
    private totalLength:any = 0
    private handleNext() {
        const length = jq('#time .time1').length - 1
        if (!this.taskType1 && this.task1NodeIndex.indexOf(this.next) > - 1) {
            if (this.next < length - 1) {
                this.next = this.next + 1
                this.handleNext()
            } else {
                this.next = 0
                jq('#time').css({left: 0 })
                this.handleNext()
            }
        } else if (!this.taskType2 && this.task2NodeIndex.indexOf(this.next) > - 1) {
            if (this.next < length - 1) {
                this.next = this.next + 1
                this.handleNext()
            } else {
                this.next = 0
                jq('#time').css({left: 0 })
                this.handleNext()
            }
        } else if (!this.taskType3 && this.task3NodeIndex.indexOf(this.next) > - 1) {
            if (this.next < length - 1) {
                this.next = this.next + 1
                this.handleNext()
            } else {
                this.next = 0
                jq('#time').css({left: 0 })
                this.handleNext()
            }
        } else if (!this.taskType4 && this.task4NodeIndex.indexOf(this.next) > - 1) {
            if (this.next < length - 1) {
                this.next = this.next + 1
                this.handleNext()
            } else {
                this.next = 0
                jq('#time').css({left: 0 })
                this.handleNext()
            }
        }
    }
    private nextOpen() {
        // console.log(this.task1NodeIndex, this.taskType1, 9966)
        // const length = jq('#time .time1').length
        // if (!this.taskType1 && !this.taskType2 && !this.taskType3 && !this.taskType4) {
        //     this.next = 0
        //     jq('#time').css({left: 0 })
        // } else if (this.next < length - 1) {
        //     this.next = this.next + 1
        //     this.handleNext()
        // } else {
        //     this.next = 0
        //     jq('#time').css({left: 0 })
        //     this.handleNext()
        // }
        // console.log(this.next, 'next')
        // let currLeft = jq('#time .time1')[this.next].style.left
        // currLeft = currLeft.replace(/rem/g, '')
        // if (Number(currLeft) >= Number(this.totalLength)) {
        //     const left = -(Number(currLeft) - Number(this.totalLength) + 1.35 ) * 100 + 'px'
        //     jq('#time').css({left: left })
        // }
        // this.left = Math.abs(Number(jq("#time").css('left').substr(0, jq("#time").css('left').length -2)))
        // if (this.taskType1 || this.taskType2 || this.taskType3 || this.taskType4) {
        //     jq('#time .time1')[this.next].click()
        // }
    }
    private totalDistance:any = []
    private getMessage1(msg: any) {
        const res: any = JSON.parse(msg.data);
        if (res.code === -8) {
            if (res.distance > 1000) {
                res.distance = (res.distance / 1000).toFixed(2)
                res.type = 1
            }
            this.melliageData = res
        }
        if (res.code === -5) {
            if (this.carId) {
                this.socket1.send(JSON.stringify({ code: 8, taskId: res.emergencyTask.taskId, carId: this.carId }));
            }
            res.emergencyTask.levelId = res.emergencyTask.emergencyLevel
            this.socket1.send(JSON.stringify({ code: 7, lng: res.emergencyTask.lng, lat: res.emergencyTask.lat, distance: this.defaultRange  }));
            if (res.emergencyTask.levelId == 1) {
                res.emergencyTask.levelId = "一";
            } else if (res.emergencyTask.levelId == 2) {
                res.emergencyTask.levelId = "二";
            } else if (res.emergencyTask.levelId == 3) {
                res.emergencyTask.levelId = "三";
            } else if (res.emergencyTask.levelId == 4) {
                res.emergencyTask.levelId = "四";
            }
            this.taskId = res.emergencyTask.taskId;
            res.emergencyTask.longitude =  res.emergencyTask.lng
            res.emergencyTask.latitude =  res.emergencyTask.lat
            this.reportDetails = res.emergencyTask
            this.rangeObj = {
                lng: this.reportDetails.lng,
                lat: this.reportDetails.lat,
                number: 3000
            };
            this.taskRemark = res.executiveDepartmentList;
            const taskList: any[] = [];
            const departmentList: any[] = [];
            (res.executiveDepartmentList || []).forEach((item:any, index:any) => {
                const type = item.departmentId == 11 ? 'st' : item.departmentId == 12 ? 'zf' : item.departmentId == 13 ? 'ns' : 'jj'
                departmentList.push({name: item.departmentName, type: type})
                for (const task of item.feedbackList) {
                    task.times = new Date(task.time).getTime();
                    task.top = (index == 0 ? -0.06 : index == 1 ?  0.32 : index == 2 ? 0.7 : 1.06)+'rem';
                    task.initIndex = index
                    task.type = item.departmentId == 11 ? 1 : item.departmentId == 12 ? 2 : item.departmentId == 13 ? 3 : 4
                    task.departmentName = item.departmentName;
                    task.replyContent = task.userName+ ':' + (task.remark || task.content)
                    taskList.push(task);
                }
            })
            this.departmentList = departmentList
            this.startTimes = new Date(res.emergencyTask.createTime).getTime()
            if (res.emergencyTask.completeStatus) {
                this.endTimes = new Date(res.emergencyTask.completeTime).getTime()
            }
            if (taskList.length !== 0) {
                const sort: any = taskList.sort((a, b) => {
                    return a.times - b.times;
                });
                // 时间轴开始时间
                let startTime: any = "";
                // 时间轴结束时间
                let endTime: any = "";
                if (!this.endTimes) {
                    startTime = new Date(
                        moment(new Date(Number(this.startTimes))).format("YYYY-MM-DD HH:00:00")
                    ).valueOf()
                    endTime =
                        new Date(
                            moment(new Date(Number(new Date().getTime()))).format(
                                "YYYY-MM-DD HH:00:00"
                            )
                        ).valueOf() + 3600000;
                } else {
                    startTime = new Date(
                        moment(new Date(Number(this.startTimes))).format("YYYY-MM-DD HH:00:00")
                    ).valueOf()
                    endTime =
                        new Date(
                            moment(new Date(Number(this.endTimes))).format(
                                "YYYY-MM-DD HH:00:00"
                            )
                        ).valueOf() + 3600000;
                }
                this.timeLineDataTime = {
                    startTime,
                    endTime
                };
                this.timeLineTimes = moment(
                    new Date(Number(this.timeLineDataTime.startTime) + 86400000)
                ).format("YYYY-MM-DD HH:00:00");
                for (const item of taskList) {
                    item.timec = startTime;
                }
                this.taskList = taskList;
                jq(".time1").remove();
                this.$nextTick(() => {
                    this.htmlLoad(startTime, endTime);
                    this.taskList.forEach((item:any, index:any) => {
                        this.addtimes(item, sort.slice(-1)[0].times, sort[0].times, index);
                    })
                    this.totalLength = jq('#times').width() / 100
                    let currLeft = jq('#time .time1')[this.next].style.left
                    currLeft = currLeft.replace(/rem/g, '')
                    // jq('#time .time1')[this.next].click()
                    if (Number(currLeft) >= Number(this.totalLength)) {
                        const left = Number(this.totalLength) - Number(currLeft) + 1.35 + 'px'
                        jq('#time').css({left: -left })
                    }
                    let length = jq('#time .time1').length
                    for(let i = 0; i < length; i++) {
                        let currLeft = jq('#time .time1')[i].style.left
                        this.totalDistance.push(Number(currLeft.replace(/rem/g, '')) * 100)
                    }
                    this.fetchTimer = setInterval(() => {
                        this.nextOpen()
                    }, 5 * 1000)
                });
            }
        }
        if (res.code === -7) {
            const data = res.stationList
            data.airStationList = data.airStationList.map((item:any) => {
                item.stationType = 1
                item.aqi = item.concentration
                item.id = item.stationCode
                item.stationName = item.positionName
                return item
            })
            data.waterStationList = data.waterStationList.map((item:any) => {
                item.longitude = item.lng
                item.latitude = item.lat
                item.stationType = 2
                item.id = item.stationId
                item.stationCode = item.stationId
                return item
            })
            data.constructionList = data.constructionList.map((item:any) => {
                item.longitude = item.gcLng
                item.latitude = item.gcLat
                item.companyName = item.projectName
                item.id = item.projectId
                item.companyId = item.projectId
                item.stationCode = item.projectId
                item.stationType = 3
                return item
            })
            data.waterCameraList = data.cameraList.map((item:any) => {
                item.longitude = item.lng
                item.latitude = item.lat
                item.stationType = 4
                item.stationCode = item.monitorId
                return item
            })
            data.carList = data.carList.map((item:any) => {
                item.longitude = item.gps.gcLng
                item.latitude = item.gps.gcLat
                item.stationType = 5
                item.stationCode = item.carId
                return item
            })
            this.emergencyStationList = data.airStationList.concat(data.constructionList).concat(data.waterStationList).concat(data.waterCameraList).concat(data.carList) || [];
            this.waterStationList = res.stationList.waterStationList
            this.carList = res.stationList.carList
            if (this.carList.length) {
                this.carId = this.carList[0].carId
                this.socket1.send(JSON.stringify({ code: 8, taskId: this.taskId, carId: this.carId }));
                this.cameraSerialList = this.carList[0].cameraList
                if (this.cameraSerialList.length) {
                    this.cameraDirection = 0
                    this.socket.send(
                        JSON.stringify({
                            code: 4,
                            cameraSerial: this.cameraSerialList[this.cameraDirection].cameraSerial
                        })
                    );
                }
            }
            if (!this.waterStationList.length) {
                this.defaultWater = ''
                this.waterTypeList = []
            } else {
                this.defaultWater = '0'
                this.waterTypeList = this.waterStationList[0].monitorItems
                if (this.waterTypeList && this.waterTypeList.length) {
                    this.defaultwaterType = this.waterTypeList[0].itemCode
                }
            }
            this.waterCameraList = res.stationList.waterCameraList
            console.log( this.waterCameraList, 'this.waterCameraList11233')
            if (this.waterCameraList.length) {
                this.defaultCameraValue = this.waterCameraList[0].monitorId
                console.log( this.defaultCameraValue, ' this.defaultCameraValue')
                this.fetchCameraUrl(
                    this.defaultCameraValue,
                    this.waterCameraList[0].snapUrl
                );
            } else {
                this.waterCameraList = [];
                this.defaultCameraValue = "";
                this.playerOptions1.sources = [
                    {
                        src: "",
                        type: "application/x-mpegURL"
                    }
                ];
                this.playerOptions1.poster = "";
            }
            this.airStationList = res.stationList.airStationList
            if (!this.airStationList.length) {
                this.defaultAir = ''
            }
            this.pollutionList = res.stationList.constructionList
            if (!this.pollutionList.length) {
                this.defaultPollution = ''
            }
            if (this.waterStationList.length > 0) {
                this.waterStationId = this.waterStationList[0].stationId;
                this.getMonitorItemDetailRecord()
            }
            // if (this.airStationList.length > 0) {
            //     this.airStationId = this.airStationList[0].stationCode;
            //     this.getAqiTrend();
            // }
            // if (this.pollutionList.length > 0) {
            //     this.pollutionStationId = this.pollutionList[0].companyId;
            //     this.getTwentyFourHour();
            // }
        }
        if (res.code === -13) {
            if (res.waterMonitorRecordList && res.waterMonitorRecordList.length) {
                const data = res.waterMonitorRecordList;
                const waterMonitor:any = {
                    bottomList: [],
                    dataList: [],
                    unit: "mg/L"
                }
                const item = this.waterTypeList.find((item:any) => item.itemCode === this.defaultwaterType)
                waterMonitor.unit = item.concentrationUnit
                for (const item of data) {
                    item.time = moment(new Date(item.time)).format("YYYY-MM-DD HH:00");
                    waterMonitor.bottomList.push(item.time);
                    waterMonitor.dataList.push(item.value);
                }
                this.waterMonitor = waterMonitor
                console.log( this.waterMonitor, ' this.waterMonitor')
                if (this.waterMonitor.bottomList.length <= 20) {
                    this.end = 100
                } else {
                    this.end = Math.floor((20 / this.waterMonitor.bottomList.length) * 100)
                }
                this.initEnd = this.end
            }
        }
        if (res.code === -14) {
            const airData: any = {
                bottomList: [],
                dataList: [],
                unit: "",
                colorType: ""
            };
            if (res.airMonitorRecordList && res.airMonitorRecordList.length) {
                for (const item of res.airMonitorRecordList) {
                    item.time = moment(new Date(item.time)).format("YYYY-MM-DD HH:00");
                    airData.bottomList.push(item.time);
                    airData.dataList.push(item.value);
                    if (this.airType == "103") {
                        airData.unit = "mg/m³";
                    } else {
                        airData.unit = "μg/m³";
                    }
                    if (this.airType == "100") {
                        airData.colorType = "SO2";
                    } else if (this.airType == "101") {
                        airData.colorType = "NO2";
                    } else if (this.airType == "102") {
                        airData.colorType = "O3";
                    } else if (this.airType == "103") {
                        airData.colorType = "CO";
                    } else if (this.airType == "104") {
                        airData.colorType = "PM10";
                    } else if (this.airType == "105") {
                        airData.colorType = "PM25";
                    }
                }
            }
            this.airData = airData;
            // 判断是否在同一类型下的污染物
            if (this.initEnd === 0) {
                if (this.airData.bottomList.length <= 20) {
                    this.end = 100
                } else {
                    this.end = Math.floor((20 / this.airData.bottomList.length) * 100)
                }
                this.initEnd = this.end
            }
        }
        if (res.code === -15) {
            const pollutionData: any = {
                bottomList: [],
                dataList: [],
                unit: "",
                colorType: ""
            };
            if (res.constructionMonitorRecordList && res.constructionMonitorRecordList.length) {
                for (const item of res.constructionMonitorRecordList) {
                    item.time = moment(new Date(item.time)).format("YYYY-MM-DD HH:00");
                    pollutionData.bottomList.push(item.time);
                    pollutionData.dataList.push(item.value);
                    if (this.airType == "103") {
                        pollutionData.unit = "mg/m³";
                    } else {
                        pollutionData.unit = "μg/m³";
                    }
                    if (this.airType == "100") {
                        pollutionData.colorType = "SO2";
                    } else if (this.airType == "101") {
                        pollutionData.colorType = "NO2";
                    } else if (this.airType == "102") {
                        pollutionData.colorType = "O3";
                    } else if (this.airType == "103") {
                        pollutionData.colorType = "CO";
                    } else if (this.airType == "104") {
                        pollutionData.colorType = "PM10";
                    } else if (this.airType == "105") {
                        pollutionData.colorType = "PM25";
                    }
                }
            }
            this.pollutionData = pollutionData;
            // 判断是否在同一类型下的污染物
            if (this.initEnd === 0) {
                if (this.pollutionData.bottomList.length <= 20) {
                    this.end = 100
                } else {
                    this.end = Math.floor((20 / this.pollutionData.bottomList.length) * 100)
                }
                this.initEnd = this.end
            }
        }
    }
    private start:any = 0
    private end:any = 0
    private initEnd:any = 0
    private cameraSerial: any = "";
    private requestTime: any = "";
    private oldCarPoint = {}
    private newCarPoint = {}
    // 获得数据
    private getMessage(msg: any) {
        const res: any = JSON.parse(msg.data);
        if (res.code == -4) {
            if (this.flvPlayer) {
                this.flvPlayer.pause();
                this.flvPlayer.unload();
                this.flvPlayer.detachMediaElement();
                this.flvPlayer.destroy();
                this.flvPlayer = null;
            }
            if (flvjs.isSupported() && res.liveAddress.online) {
                const videoElement = document.getElementById("videoElement");
                this.flvPlayer = flvjs.createPlayer({
                    type: "flv",
                    isLive: true,
                    hasVideo: true,
                    // hasAudio: true,
                    url: res.liveAddress.flv
                });
                this.flvPlayer.attachMediaElement(videoElement);
                this.flvPlayer.load();
                this.flvPlayer.play();
            }
            // clearInterval(this.requestTime);
            // this.requestTime = setInterval(() => {
            //     keepAlive(this.videoCameraSerial);
            // }, 10 * 1000);
        }
        if (res.code == 3) {
            // 车辆移动
            for (const i in this.carList) {
                const marker = this.carList[i]
                // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
                // @ts-ignore
                if (marker.carId == res.car.carId && marker.typeId) {
                    // @ts-ignore
                    this.oldCarPoint = marker
                    this.newCarPoint = {
                        type: res.car.typeId,
                        longitude: res.car.gps.gcLng,
                        latitude: res.car.gps.gcLat,
                        license: res.car.license,
                        carId: res.car.carId,
                        deptId: res.car.deptId
                    }
                }
            }
        }
    }
    private cameraDirection = 0;
    private flvPlayer: any = null;
    private cameraList: any = [];
    private carId: any = "";
    private cameraSerialList:any = []
    private type:any  =0
    private class:any  =''
    // 获取车辆视频地址
    private getCarCamera() {
        this.cameraDirection = 0
        const item = this.carList.find((item:any) => item.carId === this.carId)
        if (item.cameraList.length) {
            this.socket.send(
                JSON.stringify({
                    code: 4,
                    cameraSerial: item.cameraList[0].cameraSerial
                })
            );
            this.cameraSerialList = item.cameraList
        } else {
            if (this.flvPlayer) {
                this.flvPlayer.pause();
                this.flvPlayer.unload();
                this.flvPlayer.detachMediaElement();
                this.flvPlayer.destroy();
                this.flvPlayer = null;
            }
        }
    }
    // 切换摄像头前后方向
    changeCameraDirection(direction: number) {
        if (this.cameraSerialList[direction].cameraSerial) {
            this.cameraDirection = direction;
            this.socket.send(
                JSON.stringify({
                    code: 4,
                    // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
                    // @ts-ignore
                    cameraSerial: this.cameraSerialList[direction].cameraSerial
                })
            );
        } else {
            message.error("暂无该方向摄像头");
        }
    }
}
(window as any).taskDetail = (data: any) => {
    // for (const item of document.querySelectorAll(".time1-active")) {
    //     item.className = "time1";
    // }
    let top = data.split("---")[7] == 0 ? -0.06 :  data.split("---")[7] == 1 ? 0.32 :  data.split("---")[7] == 2 ? 0.7 : 1.06
    if (data.split("---")[7] == 0) {
        jq('#time').addClass('time-active1')
        //@ts-ignore
        // eslint-disable-next-line no-undef
        _that.class = 'time-active1'
    } else if (data.split("---")[7] == 1) {
        jq('#time').addClass('time-active2')
        //@ts-ignore
        // eslint-disable-next-line no-undef
        _that.class = 'time-active2'
    } else if (data.split("---")[7] == 2) {
        jq('#time').addClass('time-active3')
        //@ts-ignore
        // eslint-disable-next-line no-undef
        _that.class = 'time-active3'
    } else if (data.split("---")[7] == 3) {
        jq('#time').addClass('time-active4')
        //@ts-ignore
        // eslint-disable-next-line no-undef
        _that.class = 'time-active4'
    }
    //@ts-ignore
    // eslint-disable-next-line no-undef
    _that.type = data.split("---")[5]//@ts-ignore
    //@ts-ignore
    // eslint-disable-next-line no-undef
    _that.initIndex = data.split("---")[7]
    //@ts-ignore
    // eslint-disable-next-line no-undef
    _that.next = Number(data.split("---")[6])
    jq('.time1-active').removeClass('time1-active')
    // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
    //@ts-ignore
    // eslint-disable-next-line no-undef
    window.clearTimeout(_that.alertTime);
    // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
    //@ts-ignore
    // eslint-disable-next-line no-undef
    _that.departmentName = data.split("---")[0];
    // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
    //@ts-ignore
    // eslint-disable-next-line no-undef
    _that.creatTime = moment(new Date(Number(data.split("---")[1]))).format(
        "YYYY-MM-DD HH:mm"
    );
    // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
    //@ts-ignore
    // eslint-disable-next-line no-undef
    _that.content = data.split("---")[2];
    // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
    //@ts-ignore
    // eslint-disable-next-line no-undef
    _that.replyContent = data.split("---")[3];
    // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
    //@ts-ignore
    // eslint-disable-next-line no-undef
    _that.taskLogId = data.split("---")[4];
    // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
    //@ts-ignore
    const className = event.target.className
    // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
    //@ts-ignore
    // eslint-disable-next-line no-undef
    event.target.className = className + ' ' + "time1-active";
    // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
    //@ts-ignore
    // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
    //@ts-ignore
    const lefts = Number(jq('#time')[0].style.left.substr(0, jq('#time')[0].style.left.length - 2)) / 100
    // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
    //@ts-ignore
    const left = Number(event.target.style.left.substr(0,  event.target.style.left.length - 3)) + 0.62 + lefts
    //@ts-ignore
    // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
    //@ts-ignore
    document.querySelector(".taskAlert").style.left = `${left}rem`;//@ts-ignore
    // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
    //@ts-ignore
    document.querySelector(".taskAlert").style.top = `${top-1.74}rem`;
    // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
    //@ts-ignore
    // eslint-disable-next-line no-undef
    _that.taskDetailState = true;
    jq(document).one("click", function() {
        // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
        //@ts-ignore
        // eslint-disable-next-line no-undef
        _that.taskDetailState = false;
        jq('.time1-active').removeClass('time1-active')
        //@ts-ignore
        // eslint-disable-next-line no-undef
        jq('#time').removeClass(_that.class)
        // for (const item of document.querySelectorAll(".time1-active")) {
        //     // item.className = "time1";
        //     item.removeClass('time1-active')
        // }
    });
    // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
    //@ts-ignore
    // eslint-disable-next-line no-undef
    event.stopPropagation();
    // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
    //@ts-ignore
    // eslint-disable-next-line no-undef
    _that.alertTime = setTimeout(() => {
        // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
        //@ts-ignore
        // eslint-disable-next-line no-undef
        _that.taskDetailState = false;
        jq('.time1-active').removeClass('time1-active')
        //@ts-ignore
        // eslint-disable-next-line no-undef
        jq('#time').removeClass(_that.class)
        // for (const item of document.querySelectorAll(".time1-active")) {
        //     item.className = "time1";
        // }
    }, 5 * 1000);
};
</script>

