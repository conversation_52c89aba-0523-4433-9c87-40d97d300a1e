<template>
  <div class="container" ref="container"></div>
</template>
<script lang="ts">
  //@ts-ignore
  import AMap from "AMap";
  //@ts-ignore
  import { Vue, Component, Prop, Watch } from "vue-property-decorator";
  import { Icon } from "ant-design-vue";
  //@ts-ignore
  import jinniuStreet from '@/assets/map-geojson/jinniu_street';

  import emergencyBg from "../../assets/emergency/emergency_active.png";
  import dqzcIcon from '../../assets/emergency/<EMAIL>'
  import dqzcXIcon from '../../assets/emergency/<EMAIL>'
  import dqcbIcon from '../../assets/emergency/<EMAIL>'
  import dqcbXIcon from '../../assets/emergency/<EMAIL>'
  import szzcIcon from '../../assets/emergency/<EMAIL>'
  import szzcXIcon from '../../assets/emergency/<EMAIL>'
  import szcbIcon from '../../assets/emergency/<EMAIL>'
  import szcbXIcon from '../../assets/emergency/<EMAIL>'
  import gdzcIcon from '../../assets/emergency/<EMAIL>'
  import gdzcXIcon from '../../assets/emergency/<EMAIL>'
  import gdcbIcon from '../../assets/emergency/<EMAIL>'
  import gdcbXIcon from '../../assets/emergency/<EMAIL>'
  import stationIconPic from "@/assets/<EMAIL>";
  import carSelected from "@/assets/car-selected.png";
  import laji from "../../assets/<EMAIL>";
  import carIcons from "@/assets/<EMAIL>";
  import { webglcontextlostHandle } from "@/utils/index"

  interface MapCenter {
    lng: number;
    lat: number;
  }

  // 存储地图实例
  let maps: any = null

  @Component({
    name: "EmergencyMap",
    components: {
      AIcon: Icon
    }
  })

  export default class extends Vue {
    // private maps: any = "";

    @Prop({
      required: false,
      type: Number,
      default: 12.5
    })
    private mapZoom!: number;
    @Prop({ required: false })
    mapStyle!: string;
    @Prop({ required: false, default: "3D" }) viewMode!: string;
    @Prop({ required: false, default: () => {} }) viewCenter!: MapCenter;
    @Prop({ required: false, default: () => [] }) emergencyList!: any[];
    @Prop({ required: false, default: () => [] }) emergencyStationList!: any[];
    @Prop({ required: false, default: () => {} }) reportDetails!: any;
    @Prop({ required: false, default: () => {} }) rangeObj!: any;
    @Prop({ required: false, default: () => {} }) defaultCameraValue!: any;
    @Prop({ required: false, default: () => {} }) waterStationId!: any;
    @Prop({ required: false, default: () => {} }) airStationId!: any;
    @Prop({ required: false, default: () => {} }) pollutionStationId!: any;
    @Prop({ required: false, default: () => {} }) carId!: any;
    @Prop({ required: false, default: 1 }) isShow!: number;

    @Prop({
      required: false,
      default: () => {
        return {}
      }
    })
    newCarPoint!: any[]
    @Prop({
      required: false,
      default: () => {
        return {}
      }
    })
    oldCaPoint!: any[]
    @Watch('newCarPoint', { immediate: false, deep: true })
    private onNewCarPoint(newValue: any, oldValue: any) {
      if (newValue) {
        let marker: any
        this.clearEmergencyStationListMarker.forEach((item: any) => {
          if (item.w.data.carId == newValue.carId) {
            marker = item
          }
        })
        const lngLat = new AMap.LngLat(newValue.longitude, newValue.latitude)
        marker.moveTo(lngLat, 200)
      }
    }
    @Watch("rangeObj", { immediate: true, deep: true })
    private onrangeObj(newValue: any, oldValue: any) {
      if (newValue) {
        this.createCircle(newValue.lng, newValue.lat, newValue.number);
      }
    }

    @Watch("emergencyList", { immediate: true, deep: true })
    private onEmergencyList(newValue: any, oldValue: any) {
      if (newValue) {
        this.emergencyList = newValue;
        // this.createEmergencyMarker();
      }
    }

    private reportDetailsText: any = null;
    @Watch("reportDetails", { immediate: true, deep: true })
    private onReportDetails(newValue: any, oldValue: any) {
      if (newValue) {
        if (this.reportDetailsText != null) {
          maps.remove(this.reportDetailsText);
        }
        this.reportDetails = newValue;
        const markerIcon = new AMap.Icon({
          size: new AMap.Size(27, 35),
          image: emergencyBg,
          imageSize: new AMap.Size(27, 35),
          imageOffset: new AMap.Pixel(0, 0)
        });
        this.reportDetailsText = new AMap.Marker({
          map: maps,
          icon: markerIcon,
          position: [newValue.longitude, newValue.latitude],
          offset: new AMap.Pixel(0, 0),
          zIndex: 9999
        });
      }
    }

    @Watch("emergencyStationList", { immediate: true, deep: true })
    private onEmergencyStationList(newValue: any, oldValue: any) {
      if (newValue) {
        this.emergencyStationList = newValue;
        this.createEmergencyStationList();
      }
    }
    @Watch("defaultCameraValue", { immediate: true, deep: true })
    private onDefaultCameraValue(newValue: any, oldValue: any) {
      if (newValue && this.clearEmergencyStationListMarker.length) {
        this.cameraClick();
      }
    }
    @Watch("waterStationId", { immediate: true, deep: true })
    private onWaterStationId(newValue: any, oldValue: any) {
      if (newValue  && this.clearEmergencyStationListMarker.length) {
        this.handleMarker(newValue);
      }
    }
    @Watch("airStationId", { immediate: true, deep: true })
    private onAirStationId(newValue: any, oldValue: any) {
      if (newValue  && this.clearEmergencyStationListMarker.length) {
        this.handleMarker(newValue);
      }
    }
    @Watch("pollutionStationId", { immediate: true, deep: true })
    private onPollutionStationId(newValue: any, oldValue: any) {
      if (newValue  && this.clearEmergencyStationListMarker.length) {
        this.handleMarker(newValue);
      }
    }
    @Watch("carId", { immediate: true, deep: true })
    private onCarId(newValue: any, oldValue: any) {
      if (newValue  && this.clearEmergencyStationListMarker.length) {
        this.carClick();
      }
    }
    @Watch("isShow", { immediate: true, deep: true })
    private onIsShow(newValue: any, oldValue: any) {
      if (newValue  && this.clearEmergencyStationListMarker.length) {
        this.handleMarker(newValue == 2 ? this.waterStationId : newValue == 1 ? this.airStationId : this.pollutionStationId);
      }
    }

    mounted() {
      // eslint-disable-next-line @typescript-eslint/no-this-alias
      (window as any)._this = this;
      setTimeout(_ => {
        this.map();
        maps.on("complete", () => {
          this.createOverlay();
          this.creatGeojson()
          const waterCenterPosition = new AMap.LngLat(104.04, 30.685);
          maps.setCenter(waterCenterPosition);
          // this.createEmergencyHome();
          // this.createEmergencyMarker();
        })

      }, 50)
    }


    beforeDestroy() {
      maps.clearMap();

      if (this.emergencyCircle !== null) {
        maps.remove(this.emergencyCircle);
        this.emergencyCircle = null
      }

      // 销毁地图
      maps.destroy()
      maps = null;
      (window as any)._this = null;
    }
    // 添加街道划分区域地图数据
    private creatGeojson() {
      const map = maps;
      // eslint-disable-next-line @typescript-eslint/no-this-alias
      const that = this;
      // 添加金牛区地理信息数据 1
      const geojson = new AMap.GeoJSON({
        geoJSON: jinniuStreet,
        getPolygon: function(geojson: any, lnglats: any) {
          AMap.convertFrom(geojson.geometry.coordinates[0], "gps", function(
                  status: any,
                  result: any
          ) {
            // const area: any = AMap.GeometryUtil.ringArea(lnglats[0]);
            if (geojson.properties.name !== "金牛区") {
              const text = new AMap.Text({
                text: geojson.properties.name,
                anchor: "center", // 设置文本标记锚点
                draggable: false,
                cursor: "pointer",
                angle: 0,
                style: {
                  padding: ".75rem 1.25rem",
                  "margin-bottom": "1rem",
                  "border-radius": ".25rem",
                  "background-color": "transparent",
                  "border-width": 0,
                  "text-align": "center",
                  "pointer-events": "none",
                  "font-size": "14px",
                  color: "#36E9EF"
                },
                position: [
                  geojson.properties.center.lng,
                  geojson.properties.center.lat
                ]
              });
              text.setMap(map);
            }

            if (result.info === "ok") {
              const polygon = new AMap.Polygon({
                path: result.locations,
                // strokeColor: "#0ea9f9",
                strokeColor: "#2fbeb5",
                strokeWeight: 2,
                strokeOpacity: 1,
                fillOpacity: 0.5, // 多边形填充透明度
                fillColor: "rgba(0,49,113, 0.15)",
                // that.enterType === EnterType.AIR
                //   ? that.mapColor
                //   : "rgba(0,49,113, 0.15)",
                zIndex: 10
              });
              map.add(polygon);
            }
          });
        }
      });

      // 添加金牛区地理信息数据 2
      geojson.setMap(map);
    }
    // 高德地图
    private map(): void {
      // eslint-disable-next-line @typescript-eslint/no-this-alias
      const _this = this;
      // 初始化地图
      const map = new AMap.Map(this.$refs.container, {
        center: [104.05, 30.73],
        position: [104.05, 30.73],
        zoom: this.mapZoom,
        viewMode: this.viewMode,
        pitch: 40,
        zoomEnable: true,
        dragEnable: true,
        zooms: [12, 18]
      });

      // 处理webgl上下文丢失事件
      webglcontextlostHandle.call(this)

      // 设置地图样式
      map.setMapStyle(this.mapStyle);
      const trafficLayer = new AMap.TileLayer.Traffic({
        zIndex: 1
      });
      trafficLayer.setMap(map);
      maps = map;
    }

    // 添加行政区外的覆盖物
    private createOverlay() {
      const map = maps;
      // eslint-disable-next-line @typescript-eslint/no-this-alias
      const that = this;
      // 添加金牛区地理信息数据 3
      new AMap.DistrictSearch({
        extensions: "all",
        subdistrict: 0
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
      }).search("金牛区", function(status: any, result: any) {
        // 外多边形坐标数组和内多边形坐标数组
        const outer = [
          new AMap.LngLat(-360, 90, true),
          new AMap.LngLat(-360, -90, true),
          new AMap.LngLat(360, -90, true),
          new AMap.LngLat(360, 90, true)
        ];
        const holes = result.districtList[0].boundaries;

        const pathArray: any = [outer];
        // eslint-disable-next-line prefer-spread
        pathArray.push.apply(pathArray, holes);
        const polygon = new AMap.Polygon({
          pathL: pathArray,
          //线条颜色，使用16进制颜色代码赋值。默认值为#006600
          strokeColor: "rgb(255,255,255)",
          strokeWeight: 0,
          //轮廓线透明度，取值范围[0,1]，0表示完全透明，1表示不透明。默认为0.9
          strokeOpacity: 0,
          //多边形填充颜色，使用16进制颜色代码赋值，如：#FFAA00
          fillColor: "rgba(3,4,130)",
          // fillColor: "rgba(4,20,50)",
          // fillColor: "#0A1C5F",
          //多边形填充透明度，取值范围[0,1]，0表示完全透明，1表示不透明。默认为0.9
          fillOpacity: 0,
          //轮廓线样式，实线:solid，虚线:dashed
          strokeStyle: "solid",
          strokeDasharray: [10, 2, 10]
        });
        polygon.setPath(pathArray);
        map.add(polygon);
      });
    }

    // 添加应急指挥点
    private clearEmergencyStationListMarker: any[] = [];
    private emergencyAirText: any = [];
    private emergencyAirMarker: any = null;
    private createEmergencyStationList() {
      const map = maps;
      if (this.clearEmergencyStationListMarker.length > 0) {
        map.remove(this.clearEmergencyStationListMarker);
        this.clearEmergencyStationListMarker = [];
      }
      if (this.emergencyAirText.length > 0) {
        map.remove(this.emergencyAirText);
        this.emergencyAirText = [];
      }
      if (this.videoActiveMarker !== null) {
        maps.remove(this.videoActiveMarker);
      }
      if (this.emergencyAirMarker !== null) {
        map.remove(this.emergencyAirMarker);
        this.emergencyAirMarker = null;
      }
      // 工地正常
      const gdzc = new AMap.Icon({
        size: new AMap.Size(32, 30),
        image: gdzcIcon,
        imageSize: new AMap.Size(32, 30),
        imageOffset: new AMap.Pixel(0, 0)
      });
      const gdzcx = new AMap.Icon({
        size: new AMap.Size(32, 30),
        image: gdzcXIcon,
        imageSize: new AMap.Size(32, 30),
        imageOffset: new AMap.Pixel(0, 0)
      });
      // 工地超标
      const gdcb = new AMap.Icon({
        size: new AMap.Size(32, 30),
        image: gdcbIcon,
        imageSize: new AMap.Size(32, 30),
        imageOffset: new AMap.Pixel(0, 0)
      });
      const gdcbx = new AMap.Icon({
        size: new AMap.Size(32, 30),
        image: gdcbXIcon,
        imageSize: new AMap.Size(32, 30),
        imageOffset: new AMap.Pixel(0, 0)
      });
      const szzc = new AMap.Icon({
        size: new AMap.Size(32, 30),
        image: szzcIcon,
        imageSize: new AMap.Size(32, 30),
        imageOffset: new AMap.Pixel(0, 0)
      });
      // 水质
      const szzcx = new AMap.Icon({
        size: new AMap.Size(32, 30),
        image: szzcXIcon,
        imageSize: new AMap.Size(32, 30),
        imageOffset: new AMap.Pixel(0, 0)
      });
      const szcb = new AMap.Icon({
        size: new AMap.Size(32, 30),
        image: szcbIcon,
        imageSize: new AMap.Size(32, 30),
        imageOffset: new AMap.Pixel(0, 0)
      });
      const szcbx = new AMap.Icon({
        size: new AMap.Size(32, 30),
        image: szcbXIcon,
        imageSize: new AMap.Size(32, 30),
        imageOffset: new AMap.Pixel(0, 0)
      });
      // 大气
      const dqzc = new AMap.Icon({
        size: new AMap.Size(32, 30),
        image: dqzcIcon,
        imageSize: new AMap.Size(32, 30),
        imageOffset: new AMap.Pixel(0, 0)
      });
      const dqzcx = new AMap.Icon({
        size: new AMap.Size(32, 30),
        image: dqzcXIcon,
        imageSize: new AMap.Size(32, 30),
        imageOffset: new AMap.Pixel(0, 0)
      });
      const dqcb = new AMap.Icon({
        size: new AMap.Size(32, 30),
        image: dqcbIcon,
        imageSize: new AMap.Size(32, 30),
        imageOffset: new AMap.Pixel(0, 0)
      });
      const dqcbx = new AMap.Icon({
        size: new AMap.Size(32, 30),
        image: dqcbXIcon,
        imageSize: new AMap.Size(32, 30),
        imageOffset: new AMap.Pixel(0, 0)
      });
      // 监控
      const cameraIcon = new AMap.Icon({
        size: new AMap.Size(28, 28),
        // 图标的取图地址
        image: stationIconPic,
        // 图标所用图片大小
        // imageSize: new AMap.Size(28, 35),
        imageSize: new AMap.Size(28, 28),
        // 图标取图偏移量
        imageOffset: new AMap.Pixel(0, 0)
      });
      // 车辆
      const carIcon = new AMap.Icon({
        // 图标尺寸
        size: new AMap.Size(36, 20),
        // 图标的取图地址
        image: laji,
        // a.amap.com/jsapi_demos/static/demo-center/icons/dir-marker.png
        // 图标所用图片大小
        imageSize: new AMap.Size(36, 20),
        // 图标取图偏移量
        imageOffset: new AMap.Pixel(0, 0)
      });
      this.emergencyStationList.forEach((marker: any) => {
        let m: any = null;
        if (marker.stationType === 2) {
          m = new AMap.Marker({
            map: map,
            icon: marker.isAlarm ? szcb : szzc,
            position: [marker.longitude, marker.latitude],
            offset: new AMap.Pixel(-16, -15),
            data: marker,
            zIndex: 99999
          })
        } else if (marker.stationType === 1) {
          m = new AMap.Marker({
            map: map,
            icon: marker.pollutantState ? dqcb:  dqzc,
            position: [marker.longitude, marker.latitude],
            offset: new AMap.Pixel(-16, -15),
            data: marker,
            zIndex: 99999
          })
        } else if (marker.stationType === 3) {
          m = new AMap.Marker({
            map: map,
            icon: marker.pollutantState ? gdcb:  gdzc,
            position: [marker.longitude, marker.latitude],
            offset: new AMap.Pixel(-16, -15),
            data: marker,
            zIndex: 99999
          })
        } else if (marker.stationType === 4) {
          m = new AMap.Marker({
            map: map,
            icon: cameraIcon,
            position: [marker.longitude, marker.latitude],
            offset: new AMap.Pixel(-36, -20),
            data: marker,
            zIndex: 99999
          })
        }  else if (marker.stationType === 5) {
          m = new AMap.Marker({
            map: map,
            icon: carIcon,
            position: [marker.longitude, marker.latitude],
            offset: new AMap.Pixel(-18, -10),
            data: marker,
            zIndex: 99999
          })
          this.carClick();
        }
        this.clearEmergencyStationListMarker.push(m);
        AMap.event.addListener(m, "click", this.emergencyStationListClick);
      });
    }
    private emergencyStationListValue: any = null;
    private emergencyStationListClick(e: any) {
      const data = e.target.w.data
      //@ts-ignore
      // eslint-disable-next-line no-undef
      _this.$bus.emit("stationType", {
        type: data.stationType,
        stationId: data.stationCode
      });
      this.markerClick(data);
    }
    private markerClick(data1:any) {
      // 工地正常
      const gdzc = new AMap.Icon({
        size: new AMap.Size(32, 30),
        image: gdzcIcon,
        imageSize: new AMap.Size(32, 30),
        imageOffset: new AMap.Pixel(0, 0)
      });
      const gdzcx = new AMap.Icon({
        size: new AMap.Size(32, 30),
        image: gdzcXIcon,
        imageSize: new AMap.Size(32, 30),
        imageOffset: new AMap.Pixel(0, 0)
      });
      // 工地超标
      const gdcb = new AMap.Icon({
        size: new AMap.Size(32, 30),
        image: gdcbIcon,
        imageSize: new AMap.Size(32, 30),
        imageOffset: new AMap.Pixel(0, 0)
      });
      const gdcbx = new AMap.Icon({
        size: new AMap.Size(32, 30),
        image: gdcbXIcon,
        imageSize: new AMap.Size(32, 30),
        imageOffset: new AMap.Pixel(0, 0)
      });
      const szzc = new AMap.Icon({
        size: new AMap.Size(32, 30),
        image: szzcIcon,
        imageSize: new AMap.Size(32, 30),
        imageOffset: new AMap.Pixel(0, 0)
      });
      const szzcx = new AMap.Icon({
        size: new AMap.Size(32, 30),
        image: szzcXIcon,
        imageSize: new AMap.Size(32, 30),
        imageOffset: new AMap.Pixel(0, 0)
      });
      const szcb = new AMap.Icon({
        size: new AMap.Size(32, 30),
        image: szcbIcon,
        imageSize: new AMap.Size(32, 30),
        imageOffset: new AMap.Pixel(0, 0)
      });
      const szcbx = new AMap.Icon({
        size: new AMap.Size(32, 30),
        image: szcbXIcon,
        imageSize: new AMap.Size(32, 30),
        imageOffset: new AMap.Pixel(0, 0)
      });
      const dqzc = new AMap.Icon({
        size: new AMap.Size(32, 30),
        image: dqzcIcon,
        imageSize: new AMap.Size(32, 30),
        imageOffset: new AMap.Pixel(0, 0)
      });
      const dqzcx = new AMap.Icon({
        size: new AMap.Size(32, 30),
        image: dqzcXIcon,
        imageSize: new AMap.Size(32, 30),
        imageOffset: new AMap.Pixel(0, 0)
      });
      const dqcb = new AMap.Icon({
        size: new AMap.Size(32, 30),
        image: dqcbIcon,
        imageSize: new AMap.Size(32, 30),
        imageOffset: new AMap.Pixel(0, 0)
      });
      const dqcbx = new AMap.Icon({
        size: new AMap.Size(32, 30),
        image: dqcbXIcon,
        imageSize: new AMap.Size(32, 30),
        imageOffset: new AMap.Pixel(0, 0)
      });
      const carIcon = new AMap.Icon({
        size: new AMap.Size(36, 20),
        image: laji,
        imageSize: new AMap.Size(36, 20),
        imageOffset: new AMap.Pixel(0, 0)
      });
      this.clearEmergencyStationListMarker.forEach((item:any) => {
        if (!item || !item.w) {
          return
        }
        const data = item.w.data
        let icon:any = null
        if (data.id === data1.id && this.isShow == data1.stationType) {
          icon = data.stationType == 1 ? (data.pollutantState ? dqcbXIcon : dqzcXIcon)
                  : data.stationType == 2 ?  (data.isAlarm ? szcbXIcon : szzcXIcon) : data.stationType == 3 ?  (data.pollutantState ? gdcbXIcon : gdzcXIcon) : ''
          item.setIcon(icon)
        } else {
          icon = data.stationType == 1 ? (data.pollutantState ? dqcbIcon : dqzcIcon)
                  : data.stationType == 2 ?  (data.isAlarm ? szcbIcon : szzcIcon) : data.stationType == 3 ? (data.pollutantState ? gdcbIcon : gdzcIcon) : ''
          item.setIcon(icon)
        }
        if (data.stationType == 4) {
          this.cameraClick()
        }
        if (data.stationType == 5) {
          this.carClick()
        }
      })
    }
    private handleMarker(id:any) {
      const item = this.clearEmergencyStationListMarker.find((item:any) => id === item.w.data.id)
      this.markerClick(item.w.data)
    }
    private carClick() {
      const carIcon = new AMap.Icon({
        // 图标尺寸
        size: new AMap.Size(36, 20),
        // 图标的取图地址
        image: laji,
        // a.amap.com/jsapi_demos/static/demo-center/icons/dir-marker.png
        // 图标所用图片大小
        imageSize: new AMap.Size(36, 20),
        // 图标取图偏移量
        imageOffset: new AMap.Pixel(0, 0)
      });
      const lajiSelected = new AMap.Icon({
        size: new AMap.Size(50, 50),
        image: carIcons,
        imageSize: new AMap.Size(340, 352),
        imageOffset: new AMap.Pixel(-233.5, -176)
      })
      this.clearEmergencyStationListMarker.forEach((item:any) => {
        if (!item || !item.w) {
          return
        }
        const data = item.w.data
        if (data.stationType == 5) {
          if (data.carId === this.carId) {
            item.setIcon(lajiSelected)
            item.setOffset(new AMap.Pixel(-25, -25))
          } else {
            item.setIcon(carIcon)
            item.setOffset(new AMap.Pixel(-18, -10))
          }
        }
      })
    }
    private cameraClick() {
      const cameraIcon = new AMap.Icon({
        size: new AMap.Size(28, 28),
        // 图标的取图地址
        image: stationIconPic,
        // 图标所用图片大小
        // imageSize: new AMap.Size(28, 35),
        imageSize: new AMap.Size(28, 28),
        // 图标取图偏移量
        imageOffset: new AMap.Pixel(0, 0)
      });
      const carActive = new AMap.Icon({
        // 图标尺寸
        size: new AMap.Size(36, 36),
        // 图标的取图地址
        image: carSelected,
        // a.amap.com/jsapi_demos/static/demo-center/icons/dir-marker.png
        // 图标所用图片大小
        imageSize: new AMap.Size(36, 36),
        // 图标取图偏移量
        imageOffset: new AMap.Pixel(0, 0)
      });
      this.clearEmergencyStationListMarker.forEach((item:any) => {
        if (!item || !item.w) {
          return
        }
        const data = item.w.data
        if (data.stationType == 4) {
          if (data.monitorId === this.defaultCameraValue) {
            if (this.videoActiveMarker !== null) {
              maps.remove(this.videoActiveMarker);
            }
            this.videoActiveMarker = new AMap.Marker({
              map: maps,
              position: [item.getPosition().lng, item.getPosition().lat],
              icon: carActive,
              offset: new AMap.Pixel(-40, -24),
              autoRotation: true,
              zIndex: 99998
            });
            item.setIcon(cameraIcon)
          } else {
            item.setIcon(cameraIcon)
          }
        }
      })
    }
    private videoActiveMarker: any = null;
    // 添加应急指挥距离范围
    private emergencyCircle: any = null;
    private createCircle(lng: any, lat: any, number: any) {
      if (this.emergencyCircle !== null) {
        maps.remove(this.emergencyCircle);
      }
      this.emergencyCircle = new AMap.Circle({
        center: new AMap.LngLat(lng, lat), // 圆心位置
        radius: number, // 圆半径
        fillColor: "rgba(240,15,26, .26)", // 圆形填充颜色
        strokeColor: "transparent", // 描边颜色
        strokeWeight: 0 // 描边宽度
      });
      maps.add(this.emergencyCircle);
    }

    // 添加了应急指挥的线
    private createLine(longitude: any, latitude: any, type: any, height: any) {
      const map = maps;
      function pointOnCubicBezier(cp: any, t: any) {
        let ax: any, bx: any, cx: any;
        let ay: any, by: any, cy: any;
        let tSquared: any, tCubed: any;
        // eslint-disable-next-line prefer-const
        cx = 3.0 * (cp[1].lng - cp[0].lng);
        // eslint-disable-next-line prefer-const
        bx = 3.0 * (cp[2].lng - cp[1].lng) - cx;
        // eslint-disable-next-line prefer-const
        ax = cp[3].lng - cp[0].lng - cx - bx;

        // eslint-disable-next-line prefer-const
        cy = 3.0 * (cp[1].lat - cp[0].lat);
        // eslint-disable-next-line prefer-const
        by = 3.0 * (cp[2].lat - cp[1].lat) - cy;
        // eslint-disable-next-line prefer-const
        ay = cp[3].lat - cp[0].lat - cy - by;

        // eslint-disable-next-line prefer-const
        tSquared = t * t;
        // eslint-disable-next-line prefer-const
        tCubed = tSquared * t;

        const lng = ax * tCubed + bx * tSquared + cx * t + cp[0].lng;
        const lat = ay * tCubed + by * tSquared + cy * t + cp[0].lat;

        return new AMap.LngLat(lng, lat);
      }

      function computeBezier(points: any, numberOfPoints: any) {
        let dt;
        let i;
        const curve = [];

        // eslint-disable-next-line prefer-const
        dt = 1.0 / (numberOfPoints - 1);

        for (i = 0; i < numberOfPoints; i++) {
          curve[i] = pointOnCubicBezier(points, i * dt);
        }
        return curve;
      }
      function getEllipseHeight(count: any, maxHeight: any, minHeight: any) {
        const height = [];
        const radionUnit = Math.PI / 180;

        for (let i = 0; i < count; i++) {
          const radion = i * radionUnit;
          height.push(minHeight + Math.sin(radion) * maxHeight);
        }
        return height;
      }
      const points = [
        new AMap.LngLat(longitude, latitude),
        new AMap.LngLat(longitude, latitude),
        new AMap.LngLat(104.052269, 30.691354),
        new AMap.LngLat(104.052269, 30.691354)
      ];
      const object3Dlayer = new AMap.Object3DLayer();
      const meshLine = new AMap.Object3D.MeshLine({
        path: computeBezier(points, 180),
        height: getEllipseHeight(180, height, 0),
        color: type ? "#24FFA4" : "#EA160B", //#EA160B
        width: 7
      });

      meshLine.transparent = true;
      object3Dlayer.add(meshLine);
      meshLine["backOrFront"] = "both";
      map.add(object3Dlayer);
    }
  }
</script>

<style lang="less">
  @import url(./map.less);
</style>
<style lang="less" scoped>
.container {
  width: 100%;
  height: 100%;
}
.marker-selected {
  width: 0.5rem;
  height: 0.5rem;
  background-image: url("../../assets/car-selected.png");
  background-size: 100% 100%;
}
</style>
