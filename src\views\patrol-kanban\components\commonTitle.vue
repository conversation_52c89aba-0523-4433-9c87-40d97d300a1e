<template>
  <div class="common-title-container">
    <div class="title-box">
      <div class="title-text">{{ title }}</div>
      <div>
        <slot name="title"></slot>
      </div>
    </div>
    <div class="line"></div>
  </div>
</template>

<script>
export default {
  name: "",
  props: {
    title: {
      type: String,
      default: "",
    },
  },
  data() {
    return {};
  },
  created() {},
  methods: {},
  computed: {},
  watch: {},
  components: {},
};
</script>

<style lang="less" scoped>
.common-title-container {
  .title-box {
    display: flex;
    justify-content: space-between;
    .title-text {
      font-size: 0.2rem;
      font-family: Source Han Sans CN;
      font-weight: 500;
      text-shadow: 0 0 5px blue, 0 0 5px blue;
    }
  }
  .line {
    margin-top: 10px;
    width: 354px;
    height: 10px;
    background: url("~@/assets/biaoti.png") no-repeat center;
  }
}
</style>
