import request from "@/utils/request";
import { AxiosPromise } from "axios";
// 获取所有水质站点
export function getAllAirStationList(): AxiosPromise<any> {
    return request({
        url: "/water/getAllStation",
        method: "get",
    });
}
// 获取站点监测项
export function listStationItem(params: any): AxiosPromise<any> {
    return request({
        url: "/water/listStationItem",
        method: "get",
        params
    });
}
// 获取站点24小时站点监测列表
export function stationItemMonitorTrendHour(params: any): AxiosPromise<any> {
    return request({
        url: "/water/stationItemMonitorTrendHour",
        method: "get",
        params
    });
}
// 获取指定站点附近污染企业站点列表
export function listStationRangeEnterprise(data: any): AxiosPromise<any> {
    return request({
        url: "/water/listStationRangeEnterprise",
        method: "post",
        data
    });
}
// 水质站点成因分析
export function originAnalyse(params: any): AxiosPromise<any> {
    return request({
        url: "/water/originAnalyse",
        method: "get",
        params
    });
}
// 获取污染企业的监测值列表 并返回告警阈值
export function listSewageMonitorTrendHour(params: any): AxiosPromise<any> {
    return request({
        url: "/water/listSewageMonitorTrendHour",
        method: "get",
        params
    });
}
// 获取所有排口
export function listDrain(): AxiosPromise<any> {
    return request({
        url: "/water/drain/bigData/listDrain",
        method: "get"
    });
}
