<template>
  <div :id="id" :class="className" :style="{ height: height, width: width }" />
</template>

<script lang="ts">
import * as echarts from 'echarts'
import { Component, Prop } from "vue-property-decorator";
import { mixins } from "vue-class-component";
import ResizeMixin from "./mixins/resize";

@Component({
  name: "TaskPie"
})
export default class extends mixins(ResizeMixin) {
  @Prop({ default: "chart" }) private className!: string;
  @Prop({ default: "chart" }) private id!: string;
  @Prop({ default: "120px" }) private width!: string;
  @Prop({ default: "120px" }) private height!: string;
  @Prop({ default: "#4AE5D2" }) private valueColor!: string;
  @Prop({ default: 0 }) private count!: number;
  @Prop({ default: 0 }) private total!: number;

  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
  }

  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.clear();
    this.chart.dispose();
    this.chart = null;
  }

  private initChart() {

    if(this.chart === null || this.chart === undefined) {
      this.chart = echarts.init(
        document.getElementById(this.id) as HTMLDivElement
      );
    }
    this.chart.setOption({
      title: {
        text: `${this.count}个`,
        x: "center",
        y: "center",
        textStyle: {
          fontWeight: "normal",
          color: "#ffffff",
          fontSize: 20
        }
      },
      // backgroundColor: "#011128",
      color: [this.valueColor, "#193E8D", "#3B7984"],
      series: [
        {
          name: "Line 1",
          type: "pie",
          clockWise: false,
          radius: [45, 53],
          itemStyle: {
            normal: {
              label: {
                show: false
              },
              labelLine: {
                show: false
              }
            }
          },
          hoverAnimation: false,
          data: [
            {
              value: this.count
            },
            {
              value: this.total - this.count
            }
          ]
        }
        // {
        //   name: "Line 2",
        //   type: "pie",
        //   animation: false,
        //   clockWise: false,
        //   radius: [60, 58],
        //   itemStyle: {
        //     normal: {
        //       label: {
        //         show: false
        //       },
        //       labelLine: {
        //         show: false
        //       }
        //     }
        //   },
        //   hoverAnimation: false,
        //   tooltip: {
        //     show: false
        //   },
        //   data: [
        //     {
        //       value: 100
        //     },
        //     {
        //       value: 0
        //     }
        //   ]
        // }
      ]
    } as any);
  }
}
</script>
