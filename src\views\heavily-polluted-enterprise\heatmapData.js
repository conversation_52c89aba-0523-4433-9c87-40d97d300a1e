const heatmapData = [
  { lng: '104.014733', lat: '30.714836', count: 5 },
  { lng: '104.015033', lat: '30.716644', count: 10 },
  { lng: '104.009004', lat: '30.716792', count: 3 },
  { lng: '104.009926', lat: '30.716958', count: 7 },
  { lng: '104.011235', lat: '30.712364', count: 6 },
  { lng: '104.015076', lat: '30.711571', count: 4 },
  { lng: '104.01027', lat: '30.718304', count: 9 },
  { lng: '104.02072', lat: '30.710833', count: 2 },
  { lng: '104.020655', lat: '30.716921', count: 1 },
]

function randomLatLng() {
  // Math.random()*(n-m)+m
  // Math.ceil(Math.random()*100)
  for (let i = 0; i < 20; i++) {
    heatmapData.push({lng:Math.random()*(104.53-102.54)+102.54,lat:Math.random()*(31.26-30.05)+30.05,count:Math.ceil(Math.random()*100)})
  }
}
randomLatLng()

export {heatmapData}