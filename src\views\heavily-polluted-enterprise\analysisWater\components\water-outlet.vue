<style scoped lang="less">
@font-face {
  font-family: "DS-DIGII";
  src: url("~@/assets/font/DS-DIGII.ttf");
  font-display: swap;
}
.water-outlet {
  // padding: 0.2rem 0.2rem 0.2rem;
  padding: 0;
  position: relative;
  .water-outlet-content {
    width: 100%;
    height: 100%;
    background-image: url("~@/assets/kqbk.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    padding: 0.65rem 0.5rem 0.35rem;
    .details-region {
      padding: 0 0.5rem;
      margin-bottom: 0.15rem;
      .region-box {
        padding: 0.05rem 0.25rem;
        box-sizing: border-box;
        background: rgba(6, 30, 101, 1);
        border: 1px solid rgba(2, 174, 210, 1);
        display: flex;
        justify-content: space-between;
        align-items: center;
        .street {
          width: 3rem;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .site-type {
          width: 3rem;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .site-name {
          width: 3rem;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .select-time {
          width: 3.8rem;
          font-family: fontnameRegular;
          font-size: 0.36rem;
          margin-left: 0.35rem;
        }
        .line {
          width: 0.02rem;
          height: 0.7rem;
          background-image: url("~@/assets/xgx.png");
          background-repeat: no-repeat;
          background-size: 100% 100%;
        }
        .time {
          width: 3rem;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 0 0.25rem;
          box-sizing: content-box;
          flex: 1;
        }
        .name,
        .type {
          width: 3rem;
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .type {
          > div {
            width: 1.6rem;
            height: 0.44rem;
            background: #0084ff;
            border-radius: 0.44rem;
            line-height: 0.44rem;
            font-size: 0.24rem;
            display: flex;
            justify-content: center;
          }
        }
        .name {
          padding: 0 0.25rem;
          box-sizing: content-box;
        }
        .address {
          padding: 0 0.25rem;
          box-sizing: content-box;
          width: 6rem;
          font-size: 0.24rem;
          display: flex;
          align-items: center;
          flex: 1.5;
          // padding-left: 0.5rem;
          .address-text {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            flex: 1;
          }
          .icon {
            width: 0.3rem;
            height: 0.3rem;
            margin-right: 0.28rem;
            background-image: url("~@/assets/<EMAIL>");
            background-size: 100% 100%;
          }
        }
      }
    }
    .details-middle {
      padding: 0 0.5rem;
      display: flex;
      align-items: center;
      margin-top: 0.3rem;
      .left {
        height: calc(3.68rem - calc(10.8rem - 1080px) / 2);
        width: 6.76rem;
        background-size: 100% 100%;
        background-image: url("~@/assets/outlet/<EMAIL>");
        padding: 0.22rem 0.27rem 0.3rem 0.3rem;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        .video {
          width: 100%;
          height: 100%;
          background-size: 100% 100%;
          padding: 0.28rem 0.7rem;
          background-image: url("~@/assets/outlet/<EMAIL>");
          .video-area {
            width: 100%;
            height: 100%;
            .player-wrapper {
              width: 100% !important;
              height: 100% !important;
            }
            .not-online {
              width: 100%;
              height: 100%;
              background-size: 100% 100%;
              text-align: center;
              font-size: 0.16rem;
              color: black;
              line-height: calc(2.68rem - calc(10.8rem - 1080px) / 2);
            }
          }
        }
        .video-info {
          margin-left: 0.25rem;
          height: 100%;
          display: flex;
          flex-direction: column;
          width: 2.86rem;
          .title {
            margin-top: 0.2rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            background-size: 100% 100%;
            height: 0.22rem;
            background-image: url("~@/assets/outlet/<EMAIL>");
            padding-left: 0.2rem;
            padding-bottom: 0.2rem;
            span {
              &:nth-child(1) {
                font-size: 17px;
                font-weight: 500;
                font-style: italic;
                color: #60e4ff;
              }
              &:nth-child(2) {
                color: #b8d3f1;
              }
            }
          }
          .weather {
            margin-top: 0.27rem;
            height: 1rem;
            background-size: 100% 100%;
            background-image: url("~@/assets/outlet/<EMAIL>");
            display: flex;
            align-items: center;
            justify-content: space-between;
            .lists {
              display: flex;
              flex-direction: column;
              justify-content: space-between;
              align-items: center;
              position: relative;
              width: 33.3%;
              height: 100%;
              padding: 0.1rem 0;
              img {
                width: 0.206rem;
                height: 0.207rem;
              }
              .line {
                width: 1px;
                height: 0.72rem;
                position: absolute;
                right: 0;
              }
              span {
                &:nth-child(1) {
                  font-size: 0.11rem;
                  color: #ffffff;
                }
                &:nth-child(2) {
                  font-size: 0.11rem;
                  font-weight: 500;
                  color: #b8d3f1;
                }
              }
            }
          }
          .params {
            margin-top: 0.28rem;
            display: flex;
            align-items: center;
            justify-content: space-around;
            .list {
              display: flex;
              flex-direction: column;
              span {
                &:nth-child(1) {
                  display: inline-block;
                  background-size: 100% 100%;
                  background-image: url("~@/assets/outlet/<EMAIL>");
                  width: 0.72rem;
                  height: 0.66rem;
                  font-size: 0.18rem;
                  text-align: center;
                  line-height: 0.39rem;
                  font-family: DS-DIGII;
                  font-weight: 500;
                  color: #ffffff;
                }
                &:nth-child(2) {
                  text-align: center;
                  margin-top: 0.1rem;
                  font-size: 0.14rem;
                  font-weight: 400;
                  color: #a0b9d9;
                  .unit {
                    font-size: 0.1rem;
                  }
                }
              }
            }
          }
        }
      }
      .right {
        display: flex;
        align-items: center;
        width: calc(100% - 6.56rem);
        height: calc(3.68rem - calc(10.8rem - 1080px) / 2);
        margin-left: -0.2rem;
        background-size: 100% 100%;
        background-image: url("~@/assets/outlet/<EMAIL>");
        padding: 0.35rem 0.25rem 0.2rem 0.4rem;
        .right-middle {
          width: 3.5rem;
          height: 100%;
          .title {
            display: flex;
            align-items: center;
            justify-content: space-between;
            span {
              &:nth-child(1) {
                padding-left: 0.2rem;
                font-size: 0.22rem;
                margin-left: 0.14rem;
                font-weight: 500;
                color: #ffffff;
                position: relative;
                text-shadow: 0 0 0.1rem #2e73d6, 0 0 0.1rem #2e73d6;
                &::before {
                  position: absolute;
                  width: 5px;
                  height: 22px;
                  background: #ffffff;
                  box-shadow: 0 0 0.1rem #2e73d6, 0 0 0.1rem #2e73d6;
                  content: "";
                  left: 0;
                  top: 5px;
                }
              }
              &:nth-child(2) {
                font-size: 0.14rem;
                font-weight: 400;
                color: rgba(184, 211, 241, 0.6);
              }
            }
          }
          .params {
            margin-top: calc(0.14rem - calc(10.8rem - 1080px) / 10);
            background-size: 100% 100%;
            height: 0.72rem;
            background-image: url("~@/assets/outlet/<EMAIL>");
            display: flex;
            align-items: center;
            padding: 0 0.1rem;
            .params-list {
              display: flex;
              justify-content: center;
              flex: 1;
              &:nth-child(4) {
                flex: 1.2;
              }
              flex-direction: column;
              span {
                text-align: center;
                &:nth-child(1) {
                  color: #5abefe;
                  font-size: 0.14rem;
                  display: inline-block;
                  line-height: 0.14rem;
                }
                &:nth-child(2) {
                  margin-top: 0.04rem;
                  color: #5abefe;
                  display: inline-block;
                  line-height: 0.1rem;
                  font-size: 0.1rem;
                }
                &:nth-child(3) {
                  margin-top: 0.1rem;
                  color: #ffffff;
                  font-size: 0.16rem;
                  display: inline-block;
                  line-height: 0.16rem;
                }
              }
              .params-err {
                color: #e93e42 !important;
              }
            }
          }
          .device-anysis {
            margin-top: calc(0.15rem - calc(10.8rem - 1080px) / 10);
            padding-left: 0.1rem;
            .anysis-title {
              font-size: 0.14rem;
              font-weight: 500;
              color: #5abefe;
              line-height: 0.14rem;
            }
            .device-area {
              display: flex;
              align-items: center;
              margin-top: calc(0.1rem - calc(10.8rem - 1080px) / 20);
              .device-list {
                display: flex;
                align-items: center;
                flex: 1;
                span {
                  font-size: 0.14rem;
                  font-weight: 400;
                  color: #99cbeb;
                  display: inline-block;
                  line-height: 0.14rem;
                }
                img {
                  width: 0.18rem;
                  height: 0.18rem;
                  margin-left: 0.16rem;
                }
              }
            }
            .muation {
              margin-top: 0.08rem;
              span {
                font-size: 0.14rem;
                font-weight: 400;
                display: inline-block;
                height: 0.14rem;
                &:nth-child(1) {
                  color: #99cbeb;
                }
                &:nth-child(2) {
                  color: #99cbeb;
                }
              }
            }
          }
          .monitor-table {
            /*border-top: 1px solid rgba(21,51,116, .6);*/
            /*border-bottom: 1px solid rgba(21,51,116, .6);*/
            background-image: url("~@/assets/outlet/<EMAIL>");
            background-size: 100% 100%;
            min-height: calc(0.54rem - calc(10.8rem - 1080px) / 10);
            padding: calc(0.07rem - calc(10.8rem - 1080px) / 20)
              calc(0.05rem - calc(10.8rem - 1080px) / 20);
            position: relative;
            .monitor-list {
              display: flex;
              align-items: center;
              &:nth-child(1) {
                span {
                  color: #5abefe !important;
                  font-size: 0.14rem;
                }
              }
              &:not(first-child) {
                margin-top: 0.04rem;
                span {
                  &:nth-child(2) {
                    color: #ff4343;
                    span {
                      color: #ff4343;
                    }
                  }
                }
              }
              span {
                color: #ffffff;
                font-size: 0.14rem;
                line-height: 0.14rem;
                flex: 1;
                text-align: center;
                position: relative;
                &:nth-child(2),
                &:nth-child(3) {
                  flex: 1.2;
                }
              }
              .line {
                width: 1px;
                height: 0.81rem;
                position: absolute;
                right: 0;
              }
            }
            .lines {
              position: absolute;
              top: 0;
              left: 0;
              display: flex;
              align-items: center;
              width: 100%;
              height: 100%;
              div {
                width: 18.5%;
                height: 100%;
                display: flex;
                padding: 0.1rem 0;
                justify-content: flex-end;
                &:nth-child(2),
                &:nth-child(3) {
                  width: 22%;
                }
              }
            }
          }
          .relation {
            padding-left: 0.1rem;
            margin-top: calc(0.1rem - calc(10.8rem - 1080px) / 20);
            .relation-title {
              font-size: 0.14rem;
              font-weight: 500;
              color: #5abefe;
              line-height: 0.14rem;
            }
            .realtion-area {
              display: flex;
              align-items: center;
              margin-top: 0.05rem;
              .relation-list {
                display: flex;
                align-items: center;
                width: 33.3%;
                span {
                  font-size: 0.14rem;
                  font-weight: 400;
                  color: #99cbeb;
                  display: inline-block;
                  line-height: 0.14rem;
                }
                img {
                  width: 0.18rem;
                  height: 0.18rem;
                  margin-left: 0.16rem;
                }
              }
            }
          }
        }
        .right-charts {
          margin-left: 0.2rem;
          width: calc(100% - 3.7rem);
          height: 100%;
          .title {
            padding-left: 0.2rem;
            font-size: 0.22rem;
            margin-left: 0.14rem;
            font-weight: 500;
            color: #ffffff;
            position: relative;
            text-shadow: 0 0 0.1rem #2e73d6, 0 0 0.1rem #2e73d6;
            &::before {
              position: absolute;
              width: 5px;
              height: 22px;
              background: #ffffff;
              box-shadow: 0 0 0.1rem #2e73d6, 0 0 0.1rem #2e73d6;
              content: "";
              left: 0;
              top: 5px;
            }
          }
          .btn {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            border-radius: 0.04rem;
            height: 0.26rem;
            span {
              height: 0.26rem;
              padding: 0 0.25rem;
              background: #0c275c;
              display: inline-block;
              text-align: center;
              line-height: 0.26rem;
              cursor: pointer;
              font-size: 0.13rem;
              font-weight: 500;
              color: #ffffff;
              &:first-child {
                border-radius: 0.04rem 0 0 0.04rem;
              }
              &:last-child {
                border-radius: 0 0.04rem 0.04rem 0;
              }
            }
            .btn-active {
              background: #0084ff;
            }
          }
          .chart-area {
            height: calc(2.5rem - calc(10.8rem - 1080px) / 3);
          }
          .no-data {
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.2rem;
            height: 100%;
          }
        }
      }
    }
    .details-bottom {
      display: flex;
      align-items: center;
      padding: 0 0.5rem;
      justify-content: space-between;
      margin-top: 0.06rem;
      height: calc(3.6rem - calc(10.8rem - 1080px) / 2);
      .left {
        width: 6.42rem;
        height: 100%;
        background: rgba(1, 13, 45, 0.6);
        border: 1px solid #0062b1;
        padding: 0.25rem 0.3rem;
        box-sizing: border-box;
        .title {
          padding-left: 0.2rem;
          font-size: 0.22rem;
          font-weight: 500;
          color: #ffffff;
          position: relative;
          text-shadow: 0 0 0.1rem #2e73d6, 0 0 0.1rem #2e73d6;
          &::before {
            position: absolute;
            width: 5px;
            height: 22px;
            background: #ffffff;
            box-shadow: 0 0 0.1rem #2e73d6, 0 0 0.1rem #2e73d6;
            content: "";
            left: 0;
            top: 5px;
          }
        }
        .chart-area {
          margin-top: 0.15rem;
          height: calc(100% - 0.4rem);
          .swiper-container {
            .swiper-slide {
              width: 4.28rem !important;
              height: calc(2.4rem - calc(10.8rem - 1080px) / 2);
              padding: 0.3rem 0.35rem 0.2rem 0.35rem;
              position: relative;
              img {
                width: 3.58rem;
                height: 100% !important;
              }
              .bottom-mark {
                position: absolute;
                height: 0.55rem;
                background-color: rgba(0, 0, 0, 0.5);
                bottom: 0.2rem;
                width: 3.58rem;
                padding: 0.05rem 0.1rem;
                box-sizing: border-box;
                .mark-title {
                  display: flex;
                  align-items: center;
                  font-size: 0.12rem;
                  font-weight: 400;
                  color: #bfd5ef;
                  span {
                    flex: 1;
                    text-align: center;
                    display: inline-block;
                    &:nth-child(2) {
                      flex: 2;
                    }
                  }
                }
                .mark-area {
                  display: flex;
                  align-items: center;
                  .mark-list {
                    flex: 1;
                    font-size: 0.12rem;
                    font-weight: 400;
                    text-align: center;
                    color: #ffffff;
                    &:nth-child(2) {
                      flex: 2;
                      display: flex;
                      flex-direction: column;
                      .color-list {
                        display: flex;
                        line-height: 0.12rem;
                        align-items: center;
                        justify-content: center;
                      }
                    }
                  }
                }
              }
            }
            .swiper-slide-active {
              width: 4.28rem !important;
              height: calc(2.4rem - calc(10.8rem - 1080px) / 2) !important;
              padding: 0.3rem 0.35rem 0.2rem 0.35rem;
              background-size: 100% 100%;
              background-image: url("~@/assets/outlet/<EMAIL>");
              img {
                width: 3.58rem;
                height: 100% !important;
              }
            }
          }
        }
        .line {
          width: 100%;
          height: 0.04rem;
          background-image: url("~@/assets/outlet/<EMAIL>");
          background-size: 100% 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          .xz-active {
            margin: 0 1rem;
          }
          .button {
            margin-top: 0.36rem;
            font-size: 0.12rem;
            font-family: PingFang SC;
            font-weight: bold;
            color: #3371a1;
            display: flex;
            flex-direction: column;
            span {
              line-height: 0.12rem;
            }
            &:nth-child(1),
            &:nth-child(3) {
              position: relative;
              &::before {
                position: absolute;
                content: "";
                width: 0.11rem;
                height: 0.11rem;
                background-image: url("~@/assets/outlet/<EMAIL>");
                background-size: 100% 100%;
                top: -0.12rem;
                left: 0.15rem;
              }
            }
            &:nth-child(2) {
              position: relative;
              &::before {
                position: absolute;
                content: "";
                width: 0.5rem;
                height: 0.34rem;
                background-image: url("~@/assets/outlet/<EMAIL>");
                background-size: 100% 100%;
                top: -0.23rem;
                left: -0.05rem;
              }
            }
          }
        }
      }
      .right {
        width: calc(100% - 6.5rem);
        margin-left: 0.08rem;
        height: 100%;
        background: rgba(1, 13, 45, 0.6);
        border: 1px solid #0062b1;
        padding: 0.25rem 0.16rem 0.15rem;
        box-sizing: border-box;
        /*display: flex;*/
        /*align-items: center;*/
        .title {
          padding-left: 0.2rem;
          font-size: 0.22rem;
          margin-left: 0.14rem;
          font-weight: 500;
          color: #ffffff;
          position: relative;
          text-shadow: 0 0 0.1rem #2e73d6, 0 0 0.1rem #2e73d6;
          &::before {
            position: absolute;
            width: 5px;
            height: 22px;
            background: #ffffff;
            box-shadow: 0 0 0.1rem #2e73d6, 0 0 0.1rem #2e73d6;
            content: "";
            left: 0;
            top: 5px;
          }
        }
        .right-area {
          width: 100%;
          margin-top: 0.1rem;
          height: calc(100% - 0.4rem);
          .top {
            height: 0.26rem;
            border-radius: 0.04rem;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            span {
              font-size: 0.13rem;
              font-weight: 400;
              line-height: 0.26rem;
              cursor: pointer;
              color: #ffffff;
              flex: 1;
              display: inline-block;
              text-align: center;
              &:first-child {
                border-radius: 0.04rem 0 0 0.04rem;
              }
              &:last-child {
                border-radius: 0 0.04rem 0.04rem 0;
              }
            }
            .active {
              background-color: #0084ff;
            }
            .swiper-area {
              display: flex;
              align-items: center;
              width: 5.6rem;
              justify-content: space-between;
              .left-arrow {
                cursor: pointer;
                width: 0.4rem;
                height: 0.3rem;
                display: flex;
                align-items: center;
                justify-content: center;
                border: none !important;
                .svg-arrow {
                  width: 0.2rem !important;
                  height: 0.2rem !important;
                }
              }
              .middle {
                width: 4.8rem;
                .btn {
                  width: 100%;
                  .single-slide {
                    width: 0.8rem !important;
                    font-size: 0.13rem;
                    font-weight: 400;
                    line-height: 0.26rem;
                    background: #0c275c;
                    cursor: pointer;
                    color: #ffffff;
                    display: inline-block;
                    text-align: center;
                  }
                  .single-slide-active {
                    background-color: #0084ff !important;
                  }
                }
              }
              .right-arrow {
                cursor: pointer;
                width: 0.4rem;
                height: 0.3rem;
                display: flex;
                border: none !important;
                align-items: center;
                justify-content: center;
                .svg-arrow {
                  width: 0.2rem !important;
                  height: 0.2rem !important;
                }
              }
            }
          }
          .level-chart {
            margin-top: 0.24rem;
            height: calc(100% - 0.5rem);
            .no-data {
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 0.2rem;
              height: 100%;
            }
          }
        }
        /*.right-middle {*/
        /*    width: 5.3rem;*/
        /*    height: 100%;*/
        /*    .title {*/
        /*        padding-left: 0.2rem;*/
        /*        font-size: 0.22rem;*/
        /*        margin-left: 0.14rem;*/
        /*        font-weight: 500;*/
        /*        color: #FFFFFF;*/
        /*        position: relative;*/
        /*        text-shadow: 0 0 0.1rem #2e73d6, 0 0 0.1rem #2e73d6;*/
        /*        &::before {*/
        /*            position: absolute;*/
        /*            width: 5px;*/
        /*            height: 22px;*/
        /*            background: #FFFFFF;*/
        /*            box-shadow: 0 0 0.1rem #2e73d6, 0 0 0.1rem #2e73d6;*/
        /*            content: '';*/
        /*            left: 0;*/
        /*            top: 5px;*/
        /*        }*/
        /*    }*/
        /*    .monitor-tables {*/
        /*        position: relative;*/
        /*        height:calc(2.6rem - calc(10.8rem - 100vh) / 3);*/
        /*        margin-top: 0.25rem;*/
        /*        .monitor-title {*/
        /*            display: flex;*/
        /*            align-items: center;*/
        /*            height: calc(0.4rem - calc(10.8rem - 100vh) / 12);*/
        /*            background: #0F245E;*/
        /*            font-size: 0.14rem;*/
        /*            font-family: PingFang SC;*/
        /*            font-weight: 400;*/
        /*            color: #40DEFF;*/
        /*            div {*/
        /*                display: inline-block;*/
        /*                flex: 1;*/
        /*                line-height: 0.15rem;*/
        /*                text-align: center;*/
        /*                &:nth-child(1) {*/
        /*                    flex: 1.4;*/
        /*                }*/
        /*                &:nth-child(2) {*/
        /*                    flex: 1.6;*/
        /*                }*/
        /*                &:nth-child(6),&:nth-child(7) {*/
        /*                    flex: 1.3;*/
        /*                }*/
        /*            }*/
        /*        }*/
        /*        .monitor-area {*/
        /*            height:  calc(2.2rem - calc(10.8rem - 100vh) / 3);*/
        /*            .monitor-list {*/
        /*                display: flex;*/
        /*                align-items: center;*/
        /*                font-size: 0.14rem;*/
        /*                font-weight: 400;*/
        /*                color: #97B6E4;*/
        /*                height: calc(0.55rem - calc(10.8rem - 100vh) / 8);*/
        /*                &:nth-child(even) {*/
        /*                    background-color: rgba(8,31,78,.8);*/
        /*                }*/
        /*                div {*/
        /*                    display: inline-block;*/
        /*                    flex: 1;*/
        /*                    text-align: center;*/
        /*                    &:nth-child(1) {*/
        /*                        flex: 1.4;*/
        /*                        line-height: 0.15rem;*/
        /*                    }*/
        /*                    &:nth-child(2) {*/
        /*                        flex: 1.6;*/
        /*                    }*/
        /*                    &:nth-child(6),&:nth-child(7) {*/
        /*                        flex: 1.3;*/
        /*                    }*/
        /*                    &:last-child {*/
        /*                        display: flex;*/
        /*                        align-items: center;*/
        /*                        justify-content: center;*/
        /*                        span {*/
        /*                            display: inline-block;*/
        /*                            width: 0.5rem!important;*/
        /*                            height: 0.25rem;*/
        /*                            line-height: 0.25rem;*/
        /*                            background-image: url("@/assets/outlet/<EMAIL>");*/
        /*                            background-size: 100% 100%;*/
        /*                            color: #11F7DA;*/
        /*                            font-size: 0.12rem;*/
        /*                        }*/
        /*                    }*/
        /*                    .monitor-err {*/
        /*                        background-image: url("@/assets/outlet/<EMAIL>") !important;*/
        /*                        color: #F44043!important;*/
        /*                        font-size: 0.12rem;*/
        /*                    }*/
        /*                }*/
        /*            }*/
        /*        }*/
        /*        .no-data {*/
        /*            position: absolute;*/
        /*            left: 50%;*/
        /*            top: 50%;*/
        /*            transform: translate(-50%,-50%);*/
        /*            font-size: 0.2rem;*/
        /*        }*/
        /*    }*/
        /*}*/
      }
    }
  }
  .site-type-name {
    font-size: 0.24rem;
    color: #ffffff;
  }
}
</style>
<style lang="less">
.title-select2-region {
  align-items: flex-end;
  .ant-select-selection {
    width: 2.5rem;
    height: 0.3rem;
    font-size: 0.24rem;
    // background: rgba(14, 139, 255, 0.32);
    border: none;
    border-radius: unset;
    // clip-path: polygon(100% 0, 100% 100%, 12% 100%, 0% 70%, 0 0);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-color: transparent;
  }
  .ant-select-selection__placeholder,
  .ant-select-search__field__placeholder {
    height: 0.3rem;
    text-align: center;
    line-height: 0.3rem;
  }
  .ant-select-selection-selected-value {
    color: #00eaff;
  }
  .ant-select-focused .ant-select-selection,
  .ant-select-selection:focus,
  .ant-select-selection:active {
    border-color: transparent !important;
    border-right-width: 0 !important;
    outline: 0 !important;
    box-shadow: none !important;
    border: none !important;
  }
  .ant-select-selection__rendered {
    display: flex;
    // justify-content: center;
  }
}
</style>
<template>
  <section class="water-outlet">
    <div class="water-outlet-content">
      <div style="background: rgba(6, 30, 101, 0.6);">
        <!-- 头部区域选择 -->
        <div class="details-region">
          <div class="region-box">
            <div class="time">
              <div class="site-type-name">排口编号：</div>
              <!-- 河流 -->
              <a-select
                v-model="drainId"
                class="title-select2-region"
                @change="changeDrain"
              >
                <a-icon
                  slot="suffixIcon"
                  type="caret-down"
                  style="color: #1ffffc; font-size: 0.2rem"
                />
                <a-select-option
                  :value="item.waterDrainId"
                  v-for="(item, index) in drainList"
                  :key="index"
                >
                  {{ item.drainName }}
                </a-select-option>
              </a-select>
            </div>
            <div class="line"></div>
            <div class="name">
              <div class="site-type-name">监控选择：</div>
              <!-- 站点 -->
              <a-select
                v-model="monitorId"
                class="title-select2-region"
                @change="changeMonitor"
              >
                <a-icon
                  slot="suffixIcon"
                  type="caret-down"
                  style="color: #1ffffc; font-size: 0.2rem"
                />
                <a-select-option
                  :value="item.monitorId"
                  v-for="(item, index) in monitorList"
                  :key="index"
                >
                  {{ item.monitorName }}
                </a-select-option>
              </a-select>
            </div>
            <div class="line"></div>
            <div class="address">
              <div class="address-text">
                排口位置：{{ stationdDetails.address }}
              </div>
            </div>
          </div>
        </div>
        <!-- 中间部分 -->
        <div class="details-middle">
          <div class="left">
            <div class="video">
              <div class="video-area drain-area">
                <LivePlayer
                  v-if="videoUrl"
                  :video-url="videoUrl"
                  fluent
                  autoplay
                  live
                  :stretch="true"
                  style="width: 10rem; height: 4.9rem"
                />
                <div
                  v-else
                  class="not-online"
                  :style="{ backgroundImage: 'url(' + snapUrl + ')' }"
                >
                  暂无监控视频
                </div>
              </div>
            </div>
          </div>
          <div class="right">
            <div class="right-middle right-middles">
              <div class="title">
                <span>在线监测</span>
                <span
                  >时间：{{
                    recordData.recordTime
                      ? recordData.recordTime.substr(5, 11)
                      : "--"
                  }}</span
                >
              </div>
              <div class="params">
                <div class="params-list">
                  <span>液位</span>
                  <span>cm</span>
                  <span>{{
                    recordData.recordTime && recordData.waterDepth
                      ? recordData.waterDepth
                      : "--"
                  }}</span>
                </div>
                <div class="params-list">
                  <span>电导率</span>
                  <span>us/cm</span>
                  <span
                    :class="{
                      'params-err': recordData.conductivityIsAlarm
                        ? 'params-err'
                        : '',
                    }"
                    >{{
                      recordData.conductivity ? recordData.conductivity : "--"
                    }}</span
                  >
                </div>
                <div class="params-list">
                  <span>氨氮</span>
                  <span>mg/L</span>
                  <span
                    :class="{
                      'params-err': recordData.ammoniaNitrogenIsAlarm
                        ? 'params-err'
                        : '',
                    }"
                    >{{
                      recordData.ammoniaNitrogen
                        ? recordData.ammoniaNitrogen
                        : "--"
                    }}</span
                  >
                </div>
                <div class="params-list">
                  <span>降雨强度</span>
                  <span>mm/min</span>
                  <span>{{
                    recordData.rainfall ? recordData.rainfall : 0
                  }}</span>
                </div>
                <div class="params-list">
                  <span>温度</span>
                  <span>℃</span>
                  <span>{{
                    recordData.temperature ? recordData.temperature : "--"
                  }}</span>
                </div>
                <div class="params-list">
                  <span>TVOC</span>
                  <span>ppm</span>
                  <span>{{ recordData.tvoc ? recordData.tvoc : "--" }}</span>
                </div>
              </div>
              <div class="device-anysis">
                <div class="anysis-title">智能分析：</div>
                <div class="device-area">
                  <div class="device-list">
                    <span>设备状态</span>
                    <img src="@/assets/outlet/<EMAIL>" alt="" />
                  </div>
                  <div class="device-list">
                    <span>样品抽取</span>
                    <img :src="recordData.isSample ? gou : cha" alt="" />
                  </div>
                  <div class="device-list">
                    <span>是否排水</span>
                    <img :src="recordData.isDrain ? gou : cha" alt="" />
                  </div>
                </div>
                <div class="muation">
                  <span>异变量：</span>
                  <span
                    v-if="recordData.alarmList && !recordData.alarmList.length"
                    >--</span
                  >
                </div>
              </div>
              <div
                class="monitor-table"
                v-if="recordData.alarmList && recordData.alarmList.length"
              >
                <div class="monitor-list">
                  <span>监测项</span>
                  <span>监测值</span>
                  <span>阈值</span>
                  <span>超标比例</span>
                  <span>变化趋势</span>
                </div>
                <div
                  class="monitor-list"
                  v-for="(item, index) in recordData.alarmList"
                  :key="index"
                >
                  <span>{{ item.alarmParamTypeStr }}</span>
                  <span
                    >{{ item.alarmValue }}<span>{{ item.unit }}</span></span
                  >
                  <span
                    >{{ item.alarmThreshold }}<span>{{ item.unit }}</span></span
                  >
                  <span>{{ item.rate }}%</span>
                  <span>{{ item.trend }}</span>
                </div>
                <div class="lines">
                  <div>
                    <img src="@/assets/<EMAIL>" alt="" class="line" />
                  </div>
                  <div>
                    <img src="@/assets/<EMAIL>" alt="" class="line" />
                  </div>
                  <div>
                    <img src="@/assets/<EMAIL>" alt="" class="line" />
                  </div>
                  <div>
                    <img src="@/assets/<EMAIL>" alt="" class="line" />
                  </div>
                </div>
              </div>
              <div class="relation">
                <div class="relation-title">关联执行:</div>
                <div class="realtion-area">
                  <div
                    class="relation-list"
                    v-if="
                      !(recordData.alarmList && recordData.alarmList.length)
                    "
                  >
                    <span>持续监测</span>
                    <img src="@/assets/outlet/<EMAIL>" alt="" />
                  </div>
                  <div
                    class="relation-list"
                    v-if="recordData.alarmList && recordData.alarmList.length"
                  >
                    <span>留样核验</span>
                    <img src="@/assets/outlet/<EMAIL>" alt="" />
                  </div>
                  <div
                    class="relation-list"
                    v-if="recordData.alarmList && recordData.alarmList.length"
                  >
                    <span>启动追溯</span>
                    <img src="@/assets/outlet/<EMAIL>" alt="" />
                  </div>
                </div>
              </div>
            </div>
            <div class="right-charts">
              <div class="title">采样监测</div>
              <div class="btn">
                <span
                  :class="{
                    'btn-active': assayIndex == index ? 'btn-active' : '',
                  }"
                  v-for="(item, index) in assayRecord.monitorParamList"
                  :key="index"
                  @click="assayIndex = index"
                  >{{ item.name }}
                </span>
              </div>
              <div class="chart-area" v-if="assayRecord.monitorParamList">
                <LinessChartDashed
                  :id="'Liness' + new Date().getTime()"
                  :width="'100%'"
                  :height="'100%'"
                  :propData="assayRecord.monitorParamList[assayIndex].lineData"
                  :smooth="false"
                  v-if="
                    assayRecord.monitorParamList.length &&
                    assayRecord.monitorParamList[assayIndex].lineData.dataList
                      .length
                  "
                />
                <div v-else class="no-data">数据未更新</div>
              </div>
            </div>
          </div>
        </div>
        <!-- 底部图表 -->
        <div class="details-bottom">
          <div class="left">
            <div class="title">智能抓拍</div>
            <div class="chart-area">
              <div class="swiper-container">
                <swiper
                  class="swiper-wrapper"
                  :options="optionss"
                  ref="mySwiper"
                >
                  <swiper-slide
                    class="swiper-slide"
                    v-for="(item, index) in cameraRecordList"
                    :key="index"
                  >
                    <img :src="item.snapImage" />
                    <div class="bottom-mark">
                      <div class="mark-title">
                        <span>特征抽取</span>
                        <span>颜色解析</span>
                        <span>透明度</span>
                        <span>深浅度</span>
                        <span>漂浮物</span>
                      </div>
                      <div class="mark-area">
                        <div class="mark-list">
                          {{
                            item.featureExtraction
                              ? item.featureExtraction
                              : "--"
                          }}
                        </div>
                        <div class="mark-list">
                          <!--                                                <div class="color-list" v-if="item.waterColorAnalysisList && item.waterColorAnalysisList.length">-->
                          <!--                                                    <span>{{item.proportion ? item.proportion : '&#45;&#45;'}}</span>-->
                          <!--                                                    <span>{{item.chineseName ? item.chineseName : '&#45;&#45;'}}</span>-->
                          <!--                                                    <span>{{item.code ? item.code : '&#45;&#45;'}}</span>-->
                          <!--                                                </div>-->
                          <div
                            v-if="
                              item.waterColorAnalysisList &&
                              item.waterColorAnalysisList.length
                            "
                          >
                            {{ item.waterColorAnalysisList[0].chineseName }}
                          </div>
                          <div v-else>--</div>
                        </div>
                        <div class="mark-list">{{ item.chromaAnalysis }}</div>
                        <div class="mark-list">{{ item.depthAnalysis }}</div>
                        <div class="mark-list">{{ item.floating }}</div>
                      </div>
                    </div>
                  </swiper-slide>
                </swiper>
              </div>
              <div v-if="cameraRecordList.length" class="line">
                <div class="button">
                  <span>{{
                    0 == activeIndex
                      ? cameraRecordList[cameraRecordList.length - 1].time2
                      : cameraRecordList[activeIndex - 1]
                      ? cameraRecordList[activeIndex - 1].time2
                      : "--"
                  }}</span>
                  <span>{{
                    0 == activeIndex
                      ? cameraRecordList[cameraRecordList.length - 1].time1
                      : cameraRecordList[activeIndex - 1]
                      ? cameraRecordList[activeIndex - 1].time1
                      : "--"
                  }}</span>
                </div>
                <div v-if="cameraRecordList.length" class="button xz-active">
                  <span>{{
                    cameraRecordList[activeIndex]
                      ? cameraRecordList[activeIndex].time2
                      : "--"
                  }}</span>
                  <span>{{
                    cameraRecordList[activeIndex]
                      ? cameraRecordList[activeIndex].time1
                      : "--"
                  }}</span>
                </div>
                <div v-if="cameraRecordList.length" class="button">
                  <span>{{
                    cameraRecordList.length - 1 == activeIndex
                      ? cameraRecordList[0].time2
                      : cameraRecordList[activeIndex + 1]
                      ? cameraRecordList[activeIndex + 1].time2
                      : "--"
                  }}</span>
                  <span>{{
                    cameraRecordList.length - 1 == activeIndex
                      ? cameraRecordList[0].time1
                      : cameraRecordList[activeIndex + 1]
                      ? cameraRecordList[activeIndex + 1].time1
                      : "--"
                  }}</span>
                </div>
              </div>
            </div>
          </div>
          <div class="right">
            <!--                    <div class="right-middle">-->
            <!--                        <div class="title">采样监测</div>-->
            <!--                        <div class="monitor-tables" v-if="assayRecord.monitorList && assayRecord.monitorList.length">-->
            <!--                            <div class="monitor-title">-->
            <!--                                <div>样品编号</div>-->
            <!--                                <div>采样时间</div>-->
            <!--                                <div>氨氮 mg/L</div>-->
            <!--                                <div>总磷 mg/L</div>-->
            <!--                                <div>COD mg/L</div>-->
            <!--                                <div>水质类型</div>-->
            <!--                                <div>是否达标</div>-->
            <!--                            </div>-->
            <!--                            <div class="monitor-area">-->
            <!--                                <swiper class="swiper-wrapper" :options="(assayRecord.monitorList && assayRecord.monitorList.length)> 4 ? swiperOption : swiperOption1">-->
            <!--                                    <swiper-slide class="monitor-list" v-for="(item, index) in assayRecord.monitorList" :key="index">-->
            <!--                                        <div>{{item.sampleNumber}}</div>-->
            <!--                                        <div>{{item.sampleTime}}</div>-->
            <!--                                        <div>{{item.ammoniaNitrogen}}</div>-->
            <!--                                        <div>{{item.totalPhosphorus}}</div>-->
            <!--                                        <div>{{item.cod}}</div>-->
            <!--                                        <div>{{item.waterType}}</div>-->
            <!--                                        <div>-->
            <!--                                            <span :class="{'monitor-err': !item.isQualified ? 'monitor-err' : ''}">{{item.isQualified ? '达标' : '不达标'}}</span>-->
            <!--                                        </div>-->
            <!--                                    </swiper-slide>-->
            <!--                                </swiper>-->
            <!--                            </div>-->
            <!--                        </div>-->
            <!--                        <div v-else class="monitor-tables">-->
            <!--                            <div class="no-data">数据未更新</div>-->
            <!--                        </div>-->
            <!--                    </div>-->
            <div class="title">趋势分析</div>
            <div class="right-area">
              <div class="top">
                <div class="swiper-area">
                  <!-- 左侧箭头 -->
                  <div class="left-arrow">
                    <svgicon
                      name="left"
                      color="#00D9D5"
                      class="svg-arrow"
                    ></svgicon>
                  </div>
                  <div class="middle">
                    <swiper
                      class="btn"
                      :options="swiperOptions"
                      v-if="paramsData.length > 4"
                    >
                      <swiper-slide
                        class="single-slide"
                        :class="{
                          'single-slide-active':
                            paramsIndex == index ? 'single-slide-active' : '',
                        }"
                        v-for="(item, index) in paramsData"
                        :key="index"
                        :data-index="index"
                        >{{ item.name }}
                      </swiper-slide>
                    </swiper>
                    <div class="btn" v-else>
                      <div
                        class="single-slide"
                        :class="{
                          'single-slide-active':
                            paramsIndex == index ? 'single-slide-active' : '',
                        }"
                        v-for="(item, index) in paramsData"
                        :key="index"
                        :data-index="index"
                        @click="changeType(index)"
                      >
                        {{ item.name }}
                      </div>
                    </div>
                  </div>
                  <div class="right-arrow">
                    <svgicon
                      name="right"
                      color="#00D9D5"
                      class="svg-arrow"
                    ></svgicon>
                  </div>
                </div>
              </div>
              <div class="level-chart" v-if="paramsData.length">
                <LinesChartDashed
                  :id="'Lines' + new Date().getTime()"
                  :width="'100%'"
                  :height="'100%'"
                  :propData="paramsData[paramsIndex].lineData"
                  :smooth="false"
                  v-if="paramsData[paramsIndex].lineData.dataList.length"
                  @changeIndex="changeIndex"
                />
                <div v-else class="no-data">暂未开通</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <img
      style="position: absolute; top: 5px; right: 5px; cursor: pointer"
      src="@/assets/<EMAIL>"
      alt=""
      @click="handleClickClose"
    />
  </section>
</template>

<script lang="ts">
/* eslint-disable @typescript-eslint/ban-ts-ignore */
import { Component, Vue, Watch, Prop } from "vue-property-decorator";
import { Icon, Radio, Select } from "ant-design-vue";
import { Swiper, SwiperSlide } from "vue-awesome-swiper";
//@ts-ignore
import LinesChartDashed from "@/components/Charts/LinesChartDashed.vue";
//@ts-ignore
import LinessChartDashed from "@/components/Charts/LinessChartDashed.vue";
//@ts-ignore
import LivePlayer from "@liveqing/liveplayer";
import {
  getDrainListCarmera,
  fetchCameraUrl,
  getListRealTime,
  getlistTwoDayByDrainId,
  getAssayRecord,
  getByRecordId,
} from "@/api/water";
let Index: any = "";
@Component({
  name: "WaterDetails",
  components: {
    AIcon: Icon,
    ASelect: Select,
    ASelectOption: Select.Option,
    Swiper,
    SwiperSlide,
    LinesChartDashed,
    LinessChartDashed,
    LivePlayer,
  },
})
export default class extends Vue {
  @Watch("currSelectIndex", { immediate: true, deep: true })
  public oncurrSelectIndex(newValue: any, oldValue: any) {
    if (typeof newValue === "number") {
      this.recordId =
        this.paramsData[this.paramsIndex].valueList[newValue].recordId;
      this.getByRecordId();
    }
  }
  @Prop({ default: null }) private drainId!: any;
  private drainList: any = [];
  // private drainId:any=''
  private monitorId: any = "";
  private monitorList: any = [];
  private stationdDetails: any = {};
  private videoUrl: any = "";
  private monitorDetail: any = {};
  private activeIndex: any = 0;
  private swiperOption: any = {
    direction: "vertical",
    slidesPerView: 4,
    slidesPerGroup: 1,
    loop: true,
    autoplay: {
      delay: 3000,
      disableOnInteraction: false,
    },
  };
  private swiperOption1: any = {
    direction: "vertical",
    slidesPerView: 4,
    slidesPerGroup: 1,
  };
  private swiperOptions: any = {
    slidesPerView: 6,
    slidesPerGroup: 1,
    loop: false,
    loopFillGroupWithBlank: true,
    pagination: {
      el: ".swiper-pagination",
      clickable: true,
    },
    on: {
      click: function (e: any) {
        if ((window as any)._this) {
          // @ts-ignore
          (window as any)._this.paramsIndex = Number(
            e.target.getAttribute("data-index")
          );
        }
      },
    },
    autoplay: false,
    navigation: {
      nextEl: ".right-arrow",
      prevEl: ".left-arrow",
    },
  };
  private gou: any = require("@/assets/outlet/<EMAIL>");
  private cha: any = require("@/assets/outlet/<EMAIL>");
  private optionss: any = {
    watchSlidesProgress: true,
    slidesPerView: "auto",
    centeredSlides: true,
    loop: true,
    loopedSlides: 5,
    autoplay: {
      delay: 3000,
      disableOnInteraction: false,
    },
    on: {
      progress: function (progress: any) {
        // @ts-ignore
        for (let i = 0; i < this.slides.length; i++) {
          // @ts-ignore
          var slide = this.slides.eq(i);
          // @ts-ignore
          var slideProgress = this.slides[i].progress;

          if (Math.abs(slideProgress) > 1) {
            var modify = (Math.abs(slideProgress) - 1) * 0.4 + 1;
          }
          // @ts-ignore
          var translate = slideProgress * modify * 206 + "px";
          var scale = 1 - Math.abs(slideProgress) / 5;
          var zIndex = 999 - Math.abs(Math.round(10 * slideProgress));
          slide.transform("translateX(" + translate + ") scale(" + scale + ")");
          slide.css("zIndex", zIndex);
          slide.css("opacity", 1);
          if (Math.abs(slideProgress) > 1) {
            slide.css("opacity", 0);
          }
        }
      },
      touchEnd: function () {
        // @ts-ignore
        if ((window as any)._this) {
          // @ts-ignore
          (window as any)._this.activeIndex = this.realIndex;
        }
      },
      slideChangeTransitionEnd: function () {
        // @ts-ignore
        if ((window as any)._this) {
          // @ts-ignore
          (window as any)._this.activeIndex = this.realIndex;
        }
      },
      setTransition: function (transition: any) {
        // @ts-ignore
        for (var i = 0; i < this.slides.length; i++) {
          // @ts-ignore
          var slide = this.slides.eq(i);
          slide.transition(transition);
        }
      },
    },
  };
  mounted() {
    (window as any)._this = this;
    (window as any)._this.activeIndex = this.activeIndex;
    // @ts-ignore
    // this.drainId = this.$route.query.currDrainId
    console.log("this.drainId", this.drainId);
    this.getDrainListCarmera();
  }
  private getDrainListCarmera() {
    getDrainListCarmera()
      .then((res) => {
        this.drainList = (res.data.data || []).map((item: any) => {
          item.drainCameraList = item.drainCameraList || [];
          return item;
        });
        this.stationdDetails = this.drainList.find(
          (item: any) => item.waterDrainId == this.drainId
        );
        this.monitorList = this.stationdDetails.drainCameraList || [];
        if (this.monitorList.length) {
          this.monitorDetail = this.monitorList[0];
          this.monitorId = this.monitorList[0].monitorId;
          this.snapUrl = this.monitorList[0].snapUrl;
          if (this.monitorList[0].online) {
            this.getvideoUrl(this.monitorId);
          }
          this.getListRealTime();
        }
      })
      .finally(() => {
        this.getlistTwoDayByDrainId();
        this.getAssayRecord();
        this.getByRecordId();
      });
  }
  private paramsData: any = [];
  private paramsIndex: any = 0;
  private getlistTwoDayByDrainId() {
    getlistTwoDayByDrainId(this.drainId).then((res) => {
      console.log(res, "resres");
      this.paramsData = (res.data.data || []).map((item: any) => {
        item.lineData = {
          dataList: (item.valueList || []).map((items: any) => items.value),
          unit: item.concentrationUnit,
          bottomList: (item.valueList || []).map((items: any) =>
            items.time.substr(0, items.time.length - 3)
          ),
          isAlarm: (item.valueList || []).map((items: any) => items.isAlarm),
          name: item.name,
          dataList1: (item.valueList || []).map(
            (items: any) => items.yearOnYear
          ),
          dataList2: (item.valueList || []).map(
            (items: any) => items.chainRatio
          ),
        };
        return item;
      });
    });
  }
  private currSelectIndex: any = "";
  private changeIndex(args: any) {
    this.currSelectIndex = args;
  }
  private snapUrl: string = "";
  private changeMonitor() {
    const item = this.monitorList.find(
      (item: any) => item.monitorId == this.monitorId
    );
    console.log(item);
    this.snapUrl = item.snapUrl;
    if (item.online) {
      this.getvideoUrl(item.monitorId);
    } else {
      this.videoUrl = "";
    }
    this.getListRealTime();
  }
  private changeDrain() {
    this.stationdDetails = this.drainList.find(
      (item: any) => item.waterDrainId == this.drainId
    );
    this.monitorList = this.stationdDetails.drainCameraList || [];
    if (this.monitorList.length) {
      this.monitorDetail = this.monitorList[0];
      this.monitorId = this.monitorList[0].monitorId;
      this.getvideoUrl(this.monitorId);
      this.getListRealTime();
    }
    this.getlistTwoDayByDrainId();
    this.getAssayRecord();
    this.getByRecordId();
  }
  private getvideoUrl(monitorId: any) {
    fetchCameraUrl(monitorId).then((res: any) => {
      if (res.data.data) {
        this.videoUrl = res.data.data.wsFlvHttps;
      }
    });
  }
  private cameraRecordList: any = [];
  private getListRealTime() {
    getListRealTime(this.drainId, this.monitorId).then((res) => {
      this.cameraRecordList = (res.data.data || [])
        .reverse()
        .map((item: any) => {
          item.time1 = item.snapTime.substr(5, 5);
          item.time2 = item.snapTime.substr(11, 5);
          return item;
        });
    });
  }
  private assayRecord: any = {};
  private assayIndex: any = 0;
  private getAssayRecord() {
    getAssayRecord(this.drainId).then((res) => {
      this.assayRecord = res.data.data || {};
      if (this.assayRecord.monitorList && this.assayRecord.monitorList.length) {
        this.assayRecord.monitorList = this.assayRecord.monitorList.map(
          (item: any) => {
            item.sampleTime = item.sampleTime
              ? item.sampleTime.substr(5, 11)
              : "--";
            return item;
          }
        );
      } else {
        this.assayRecord.monitorList = [];
      }
      if (
        this.assayRecord.monitorParamList &&
        this.assayRecord.monitorParamList.length
      ) {
        this.assayRecord.monitorParamList =
          this.assayRecord.monitorParamList.map((item: any) => {
            item.lineData = {
              dataList: (item.valueList || []).map((items: any) => items.value),
              unit: item.concentrationUnit,
              bottomList: (item.valueList || []).map(
                (items: any) => items.time
              ),
              name: item.name,
            };
            return item;
          });
      } else {
        this.assayRecord.monitorParamList = [];
      }
      console.log(this.assayRecord, "assayRecord");
    });
  }
  private recordId: any = undefined;
  private recordData: any = {};
  private getByRecordId() {
    getByRecordId(this.drainId, this.recordId).then((res) => {
      this.recordData = res.data.data || {};
      console.log(this.recordData, "this.recordData");
    });
  }
  private changeType(index: any) {
    this.paramsIndex = index;
  }
  private handleClickClose() {
    this.$emit('handleClickClose')
  }
}
</script>
