<style lang="less" scoped>
.water-video {
  padding: 0.2rem 0.2rem 0.2rem;
  position: relative;
  .water-video-conetnt {
    box-sizing: border-box;
    padding: 0.67rem 1rem 0.92rem 1.1rem;
    display: flex;
    justify-content: space-between;
    flex-direction: column;
    height: 100%;
    background-image: url(../../assets/water-video.png) !important;
    background-size: 100% 100% !important;
    .header {
      display: flex;
      align-items: center;
    }
    .video-search {
      display: flex;
      align-items: center;
      width: 5.6rem;
      height: 0.58rem;
      > :nth-of-type(1) {
        display: flex;
        align-items: center;
        width: 4.5rem;
        height: 0.58rem;
        background: rgba(5, 7, 95, 0.7);
        border: 1px solid #034fa8;
        input {
          width: 100%;
          margin-left: 0.25rem;
          // margin-right: 0.25rem;
          background: transparent;
          border: none;
          outline: none;
          font-size: 0.22rem;
        }
        input::-webkit-input-placeholder {
          color: #ffffff;
        }
        input::-moz-placeholder {
          /* Mozilla Firefox 19+ */
          color: #ffffff;
        }
        input:-moz-placeholder {
          /* Mozilla Firefox 4 to 18 */
          color: #ffffff;
        }
        input:-ms-input-placeholder {
          /* Internet Explorer 10-11 */
          color: #ffffff;
        }
      }
      > :nth-of-type(2) {
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(5, 7, 95, 0.7);
        border: 1px solid #034fa8;
        height: 0.58rem;
        width: 1.1rem;
        img {
          cursor: pointer;
        }
      }
    }
    .video-list {
      margin-top: 0.2rem;
      height: 6.08rem;
      display: flex;
      flex-wrap: wrap;
      overflow-y: scroll;
      .no-more {
        text-align: center;
        font-size: 0.22rem;
        width: 100%;
        height: 5.62rem;
        line-height: 5.62rem;
      }
      .video-list-content {
        width: 23.4%;
        height: 2.89rem;
        box-sizing: border-box;
        padding: 0.14rem 0.2rem 0.18rem 0.21rem;
        margin-right: 2.1%;
        background-image: url("../../assets/heavily/<EMAIL>");
        background-size: 100% 100%;
        &:nth-child(4n) {
          margin-right: 0;
        }
        &:nth-child(1) {
          margin-bottom: 0.3rem;
        }
        &:nth-child(2) {
          margin-bottom: 0.3rem;
        }
        &:nth-child(3) {
          margin-bottom: 0.3rem;
        }
        &:nth-child(4) {
          margin-bottom: 0.3rem;
        }
        .vidoe-address {
          height: 0.44rem;
          background-color: #092450;
          line-height: 0.44rem;
          padding-left: 0.1rem;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .tilte {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding-bottom: 0.05rem;
          border-bottom: 0.01rem solid rgba(11,121,180, .6);
          margin-bottom: 0.1rem;
          > div {
            display: flex;
            align-items: center;
            .circle {
              display: inline-block;
              width: 5px;
              height: 5px;
              background: #11F7DA;
              border-radius: 50%;
              margin-right: 0.05rem;
            }
            span {
              color: #11F7DA;
              font-size: 0.12rem;
            }
          }
        }
        .sub-title {
          display: flex;
          align-items: center;
          margin-bottom: 0.15rem;
          img {
            width: 0.14rem;
            height: 0.16rem;
            margin-right: 0.12rem;
          }
        }
      }
    }
    .bottom-image {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 0.4rem;
      img {
        cursor: pointer;
        width: 0.27rem;
        height: 100%;
      }
    }
  }
  ::-webkit-scrollbar {
    width: 0 !important;
  }
}
</style>

<style lang="less">
.video-select {
  .title-video-select {
    display: flex;
    justify-content: space-between;
    margin-right: 0.1rem;
    .ant-select-selection {
      width: 1.5rem;
      height: 0.3rem;
      // background: rgba(14, 139, 255, 0.32);
      border: none;
      border-radius: unset;
      // clip-path: polygon(100% 0, 100% 100%, 12% 100%, 0% 70%, 0 0);
      background: url(../../assets/<EMAIL>);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      display: flex;
      justify-content: center;
    }
    .ant-select-selection-selected-value {
      color: rgba(0, 234, 255, 1);
    }
    .ant-select-focused .ant-select-selection,
    .ant-select-selection:focus,
    .ant-select-selection:active {
      border-color: #40a9ff;
      border-right-width: 0 !important;
      outline: 0;
      box-shadow: none;
    }
  }
}
.header {
  .street-search {
    margin-right: 0.1rem;
    .ant-select-selection {
      width: 2.8rem;
      height: 0.58rem;
      box-sizing: border-box;
      border: 1px solid rgba(10,104,159, .9);
      // background: rgba(14, 139, 255, 0.32);
      border-radius: unset;
      background-color: rgba(5, 7, 95, 0.7);
      // clip-path: polygon(100% 0, 100% 100%, 12% 100%, 0% 70%, 0 0);
      display: flex;
      justify-content: center;
    }
    .ant-select-selection-selected-value {
      color: white;
      height: 100%;
      width: 100%;
      line-height: 0.58rem;
    }
    .ant-select-focused .ant-select-selection,
    .ant-select-selection:focus,
    .ant-select-selection:active {
      border-color: rgba(10,104,159, .9);
      /*border-right-width: 0 !important;*/
      outline: 0;
      box-shadow: none;
    }
    .ant-select-selection__rendered {
      outline: none;
      font-size: 0.22rem;
    }
    .ant-select-selection__placeholder, .ant-select-search__field__placeholder {
      color: white!important;
      height: 0.24rem!important;
      line-height: 0.24rem!important;
      font-size: 0.22rem!important;
    }
  }
}
.video-list-content {
  .video-list-content {
    .vjs-custom-skin {
      height: 1.9rem;
    }
  }
  .video-js.vjs-fluid {
    height: 100% !important;
  }
  .vjs-poster {
    background-size: 100% 100%;
  }
}
</style>

<template>
  <section class="water-video">
    <section class="water-video-conetnt">
      <section class="header">
        <div class="street-search">
          <a-select
                  v-model="currStreet"
                  @change="changeStreet"
                  placeholder="街道名称"
                  class="select-main"
          >
            <a-icon
                    slot="suffixIcon"
                    type="caret-down"
                    style="color:rgba(0, 234, 255, 1);width:0.23rem;height:0.11rem"
            />
            <a-select-option
                    :value="item.streetCode"
                    v-for="(item, index) in streetList"
                    :key="index"
            >
              {{ item.streetName }}</a-select-option
            >
          </a-select>
        </div>
        <div class="video-search">
          <div style="width: 3.87rem;">
            <input
                    type="text"
                    :placeholder="type === 1 ? '请输入想要查看的印刷厂名称' : '请输入想要查看的汽修厂名称'"
                    v-model="videoSearch"
                    @keyup.enter="videoSearchBtn(1)"
            />
            <a-icon
                    type="close-circle"
                    style="font-size:0.2rem;cursor: pointer;margin-right: 0.15rem;"
                    v-if="videoSearch != ''"
                    @click="clearSearch"
            />
          </div>
          <div style="border-left: none">
            <img src="@/assets/search.png" alt="" @click="videoSearchBtn(1)" />
          </div>
        </div>
      </section>
      <section class="video-list">
        <section v-if="type === 1 || type ===2" class="video-list-content" v-for="(item, index) in printFactoryList" :key="index">
          <div class="tilte">
            <span>{{ type === 1 ? item.printFactoryName : item.garageName }}</span>
          </div>
          <div class="sub-title">
            <img src="../../assets/heavily/<EMAIL>" alt="">
            <span>四川省成都市金牛区政通路</span>
          </div>
          <livePlayer v-if="type === 2" :playerId="item.index" :vidoeUrl="item.liveUrl" :snapUrl="item.snapUrl" :online="item.online" style="width: 100%;height: 2.12rem" />
          <EZUIKitJs v-if="type === 1" :id="item.id" :width="width" :height="height" :accessToken="item.accessToken" :url="item.ezopen" />
        </section>
        <div v-if="isShow" class="no-more">暂无数据</div>
      </section>
      <section v-if="pages > 1" class="bottom-image">
        <img :src="index === pageNum - 1  ? src1 : src2" alt="" v-for="(item, index) in pages" :key="index" @click="changePage(index)">
      </section>
    </section>
  </section>
</template>

<script lang="ts">
/* eslint-disable @typescript-eslint/ban-ts-ignore */
import { Component, Vue, Watch } from "vue-property-decorator";
import { Table, Radio, Select, Icon } from "ant-design-vue";
import EZUIKitJs from '../details/EZUIKitJs.vue'
import livePlayer from '../details/livePlayer.vue'
import {getToken} from "@/utils/authority";
import {
  getStreet
} from "@/api/headvily-pollution";
@Component({
  name: "moreVideo",
  components: {
    ATable: Table,
    ARadioGroup: Radio.Group,
    ARadioButton: Radio.Button,
    ASelect: Select,
    AIcon: Icon,
    ASelectOption: Select.Option,
    EZUIKitJs,
    livePlayer
  }
})
export default class extends Vue {
  private videoSearch = ''
  private printFactoryList: any[] = [];
  private accessToken = ''
  private width = 351
  private height = 186
  private type = 1
  private pageNum = 1
  private pageSize = 8
  private src1 = require('../../assets/react.png')
  private src2 = require('../../assets/react2x.png')
  private isShow = false
  private socket:any
  private pages = 0
  private currList:any = []
  private streetList:any = []
  private changeStreet() {
    this.videoSearchBtn(1)
  }
  private currStreet = '全部街道'
  // 视频搜索
  private videoSearchBtn(isPage:any) {
    // this.printFactoryList = []
    // this.pageNum  = 1
    let streetCode = this.currStreet == '全部街道' ? undefined : this.currStreet
    if (this.type === 1) {
      this.printFactoryList = []
      this.socket.send(JSON.stringify({ code: 24, keywords: this.videoSearch, token: getToken(), pageNum: isPage ? 1 : this.pageNum, pageSize: this.pageSize, streetCode: streetCode }));
    } else {
      this.socket.send(JSON.stringify({ code: 15,  token: getToken(), keywords: this.videoSearch, pageNum: isPage ? 1 : this.pageNum, pageSize: this.pageSize, streetCode: streetCode }));
    }
    // 监听socket消息
    this.socket.onmessage = this.getMessage;
  }
  private changePage(index:any) {
    this.pageNum = index + 1
    this.videoSearchBtn(0)
  }
  private getMessage(msg:any) {
    const data: any = JSON.parse(msg.data);
    this.currList = []
    // this.loading = false
    const cameraList = data.cameraList ? data.cameraList.records || [] : []
    if (data.code === 24 && !data.success) {
      this.pages = data.cameraList ? data.cameraList.pages : 0
      if (!cameraList.length) {
        this.isShow = true
      } else {
        this.isShow = false
      }
      this.printFactoryList = (cameraList || []).map((item:any, index:any) => {
        this.currList.push(item.printFactoryId)
        item.accessToken = data.accessToken || ''
        item.id = index + '_' + new Date().getTime()
        return item
      })
      console.log(this.printFactoryList, 'this.printFactoryList')
    }
    if (data.code === -15 && data.success) {
      this.pages = data.cameraList ? data.cameraList.pages : 0
      const cameraList = data.cameraList ? data.cameraList.records || [] : []
      this.printFactoryList = cameraList.map((item:any, index:any) => {
        item.index = index + '_' + new Date().getTime()
        return item
      })
      if (!cameraList.length) {
        this.isShow = true
      } else {
        this.isShow = false
      }
    }
  }
  // 清除视频搜索
  private clearSearch() {
    this.videoSearch = ''
    this.videoSearchBtn(1)
  }
  private getStreet() {
    getStreet().then(res => {
      this.streetList = res.data.data || []
      this.streetList.unshift({
        streetCode: '全部街道',
        streetName: '全部街道'
      })
    })
  }
  mounted() {
    const data:any = this.$route.params.socketData
    this.socket = this.$route.params.socket
    this.type = Number(this.$route.params.type)
    if (this.type === 1) {
      this.videoSearchBtn(1)
    } else if (this.type === 2) {
      this.videoSearchBtn(1)
    }
    this.getStreet()
  }
}
</script>
