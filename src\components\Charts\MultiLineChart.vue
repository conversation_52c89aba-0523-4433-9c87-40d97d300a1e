<style lang="less" scoped>
.no-data {
  font-size: 0.2rem;
  position: relative;
  .no-datas {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}
</style>
<template>
  <div
    v-if="
      airDataProp.airTrendYesterday.length > 0 ||
        airDataProp.airToday1.length > 0 ||
        airDataProp.airToday2.length > 0 ||
        airDataProp.airAverage.length > 0
    "
    :id="id"
    :style="{ height: height, width: width }"
  />
  <div v-else class="no-data" :style="{ height: height, width: width }">
    <div class="no-datas">设备离线</div>
  </div>
</template>

<script lang="ts">
import * as echarts from 'echarts'
import { Component, Prop, Watch } from "vue-property-decorator";
import { mixins } from "vue-class-component";
import ResizeMixin from "./mixins/resize";
enum Type {
  TREND = 1,
  CONTRAST = 2
}
interface AirDataProp {
  airTrendYesterday: string | number[];
  airToday1: string | number[];
  airToday2: string | number[];
  airAverage: string | number[];
  unit: string;
}
interface AirContrast {
  monthList: string | number[];
  thisYear: string | number[];
  lastYear: string | number[];
  thisYearName?: string;
  lastYearName?: string;
}
@Component({
  name: "MultiLineChart"
})
export default class extends mixins(ResizeMixin) {
  @Prop({ default: "chart" }) private className!: string;
  @Prop({ default: "chart" }) private id!: string;
  @Prop({ default: "200px" }) private width!: string;
  @Prop({ default: "200px" }) private height!: string;
  @Prop({ default: 1 }) private type!: number;
  @Prop({ required: true }) private airDataProp!: AirDataProp;
  @Prop({ required: true }) private airContrast!: AirContrast;
  @Watch("airDataProp", { immediate: true, deep: true })
  public onAirDataProp(newValue: string, oldValue: string) {
    if (this.chart) {
      this.chart.clear();
    }
    this.$nextTick(() => {
      this.initChart();
    });
  }
  @Watch("airContrast", { immediate: true, deep: true })
  public onAirContrast(newValue: string, oldValue: string) {
    this.MONTHLIST = this.airContrast.monthList;
    if (this.chart) {
      this.chart.clear();
    }
    this.$nextTick(() => {
      this.initChart();
    });
  }
  private HOURLIST: Array<string> = [
    "00:00",
    "01:00",
    "02:00",
    "03:00",
    "04:00",
    "05:00",
    "06:00",
    "07:00",
    "08:00",
    "09:00",
    "10:00",
    "11:00",
    "12:00",
    "13:00",
    "14:00",
    "15:00",
    "16:00",
    "17:00",
    "18:00",
    "19:00",
    "20:00",
    "21:00",
    "22:00",
    "23:00"
  ];

  private MONTHLIST: string | number[] = this.airContrast.monthList;

  mounted() {
    if (this.chart) {
      this.chart.clear();
    }
    this.$nextTick(() => {
      this.initChart();
    });
  }

  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.clear();
    this.chart.dispose();
    this.chart = null;
  }

  private initChart() {

    if(this.chart === null || this.chart === undefined) {
      this.chart = echarts.init(
        document.getElementById(this.id) as HTMLDivElement
      );
    }
    const colors: Array<string> = [
      "#1D9DFF",
      "#FF9F7F",
      "#FB7293",
      "#E7BCF3",
      "#8378EA",
      "#32C5E9",
      "#9FE6B8",
      "#FFDB5C"
    ];

    const TrendSeriesData = [
      {
        name: "昨日趋势",
        data: this.airDataProp.airTrendYesterday,
        type: "line",
        lineStyle: {
          color: "#12B160" //改变折线颜色
        },
        symbolSize: 8,
        itemStyle: {
          color: "#12B160", //改变折线点的颜色
          borderColor: "#12B160",
          borderWidth: 2
        }
      },
      {
        name: "今日趋势",
        data: this.airDataProp.airToday1,
        type: "line",
        lineStyle: {
          color: "#EA7638" //改变折线颜色
        },
        symbolSize: 8,
        itemStyle: {
          color: "#EA7638", //改变折线点的颜色
          borderColor: "#EA7638",
          borderWidth: 2
        }
      },
      {
        name: "今日趋势",
        data: this.airDataProp.airToday2,
        type: "line",
        lineStyle: {
          color: "#EA7638",
          type: "dashed"
        },
        symbolSize: 8,
        itemStyle: {
          color: "#EA7638", //改变折线点的颜色
          borderColor: "#EA7638",
          borderWidth: 2
        }
      },
      {
        name: "昨日平均",
        data: this.airDataProp.airAverage,
        type: "line",
        lineStyle: {
          color: "#0492FF" //改变折线颜色
        },
        symbolSize: 0,
        itemStyle: {
          color: "#0492FF", //改变折线点的颜色
          borderColor: "#0492FF",
          borderWidth: 0
        }
      }
    ];

    const ContrastSeriesData = [
      {
        // 今年
        name: this.airContrast.thisYearName,
        data: this.airContrast.thisYear,
        type: "line",
        lineStyle: {
          color: "#1D9DFF" //改变折线颜色
        },
        symbolSize: 8,
        itemStyle: {
          color: "#298DD4", //改变折线点的颜色
          borderColor: "#298DD4",
          borderWidth: 2
        }
      },
      {
        // 去年
        name: this.airContrast.lastYearName,
        data: this.airContrast.lastYear,
        type: "line",
        lineStyle: {
          color: "#FFBF35" //改变折线颜色
        },
        symbolSize: 8,
        itemStyle: {
          color: "#FFBF35", //改变折线点的颜色
          borderColor: "#FFBF35",
          borderWidth: 2
        }
      }
    ];
    this.chart.setOption({
        backgroundColor: "transparent",
        grid: {
          left: 50,
          bottom: 30,
          right: 20,
          top: 40
        },
        legend: {
          top: 0,
          right: 20,
          textStyle: {
            color: "#fff",
            fontWeight: "400",
            fontFamily: "Source Han Sans CN",
            fontSize: 14
          },
          itemWidth: 40
        },
        xAxis: {
          type: "category",
          data: this.type===Type.TREND? this.HOURLIST:this.MONTHLIST,
          axisLabel: {
            textStyle: {
              fontSize: 20,
              color: "#fff"
            }
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: "#fff"
            }
          },
          axisTick: {
            show: false
          }
        },
        yAxis: {
          type: "value",
          name: this.type===Type.TREND? this.airDataProp.unit:"",
          nameTextStyle: {
            color: "#fff",
            fontSize: 16
          },
          axisLabel: {
            textStyle: {
              fontSize: 20,
              color: "white"
            }
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          splitLine: {
            lineStyle: {
              type: "dashed"
            }
          }
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross",
            label: {
              backgroundColor: "#6a7985"
            }
          },
          formatter: function(params: Array<any>): string {
            let ret=`${params[0].name}`;
            params.forEach((series: any) => {
              if(new RegExp(`${series.seriesName}`,"g").test(ret))
                return;
              if(series.value!=="-") {
                ret+=`<br/>${series.seriesName}: ${series.value}`;
              }
            });
            return ret;
          }
        },
        series: this.type===Type.TREND? TrendSeriesData:ContrastSeriesData
      } as unknown as EChartOption<EChartOption.SeriesLine>);
  }
}
</script>
