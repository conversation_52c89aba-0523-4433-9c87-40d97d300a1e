<template>
  <div class="people-area">
    <peopleRow :list="title" rowTitle="row-title" :height="40"></peopleRow>

    <!-- <Swiper
      :mousewheel="true"
      :autoplay="swiper_options.autoplay"
      :loop="swiper_options.loop"
      :speed="swiper_options.speed"
      :direction="swiper_options.direction"
      :slidesPerView="swiper_options.slidesPerView"
      :spaceBetween="swiper_options.spaceBetween"
      style="height: 252px"
    >
      <SwiperSlide
        v-for="(item, index) in data1"
        :key="index"
        style="height: 36px"
      >
        
      </SwiperSlide>
    </Swiper> -->
    <div class="meterBox">
      <peopleRow 
        v-for="(item, index) in data1"
        :key="index"
        :list="item"
        :index="index"
      />
    </div>
  </div>
</template>

<script>
import { Swiper, SwiperSlide } from 'vue-awesome-swiper'
import peopleRow from './peopleRow.vue'
export default {
  name: 'materialsList',
  props: {
    listData: {
      type: Array,
      default: () => [],
    },
  },
  components: {
    peopleRow,
    Swiper,
    SwiperSlide
  },
  data() {
    return {
      title: {
        departmentName: '储备名称',
        name: '所属单位',
        tel: '管理人员',
        state: '联系电话',
      },
      swiper_options: {
        autoplay: {
          delay: 3000,
          disableOnInteraction: false,
        },
        loop: false,
        slidesPerView: 7,
        spaceBetween: 0,
        direction: 'vertical',
      },
      data1: [],
    }
  },
  watch: {
    listData: {
      handler(nval, oval) {
        if (nval) {
          this.data1 = nval.map((e) => {
            return {
              departmentName: e.name || '--',
              name: e.baseDepartmentName,
              tel: e.userName,
              state: e.phone || '--',
            }
          })
        } else {
          this.data1 = []
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {},
}
</script>

<style lang="less" scoped>
.meterBox{
  height: 252px;
  overflow-y: auto;
  &::-webkit-scrollbar-track {
    border-radius: 10px;
    background: rgba(57, 177, 255, 0);
  }
  &::-webkit-scrollbar-thumb {
    border-radius: 10px;
    width: 1px;
    background: rgba(21, 62, 105, 0.5);
  }
}
</style>