<template>
  <div :id="id" :style="{ height: height, width: width }" />
</template>

<script lang="ts">
import * as echarts from 'echarts'
import { Component, Prop, Watch } from "vue-property-decorator";
import { mixins } from "vue-class-component";
import ResizeMixin from "./mixins/resize";
interface TaskData {
  bottomList: string[];
  dataList: any[];
  typeList: string[];
}
@Component({
  name: "TaskLineChart"
})
export default class extends mixins(ResizeMixin) {
  // @Prop({ default: "chart" }) private className!: string;
  @Prop({ default: "chart" }) private id!: string;
  @Prop({ default: "200px" }) private width!: string;
  @Prop({ default: "200px" }) private height!: string;
  @Prop({ required: true }) private propData!: TaskData;
  @Watch("propData", { immediate: true, deep: true }) public onMsgChanged(
    newValue: TaskData,
    oldValue: TaskData
  ) {
    if (this.propData) {
      console.log(this.propData, 'propData')
      if (this.chart) {
        this.chart.clear();
      }
      this.$nextTick(() => {
        this.initChart();
      });
    }
  }
  mounted() {
    if (this.propData) {
      if (this.chart) {
        this.chart.clear();
      }
      this.initChart();
    }
  }
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.clear();
    this.chart.dispose();
    this.chart = null;
  }

  private initChart() {

    if(this.chart === null || this.chart === undefined) {
      this.chart = echarts.init(
        document.getElementById(this.id) as HTMLDivElement
      );
    }
    this.chart.setOption({
      color: ["#01D0FE", "#357ADD", "#5D17DB"],
      backgroundColor: "transparent",
      grid: {
        height: "80%",
        width: "100%",
        bottom: 20,
        right: 0,
        top: 35,
        left: "left",
        containLabel: true
      },
      legend: {
        icon: "pin",
        data: this.propData.typeList,
        left: 0,
        textStyle: {
          color: "#fff"
        }
      },
      xAxis: {
        type: "category",
        nameTextStyle: {
          color: "#fff"
        },
        data: this.propData.bottomList,
        axisLabel: {
          textStyle: {
            fontSize: 12,
            color: "white"
          }
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        splitLine: {
          show: true,
          lineStyle: {
            // type: "dashed"
            color: "RGBA(2, 39, 75, 1)"
          }
        }
      },
      yAxis: {
        type: "value",
        nameTextStyle: {
          color: "#fff"
          // align: "center"
        },
        axisLabel: {
          textStyle: {
            fontSize: 12,
            color: "white"
          }
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        splitLine: {
          show: false,
          lineStyle: {
            // type: "dashed"
            color: "RGBA(2, 39, 75, 1)"
          }
        }
      },
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "cross",
          label: {
            backgroundColor: "#6a7985"
          }
        }
      },
      series: [
        {
          // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
          // @ts-ignore
          name: "未完成",
          data: this.propData.dataList[0],
          type: "line",
          smooth: true,
          lineStyle: {
            color: "rgba(1, 208, 254, 1)" //改变折线颜色
          },
          symbol: "circle",
          symbolSize: 0,
          areaStyle: {
            color: {
              type: "linear",
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: "RGBA(1, 208, 254, 1)"
                },
                {
                  offset: 1,
                  color: "RGBA(1, 208, 254, 0)" // 100% 处的颜色
                }
              ],
              global: false
            }
          }
        },
        {
          // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
          // @ts-ignore
          name: "已关闭",
          data: this.propData.dataList[1],
          type: "line",
          smooth: true,
          lineStyle: {
            color: "rgba(53, 122, 221, 1)" //改变折线颜色
          },
          symbol: "circle",
          symbolSize: 0,
          areaStyle: {
            color: {
              type: "linear",
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: "rgba(53, 122, 221, 1)"
                },
                {
                  offset: 1,
                  color: "rgba(53, 122, 221, 0)" // 100% 处的颜色
                }
              ],
              global: false
            }
          }
        },
        {
          // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
          // @ts-ignore
          name: "已完成",
          data: this.propData.dataList[2],
          type: "line",
          smooth: true,
          lineStyle: {
            color: "rgba(93, 23, 219, 1)" //改变折线颜色
          },
          symbol: "circle",
          symbolSize: 0,
          areaStyle: {
            color: {
              type: "linear",
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: "rgba(93, 23, 219, 1)"
                },
                {
                  offset: 1,
                  color: "rgba(93, 23, 219, 0)" // 100% 处的颜色
                }
              ],
              global: false
            }
          }
        },
        // {
        //   // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
        //   // @ts-ignore
        //   name: "逾期",
        //   data: this.propData.dataList[3],
        //   type: "line",
        //   smooth: true,
        //   lineStyle: {
        //     color: "rgba(53, 122, 222, 1)" //改变折线颜色
        //   },
        //   symbol: "circle",
        //   symbolSize: 0,
        //   areaStyle: {
        //     color: {
        //       type: "linear",
        //       x: 0,
        //       y: 0,
        //       x2: 0,
        //       y2: 1,
        //       colorStops: [
        //         {
        //           offset: 0,
        //           color: "rgba(53, 122, 222, 1)"
        //         },
        //         {
        //           offset: 1,
        //           color: "rgba(53, 122, 222, 0)" // 100% 处的颜色
        //         }
        //       ],
        //       global: false
        //     }
        //   }
        // }
      ]
    } as EChartOption<EChartOption>);
  }
}
</script>
