<!-- eslint-disable no-irregular-whitespace -->
<style lang="less">
// @import '@/styles/mixin.scss';
// $marginBot: 15px;
.task-image {
  .el-image {
    display: inline-block;
    height: 120px;
    width: 120px;
    margin: 0 10px 10px 0;
  }
}
.task-attachment {
  .attachment-body {
    .el-image {
      display: block;
      height: 100px;
      width: 100px;
      margin: 0 10px 10px 0;
    }
  }
}
.feedback {
  .el-dialog__header {
    border-bottom: 1px solid rgba(59, 182, 111, 0.1);
  }
}
.alarm-info {
  color: #212121;
  font-size: 16px;
  margin-bottom: 15px;
  // span:last-child {
  //   margin-left: 15px;
  // }
}
.alarm-table {
  width: 800px;
}
.new-task-upload {
  .el-upload-dragger {
    background-color: #fff;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    width: 360px;
    height: 40px;
    text-align: center;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
}
.feedback {
  .el-timeline-item__timestamp.is-top {
    margin-bottom: 0 !important;
    padding-top: 0 !important;
  }
  .el-timeline-item__tail {
    border-left-color: rgba(42, 159, 170, 0.14) !important;
    height: calc(100% - 12px) !important;
    margin-top: 18px !important;
  }
  .el-timeline-item__node {
    // background-color: transparent !important;
    background-color: #13CE66;
    border: 3px solid #fff;
    box-shadow: 0 0 1px 1.5px #13CE66;
  }
  .el-timeline-item__node--normal {
    top: 6px;
  }
}
.feedbacks {
  .el-timeline-item__timestamp.is-top {
    margin-bottom: 8px !important;
    padding-top: 4px !important;
  }
}

.task-container {
  .el-dialog__body {
    .task-attachment {
      margin-bottom: 15px;
      display: flex;
      flex-direction: row;
      .attachment-content {
        img {
          width: 20px;
          height: 20px;
          display: inline-block;
        }
        a {
          // @include textEllipsis;
          display: inline-block;
          width: 100%;
          font-size: 14px;
        }
      }
    }
    .infor-describe {
      font-size: 18px;
    }
    .text-bold {
      color: #303133;
      font-weight: bold;
    }
    .margin-bot-20 {
      margin-bottom: 20px;
    }
  }
}

.customConfirm {
  .el-message-box__content {
    color: red;
  }
}
</style>

<style lang="less" scoped>
// @import '@/styles/mixin.scss';
// $fontSize: 24px;
// $marginBot: 15px;
.task-body {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  .task-title {
    margin-bottom: 15px;
    .titile-content {
      margin-bottom: 5px;
      font-size: x-large;
      font-weight: 500;
    }
    .title-date {
      color: #9b9c97;
      font-size: 12px;
    }
    .title-type {
      font-size: 24px;
      font-weight: bold;
    }
  }
  .task-operation {
    display: flex;
    justify-content: space-between;
    // margin-bottom: 30px;
    margin: 20px 0;
    .left {
      display: flex;
      align-items: center;
    }
    .right {
      width: 88px;
      height: 32px;
      border: 1px solid rgba(153, 153, 153, 0.2);
      // opacity: 0.2;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      font-family: PingFang SC;
      font-weight: 400;
      color: #4d4d4d;
      cursor: pointer;
      span {
        margin-left: 5px;
      }
    }
  }
  .task-content {
    // margin-bottom: $marginBot;
    .task-describe {
      // margin-bottom: $marginBot;
      // @include textEllipsis;
      word-wrap: break-word;
    }
    .task-image {
      overflow-y: auto;
    }
  }
  .task-attachment {
    // margin-bottom: $marginBot;
    // display: flex;
    // flex-direction: row;
    width: 100%;
    display: inline-block;
    .attachment-content {
       width: 100%;
      img {
        width: 20px;
        height: 20px;
        display: inline-block;
      }
      a {
        @include textEllipsis;
        display: inline-block;
        width: 100%;
        font-size: 14px;
      }
    }
  }
  .log-container {
    padding: 0 0 0 25px;
    color: #858992;
  }
}
.drawer-content {
  width: 100%;
  height: 100%;
  padding: 0 20px;
  .footer {
    width: 100%;
    margin-top: 50px;
    padding-left: 50px;
  }
}
.feedback-list {
  .feedback-text {
    color: #999999;
    font-size: 16px;
    /*margin-bottom: 30px;*/
    /*&::before {*/
    /*  position: absolute;*/
    /*  width: 12px;*/
    /*  height: 12px;*/
    /*  content: '';*/
    /*  border: 1px solid #3BB66F;*/
    /*  border-radius: 50%;*/
    /*  top: 8px;*/
    /*  left: 15px;*/
    /*}*/
    .feedback-com {
      color: #228fff;
      font-size: 14px;
      cursor: pointer;
      padding-left: 20px;
    }
  }
}
.feedback {
  .feedback-content {
    margin-bottom: 40px;
    &:last-child {
      margin-bottom: 0;
    }
    .feedback-txt {
      padding-left: 20px;
      span {
        &:nth-child(1) {
          display: inline-block;
          text-align: left;
          width: 80px;
          color: #202020;
        }
        &:nth-child(2) {
          color: #333333;
        }
      }
    }
  }
}
.noData {
  text-align: center;
  color: #909399;
}
.text-bold {
  font-size: 18px;
  font-weight: bold;
}
.text-normal {
  font-size: 16px;
  white-space: nowrap;
}
.task-result-style,
.comment-style,
.task-assign-style,
.task-process-detail-style {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
}
.feedback-style {
  display: flex;
  flex-direction: row;
  justify-content: center;
}
.attachment-content {
  cursor: pointer;
  img {
    width: 64px;
    height: 64px;
    display: inline-block;
  }
  a {
    @include textEllipsis;
    display: inline-block;
    width: 100%;
    font-size: 14px;
  }
}
::v-deep .task-body {
  .el-timeline-item__content {
    .el-card__header {
      padding: 5px 20px;
      border-bottom: 1px solid #e6ebf5;
      -webkit-box-sizing: border-box;
      box-sizing: border-box;
      background-color: #fff !important;
    }
    .el-card__body {
      padding: 20px;
      background-color: #fff !important;
    }
  }
  .el-collapse {
    border-top: 1px solid #e6ebf5;
    border-bottom: 1px solid #e6ebf5;
  }
}
</style>

<template>
  <div class="main">
    <div class="main-left">
      <section v-loading="taskMainLoading">
        <div
          v-if="!taskId"
          class="noData"
        >暂无数据</div>
        <!-- 任务内容和附件 -->
        <div
          v-else
          class="task-body"
        >
          <!-- <div class="task-title">
            <el-row
              :gutter="60"
              type="flex"
              align="middle"
              justify="start"
            >
              <el-col :span="1">
                <svg-icon
                  :icon-class="`taskType-${Number(task.taskType) - 1}`"
                  style="width: 48px; height: 48px"
                />
              </el-col>
              <el-col :span="20">
                <div style="font-size: x-large; font-weight: 500">
                  {{ task.title }}
                </div>
                <div class="title-date">{{ task.createTime }}</div>
              </el-col>
              <div class="title-type">
                {{ (Number(task.taskType) - 1) | taskTypeFilter }}
              </div>
            </el-row>
          </div> -->
          <div class="left-top">
            <div class="left-top-box">
              <div class="title">
                <img
                  class="biaodanIcon"
                  src="~@/assets/waterSource/biaodan.png"
                >{{ taskDetail.title }}</div>
              <div
                class="btn-back pointer"
                @click="returnToTable"
              >
                返回
              </div>
            </div>
            <div class="left-top-box">
              <div class="title-my"> {{ taskStateForMe | taskTypeFilter }}</div>
              <div
                class="btn-refresh pointer"
                @click="Refresh"
              >
                <img
                  class="refreshIcon"
                  src="@/assets/waterSource/shuaxin.png"
                >
                刷新
              </div>
            </div>
          </div>
          <!-- 操作按钮 -->
          <div class="task-operation">
            <div class="left">
              <!-- <el-button
                size="small"
                type="info"
                plain
                icon="el-icon-back"
                @click="returnToTable"
              >返回</el-button> -->
              <el-button
                v-if="isSender"
                :disabled="isFinished"
                type="danger"
                plain
                size="small"
                @click=";(taskResultCloseVisible = true), (isNodeClosed = false)"
              >关闭任务</el-button>
              <el-button
                v-if="taskDetail.userId === userId"
                type="danger"
                plain
                size="small"
                @click="handleDelete"
              >删除任务</el-button>
              <el-button
                v-if="isSender"
                :disabled="isFinished"
                type="success"
                plain
                size="small"
                @click="taskResultVisible = true"
              >完成</el-button>
              <el-button
                v-if="isCopyTo"
                type="primary"
                plain
                size="small"
                @click="openReplyTask"
              >评论</el-button>
              <el-button
                v-if="taskNodeIdList.indexOf(userId) > -1 && taskStateForMe !== 1"
                icon="el-icon-upload2"
                class="global-btn-color"
                type="primary"
                size="small"
                @click="exportExcel"
              >导出Excel</el-button>
            </div>
            <!-- <div
              class="right"
              @click="Refresh"
            >
              <img
                src="@/assets/images/<EMAIL>"
                alt=""
              >
              <span>刷新</span>
            </div> -->
          </div>

          <div class="left-content">
            <el-collapse v-model="taskMainActiveNames">
              <el-collapse-item
                title="任务详情"
                name="1"
              >
                <!-- <div class="content">
                  <span class="content-label">任务街道：</span>
                  <span class="content-text">{{ taskDetail.departmentName }}</span>
                </div> -->
                <div class="content">
                  <span class="content-label">详细地址：</span>
                  <span
                    class="content-text"
                    style="cursor: pointer;"
                    @click="handleClickMap(1)"
                  >
                    {{ taskDetail.address }}
                    <img
                      v-if="taskDetail.address"
                      src="@/assets/images/position.png"
                      alt=""
                      style="width: 20px; cursor: pointer; vertical-align:middle"
                    >
                    <span
                      v-else
                      class="content-text"
                    >--</span>
                  </span>

                </div>
                <div class="content">
                  <span class="content-label">描　　述：</span>
                  <span class="content-text">{{ taskDetail.content }}</span>
                </div>
                <div
                  class="content"
                  style="display:flex;"
                >
                  <div style="width:80px">
                    <span
                      class="content-label"
                    >附　　件：</span>
                  </div>

                  <div style="width:90%">
                    <div
                      v-if="picList.length>0"
                      class="img-box"
                      style="margin-top:10px"
                    >
                      <template v-for="(pic, indexs) in picList">
                        <ComImage
                          :key="indexs"
                          :src="pic"
                          fit="cover"
                          :preview-src-list="[...picList.map(it => it)]"
                        />
                      </template>
                      <!-- <el-image
                        v-for="(pic, index) in picList"
                        :key="index"
                        :src="pic"
                        fit="cover"
                        lazy
                        @click="openImg(index)"
                      /> -->
                    </div>
                    <div
                      v-if="fileList.length > 0"
                      class="task-attachment"
                    >
                      <div
                        v-for="(file, index) in fileList"
                        :key="index"
                        class="attachment-content"
                        @click="downLoadBlob(file)"
                      >
                        <img
                          v-if="file.type === 0"
                          src="@/assets/local_images/tp2.png"
                        >
                        <img
                          v-if="file.type === 1"
                          src="@/assets/local_images/word2.png"
                        >
                        <img
                          v-if="file.type === 2"
                          src="@/assets/local_images/xls2.png"
                        >
                        <img
                          v-if="file.type === 3"
                          src="@/assets/local_images/pdf2.png"
                        >
                        <img
                          v-if="file.type === 4"
                          src="@/assets/local_images/ppt2.png"
                        >
                        <img
                          v-if="file.type === 5"
                          src="@/assets/local_images/zip2.png"
                        >
                        <div
                          style="white-space: nowrap;
                      overflow: hidden;text-overflow: ellipsis;"
                        >{{ file.fileName }}</div>
                      </div>
                    </div>
                    <div
                      v-if="picList.length<1&&fileList.length<1"
                      class="content-text"
                    >暂无附件</div>
                  </div>
                </div>
              </el-collapse-item>
              <el-collapse-item
                v-if="alarmListData.length"
                title="告警信息"
                name="2"
              >
                <!-- <template slot="title">
                  <div class="text-bold text-indent">告警信息</div>
                </template> -->
                <div class="alarm-info">
                  <span
                    style="margin-right: 20px"
                  >街道：
                    {{ alarmListData.length ? alarmListData[0].streetName : '--' }}
                  </span>
                  <span
                    style="margin-right: 20px"
                  >告警站点：
                    {{
                      alarmListData.length ? alarmListData[0].stationName : '--'
                    }}</span>
                  <span @click="handleClickMap(2)">
                    告警地址：{{
                      alarmListData.length ? alarmListData[0].stationAddress : '--'
                    }}<img
                      src="@/assets/images/position.png"
                      alt=""
                      style="width: 20px; cursor: pointer;vertical-align:middle"
                    ></span>
                  <el-button
                    v-if="historicalList.length !== 0"
                    type="success"
                    style="margin-left: 20px"
                    @click="handleHistorical(taskDetail.taskId)"
                  >历史数据</el-button>
                  <!-- <el-button
              v-if="historicalList.length !== 0"
              type="primary"
              style="margin-left: 20px;"
              @click="handleHistory(taskDetail)"
            >历史监测</el-button> -->
                </div>
                <el-table
                  ref="alarmTable"
                  :data="alarmListData"
                  :row-style="{ height: '54px' }"
                  :header-cell-style="{
                    background: '#F5F7FA',
                    color: '#333333',
                    fontWeight: 'bold',
                  }"
                  class="alarm-table"
                  border
                >
                  <el-table-column
                    label="告警内容"
                    align="center"
                    prop="alarmContent"
                  />
                  <el-table-column
                    label="告警值"
                    align="center"
                    prop="alarmValue"
                  >
                    <template slot-scope="scope">
                      {{ scope.row.alarmValue
                      }}{{ scope.row.alarmItemUnit }}</template>
                  </el-table-column>
                  <el-table-column
                    label="告警阈值"
                    align="center"
                    prop="alarmThreshold"
                  >
                    <template slot-scope="scope">
                      <span
                        v-if="
                          scope.row.alarmValueType === 0 ||
                            scope.row.alarmValueType === null
                        "
                      >
                        {{ scope.row.alarmThreshold
                        }}{{ scope.row.alarmItemUnit }}</span>
                      <span v-if="scope.row.alarmValueType === 1">
                        {{ scope.row.alarmMinValue
                        }}{{ scope.row.alarmItemUnit }}&lt;,&lt;
                        {{ scope.row.alarmMaxValue
                        }}{{ scope.row.alarmItemUnit }}</span>
                      <span
                        v-if="scope.row.alarmValueType === 2"
                      >&lt; {{ scope.row.alarmMinValue
                      }}{{ scope.row.alarmItemUnit }},&gt;
                        {{ scope.row.alarmMaxValue
                        }}{{ scope.row.alarmItemUnit }}</span>
                      <span
                        v-if="scope.row.alarmValueType === 3"
                      >&lt; {{ scope.row.alarmMinValue
                      }}{{ scope.row.alarmItemUnit }}
                      </span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    label="告警时间"
                    align="center"
                    prop="alarmTime"
                  />
                </el-table>
              </el-collapse-item>
              <el-collapse-item
                v-if="myTaskNodeList.length"
                name="5"
                title="执行节点"
              >
                <!-- <template slot="title">
                  <div class="text-bold text-indent">执行节点</div>
                </template> -->
                <el-timeline class="feedback">
                  <el-timeline-item
                    v-for="(item, index) in myTaskNodeList"
                    :key="index"
                    placement="top"
                  >
                    <div class="feedback-list">
                      <span
                        :title="
                          Number(item.completeStatus) === 2 ? '上级已退单该任务' : ''
                        "
                        class="feedback-text"
                      >{{ item.taskNodeContent
                      }}<span
                        class="feedback-com"
                        :style="{
                          'text-decoration':
                            Number(item.completeStatus) === 0 ? 'underline' : '',
                        }"
                        @click="handleNodeComplete(item)"
                      >{{
                        Number(item.completeStatus) === 0
                          ? '完成任务'
                          : Number(item.completeStatus) === 1
                            ? '已完成'
                            : '已退单'
                      }}</span><span
                        v-if="Number(item.completeStatus) === 0"
                        class="feedback-com"
                        :style="{
                          'text-decoration':
                            Number(item.completeStatus) === 0 ? 'underline' : '',
                        }"
                        @click="handleNodeClose1(item)"
                      >任务退单</span></span>
                    </div>
                  </el-timeline-item>
                </el-timeline>
              </el-collapse-item>
              <el-collapse-item
                name="6"
                title="执行流程"
              >
                <!-- <template slot="title">
                  <div class="text-bold text-indent">处理流程</div>
                </template> -->
                <flow-chart
                  :flow-chart-id="`flowChartUniqueId_${new Date().getTime()}`"
                  :task-id="taskId"
                  :user-id="userId"
                  :model-data="flowChartData"
                  @detail="handleDetail"
                  @complete="handleNodeComplete"
                  @add="handleAddNode"
                  @close="handleNodeClose"
                  @params="handleParams"
                />
              </el-collapse-item>
              <el-collapse-item
                name="7"
                title="执行反馈"
              >
                <!-- <template slot="title">
                  <div class="text-bold text-indent">执行反馈</div>
                </template> -->
                <el-timeline
                  v-if="executiveFeedbackList.length"
                  class="feedback feedbacks"
                >
                  <!-- class="feedback feedbacks" -->
                  <el-timeline-item
                    v-for="(item,index) in executiveFeedbackList"
                    :key="index"
                    placement="top"
                    color="#13CE66"
                  >
                    <el-card>
                      <div class="card-title">
                        <div>
                          <!-- <img :src="item.avatarUrl"> -->
                          <el-avatar
                            :src="item.avatarUrl"
                          />
                          <span>{{ item.accountName }}</span>
                        </div>
                        <div>{{
                          item.time
                            ? item.time.substr(0, item.time.length - 3)
                            : undefined
                        }}</div>
                      </div>
                      <div style="margin-left:50px">
                        <p class="pText"> {{ item.remark }}</p>
                        <div
                          v-if="item.taskAnnexList.length>0"
                          class="img-box"
                        >
                          <template v-for="(pic, indexs) in item.taskAnnexList">
                            <ComImage
                              :key="indexs"
                              :src="pic.annexUrl"
                              fit="cover"
                              :preview-src-list="[...item.taskAnnexList.map(it => it.annexUrl)]"
                            />
                          </template>
                          <!-- <el-image
                            v-for="(pic, indexs) in item.taskAnnexList"
                            v-show="pic.annexUrl !== null"
                            :key="indexs"
                            :src="pic.annexUrl"
                            fit="cover"
                            @click="openRestImg(indexs, item.taskAnnexList)"
                          /> -->
                        </div>
                      </div>
                    </el-card>
                  </el-timeline-item>
                </el-timeline>
                <!-- <el-timeline-item
                    v-for="(item, index) in executiveFeedbackList"
                    :key="index"
                    :timestamp="parseTime(item.time)"
                    placement="top"
                  >
                    <el-card
                      shadow="never"
                      style="width: 80%"
                    >
                      <el-row
                        slot="header"
                        :gutter="10"
                        type="flex"
                        align="middle"
                        justify="start"
                      >
                        <el-col :span="1.5">
                          <el-avatar
                            :src="item.avatarUrl"
                            icon="el-icon-user-solid"
                          />
                        </el-col>
                        <el-col :span="20">
                          <span>{{ item.accountName }}</span>
                        </el-col>
                        <el-col :span="8">
                          <span>{{
                            item.time
                              ? item.time.substr(0, item.time.length - 3)
                              : undefined
                          }}</span>
                        </el-col>
                      </el-row>
                      <div class="task-content">
                        <div class="task-describe">
                          {{ item.remark }}
                          <div class="task-image">
                            <el-image
                              v-for="(pic, indexs) in item.taskAnnexList"
                              v-show="pic.annexUrl !== null"
                              :key="indexs"
                              :src="pic.annexUrl"
                              fit="cover"
                              lazy
                              @click="openRestImg(indexs, item.taskAnnexList)"
                            />
                          </div>
                        </div>
                      </div>
                    </el-card>
                  </el-timeline-item> -->
                <div
                  v-else
                >暂无反馈</div>
              </el-collapse-item>
              <!--        <el-collapse-item name="6">-->
              <!--          <template slot="title">-->
              <!--            <div class="text-bold text-indent">执行反馈</div>-->
              <!--          </template>-->
              <!--          <div-->
              <!--            v-for="(item, index) in executiveFeedbackList"-->
              <!--            :key="index"-->
              <!--            class="log-container"-->
              <!--          >-->
              <!--            <el-row :gutter="10" type="flex" align="middle">-->
              <!--              <el-col :span="1">-->
              <!--                <el-avatar :src="item.avatarUrl" icon="el-icon-user-solid" />-->
              <!--              </el-col>-->
              <!--              <el-col :span="6">-->
              <!--                <span v-html="item.accountName" />-->
              <!--              </el-col>-->
              <!--              <el-col :span="12">-->
              <!--                <span v-html="item.content" />-->
              <!--              </el-col>-->
              <!--              <el-col :span="4">-->
              <!--                <span>{{ item.time ?
          item.time.substr(0, item.time.length-3) : undefined }}</span>-->
              <!--              </el-col>-->
              <!--            </el-row>-->
              <!--            <el-divider v-if="index < taskLogListData.length - 1" />-->
              <!--          </div>-->
              <!--        </el-collapse-item>-->
              <!-- 已读回置 -->
              <el-collapse-item
                name="8"
                title="已读回执"
                class="collapse-item-red"
              >
                <!-- <template slot="title">
                  <div class="text-bold text-indent">已读回执</div>
                </template> -->
                <div>
                  <el-tabs
                    v-model="activeName"
                    type="border-card"
                  >
                    <el-tab-pane
                      :label="'已读(' + read.length + ')'"
                      name="first"
                    >
                      <div class="message-journal">
                        <div
                          v-for="(item, index) in read"
                          :key="index"
                          style="display: inline-flex; align-items: center"
                        >
                          <img
                            src="@/assets/images/<EMAIL>"
                            alt=""
                            style="width: 16px; height: 16px"
                          >
                          <span style="margin: 0 20px 0 8px">
                            {{ item.departmentName }} - {{ item.userName }}
                            {{ index + 1 === read.length ? '' : ';' }}</span>
                        </div>
                      </div>
                    </el-tab-pane>
                    <el-tab-pane
                      :label="'未读(' + unread.length + ')'"
                      name="second"
                    >
                      <div class="message-journal">
                        <div
                          v-for="(item, index) in unread"
                          :key="index"
                          style="display: inline-flex; align-items: center"
                        >
                          <img
                            src="@/assets/images/<EMAIL>"
                            alt=""
                            style="width: 16px; height: 16px"
                          >
                          <span style="margin: 0 20px 0 8px">
                            {{ item.departmentName }} - {{ item.userName }}
                            {{ index + 1 === unread.length ? '' : ';' }}</span>
                        </div>
                      </div>
                    </el-tab-pane>
                  </el-tabs>
                </div>
              </el-collapse-item>
            </el-collapse>
          </div>
        </div>
      </section>
    </div>
    <div class="main-right">
      <div class="right-box">
        <div class="right-title">
          表单信息
        </div>
        <div class="content">
          <span class="content-label">发起人：</span>
          <span class="content-text">
            {{ taskDetail.departmentName }}-{{ taskDetail.userName }}</span>
        </div>
        <div class="content">
          <span class="content-label">开始时间：</span>
          <span class="content-text">
            {{ taskDetail.startTime ? taskDetail.startTime
              .substr(0, taskDetail.startTime.length-3) : undefined }}
          </span>
        </div>
        <div class="content">
          <span class="content-label">结束时间：</span>
          <span class="content-text">
            {{ taskDetail.endTime ? taskDetail.endTime
              .substr(0, taskDetail.endTime.length-3) : undefined }}
          </span>

        </div>
      </div>
      <div class="right-box">
        <div class="right-title">
          时间跟踪
        </div>
        <div class="content">
          <span class="content-label">剩余时间：</span>
          <!-- <span class="content-progress">
            <el-progress
              :percentage="50"
              :show-text="false"
            />
          </span>
          <span class="content-text">2天</span> -->
          <span class="content-progress">
            <el-progress
              :percentage="calcPercent"
              :stroke-width="8"
              :color="progressCustomColor"
              :format="formatProgress"
            />
          </span>
        </div>
      </div>
      <div class="right-box">
        <div class="right-title">
          更新日志
        </div>
        <div
          v-for="(item,index) in taskLogListData"
          :key="index"
          class="content between"
        >
          <!-- <span class="content-label">生态环境局-金牛区生态环境局：生成任务</span> -->
          <span class="content-label">{{ item.accountName }}：{{ item.content }}</span>
          <span class="content-text">{{ item.time ?
            item.time
              .substr(0, item.time .length-3) : undefined }}</span>
        </div>
      </div>
    </div>

    <!-- 分割线 -->
    <el-divider v-if="taskId && commentList.length > 0" />
    <!-- 历史告警信息 -->
    <el-dialog
      title="历史告警信息"
      :visible.sync="historicalDataVisible"
      width="850px"
    >
      <el-table
        ref="alarmTable"
        :data="historicalList"
        :header-cell-style="{ background: '#F7FCFB' }"
        stripe
        class="alarm-table"
        border
      >
        <el-table-column
          label="告警内容"
          align="center"
          prop="alarmContent"
        />
        <el-table-column
          label="告警项"
          align="center"
          prop="alarmItemName"
        />
        <el-table-column
          label="告警值"
          align="center"
          prop="alarmValue"
        >
          <template slot-scope="scope">
            {{ scope.row.alarmValue }}{{ scope.row.alarmItemUnit }}</template>
        </el-table-column>
        <el-table-column
          label="告警阈值"
          align="center"
          prop="alarmThreshold"
        >
          <template slot-scope="scope">
            <span
              v-if="
                scope.row.alarmValueType === 0 ||
                  scope.row.alarmValueType === null
              "
            >
              {{ scope.row.alarmThreshold }}{{ scope.row.alarmItemUnit }}</span>
            <span v-if="scope.row.alarmValueType === 1">
              {{ scope.row.alarmMinValue
              }}{{ scope.row.alarmItemUnit }}&lt;,&lt;
              {{ scope.row.alarmMaxValue }}{{ scope.row.alarmItemUnit }}</span>
            <span
              v-if="scope.row.alarmValueType === 2"
            >&lt; {{ scope.row.alarmMinValue
            }}{{ scope.row.alarmItemUnit }},&gt; {{ scope.row.alarmMaxValue
            }}{{ scope.alarmItemUnit.unit }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="告警时间"
          width="250px"
          align="center"
          prop="alarmTime"
        />
      </el-table>
      <el-pagination
        background
        :current-page.sync="pageNumber"
        layout="prev, pager, next, jumper"
        :total="total"
        style="margin-top: 20px"
        @current-change="handleCurrentChange"
      />
    </el-dialog>
    <!-- 部门反馈 -->
    <el-dialog
      :title="'执行反馈'"
      :visible.sync="feedbackVisible"
      width="500px"
      class="feedback"
      :close-on-click-modal="false"
      @close=";(feedbackVisible = false), (isReject = false), (nodeData = {})"
    >
      <div class="feedback-content">
        <div class="feedback-txt">
          <span>{{
            isTaskClose
              ? nodeData.parentId
                ? '退单者'
                : '关闭者'
              : '执行者'
          }}：
          </span>
          <span>{{ nodeData.departmentName }} - {{ nodeData.userName }}</span>
        </div>
      </div>
      <div class="feedback-content">
        <div class="feedback-txt">
          <span>{{
            isTaskClose
              ? nodeData.parentId
                ? '退单时间'
                : '关闭时间'
              : '反馈时间'
          }}：
          </span>
          <span>{{
            nodeData.completeTime ? nodeData.completeTime.substr(0, 16) : '--'
          }}</span>
        </div>
      </div>
      <div class="feedback-content">
        <div class="feedback-txt">
          <span>{{
            isTaskClose
              ? nodeData.parentId
                ? '退单详情'
                : '关闭详情'
              : '执行详情'
          }}：
          </span>
          <span>{{ nodeData.completeRemark || '--' }}</span>
        </div>
      </div>
      <span
        slot="footer"
        class="feedback-style"
      >
        <el-button
          v-if="isReject"
          class="search-button"
          @click="handleReject"
        >驳回</el-button>
        <el-button
          @click="
            ;(feedbackVisible = false), (isReject = false), (nodeData = {})
          "
        >关闭</el-button>
      </span>
    </el-dialog>
    <!-- 完成任务弹框 -->
    <el-dialog
      v-loading="taskResultLoading"
      element-loading-text="任务完成提交中..."
      title="完成任务"
      :visible.sync="taskResultVisible"
      :close-on-click-modal="false"
      width="500px"
      @close="cancleComplete('taskResult')"
    >
      <el-form
        ref="taskResult"
        :model="taskResult"
        :rules="taskResultRules"
        label-width="100px"
      >
        <el-form-item
          label="完成备注"
          prop="completeRemark"
        >
          <el-row>
            <el-col
              :xs="24"
              :lg="22"
            >
              <el-input
                v-model="taskResult.completeRemark"
                type="textarea"
                maxlength="255"
                show-word-limit
                :rows="4"
              />
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item
          v-if="isNodeCompleted"
          label="上传附件"
        >
          <el-row>
            <el-col
              :xs="24"
              :lg="22"
            >
              <el-upload
                ref="upload"
                class="new-task-upload"
                :auto-upload="true"
                :http-request="noop"
                :file-list="uploadFileList"
                :on-preview="handleFilePreview"
                :before-remove="fileBeforeRemove"
                :on-remove="handleFileRemove"
                drag
                multiple
                action="customize"
              >
                <div class="el-upload__text">
                  <em>上传附件</em>
                </div>
                <div
                  slot="tip"
                  class="el-upload__tip"
                >只能上传image文件</div>
              <!--                /docx/doc/xlsx/xls/ppt/pdf/zip-->
              </el-upload>
            </el-col>
          </el-row>
        </el-form-item>
      </el-form>
      <span
        slot="footer"
        class="task-result-style"
      >
        <el-button @click="cancleComplete('taskResult')">取 消</el-button>
        <el-button
          type="primary"
          @click="completeTask('taskResult')"
        >确 定</el-button>
      </span>
    </el-dialog>
    <!-- 关闭任务弹框 -->
    <el-dialog
      v-loading="taskResultCloseLoading"
      :element-loading-text="
        isNodeClosed ? '任务退单提交中...' : '任务关闭提交中...'
      "
      :title="isNodeClosed ? '任务退单' : '任务关闭'"
      :visible.sync="taskResultCloseVisible"
      :close-on-click-modal="false"
      width="500px"
      @close="cancleCloseComplete('taskResultClose')"
    >
      <!-- :rules="taskResultCloseRules" -->
      <el-form
        ref="taskResultClose"
        :model="taskResultClose"
        label-width="100px"
      >
        <el-form-item
          :label="isNodeClosed ? '退单备注' : '关闭备注'"
          prop="completeRemark"
          :rules="[
            {
              required: true,
              message: isNodeClosed
                ? '请填写任务退单备注'
                : '请填写任务关闭备注',
              trigger: 'blur',
            },
            {
              min: 3,
              max: 256,
              message: '长度在 3 到 256 个字符',
              trigger: 'blur',
            },
          ]"
        >
          <el-row>
            <el-col
              :xs="24"
              :lg="22"
            >
              <el-input
                v-model="taskResultClose.completeRemark"
                type="textarea"
                maxlength="255"
                show-word-limit
                :rows="4"
              />
            </el-col>
          </el-row>
        </el-form-item>

      </el-form>
      <span
        slot="footer"
        class="task-result-style"
      >
        <el-button
          @click="cancleCloseComplete('taskResultClose')"
        >取 消</el-button>
        <el-button
          type="primary"
          @click="completeCloseTask('taskResultClose')"
        >确 定</el-button>
      </span>
    </el-dialog>
    <!-- 驳回节点弹框 -->
    <el-dialog
      v-loading="taskResultRejectLoading"
      element-loading-text="任务驳回提交中..."
      title="驳回任务"
      :visible.sync="taskResultRejectVisible"
      :close-on-click-modal="false"
      width="500px"
      @close="cancleRejectComplete('taskResultClose')"
    >
      <el-form
        ref="taskResultReject"
        :model="taskResultReject"
        :rules="taskResultRejectRules"
        label-width="100px"
      >
        <el-form-item
          label="驳回备注"
          prop="completeRemark"
        >
          <el-row>
            <el-col
              :xs="24"
              :lg="22"
            >
              <el-input
                v-model="taskResultReject.completeRemark"
                type="textarea"
                :rows="4"
              />
            </el-col>
          </el-row>
        </el-form-item>

      </el-form>
      <span
        slot="footer"
        class="task-result-style"
      >
        <el-button
          @click="cancleRejectComplete('taskResultReject')"
        >取 消</el-button>
        <el-button
          type="primary"
          @click="completeRejectTask('taskResultReject')"
        >确 定</el-button>
      </span>
    </el-dialog>
    <!-- 任务评论弹框 -->
    <el-dialog
      v-loading="commentLoading"
      title="评论任务"
      :visible.sync="commentVisible"
      width="40%"
      :close-on-click-modal="false"
      @close="replyContent = undefined"
    >
      <Tinymce
        v-if="commentVisible"
        :id="dynamicId"
        :value="''"
        :height="200"
        @input="getRichContent"
        @upload="getUploadFile"
      />
      <span
        slot="footer"
        class="comment-style"
      >
        <el-button @click="commentVisible = false">取 消</el-button>
        <el-button
          type="primary"
          @click="addTaskComment"
        >确 定</el-button>
      </span>
    </el-dialog>
    <!-- 指派任务弹框 -->
    <el-dialog
      v-loading="taskAssignLoading"
      element-loading-text="任务指派提交中..."
      :visible.sync="taskAssignVisible"
      :close-on-click-modal="false"
      width="35%"
      title="指派任务"
      @close="cancleAssignTask('taskAssign')"
    >
      <el-form
        ref="taskAssign"
        :model="taskAssign"
        :rules="taskAssignRules"
        label-width="100px"
      >
        <el-form-item
          :label="'执行者'"
          :rules="{
            required: true,
            type: 'array',
            message: '请选择执行人',
            trigger: 'change',
          }"
          prop="executor"
        >
          <el-row>
            <el-col
              :xs="24"
              :lg="22"
            >
              <el-cascader
                v-model="taskAssign.executor"
                :show-all-levels="false"
                :options="displayDepartmentList"
                :props="person"
                style="width: 100%"
                clearable
                placeholder="执行者"
              />
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item
          label="选择时间"
          prop="dateRange"
        >
          <el-row>
            <el-col
              :xs="24"
              :lg="22"
            >
              <el-date-picker
                v-model="taskAssign.dateRange"
                style="width: 100%"
                type="datetimerange"
                format="yyyy-MM-dd HH:mm"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :picker-options="pickerAssingTimeOptions"
              />
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item
          label="指派内容"
          prop="content"
        >
          <el-row>
            <el-col
              :xs="24"
              :lg="22"
            >
              <el-input
                v-model="taskAssign.content"
                type="textarea"
                :rows="4"
              />
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item label="上传附件">
          <el-row>
            <el-col
              :xs="24"
              :lg="22"
            >
              <el-upload
                ref="upload"
                class="new-task-upload"
                :auto-upload="true"
                :http-request="noop"
                :file-list="uploadFileList"
                :on-success="success"
                :on-preview="handleFilePreview"
                :before-remove="fileBeforeRemove"
                :on-remove="handleFileRemove"
                drag
                multiple
                action="customize"
              >
                <!-- <i class="el-icon-upload" /> -->
                <div class="el-upload__text">
                  <em>上传附件</em>
                </div>
                <div
                  slot="tip"
                  class="el-upload__tip"
                >
                  只能上传image/docx/doc/xlsx/xls/ppt/pdf/zip文件
                </div>
              </el-upload>
            </el-col>
          </el-row>
        </el-form-item>
      </el-form>
      <span
        slot="footer"
        class="task-assign-style"
      >
        <el-button @click="cancleAssignTask('taskAssign')">取 消</el-button>
        <el-button
          type="primary"
          @click="assignTaskFinish('taskAssign')"
        >确 定</el-button>
      </span>
    </el-dialog>
    <!-- 处理流程详情弹框 -->
    <el-dialog
      title="处理流程详情"
      :visible.sync="taskProcessDetailVisible"
      width="45%"
    >
      <div>
        <el-row
          :gutter="10"
          type="flex"
        >
          <el-col :span="4">
            <div class="text-bold infor-normal">指派人：</div>
          </el-col>
          <el-col :span="8">
            <a class="infor-normal">{{ assignTaskDetailData.accountName }}</a>
          </el-col>
        </el-row>
        <el-divider />
        <el-row
          :gutter="10"
          type="flex"
          align="middle"
          class="margin-bot-20"
        >
          <el-col :span="4">
            <div class="text-bold infor-normal">指派时间：</div>
          </el-col>
          <el-col :span="6">
            <a class="infor-normal">{{
              assignTaskDetailData.startTime
                ? assignTaskDetailData.startTime.substr(
                  0,
                  assignTaskDetailData.startTime.length - 3
                )
                : undefined
            }}</a>
          </el-col>
          <el-col :span="2">
            <div class="infor-normal">至</div>
          </el-col>
          <el-col :span="6">
            <a class="infor-normal">{{
              assignTaskDetailData.endTime
                ? assignTaskDetailData.endTime.substr(
                  0,
                  assignTaskDetailData.endTime.length - 3
                )
                : undefined
            }}</a>
          </el-col>
        </el-row>
        <el-collapse v-model="taskProcessDetailActiveNames">
          <el-collapse-item name="1">
            <template slot="title">
              <div class="text-bold text-indent">指派内容</div>
            </template>
            <div class="task-content">
              <div class="task-describe">
                {{ assignTaskDetailData.childContent }}
              </div>
              <div class="task-image">
                <!-- <el-scrollbar style="height: 100%; overflow-x: hidden;" :vertical="true"> -->
                <el-image
                  v-for="(pic, index) in assignTaskDetailData.childpicList"
                  :key="index"
                  :src="pic"
                  fit="cover"
                  lazy
                  @click="openRestImg(index, assignTaskDetailData.childpicList)"
                />
              <!-- </el-scrollbar> -->
              </div>
            </div>
          </el-collapse-item>
          <el-collapse-item name="2">
            <template slot="title">
              <div class="text-bold text-indent">附件</div>
            </template>
            <div
              v-if="
                assignTaskDetailData.childfileList &&
                  assignTaskDetailData.childfileList.length > 0
              "
              class="task-attachment"
              style="display: inline-block"
            >
              <div
                v-for="(file, index) in assignTaskDetailData.childfileList"
                :key="index"
                class="attachment-content"
              >
                <img
                  v-if="file.type === 1"
                  src="@/assets/local_images/word.png"
                >
                <img
                  v-if="file.type === 2"
                  src="@/assets/local_images/excel.png"
                >
                <img
                  v-if="file.type === 3"
                  src="@/assets/local_images/pdf.png"
                >
                <img
                  v-if="file.type === 4"
                  src="@/assets/local_images/ppt.png"
                >
                <img
                  v-if="file.type === 5"
                  src="@/assets/local_images/zip.png"
                >
                <a
                  :href="file.url"
                  target="_blank"
                  :download="file.fileName"
                >{{
                  file.fileName
                }}</a>
              </div>
            </div>
          </el-collapse-item>
          <el-collapse-item name="3">
            <template slot="title">
              <div class="text-bold text-indent">任务内容</div>
            </template>
            <div class="task-content">
              <div class="task-describe">
                {{ assignTaskDetailData.content }}
              </div>
              <div class="task-image">
                <!-- <el-scrollbar style="height: 100%; overflow-x: hidden;" :vertical="true"> -->
                <el-image
                  v-for="(pic, index) in assignTaskDetailData.picList"
                  :key="index"
                  :src="pic"
                  fit="cover"
                  lazy
                  @click="openRestImg(index, assignTaskDetailData.picList)"
                />
              <!-- </el-scrollbar> -->
              </div>
            </div>
          </el-collapse-item>
          <el-collapse-item name="4">
            <template slot="title">
              <div class="text-bold text-indent">附件</div>
            </template>
            <div
              v-if="
                assignTaskDetailData.fileList &&
                  assignTaskDetailData.fileList.length > 0
              "
              class="task-attachment"
              style="display: inline-block"
            >
              <div
                v-for="(file, index) in assignTaskDetailData.fileList"
                :key="index"
                class="attachment-content"
              >
                <img
                  v-if="file.type === 1"
                  src="@/assets/local_images/word.png"
                >
                <img
                  v-if="file.type === 2"
                  src="@/assets/local_images/excel.png"
                >
                <img
                  v-if="file.type === 3"
                  src="@/assets/local_images/pdf.png"
                >
                <img
                  v-if="file.type === 4"
                  src="@/assets/local_images/ppt.png"
                >
                <img
                  v-if="file.type === 5"
                  src="@/assets/local_images/zip.png"
                >
                <a
                  :href="file.url"
                  target="_blank"
                  :download="file.fileName"
                >{{
                  file.fileName
                }}</a>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
      <span
        slot="footer"
        class="task-process-detail-style"
      >
        <el-button @click="taskProcessDetailVisible = false">取 消</el-button>
        <el-button
          type="primary"
          @click="taskProcessDetailVisible = false"
        >确 定</el-button>
      </span>
    </el-dialog>
    <!-- 抄送人评论弹框 -->
    <el-dialog
      title="评论详情"
      :visible.sync="copyToCommentVisible"
      width="45%"
    >
      <div
        v-if="copyToCommentData.length < 1"
        class="noData"
      >暂无数据</div>
      <div
        v-for="(item, index) in copyToCommentData"
        :key="index"
      >
        <el-row
          :gutter="10"
          type="flex"
          align="middle"
        >
          <el-col :span="2">
            <el-avatar
              :src="item.avatar"
              icon="el-icon-user-solid"
            />
          </el-col>
          <el-col :span="4">
            <span v-html="item.accountName" />
          </el-col>
          <el-col :span="6">
            <span v-html="item.commentDate" />
          </el-col>
          <el-col :span="8">
            <span v-html="item.commentContent" />
          </el-col>
        </el-row>
        <el-divider v-if="index < copyToCommentData.length - 1" />
      </div>
      <span
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          type="primary"
          @click="copyToCommentVisible = false"
        >确 定</el-button>
      </span>
    </el-dialog>
    <!-- 附件图片预览 -->
    <el-dialog
      :visible.sync="imagePreviewVisible"
      top="10vh"
      custom-class="img-height"
      :modal="true"
      :modal-append-to-body="false"
    >
      <img
        :src="imagePreviewUrl"
        style="
          max-height: 100%;
          max-width: 100%;
          margin: 0 auto;
          display: block;
        "
        alt
      >
    </el-dialog>
    <!--    指派节点-->
    <el-dialog
      v-loading="taskAssignLoading"
      element-loading-text="任务指派提交中..."
      :close-on-click-modal="false"
      :visible.sync="dialogDrawer"
      :title="number === 1 ? '添加执行者' : '添加抄送者'"
      width="500px"
    >
      <el-form
        ref="addNode"
        :model="addNode"
        :rules="nodeRules"
        class="drawer-content"
        label-width="100px"
        label-position="right"
      >
        <el-form-item
          v-if="number === 1"
          label="执行者:"
          prop="obj"
        >
          <el-cascader
            v-model="addNode.obj"
            collapse-tags
            :options="displayDepartmentList"
            :show-all-levels="true"
            :props="miltPerson"
            style="width: 100%"
            placeholder="执行者"
          />
        </el-form-item>
        <el-form-item
          v-else
          label="抄送者:"
          prop="obj"
        >
          <el-cascader
            v-model="addNode.obj"
            collapse-tags
            :options="displayDepartmentList"
            :show-all-levels="true"
            :props="miltPerson"
            style="width: 100%"
            placeholder="抄送者"
          />
        </el-form-item>
        <el-form-item
          v-if="number === 1"
          label="任务描述:"
          prop="taskNodeContent"
        >
          <el-input
            v-model="addNode.taskNodeContent"
            size="medium"
            style="width: 300px"
            type="textarea"
            rows="5"
            maxlength="500"
          />
        </el-form-item>
        <div class="footer">
          <el-button
            class="search-button"
            @click="handleaddNode"
          >确定</el-button>
          <el-button
            style="margin-left: 50px"
            @click="handleClose"
          >关闭</el-button>
        </div>
      </el-form>
    </el-dialog>
    <openImage
      v-if="imageState"
      :url-list="picList"
      :url-index="urlIndex"
      @closeImage="closeImage"
    />
    <openImage
      v-if="resultImageState"
      :url-list="resultPicList"
      :url-index="resturlIndex"
      @closeImage="closeRestImage"
    />
    <el-dialog
      title="当前位置"
      :visible.sync="mapVisible"
      width="850px"
    >
      <div
        id="map"
        ref="map"
        style="width: 800px; height: 500px"
      />
    </el-dialog>
    <!-- 地图弹框 -->
    <el-dialog
      title="所属位置"
      :close-on-click-modal="false"
      :modal-append-to-body="false"
      :visible.sync="mapVisible1"
      width="1000px"
    >
      <address-map
        v-if="mapVisible1"
        :latlng="latlng"
        height="600px"
      />
      <div slot="footer">
        <el-button
          size="medium"
          plain
          @click="mapVisible1 = false"
        >关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
/* eslint-disable */
import {
  startTask,
  closeTask,
  closeNodeTask,
  completeTask,
  completeNodeTask,
  assignNodeTask,
  getPersonList,
  rejectNodeTask,
  createMicroTask,
  taskDetail,
  commentTask,
  fetchTaskProcessFlow,
  assignTaskDetail,
  taskLogList,
  taskCommentList,
  fetchTaskFinishedDetail,
  getTopTaskExecutorList,
  exportTaskDetail,
  removeTask,
} from '@/api/taskManagement'
import { getHistoricalData } from '@/api/jointEnforcement'
import Tinymce from '@/components/Tinymce'
import openImage from './openImage'
import { mapGetters } from 'vuex'
import { formatDate } from '@/utils/date'
import flowChart from './index'
import { download } from '@/utils/downloadFile'
import downLoadBlob from '@/utils/downLoadBlob'
import addressMap from './addressMap'
import uploadMixin from '@/mixins/upload'
import ComImage from '@/components/image/src/main.vue'
const _ = require('lodash')
// const currentUser = JSON.parse(localStorage.getItem('UserInfor')).accountId
const TASK_TYPE_DICTIONARY = {
  0: '我发起的',
  1: '抄送我的',
  2: '我处理的'
}
// const STATE_COLOR = {
//   '0': '未开始',
//   '1': '进行中',
//   '2': '已完成',
//   '3': '逾期',
//   '4': '已关闭'
// }
const FILE_TYPE_DIC = {
  png: 0,
  jpg: 0,
  jpeg: 0,
  bmp: 0,
  gif: 0,
  docx: 1,
  doc: 1,
  xlsx: 2,
  xls: 2,
  pdf: 3,
  pptx: 4,
  ppt: 4,
  rar: 5,
  zip: 5,
}
export default {
  name: 'TaskMain',
  components: {
    Tinymce,
    flowChart,
    openImage,
    addressMap,
    ComImage
  },
  filters: {
    taskTypeFilter(value) {
      return TASK_TYPE_DICTIONARY[value]
    },
  },
  props: {
    taskId: {
      type: [String, Number],
      require: true,
      default: null,
    },
  },
  mixins: [uploadMixin],
  data() {
    const that = this
    const timeOptionValidator = function (rule, value, callback) {
      if (new Date(value[0]).getTime() < new Date().getTime()) {
        callback(new Error('开始时间应大于当前时间'))
      } else if (new Date(value[1]).getTime() < new Date(value[0]).getTime()) {
        callback(new Error('结束时间应大于开始时间'))
      }
      callback()
    }
    const objValidator = function (rule, value, callback) {
      if (JSON.stringify(value) === '{}') {
        callback(new Error('请选择执行者'))
      }
      callback()
    }
    return {
      pageNumber: 1,
      pageSize: 10,
      total: 0,
      taskNodeIdList: [],
      mapVisible: false,
      mapVisible1: false,
      latlng: {},
      historicalList: [],
      historicalDataVisible: false,
      withStreet: 1,
      urlIndex: 0,
      imageState: false,
      showRichText: false,
      dynamicId: _.uniqueId('richText_'),
      taskMainLoading: false,
      taskDetail: {},
      task: {},
      myTaskNodeList: [],
      replyContent: '',
      replyType: 0,
      collaborator: [],
      sender: {},
      resolver: {},
      parentId: 0,
      replyerId: 0,
      replyerName: 0,
      commentUpload: [],
      picList: [],
      fileList: [],
      uploadFileList: [],
      commentList: [],
      replyerType: undefined,
      taskMainActiveNames: ['1', '2', '3', '4', '5', '6', '7', '8'],
      taskProcessDetailActiveNames: ['1'],
      flowChartData: {},
      updateRecordList: [],
      taskResultCloseLoading: false,
      taskResultCloseVisible: false,
      taskResultReject: {
        completeRemark: undefined,
      },
      taskResultRejectRules: {
        completeRemark: [
          { required: true, message: '请填写任务驳回备注', trigger: 'blur' },
          {
            min: 3,
            max: 256,
            message: '长度在 3 到 256 个字符',
            trigger: 'blur',
          },
        ],
      },
      taskResultClose: {
        completeRemark: undefined,
      },
      taskResultCloseRules: {
        completeRemark: [
          { required: true, message: '请填写任务退单备注', trigger: 'blur' },
          {
            min: 3,
            max: 256,
            message: '长度在 3 到 256 个字符',
            trigger: 'blur',
          },
        ],
      },
      taskResult: {
        completeRemark: undefined,
      },
      taskResultRules: {
        completeRemark: [
          { required: true, message: '请填写任务完成备注', trigger: 'blur' },
          {
            min: 3,
            max: 256,
            message: '长度在 3 到 256 个字符',
            trigger: 'blur',
          },
        ],
      },
      taskResultVisible: false,
      commentVisible: false,
      taskAssignVisible: false,
      taskProcessDetailVisible: false,
      copyToCommentVisible: false,
      taskAssign: {
        executor: [],
        dateRange: [],
        content: undefined,
      },
      person: {
        emitPath: false,
        multiple: true,
        expandTrigger: 'hover',
        checkStrictly: true,
        value: 'needsData',
        children: 'departmentAccountList',
        label: 'departmentName',
        accountId: '',
        depId: '',
      },
      taskAssignRules: {
        content: [
          { required: true, message: '请填写任务内容', trigger: 'blur' },
          {
            min: 3,
            max: 256,
            message: '长度在 3 到 256 个字符',
            trigger: 'blur',
          },
        ],
        dateRange: [
          { required: true, validator: timeOptionValidator, trigger: 'change' },
        ],
        executor: [
          {
            required: true,
            type: 'array',
            message: '请选择执行人',
            trigger: 'change',
          },
        ],
      },
      upLoadContainer: {
        fileList: [],
      },
      displayDepartmentList: [],
      commentLoading: false,
      taskResultLoading: false,
      taskAssignLoading: false,
      isStartedTask: false,
      isTaskMember: false,
      isFinishedtask: false,
      isOverduetask: false,
      isSender: false,
      isResolver: true,
      isCopyTo: false,
      isFinished: false,
      assignTaskDetailData: {},
      taskLogListData: [],
      executiveFeedbackList: [],
      copyToCommentData: [],
      imagePreviewVisible: false,
      imagePreviewUrl: null,
      pickerAssingTimeOptions: {
        disabledDate: (time) =>
          new Date(time).getTime() < new Date().getTime() - 8.64e7,
      },
      taskFinishedDetailList: [],
      resultImageState: false,
      resultPicList: [],
      resturlIndex: 0,
      record: 0,
      record1: 0,
      record2: 0,
      feedbackVisible: false,
      nodeData: {},
      isReject: false,
      activeName: 'first',
      read: [],
      unread: [],
      icon0: require('@/assets/images/toux.png'),
      icon1: require('@/assets/images/tou1.png'),
      icon2: require('@/assets/images/toux_2.png'),
      icon3: require('@/assets/images/tou3.png'),
      isNodeCompleted: false,
      isNodeClosed: false,
      taskNodeId: null,
      currNodeData: {},
      taskResultRejectLoading: false,
      taskResultRejectVisible: false,
      dialogDrawer: false,
      number: 1,
      addNode: {
        taskNodeContent: undefined,
        obj: {},
      },
      nodeRules: {
        obj: [{ validator: objValidator, required: true, trigger: 'change' }],
      },
      miltPerson: {
        emitPath: false,
        multiple: false,
        expandTrigger: 'hover',
        value: 'needsData',
        children: 'departmentAccountList',
        label: 'departmentName',
        accountId: '',
        disabled: 'disabled',
        depId: '',
      },
      isAdd: false,
      alarmListData: [],
      isTaskClose: false,
      progressCustomColor: [
        { color: '#f56c6c', percentage: 100 },
        { color: '#e6a23c', percentage: 80 },
        { color: '#5cb87a', percentage: 60 },
        { color: '#1989fa', percentage: 40 },
        { color: '#6f7ad3', percentage: 20 }
      ],
      // 我的任务节点信息
      myNode: {}
    }
  },
  computed: {
    ...mapGetters([
      'name',
      'accountId',
      'departmentId',
      'departmentName',
      'userId',
      'departmentType',
    ]),
    calcPercent: {
      get() {
        if (
          this.taskDetail &&
          this.taskDetail.startTime &&
          this.taskDetail.endTime
        ) {
          const startTime = new Date(this.taskDetail.startTime).getTime()
          const nowTime = new Date().getTime()
          const endTime = new Date(this.taskDetail.endTime).getTime()
          if (nowTime - startTime < 0) {
            return 0
          }
          const percent = Math.floor(
            ((nowTime - startTime) / (endTime - startTime)) * 100
          )
          return percent >= 100 ? 100 : percent
        }
        return 0
      },
      set() {
        // noop
      }
    },
    /**
     * 是否是我创建的
     */
    isMyAdd() {
      return this.$route.name === 'taskMyAdd'
    },
    /**
     * 任务的属性 0 - 我创建的  1 - 抄送我的  2 - 我执行的
     */
    taskStateForMe(){
      if(this.isMyAdd) return 0
      return Number(this.myNode.permission)
    }
  },
  watch: {
    taskId: {
      handler(newVal, oldVal) {
        this.fetchtaskDetail(newVal)
        // this.fetchTaskProcessFlow(newVal)
        // this.fetchTaskLogList(newVal)
      },
      deep: false,
      immediate: true,
    },
    $route: {
      handler() {
        this.taskId = this.$route.query.taskId
        // this.fetchtaskDetail(this.taskId)
      },
      deep: true, //深度监听，同时也可监听到param参数变化
    },
  },
  mounted() {
    const that = this
    this.getPersonList()
    // this.$bus.on('updateTaskDetail', (arg) => {
    //   that.fetchtaskDetail(arg[0])
    // })
    // this.getHistoricalData(this.taskId)
  },
  beforeDestroy() {
    this.$bus.off('updateTaskDetail')
  },
  methods: {
    Refresh() {
      this.fetchtaskDetail()
    },
    // 历史数据
    handleHistorical() {
      this.historicalDataVisible = true
    },
    handleHistory(task) {
      this.$router.push({
        name:
          task.stationTypeId == 1
            ? 'waterSite'
            : task.stationTypeId == 2
            ? 'airSite'
            : task.stationTypeId == 5
            ? 'constructionPlace'
            : task.stationTypeId == 6
            ? 'print'
            : task.stationTypeId == 7
            ? 'garage'
            : task.stationTypeId == 8
            ? 'restaurant'
            : 'gas',
        query: {
          id: task.stationId,
        },
      })
    },
    handleCurrentChange(page) {
      this.pageNumber = page
      this.getHistoricalData(this.taskDetail.parentId || this.taskId)
    },
    gethistoricalList() {
      this.taskMainLoading = true
      this.getHistoricalData()
    },
    getHistoricalData(data) {
      getHistoricalData(this.pageNumber, this.pageSize, data)
        .then((res) => {
          const datas = res.data.data
          if (datas) {
            this.historicalList = datas.records
            this.total = datas.total
          } else {
            this.historicalList = []
            this.total = 0
          }
        })
        .finally(() => {
          this.taskMainLoading = false
        })
    },
    success(res, file, fileList) {},
    // 节点点击详情
    handleDetail(args) {
      const { data } = args
      this.nodeData = data
      if (
        (!data.parentId &&
          (Number(data.completeStatus) === 1 ||
            Number(data.completeStatus) === 2)) ||
        (Number(data.permission) === 2 &&
          (Number(data.completeStatus) === 1 ||
            Number(data.completeStatus) === 2))
      ) {
        this.feedbackVisible = true
        this.isTaskClose = Number(data.completeStatus) === 2
      }
      if (Number(data.permission) === 2 && Number(data.completeStatus) === 1) {
        const currLinkObj = this.flowChartData.linkDataArray.filter(
          (item) => item.to === data.key
        )[0]
        const currNodeObj = this.flowChartData.nodeDataArray.filter(
          (item) => item.key === currLinkObj.from
        )[0]
        this.isReject = currNodeObj.userId === this.userId
      }
    },
    // 完成节点
    handleNodeComplete(data) {
      if (Number(data.completeStatus) === 0) {
        this.taskNodeId = data.key || data.taskNodeId
        this.isNodeCompleted = data.parentId
        this.taskResultVisible = true
      }
    },
    // 参数
    handleParams(args) {
      this.currNodeData = args.data
    },
    // 关闭节点
    handleNodeClose(args) {
      const { data } = args
      this.taskNodeId = data.key
      this.taskResultCloseVisible = true
      this.isNodeClosed = data.parentId != null
    },
    // 关闭节点
    handleNodeClose1(data) {
      this.taskNodeId = data.taskNodeId
      this.taskResultCloseVisible = true
      this.isNodeClosed = true
    },
    // 驳回
    handleReject() {
      this.taskResultRejectVisible = true
      // this.$confirm('此操作将驳回任务, 是否驳回?', '提示', {
      //   confirmButtonText: '确定',
      //   cancelButtonText: '取消',
      //   type: 'warning',
      //   center: true
      // })
      //   .then(() => {
      //     this.taskResultRejectVisible = true
      //     // this.
      //     // rejectNodeTask(this.nodeData.key, this.userId).then(res => {
      //     //
      //     // })
      //   })
      //   .catch(() => {
      //     this.$message({
      //       type: 'info',
      //       message: '已取消驳回'
      //     })
      //   })
    },
    handleClickMap(num) {
      this.mapVisible = true
      this.$nextTick(() => {
        if(num===1){
          this.map(this.taskDetail.lng,this.taskDetail.lat)
        }else{
          this.map(this.alarmListData[0].stationLng,this.alarmListData[0].stationLat)
        }
      })
    },
    map(lng,lat) {
      const map = new BMap.Map('map', { enableMapClick: false })
      map.centerAndZoom(
        new BMap.Point(
          // Number(this.alarmListData[0].stationLng),
          // Number(this.alarmListData[0].stationLat)
          Number(lng),
          Number(lat)
        ),
        14
      ) // 初始化地图,设置城市和地图级别。
      map.enableScrollWheelZoom()
      map.setMapStyleV2({
        styleId: '62515f0fc623b51ecba40a7c72d766c2',
      })
      const marker = new BMap.Marker(
        new BMap.Point(
          Number(lng),
          Number(lat)
        )
      )
      map.addOverlay(marker)

      // 处理百度地图未放在标准文档流中，会出现放大或缩小后,中心点偏移(中心点不是在放大前的点)
      // 临时存储地图中心点经度和纬度
      let center_lng = 0
      let center_lat = 0
      // 监听地图缩放开始事件 lng表示经度，lat表示纬度
      map.addEventListener('zoomstart', function (e) {
        center_lng = map.getCenter().lng
        center_lat = map.getCenter().lat
      })
      // 监听地图缩放结束事件 lng表示经度，lat表示纬度
      map.addEventListener('zoomend', function (e) {
        map.centerAndZoom(new BMap.Point(center_lng, center_lat), map.getZoom())
      })
    },
    openImg(index, item) {
      this.imageState = true
      this.urlIndex = index
      document.documentElement.style.overflowY = 'hidden'
    },
    // 获取递归后的部门数组
    // getDepartListAll(data) {
    //   const departmentAccountList = data.departmentAccountList
    //   const userAccountList = data.userAccountList
    //   for (const item of departmentAccountList) {
    //     item.needsData = { accountId: item.accountId, departmentId: item.departmentId }
    //   }
    //   for (const item of userAccountList) {
    //     item.departmentName = item.userName
    //     item.needsData = { accountId: item.accountId, departmentId: item.departmentId }
    //   }
    //   // if (userAccountList && userAccountList.length) {
    //   data.departmentAccountList = departmentAccountList.concat(userAccountList)
    //   for (let i = 0; i < departmentAccountList.length; i++) {
    //     this.getDepartListAll(departmentAccountList[i])
    //   }
    //   // }
    //   return data
    // },
    // 获取执行者列表
    getPersonList() {
      getTopTaskExecutorList().then(res => {
          const { data } = res.data
          const data1 = {
            departmentList: data,
            userList: []
          }
          console.log('data1', data)
          this.displayDepartmentList = this.getDepartListAll(JSON.parse(JSON.stringify(data1))).departmentAccountList
        })
      // if (this.departmentType === 1) {
      //   getTopTaskExecutorList().then((res) => {
      //     const { data } = res.data
      //     const data1 = {
      //       departmentList: data,
      //       userList: [],
      //     }
      //     this.displayDepartmentList = this.getDepartListAll(
      //       JSON.parse(JSON.stringify(data1))
      //     ).departmentAccountList
      //   })
      // } else {
      //   getPersonList(this.departmentId, this.withStreet).then((res) => {
      //     this.displayDepartmentList = this.getDepartListAll(
      //       JSON.parse(JSON.stringify(res.data.data))
      //     ).departmentAccountList
      //   })
      // }
    },
    // 获取递归后的部门数组
    getDepartListAll(data) {
      const departmentAccountList = data.departmentList || []
      const userAccountList =
        data.userList === null
          ? []
          : data.userList.map((item) => {
              if (item.isAdmin) {
                item.userName = item.userName + '(管理员)'
              }
              return item
            })
      for (const item of departmentAccountList) {
        item.needsData = { departmentId: item.departmentId }
      }
      for (const item of userAccountList) {
        item.departmentName = item.userName
        item.needsData = {
          userId: item.userId,
          departmentId: item.departmentId,
          userName: item.userName,
        }
        item.disabled = item.userId === this.userId
      }
      data.departmentAccountList = departmentAccountList.concat(userAccountList)
      for (let i = 0; i < departmentAccountList.length; i++) {
        this.getDepartListAll(departmentAccountList[i])
      }
      return data
    },
    // 新增子节点
    handleAddNode(args) {
      this.number = args
      this.dialogDrawer = true
      if (this.$refs.addNode) {
        this.$refs.addNode.resetFields()
      }
      this.addNode = {
        taskNodeContent: undefined,
        obj: {},
      }
      this.isAdd = true
    },
    handleClose() {
      this.$refs.addNode.resetFields()
      this.addNode = {
        taskNodeContent: undefined,
        obj: {},
      }
      this.dialogDrawer = false
    },
    handleaddNode() {
      this.$refs.addNode.validate((valid) => {
        if (valid) {
          const newTime = new Date()
          if (newTime.getTime() - this.record1 > 400) {
            const data = JSON.parse(JSON.stringify(this.addNode))
            data.departmentId = data.obj.departmentId
            data.userId = data.obj.userId
            data.taskNodeType = 2
            data.taskNodeName = data.obj.userName
            data.permission = this.number === 1 ? 2 : 1
            data.completionCondition = 2
            data.operatorId = this.userId
            data.parentId = this.currNodeData.key
            delete data.obj
            this.taskAssignLoading = true
            assignNodeTask(data)
              .then((res) => {
                if (res.data.code === 200) {
                  this.$message.success('新建任务成功')
                } else {
                  this.$message.warning(res.data.msg)
                }
                this.currNodeData = {}
                this.handleClose()
                this.fetchtaskDetail()
              })
              .finally(() => {
                this.taskAssignLoading = false
              })
          }
          this.record1 = new Date().getTime()
          // this.$refs.flowCharts1.addNode(data)
          // this.handleClose()
        }
      })
    },
    closeImage() {
      this.imageState = false
      document.documentElement.style.overflowY = 'scroll'
    },
    openRestImg(index, item) {
      if (item) {
        this.resultPicList = item.map((v) => {
          return v.annexUrl
        })
      }
      this.resultImageState = true
      this.resturlIndex = index
      document.documentElement.style.overflowY = 'hidden'
    },
    closeRestImage() {
      this.resultImageState = false
      document.documentElement.style.overflowY = 'scroll'
    },
    async fetchtaskDetail(taskId = this.taskId) {
      this.taskMainLoading = true
      await taskDetail(taskId, this.userId)
        .then((res) => {
          if (res && res.data.data) {
            this.taskDetail = res.data.data
            if (this.taskDetail.lat) {
              this.latlng = {
                lat: this.taskDetail.lat,
                lng: this.taskDetail.lng,
              }
            }
            this.task = res.data.data
            this.myTaskNodeList = res.data.data.myTaskNodeList
            this.taskNodeIdList = this.taskDetail.taskNodeList.map(
              (item) => {
                const {userId} = item
                if(userId === this.userId) {
                  this.myNode = item
                }
                return item.userId
              }
            )
            this.picList = _.isEmpty(this.taskDetail.taskAnnexList)
              ? []
              : this.taskDetail.taskAnnexList
                  .filter((item) => FILE_TYPE_DIC[item.format] === 0)
                  .map((item) => item.annexUrl)
            this.fileList = _.isEmpty(this.taskDetail.taskAnnexList)
              ? []
              : this.taskDetail.taskAnnexList
                  .filter((item) => FILE_TYPE_DIC[item.format] !== 0) // !==0
                  .map((item) => ({
                    fileName: item.name,
                    type: FILE_TYPE_DIC[item.format],
                    url: item.annexUrl,
                  }))
            this.flowChartData = {
              nodeDataArray: this.taskDetail.taskNodeList.map((item) => {
                item.key = item.taskNodeId
                item.avatar =
                  Number(item.completeStatus) === 0
                    ? this.icon0
                    : Number(item.completeStatus) === 1
                    ? this.icon1
                    : this.icon3
                if (item.permission === 1) {
                  item.avatar = this.icon2
                }
                delete item.taskNodeId
                return item
              }),
              linkDataArray: this.taskDetail.taskNodeLinkList,
            }
            this.taskLogListData = this.taskDetail.taskLogList.map((item) => {
              item.accountName = item.departmentName ? `${item.departmentName}-${item.userName}` : `${item.userName}`
              return item
            })
            this.executiveFeedbackList =
              this.taskDetail.executiveFeedbackList.map((item) => {
                item.accountName = item.departmentName ? `${item.departmentName}-${item.userName}` : `${item.userName}`
                // item.taskAnnexList = item.taskAnnexList.map((item) => item.annexUrl)
                return item
              })
            this.alarmListData = this.taskDetail.taskAlarmList
            this.read = this.taskDetail.read
            this.unread = this.taskDetail.unread
            this.$bus.emit('getTaskLog', this.taskLogListData)
            this.$bus.emit('getTaskDetail', this.task)
          }
        })
        .catch((error) => {
          throw new Error(error)
        })
        .finally(() => {
          this.taskMainLoading = false
          this.getHistoricalData(this.taskDetail.parentId || this.taskId)
        })
      this.isSender = this.task.userId === this.userId
      this.isCopyTo = _.some(this.taskDetail.cc, ['accountId', this.accountId])
      this.isStartedTask = Number(this.taskDetail.taskStatus) > 0
      this.isFinished = Number(this.task.completeStatus) !== 0
    },
    downLoadBlob(file) {
      download(file.url, file.fileName)
    },
    async fetchTaskProcessFlow(taskId = this.taskId) {
      await fetchTaskProcessFlow(taskId).then((res) => {
        if (res && res.data.data) {
          this.taskProcessFlowData = res.data.data
        }
      })
      // type
      // '0': '我发起的',
      // '1': '我处理的',
      // '2': '抄送我的'
      // 给第一级任务添加一个初始key和parent并添加到nodeDataArray
      const nodeDataArray = _.map(
        _.filter(this.taskProcessFlowData, (item) => !item.taskParentId),
        (item, index) => {
          let tempNode = {}
          switch (item.type) {
            case '0': // 任务发起者
              tempNode = {
                departmentId: item.departmentId,
                title: item.departmentName,
                key: `${item.taskId}_${item.type}_1`,
                totalTaskId: item.totalTaskId,
                taskParentId: item.taskParentId,
                type: item.type,
                avatar: item.avatarUrl,
                taskId: item.taskId,
                accountId: item.accountId,
                name: item.accountName,
              }
              break
            case '1': // 任务执行者
              tempNode = {
                departmentId: item.departmentId,
                title: item.departmentName,
                key: item.isLine
                  ? `${item.taskId}_${item.type}_${item.serialNumber}`
                  : `${item.taskId}_${item.type}_${item.accountId}`,
                parent: item.isLine
                  ? item.serialNumber === 1
                    ? `${item.taskId}_0_1`
                    : `${item.taskId}_${item.type}_${item.serialNumber - 1}`
                  : `${item.taskId}_0_1`,
                totalTaskId: item.totalTaskId,
                taskParentId: item.taskParentId,
                type: item.type,
                avatar: item.avatarUrl,
                taskId: item.taskId,
                accountId: item.accountId,
                name: item.accountName,
              }
              break
            case '2': // 任务抄送者
              tempNode = {
                departmentId: item.departmentId,
                title: item.departmentName,
                key: `${item.taskId}_${item.type}_${index + 1}`,
                parent: `${item.taskId}_0_1`,
                totalTaskId: item.totalTaskId,
                taskParentId: item.taskParentId,
                type: item.type,
                avatar: item.avatarUrl,
                taskId: item.taskId,
                accountId: item.accountId,
                name: item.accountName,
              }
              break
          }
          return tempNode
        }
      )

      // 获取所有任务分组
      const groupAllTask = _.groupBy(this.taskProcessFlowData, 'taskId')

      // 获取所有次级任务
      const allAssignTask = _.filter(
        this.taskProcessFlowData,
        (item, index) => item.taskParentId
      )

      // 将所有次级任务根据taskId分组
      const groupAssignTask = _.groupBy(allAssignTask, 'taskId')

      // 给每组指派任务添加对应的parentKey
      _.forEach(groupAssignTask, (group) => {
        let parentKey = null
        // 找到当前任务确切的父任务信息
        const parentTask = groupAllTask[group[0].taskParentId]
        _.forEach(group, (cur) => {
          _.forEach(parentTask, (parent) => {
            if (cur.type === '0' && cur.accountId === parent.accountId) {
              // 从所有执行这种找出该组任务的发起者并生成该组任务的parentKey
              parentKey = parent.isLine
                ? `${parent.taskId}_${parent.type}_${parent.serialNumber}`
                : `${parent.taskId}_${parent.type}_${parent.accountId}`
            }
          })
        })
        // 重新整合数据并生成唯一key
        const filterGruop = _.map(
          _.filter(group, (item) => item.type === '1'),
          (item, index) => ({
            departmentId: item.departmentId,
            title: item.departmentName,
            key: `${item.taskId}_${item.type}_${item.accountId}`, // 指派任务只能为并行，故不判断是否线性
            parent: parentKey,
            totalTaskId: item.totalTaskId,
            taskParentId: item.taskParentId,
            type: item.type,
            avatar: item.avatarUrl,
            taskId: item.taskId,
            accountId: item.accountId,
            name: item.accountName,
          })
        )
        // 将所有次级任务添加进 nodeDataArray
        nodeDataArray.push(...filterGruop)
      })
      this.flowChartData = {
        class: 'go.TreeModel',
        nodeDataArray,
      }
    },
    async fetchTaskFinishedDetail(taskId = this.taskId) {
      await fetchTaskFinishedDetail(taskId).then((res) => {
        if (res && res.data.data) {
          this.taskFinishedDetailList = res.data.data
          this.taskFinishedDetailList.map((item) => {
            const piclist = item.annexUrl
              ? item.annexUrl
                  .split(',')
                  .filter((item) => FILE_TYPE_DIC[item.split('.').pop()] === 0)
              : []
            const fileList = item.annexUrl
              ? item.annexUrl
                  .split(',')
                  .filter((item) => FILE_TYPE_DIC[item.split('.').pop()] !== 0)
                  .map((item) => ({
                    fileName: item.split('/').pop(),
                    type: FILE_TYPE_DIC[item.split('.').pop()],
                    url: item,
                  }))
              : []
            item.piclist = piclist
            item.fileList = fileList
            return { ...item }
          })
        }
      })
    },
    fetchTaskLogList(taskId = this.taskId) {
      taskLogList(taskId).then((res) => {
        if (res && res.data.data) {
          this.taskLogListData = res.data.data
          this.$bus.emit('getTaskLog', this.taskLogListData)
        }
      })
    },
    getRichContent(content) {
      this.replyContent = content // .slice(3,-4)
    },
    getUploadFile(upload) {
      this.commentUpload = upload
    },
    returnToTable() {
      this.$emit('returnToTable')
    },
    startTask() {
      startTask(this.accountId, this.name, this.taskId)
        .then((res) => {
          this.$message({
            message: '开始任务成功',
            type: 'success',
          })
          this.fetchtaskDetail()
        })
        .catch((e) => {
          throw new Error(e)
        })
    },
    openAssignTask(formName) {
      if (Number(this.taskDetail.taskStatus) !== 1) {
        this.$message.warning('当前任务已逾期，不能指派')
        return
      }
      this.taskAssignVisible = true
      // 组件视图更新后再执行
      this.$nextTick(() => {
        this.$refs[formName].clearValidate()
      })
    },
    assignTaskFinish(formName) {
      if(this.isUploading()) return
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.taskAssignLoading = true
          const executorList = this.taskAssign.executor.map((item) => ({
            accountId: item.accountId,
            userName: item.userName,
            departmentId: item.departmentId,
            departmentName: item.departmentName,
            taskType: 1,
          }))
          // 去除重复上传的图片
          const map = new Map()
          const swapList = this.upLoadContainer.fileList.splice(0)
          const sendFileList = swapList.filter(
            (item) => !map.has(item.name) && map.set(item.name, true)
          )
          const newTime = new Date()
          if (newTime.getTime() - this.record1 > 400) {
            createMicroTask(
              this.taskId,
              this.accountId,
              this.name,
              this.departmentName,
              this.departmentId,
              this.taskAssign.content,
              sendFileList,
              formatDate(this.taskAssign.dateRange[0], 'yyyy-MM-dd hh:mm:ss'),
              formatDate(this.taskAssign.dateRange[1], 'yyyy-MM-dd hh:mm:ss'),
              false,
              executorList,
              this.taskDetail.totalTaskId
                ? this.taskDetail.totalTaskId
                : undefined,
              this.taskDetail.taskSource,
              this.taskDetail.pollutantType
            )
              .then((res) => {
                this.taskAssignVisible = false
                this.taskAssignLoading = false
                this.$message.success('指派任务成功')
                this.fetchtaskDetail()
                this.fetchTaskLogList()
              })
              .catch((err) => {
                // this.$message.warning('指派任务失败，请重试')
                throw new Error(err)
              })
              .finally(() => {
                this.taskAssignVisible = false
                this.taskAssignLoading = false
              })
          }
          this.record1 = new Date().getTime()
        } else {
          return false
        }
      })
    },
    cancleAssignTask(formName) {
      this.$refs[formName].resetFields()
      this.uploadFileList = []
      this.taskAssign.executor = []
      this.taskAssignVisible = false
    },
    openReplyTask() {
      this.commentVisible = true
    },
    addTaskComment() {
      if (!this.replyContent) {
        this.$message.warning('任务评论不能为空')
        return
      }
      this.commentLoading = true
      const newTime = new Date()
      if (newTime.getTime() - this.record2 > 400) {
        commentTask(this.taskId, this.accountId, this.replyContent, this.name)
          .then((res) => {
            this.$message({
              type: 'success',
              message: '评论成功',
            })
            this.commentVisible = false
            this.commentLoading = false
          })
          .catch((err) => {
            this.$message.warning('评论失败，请重试')
            throw new Error(err)
          })
          .finally(() => {
            this.commentVisible = false
            this.commentLoading = false
          })
      }
      this.record2 = new Date().getTime()
    },
    // 完成任务
    completeTask(formName) {
      if(this.isUploading()) return
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.taskResultLoading = true
          const map = new Map()
          const swapList = this.upLoadContainer.fileList.splice(0)
          const sendFileList = swapList.filter(
            (item) => !map.has(item.name) && map.set(item.name, true)
          )
          const newTime = new Date()
          if (newTime.getTime() - this.record > 400) {
            const promise = this.isNodeCompleted
              ? completeNodeTask(
                  this.taskNodeId,
                  this.userId,
                  this.taskResult.completeRemark,
                  sendFileList.map((item) => {
                    item.annexUrl = item.data
                    delete item.data
                    return item
                  })
                )
              : completeTask(
                  this.taskId,
                  this.userId,
                  this.taskResult.completeRemark,
                  sendFileList.map((item) => {
                    item.annexUrl = item.data
                    delete item.data
                    return item
                  })
                )
            promise
              .then((res) => {
                this.$message({
                  type: 'success',
                  message: this.isNodeCompleted
                    ? '完成节点任务成功'
                    : '完成任务成功',
                })
                this.taskResultVisible = false
                this.taskResultLoading = false
                this.isNodeCompleted = false
                this.taskNodeId = null
                this.fetchtaskDetail()
                // this.returnToTable()
              })
              .finally(() => {
                this.taskResultLoading = false
              })
          }
          this.record = new Date().getTime()
        } else {
          return false
        }
      })
    },
    // 关闭任务
    completeCloseTask(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.taskResultCloseLoading = true
          const newTime = new Date()
          if (newTime.getTime() - this.record > 400) {
            const promise = this.isNodeClosed
              ? closeNodeTask(
                  this.taskNodeId,
                  this.userId,
                  this.taskResultClose.completeRemark
                )
              : closeTask(
                  this.taskId,
                  this.userId,
                  this.taskResultClose.completeRemark
                )
            promise
              .then((res) => {
                this.$message({
                  type: 'success',
                  message: this.isNodeClosed ? '任务退单成功' : '任务关闭成功',
                })
                this.taskResultCloseVisible = false
                this.taskResultCloseLoading = false
                this.isNodeClosed = false
                this.taskNodeId = null
                this.executiveFeedbackList.push({
                  time: new Date(),
                  accountName: this.userName,
                  remark: this.taskResultClose.completeRemark,
                })
                this.fetchtaskDetail()
                // this.returnToTable()
              })
              .catch((err) => {
                throw new Error(err)
              })
              .finally(() => {
                // this.taskResultCloseVisible = false
                this.taskResultCloseLoading = false
              })
          }
          this.record = new Date().getTime()
        } else {
          return false
        }
      })
    },
    // 驳回任务
    completeRejectTask(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.$confirm('此操作将驳回任务, 是否驳回?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            customClass: 'customConfirm',
            type: 'warning',
            center: true,
          })
            .then(() => {
              this.taskResultRejectLoading = true
              const newTime = new Date()
              if (newTime.getTime() - this.record > 400) {
                rejectNodeTask(
                  this.nodeData.key,
                  this.userId,
                  this.taskResultReject.completeRemark
                )
                  .then((res) => {
                    this.$message({
                      type: 'success',
                      message: '驳回任务成功',
                    })
                    this.taskResultRejectVisible = false
                    this.taskResultRejectLoading = false
                    this.feedbackVisible = false
                    this.fetchtaskDetail()
                    // this.returnToTable()
                  })
                  .catch((err) => {
                    throw new Error(err)
                  })
                  .finally(() => {
                    // this.taskResultCloseVisible = false
                    this.taskResultRejectLoading = false
                  })
              }
              this.record = new Date().getTime()
            })
            .catch(() => {
              this.$message({
                type: 'info',
                message: '已取消驳回',
              })
            })
        } else {
          return false
        }
      })
    },
    cancleComplete(formName) {
      this.$refs[formName].resetFields()
      this.uploadFileList = []
      this.taskResultVisible = false
    },
    cancleCloseComplete(formName) {
      this.$refs[formName].resetFields()
      this.taskResultCloseVisible = false
    },
    cancleRejectComplete(formName) {
      this.$refs[formName].resetFields()
      this.taskResultRejectVisible = false
    },
    closeTask() {
      // if (this.taskDetail.myStatus === '2' || this.taskDetail.taskStatus === '2') {
      //   this.$message.warning('当前任务已完成，不能删除')
      //   return
      // }
      this.$confirm('此操作将退单该任务, 是否退单?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        center: true,
      })
        .then(() => {
          closeTask(this.taskId, this.userId).then((res) => {
            if (res.data) {
              this.$message({
                type: 'success',
                message: '退单成功!',
              })
              this.$emit('fetchtaskList')
              this.returnToTable()
            }
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消退单',
          })
        })
    },
    handleFilePreview(file) {
      const rawFile = file.raw
      const filetype = rawFile.name.split('.').pop()
      if (FILE_TYPE_DIC[filetype] !== 0) return
      const reader = new FileReader()
      const that = this
      reader.readAsDataURL(rawFile)
      reader.onload = function (e) {
        that.imagePreviewUrl = e.target.result
      }
      this.imagePreviewVisible = true
    },
    fileBeforeRemove(file, fileList) {
      return this.$confirm(`确定移除 ${file.name}？`)
    },
    handleFileRemove(file, fileList) {
      const index = this.upLoadContainer.fileList.findIndex(
        (item) => item.name === file.name
      )
      this.upLoadContainer.fileList.splice(index, 1)
    },
    /**
     * 覆盖原始的文件上传事件
     */
    noop(uploadData) {
      this.myUploadHandle({
        uploadData,
        checkFn: (file, filename, filetype) => {
          if (file.size / 1024 / 1024 > 60) {
            // 文件大于60MB
            this.$message.error('上传文件不能大于60M')
            uploadData.onError()
            return false
          }

          if (FILE_TYPE_DIC[filetype] !== 0) {
            this.$message.error('只能上传image文件')
            uploadData.onError()
            return false
          }

          return true
        },
        success: (successData) => {
          this.upLoadContainer.fileList.push(successData)
        }
      })
    },
    flowChartTreeCollapsed({ node }) {
      this.taskProcessDetailVisible = false
    },
    flowChartTreeExpanded({ node }) {
      this.taskProcessDetailVisible = false
    },
    flowChartNodeClick({ node }) {
      if (!_.isEmpty(node.taskParentId)) {
        // 指派详情
        this.fetchassignTaskDetail(node.taskParentId)
      }

      if (node.type === '2') {
        // 抄送人评论
        this.fetchCopyToComment(node.taskId, node.accountId)
      }
    },
    fetchassignTaskDetail(taskId) {
      this.taskProcessDetailVisible = true
      assignTaskDetail(taskId).then((res) => {
        if (res && res.data.data) {
          this.assignTaskDetailData = res.data.data
          this.assignTaskDetailData.picList = _.isEmpty(
            this.assignTaskDetailData.annexUrl
          )
            ? []
            : this.assignTaskDetailData.annexUrl
                .split(',')
                .filter((item) => FILE_TYPE_DIC[item.split('.').pop()] === 0)
          this.assignTaskDetailData.fileList = _.isEmpty(
            this.assignTaskDetailData.annexUrl
          )
            ? []
            : this.assignTaskDetailData.annexUrl
                .split(',')
                .filter((item) => FILE_TYPE_DIC[item.split('.').pop()] !== 0)
                .map((item) => ({
                  fileName: item.split('/').pop(),
                  type: FILE_TYPE_DIC[item.split('.').pop()],
                  url: item,
                }))
          this.assignTaskDetailData.childpicList = _.isEmpty(
            this.assignTaskDetailData.childAnnexUrl
          )
            ? []
            : this.assignTaskDetailData.childAnnexUrl
                .split(',')
                .filter((item) => FILE_TYPE_DIC[item.split('.').pop()] === 0)
          this.assignTaskDetailData.childfileList = _.isEmpty(
            this.assignTaskDetailData.childAnnexUrl
          )
            ? []
            : this.assignTaskDetailData.childAnnexUrl
                .split(',')
                .filter((item) => FILE_TYPE_DIC[item.split('.').pop()] !== 0)
                .map((item) => ({
                  fileName: item.split('/').pop(),
                  type: FILE_TYPE_DIC[item.split('.').pop()],
                  url: item,
                }))
        }
      })
    },
    fetchCopyToComment(taskId, accountId) {
      this.copyToCommentVisible = true
      taskCommentList(taskId, accountId).then((res) => {
        if (res && res.data.data) {
          this.copyToCommentData = res.data.data
        }
      })
    },
    parseTime(time) {
      return formatDate(new Date(time), 'yyyy年MM月')
    },
    /**
     * 导出任务
     */
    exportExcel() {
      exportTaskDetail({ taskId: this.taskDetail.taskId, userId: this.userId })
        .then((res) => {
          downLoadBlob(res, '任务详情.xlsx')
          this.$message.success('导出成功')
        })
        .catch((err) => {
          throw new Error(err)
        })
    },
    /**
     * 删除
     */
    handleDelete() {
      this.$confirm('此操作将删除任务, 是否删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        center: true,
      })
        .then(() => {
          this.removeTask()
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除',
          })
        })
    },
    removeTask() {
      removeTask({ taskId: this.taskDetail.taskId }).then((res) => {
        this.$message.success('删除任务')
        this.returnToTable()
      })
    },
    handleAddMap() {
      this.mapVisible1 = true
    },
    formatProgress(percent) {
      const lastSeconds =
        (new Date(this.taskDetail.endTime).getTime() - new Date().getTime()) /
        1000
      if (lastSeconds > 0) {
        let text = ''
        const minutes = Math.floor(lastSeconds / 60)
        const hours = Math.floor(minutes / 60)
        const days = Math.floor(hours / 24)
        const showHours = hours - (days * 24)
        const showMinutes = minutes - (hours * 60)
        if(days>=1) {
          text += `${days}天`
        }

        if(days>=1 || showHours>=1) {
          text += ` ${showHours}小时`
        }

        if(days>=1 || showHours>=1 || showMinutes>=1) {
          text += ` ${showMinutes}分钟`
        }
        return text
      }
      return '无'
    },
  },
}
</script>

<style lang="less" scoped>
// @import '@/styles/mixin.scss';
// $marginBot: 15px;
.main {
  padding: 20px;
  width: 100%;
  display: flex;
  &-left{
    width: 65%;
    background: #FFFFFF;
    box-shadow: 0px 0px 18px 2px rgba(118, 168, 183, 0.2);
    border-radius: 8px;
    margin-right: 20px;
    padding: 30px;
    .left-top{
      display: flex;
      justify-content: space-between;
      border-bottom: 1px solid #F5F5F5;
      padding-bottom: 20px;
      .title{
        font-size: 22px;
        font-family: PingFang SC;
        font-weight: bold;
        color: #050505;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        .biaodanIcon{
          width: 20px;
          height: 20px;
          margin-right: 5px;
        }
      }
      .btn-back{
        width: 68px;
        height: 32px;
        background: rgba(144, 144, 144, 0.1);
        border-radius: 4px;
        font-size: 14px;
        font-family: PingFang;
        font-weight: 500;
        color: #909090;
        text-align: center;
        line-height: 32px;
      }
      .title-my{
        font-size: 20px;
        font-family: PingFang;
        font-weight: bold;
        color: #13CE66;
        margin-bottom: 20px;
      }
      .btn-refresh{
        width: 88px;
        height: 32px;
        border: 1px solid rgba(153, 153, 153, 0.2);
        border-radius: 4px;
        font-size: 14px;
        font-family: PingFang SC;
        font-weight: 400;
        color: #4D4D4D;
        display: flex;
        align-items: center;
        justify-content: center;
        .refreshIcon{
          width: 14px;
          height: 14px;
          margin-right: 5px;
        }
      }
    }
  }
  &-right{
    width: 35%;
    background: #FFFFFF;
    box-shadow: 0px 0px 18px 2px rgba(118, 168, 183, 0.2);
    border-radius: 8px;
    padding: 10px 30px 30px;
    .right-box{
      border-bottom:1px solid #F2F5FA;
      margin-top: 20px;
      padding-bottom: 14px;
      .right-title{
        font-size: 17px;
        font-family: PingFang;
        font-weight: bold;
        color: #202020;
        margin-bottom: 8px;
      }

      .between{
        display: flex;
        justify-content: space-between;
      }
    }
  }
  .content{
    font-size: 16px;
    font-family: PingFang;
    font-weight: 500;
    line-height: 36px;
    &-label{
      color: #505050;
    }
    &-text{
      color: #999999;
    }
    &-progress{
      display: inline-block;
      width: 400px;

      ::v-deep .el-progress-bar  {
        width: 70%;
      }
    }
    // &-img{
    //   display: inline-block;
    //   width: 80px;
    //   height: 80px;
    //   border-radius: 10px;
    //   margin-right: 20px;
    //   .el-image{
    //     .img{
    //     width: 100%;
    //     height: 100%;
    //     vertical-align: middle;
    //   }
    //   }
    // }
  }
  .parting-line{
    height: 1px;
    border: 1px solid #F5F5F5;
    background: linear-gradient(0deg, #000000 0%, #FFFFFF 100%);
    margin-bottom: 20px;
  }
}
::v-deep .el-collapse-item__header{
  height: 34px;
  background: #F4F7F9;
  border-radius: 2px;
  border: 0;
  border-bottom:0;
  // text-indent: 16px;
  font-size: 16px;
  font-family: PingFang SC;
  font-weight: 500;
  color: #212121;
  margin-bottom: 20px;
  padding-left: 16px;
}
::v-deep .el-collapse-item__content{
  padding-left: 16px;
}
// 已读回执
::v-deep .collapse-item-red .el-collapse-item__content{
  padding-left: 0px;
}
.card-title{
  display: flex;
  justify-content: space-between;
  padding-bottom: 14px;
  border-bottom:  1px solid #F2F5FA;
  >div:nth-child(1){
    font-size: 16px;
    font-family: PingFang;
    font-weight: 500;
    color: #1A1A1A;
    display: flex;
    align-items: center;
    .el-avatar{
    margin-right: 12px;
    }
    .el-avatar--icon {
    font-size: 15px;
    }
  }
  >div:nth-child(2){
  font-size: 14px;
  font-family: PingFang;
  font-weight: 400;
  color: #808080;
  }
}
.pText{
  font-size: 16px;
  font-family: PingFang;
  font-weight: 400;
  color: #666666;
}
.img-box{
  // display: flex;
  .el-image{
   width: 80px;
   height: 66px;
   border-radius: 6px;
   margin-right: 16px;
  img{
    width: 100%;
    height: 100%;
  }
  }


}
::v-deep .el-collapse-item__wrap{
   border-bottom:0;
}

.task-attachment {
  margin-bottom: 15px;
  display: flex;
  flex-direction: row;
  display: inline-block;
  .attachment-content {
    display: flex;
    align-items: center;
    img {
      width: 20px;
      height: 20px;
      display: inline-block;
      margin-right: 10px;
    }
    a {
      @include textEllipsis;
      display: inline-block;
      width: 100%;
      font-size: 14px;
    }
  }
}
</style>
