
// swiper 额外组件配置
import SwiperCore from 'swiper'
import Pagination from 'swiper'
import Mousewheel from 'swiper'
import Navigation from 'swiper'
import Autoplay from 'swiper'
import Swiper from 'swiper'
import SwiperSlide from 'swiper'

// swiper 单独样式 （less / scss）
import 'swiper/swiper.less'
import 'swiper/components/pagination/pagination.less'
import 'swiper/components/navigation/navigation.less'

// swiper 必备组件
// import { Swiper, SwiperSlide } from 'swiper'

// 使用额外组件
SwiperCore.use([Pagination, Mousewheel, Navigation, Autoplay])

// 全局注册 swiper 必备组件
const plugins = [Swiper, SwiperSlide]

const swiper = {
  install: function (app) {
    plugins.forEach(item => {
      app.component(item.name, item)
    })
  }
}

export default swiper
