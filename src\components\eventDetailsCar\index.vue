<template>
  <div
    class="eventDetailsCar"
    :style="!eventTime ? { padding: '30px' } : ''"
  >
    <div class="title">{{ title }}</div>
    <div class="address">{{ content }}</div>
    <div class="infor-item">
      <img src="@/assets/images/<EMAIL>" alt="" />
      <span>事件类型：</span>
      <span>{{ eventType }}</span>
    </div>
    <div class="infor-address">
      <img src="@/assets/images/<EMAIL>" alt="" />
      <span>事件地址：</span>
      <span class="pro_address">{{ address }}</span>
    </div>
    <div class="item-time">
      <div class="infor-item">
        <img src="@/assets/images/<EMAIL>" alt="" />
        <span>事件等级：</span>
        <span>{{ eventLevel }}</span>
      </div>
      <div class="infor-time">{{ eventTime }}</div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'eventDetailsItem',
  props: {
    title: {
      type: String,
      default:'——'
    },
    address: {
      type: String,
      default: '——'
    },
    eventType: {
      type: String,
      default: '——'
    },
    eventLevel: {
      type: String,
      default: '——'
    },
    eventTime: {
      type: String,
      default: '——'
    },
    content: {
      type: String,
      default: '——'
    },
  },
}
</script>

<style lang="less" scoped>
.eventDetailsCar {
  padding: 15px 30px;
  // background-image: url('../../assets/images/<EMAIL>');
  // background-size: 100% 100%;
  background-image: linear-gradient(
    to right,
    rgba(6, 24, 51, 0.8),
    rgba(6, 24, 51, 0)
  );
  border-top: 0.5px solid rgba(39, 73, 93, 0.5);
  border-left: 0.5px solid rgba(39, 73, 93, 0.5);
  box-shadow: 2px 0px 7px 2px #060d1d;
  .title {
    font-size: 18px;
    color: #dbf5ff;
    line-height: 24px;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
  }
  .address {
    margin: 15px 0;
    font-size: 14px;
    color: #49769f;
    min-height: 21px;
    max-height: 65px;
    overflow-y: auto;
    &::-webkit-scrollbar-track {
      border-radius: 10px;
      background: rgba(57, 177, 255, 0);
    }
    &::-webkit-scrollbar-thumb {
      border-radius: 10px;
      width: 1px;
      background: rgba(21, 62, 105, 0.5);
    }
    // display:-webkit-box; //必须结合的属性 ，将对象作为弹性伸缩盒子模型显示
    // -webkit-line-clamp:3; //用来限制在一个块元素显示的文本的行数超过两行省略。
    // overflow:hidden;
    // -webkit-box-orient:vertical; // 必须结合的属性 ，设置或检索伸缩盒对象的子元素的排列方式 。
    // text-overflow: ellipsis;
  }
  .infor-address {
    display: flex;
    align-items: center;
    color: #bbdefd;
    .pro_address {
      width: 285px;
    }
    & > img {
      width: 18px;
      height: 16px;
      margin-right: 6px;
    }
  }
  .infor-item {
    display: flex;
    align-items: center;
    color: #bbdefd;
    & > img {
      width: 18px;
      height: 16px;
      margin-right: 6px;
    }
  }
  .item-time {
    display: flex;
    justify-content: space-between;
    & > div {
      flex: 1;
    }
    & > .infor-time {
      font-size: 13px;
      color: #49769f;
      text-align: right;
    }
  }
}
</style>
