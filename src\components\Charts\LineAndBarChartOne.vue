<template>
  <div :id="id" :style="{ height: height, width: width }" />
</template>

<script lang="ts">
import * as echarts from 'echarts'
import { Component, Prop, Watch } from "vue-property-decorator";
import { mixins } from "vue-class-component";
import ResizeMixin from "./mixins/resize";
@Component({
  name: "LineAndBarChartOne"
})
export default class extends mixins(ResizeMixin) {
  // @Prop({ default: "chart" }) private className!: string;
  @Prop({ default: "chart" }) private id!: string;
  @Prop({ default: "200px" }) private width!: string;
  @Prop({ default: "200px" }) private height!: string;
  @Prop({ required: true }) private propData!: any;
  @Watch("propData", { immediate: true, deep: true })
  private onChartDataChange(newValue: any, oldValue: any): void {
    if (this.propData && newValue) {
      if (this.chart) {
        this.chart.clear();
      }
      this.$nextTick(() => {
        this.initChart();
      });
    }
  }
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
  }

  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.clear();
    this.chart.dispose();
    this.chart = null;
  }

  private initChart() {

    if(this.chart === null || this.chart === undefined) {
      this.chart = echarts.init(
        document.getElementById(this.id) as HTMLDivElement
      );
    }
    this.chart.setOption({
      backgroundColor: "transparent",
      grid: {
        // height: 130,
        top: 60,
        bottom: 0,
        right: 26,
        // left: "left",
        left: 24,
        containLabel: true
      },
      legend: [
        {
          show: true,
          data: [this.propData.monthName, this.propData.ringRatioName],
          right: 0,
          top: -10,
          textStyle: {
            color: "#fff"
          }
        }
      ],
      xAxis: {
        // name: "月",
        // nameTextStyle: {
        //   color: "#fff",
        //   lineHeight: -30,
        //   align: "center",
        //   verticalAlign: "bottom"
        // },
        type: "category",
        data: this.propData.bottomList,
        // offset:10,
        axisLabel: {
          rotate: 40,
          interval: 0,
          textStyle: {
            fontSize: 11,
            color: "white"
          }
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        }
      },
      yAxis: [
        {
          name: this.propData.unit,
          nameTextStyle: {
            color: "white",
            align: "left"
          },
          type: "value",
          axisLabel: {
            // align: "left",
            textStyle: {
              fontSize: 12,
              color: "white"
            }
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          splitLine: {
            lineStyle: {
              type: "dashed"
            }
          }
        },
        {
          name: this.propData.unit1,
          nameTextStyle: {
            color: "white"
          },
          type: "value",
          axisLabel: {
            // align: "left",
            textStyle: {
              fontSize: 12,
              color: "white"
            }
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          splitLine: {
            show: false,
            lineStyle: {
              type: "dashed"
            }
          }
        }
      ],
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "cross",
          label: {
            backgroundColor: "#6a7985"
          }
        }
      },
      color: [
        "#22d3ff",
        "#428AFF",
        "#1D9DFF",
        "#9FE6B8",
        "#FB7293",
        "#8378EA",
        "#E7BCF3",
        "#FF9F7F",
        "#32C5E9",
        "#FFDB5C"
      ],
      series: [
        {
          name: this.propData.monthName,
          type: "bar",
          barWidth: this.propData.monthList.length <= 5 ? "20" : "30%",
          data: this.propData.monthList
        },
        {
          name: this.propData.ringRatioName,
          type: "line",
          yAxisIndex: 1,
          data: this.propData.ringRatioList,
          lineStyle: {
            color: "#FFBF35" //改变折线颜色
          },
          symbol: "circle",
          symbolSize: 6,
          itemStyle: {
            color: "#FFBF35", //改变折线点的颜色
            borderColor: "#FFBF35"
            // borderWidth: 2
          }
        }
      ]
    } as EChartOption<EChartOption.SeriesLine>);
  }
}
</script>
