<style lang="less" scoped>
.pollution-video {
  padding: 0.2rem 0.2rem 0.2rem;
  position: relative;
  .big_window {
    z-index: 10000000;
    width: 1700px;
    height: 980px;
    margin: 0 auto;
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    background: url(../../../assets/<EMAIL>) !important;
    background-size: 100%;
    background-repeat: no-repeat;
    padding: 60px 85px;
    > img {
      position: absolute;
      width: 60px;
      height: 60px;
      right: 35px;
      top: 12px;
      z-index: 99999999999;
    }
    .bottom_control {
      position: absolute;
      right: 100px;
      bottom: 55px;
      z-index: 99999999999;
      width: 276px;
      height: 410px;
      background: url(../../../assets/<EMAIL>) !important;
      background-size: 100%;
      background-repeat: no-repeat;
    }
    .control_title {
      width: 276px;
      height: 31px;
      background: url(../../../assets/<EMAIL>) !important;
      background-size: 100%;
      background-repeat: no-repeat;
    }
    .control_cercle {
      width: 180px;
      height: 180px;
      background: url(../../../assets/<EMAIL>) !important;
      background-size: 100%;
      background-repeat: no-repeat;
      margin: 0 auto;
      margin-top: 5px;
      padding: 17px;
      position: relative;
      .btn_cercle {
        width: 30px;
        height: 27px;
        position: absolute;
        left: 75px;
        transform-origin: center 73px;
        border-radius: 7px;
        &:active {
          box-shadow: 0 0 6px 6px #2c94bdce inset;
        }
      }
    }

    .btn_list {
      margin: 13px auto 12px;
      width: 152px;
      height: 28px;
      background: rgba(1, 3, 27, 0.33);
      border: 1px solid #1996b9;
      border-radius: 4px;
      display: flex;
      > div:nth-child(1) {
        width: 28px;
        border-right: 1px solid #1996b9;
        text-align: center;
        line-height: 28px;
        color: #28bbd1;
      }
      > div:nth-child(2) {
        text-align: center;
        font-size: 14px;
        font-family: PingFang SC;
        font-weight: 400;
        color: #ffffff;
        line-height: 28px;
        flex: 1;
      }
      > div:nth-child(3) {
        width: 28px;
        border-left: 1px solid #1996b9;
        text-align: center;
        line-height: 28px;
        color: #28bbd1;
      }
      > div:nth-child(1):active,
      > div:nth-child(3):active {
        background-color: #28bbd13d !important;
      }
    }

    .btn_jidu {
      display: flex;
      padding: 0 24px;
      font-size: 14px;
      font-family: PingFang SC;
      font-weight: 400;
      color: #ffffff;
      margin-top: 25px;
      justify-content: space-between;
      .jiedian {
        width: 150px;
        height: 6px;
        margin-top: 8px;
        display: flex;
        justify-content: space-between;
        > div {
          width: 31%;
          border-radius: 2px;
          background-color: #cdcdce;
        }
      }
    }
  }
  .pollution-video-conetnt {
    box-sizing: border-box;
    padding: 0.5rem 1rem;
    height: 100%;
    background-image: url(../../../assets/water-video.png) !important;
    background-size: 100% 100% !important;
    .video-select {
      display: flex;
      position: relative;
      .back {
        width: 130px;
        height: 0.64rem;
        line-height: 0.64rem;
        font-size: 0.2rem;
        text-align: center;
        // border: solid 1px #0A48A5;
        background-image: url("../../../assets/video/<EMAIL>");
        background-size: 100% 100%;
        margin-right: 0.08rem;
        cursor: pointer;
        color: #32cdf9;
      }
      .fullCr {
        position: absolute;
        right: 0px;
        top: 15px;
        background-image: url("../../../assets/video/<EMAIL>");
        background-size: 100% 100%;
        width: 1.1rem;
        height: 0.46rem;
        text-align: center;
        line-height: 0.44rem;
        color: #32cdf9;
        padding-left: 0.2rem;
        font-size: 16px;
        cursor: pointer;
      }
    }
    .video-list {
      margin-top: 0.2rem;
      height: calc(100% - 1.1rem);
      display: flex;
      flex-wrap: wrap;
      overflow-y: scroll;
      align-content: flex-start;
      ::-webkit-scrollbar {
        width: 0 !important;
      }
      .video-list-content {
        box-sizing: border-box;
        position: relative;
        width: 25%;
        border: 1px solid #2a76d0;
        margin-top: 0;
        border-radius: 5px;
        overflow: hidden;
        // margin: 0 0.08rem;
        // margin-bottom: 0.3rem;
        > :nth-of-type(2) {
          // margin-top: 0.15rem;
          p {
            margin-bottom: 0;
            font-size: 0.18rem;
          }
        }
        .video_name_style {
          position: absolute;
          left: 0px;
          top: 0px;
          z-index: 9;
          width: 100%;
          height: 0.46rem;
          line-height: 0.46rem;
          font-size: 0.18rem;
          color: #cedbee;
          padding-left: 0.1rem;
          background: linear-gradient(
            180deg,
            rgba(4, 13, 31, 0.8) 0%,
            rgba(4, 13, 31, 0) 100%
          );
        }
        .video_style {
          position: absolute;
          right: 0;
          top: 0px;
          z-index: 222;
          width: 1.2rem;
          height: 0.36rem;
          font-size: 0.18rem;
          color: #cedbee;
          cursor: pointer;
          background-color: rgba(0, 0, 0, 0.5);
          text-align: center;
          border-left: 1px solid #226da2;
          border-bottom: 1px solid #226da2;
          line-height: 0.36rem;
        }
        .btn_class {
          right: 0;
          top: 0px;
        }
        // .video_style:hover {
        //   color: #759ce4;
        // }
      }
    }
  }
  .bottom-image {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 0.3rem;
    margin-top: 0.1rem;
    img {
      cursor: pointer;
      width: 0.27rem;
      height: 100%;
    }
  }
  .false-camera {
    width: 100%;
    height: 100%;
    background-repeat: no-repeat;
    display: flex;
    justify-content: center;
    background-size: 100% 100%;
    align-items: center;
    .play {
      width: 0.8rem;
      height: 0.44rem;
      background: #2b333f;
      cursor: pointer;
      border-radius: 0.3em;
      border: 0.06666em solid #fff;
      display: flex;
      justify-content: center;
      align-content: center;
      > span {
        display: block;
        line-height: 0.45rem;
      }
      .el-icon-caret-right {
        position: relative;
        top: 3px;
        left: 2px;
        font-size: 0.36rem;
        color: white;
      }
      .el-icon-caret-right:hover {
        color: white;
      }
    }
    .play:hover {
      background-color: rgba(43, 51, 63, 0.7);
    }
  }
  ::-webkit-scrollbar {
    width: 0 !important;
  }
}
.position_Ptz {
  position: absolute;
  right: 0;
  top: 0;
  background-color: rgba(66, 122, 224, 0.541);
  width: 66px;
  text-align: center;
  line-height: 36px;
  color: #ffffff;
  z-index: 99999;
}
.bgc_controls {
  background: #1bacc2 !important;
}
</style>

<style lang="less">
.video-select {
  .hc-title-video-select {
    display: flex;
    justify-content: space-between;
    margin-top: -0.01rem;
    width: 200px !important;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    // margin-right: 0.1rem;
    .ant-select-selection {
      width: 200px !important;
      height: 0.6rem;
      padding-top: 0.2rem;
      // background: rgba(14, 139, 255, 0.32);
      border: none;
      border-radius: unset;
      font-size: 0.2rem;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      background: url(../../../assets/video/<EMAIL>) !important;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      display: flex;
      justify-content: center;
    }
    .ant-select-selection-selected-value {
      // color: rgba(0, 234, 255, 1);
      color: #a6bfdc;
      width: 200px !important;
      padding: 0 0.2rem !important;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .ant-select-focused .ant-select-selection,
    .ant-select-selection:focus,
    .ant-select-selection:active {
      border-color: #40a9ff;
      border-right-width: 0 !important;
      outline: 0;
      box-shadow: none;
    }
  }
  .right {
    position: relative;
    display: flex;
    align-items: center;
    width: 4.06rem;
    height: 0.64rem;
    background-image: url("../../../assets/video/<EMAIL>");
    background-size: 100% 100%;
    /*background: rgba(5, 7, 95, 0.7);*/
    /*border: 1px solid #034fa8;*/
    margin-left: 0.08rem;
    input {
      width: 100%;
      margin-left: 0.15rem;
      // margin-right: 0.25rem;
      background: transparent;
      border: none;
      outline: none;
      font-size: 0.2rem;
      padding-top: 0.05rem;
      color: #a6bfdc;
    }
    input::-webkit-input-placeholder {
      color: #a6bfdc;
    }
    input::-moz-placeholder {
      /* Mozilla Firefox 19+ */
      color: #a6bfdc;
    }
    input:-moz-placeholder {
      /* Mozilla Firefox 4 to 18 */
      color: #a6bfdc;
    }
    input:-ms-input-placeholder {
      /* Internet Explorer 10-11 */
      color: #a6bfdc;
    }
  }
  .icon {
    position: absolute;
    right: 0px;
    width: 40px;
    height: 40px;
    z-index: 5;
    padding: 10px 0 0 8px;
    img {
      cursor: pointer;
      height: 23px;
      width: 23px;
    }
  }
}

.video-list-content {
  .video-list-content {
    .vjs-custom-skin {
      height: 100% !important;
    }
  }
  .player-wrapper,
  .vjs-tech,
  .video-wrapper {
    height: 100% !important;
  }
  .video-js.vjs-fluid {
    height: 100% !important;
  }
  .vjs-poster {
    background-size: 100% 100%;
  }
}

.center_btn {
  position: relative;
  .vjs-big-play-button {
    position: absolute !important;
    margin: 0 auto !important;
    left: 0 !important;
    top: 45% !important;
    right: 0 !important;
    // bottom: 0 !important;
  }
}
</style>

<template>
  <section class="pollution-video">
    <section class="pollution-video-conetnt">
      <section class="video-select">
        <div class="back" @click="back">
          <img src="@/assets/video/<EMAIL>" alt="" />
          返回
        </div>
        <a-select
          v-model="streetCode"
          class="hc-title-video-select"
          @change="ChangeStreet"
        >
          <a-icon
            slot="suffixIcon"
            type="caret-down"
            style="color: rgba(0, 234, 255, 1); width: 14px; height: 8px"
          />
          <a-select-option
            :value="item.streetCode"
            v-for="(item, index) in CameraData"
            :key="index"
            >{{ item.streetName }}</a-select-option
          >
        </a-select>
        <a-select
          v-model="stationName"
          class="hc-title-video-select"
          style="margin-left: 15px"
          @change="ChangeStation"
        >
          <a-icon
            slot="suffixIcon"
            type="caret-down"
            style="color: rgba(0, 234, 255, 1); width: 14px; height: 8px"
          />
          <a-select-option
            :value="item.stationName"
            v-for="(item, index) in stationList"
            :key="index"
            >{{ item.stationName }}</a-select-option
          >
        </a-select>
        <div class="right">
          <input
            style="width: 80%"
            type="text"
            placeholder="请输入监控名称"
            v-model="keywords"
            @keyup.enter="search"
            @input="search"
          />
          <div class="icon" @click="search">
            <img src="@/assets/video/<EMAIL>" alt="" />
          </div>
        </div>
        <div @click="fullscreenSwich" class="fullCr">全屏</div>
      </section>
      <section v-if="cameraList.length" class="video-list" id="pollution_video">
        <section
          v-for="(item, index) in cameraList"
          :key="index"
          class="video-list-content"
          :style="{ height: '33.33%' }"
          @mouseenter="repalyIndex = index"
          @mouseleave="repalyIndex = null"
        >
          <div
            class="video_style"
            @click="CheckVideo(item, index)"
            v-show="false"
          >
            回放录像
          </div>
          <div class="video_name_style">
            {{ item.name }}
          </div>
          <!-- <div
            v-if="!item.videoUrl"
            class="false-camera"
            :style="{ backgroundImage: 'url(' + item.snapUrl + ')' }"
            style="position: relative"
          >
            <span
              v-if="!item.online"
              style="
                position: absolute;
                bottom: 0.03rem;
                left: 0.05rem;
                font-size: 0.1rem;
                color: red;
              "
              >离线</span
            >
            <div class="play" @click="play(index)">
              <a-icon
                v-if="!item.loading"
                class="el-icon-caret-right"
                type="caret-right"
              />
              <span v-else style="color: #fff">加载中...</span>
            </div>
          </div> -->
          <div style="position: relative; height: 100%; width: 100%">
            <LivePlayer
              :ref="'video' + index"
              :video-url="item.liveAddress"
              :poster="defaultPoster"
              fluent
              autoplay
              live
              :stretch="true"
              :hasaudio="false"
            />
            <div
              v-if="item.isPtz"
              @click="showWindonw(item.liveAddress, item)"
              class="position_Ptz"
            >
              云台控制
            </div>
            <!-- <player
              v-if="item.videoUrl.length > 0"
              :video-url="item.videoUrl"
              :has-audio="false"
              height="200px"
              style="width: 320px"
              :isFullResize="true"
              :autoplay="true"
              :index="index"
              live
              muted
            /> -->
          </div>
        </section>
      </section>
      <section v-else class="video-list">
        <div
          style="
            text-align: center;
            line-height: 6.6rem;
            width: 100%;
            font-size: 22px;
          "
        >
          暂无数据
        </div>
      </section>
      <!-- <section class="bottom-image">
        <img
          :src="index === pageNum - 1 ? src1 : src2"
          alt=""
          v-for="(item, index) in pages"
          :key="index"
          @click="changePage(index)"
        />
      </section> -->
    </section>
    <!--弹框大屏 -->

    <div class="big_window" v-if="show">
      <img src="@/assets/<EMAIL>" alt="" @click="closeWindow" />
      <LivePlayer
        class="center_btn"
        style="width: 100%; height: 100%"
        :video-url="liveAddress"
        fluent
        autoplay
        live
        :stretch="true"
        :hasaudio="false"
        :hide-fullscreen-button="true"
      />
      <div class="bottom_control">
        <div class="control_title"></div>
        <!-- 1 -->
        <div class="control_cercle">
          <div
            class="btn_cercle"
            v-for="(item, index) in btnList"
            :style="`transform:rotateZ(${45 * index + 'deg'})`"
            :key="index"
            @mousedown="DchangePtz(item)"
            @mouseup="UchangePtz(item)"
          ></div>
        </div>
        <!-- 2 -->
        <div class="btn_list" v-for="(item, index) in btnList1" :key="index">
          <div
            @mousedown="DchangePtzT(item, false)"
            @mouseup="UchangePtzT(item, false)"
          >
            <i class="el-icon-caret-left"></i>
          </div>
          <div>{{ item }}</div>
          <div
            @mousedown="DchangePtzT(item, true)"
            @mouseup="UchangePtzT(item, true)"
          >
            <i class="el-icon-caret-right"></i>
          </div>
        </div>

        <!-- 3 -->
        <div class="btn_jidu">
          <div>云台速度</div>
          <div class="jiedian">
            <div
              v-for="(item, index) in 3"
              :key="item"
              :class="{ bgc_controls: speed >= index }"
              @click="changeSpeed(index)"
            ></div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script lang="ts">
/* eslint-disable @typescript-eslint/ban-ts-ignore */
// import store from '@/store';
import { Component, Vue, Watch } from "vue-property-decorator";
import { Table, Radio, Select, Icon, message } from "ant-design-vue";
import "video.js/dist/video-js.css";
import "videojs-contrib-hls";
import "videojs-flash";
import { ptzStop, ptzC } from "@/api/air";
/* eslint-disable @typescript-eslint/ban-ts-ignore */
//@ts-ignore
import LivePlayer from "@liveqing/liveplayer";
import player from "@/components/jessibucaPlayer/jessibuca.vue";
import { log } from "console";
import defaultPoster from "@/assets/defaultPoster.png";
@Component({
  name: "waterVideo",
  components: {
    ATable: Table,
    ARadioGroup: Radio.Group,
    ARadioButton: Radio.Button,
    ASelect: Select,
    AIcon: Icon,
    ASelectOption: Select.Option,
    LivePlayer,
    player,
  },
})
export default class extends Vue {
  keywords = "";
  defaultValue: any = "连心桥站";
  fullScreen = false;
  show = false;
  isPtz = false;
  repalyIndex: any = null;
  streetCode: any = "";
  CameraData: any = [];
  stationList: any = [];
  stationName: string = "";
  liveAddress: string = "";
  cameraList: any[] = [];
  cameraListCopay: any[] = [];
  speed: number = 1;
  ptz: number = 0;
  monitorId: number = -1;
  btnList: any = [0, 6, 3, 7, 1, 5, 2, 4];

  btnList1: any = ["变焦", "缩放"];
  defaultPoster: any = defaultPoster;

  mounted() {
    this.CameraData = this.$route.params.CameraData;
    console.log(this.CameraData, " this.CameraData");
    this.stationName = this.$route.params.stationName;
    this.CameraData.forEach((v: any) => {
      v.stationList.forEach((x: any) => {
        if (x.stationName === this.stationName) {
          this.stationList = v.stationList;
          this.streetCode = v.streetCode;
          this.cameraList = x.cameList;
          this.cameraListCopay = x.cameList;
        }
      });
    });
  }
  back() {
    this.$store.dispatch("user/setPageUnm", 1);
    this.$router.back();
  }
  // 清空搜索框
  clearSearch() {
    this.keywords = "";
    this.search();
  }
  /* 放大 */
  showWindonw(url: string, item: any) {
    this.liveAddress = url;
    this.show = true;
    this.monitorId = item.monitorId;
    console.log(this.show, 999);
    // this.$refs['video' + index].pause()
  }

  /* 圆圈按钮按下 */
  DchangePtz(item: any) {
    this.ptz = item;
    this.start();
  }
  /* 圆圈按钮释放 */
  UchangePtz(item: any) {
    this.ptz = item;
    this.stop();
  }

  DchangePtzT(item: any, isAdd: boolean) {
    if (isAdd) {
      switch (item) {
        case "变焦":
          this.ptz = 11;
          break;

        case "缩放":
          this.ptz = 8;

          break;

        default:
          this.ptz = 8;
          break;
      }
    } else {
      switch (item) {
        case "变焦":
          this.ptz = 10;
          break;

        case "缩放":
          this.ptz = 9;
          break;

        default:
          this.ptz = 8;
          break;
      }
    }
    this.start();
  }

  UchangePtzT(item: any, isAdd: boolean) {
    if (isAdd) {
      switch (item) {
        case "变焦":
          this.ptz = 11;
          break;

        case "缩放":
          this.ptz = 8;

          break;

        default:
          this.ptz = 8;
          break;
      }
    } else {
      switch (item) {
        case "变焦":
          this.ptz = 10;
          break;

        case "缩放":
          this.ptz = 9;
          break;

        default:
          this.ptz = 8;
          break;
      }
    }
    this.stop();
  }

  /* 开始 */
  start() {
    const data = {
      monitorId: this.monitorId,
      ptz: this.ptz,
      speed: this.speed,
    };
    ptzC(data).then((res) => {});
  }

  /* 停止 */
  stop() {
    const data = {
      monitorId: this.monitorId,
      ptz: this.ptz,
    };
    ptzStop(data).then((res) => {});
  }

  /* 速度 */
  changeSpeed(index: number) {
    this.speed = index;
  }
  // 搜索
  search() {
    if (this.keywords) {
      this.cameraList = this.cameraListCopay.filter((v) => {
        return v.name.includes(this.keywords.trim());
      });
    } else {
      this.cameraList = this.cameraListCopay;
    }
  }
  videoError(e: any) {
    console.log(`播放器错误：${JSON.stringify(e)}`);
  }
  changePage(index: any) {
    this.pageNum = index + 1;
    this.$store.dispatch("user/setPageUnm", index + 1);
  }
  // 水质站点列表
  waterStationList: Array<any> = [];
  // 默认选中水质站点
  btnClass = false;
  defaultStationValue = "";
  pageNum: number = 1;
  pageSize: number = 12;
  src1 = require("../../../assets/react.png");
  src2 = require("../../../assets/react2x.png");

  closeWindow() {
    this.show = false;
  }

  // 街道切换
  ChangeStreet(value: string): void {
    this.stationList = [];
    this.stationName = "";
    this.CameraData.forEach((v: any) => {
      if (v.streetCode === this.streetCode) {
        this.stationList = v.stationList;
      }
    });
    this.stationName = this.stationList[0].stationName;
    this.cameraList = this.stationList[0].cameList;
    this.cameraListCopay = this.stationList[0].cameList;
  }

  //站点切换
  ChangeStation(value: string): void {
    this.stationList.forEach((v: any) => {
      if (v.stationName === this.stationName) {
        this.cameraList = v.cameList;
        this.cameraListCopay = v.cameList;
      }
    });
  }
  /*回放录像 */
  CheckVideo(item: any, index: any) {
    this.btnClass = false;
    // console.log(item);
    this.$router.push(
      `/videoList?id=${item.monitorId}&pageNum=${this.pageNum}&type=pollution`
    );
  }
  /**
   * 切换全屏状态
   */
  fullscreenSwich() {
    const docElm: any = document.documentElement;
    const ele: any = document.querySelector("#pollution_video");
    // ele.style.height = '10.8rem'
    // ele.style.width = '19.2rem'
    // if (!this.fullScreen) {
    if (ele.requestFullscreen) {
      ele.requestFullscreen();
    } else if (ele.msRequestFullscreen) {
      ele.msRequestFullscreen();
    } else if (ele.mozRequestFullScreen) {
      ele.mozRequestFullScreen();
    } else if (ele.webkitRequestFullScreen) {
      ele.webkitRequestFullScreen();
    }
    // } else {
    //   this.exitFullscreen()
    // }
    // this.fullScreen = false
  }
  /**
   * 退出全屏
   */
  exitFullscreen() {
    const docu: any = document;
    const ele: any = document.querySelector("#pollution_video");
    // ele.style.height = '7.64rem'
    // ele.style.width = '16.78rem'
    if (ele.exitFullscreen) {
      ele.exitFullscreen();
    } else if (ele.msExitFullscreen) {
      ele.msExitFullscreen();
    } else if (ele.mozCancelFullScreen) {
      ele.mozCancelFullScreen();
    } else if (ele.webkitCancelFullScreen) {
      ele.webkitCancelFullScreen();
    }
  }
}
</script>
