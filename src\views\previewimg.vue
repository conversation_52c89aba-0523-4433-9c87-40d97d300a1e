<template>
  <div>
    <el-image-viewer
      v-if="showViewer"
      :on-close="closeViewer"
      :url-list="urlList"
    />
  </div>
</template>

<script>
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'

export default {
  name: 'PreviewImage',
  components: {
    ElImageViewer
  },
  props: {
    urlList: { // 预览图片列表
      type: Array,
      default: null
    },
    index: {
      type: Number,
      default: 0
    },
    showViewer: {
      type: Boolean,
      default: false
    }

  },
  data() {
    return {
      // showViewer: false
    }
  },
  methods: {
    closeViewer() {
      // this.showViewer = false
      this.$emit('closeViewer')
    }
  }
}
</script>

<style>
</style>
