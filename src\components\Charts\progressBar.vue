<template>
  <div :id="id" :style="{ height: height, width: width }" />
</template>

<script lang="ts">
import * as echarts from 'echarts'
import { Component, Prop, Watch } from "vue-property-decorator";
import { mixins } from "vue-class-component";
import ResizeMixin from "./mixins/resize";
interface AirData {
  bottomList: string[];
  dataList: string[];
  unit: string;
  name?: string;
  colorType?: string;
  warnValue?: number;
  miniValue?: number;
  maxValue?: number;
}
@Component({
  name: "CarLineChartDashed"
})
export default class extends mixins(ResizeMixin) {
  // @Prop({ default: "chart" }) private className!: string;
  @Prop({ default: "chart" }) private id!: string;
  @Prop({ default: "200px" }) private width!: string;
  @Prop({ default: "200px" }) private height!: string;
  // @Prop({ required: false }) private propData!: AirData;
  @Prop({ required: true }) private propData:any;
  @Watch("propData", { immediate: true, deep: true })
  public onMsgChanged(newValue: any, oldValue: any) {
    if (newValue) {
      this.$nextTick(() => {
        this.initChart();
      });
    }
  }
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
  }
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.clear();
    this.chart.dispose();
    this.chart = null;
  }

  private initChart() {

    if(this.chart === null || this.chart === undefined) {
      this.chart = echarts.init(
        document.getElementById(this.id) as HTMLDivElement
      );
    }
    const myColor = ["#0096f3"];
    this.chart.setOption({
      grid: {
        left: 10
        // top: "20%"
        // right: "0%",
        // bottom: -50
        // left: "left"
        // containLabel: true
      },
      xAxis: [
        {
          show: false
        }
      ],
      yAxis: [
        {
          axisTick: "none",
          axisLine: "none",
          offset: "27",
          axisLabel: {
            textStyle: {
              color: "#ffffff",
              fontSize: "16"
            }
          },
          show: false,
          data: ["8"]
        },
        {
          axisTick: "none",
          axisLine: "none",
          axisLabel: {
            textStyle: {
              color: "#ffffff",
              fontSize: "16"
            }
          },
          show: false,
          data: ["12567"]
        },
        {
          axisLine: {
            lineStyle: {
              color: "rgba(0,0,0,0)"
            }
          },
          data: []
        }
      ],
      series: [
        {
          name: "条",
          type: "bar",
          stack: "圆",
          yAxisIndex: 0,
          data: [this.propData],
          label: {
            normal: {
              show: true,
              position: "right",
              distance: 10,
              formatter: function(param: any) {
                return param.value + "%";
              },
              textStyle: {
                color: "#ffffff",
                fontSize: "16"
              }
            }
          },
          barWidth: 8,
          itemStyle: {
            normal: {
              color: function(params: any) {
                const num = myColor.length;
                return myColor[params.dataIndex % num];
              }
            }
          },
          z: 2
        },
        {
          name: "内圆",
          type: "scatter",
          stack: "圆",
          yAxisIndex: 0,
          data: [0],
          label: {
            normal: {
              show: false,
              position: "right",
              formatter: function(param: any) {
                return param.value + "%";
              },

              textStyle: {
                color: "#ffffff",
                fontSize: "16"
              }
            }
          },
          // eslint-disable-next-line no-dupe-keys
          // yAxisIndex: 2,
          symbolSize: 14,
          itemStyle: {
            normal: {
              borderColor: "#fff",
              borderWidth: 3,
              color: function(params: any) {
                const num = myColor.length;
                return myColor[params.dataIndex % num];
              },
              opacity: 1
            }
          },
          z: 2
        },
        {
          name: "白框",
          type: "bar",
          yAxisIndex: 1,
          barGap: "-100%",
          data: [99.5],
          barWidth: 20,
          itemStyle: {
            normal: {
              color: "#0e2147",
              barBorderRadius: 5
            }
          },
          z: 1
        },
        {
          name: "外框",
          type: "bar",
          yAxisIndex: 2,
          barGap: "-100%",
          data: [100],
          barWidth: 21,
          itemStyle: {
            normal: {
              color: function(params: any) {
                const num = myColor.length;
                return myColor[params.dataIndex % num];
              },
              barBorderRadius: 2
            }
          },
          z: 0
        },
        {
          name: "外圆",
          type: "scatter",
          hoverAnimation: false,
          data: [0],
          yAxisIndex: 2,
          symbolSize: 20,
          itemStyle: {
            normal: {
              color: function(params: any) {
                const num = myColor.length;
                return myColor[params.dataIndex % num];
              },
              opacity: 1
            }
          },
          z: 2
        }
      ]
    } as any);
  }
}
</script>
