(function e(t,i){if(typeof exports==="object"&&typeof module==="object")module.exports=i();else if(typeof define==="function"&&define.amd)define([],i);else{var n=i();for(var r in n)(typeof exports==="object"?exports:t)[r]=n[r]}})(window,function(){return function(e){var t={};function i(n){if(t[n]){return t[n].exports}var r=t[n]={i:n,l:false,exports:{}};e[n].call(r.exports,r,r.exports,i);r.l=true;return r.exports}i.m=e;i.c=t;i.d=function(e,t,n){if(!i.o(e,t)){Object.defineProperty(e,t,{enumerable:true,get:n})}};i.r=function(e){if(typeof Symbol!=="undefined"&&Symbol.toStringTag){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"})}Object.defineProperty(e,"__esModule",{value:true})};i.t=function(e,t){if(t&1)e=i(e);if(t&8)return e;if(t&4&&typeof e==="object"&&e&&e.__esModule)return e;var n=Object.create(null);i.r(n);Object.defineProperty(n,"default",{enumerable:true,value:e});if(t&2&&typeof e!="string")for(var r in e)i.d(n,r,function(t){return e[t]}.bind(null,r));return n};i.n=function(e){var t=e&&e.__esModule?function t(){return e["default"]}:function t(){return e};i.d(t,"a",t);return t};i.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)};i.p="";return i(i.s=12)}([function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.setLogger=a;t.isEnable=o;t.log=s;t.error=l;var n=void 0;var r=void 0;function a(){n=console.log;r=console.error}function o(){return n!=null}function s(e){if(n){for(var t=arguments.length,i=Array(t>1?t-1:0),r=1;r<t;r++){i[r-1]=arguments[r]}n.apply(undefined,[e].concat(i))}}function l(e){if(r){for(var t=arguments.length,i=Array(t>1?t-1:0),n=1;n<t;n++){i[n-1]=arguments[n]}r.apply(undefined,[e].concat(i))}}},function(e,t,i){var n,r;
/*!
 * jQuery JavaScript Library v1.12.1
 * http://jquery.com/
 *
 * Includes Sizzle.js
 * http://sizzlejs.com/
 *
 * Copyright jQuery Foundation and other contributors
 * Released under the MIT license
 * http://jquery.org/license
 *
 * Date: 2016-02-22T19:07Z
 */
/*!
 * jQuery JavaScript Library v1.12.1
 * http://jquery.com/
 *
 * Includes Sizzle.js
 * http://sizzlejs.com/
 *
 * Copyright jQuery Foundation and other contributors
 * Released under the MIT license
 * http://jquery.org/license
 *
 * Date: 2016-02-22T19:07Z
 */
(function(t,i){if(typeof e==="object"&&typeof e.exports==="object"){e.exports=t.document?i(t,true):function(e){if(!e.document){throw new Error("jQuery requires a window with a document")}return i(e)}}else{i(t)}})(typeof window!=="undefined"?window:this,function(i,a){var o=[];var s=i.document;var l=o.slice;var u=o.concat;var f=o.push;var c=o.indexOf;var d={};var h=d.toString;var p=d.hasOwnProperty;var y={};var v="1.12.1",m=function(e,t){return new m.fn.init(e,t)},g=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,b=/^-ms-/,S=/-([\da-z])/gi,w=function(e,t){return t.toUpperCase()};m.fn=m.prototype={jquery:v,constructor:m,selector:"",length:0,toArray:function(){return l.call(this)},get:function(e){return e!=null?e<0?this[e+this.length]:this[e]:l.call(this)},pushStack:function(e){var t=m.merge(this.constructor(),e);t.prevObject=this;t.context=this.context;return t},each:function(e){return m.each(this,e)},map:function(e){return this.pushStack(m.map(this,function(t,i){return e.call(t,i,t)}))},slice:function(){return this.pushStack(l.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},eq:function(e){var t=this.length,i=+e+(e<0?t:0);return this.pushStack(i>=0&&i<t?[this[i]]:[])},end:function(){return this.prevObject||this.constructor()},push:f,sort:o.sort,splice:o.splice};m.extend=m.fn.extend=function(){var e,t,i,n,r,a,o=arguments[0]||{},s=1,l=arguments.length,u=false;if(typeof o==="boolean"){u=o;o=arguments[s]||{};s++}if(typeof o!=="object"&&!m.isFunction(o)){o={}}if(s===l){o=this;s--}for(;s<l;s++){if((r=arguments[s])!=null){for(n in r){e=o[n];i=r[n];if(o===i){continue}if(u&&i&&(m.isPlainObject(i)||(t=m.isArray(i)))){if(t){t=false;a=e&&m.isArray(e)?e:[]}else{a=e&&m.isPlainObject(e)?e:{}}o[n]=m.extend(u,a,i)}else if(i!==undefined){o[n]=i}}}}return o};m.extend({expando:"jQuery"+(v+Math.random()).replace(/\D/g,""),isReady:true,error:function(e){throw new Error(e)},noop:function(){},isFunction:function(e){return m.type(e)==="function"},isArray:Array.isArray||function(e){return m.type(e)==="array"},isWindow:function(e){return e!=null&&e==e.window},isNumeric:function(e){var t=e&&e.toString();return!m.isArray(e)&&t-parseFloat(t)+1>=0},isEmptyObject:function(e){var t;for(t in e){return false}return true},isPlainObject:function(e){var t;if(!e||m.type(e)!=="object"||e.nodeType||m.isWindow(e)){return false}try{if(e.constructor&&!p.call(e,"constructor")&&!p.call(e.constructor.prototype,"isPrototypeOf")){return false}}catch(e){return false}if(!y.ownFirst){for(t in e){return p.call(e,t)}}for(t in e){}return t===undefined||p.call(e,t)},type:function(e){if(e==null){return e+""}return typeof e==="object"||typeof e==="function"?d[h.call(e)]||"object":typeof e},globalEval:function(e){if(e&&m.trim(e)){(i.execScript||function(e){i["eval"].call(i,e)})(e)}},camelCase:function(e){return e.replace(b,"ms-").replace(S,w)},nodeName:function(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()},each:function(e,t){var i,n=0;if(P(e)){i=e.length;for(;n<i;n++){if(t.call(e[n],n,e[n])===false){break}}}else{for(n in e){if(t.call(e[n],n,e[n])===false){break}}}return e},trim:function(e){return e==null?"":(e+"").replace(g,"")},makeArray:function(e,t){var i=t||[];if(e!=null){if(P(Object(e))){m.merge(i,typeof e==="string"?[e]:e)}else{f.call(i,e)}}return i},inArray:function(e,t,i){var n;if(t){if(c){return c.call(t,e,i)}n=t.length;i=i?i<0?Math.max(0,n+i):i:0;for(;i<n;i++){if(i in t&&t[i]===e){return i}}}return-1},merge:function(e,t){var i=+t.length,n=0,r=e.length;while(n<i){e[r++]=t[n++]}if(i!==i){while(t[n]!==undefined){e[r++]=t[n++]}}e.length=r;return e},grep:function(e,t,i){var n,r=[],a=0,o=e.length,s=!i;for(;a<o;a++){n=!t(e[a],a);if(n!==s){r.push(e[a])}}return r},map:function(e,t,i){var n,r,a=0,o=[];if(P(e)){n=e.length;for(;a<n;a++){r=t(e[a],a,i);if(r!=null){o.push(r)}}}else{for(a in e){r=t(e[a],a,i);if(r!=null){o.push(r)}}}return u.apply([],o)},guid:1,proxy:function(e,t){var i,n,r;if(typeof t==="string"){r=e[t];t=e;e=r}if(!m.isFunction(e)){return undefined}i=l.call(arguments,2);n=function(){return e.apply(t||this,i.concat(l.call(arguments)))};n.guid=e.guid=e.guid||m.guid++;return n},now:function(){return+new Date},support:y});if(typeof Symbol==="function"){m.fn[Symbol.iterator]=o[Symbol.iterator]}m.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(e,t){d["[object "+t+"]"]=t.toLowerCase()});function P(e){var t=!!e&&"length"in e&&e.length,i=m.type(e);if(i==="function"||m.isWindow(e)){return false}return i==="array"||t===0||typeof t==="number"&&t>0&&t-1 in e}var C=
/*!
 * Sizzle CSS Selector Engine v2.2.1
 * http://sizzlejs.com/
 *
 * Copyright jQuery Foundation and other contributors
 * Released under the MIT license
 * http://jquery.org/license
 *
 * Date: 2015-10-17
 */
function(e){var t,i,n,r,a,o,s,l,u,f,c,d,h,p,y,v,m,g,b,S="sizzle"+1*new Date,w=e.document,P=0,C=0,_=ae(),k=ae(),D=ae(),x=function(e,t){if(e===t){c=true}return 0},T=1<<31,M={}.hasOwnProperty,L=[],W=L.pop,E=L.push,A=L.push,R=L.slice,B=function(e,t){var i=0,n=e.length;for(;i<n;i++){if(e[i]===t){return i}}return-1},I="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",z="[\\x20\\t\\r\\n\\f]",O="(?:\\\\.|[\\w-]|[^\\x00-\\xa0])+",F="\\["+z+"*("+O+")(?:"+z+"*([*^$|!~]?=)"+z+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+O+"))|)"+z+"*\\]",N=":("+O+")(?:\\(("+"('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|"+"((?:\\\\.|[^\\\\()[\\]]|"+F+")*)|"+".*"+")\\)|)",U=new RegExp(z+"+","g"),j=new RegExp("^"+z+"+|((?:^|[^\\\\])(?:\\\\.)*)"+z+"+$","g"),H=new RegExp("^"+z+"*,"+z+"*"),q=new RegExp("^"+z+"*([>+~]|"+z+")"+z+"*"),V=new RegExp("="+z+"*([^\\]'\"]*?)"+z+"*\\]","g"),J=new RegExp(N),G=new RegExp("^"+O+"$"),Y={ID:new RegExp("^#("+O+")"),CLASS:new RegExp("^\\.("+O+")"),TAG:new RegExp("^("+O+"|[*])"),ATTR:new RegExp("^"+F),PSEUDO:new RegExp("^"+N),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+z+"*(even|odd|(([+-]|)(\\d*)n|)"+z+"*(?:([+-]|)"+z+"*(\\d+)|))"+z+"*\\)|)","i"),bool:new RegExp("^(?:"+I+")$","i"),needsContext:new RegExp("^"+z+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+z+"*((?:-\\d)?\\d*)"+z+"*\\)|)(?=[^-]|$)","i")},X=/^(?:input|select|textarea|button)$/i,K=/^h\d$/i,$=/^[^{]+\{\s*\[native \w/,Z=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,Q=/[+~]/,ee=/'|\\/g,te=new RegExp("\\\\([\\da-f]{1,6}"+z+"?|("+z+")|.)","ig"),ie=function(e,t,i){var n="0x"+t-65536;return n!==n||i?t:n<0?String.fromCharCode(n+65536):String.fromCharCode(n>>10|55296,n&1023|56320)},ne=function(){d()};try{A.apply(L=R.call(w.childNodes),w.childNodes);L[w.childNodes.length].nodeType}catch(e){A={apply:L.length?function(e,t){E.apply(e,R.call(t))}:function(e,t){var i=e.length,n=0;while(e[i++]=t[n++]){}e.length=i-1}}}function re(e,t,n,r){var a,s,u,f,c,p,m,g,P=t&&t.ownerDocument,C=t?t.nodeType:9;n=n||[];if(typeof e!=="string"||!e||C!==1&&C!==9&&C!==11){return n}if(!r){if((t?t.ownerDocument||t:w)!==h){d(t)}t=t||h;if(y){if(C!==11&&(p=Z.exec(e))){if(a=p[1]){if(C===9){if(u=t.getElementById(a)){if(u.id===a){n.push(u);return n}}else{return n}}else{if(P&&(u=P.getElementById(a))&&b(t,u)&&u.id===a){n.push(u);return n}}}else if(p[2]){A.apply(n,t.getElementsByTagName(e));return n}else if((a=p[3])&&i.getElementsByClassName&&t.getElementsByClassName){A.apply(n,t.getElementsByClassName(a));return n}}if(i.qsa&&!D[e+" "]&&(!v||!v.test(e))){if(C!==1){P=t;g=e}else if(t.nodeName.toLowerCase()!=="object"){if(f=t.getAttribute("id")){f=f.replace(ee,"\\$&")}else{t.setAttribute("id",f=S)}m=o(e);s=m.length;c=G.test(f)?"#"+f:"[id='"+f+"']";while(s--){m[s]=c+" "+ye(m[s])}g=m.join(",");P=Q.test(e)&&he(t.parentNode)||t}if(g){try{A.apply(n,P.querySelectorAll(g));return n}catch(e){}finally{if(f===S){t.removeAttribute("id")}}}}}}return l(e.replace(j,"$1"),t,n,r)}function ae(){var e=[];function t(i,r){if(e.push(i+" ")>n.cacheLength){delete t[e.shift()]}return t[i+" "]=r}return t}function oe(e){e[S]=true;return e}function se(e){var t=h.createElement("div");try{return!!e(t)}catch(e){return false}finally{if(t.parentNode){t.parentNode.removeChild(t)}t=null}}function le(e,t){var i=e.split("|"),r=i.length;while(r--){n.attrHandle[i[r]]=t}}function ue(e,t){var i=t&&e,n=i&&e.nodeType===1&&t.nodeType===1&&(~t.sourceIndex||T)-(~e.sourceIndex||T);if(n){return n}if(i){while(i=i.nextSibling){if(i===t){return-1}}}return e?1:-1}function fe(e){return function(t){var i=t.nodeName.toLowerCase();return i==="input"&&t.type===e}}function ce(e){return function(t){var i=t.nodeName.toLowerCase();return(i==="input"||i==="button")&&t.type===e}}function de(e){return oe(function(t){t=+t;return oe(function(i,n){var r,a=e([],i.length,t),o=a.length;while(o--){if(i[r=a[o]]){i[r]=!(n[r]=i[r])}}})})}function he(e){return e&&typeof e.getElementsByTagName!=="undefined"&&e}i=re.support={};a=re.isXML=function(e){var t=e&&(e.ownerDocument||e).documentElement;return t?t.nodeName!=="HTML":false};d=re.setDocument=function(e){var t,r,o=e?e.ownerDocument||e:w;if(o===h||o.nodeType!==9||!o.documentElement){return h}h=o;p=h.documentElement;y=!a(h);if((r=h.defaultView)&&r.top!==r){if(r.addEventListener){r.addEventListener("unload",ne,false)}else if(r.attachEvent){r.attachEvent("onunload",ne)}}i.attributes=se(function(e){e.className="i";return!e.getAttribute("className")});i.getElementsByTagName=se(function(e){e.appendChild(h.createComment(""));return!e.getElementsByTagName("*").length});i.getElementsByClassName=$.test(h.getElementsByClassName);i.getById=se(function(e){p.appendChild(e).id=S;return!h.getElementsByName||!h.getElementsByName(S).length});if(i.getById){n.find["ID"]=function(e,t){if(typeof t.getElementById!=="undefined"&&y){var i=t.getElementById(e);return i?[i]:[]}};n.filter["ID"]=function(e){var t=e.replace(te,ie);return function(e){return e.getAttribute("id")===t}}}else{delete n.find["ID"];n.filter["ID"]=function(e){var t=e.replace(te,ie);return function(e){var i=typeof e.getAttributeNode!=="undefined"&&e.getAttributeNode("id");return i&&i.value===t}}}n.find["TAG"]=i.getElementsByTagName?function(e,t){if(typeof t.getElementsByTagName!=="undefined"){return t.getElementsByTagName(e)}else if(i.qsa){return t.querySelectorAll(e)}}:function(e,t){var i,n=[],r=0,a=t.getElementsByTagName(e);if(e==="*"){while(i=a[r++]){if(i.nodeType===1){n.push(i)}}return n}return a};n.find["CLASS"]=i.getElementsByClassName&&function(e,t){if(typeof t.getElementsByClassName!=="undefined"&&y){return t.getElementsByClassName(e)}};m=[];v=[];if(i.qsa=$.test(h.querySelectorAll)){se(function(e){p.appendChild(e).innerHTML="<a id='"+S+"'></a>"+"<select id='"+S+"-\r\\' msallowcapture=''>"+"<option selected=''></option></select>";if(e.querySelectorAll("[msallowcapture^='']").length){v.push("[*^$]="+z+"*(?:''|\"\")")}if(!e.querySelectorAll("[selected]").length){v.push("\\["+z+"*(?:value|"+I+")")}if(!e.querySelectorAll("[id~="+S+"-]").length){v.push("~=")}if(!e.querySelectorAll(":checked").length){v.push(":checked")}if(!e.querySelectorAll("a#"+S+"+*").length){v.push(".#.+[+~]")}});se(function(e){var t=h.createElement("input");t.setAttribute("type","hidden");e.appendChild(t).setAttribute("name","D");if(e.querySelectorAll("[name=d]").length){v.push("name"+z+"*[*^$|!~]?=")}if(!e.querySelectorAll(":enabled").length){v.push(":enabled",":disabled")}e.querySelectorAll("*,:x");v.push(",.*:")})}if(i.matchesSelector=$.test(g=p.matches||p.webkitMatchesSelector||p.mozMatchesSelector||p.oMatchesSelector||p.msMatchesSelector)){se(function(e){i.disconnectedMatch=g.call(e,"div");g.call(e,"[s!='']:x");m.push("!=",N)})}v=v.length&&new RegExp(v.join("|"));m=m.length&&new RegExp(m.join("|"));t=$.test(p.compareDocumentPosition);b=t||$.test(p.contains)?function(e,t){var i=e.nodeType===9?e.documentElement:e,n=t&&t.parentNode;return e===n||!!(n&&n.nodeType===1&&(i.contains?i.contains(n):e.compareDocumentPosition&&e.compareDocumentPosition(n)&16))}:function(e,t){if(t){while(t=t.parentNode){if(t===e){return true}}}return false};x=t?function(e,t){if(e===t){c=true;return 0}var n=!e.compareDocumentPosition-!t.compareDocumentPosition;if(n){return n}n=(e.ownerDocument||e)===(t.ownerDocument||t)?e.compareDocumentPosition(t):1;if(n&1||!i.sortDetached&&t.compareDocumentPosition(e)===n){if(e===h||e.ownerDocument===w&&b(w,e)){return-1}if(t===h||t.ownerDocument===w&&b(w,t)){return 1}return f?B(f,e)-B(f,t):0}return n&4?-1:1}:function(e,t){if(e===t){c=true;return 0}var i,n=0,r=e.parentNode,a=t.parentNode,o=[e],s=[t];if(!r||!a){return e===h?-1:t===h?1:r?-1:a?1:f?B(f,e)-B(f,t):0}else if(r===a){return ue(e,t)}i=e;while(i=i.parentNode){o.unshift(i)}i=t;while(i=i.parentNode){s.unshift(i)}while(o[n]===s[n]){n++}return n?ue(o[n],s[n]):o[n]===w?-1:s[n]===w?1:0};return h};re.matches=function(e,t){return re(e,null,null,t)};re.matchesSelector=function(e,t){if((e.ownerDocument||e)!==h){d(e)}t=t.replace(V,"='$1']");if(i.matchesSelector&&y&&!D[t+" "]&&(!m||!m.test(t))&&(!v||!v.test(t))){try{var n=g.call(e,t);if(n||i.disconnectedMatch||e.document&&e.document.nodeType!==11){return n}}catch(e){}}return re(t,h,null,[e]).length>0};re.contains=function(e,t){if((e.ownerDocument||e)!==h){d(e)}return b(e,t)};re.attr=function(e,t){if((e.ownerDocument||e)!==h){d(e)}var r=n.attrHandle[t.toLowerCase()],a=r&&M.call(n.attrHandle,t.toLowerCase())?r(e,t,!y):undefined;return a!==undefined?a:i.attributes||!y?e.getAttribute(t):(a=e.getAttributeNode(t))&&a.specified?a.value:null};re.error=function(e){throw new Error("Syntax error, unrecognized expression: "+e)};re.uniqueSort=function(e){var t,n=[],r=0,a=0;c=!i.detectDuplicates;f=!i.sortStable&&e.slice(0);e.sort(x);if(c){while(t=e[a++]){if(t===e[a]){r=n.push(a)}}while(r--){e.splice(n[r],1)}}f=null;return e};r=re.getText=function(e){var t,i="",n=0,a=e.nodeType;if(!a){while(t=e[n++]){i+=r(t)}}else if(a===1||a===9||a===11){if(typeof e.textContent==="string"){return e.textContent}else{for(e=e.firstChild;e;e=e.nextSibling){i+=r(e)}}}else if(a===3||a===4){return e.nodeValue}return i};n=re.selectors={cacheLength:50,createPseudo:oe,match:Y,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:true}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:true},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){e[1]=e[1].replace(te,ie);e[3]=(e[3]||e[4]||e[5]||"").replace(te,ie);if(e[2]==="~="){e[3]=" "+e[3]+" "}return e.slice(0,4)},CHILD:function(e){e[1]=e[1].toLowerCase();if(e[1].slice(0,3)==="nth"){if(!e[3]){re.error(e[0])}e[4]=+(e[4]?e[5]+(e[6]||1):2*(e[3]==="even"||e[3]==="odd"));e[5]=+(e[7]+e[8]||e[3]==="odd")}else if(e[3]){re.error(e[0])}return e},PSEUDO:function(e){var t,i=!e[6]&&e[2];if(Y["CHILD"].test(e[0])){return null}if(e[3]){e[2]=e[4]||e[5]||""}else if(i&&J.test(i)&&(t=o(i,true))&&(t=i.indexOf(")",i.length-t)-i.length)){e[0]=e[0].slice(0,t);e[2]=i.slice(0,t)}return e.slice(0,3)}},filter:{TAG:function(e){var t=e.replace(te,ie).toLowerCase();return e==="*"?function(){return true}:function(e){return e.nodeName&&e.nodeName.toLowerCase()===t}},CLASS:function(e){var t=_[e+" "];return t||(t=new RegExp("(^|"+z+")"+e+"("+z+"|$)"))&&_(e,function(e){return t.test(typeof e.className==="string"&&e.className||typeof e.getAttribute!=="undefined"&&e.getAttribute("class")||"")})},ATTR:function(e,t,i){return function(n){var r=re.attr(n,e);if(r==null){return t==="!="}if(!t){return true}r+="";return t==="="?r===i:t==="!="?r!==i:t==="^="?i&&r.indexOf(i)===0:t==="*="?i&&r.indexOf(i)>-1:t==="$="?i&&r.slice(-i.length)===i:t==="~="?(" "+r.replace(U," ")+" ").indexOf(i)>-1:t==="|="?r===i||r.slice(0,i.length+1)===i+"-":false}},CHILD:function(e,t,i,n,r){var a=e.slice(0,3)!=="nth",o=e.slice(-4)!=="last",s=t==="of-type";return n===1&&r===0?function(e){return!!e.parentNode}:function(t,i,l){var u,f,c,d,h,p,y=a!==o?"nextSibling":"previousSibling",v=t.parentNode,m=s&&t.nodeName.toLowerCase(),g=!l&&!s,b=false;if(v){if(a){while(y){d=t;while(d=d[y]){if(s?d.nodeName.toLowerCase()===m:d.nodeType===1){return false}}p=y=e==="only"&&!p&&"nextSibling"}return true}p=[o?v.firstChild:v.lastChild];if(o&&g){d=v;c=d[S]||(d[S]={});f=c[d.uniqueID]||(c[d.uniqueID]={});u=f[e]||[];h=u[0]===P&&u[1];b=h&&u[2];d=h&&v.childNodes[h];while(d=++h&&d&&d[y]||(b=h=0)||p.pop()){if(d.nodeType===1&&++b&&d===t){f[e]=[P,h,b];break}}}else{if(g){d=t;c=d[S]||(d[S]={});f=c[d.uniqueID]||(c[d.uniqueID]={});u=f[e]||[];h=u[0]===P&&u[1];b=h}if(b===false){while(d=++h&&d&&d[y]||(b=h=0)||p.pop()){if((s?d.nodeName.toLowerCase()===m:d.nodeType===1)&&++b){if(g){c=d[S]||(d[S]={});f=c[d.uniqueID]||(c[d.uniqueID]={});f[e]=[P,b]}if(d===t){break}}}}}b-=r;return b===n||b%n===0&&b/n>=0}}},PSEUDO:function(e,t){var i,r=n.pseudos[e]||n.setFilters[e.toLowerCase()]||re.error("unsupported pseudo: "+e);if(r[S]){return r(t)}if(r.length>1){i=[e,e,"",t];return n.setFilters.hasOwnProperty(e.toLowerCase())?oe(function(e,i){var n,a=r(e,t),o=a.length;while(o--){n=B(e,a[o]);e[n]=!(i[n]=a[o])}}):function(e){return r(e,0,i)}}return r}},pseudos:{not:oe(function(e){var t=[],i=[],n=s(e.replace(j,"$1"));return n[S]?oe(function(e,t,i,r){var a,o=n(e,null,r,[]),s=e.length;while(s--){if(a=o[s]){e[s]=!(t[s]=a)}}}):function(e,r,a){t[0]=e;n(t,null,a,i);t[0]=null;return!i.pop()}}),has:oe(function(e){return function(t){return re(e,t).length>0}}),contains:oe(function(e){e=e.replace(te,ie);return function(t){return(t.textContent||t.innerText||r(t)).indexOf(e)>-1}}),lang:oe(function(e){if(!G.test(e||"")){re.error("unsupported lang: "+e)}e=e.replace(te,ie).toLowerCase();return function(t){var i;do{if(i=y?t.lang:t.getAttribute("xml:lang")||t.getAttribute("lang")){i=i.toLowerCase();return i===e||i.indexOf(e+"-")===0}}while((t=t.parentNode)&&t.nodeType===1);return false}}),target:function(t){var i=e.location&&e.location.hash;return i&&i.slice(1)===t.id},root:function(e){return e===p},focus:function(e){return e===h.activeElement&&(!h.hasFocus||h.hasFocus())&&!!(e.type||e.href||~e.tabIndex)},enabled:function(e){return e.disabled===false},disabled:function(e){return e.disabled===true},checked:function(e){var t=e.nodeName.toLowerCase();return t==="input"&&!!e.checked||t==="option"&&!!e.selected},selected:function(e){if(e.parentNode){e.parentNode.selectedIndex}return e.selected===true},empty:function(e){for(e=e.firstChild;e;e=e.nextSibling){if(e.nodeType<6){return false}}return true},parent:function(e){return!n.pseudos["empty"](e)},header:function(e){return K.test(e.nodeName)},input:function(e){return X.test(e.nodeName)},button:function(e){var t=e.nodeName.toLowerCase();return t==="input"&&e.type==="button"||t==="button"},text:function(e){var t;return e.nodeName.toLowerCase()==="input"&&e.type==="text"&&((t=e.getAttribute("type"))==null||t.toLowerCase()==="text")},first:de(function(){return[0]}),last:de(function(e,t){return[t-1]}),eq:de(function(e,t,i){return[i<0?i+t:i]}),even:de(function(e,t){var i=0;for(;i<t;i+=2){e.push(i)}return e}),odd:de(function(e,t){var i=1;for(;i<t;i+=2){e.push(i)}return e}),lt:de(function(e,t,i){var n=i<0?i+t:i;for(;--n>=0;){e.push(n)}return e}),gt:de(function(e,t,i){var n=i<0?i+t:i;for(;++n<t;){e.push(n)}return e})}};n.pseudos["nth"]=n.pseudos["eq"];for(t in{radio:true,checkbox:true,file:true,password:true,image:true}){n.pseudos[t]=fe(t)}for(t in{submit:true,reset:true}){n.pseudos[t]=ce(t)}function pe(){}pe.prototype=n.filters=n.pseudos;n.setFilters=new pe;o=re.tokenize=function(e,t){var i,r,a,o,s,l,u,f=k[e+" "];if(f){return t?0:f.slice(0)}s=e;l=[];u=n.preFilter;while(s){if(!i||(r=H.exec(s))){if(r){s=s.slice(r[0].length)||s}l.push(a=[])}i=false;if(r=q.exec(s)){i=r.shift();a.push({value:i,type:r[0].replace(j," ")});s=s.slice(i.length)}for(o in n.filter){if((r=Y[o].exec(s))&&(!u[o]||(r=u[o](r)))){i=r.shift();a.push({value:i,type:o,matches:r});s=s.slice(i.length)}}if(!i){break}}return t?s.length:s?re.error(e):k(e,l).slice(0)};function ye(e){var t=0,i=e.length,n="";for(;t<i;t++){n+=e[t].value}return n}function ve(e,t,i){var n=t.dir,r=i&&n==="parentNode",a=C++;return t.first?function(t,i,a){while(t=t[n]){if(t.nodeType===1||r){return e(t,i,a)}}}:function(t,i,o){var s,l,u,f=[P,a];if(o){while(t=t[n]){if(t.nodeType===1||r){if(e(t,i,o)){return true}}}}else{while(t=t[n]){if(t.nodeType===1||r){u=t[S]||(t[S]={});l=u[t.uniqueID]||(u[t.uniqueID]={});if((s=l[n])&&s[0]===P&&s[1]===a){return f[2]=s[2]}else{l[n]=f;if(f[2]=e(t,i,o)){return true}}}}}}}function me(e){return e.length>1?function(t,i,n){var r=e.length;while(r--){if(!e[r](t,i,n)){return false}}return true}:e[0]}function ge(e,t,i){var n=0,r=t.length;for(;n<r;n++){re(e,t[n],i)}return i}function be(e,t,i,n,r){var a,o=[],s=0,l=e.length,u=t!=null;for(;s<l;s++){if(a=e[s]){if(!i||i(a,n,r)){o.push(a);if(u){t.push(s)}}}}return o}function Se(e,t,i,n,r,a){if(n&&!n[S]){n=Se(n)}if(r&&!r[S]){r=Se(r,a)}return oe(function(a,o,s,l){var u,f,c,d=[],h=[],p=o.length,y=a||ge(t||"*",s.nodeType?[s]:s,[]),v=e&&(a||!t)?be(y,d,e,s,l):y,m=i?r||(a?e:p||n)?[]:o:v;if(i){i(v,m,s,l)}if(n){u=be(m,h);n(u,[],s,l);f=u.length;while(f--){if(c=u[f]){m[h[f]]=!(v[h[f]]=c)}}}if(a){if(r||e){if(r){u=[];f=m.length;while(f--){if(c=m[f]){u.push(v[f]=c)}}r(null,m=[],u,l)}f=m.length;while(f--){if((c=m[f])&&(u=r?B(a,c):d[f])>-1){a[u]=!(o[u]=c)}}}}else{m=be(m===o?m.splice(p,m.length):m);if(r){r(null,o,m,l)}else{A.apply(o,m)}}})}function we(e){var t,i,r,a=e.length,o=n.relative[e[0].type],s=o||n.relative[" "],l=o?1:0,f=ve(function(e){return e===t},s,true),c=ve(function(e){return B(t,e)>-1},s,true),d=[function(e,i,n){var r=!o&&(n||i!==u)||((t=i).nodeType?f(e,i,n):c(e,i,n));t=null;return r}];for(;l<a;l++){if(i=n.relative[e[l].type]){d=[ve(me(d),i)]}else{i=n.filter[e[l].type].apply(null,e[l].matches);if(i[S]){r=++l;for(;r<a;r++){if(n.relative[e[r].type]){break}}return Se(l>1&&me(d),l>1&&ye(e.slice(0,l-1).concat({value:e[l-2].type===" "?"*":""})).replace(j,"$1"),i,l<r&&we(e.slice(l,r)),r<a&&we(e=e.slice(r)),r<a&&ye(e))}d.push(i)}}return me(d)}function Pe(e,t){var i=t.length>0,r=e.length>0,a=function(a,o,s,l,f){var c,p,v,m=0,g="0",b=a&&[],S=[],w=u,C=a||r&&n.find["TAG"]("*",f),_=P+=w==null?1:Math.random()||.1,k=C.length;if(f){u=o===h||o||f}for(;g!==k&&(c=C[g])!=null;g++){if(r&&c){p=0;if(!o&&c.ownerDocument!==h){d(c);s=!y}while(v=e[p++]){if(v(c,o||h,s)){l.push(c);break}}if(f){P=_}}if(i){if(c=!v&&c){m--}if(a){b.push(c)}}}m+=g;if(i&&g!==m){p=0;while(v=t[p++]){v(b,S,o,s)}if(a){if(m>0){while(g--){if(!(b[g]||S[g])){S[g]=W.call(l)}}}S=be(S)}A.apply(l,S);if(f&&!a&&S.length>0&&m+t.length>1){re.uniqueSort(l)}}if(f){P=_;u=w}return b};return i?oe(a):a}s=re.compile=function(e,t){var i,n=[],r=[],a=D[e+" "];if(!a){if(!t){t=o(e)}i=t.length;while(i--){a=we(t[i]);if(a[S]){n.push(a)}else{r.push(a)}}a=D(e,Pe(r,n));a.selector=e}return a};l=re.select=function(e,t,r,a){var l,u,f,c,d,h=typeof e==="function"&&e,p=!a&&o(e=h.selector||e);r=r||[];if(p.length===1){u=p[0]=p[0].slice(0);if(u.length>2&&(f=u[0]).type==="ID"&&i.getById&&t.nodeType===9&&y&&n.relative[u[1].type]){t=(n.find["ID"](f.matches[0].replace(te,ie),t)||[])[0];if(!t){return r}else if(h){t=t.parentNode}e=e.slice(u.shift().value.length)}l=Y["needsContext"].test(e)?0:u.length;while(l--){f=u[l];if(n.relative[c=f.type]){break}if(d=n.find[c]){if(a=d(f.matches[0].replace(te,ie),Q.test(u[0].type)&&he(t.parentNode)||t)){u.splice(l,1);e=a.length&&ye(u);if(!e){A.apply(r,a);return r}break}}}}(h||s(e,p))(a,t,!y,r,!t||Q.test(e)&&he(t.parentNode)||t);return r};i.sortStable=S.split("").sort(x).join("")===S;i.detectDuplicates=!!c;d();i.sortDetached=se(function(e){return e.compareDocumentPosition(h.createElement("div"))&1});if(!se(function(e){e.innerHTML="<a href='#'></a>";return e.firstChild.getAttribute("href")==="#"})){le("type|href|height|width",function(e,t,i){if(!i){return e.getAttribute(t,t.toLowerCase()==="type"?1:2)}})}if(!i.attributes||!se(function(e){e.innerHTML="<input/>";e.firstChild.setAttribute("value","");return e.firstChild.getAttribute("value")===""})){le("value",function(e,t,i){if(!i&&e.nodeName.toLowerCase()==="input"){return e.defaultValue}})}if(!se(function(e){return e.getAttribute("disabled")==null})){le(I,function(e,t,i){var n;if(!i){return e[t]===true?t.toLowerCase():(n=e.getAttributeNode(t))&&n.specified?n.value:null}})}return re}(i);m.find=C;m.expr=C.selectors;m.expr[":"]=m.expr.pseudos;m.uniqueSort=m.unique=C.uniqueSort;m.text=C.getText;m.isXMLDoc=C.isXML;m.contains=C.contains;var _=function(e,t,i){var n=[],r=i!==undefined;while((e=e[t])&&e.nodeType!==9){if(e.nodeType===1){if(r&&m(e).is(i)){break}n.push(e)}}return n};var k=function(e,t){var i=[];for(;e;e=e.nextSibling){if(e.nodeType===1&&e!==t){i.push(e)}}return i};var D=m.expr.match.needsContext;var x=/^<([\w-]+)\s*\/?>(?:<\/\1>|)$/;var T=/^.[^:#\[\.,]*$/;function M(e,t,i){if(m.isFunction(t)){return m.grep(e,function(e,n){return!!t.call(e,n,e)!==i})}if(t.nodeType){return m.grep(e,function(e){return e===t!==i})}if(typeof t==="string"){if(T.test(t)){return m.filter(t,e,i)}t=m.filter(t,e)}return m.grep(e,function(e){return m.inArray(e,t)>-1!==i})}m.filter=function(e,t,i){var n=t[0];if(i){e=":not("+e+")"}return t.length===1&&n.nodeType===1?m.find.matchesSelector(n,e)?[n]:[]:m.find.matches(e,m.grep(t,function(e){return e.nodeType===1}))};m.fn.extend({find:function(e){var t,i=[],n=this,r=n.length;if(typeof e!=="string"){return this.pushStack(m(e).filter(function(){for(t=0;t<r;t++){if(m.contains(n[t],this)){return true}}}))}for(t=0;t<r;t++){m.find(e,n[t],i)}i=this.pushStack(r>1?m.unique(i):i);i.selector=this.selector?this.selector+" "+e:e;return i},filter:function(e){return this.pushStack(M(this,e||[],false))},not:function(e){return this.pushStack(M(this,e||[],true))},is:function(e){return!!M(this,typeof e==="string"&&D.test(e)?m(e):e||[],false).length}});var L,W=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]*))$/,E=m.fn.init=function(e,t,i){var n,r;if(!e){return this}i=i||L;if(typeof e==="string"){if(e.charAt(0)==="<"&&e.charAt(e.length-1)===">"&&e.length>=3){n=[null,e,null]}else{n=W.exec(e)}if(n&&(n[1]||!t)){if(n[1]){t=t instanceof m?t[0]:t;m.merge(this,m.parseHTML(n[1],t&&t.nodeType?t.ownerDocument||t:s,true));if(x.test(n[1])&&m.isPlainObject(t)){for(n in t){if(m.isFunction(this[n])){this[n](t[n])}else{this.attr(n,t[n])}}}return this}else{r=s.getElementById(n[2]);if(r&&r.parentNode){if(r.id!==n[2]){return L.find(e)}this.length=1;this[0]=r}this.context=s;this.selector=e;return this}}else if(!t||t.jquery){return(t||i).find(e)}else{return this.constructor(t).find(e)}}else if(e.nodeType){this.context=this[0]=e;this.length=1;return this}else if(m.isFunction(e)){return typeof i.ready!=="undefined"?i.ready(e):e(m)}if(e.selector!==undefined){this.selector=e.selector;this.context=e.context}return m.makeArray(e,this)};E.prototype=m.fn;L=m(s);var A=/^(?:parents|prev(?:Until|All))/,R={children:true,contents:true,next:true,prev:true};m.fn.extend({has:function(e){var t,i=m(e,this),n=i.length;return this.filter(function(){for(t=0;t<n;t++){if(m.contains(this,i[t])){return true}}})},closest:function(e,t){var i,n=0,r=this.length,a=[],o=D.test(e)||typeof e!=="string"?m(e,t||this.context):0;for(;n<r;n++){for(i=this[n];i&&i!==t;i=i.parentNode){if(i.nodeType<11&&(o?o.index(i)>-1:i.nodeType===1&&m.find.matchesSelector(i,e))){a.push(i);break}}}return this.pushStack(a.length>1?m.uniqueSort(a):a)},index:function(e){if(!e){return this[0]&&this[0].parentNode?this.first().prevAll().length:-1}if(typeof e==="string"){return m.inArray(this[0],m(e))}return m.inArray(e.jquery?e[0]:e,this)},add:function(e,t){return this.pushStack(m.uniqueSort(m.merge(this.get(),m(e,t))))},addBack:function(e){return this.add(e==null?this.prevObject:this.prevObject.filter(e))}});function B(e,t){do{e=e[t]}while(e&&e.nodeType!==1);return e}m.each({parent:function(e){var t=e.parentNode;return t&&t.nodeType!==11?t:null},parents:function(e){return _(e,"parentNode")},parentsUntil:function(e,t,i){return _(e,"parentNode",i)},next:function(e){return B(e,"nextSibling")},prev:function(e){return B(e,"previousSibling")},nextAll:function(e){return _(e,"nextSibling")},prevAll:function(e){return _(e,"previousSibling")},nextUntil:function(e,t,i){return _(e,"nextSibling",i)},prevUntil:function(e,t,i){return _(e,"previousSibling",i)},siblings:function(e){return k((e.parentNode||{}).firstChild,e)},children:function(e){return k(e.firstChild)},contents:function(e){return m.nodeName(e,"iframe")?e.contentDocument||e.contentWindow.document:m.merge([],e.childNodes)}},function(e,t){m.fn[e]=function(i,n){var r=m.map(this,t,i);if(e.slice(-5)!=="Until"){n=i}if(n&&typeof n==="string"){r=m.filter(n,r)}if(this.length>1){if(!R[e]){r=m.uniqueSort(r)}if(A.test(e)){r=r.reverse()}}return this.pushStack(r)}});var I=/\S+/g;function z(e){var t={};m.each(e.match(I)||[],function(e,i){t[i]=true});return t}m.Callbacks=function(e){e=typeof e==="string"?z(e):m.extend({},e);var t,i,n,r,a=[],o=[],s=-1,l=function(){r=e.once;n=t=true;for(;o.length;s=-1){i=o.shift();while(++s<a.length){if(a[s].apply(i[0],i[1])===false&&e.stopOnFalse){s=a.length;i=false}}}if(!e.memory){i=false}t=false;if(r){if(i){a=[]}else{a=""}}},u={add:function(){if(a){if(i&&!t){s=a.length-1;o.push(i)}(function t(i){m.each(i,function(i,n){if(m.isFunction(n)){if(!e.unique||!u.has(n)){a.push(n)}}else if(n&&n.length&&m.type(n)!=="string"){t(n)}})})(arguments);if(i&&!t){l()}}return this},remove:function(){m.each(arguments,function(e,t){var i;while((i=m.inArray(t,a,i))>-1){a.splice(i,1);if(i<=s){s--}}});return this},has:function(e){return e?m.inArray(e,a)>-1:a.length>0},empty:function(){if(a){a=[]}return this},disable:function(){r=o=[];a=i="";return this},disabled:function(){return!a},lock:function(){r=true;if(!i){u.disable()}return this},locked:function(){return!!r},fireWith:function(e,i){if(!r){i=i||[];i=[e,i.slice?i.slice():i];o.push(i);if(!t){l()}}return this},fire:function(){u.fireWith(this,arguments);return this},fired:function(){return!!n}};return u};m.extend({Deferred:function(e){var t=[["resolve","done",m.Callbacks("once memory"),"resolved"],["reject","fail",m.Callbacks("once memory"),"rejected"],["notify","progress",m.Callbacks("memory")]],i="pending",n={state:function(){return i},always:function(){r.done(arguments).fail(arguments);return this},then:function(){var e=arguments;return m.Deferred(function(i){m.each(t,function(t,a){var o=m.isFunction(e[t])&&e[t];r[a[1]](function(){var e=o&&o.apply(this,arguments);if(e&&m.isFunction(e.promise)){e.promise().progress(i.notify).done(i.resolve).fail(i.reject)}else{i[a[0]+"With"](this===n?i.promise():this,o?[e]:arguments)}})});e=null}).promise()},promise:function(e){return e!=null?m.extend(e,n):n}},r={};n.pipe=n.then;m.each(t,function(e,a){var o=a[2],s=a[3];n[a[1]]=o.add;if(s){o.add(function(){i=s},t[e^1][2].disable,t[2][2].lock)}r[a[0]]=function(){r[a[0]+"With"](this===r?n:this,arguments);return this};r[a[0]+"With"]=o.fireWith});n.promise(r);if(e){e.call(r,r)}return r},when:function(e){var t=0,i=l.call(arguments),n=i.length,r=n!==1||e&&m.isFunction(e.promise)?n:0,a=r===1?e:m.Deferred(),o=function(e,t,i){return function(n){t[e]=this;i[e]=arguments.length>1?l.call(arguments):n;if(i===s){a.notifyWith(t,i)}else if(!--r){a.resolveWith(t,i)}}},s,u,f;if(n>1){s=new Array(n);u=new Array(n);f=new Array(n);for(;t<n;t++){if(i[t]&&m.isFunction(i[t].promise)){i[t].promise().progress(o(t,u,s)).done(o(t,f,i)).fail(a.reject)}else{--r}}}if(!r){a.resolveWith(f,i)}return a.promise()}});var O;m.fn.ready=function(e){m.ready.promise().done(e);return this};m.extend({isReady:false,readyWait:1,holdReady:function(e){if(e){m.readyWait++}else{m.ready(true)}},ready:function(e){if(e===true?--m.readyWait:m.isReady){return}m.isReady=true;if(e!==true&&--m.readyWait>0){return}O.resolveWith(s,[m]);if(m.fn.triggerHandler){m(s).triggerHandler("ready");m(s).off("ready")}}});function F(){if(s.addEventListener){s.removeEventListener("DOMContentLoaded",N);i.removeEventListener("load",N)}else{s.detachEvent("onreadystatechange",N);i.detachEvent("onload",N)}}function N(){if(s.addEventListener||i.event.type==="load"||s.readyState==="complete"){F();m.ready()}}m.ready.promise=function(e){if(!O){O=m.Deferred();if(s.readyState==="complete"||s.readyState!=="loading"&&!s.documentElement.doScroll){i.setTimeout(m.ready)}else if(s.addEventListener){s.addEventListener("DOMContentLoaded",N);i.addEventListener("load",N)}else{s.attachEvent("onreadystatechange",N);i.attachEvent("onload",N);var t=false;try{t=i.frameElement==null&&s.documentElement}catch(e){}if(t&&t.doScroll){(function e(){if(!m.isReady){try{t.doScroll("left")}catch(t){return i.setTimeout(e,50)}F();m.ready()}})()}}}return O.promise(e)};m.ready.promise();var U;for(U in m(y)){break}y.ownFirst=U==="0";y.inlineBlockNeedsLayout=false;m(function(){var e,t,i,n;i=s.getElementsByTagName("body")[0];if(!i||!i.style){return}t=s.createElement("div");n=s.createElement("div");n.style.cssText="position:absolute;border:0;width:0;height:0;top:0;left:-9999px";i.appendChild(n).appendChild(t);if(typeof t.style.zoom!=="undefined"){t.style.cssText="display:inline;margin:0;border:0;padding:1px;width:1px;zoom:1";y.inlineBlockNeedsLayout=e=t.offsetWidth===3;if(e){i.style.zoom=1}}i.removeChild(n)});(function(){var e=s.createElement("div");y.deleteExpando=true;try{delete e.test}catch(e){y.deleteExpando=false}e=null})();var j=function(e){var t=m.noData[(e.nodeName+" ").toLowerCase()],i=+e.nodeType||1;return i!==1&&i!==9?false:!t||t!==true&&e.getAttribute("classid")===t};var H=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,q=/([A-Z])/g;function V(e,t,i){if(i===undefined&&e.nodeType===1){var n="data-"+t.replace(q,"-$1").toLowerCase();i=e.getAttribute(n);if(typeof i==="string"){try{i=i==="true"?true:i==="false"?false:i==="null"?null:+i+""===i?+i:H.test(i)?m.parseJSON(i):i}catch(e){}m.data(e,t,i)}else{i=undefined}}return i}function J(e){var t;for(t in e){if(t==="data"&&m.isEmptyObject(e[t])){continue}if(t!=="toJSON"){return false}}return true}function G(e,t,i,n){if(!j(e)){return}var r,a,s=m.expando,l=e.nodeType,u=l?m.cache:e,f=l?e[s]:e[s]&&s;if((!f||!u[f]||!n&&!u[f].data)&&i===undefined&&typeof t==="string"){return}if(!f){if(l){f=e[s]=o.pop()||m.guid++}else{f=s}}if(!u[f]){u[f]=l?{}:{toJSON:m.noop}}if(typeof t==="object"||typeof t==="function"){if(n){u[f]=m.extend(u[f],t)}else{u[f].data=m.extend(u[f].data,t)}}a=u[f];if(!n){if(!a.data){a.data={}}a=a.data}if(i!==undefined){a[m.camelCase(t)]=i}if(typeof t==="string"){r=a[t];if(r==null){r=a[m.camelCase(t)]}}else{r=a}return r}function Y(e,t,i){if(!j(e)){return}var n,r,a=e.nodeType,o=a?m.cache:e,s=a?e[m.expando]:m.expando;if(!o[s]){return}if(t){n=i?o[s]:o[s].data;if(n){if(!m.isArray(t)){if(t in n){t=[t]}else{t=m.camelCase(t);if(t in n){t=[t]}else{t=t.split(" ")}}}else{t=t.concat(m.map(t,m.camelCase))}r=t.length;while(r--){delete n[t[r]]}if(i?!J(n):!m.isEmptyObject(n)){return}}}if(!i){delete o[s].data;if(!J(o[s])){return}}if(a){m.cleanData([e],true)}else if(y.deleteExpando||o!=o.window){delete o[s]}else{o[s]=undefined}}m.extend({cache:{},noData:{"applet ":true,"embed ":true,"object ":"clsid:D27CDB6E-AE6D-11cf-96B8-************"},hasData:function(e){e=e.nodeType?m.cache[e[m.expando]]:e[m.expando];return!!e&&!J(e)},data:function(e,t,i){return G(e,t,i)},removeData:function(e,t){return Y(e,t)},_data:function(e,t,i){return G(e,t,i,true)},_removeData:function(e,t){return Y(e,t,true)}});m.fn.extend({data:function(e,t){var i,n,r,a=this[0],o=a&&a.attributes;if(e===undefined){if(this.length){r=m.data(a);if(a.nodeType===1&&!m._data(a,"parsedAttrs")){i=o.length;while(i--){if(o[i]){n=o[i].name;if(n.indexOf("data-")===0){n=m.camelCase(n.slice(5));V(a,n,r[n])}}}m._data(a,"parsedAttrs",true)}}return r}if(typeof e==="object"){return this.each(function(){m.data(this,e)})}return arguments.length>1?this.each(function(){m.data(this,e,t)}):a?V(a,e,m.data(a,e)):undefined},removeData:function(e){return this.each(function(){m.removeData(this,e)})}});m.extend({queue:function(e,t,i){var n;if(e){t=(t||"fx")+"queue";n=m._data(e,t);if(i){if(!n||m.isArray(i)){n=m._data(e,t,m.makeArray(i))}else{n.push(i)}}return n||[]}},dequeue:function(e,t){t=t||"fx";var i=m.queue(e,t),n=i.length,r=i.shift(),a=m._queueHooks(e,t),o=function(){m.dequeue(e,t)};if(r==="inprogress"){r=i.shift();n--}if(r){if(t==="fx"){i.unshift("inprogress")}delete a.stop;r.call(e,o,a)}if(!n&&a){a.empty.fire()}},_queueHooks:function(e,t){var i=t+"queueHooks";return m._data(e,i)||m._data(e,i,{empty:m.Callbacks("once memory").add(function(){m._removeData(e,t+"queue");m._removeData(e,i)})})}});m.fn.extend({queue:function(e,t){var i=2;if(typeof e!=="string"){t=e;e="fx";i--}if(arguments.length<i){return m.queue(this[0],e)}return t===undefined?this:this.each(function(){var i=m.queue(this,e,t);m._queueHooks(this,e);if(e==="fx"&&i[0]!=="inprogress"){m.dequeue(this,e)}})},dequeue:function(e){return this.each(function(){m.dequeue(this,e)})},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,t){var i,n=1,r=m.Deferred(),a=this,o=this.length,s=function(){if(!--n){r.resolveWith(a,[a])}};if(typeof e!=="string"){t=e;e=undefined}e=e||"fx";while(o--){i=m._data(a[o],e+"queueHooks");if(i&&i.empty){n++;i.empty.add(s)}}s();return r.promise(t)}});(function(){var e;y.shrinkWrapBlocks=function(){if(e!=null){return e}e=false;var t,i,n;i=s.getElementsByTagName("body")[0];if(!i||!i.style){return}t=s.createElement("div");n=s.createElement("div");n.style.cssText="position:absolute;border:0;width:0;height:0;top:0;left:-9999px";i.appendChild(n).appendChild(t);if(typeof t.style.zoom!=="undefined"){t.style.cssText="-webkit-box-sizing:content-box;-moz-box-sizing:content-box;"+"box-sizing:content-box;display:block;margin:0;border:0;"+"padding:1px;width:1px;zoom:1";t.appendChild(s.createElement("div")).style.width="5px";e=t.offsetWidth!==3}i.removeChild(n);return e}})();var X=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source;var K=new RegExp("^(?:([+-])=|)("+X+")([a-z%]*)$","i");var $=["Top","Right","Bottom","Left"];var Z=function(e,t){e=t||e;return m.css(e,"display")==="none"||!m.contains(e.ownerDocument,e)};function Q(e,t,i,n){var r,a=1,o=20,s=n?function(){return n.cur()}:function(){return m.css(e,t,"")},l=s(),u=i&&i[3]||(m.cssNumber[t]?"":"px"),f=(m.cssNumber[t]||u!=="px"&&+l)&&K.exec(m.css(e,t));if(f&&f[3]!==u){u=u||f[3];i=i||[];f=+l||1;do{a=a||".5";f=f/a;m.style(e,t,f+u)}while(a!==(a=s()/l)&&a!==1&&--o)}if(i){f=+f||+l||0;r=i[1]?f+(i[1]+1)*i[2]:+i[2];if(n){n.unit=u;n.start=f;n.end=r}}return r}var ee=function(e,t,i,n,r,a,o){var s=0,l=e.length,u=i==null;if(m.type(i)==="object"){r=true;for(s in i){ee(e,t,s,i[s],true,a,o)}}else if(n!==undefined){r=true;if(!m.isFunction(n)){o=true}if(u){if(o){t.call(e,n);t=null}else{u=t;t=function(e,t,i){return u.call(m(e),i)}}}if(t){for(;s<l;s++){t(e[s],i,o?n:n.call(e[s],s,t(e[s],i)))}}}return r?e:u?t.call(e):l?t(e[0],i):a};var te=/^(?:checkbox|radio)$/i;var ie=/<([\w:-]+)/;var ne=/^$|\/(?:java|ecma)script/i;var re=/^\s+/;var ae="abbr|article|aside|audio|bdi|canvas|data|datalist|"+"details|dialog|figcaption|figure|footer|header|hgroup|main|"+"mark|meter|nav|output|picture|progress|section|summary|template|time|video";function oe(e){var t=ae.split("|"),i=e.createDocumentFragment();if(i.createElement){while(t.length){i.createElement(t.pop())}}return i}(function(){var e=s.createElement("div"),t=s.createDocumentFragment(),i=s.createElement("input");e.innerHTML="  <link/><table></table><a href='/a'>a</a><input type='checkbox'/>";y.leadingWhitespace=e.firstChild.nodeType===3;y.tbody=!e.getElementsByTagName("tbody").length;y.htmlSerialize=!!e.getElementsByTagName("link").length;y.html5Clone=s.createElement("nav").cloneNode(true).outerHTML!=="<:nav></:nav>";i.type="checkbox";i.checked=true;t.appendChild(i);y.appendChecked=i.checked;e.innerHTML="<textarea>x</textarea>";y.noCloneChecked=!!e.cloneNode(true).lastChild.defaultValue;t.appendChild(e);i=s.createElement("input");i.setAttribute("type","radio");i.setAttribute("checked","checked");i.setAttribute("name","t");e.appendChild(i);y.checkClone=e.cloneNode(true).cloneNode(true).lastChild.checked;y.noCloneEvent=!!e.addEventListener;e[m.expando]=1;y.attributes=!e.getAttribute(m.expando)})();var se={option:[1,"<select multiple='multiple'>","</select>"],legend:[1,"<fieldset>","</fieldset>"],area:[1,"<map>","</map>"],param:[1,"<object>","</object>"],thead:[1,"<table>","</table>"],tr:[2,"<table><tbody>","</tbody></table>"],col:[2,"<table><tbody></tbody><colgroup>","</colgroup></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:y.htmlSerialize?[0,"",""]:[1,"X<div>","</div>"]};se.optgroup=se.option;se.tbody=se.tfoot=se.colgroup=se.caption=se.thead;se.th=se.td;function le(e,t){var i,n,r=0,a=typeof e.getElementsByTagName!=="undefined"?e.getElementsByTagName(t||"*"):typeof e.querySelectorAll!=="undefined"?e.querySelectorAll(t||"*"):undefined;if(!a){for(a=[],i=e.childNodes||e;(n=i[r])!=null;r++){if(!t||m.nodeName(n,t)){a.push(n)}else{m.merge(a,le(n,t))}}}return t===undefined||t&&m.nodeName(e,t)?m.merge([e],a):a}function ue(e,t){var i,n=0;for(;(i=e[n])!=null;n++){m._data(i,"globalEval",!t||m._data(t[n],"globalEval"))}}var fe=/<|&#?\w+;/,ce=/<tbody/i;function de(e){if(te.test(e.type)){e.defaultChecked=e.checked}}function he(e,t,i,n,r){var a,o,s,l,u,f,c,d=e.length,h=oe(t),p=[],v=0;for(;v<d;v++){o=e[v];if(o||o===0){if(m.type(o)==="object"){m.merge(p,o.nodeType?[o]:o)}else if(!fe.test(o)){p.push(t.createTextNode(o))}else{l=l||h.appendChild(t.createElement("div"));u=(ie.exec(o)||["",""])[1].toLowerCase();c=se[u]||se._default;l.innerHTML=c[1]+m.htmlPrefilter(o)+c[2];a=c[0];while(a--){l=l.lastChild}if(!y.leadingWhitespace&&re.test(o)){p.push(t.createTextNode(re.exec(o)[0]))}if(!y.tbody){o=u==="table"&&!ce.test(o)?l.firstChild:c[1]==="<table>"&&!ce.test(o)?l:0;a=o&&o.childNodes.length;while(a--){if(m.nodeName(f=o.childNodes[a],"tbody")&&!f.childNodes.length){o.removeChild(f)}}}m.merge(p,l.childNodes);l.textContent="";while(l.firstChild){l.removeChild(l.firstChild)}l=h.lastChild}}}if(l){h.removeChild(l)}if(!y.appendChecked){m.grep(le(p,"input"),de)}v=0;while(o=p[v++]){if(n&&m.inArray(o,n)>-1){if(r){r.push(o)}continue}s=m.contains(o.ownerDocument,o);l=le(h.appendChild(o),"script");if(s){ue(l)}if(i){a=0;while(o=l[a++]){if(ne.test(o.type||"")){i.push(o)}}}}l=null;return h}(function(){var e,t,n=s.createElement("div");for(e in{submit:true,change:true,focusin:true}){t="on"+e;if(!(y[e]=t in i)){n.setAttribute(t,"t");y[e]=n.attributes[t].expando===false}}n=null})();var pe=/^(?:input|select|textarea)$/i,ye=/^key/,ve=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,me=/^(?:focusinfocus|focusoutblur)$/,ge=/^([^.]*)(?:\.(.+)|)/;function be(){return true}function Se(){return false}function we(){try{return s.activeElement}catch(e){}}function Pe(e,t,i,n,r,a){var o,s;if(typeof t==="object"){if(typeof i!=="string"){n=n||i;i=undefined}for(s in t){Pe(e,s,i,n,t[s],a)}return e}if(n==null&&r==null){r=i;n=i=undefined}else if(r==null){if(typeof i==="string"){r=n;n=undefined}else{r=n;n=i;i=undefined}}if(r===false){r=Se}else if(!r){return e}if(a===1){o=r;r=function(e){m().off(e);return o.apply(this,arguments)};r.guid=o.guid||(o.guid=m.guid++)}return e.each(function(){m.event.add(this,t,r,n,i)})}m.event={global:{},add:function(e,t,i,n,r){var a,o,s,l,u,f,c,d,h,p,y,v=m._data(e);if(!v){return}if(i.handler){l=i;i=l.handler;r=l.selector}if(!i.guid){i.guid=m.guid++}if(!(o=v.events)){o=v.events={}}if(!(f=v.handle)){f=v.handle=function(e){return typeof m!=="undefined"&&(!e||m.event.triggered!==e.type)?m.event.dispatch.apply(f.elem,arguments):undefined};f.elem=e}t=(t||"").match(I)||[""];s=t.length;while(s--){a=ge.exec(t[s])||[];h=y=a[1];p=(a[2]||"").split(".").sort();if(!h){continue}u=m.event.special[h]||{};h=(r?u.delegateType:u.bindType)||h;u=m.event.special[h]||{};c=m.extend({type:h,origType:y,data:n,handler:i,guid:i.guid,selector:r,needsContext:r&&m.expr.match.needsContext.test(r),namespace:p.join(".")},l);if(!(d=o[h])){d=o[h]=[];d.delegateCount=0;if(!u.setup||u.setup.call(e,n,p,f)===false){if(e.addEventListener){e.addEventListener(h,f,false)}else if(e.attachEvent){e.attachEvent("on"+h,f)}}}if(u.add){u.add.call(e,c);if(!c.handler.guid){c.handler.guid=i.guid}}if(r){d.splice(d.delegateCount++,0,c)}else{d.push(c)}m.event.global[h]=true}e=null},remove:function(e,t,i,n,r){var a,o,s,l,u,f,c,d,h,p,y,v=m.hasData(e)&&m._data(e);if(!v||!(f=v.events)){return}t=(t||"").match(I)||[""];u=t.length;while(u--){s=ge.exec(t[u])||[];h=y=s[1];p=(s[2]||"").split(".").sort();if(!h){for(h in f){m.event.remove(e,h+t[u],i,n,true)}continue}c=m.event.special[h]||{};h=(n?c.delegateType:c.bindType)||h;d=f[h]||[];s=s[2]&&new RegExp("(^|\\.)"+p.join("\\.(?:.*\\.|)")+"(\\.|$)");l=a=d.length;while(a--){o=d[a];if((r||y===o.origType)&&(!i||i.guid===o.guid)&&(!s||s.test(o.namespace))&&(!n||n===o.selector||n==="**"&&o.selector)){d.splice(a,1);if(o.selector){d.delegateCount--}if(c.remove){c.remove.call(e,o)}}}if(l&&!d.length){if(!c.teardown||c.teardown.call(e,p,v.handle)===false){m.removeEvent(e,h,v.handle)}delete f[h]}}if(m.isEmptyObject(f)){delete v.handle;m._removeData(e,"events")}},trigger:function(e,t,n,r){var a,o,l,u,f,c,d,h=[n||s],y=p.call(e,"type")?e.type:e,v=p.call(e,"namespace")?e.namespace.split("."):[];l=c=n=n||s;if(n.nodeType===3||n.nodeType===8){return}if(me.test(y+m.event.triggered)){return}if(y.indexOf(".")>-1){v=y.split(".");y=v.shift();v.sort()}o=y.indexOf(":")<0&&"on"+y;e=e[m.expando]?e:new m.Event(y,typeof e==="object"&&e);e.isTrigger=r?2:3;e.namespace=v.join(".");e.rnamespace=e.namespace?new RegExp("(^|\\.)"+v.join("\\.(?:.*\\.|)")+"(\\.|$)"):null;e.result=undefined;if(!e.target){e.target=n}t=t==null?[e]:m.makeArray(t,[e]);f=m.event.special[y]||{};if(!r&&f.trigger&&f.trigger.apply(n,t)===false){return}if(!r&&!f.noBubble&&!m.isWindow(n)){u=f.delegateType||y;if(!me.test(u+y)){l=l.parentNode}for(;l;l=l.parentNode){h.push(l);c=l}if(c===(n.ownerDocument||s)){h.push(c.defaultView||c.parentWindow||i)}}d=0;while((l=h[d++])&&!e.isPropagationStopped()){e.type=d>1?u:f.bindType||y;a=(m._data(l,"events")||{})[e.type]&&m._data(l,"handle");if(a){a.apply(l,t)}a=o&&l[o];if(a&&a.apply&&j(l)){e.result=a.apply(l,t);if(e.result===false){e.preventDefault()}}}e.type=y;if(!r&&!e.isDefaultPrevented()){if((!f._default||f._default.apply(h.pop(),t)===false)&&j(n)){if(o&&n[y]&&!m.isWindow(n)){c=n[o];if(c){n[o]=null}m.event.triggered=y;try{n[y]()}catch(e){}m.event.triggered=undefined;if(c){n[o]=c}}}}return e.result},dispatch:function(e){e=m.event.fix(e);var t,i,n,r,a,o=[],s=l.call(arguments),u=(m._data(this,"events")||{})[e.type]||[],f=m.event.special[e.type]||{};s[0]=e;e.delegateTarget=this;if(f.preDispatch&&f.preDispatch.call(this,e)===false){return}o=m.event.handlers.call(this,e,u);t=0;while((r=o[t++])&&!e.isPropagationStopped()){e.currentTarget=r.elem;i=0;while((a=r.handlers[i++])&&!e.isImmediatePropagationStopped()){if(!e.rnamespace||e.rnamespace.test(a.namespace)){e.handleObj=a;e.data=a.data;n=((m.event.special[a.origType]||{}).handle||a.handler).apply(r.elem,s);if(n!==undefined){if((e.result=n)===false){e.preventDefault();e.stopPropagation()}}}}}if(f.postDispatch){f.postDispatch.call(this,e)}return e.result},handlers:function(e,t){var i,n,r,a,o=[],s=t.delegateCount,l=e.target;if(s&&l.nodeType&&(e.type!=="click"||isNaN(e.button)||e.button<1)){for(;l!=this;l=l.parentNode||this){if(l.nodeType===1&&(l.disabled!==true||e.type!=="click")){n=[];for(i=0;i<s;i++){a=t[i];r=a.selector+" ";if(n[r]===undefined){n[r]=a.needsContext?m(r,this).index(l)>-1:m.find(r,this,null,[l]).length}if(n[r]){n.push(a)}}if(n.length){o.push({elem:l,handlers:n})}}}}if(s<t.length){o.push({elem:this,handlers:t.slice(s)})}return o},fix:function(e){if(e[m.expando]){return e}var t,i,n,r=e.type,a=e,o=this.fixHooks[r];if(!o){this.fixHooks[r]=o=ve.test(r)?this.mouseHooks:ye.test(r)?this.keyHooks:{}}n=o.props?this.props.concat(o.props):this.props;e=new m.Event(a);t=n.length;while(t--){i=n[t];e[i]=a[i]}if(!e.target){e.target=a.srcElement||s}if(e.target.nodeType===3){e.target=e.target.parentNode}e.metaKey=!!e.metaKey;return o.filter?o.filter(e,a):e},props:("altKey bubbles cancelable ctrlKey currentTarget detail eventPhase "+"metaKey relatedTarget shiftKey target timeStamp view which").split(" "),fixHooks:{},keyHooks:{props:"char charCode key keyCode".split(" "),filter:function(e,t){if(e.which==null){e.which=t.charCode!=null?t.charCode:t.keyCode}return e}},mouseHooks:{props:("button buttons clientX clientY fromElement offsetX offsetY "+"pageX pageY screenX screenY toElement").split(" "),filter:function(e,t){var i,n,r,a=t.button,o=t.fromElement;if(e.pageX==null&&t.clientX!=null){n=e.target.ownerDocument||s;r=n.documentElement;i=n.body;e.pageX=t.clientX+(r&&r.scrollLeft||i&&i.scrollLeft||0)-(r&&r.clientLeft||i&&i.clientLeft||0);e.pageY=t.clientY+(r&&r.scrollTop||i&&i.scrollTop||0)-(r&&r.clientTop||i&&i.clientTop||0)}if(!e.relatedTarget&&o){e.relatedTarget=o===e.target?t.toElement:o}if(!e.which&&a!==undefined){e.which=a&1?1:a&2?3:a&4?2:0}return e}},special:{load:{noBubble:true},focus:{trigger:function(){if(this!==we()&&this.focus){try{this.focus();return false}catch(e){}}},delegateType:"focusin"},blur:{trigger:function(){if(this===we()&&this.blur){this.blur();return false}},delegateType:"focusout"},click:{trigger:function(){if(m.nodeName(this,"input")&&this.type==="checkbox"&&this.click){this.click();return false}},_default:function(e){return m.nodeName(e.target,"a")}},beforeunload:{postDispatch:function(e){if(e.result!==undefined&&e.originalEvent){e.originalEvent.returnValue=e.result}}}},simulate:function(e,t,i){var n=m.extend(new m.Event,i,{type:e,isSimulated:true});m.event.trigger(n,null,t);if(n.isDefaultPrevented()){i.preventDefault()}}};m.removeEvent=s.removeEventListener?function(e,t,i){if(e.removeEventListener){e.removeEventListener(t,i)}}:function(e,t,i){var n="on"+t;if(e.detachEvent){if(typeof e[n]==="undefined"){e[n]=null}e.detachEvent(n,i)}};m.Event=function(e,t){if(!(this instanceof m.Event)){return new m.Event(e,t)}if(e&&e.type){this.originalEvent=e;this.type=e.type;this.isDefaultPrevented=e.defaultPrevented||e.defaultPrevented===undefined&&e.returnValue===false?be:Se}else{this.type=e}if(t){m.extend(this,t)}this.timeStamp=e&&e.timeStamp||m.now();this[m.expando]=true};m.Event.prototype={constructor:m.Event,isDefaultPrevented:Se,isPropagationStopped:Se,isImmediatePropagationStopped:Se,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=be;if(!e){return}if(e.preventDefault){e.preventDefault()}else{e.returnValue=false}},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=be;if(!e||this.isSimulated){return}if(e.stopPropagation){e.stopPropagation()}e.cancelBubble=true},stopImmediatePropagation:function(){var e=this.originalEvent;this.isImmediatePropagationStopped=be;if(e&&e.stopImmediatePropagation){e.stopImmediatePropagation()}this.stopPropagation()}};m.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(e,t){m.event.special[e]={delegateType:t,bindType:t,handle:function(e){var i,n=this,r=e.relatedTarget,a=e.handleObj;if(!r||r!==n&&!m.contains(n,r)){e.type=a.origType;i=a.handler.apply(this,arguments);e.type=t}return i}}});if(!y.submit){m.event.special.submit={setup:function(){if(m.nodeName(this,"form")){return false}m.event.add(this,"click._submit keypress._submit",function(e){var t=e.target,i=m.nodeName(t,"input")||m.nodeName(t,"button")?m.prop(t,"form"):undefined;if(i&&!m._data(i,"submit")){m.event.add(i,"submit._submit",function(e){e._submitBubble=true});m._data(i,"submit",true)}})},postDispatch:function(e){if(e._submitBubble){delete e._submitBubble;if(this.parentNode&&!e.isTrigger){m.event.simulate("submit",this.parentNode,e)}}},teardown:function(){if(m.nodeName(this,"form")){return false}m.event.remove(this,"._submit")}}}if(!y.change){m.event.special.change={setup:function(){if(pe.test(this.nodeName)){if(this.type==="checkbox"||this.type==="radio"){m.event.add(this,"propertychange._change",function(e){if(e.originalEvent.propertyName==="checked"){this._justChanged=true}});m.event.add(this,"click._change",function(e){if(this._justChanged&&!e.isTrigger){this._justChanged=false}m.event.simulate("change",this,e)})}return false}m.event.add(this,"beforeactivate._change",function(e){var t=e.target;if(pe.test(t.nodeName)&&!m._data(t,"change")){m.event.add(t,"change._change",function(e){if(this.parentNode&&!e.isSimulated&&!e.isTrigger){m.event.simulate("change",this.parentNode,e)}});m._data(t,"change",true)}})},handle:function(e){var t=e.target;if(this!==t||e.isSimulated||e.isTrigger||t.type!=="radio"&&t.type!=="checkbox"){return e.handleObj.handler.apply(this,arguments)}},teardown:function(){m.event.remove(this,"._change");return!pe.test(this.nodeName)}}}if(!y.focusin){m.each({focus:"focusin",blur:"focusout"},function(e,t){var i=function(e){m.event.simulate(t,e.target,m.event.fix(e))};m.event.special[t]={setup:function(){var n=this.ownerDocument||this,r=m._data(n,t);if(!r){n.addEventListener(e,i,true)}m._data(n,t,(r||0)+1)},teardown:function(){var n=this.ownerDocument||this,r=m._data(n,t)-1;if(!r){n.removeEventListener(e,i,true);m._removeData(n,t)}else{m._data(n,t,r)}}}})}m.fn.extend({on:function(e,t,i,n){return Pe(this,e,t,i,n)},one:function(e,t,i,n){return Pe(this,e,t,i,n,1)},off:function(e,t,i){var n,r;if(e&&e.preventDefault&&e.handleObj){n=e.handleObj;m(e.delegateTarget).off(n.namespace?n.origType+"."+n.namespace:n.origType,n.selector,n.handler);return this}if(typeof e==="object"){for(r in e){this.off(r,t,e[r])}return this}if(t===false||typeof t==="function"){i=t;t=undefined}if(i===false){i=Se}return this.each(function(){m.event.remove(this,e,i,t)})},trigger:function(e,t){return this.each(function(){m.event.trigger(e,t,this)})},triggerHandler:function(e,t){var i=this[0];if(i){return m.event.trigger(e,t,i,true)}}});var Ce=/ jQuery\d+="(?:null|\d+)"/g,_e=new RegExp("<(?:"+ae+")[\\s/>]","i"),ke=/<(?!area|br|col|embed|hr|img|input|link|meta|param)(([\w:-]+)[^>]*)\/>/gi,De=/<script|<style|<link/i,xe=/checked\s*(?:[^=]|=\s*.checked.)/i,Te=/^true\/(.*)/,Me=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g,Le=oe(s),We=Le.appendChild(s.createElement("div"));function Ee(e,t){return m.nodeName(e,"table")&&m.nodeName(t.nodeType!==11?t:t.firstChild,"tr")?e.getElementsByTagName("tbody")[0]||e.appendChild(e.ownerDocument.createElement("tbody")):e}function Ae(e){e.type=(m.find.attr(e,"type")!==null)+"/"+e.type;return e}function Re(e){var t=Te.exec(e.type);if(t){e.type=t[1]}else{e.removeAttribute("type")}return e}function Be(e,t){if(t.nodeType!==1||!m.hasData(e)){return}var i,n,r,a=m._data(e),o=m._data(t,a),s=a.events;if(s){delete o.handle;o.events={};for(i in s){for(n=0,r=s[i].length;n<r;n++){m.event.add(t,i,s[i][n])}}}if(o.data){o.data=m.extend({},o.data)}}function Ie(e,t){var i,n,r;if(t.nodeType!==1){return}i=t.nodeName.toLowerCase();if(!y.noCloneEvent&&t[m.expando]){r=m._data(t);for(n in r.events){m.removeEvent(t,n,r.handle)}t.removeAttribute(m.expando)}if(i==="script"&&t.text!==e.text){Ae(t).text=e.text;Re(t)}else if(i==="object"){if(t.parentNode){t.outerHTML=e.outerHTML}if(y.html5Clone&&(e.innerHTML&&!m.trim(t.innerHTML))){t.innerHTML=e.innerHTML}}else if(i==="input"&&te.test(e.type)){t.defaultChecked=t.checked=e.checked;if(t.value!==e.value){t.value=e.value}}else if(i==="option"){t.defaultSelected=t.selected=e.defaultSelected}else if(i==="input"||i==="textarea"){t.defaultValue=e.defaultValue}}function ze(e,t,i,n){t=u.apply([],t);var r,a,o,s,l,f,c=0,d=e.length,h=d-1,p=t[0],v=m.isFunction(p);if(v||d>1&&typeof p==="string"&&!y.checkClone&&xe.test(p)){return e.each(function(r){var a=e.eq(r);if(v){t[0]=p.call(this,r,a.html())}ze(a,t,i,n)})}if(d){f=he(t,e[0].ownerDocument,false,e,n);r=f.firstChild;if(f.childNodes.length===1){f=r}if(r||n){s=m.map(le(f,"script"),Ae);o=s.length;for(;c<d;c++){a=f;if(c!==h){a=m.clone(a,true,true);if(o){m.merge(s,le(a,"script"))}}i.call(e[c],a,c)}if(o){l=s[s.length-1].ownerDocument;m.map(s,Re);for(c=0;c<o;c++){a=s[c];if(ne.test(a.type||"")&&!m._data(a,"globalEval")&&m.contains(l,a)){if(a.src){if(m._evalUrl){m._evalUrl(a.src)}}else{m.globalEval((a.text||a.textContent||a.innerHTML||"").replace(Me,""))}}}}f=r=null}}return e}function Oe(e,t,i){var n,r=t?m.filter(t,e):e,a=0;for(;(n=r[a])!=null;a++){if(!i&&n.nodeType===1){m.cleanData(le(n))}if(n.parentNode){if(i&&m.contains(n.ownerDocument,n)){ue(le(n,"script"))}n.parentNode.removeChild(n)}}return e}m.extend({htmlPrefilter:function(e){return e.replace(ke,"<$1></$2>")},clone:function(e,t,i){var n,r,a,o,s,l=m.contains(e.ownerDocument,e);if(y.html5Clone||m.isXMLDoc(e)||!_e.test("<"+e.nodeName+">")){a=e.cloneNode(true)}else{We.innerHTML=e.outerHTML;We.removeChild(a=We.firstChild)}if((!y.noCloneEvent||!y.noCloneChecked)&&(e.nodeType===1||e.nodeType===11)&&!m.isXMLDoc(e)){n=le(a);s=le(e);for(o=0;(r=s[o])!=null;++o){if(n[o]){Ie(r,n[o])}}}if(t){if(i){s=s||le(e);n=n||le(a);for(o=0;(r=s[o])!=null;o++){Be(r,n[o])}}else{Be(e,a)}}n=le(a,"script");if(n.length>0){ue(n,!l&&le(e,"script"))}n=s=r=null;return a},cleanData:function(e,t){var i,n,r,a,s=0,l=m.expando,u=m.cache,f=y.attributes,c=m.event.special;for(;(i=e[s])!=null;s++){if(t||j(i)){r=i[l];a=r&&u[r];if(a){if(a.events){for(n in a.events){if(c[n]){m.event.remove(i,n)}else{m.removeEvent(i,n,a.handle)}}}if(u[r]){delete u[r];if(!f&&typeof i.removeAttribute!=="undefined"){i.removeAttribute(l)}else{i[l]=undefined}o.push(r)}}}}}});m.fn.extend({domManip:ze,detach:function(e){return Oe(this,e,true)},remove:function(e){return Oe(this,e)},text:function(e){return ee(this,function(e){return e===undefined?m.text(this):this.empty().append((this[0]&&this[0].ownerDocument||s).createTextNode(e))},null,e,arguments.length)},append:function(){return ze(this,arguments,function(e){if(this.nodeType===1||this.nodeType===11||this.nodeType===9){var t=Ee(this,e);t.appendChild(e)}})},prepend:function(){return ze(this,arguments,function(e){if(this.nodeType===1||this.nodeType===11||this.nodeType===9){var t=Ee(this,e);t.insertBefore(e,t.firstChild)}})},before:function(){return ze(this,arguments,function(e){if(this.parentNode){this.parentNode.insertBefore(e,this)}})},after:function(){return ze(this,arguments,function(e){if(this.parentNode){this.parentNode.insertBefore(e,this.nextSibling)}})},empty:function(){var e,t=0;for(;(e=this[t])!=null;t++){if(e.nodeType===1){m.cleanData(le(e,false))}while(e.firstChild){e.removeChild(e.firstChild)}if(e.options&&m.nodeName(e,"select")){e.options.length=0}}return this},clone:function(e,t){e=e==null?false:e;t=t==null?e:t;return this.map(function(){return m.clone(this,e,t)})},html:function(e){return ee(this,function(e){var t=this[0]||{},i=0,n=this.length;if(e===undefined){return t.nodeType===1?t.innerHTML.replace(Ce,""):undefined}if(typeof e==="string"&&!De.test(e)&&(y.htmlSerialize||!_e.test(e))&&(y.leadingWhitespace||!re.test(e))&&!se[(ie.exec(e)||["",""])[1].toLowerCase()]){e=m.htmlPrefilter(e);try{for(;i<n;i++){t=this[i]||{};if(t.nodeType===1){m.cleanData(le(t,false));t.innerHTML=e}}t=0}catch(e){}}if(t){this.empty().append(e)}},null,e,arguments.length)},replaceWith:function(){var e=[];return ze(this,arguments,function(t){var i=this.parentNode;if(m.inArray(this,e)<0){m.cleanData(le(this));if(i){i.replaceChild(t,this)}}},e)}});m.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(e,t){m.fn[e]=function(e){var i,n=0,r=[],a=m(e),o=a.length-1;for(;n<=o;n++){i=n===o?this:this.clone(true);m(a[n])[t](i);f.apply(r,i.get())}return this.pushStack(r)}});var Fe,Ne={HTML:"block",BODY:"block"};function Ue(e,t){var i=m(t.createElement(e)).appendTo(t.body),n=m.css(i[0],"display");i.detach();return n}function je(e){var t=s,i=Ne[e];if(!i){i=Ue(e,t);if(i==="none"||!i){Fe=(Fe||m("<iframe frameborder='0' width='0' height='0'/>")).appendTo(t.documentElement);t=(Fe[0].contentWindow||Fe[0].contentDocument).document;t.write();t.close();i=Ue(e,t);Fe.detach()}Ne[e]=i}return i}var He=/^margin/;var qe=new RegExp("^("+X+")(?!px)[a-z%]+$","i");var Ve=function(e,t,i,n){var r,a,o={};for(a in t){o[a]=e.style[a];e.style[a]=t[a]}r=i.apply(e,n||[]);for(a in t){e.style[a]=o[a]}return r};var Je=s.documentElement;(function(){var e,t,n,r,a,o,l=s.createElement("div"),u=s.createElement("div");if(!u.style){return}u.style.cssText="float:left;opacity:.5";y.opacity=u.style.opacity==="0.5";y.cssFloat=!!u.style.cssFloat;u.style.backgroundClip="content-box";u.cloneNode(true).style.backgroundClip="";y.clearCloneStyle=u.style.backgroundClip==="content-box";l=s.createElement("div");l.style.cssText="border:0;width:8px;height:0;top:0;left:-9999px;"+"padding:0;margin-top:1px;position:absolute";u.innerHTML="";l.appendChild(u);y.boxSizing=u.style.boxSizing===""||u.style.MozBoxSizing===""||u.style.WebkitBoxSizing==="";m.extend(y,{reliableHiddenOffsets:function(){if(e==null){f()}return r},boxSizingReliable:function(){if(e==null){f()}return n},pixelMarginRight:function(){if(e==null){f()}return t},pixelPosition:function(){if(e==null){f()}return e},reliableMarginRight:function(){if(e==null){f()}return a},reliableMarginLeft:function(){if(e==null){f()}return o}});function f(){var f,c,d=s.documentElement;d.appendChild(l);u.style.cssText="-webkit-box-sizing:border-box;box-sizing:border-box;"+"position:relative;display:block;"+"margin:auto;border:1px;padding:1px;"+"top:1%;width:50%";e=n=o=false;t=a=true;if(i.getComputedStyle){c=i.getComputedStyle(u);e=(c||{}).top!=="1%";o=(c||{}).marginLeft==="2px";n=(c||{width:"4px"}).width==="4px";u.style.marginRight="50%";t=(c||{marginRight:"4px"}).marginRight==="4px";f=u.appendChild(s.createElement("div"));f.style.cssText=u.style.cssText="-webkit-box-sizing:content-box;-moz-box-sizing:content-box;"+"box-sizing:content-box;display:block;margin:0;border:0;padding:0";f.style.marginRight=f.style.width="0";u.style.width="1px";a=!parseFloat((i.getComputedStyle(f)||{}).marginRight);u.removeChild(f)}u.style.display="none";r=u.getClientRects().length===0;if(r){u.style.display="";u.innerHTML="<table><tr><td></td><td>t</td></tr></table>";f=u.getElementsByTagName("td");f[0].style.cssText="margin:0;border:0;padding:0;display:none";r=f[0].offsetHeight===0;if(r){f[0].style.display="";f[1].style.display="none";r=f[0].offsetHeight===0}}d.removeChild(l)}})();var Ge,Ye,Xe=/^(top|right|bottom|left)$/;if(i.getComputedStyle){Ge=function(e){var t=e.ownerDocument.defaultView;if(!t||!t.opener){t=i}return t.getComputedStyle(e)};Ye=function(e,t,i){var n,r,a,o,s=e.style;i=i||Ge(e);o=i?i.getPropertyValue(t)||i[t]:undefined;if((o===""||o===undefined)&&!m.contains(e.ownerDocument,e)){o=m.style(e,t)}if(i){if(!y.pixelMarginRight()&&qe.test(o)&&He.test(t)){n=s.width;r=s.minWidth;a=s.maxWidth;s.minWidth=s.maxWidth=s.width=o;o=i.width;s.width=n;s.minWidth=r;s.maxWidth=a}}return o===undefined?o:o+""}}else if(Je.currentStyle){Ge=function(e){return e.currentStyle};Ye=function(e,t,i){var n,r,a,o,s=e.style;i=i||Ge(e);o=i?i[t]:undefined;if(o==null&&s&&s[t]){o=s[t]}if(qe.test(o)&&!Xe.test(t)){n=s.left;r=e.runtimeStyle;a=r&&r.left;if(a){r.left=e.currentStyle.left}s.left=t==="fontSize"?"1em":o;o=s.pixelLeft+"px";s.left=n;if(a){r.left=a}}return o===undefined?o:o+""||"auto"}}function Ke(e,t){return{get:function(){if(e()){delete this.get;return}return(this.get=t).apply(this,arguments)}}}var $e=/alpha\([^)]*\)/i,Ze=/opacity\s*=\s*([^)]*)/i,Qe=/^(none|table(?!-c[ea]).+)/,et=new RegExp("^("+X+")(.*)$","i"),tt={position:"absolute",visibility:"hidden",display:"block"},it={letterSpacing:"0",fontWeight:"400"},nt=["Webkit","O","Moz","ms"],rt=s.createElement("div").style;function at(e){if(e in rt){return e}var t=e.charAt(0).toUpperCase()+e.slice(1),i=nt.length;while(i--){e=nt[i]+t;if(e in rt){return e}}}function ot(e,t){var i,n,r,a=[],o=0,s=e.length;for(;o<s;o++){n=e[o];if(!n.style){continue}a[o]=m._data(n,"olddisplay");i=n.style.display;if(t){if(!a[o]&&i==="none"){n.style.display=""}if(n.style.display===""&&Z(n)){a[o]=m._data(n,"olddisplay",je(n.nodeName))}}else{r=Z(n);if(i&&i!=="none"||!r){m._data(n,"olddisplay",r?i:m.css(n,"display"))}}}for(o=0;o<s;o++){n=e[o];if(!n.style){continue}if(!t||n.style.display==="none"||n.style.display===""){n.style.display=t?a[o]||"":"none"}}return e}function st(e,t,i){var n=et.exec(t);return n?Math.max(0,n[1]-(i||0))+(n[2]||"px"):t}function lt(e,t,i,n,r){var a=i===(n?"border":"content")?4:t==="width"?1:0,o=0;for(;a<4;a+=2){if(i==="margin"){o+=m.css(e,i+$[a],true,r)}if(n){if(i==="content"){o-=m.css(e,"padding"+$[a],true,r)}if(i!=="margin"){o-=m.css(e,"border"+$[a]+"Width",true,r)}}else{o+=m.css(e,"padding"+$[a],true,r);if(i!=="padding"){o+=m.css(e,"border"+$[a]+"Width",true,r)}}}return o}function ut(e,t,n){var r=true,a=t==="width"?e.offsetWidth:e.offsetHeight,o=Ge(e),l=y.boxSizing&&m.css(e,"boxSizing",false,o)==="border-box";if(s.msFullscreenElement&&i.top!==i){if(e.getClientRects().length){a=Math.round(e.getBoundingClientRect()[t]*100)}}if(a<=0||a==null){a=Ye(e,t,o);if(a<0||a==null){a=e.style[t]}if(qe.test(a)){return a}r=l&&(y.boxSizingReliable()||a===e.style[t]);a=parseFloat(a)||0}return a+lt(e,t,n||(l?"border":"content"),r,o)+"px"}m.extend({cssHooks:{opacity:{get:function(e,t){if(t){var i=Ye(e,"opacity");return i===""?"1":i}}}},cssNumber:{animationIterationCount:true,columnCount:true,fillOpacity:true,flexGrow:true,flexShrink:true,fontWeight:true,lineHeight:true,opacity:true,order:true,orphans:true,widows:true,zIndex:true,zoom:true},cssProps:{float:y.cssFloat?"cssFloat":"styleFloat"},style:function(e,t,i,n){if(!e||e.nodeType===3||e.nodeType===8||!e.style){return}var r,a,o,s=m.camelCase(t),l=e.style;t=m.cssProps[s]||(m.cssProps[s]=at(s)||s);o=m.cssHooks[t]||m.cssHooks[s];if(i!==undefined){a=typeof i;if(a==="string"&&(r=K.exec(i))&&r[1]){i=Q(e,t,r);a="number"}if(i==null||i!==i){return}if(a==="number"){i+=r&&r[3]||(m.cssNumber[s]?"":"px")}if(!y.clearCloneStyle&&i===""&&t.indexOf("background")===0){l[t]="inherit"}if(!o||!("set"in o)||(i=o.set(e,i,n))!==undefined){try{l[t]=i}catch(e){}}}else{if(o&&"get"in o&&(r=o.get(e,false,n))!==undefined){return r}return l[t]}},css:function(e,t,i,n){var r,a,o,s=m.camelCase(t);t=m.cssProps[s]||(m.cssProps[s]=at(s)||s);o=m.cssHooks[t]||m.cssHooks[s];if(o&&"get"in o){a=o.get(e,true,i)}if(a===undefined){a=Ye(e,t,n)}if(a==="normal"&&t in it){a=it[t]}if(i===""||i){r=parseFloat(a);return i===true||isFinite(r)?r||0:a}return a}});m.each(["height","width"],function(e,t){m.cssHooks[t]={get:function(e,i,n){if(i){return Qe.test(m.css(e,"display"))&&e.offsetWidth===0?Ve(e,tt,function(){return ut(e,t,n)}):ut(e,t,n)}},set:function(e,i,n){var r=n&&Ge(e);return st(e,i,n?lt(e,t,n,y.boxSizing&&m.css(e,"boxSizing",false,r)==="border-box",r):0)}}});if(!y.opacity){m.cssHooks.opacity={get:function(e,t){return Ze.test((t&&e.currentStyle?e.currentStyle.filter:e.style.filter)||"")?.01*parseFloat(RegExp.$1)+"":t?"1":""},set:function(e,t){var i=e.style,n=e.currentStyle,r=m.isNumeric(t)?"alpha(opacity="+t*100+")":"",a=n&&n.filter||i.filter||"";i.zoom=1;if((t>=1||t==="")&&m.trim(a.replace($e,""))===""&&i.removeAttribute){i.removeAttribute("filter");if(t===""||n&&!n.filter){return}}i.filter=$e.test(a)?a.replace($e,r):a+" "+r}}}m.cssHooks.marginRight=Ke(y.reliableMarginRight,function(e,t){if(t){return Ve(e,{display:"inline-block"},Ye,[e,"marginRight"])}});m.cssHooks.marginLeft=Ke(y.reliableMarginLeft,function(e,t){if(t){return(parseFloat(Ye(e,"marginLeft"))||(m.contains(e.ownerDocument,e)?e.getBoundingClientRect().left-Ve(e,{marginLeft:0},function(){return e.getBoundingClientRect().left}):0))+"px"}});m.each({margin:"",padding:"",border:"Width"},function(e,t){m.cssHooks[e+t]={expand:function(i){var n=0,r={},a=typeof i==="string"?i.split(" "):[i];for(;n<4;n++){r[e+$[n]+t]=a[n]||a[n-2]||a[0]}return r}};if(!He.test(e)){m.cssHooks[e+t].set=st}});m.fn.extend({css:function(e,t){return ee(this,function(e,t,i){var n,r,a={},o=0;if(m.isArray(t)){n=Ge(e);r=t.length;for(;o<r;o++){a[t[o]]=m.css(e,t[o],false,n)}return a}return i!==undefined?m.style(e,t,i):m.css(e,t)},e,t,arguments.length>1)},show:function(){return ot(this,true)},hide:function(){return ot(this)},toggle:function(e){if(typeof e==="boolean"){return e?this.show():this.hide()}return this.each(function(){if(Z(this)){m(this).show()}else{m(this).hide()}})}});function ft(e,t,i,n,r){return new ft.prototype.init(e,t,i,n,r)}m.Tween=ft;ft.prototype={constructor:ft,init:function(e,t,i,n,r,a){this.elem=e;this.prop=i;this.easing=r||m.easing._default;this.options=t;this.start=this.now=this.cur();this.end=n;this.unit=a||(m.cssNumber[i]?"":"px")},cur:function(){var e=ft.propHooks[this.prop];return e&&e.get?e.get(this):ft.propHooks._default.get(this)},run:function(e){var t,i=ft.propHooks[this.prop];if(this.options.duration){this.pos=t=m.easing[this.easing](e,this.options.duration*e,0,1,this.options.duration)}else{this.pos=t=e}this.now=(this.end-this.start)*t+this.start;if(this.options.step){this.options.step.call(this.elem,this.now,this)}if(i&&i.set){i.set(this)}else{ft.propHooks._default.set(this)}return this}};ft.prototype.init.prototype=ft.prototype;ft.propHooks={_default:{get:function(e){var t;if(e.elem.nodeType!==1||e.elem[e.prop]!=null&&e.elem.style[e.prop]==null){return e.elem[e.prop]}t=m.css(e.elem,e.prop,"");return!t||t==="auto"?0:t},set:function(e){if(m.fx.step[e.prop]){m.fx.step[e.prop](e)}else if(e.elem.nodeType===1&&(e.elem.style[m.cssProps[e.prop]]!=null||m.cssHooks[e.prop])){m.style(e.elem,e.prop,e.now+e.unit)}else{e.elem[e.prop]=e.now}}}};ft.propHooks.scrollTop=ft.propHooks.scrollLeft={set:function(e){if(e.elem.nodeType&&e.elem.parentNode){e.elem[e.prop]=e.now}}};m.easing={linear:function(e){return e},swing:function(e){return.5-Math.cos(e*Math.PI)/2},_default:"swing"};m.fx=ft.prototype.init;m.fx.step={};var ct,dt,ht=/^(?:toggle|show|hide)$/,pt=/queueHooks$/;function yt(){i.setTimeout(function(){ct=undefined});return ct=m.now()}function vt(e,t){var i,n={height:e},r=0;t=t?1:0;for(;r<4;r+=2-t){i=$[r];n["margin"+i]=n["padding"+i]=e}if(t){n.opacity=n.width=e}return n}function mt(e,t,i){var n,r=(St.tweeners[t]||[]).concat(St.tweeners["*"]),a=0,o=r.length;for(;a<o;a++){if(n=r[a].call(i,t,e)){return n}}}function gt(e,t,i){var n,r,a,o,s,l,u,f,c=this,d={},h=e.style,p=e.nodeType&&Z(e),v=m._data(e,"fxshow");if(!i.queue){s=m._queueHooks(e,"fx");if(s.unqueued==null){s.unqueued=0;l=s.empty.fire;s.empty.fire=function(){if(!s.unqueued){l()}}}s.unqueued++;c.always(function(){c.always(function(){s.unqueued--;if(!m.queue(e,"fx").length){s.empty.fire()}})})}if(e.nodeType===1&&("height"in t||"width"in t)){i.overflow=[h.overflow,h.overflowX,h.overflowY];u=m.css(e,"display");f=u==="none"?m._data(e,"olddisplay")||je(e.nodeName):u;if(f==="inline"&&m.css(e,"float")==="none"){if(!y.inlineBlockNeedsLayout||je(e.nodeName)==="inline"){h.display="inline-block"}else{h.zoom=1}}}if(i.overflow){h.overflow="hidden";if(!y.shrinkWrapBlocks()){c.always(function(){h.overflow=i.overflow[0];h.overflowX=i.overflow[1];h.overflowY=i.overflow[2]})}}for(n in t){r=t[n];if(ht.exec(r)){delete t[n];a=a||r==="toggle";if(r===(p?"hide":"show")){if(r==="show"&&v&&v[n]!==undefined){p=true}else{continue}}d[n]=v&&v[n]||m.style(e,n)}else{u=undefined}}if(!m.isEmptyObject(d)){if(v){if("hidden"in v){p=v.hidden}}else{v=m._data(e,"fxshow",{})}if(a){v.hidden=!p}if(p){m(e).show()}else{c.done(function(){m(e).hide()})}c.done(function(){var t;m._removeData(e,"fxshow");for(t in d){m.style(e,t,d[t])}});for(n in d){o=mt(p?v[n]:0,n,c);if(!(n in v)){v[n]=o.start;if(p){o.end=o.start;o.start=n==="width"||n==="height"?1:0}}}}else if((u==="none"?je(e.nodeName):u)==="inline"){h.display=u}}function bt(e,t){var i,n,r,a,o;for(i in e){n=m.camelCase(i);r=t[n];a=e[i];if(m.isArray(a)){r=a[1];a=e[i]=a[0]}if(i!==n){e[n]=a;delete e[i]}o=m.cssHooks[n];if(o&&"expand"in o){a=o.expand(a);delete e[n];for(i in a){if(!(i in e)){e[i]=a[i];t[i]=r}}}else{t[n]=r}}}function St(e,t,i){var n,r,a=0,o=St.prefilters.length,s=m.Deferred().always(function(){delete l.elem}),l=function(){if(r){return false}var t=ct||yt(),i=Math.max(0,u.startTime+u.duration-t),n=i/u.duration||0,a=1-n,o=0,l=u.tweens.length;for(;o<l;o++){u.tweens[o].run(a)}s.notifyWith(e,[u,a,i]);if(a<1&&l){return i}else{s.resolveWith(e,[u]);return false}},u=s.promise({elem:e,props:m.extend({},t),opts:m.extend(true,{specialEasing:{},easing:m.easing._default},i),originalProperties:t,originalOptions:i,startTime:ct||yt(),duration:i.duration,tweens:[],createTween:function(t,i){var n=m.Tween(e,u.opts,t,i,u.opts.specialEasing[t]||u.opts.easing);u.tweens.push(n);return n},stop:function(t){var i=0,n=t?u.tweens.length:0;if(r){return this}r=true;for(;i<n;i++){u.tweens[i].run(1)}if(t){s.notifyWith(e,[u,1,0]);s.resolveWith(e,[u,t])}else{s.rejectWith(e,[u,t])}return this}}),f=u.props;bt(f,u.opts.specialEasing);for(;a<o;a++){n=St.prefilters[a].call(u,e,f,u.opts);if(n){if(m.isFunction(n.stop)){m._queueHooks(u.elem,u.opts.queue).stop=m.proxy(n.stop,n)}return n}}m.map(f,mt,u);if(m.isFunction(u.opts.start)){u.opts.start.call(e,u)}m.fx.timer(m.extend(l,{elem:e,anim:u,queue:u.opts.queue}));return u.progress(u.opts.progress).done(u.opts.done,u.opts.complete).fail(u.opts.fail).always(u.opts.always)}m.Animation=m.extend(St,{tweeners:{"*":[function(e,t){var i=this.createTween(e,t);Q(i.elem,e,K.exec(t),i);return i}]},tweener:function(e,t){if(m.isFunction(e)){t=e;e=["*"]}else{e=e.match(I)}var i,n=0,r=e.length;for(;n<r;n++){i=e[n];St.tweeners[i]=St.tweeners[i]||[];St.tweeners[i].unshift(t)}},prefilters:[gt],prefilter:function(e,t){if(t){St.prefilters.unshift(e)}else{St.prefilters.push(e)}}});m.speed=function(e,t,i){var n=e&&typeof e==="object"?m.extend({},e):{complete:i||!i&&t||m.isFunction(e)&&e,duration:e,easing:i&&t||t&&!m.isFunction(t)&&t};n.duration=m.fx.off?0:typeof n.duration==="number"?n.duration:n.duration in m.fx.speeds?m.fx.speeds[n.duration]:m.fx.speeds._default;if(n.queue==null||n.queue===true){n.queue="fx"}n.old=n.complete;n.complete=function(){if(m.isFunction(n.old)){n.old.call(this)}if(n.queue){m.dequeue(this,n.queue)}};return n};m.fn.extend({fadeTo:function(e,t,i,n){return this.filter(Z).css("opacity",0).show().end().animate({opacity:t},e,i,n)},animate:function(e,t,i,n){var r=m.isEmptyObject(e),a=m.speed(t,i,n),o=function(){var t=St(this,m.extend({},e),a);if(r||m._data(this,"finish")){t.stop(true)}};o.finish=o;return r||a.queue===false?this.each(o):this.queue(a.queue,o)},stop:function(e,t,i){var n=function(e){var t=e.stop;delete e.stop;t(i)};if(typeof e!=="string"){i=t;t=e;e=undefined}if(t&&e!==false){this.queue(e||"fx",[])}return this.each(function(){var t=true,r=e!=null&&e+"queueHooks",a=m.timers,o=m._data(this);if(r){if(o[r]&&o[r].stop){n(o[r])}}else{for(r in o){if(o[r]&&o[r].stop&&pt.test(r)){n(o[r])}}}for(r=a.length;r--;){if(a[r].elem===this&&(e==null||a[r].queue===e)){a[r].anim.stop(i);t=false;a.splice(r,1)}}if(t||!i){m.dequeue(this,e)}})},finish:function(e){if(e!==false){e=e||"fx"}return this.each(function(){var t,i=m._data(this),n=i[e+"queue"],r=i[e+"queueHooks"],a=m.timers,o=n?n.length:0;i.finish=true;m.queue(this,e,[]);if(r&&r.stop){r.stop.call(this,true)}for(t=a.length;t--;){if(a[t].elem===this&&a[t].queue===e){a[t].anim.stop(true);a.splice(t,1)}}for(t=0;t<o;t++){if(n[t]&&n[t].finish){n[t].finish.call(this)}}delete i.finish})}});m.each(["toggle","show","hide"],function(e,t){var i=m.fn[t];m.fn[t]=function(e,n,r){return e==null||typeof e==="boolean"?i.apply(this,arguments):this.animate(vt(t,true),e,n,r)}});m.each({slideDown:vt("show"),slideUp:vt("hide"),slideToggle:vt("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(e,t){m.fn[e]=function(e,i,n){return this.animate(t,e,i,n)}});m.timers=[];m.fx.tick=function(){var e,t=m.timers,i=0;ct=m.now();for(;i<t.length;i++){e=t[i];if(!e()&&t[i]===e){t.splice(i--,1)}}if(!t.length){m.fx.stop()}ct=undefined};m.fx.timer=function(e){m.timers.push(e);if(e()){m.fx.start()}else{m.timers.pop()}};m.fx.interval=13;m.fx.start=function(){if(!dt){dt=i.setInterval(m.fx.tick,m.fx.interval)}};m.fx.stop=function(){i.clearInterval(dt);dt=null};m.fx.speeds={slow:600,fast:200,_default:400};m.fn.delay=function(e,t){e=m.fx?m.fx.speeds[e]||e:e;t=t||"fx";return this.queue(t,function(t,n){var r=i.setTimeout(t,e);n.stop=function(){i.clearTimeout(r)}})};(function(){var e,t=s.createElement("input"),i=s.createElement("div"),n=s.createElement("select"),r=n.appendChild(s.createElement("option"));i=s.createElement("div");i.setAttribute("className","t");i.innerHTML="  <link/><table></table><a href='/a'>a</a><input type='checkbox'/>";e=i.getElementsByTagName("a")[0];t.setAttribute("type","checkbox");i.appendChild(t);e=i.getElementsByTagName("a")[0];e.style.cssText="top:1px";y.getSetAttribute=i.className!=="t";y.style=/top/.test(e.getAttribute("style"));y.hrefNormalized=e.getAttribute("href")==="/a";y.checkOn=!!t.value;y.optSelected=r.selected;y.enctype=!!s.createElement("form").enctype;n.disabled=true;y.optDisabled=!r.disabled;t=s.createElement("input");t.setAttribute("value","");y.input=t.getAttribute("value")==="";t.value="t";t.setAttribute("type","radio");y.radioValue=t.value==="t"})();var wt=/\r/g;m.fn.extend({val:function(e){var t,i,n,r=this[0];if(!arguments.length){if(r){t=m.valHooks[r.type]||m.valHooks[r.nodeName.toLowerCase()];if(t&&"get"in t&&(i=t.get(r,"value"))!==undefined){return i}i=r.value;return typeof i==="string"?i.replace(wt,""):i==null?"":i}return}n=m.isFunction(e);return this.each(function(i){var r;if(this.nodeType!==1){return}if(n){r=e.call(this,i,m(this).val())}else{r=e}if(r==null){r=""}else if(typeof r==="number"){r+=""}else if(m.isArray(r)){r=m.map(r,function(e){return e==null?"":e+""})}t=m.valHooks[this.type]||m.valHooks[this.nodeName.toLowerCase()];if(!t||!("set"in t)||t.set(this,r,"value")===undefined){this.value=r}})}});m.extend({valHooks:{option:{get:function(e){var t=m.find.attr(e,"value");return t!=null?t:m.trim(m.text(e))}},select:{get:function(e){var t,i,n=e.options,r=e.selectedIndex,a=e.type==="select-one"||r<0,o=a?null:[],s=a?r+1:n.length,l=r<0?s:a?r:0;for(;l<s;l++){i=n[l];if((i.selected||l===r)&&(y.optDisabled?!i.disabled:i.getAttribute("disabled")===null)&&(!i.parentNode.disabled||!m.nodeName(i.parentNode,"optgroup"))){t=m(i).val();if(a){return t}o.push(t)}}return o},set:function(e,t){var i,n,r=e.options,a=m.makeArray(t),o=r.length;while(o--){n=r[o];if(m.inArray(m.valHooks.option.get(n),a)>=0){try{n.selected=i=true}catch(e){n.scrollHeight}}else{n.selected=false}}if(!i){e.selectedIndex=-1}return r}}}});m.each(["radio","checkbox"],function(){m.valHooks[this]={set:function(e,t){if(m.isArray(t)){return e.checked=m.inArray(m(e).val(),t)>-1}}};if(!y.checkOn){m.valHooks[this].get=function(e){return e.getAttribute("value")===null?"on":e.value}}});var Pt,Ct,_t=m.expr.attrHandle,kt=/^(?:checked|selected)$/i,Dt=y.getSetAttribute,xt=y.input;m.fn.extend({attr:function(e,t){return ee(this,m.attr,e,t,arguments.length>1)},removeAttr:function(e){return this.each(function(){m.removeAttr(this,e)})}});m.extend({attr:function(e,t,i){var n,r,a=e.nodeType;if(a===3||a===8||a===2){return}if(typeof e.getAttribute==="undefined"){return m.prop(e,t,i)}if(a!==1||!m.isXMLDoc(e)){t=t.toLowerCase();r=m.attrHooks[t]||(m.expr.match.bool.test(t)?Ct:Pt)}if(i!==undefined){if(i===null){m.removeAttr(e,t);return}if(r&&"set"in r&&(n=r.set(e,i,t))!==undefined){return n}e.setAttribute(t,i+"");return i}if(r&&"get"in r&&(n=r.get(e,t))!==null){return n}n=m.find.attr(e,t);return n==null?undefined:n},attrHooks:{type:{set:function(e,t){if(!y.radioValue&&t==="radio"&&m.nodeName(e,"input")){var i=e.value;e.setAttribute("type",t);if(i){e.value=i}return t}}}},removeAttr:function(e,t){var i,n,r=0,a=t&&t.match(I);if(a&&e.nodeType===1){while(i=a[r++]){n=m.propFix[i]||i;if(m.expr.match.bool.test(i)){if(xt&&Dt||!kt.test(i)){e[n]=false}else{e[m.camelCase("default-"+i)]=e[n]=false}}else{m.attr(e,i,"")}e.removeAttribute(Dt?i:n)}}}});Ct={set:function(e,t,i){if(t===false){m.removeAttr(e,i)}else if(xt&&Dt||!kt.test(i)){e.setAttribute(!Dt&&m.propFix[i]||i,i)}else{e[m.camelCase("default-"+i)]=e[i]=true}return i}};m.each(m.expr.match.bool.source.match(/\w+/g),function(e,t){var i=_t[t]||m.find.attr;if(xt&&Dt||!kt.test(t)){_t[t]=function(e,t,n){var r,a;if(!n){a=_t[t];_t[t]=r;r=i(e,t,n)!=null?t.toLowerCase():null;_t[t]=a}return r}}else{_t[t]=function(e,t,i){if(!i){return e[m.camelCase("default-"+t)]?t.toLowerCase():null}}}});if(!xt||!Dt){m.attrHooks.value={set:function(e,t,i){if(m.nodeName(e,"input")){e.defaultValue=t}else{return Pt&&Pt.set(e,t,i)}}}}if(!Dt){Pt={set:function(e,t,i){var n=e.getAttributeNode(i);if(!n){e.setAttributeNode(n=e.ownerDocument.createAttribute(i))}n.value=t+="";if(i==="value"||t===e.getAttribute(i)){return t}}};_t.id=_t.name=_t.coords=function(e,t,i){var n;if(!i){return(n=e.getAttributeNode(t))&&n.value!==""?n.value:null}};m.valHooks.button={get:function(e,t){var i=e.getAttributeNode(t);if(i&&i.specified){return i.value}},set:Pt.set};m.attrHooks.contenteditable={set:function(e,t,i){Pt.set(e,t===""?false:t,i)}};m.each(["width","height"],function(e,t){m.attrHooks[t]={set:function(e,i){if(i===""){e.setAttribute(t,"auto");return i}}}})}if(!y.style){m.attrHooks.style={get:function(e){return e.style.cssText||undefined},set:function(e,t){return e.style.cssText=t+""}}}var Tt=/^(?:input|select|textarea|button|object)$/i,Mt=/^(?:a|area)$/i;m.fn.extend({prop:function(e,t){return ee(this,m.prop,e,t,arguments.length>1)},removeProp:function(e){e=m.propFix[e]||e;return this.each(function(){try{this[e]=undefined;delete this[e]}catch(e){}})}});m.extend({prop:function(e,t,i){var n,r,a=e.nodeType;if(a===3||a===8||a===2){return}if(a!==1||!m.isXMLDoc(e)){t=m.propFix[t]||t;r=m.propHooks[t]}if(i!==undefined){if(r&&"set"in r&&(n=r.set(e,i,t))!==undefined){return n}return e[t]=i}if(r&&"get"in r&&(n=r.get(e,t))!==null){return n}return e[t]},propHooks:{tabIndex:{get:function(e){var t=m.find.attr(e,"tabindex");return t?parseInt(t,10):Tt.test(e.nodeName)||Mt.test(e.nodeName)&&e.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}});if(!y.hrefNormalized){m.each(["href","src"],function(e,t){m.propHooks[t]={get:function(e){return e.getAttribute(t,4)}}})}if(!y.optSelected){m.propHooks.selected={get:function(e){var t=e.parentNode;if(t){t.selectedIndex;if(t.parentNode){t.parentNode.selectedIndex}}return null}}}m.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){m.propFix[this.toLowerCase()]=this});if(!y.enctype){m.propFix.enctype="encoding"}var Lt=/[\t\r\n\f]/g;function Wt(e){return m.attr(e,"class")||""}m.fn.extend({addClass:function(e){var t,i,n,r,a,o,s,l=0;if(m.isFunction(e)){return this.each(function(t){m(this).addClass(e.call(this,t,Wt(this)))})}if(typeof e==="string"&&e){t=e.match(I)||[];while(i=this[l++]){r=Wt(i);n=i.nodeType===1&&(" "+r+" ").replace(Lt," ");if(n){o=0;while(a=t[o++]){if(n.indexOf(" "+a+" ")<0){n+=a+" "}}s=m.trim(n);if(r!==s){m.attr(i,"class",s)}}}}return this},removeClass:function(e){var t,i,n,r,a,o,s,l=0;if(m.isFunction(e)){return this.each(function(t){m(this).removeClass(e.call(this,t,Wt(this)))})}if(!arguments.length){return this.attr("class","")}if(typeof e==="string"&&e){t=e.match(I)||[];while(i=this[l++]){r=Wt(i);n=i.nodeType===1&&(" "+r+" ").replace(Lt," ");if(n){o=0;while(a=t[o++]){while(n.indexOf(" "+a+" ")>-1){n=n.replace(" "+a+" "," ")}}s=m.trim(n);if(r!==s){m.attr(i,"class",s)}}}}return this},toggleClass:function(e,t){var i=typeof e;if(typeof t==="boolean"&&i==="string"){return t?this.addClass(e):this.removeClass(e)}if(m.isFunction(e)){return this.each(function(i){m(this).toggleClass(e.call(this,i,Wt(this),t),t)})}return this.each(function(){var t,n,r,a;if(i==="string"){n=0;r=m(this);a=e.match(I)||[];while(t=a[n++]){if(r.hasClass(t)){r.removeClass(t)}else{r.addClass(t)}}}else if(e===undefined||i==="boolean"){t=Wt(this);if(t){m._data(this,"__className__",t)}m.attr(this,"class",t||e===false?"":m._data(this,"__className__")||"")}})},hasClass:function(e){var t,i,n=0;t=" "+e+" ";while(i=this[n++]){if(i.nodeType===1&&(" "+Wt(i)+" ").replace(Lt," ").indexOf(t)>-1){return true}}return false}});m.each(("blur focus focusin focusout load resize scroll unload click dblclick "+"mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave "+"change select submit keydown keypress keyup error contextmenu").split(" "),function(e,t){m.fn[t]=function(e,i){return arguments.length>0?this.on(t,null,e,i):this.trigger(t)}});m.fn.extend({hover:function(e,t){return this.mouseenter(e).mouseleave(t||e)}});var Et=i.location;var At=m.now();var Rt=/\?/;var Bt=/(,)|(\[|{)|(}|])|"(?:[^"\\\r\n]|\\["\\\/bfnrt]|\\u[\da-fA-F]{4})*"\s*:?|true|false|null|-?(?!0\d)\d+(?:\.\d+|)(?:[eE][+-]?\d+|)/g;m.parseJSON=function(e){if(i.JSON&&i.JSON.parse){return i.JSON.parse(e+"")}var t,n=null,r=m.trim(e+"");return r&&!m.trim(r.replace(Bt,function(e,i,r,a){if(t&&i){n=0}if(n===0){return e}t=r||i;n+=!a-!r;return""}))?Function("return "+r)():m.error("Invalid JSON: "+e)};m.parseXML=function(e){var t,n;if(!e||typeof e!=="string"){return null}try{if(i.DOMParser){n=new i.DOMParser;t=n.parseFromString(e,"text/xml")}else{t=new i.ActiveXObject("Microsoft.XMLDOM");t.async="false";t.loadXML(e)}}catch(e){t=undefined}if(!t||!t.documentElement||t.getElementsByTagName("parsererror").length){m.error("Invalid XML: "+e)}return t};var It=/#.*$/,zt=/([?&])_=[^&]*/,Ot=/^(.*?):[ \t]*([^\r\n]*)\r?$/gm,Ft=/^(?:about|app|app-storage|.+-extension|file|res|widget):$/,Nt=/^(?:GET|HEAD)$/,Ut=/^\/\//,jt=/^([\w.+-]+:)(?:\/\/(?:[^\/?#]*@|)([^\/?#:]*)(?::(\d+)|)|)/,Ht={},qt={},Vt="*/".concat("*"),Jt=Et.href,Gt=jt.exec(Jt.toLowerCase())||[];function Yt(e){return function(t,i){if(typeof t!=="string"){i=t;t="*"}var n,r=0,a=t.toLowerCase().match(I)||[];if(m.isFunction(i)){while(n=a[r++]){if(n.charAt(0)==="+"){n=n.slice(1)||"*";(e[n]=e[n]||[]).unshift(i)}else{(e[n]=e[n]||[]).push(i)}}}}}function Xt(e,t,i,n){var r={},a=e===qt;function o(s){var l;r[s]=true;m.each(e[s]||[],function(e,s){var u=s(t,i,n);if(typeof u==="string"&&!a&&!r[u]){t.dataTypes.unshift(u);o(u);return false}else if(a){return!(l=u)}});return l}return o(t.dataTypes[0])||!r["*"]&&o("*")}function Kt(e,t){var i,n,r=m.ajaxSettings.flatOptions||{};for(n in t){if(t[n]!==undefined){(r[n]?e:i||(i={}))[n]=t[n]}}if(i){m.extend(true,e,i)}return e}function $t(e,t,i){var n,r,a,o,s=e.contents,l=e.dataTypes;while(l[0]==="*"){l.shift();if(r===undefined){r=e.mimeType||t.getResponseHeader("Content-Type")}}if(r){for(o in s){if(s[o]&&s[o].test(r)){l.unshift(o);break}}}if(l[0]in i){a=l[0]}else{for(o in i){if(!l[0]||e.converters[o+" "+l[0]]){a=o;break}if(!n){n=o}}a=a||n}if(a){if(a!==l[0]){l.unshift(a)}return i[a]}}function Zt(e,t,i,n){var r,a,o,s,l,u={},f=e.dataTypes.slice();if(f[1]){for(o in e.converters){u[o.toLowerCase()]=e.converters[o]}}a=f.shift();while(a){if(e.responseFields[a]){i[e.responseFields[a]]=t}if(!l&&n&&e.dataFilter){t=e.dataFilter(t,e.dataType)}l=a;a=f.shift();if(a){if(a==="*"){a=l}else if(l!=="*"&&l!==a){o=u[l+" "+a]||u["* "+a];if(!o){for(r in u){s=r.split(" ");if(s[1]===a){o=u[l+" "+s[0]]||u["* "+s[0]];if(o){if(o===true){o=u[r]}else if(u[r]!==true){a=s[0];f.unshift(s[1])}break}}}}if(o!==true){if(o&&e["throws"]){t=o(t)}else{try{t=o(t)}catch(e){return{state:"parsererror",error:o?e:"No conversion from "+l+" to "+a}}}}}}}return{state:"success",data:t}}m.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:Jt,type:"GET",isLocal:Ft.test(Gt[1]),global:true,processData:true,async:true,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":Vt,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":true,"text json":m.parseJSON,"text xml":m.parseXML},flatOptions:{url:true,context:true}},ajaxSetup:function(e,t){return t?Kt(Kt(e,m.ajaxSettings),t):Kt(m.ajaxSettings,e)},ajaxPrefilter:Yt(Ht),ajaxTransport:Yt(qt),ajax:function(e,t){if(typeof e==="object"){t=e;e=undefined}t=t||{};var n,r,a,o,s,l,u,f,c=m.ajaxSetup({},t),d=c.context||c,h=c.context&&(d.nodeType||d.jquery)?m(d):m.event,p=m.Deferred(),y=m.Callbacks("once memory"),v=c.statusCode||{},g={},b={},S=0,w="canceled",P={readyState:0,getResponseHeader:function(e){var t;if(S===2){if(!f){f={};while(t=Ot.exec(o)){f[t[1].toLowerCase()]=t[2]}}t=f[e.toLowerCase()]}return t==null?null:t},getAllResponseHeaders:function(){return S===2?o:null},setRequestHeader:function(e,t){var i=e.toLowerCase();if(!S){e=b[i]=b[i]||e;g[e]=t}return this},overrideMimeType:function(e){if(!S){c.mimeType=e}return this},statusCode:function(e){var t;if(e){if(S<2){for(t in e){v[t]=[v[t],e[t]]}}else{P.always(e[P.status])}}return this},abort:function(e){var t=e||w;if(u){u.abort(t)}C(0,t);return this}};p.promise(P).complete=y.add;P.success=P.done;P.error=P.fail;c.url=((e||c.url||Jt)+"").replace(It,"").replace(Ut,Gt[1]+"//");c.type=t.method||t.type||c.method||c.type;c.dataTypes=m.trim(c.dataType||"*").toLowerCase().match(I)||[""];if(c.crossDomain==null){n=jt.exec(c.url.toLowerCase());c.crossDomain=!!(n&&(n[1]!==Gt[1]||n[2]!==Gt[2]||(n[3]||(n[1]==="http:"?"80":"443"))!==(Gt[3]||(Gt[1]==="http:"?"80":"443"))))}if(c.data&&c.processData&&typeof c.data!=="string"){c.data=m.param(c.data,c.traditional)}Xt(Ht,c,t,P);if(S===2){return P}l=m.event&&c.global;if(l&&m.active++===0){m.event.trigger("ajaxStart")}c.type=c.type.toUpperCase();c.hasContent=!Nt.test(c.type);a=c.url;if(!c.hasContent){if(c.data){a=c.url+=(Rt.test(a)?"&":"?")+c.data;delete c.data}if(c.cache===false){c.url=zt.test(a)?a.replace(zt,"$1_="+At++):a+(Rt.test(a)?"&":"?")+"_="+At++}}if(c.ifModified){if(m.lastModified[a]){P.setRequestHeader("If-Modified-Since",m.lastModified[a])}if(m.etag[a]){P.setRequestHeader("If-None-Match",m.etag[a])}}if(c.data&&c.hasContent&&c.contentType!==false||t.contentType){P.setRequestHeader("Content-Type",c.contentType)}P.setRequestHeader("Accept",c.dataTypes[0]&&c.accepts[c.dataTypes[0]]?c.accepts[c.dataTypes[0]]+(c.dataTypes[0]!=="*"?", "+Vt+"; q=0.01":""):c.accepts["*"]);for(r in c.headers){P.setRequestHeader(r,c.headers[r])}if(c.beforeSend&&(c.beforeSend.call(d,P,c)===false||S===2)){return P.abort()}w="abort";for(r in{success:1,error:1,complete:1}){P[r](c[r])}u=Xt(qt,c,t,P);if(!u){C(-1,"No Transport")}else{P.readyState=1;if(l){h.trigger("ajaxSend",[P,c])}if(S===2){return P}if(c.async&&c.timeout>0){s=i.setTimeout(function(){P.abort("timeout")},c.timeout)}try{S=1;u.send(g,C)}catch(e){if(S<2){C(-1,e)}else{throw e}}}function C(e,t,n,r){var f,g,b,w,C,_=t;if(S===2){return}S=2;if(s){i.clearTimeout(s)}u=undefined;o=r||"";P.readyState=e>0?4:0;f=e>=200&&e<300||e===304;if(n){w=$t(c,P,n)}w=Zt(c,w,P,f);if(f){if(c.ifModified){C=P.getResponseHeader("Last-Modified");if(C){m.lastModified[a]=C}C=P.getResponseHeader("etag");if(C){m.etag[a]=C}}if(e===204||c.type==="HEAD"){_="nocontent"}else if(e===304){_="notmodified"}else{_=w.state;g=w.data;b=w.error;f=!b}}else{b=_;if(e||!_){_="error";if(e<0){e=0}}}P.status=e;P.statusText=(t||_)+"";if(f){p.resolveWith(d,[g,_,P])}else{p.rejectWith(d,[P,_,b])}P.statusCode(v);v=undefined;if(l){h.trigger(f?"ajaxSuccess":"ajaxError",[P,c,f?g:b])}y.fireWith(d,[P,_]);if(l){h.trigger("ajaxComplete",[P,c]);if(!--m.active){m.event.trigger("ajaxStop")}}}return P},getJSON:function(e,t,i){return m.get(e,t,i,"json")},getScript:function(e,t){return m.get(e,undefined,t,"script")}});m.each(["get","post"],function(e,t){m[t]=function(e,i,n,r){if(m.isFunction(i)){r=r||n;n=i;i=undefined}return m.ajax(m.extend({url:e,type:t,dataType:r,data:i,success:n},m.isPlainObject(e)&&e))}});m._evalUrl=function(e){return m.ajax({url:e,type:"GET",dataType:"script",cache:true,async:false,global:false,throws:true})};m.fn.extend({wrapAll:function(e){if(m.isFunction(e)){return this.each(function(t){m(this).wrapAll(e.call(this,t))})}if(this[0]){var t=m(e,this[0].ownerDocument).eq(0).clone(true);if(this[0].parentNode){t.insertBefore(this[0])}t.map(function(){var e=this;while(e.firstChild&&e.firstChild.nodeType===1){e=e.firstChild}return e}).append(this)}return this},wrapInner:function(e){if(m.isFunction(e)){return this.each(function(t){m(this).wrapInner(e.call(this,t))})}return this.each(function(){var t=m(this),i=t.contents();if(i.length){i.wrapAll(e)}else{t.append(e)}})},wrap:function(e){var t=m.isFunction(e);return this.each(function(i){m(this).wrapAll(t?e.call(this,i):e)})},unwrap:function(){return this.parent().each(function(){if(!m.nodeName(this,"body")){m(this).replaceWith(this.childNodes)}}).end()}});function Qt(e){return e.style&&e.style.display||m.css(e,"display")}function ei(e){while(e&&e.nodeType===1){if(Qt(e)==="none"||e.type==="hidden"){return true}e=e.parentNode}return false}m.expr.filters.hidden=function(e){return y.reliableHiddenOffsets()?e.offsetWidth<=0&&e.offsetHeight<=0&&!e.getClientRects().length:ei(e)};m.expr.filters.visible=function(e){return!m.expr.filters.hidden(e)};var ti=/%20/g,ii=/\[\]$/,ni=/\r?\n/g,ri=/^(?:submit|button|image|reset|file)$/i,ai=/^(?:input|select|textarea|keygen)/i;function oi(e,t,i,n){var r;if(m.isArray(t)){m.each(t,function(t,r){if(i||ii.test(e)){n(e,r)}else{oi(e+"["+(typeof r==="object"&&r!=null?t:"")+"]",r,i,n)}})}else if(!i&&m.type(t)==="object"){for(r in t){oi(e+"["+r+"]",t[r],i,n)}}else{n(e,t)}}m.param=function(e,t){var i,n=[],r=function(e,t){t=m.isFunction(t)?t():t==null?"":t;n[n.length]=encodeURIComponent(e)+"="+encodeURIComponent(t)};if(t===undefined){t=m.ajaxSettings&&m.ajaxSettings.traditional}if(m.isArray(e)||e.jquery&&!m.isPlainObject(e)){m.each(e,function(){r(this.name,this.value)})}else{for(i in e){oi(i,e[i],t,r)}}return n.join("&").replace(ti,"+")};m.fn.extend({serialize:function(){return m.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var e=m.prop(this,"elements");return e?m.makeArray(e):this}).filter(function(){var e=this.type;return this.name&&!m(this).is(":disabled")&&ai.test(this.nodeName)&&!ri.test(e)&&(this.checked||!te.test(e))}).map(function(e,t){var i=m(this).val();return i==null?null:m.isArray(i)?m.map(i,function(e){return{name:t.name,value:e.replace(ni,"\r\n")}}):{name:t.name,value:i.replace(ni,"\r\n")}}).get()}});m.ajaxSettings.xhr=i.ActiveXObject!==undefined?function(){if(this.isLocal){return ci()}if(s.documentMode>8){return fi()}return/^(get|post|head|put|delete|options)$/i.test(this.type)&&fi()||ci()}:fi;var si=0,li={},ui=m.ajaxSettings.xhr();if(i.attachEvent){i.attachEvent("onunload",function(){for(var e in li){li[e](undefined,true)}})}y.cors=!!ui&&"withCredentials"in ui;ui=y.ajax=!!ui;if(ui){m.ajaxTransport(function(e){if(!e.crossDomain||y.cors){var t;return{send:function(n,r){var a,o=e.xhr(),s=++si;o.open(e.type,e.url,e.async,e.username,e.password);if(e.xhrFields){for(a in e.xhrFields){o[a]=e.xhrFields[a]}}if(e.mimeType&&o.overrideMimeType){o.overrideMimeType(e.mimeType)}if(!e.crossDomain&&!n["X-Requested-With"]){n["X-Requested-With"]="XMLHttpRequest"}for(a in n){if(n[a]!==undefined){o.setRequestHeader(a,n[a]+"")}}o.send(e.hasContent&&e.data||null);t=function(i,n){var a,l,u;if(t&&(n||o.readyState===4)){delete li[s];t=undefined;o.onreadystatechange=m.noop;if(n){if(o.readyState!==4){o.abort()}}else{u={};a=o.status;if(typeof o.responseText==="string"){u.text=o.responseText}try{l=o.statusText}catch(e){l=""}if(!a&&e.isLocal&&!e.crossDomain){a=u.text?200:404}else if(a===1223){a=204}}}if(u){r(a,l,u,o.getAllResponseHeaders())}};if(!e.async){t()}else if(o.readyState===4){i.setTimeout(t)}else{o.onreadystatechange=li[s]=t}},abort:function(){if(t){t(undefined,true)}}}}})}function fi(){try{return new i.XMLHttpRequest}catch(e){}}function ci(){try{return new i.ActiveXObject("Microsoft.XMLHTTP")}catch(e){}}m.ajaxPrefilter(function(e){if(e.crossDomain){e.contents.script=false}});m.ajaxSetup({accepts:{script:"text/javascript, application/javascript, "+"application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(e){m.globalEval(e);return e}}});m.ajaxPrefilter("script",function(e){if(e.cache===undefined){e.cache=false}if(e.crossDomain){e.type="GET";e.global=false}});m.ajaxTransport("script",function(e){if(e.crossDomain){var t,i=s.head||m("head")[0]||s.documentElement;return{send:function(n,r){t=s.createElement("script");t.async=true;if(e.scriptCharset){t.charset=e.scriptCharset}t.src=e.url;t.onload=t.onreadystatechange=function(e,i){if(i||!t.readyState||/loaded|complete/.test(t.readyState)){t.onload=t.onreadystatechange=null;if(t.parentNode){t.parentNode.removeChild(t)}t=null;if(!i){r(200,"success")}}};i.insertBefore(t,i.firstChild)},abort:function(){if(t){t.onload(undefined,true)}}}}});var di=[],hi=/(=)\?(?=&|$)|\?\?/;m.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var e=di.pop()||m.expando+"_"+At++;this[e]=true;return e}});m.ajaxPrefilter("json jsonp",function(e,t,n){var r,a,o,s=e.jsonp!==false&&(hi.test(e.url)?"url":typeof e.data==="string"&&(e.contentType||"").indexOf("application/x-www-form-urlencoded")===0&&hi.test(e.data)&&"data");if(s||e.dataTypes[0]==="jsonp"){r=e.jsonpCallback=m.isFunction(e.jsonpCallback)?e.jsonpCallback():e.jsonpCallback;if(s){e[s]=e[s].replace(hi,"$1"+r)}else if(e.jsonp!==false){e.url+=(Rt.test(e.url)?"&":"?")+e.jsonp+"="+r}e.converters["script json"]=function(){if(!o){m.error(r+" was not called")}return o[0]};e.dataTypes[0]="json";a=i[r];i[r]=function(){o=arguments};n.always(function(){if(a===undefined){m(i).removeProp(r)}else{i[r]=a}if(e[r]){e.jsonpCallback=t.jsonpCallback;di.push(r)}if(o&&m.isFunction(a)){a(o[0])}o=a=undefined});return"script"}});y.createHTMLDocument=function(){if(!s.implementation.createHTMLDocument){return false}var e=s.implementation.createHTMLDocument("");e.body.innerHTML="<form></form><form></form>";return e.body.childNodes.length===2}();m.parseHTML=function(e,t,i){if(!e||typeof e!=="string"){return null}if(typeof t==="boolean"){i=t;t=false}t=t||(y.createHTMLDocument?s.implementation.createHTMLDocument(""):s);var n=x.exec(e),r=!i&&[];if(n){return[t.createElement(n[1])]}n=he([e],t,r);if(r&&r.length){m(r).remove()}return m.merge([],n.childNodes)};var pi=m.fn.load;m.fn.load=function(e,t,i){if(typeof e!=="string"&&pi){return pi.apply(this,arguments)}var n,r,a,o=this,s=e.indexOf(" ");if(s>-1){n=m.trim(e.slice(s,e.length));e=e.slice(0,s)}if(m.isFunction(t)){i=t;t=undefined}else if(t&&typeof t==="object"){r="POST"}if(o.length>0){m.ajax({url:e,type:r||"GET",dataType:"html",data:t}).done(function(e){a=arguments;o.html(n?m("<div>").append(m.parseHTML(e)).find(n):e)}).always(i&&function(e,t){o.each(function(){i.apply(o,a||[e.responseText,t,e])})})}return this};m.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(e,t){m.fn[t]=function(e){return this.on(t,e)}});m.expr.filters.animated=function(e){return m.grep(m.timers,function(t){return e===t.elem}).length};function yi(e){return m.isWindow(e)?e:e.nodeType===9?e.defaultView||e.parentWindow:false}m.offset={setOffset:function(e,t,i){var n,r,a,o,s,l,u,f=m.css(e,"position"),c=m(e),d={};if(f==="static"){e.style.position="relative"}s=c.offset();a=m.css(e,"top");l=m.css(e,"left");u=(f==="absolute"||f==="fixed")&&m.inArray("auto",[a,l])>-1;if(u){n=c.position();o=n.top;r=n.left}else{o=parseFloat(a)||0;r=parseFloat(l)||0}if(m.isFunction(t)){t=t.call(e,i,m.extend({},s))}if(t.top!=null){d.top=t.top-s.top+o}if(t.left!=null){d.left=t.left-s.left+r}if("using"in t){t.using.call(e,d)}else{c.css(d)}}};m.fn.extend({offset:function(e){if(arguments.length){return e===undefined?this:this.each(function(t){m.offset.setOffset(this,e,t)})}var t,i,n={top:0,left:0},r=this[0],a=r&&r.ownerDocument;if(!a){return}t=a.documentElement;if(!m.contains(t,r)){return n}if(typeof r.getBoundingClientRect!=="undefined"){n=r.getBoundingClientRect()}i=yi(a);return{top:n.top+(i.pageYOffset||t.scrollTop)-(t.clientTop||0),left:n.left+(i.pageXOffset||t.scrollLeft)-(t.clientLeft||0)}},position:function(){if(!this[0]){return}var e,t,i={top:0,left:0},n=this[0];if(m.css(n,"position")==="fixed"){t=n.getBoundingClientRect()}else{e=this.offsetParent();t=this.offset();if(!m.nodeName(e[0],"html")){i=e.offset()}i.top+=m.css(e[0],"borderTopWidth",true);i.left+=m.css(e[0],"borderLeftWidth",true)}return{top:t.top-i.top-m.css(n,"marginTop",true),left:t.left-i.left-m.css(n,"marginLeft",true)}},offsetParent:function(){return this.map(function(){var e=this.offsetParent;while(e&&(!m.nodeName(e,"html")&&m.css(e,"position")==="static")){e=e.offsetParent}return e||Je})}});m.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(e,t){var i=/Y/.test(t);m.fn[e]=function(n){return ee(this,function(e,n,r){var a=yi(e);if(r===undefined){return a?t in a?a[t]:a.document.documentElement[n]:e[n]}if(a){a.scrollTo(!i?r:m(a).scrollLeft(),i?r:m(a).scrollTop())}else{e[n]=r}},e,n,arguments.length,null)}});m.each(["top","left"],function(e,t){m.cssHooks[t]=Ke(y.pixelPosition,function(e,i){if(i){i=Ye(e,t);return qe.test(i)?m(e).position()[t]+"px":i}})});m.each({Height:"height",Width:"width"},function(e,t){m.each({padding:"inner"+e,content:t,"":"outer"+e},function(i,n){m.fn[n]=function(n,r){var a=arguments.length&&(i||typeof n!=="boolean"),o=i||(n===true||r===true?"margin":"border");return ee(this,function(t,i,n){var r;if(m.isWindow(t)){return t.document.documentElement["client"+e]}if(t.nodeType===9){r=t.documentElement;return Math.max(t.body["scroll"+e],r["scroll"+e],t.body["offset"+e],r["offset"+e],r["client"+e])}return n===undefined?m.css(t,i,o):m.style(t,i,n,o)},t,a?n:undefined,a,null)}})});m.fn.extend({bind:function(e,t,i){return this.on(e,null,t,i)},unbind:function(e,t){return this.off(e,null,t)},delegate:function(e,t,i,n){return this.on(t,e,i,n)},undelegate:function(e,t,i){return arguments.length===1?this.off(e,"**"):this.off(t,e||"**",i)}});m.fn.size=function(){return this.length};m.fn.andSelf=m.fn.addBack;if(true){!(n=[],r=function(){return m}.apply(t,n),r!==undefined&&(e.exports=r))}var vi=i.jQuery,mi=i.$;m.noConflict=function(e){if(i.$===m){i.$=mi}if(e&&i.jQuery===m){i.jQuery=vi}return m};if(!a){i.jQuery=i.$=m}return m})},function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:true});var n=function(){function e(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||false;n.configurable=true;if("value"in n)n.writable=true;Object.defineProperty(e,n.key,n)}}return function(t,i,n){if(i)e(t.prototype,i);if(n)e(t,n);return t}}();function r(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var a=function(){function e(t){r(this,e);this.listener={};this.type=t|""}n(e,[{key:"on",value:function e(t,i){if(!this.listener[t]){this.listener[t]=[]}this.listener[t].push(i);return true}},{key:"off",value:function e(t,i){if(this.listener[t]){var n=this.listener[t].indexOf(i);if(n>-1){this.listener[t].splice(n,1)}return true}return false}},{key:"offAll",value:function e(){this.listener={}}},{key:"dispatch",value:function e(t,i){if(this.listener[t]){this.listener[t].map(function(e){e.apply(null,[i])});return true}return false}}]);return e}();t.default=a},function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:true});var n=function(){function e(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||false;n.configurable=true;if("value"in n)n.writable=true;Object.defineProperty(e,n.key,n)}}return function(t,i,n){if(i)e(t.prototype,i);if(n)e(t,n);return t}}();function r(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var a=function(){function e(){r(this,e);this._keyStr="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="}n(e,[{key:"$",value:function e(t){var i=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]*))$/;var n=/^(?:\s*(<[\w\W]+>)[^>]*|.([\w-]*))$/;if(i.test(t)){var r=i.exec(t);return document.getElementById(r[2])}else if(n.test(t)){var a=n.exec(t);var o=document.getElementsByTagName("*");var s=[];for(var l=0,u=o.length;l<u;l++){if(o[l].className.match(new RegExp("(\\s|^)"+a[2]+"(\\s|$)"))){s.push(o[l])}}return s}}},{key:"dateFormat",value:function e(t,i){var n={"M+":t.getMonth()+1,"d+":t.getDate(),"h+":t.getHours(),"m+":t.getMinutes(),"s+":t.getSeconds(),"q+":Math.floor((t.getMonth()+3)/3),S:t.getMilliseconds()};if(/(y+)/.test(i)){i=i.replace(RegExp.$1,(t.getFullYear()+"").substr(4-RegExp.$1.length))}for(var r in n){if(new RegExp("("+r+")").test(i)){i=i.replace(RegExp.$1,RegExp.$1.length===1?n[r]:("00"+n[r]).substr((""+n[r]).length))}}return i}},{key:"downloadFile",value:function e(t,i){var n=t;if(!(t instanceof Blob||t instanceof File)){n=new Blob([t])}var r=window.URL.createObjectURL(n);var a=window.document.createElement("a");a.href=r;a.download=i;var o=document.createEvent("MouseEvents");o.initEvent("click",true,true);a.dispatchEvent(o)}},{key:"createxmlDoc",value:function e(){var t;var i=["MSXML2.DOMDocument","MSXML2.DOMDocument.5.0","MSXML2.DOMDocument.4.0","MSXML2.DOMDocument.3.0","Microsoft.XmlDom"];for(var n=0,r=i.length;n<r;n++){try{t=new ActiveXObject(i[n]);break}catch(e){t=document.implementation.createDocument("","",null);break}}t.async="false";return t}},{key:"parseXmlFromStr",value:function e(t){if(null===t||""===t){return null}var i=this.createxmlDoc();if(navigator.appName==="Netscape"||navigator.appName==="Opera"){var n=new DOMParser;i=n.parseFromString(t,"text/xml")}else{i.loadXML(t)}return i}},{key:"encode",value:function e(t){var i="";var n;var r;var a;var o;var s;var l;var u;var f=0;t=this._utf8_encode(t);while(f<t.length){n=t.charCodeAt(f++);r=t.charCodeAt(f++);a=t.charCodeAt(f++);o=n>>2;s=(n&3)<<4|r>>4;l=(r&15)<<2|a>>6;u=a&63;if(isNaN(r)){l=u=64}else if(isNaN(a)){u=64}i=i+this._keyStr.charAt(o)+this._keyStr.charAt(s)+this._keyStr.charAt(l)+this._keyStr.charAt(u)}return i}},{key:"decode",value:function e(t){var i="";var n;var r;var a;var o;var s;var l;var u;var f=0;t=t.replace(/[^A-Za-z0-9+/=]/g,"");while(f<t.length){o=this._keyStr.indexOf(t.charAt(f++));s=this._keyStr.indexOf(t.charAt(f++));l=this._keyStr.indexOf(t.charAt(f++));u=this._keyStr.indexOf(t.charAt(f++));n=o<<2|s>>4;r=(s&15)<<4|l>>2;a=(l&3)<<6|u;i=i+String.fromCharCode(n);if(l!==64){i=i+String.fromCharCode(r)}if(u!==64){i=i+String.fromCharCode(a)}}i=this._utf8_decode(i);return i}},{key:"_utf8_encode",value:function e(t){t=t.replace(/\r\n/g,"\n");var i="";for(var n=0;n<t.length;n++){var r=t.charCodeAt(n);if(r<128){i+=String.fromCharCode(r)}else if(r>127&&r<2048){i+=String.fromCharCode(r>>6|192);i+=String.fromCharCode(r&63|128)}else{i+=String.fromCharCode(r>>12|224);i+=String.fromCharCode(r>>6&63|128);i+=String.fromCharCode(r&63|128)}}return i}},{key:"_utf8_decode",value:function e(t){var i="";var n=0;var r=0;var a=0;while(n<t.length){r=t.charCodeAt(n);if(r<128){i+=String.fromCharCode(r);n++}else if(r>191&&r<224){a=t.charCodeAt(n+1);i+=String.fromCharCode((r&31)<<6|a&63);n+=2}else{a=t.charCodeAt(n+1);var o=t.charCodeAt(n+2);i+=String.fromCharCode((r&15)<<12|(a&63)<<6|o&63);n+=3}}return i}},{key:"isFirefox",value:function e(){var t=false;var i=navigator.userAgent.toLowerCase();var n="";var r=-1;if(i.match(/firefox\/([\d.]+)/)){n=i.match(/firefox\/([\d.]+)/)[1];r=parseInt(n.split(".")[0],10);if(r>-1){t=true}}return t}},{key:"isSafari",value:function e(){var t=false;var i=navigator.userAgent.toLowerCase();var n="";var r=-1;if(i.match(/version\/([\d.]+).safari./)){n=i.match(/version\/([\d.]+).safari./)[1];r=parseInt(n.split(".")[0],10);if(r>-1){t=true}}return t}},{key:"isEdge",value:function e(){return navigator.userAgent.toLowerCase().indexOf("edge")>-1}},{key:"dataURLtoBlob",value:function e(t){var i=t.split(",");var n=i[0].match(/:(.*?);/)[1];var r=atob(i[1]);var a=r.length;var o=new Uint8Array(a);while(a--){o[a]=r.charCodeAt(a)}return new Blob([o],{type:n})}},{key:"intToHexString",value:function e(t){var i=t.toString(16);if(i.length===1){i="0"+i}return i}}]);return e}();var o=t.oTool=new a},function(e,t,i){var n=i(15);var r=i(16);var a=r;a.v1=n;a.v4=r;e.exports=a},function(e,t){var i=typeof crypto!="undefined"&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)||typeof msCrypto!="undefined"&&typeof window.msCrypto.getRandomValues=="function"&&msCrypto.getRandomValues.bind(msCrypto);if(i){var n=new Uint8Array(16);e.exports=function e(){i(n);return n}}else{var r=new Array(16);e.exports=function e(){for(var t=0,i;t<16;t++){if((t&3)===0)i=Math.random()*4294967296;r[t]=i>>>((t&3)<<3)&255}return r}}},function(e,t){var i=[];for(var n=0;n<256;++n){i[n]=(n+256).toString(16).substr(1)}function r(e,t){var n=t||0;var r=i;return[r[e[n++]],r[e[n++]],r[e[n++]],r[e[n++]],"-",r[e[n++]],r[e[n++]],"-",r[e[n++]],r[e[n++]],"-",r[e[n++]],r[e[n++]],"-",r[e[n++]],r[e[n++]],r[e[n++]],r[e[n++]],r[e[n++]],r[e[n++]]].join("")}e.exports=r},function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:true});var n=function(){function e(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||false;n.configurable=true;if("value"in n)n.writable=true;Object.defineProperty(e,n.key,n)}}return function(t,i,n){if(i)e(t.prototype,i);if(n)e(t,n);return t}}();function r(e,t,i){if(t in e){Object.defineProperty(e,t,{value:i,enumerable:true,configurable:true,writable:true})}else{e[t]=i}return e}function a(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var o=t.NALU=function(){n(e,null,[{key:"type",value:function t(i){if(i.ntype in e.TYPES){return e.TYPES[i.ntype]}else{return"UNKNOWN"}}},{key:"NDR",get:function e(){return 1}},{key:"IDR",get:function e(){return 5}},{key:"SEI",get:function e(){return 6}},{key:"SPS",get:function e(){return 7}},{key:"PPS",get:function e(){return 8}},{key:"AUD",get:function e(){return 9}},{key:"TYPES",get:function t(){var i;return i={},r(i,e.IDR,"IDR"),r(i,e.SEI,"SEI"),r(i,e.SPS,"SPS"),r(i,e.PPS,"PPS"),r(i,e.NDR,"NDR"),r(i,e.AUD,"AUD"),i}}]);function e(t){a(this,e);this.payload=t;this.nri=(this.payload[0]&96)>>5;this.ntype=this.payload[0]&31}n(e,[{key:"toString",value:function t(){return e.type(this)+": NRI: "+this.getNri()}},{key:"getNri",value:function e(){return this.nri>>6}},{key:"type",value:function e(){return this.ntype}},{key:"isKeyframe",value:function t(){return this.ntype==e.IDR}},{key:"getSize",value:function e(){return 4+this.payload.byteLength}},{key:"getData",value:function e(){var t=new Uint8Array(this.getSize());var i=new DataView(t.buffer);i.setUint32(0,this.getSize()-4);t.set(this.payload,4);return t}}]);return e}()},function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.H264Parser=undefined;var n=function(){function e(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||false;n.configurable=true;if("value"in n)n.writable=true;Object.defineProperty(e,n.key,n)}}return function(t,i,n){if(i)e(t.prototype,i);if(n)e(t,n);return t}}();var r=i(25);var a=i(7);var o=i(0);var s=l(o);function l(e){if(e&&e.__esModule){return e}else{var t={};if(e!=null){for(var i in e){if(Object.prototype.hasOwnProperty.call(e,i))t[i]=e[i]}}t.default=e;return t}}function u(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var f=t.H264Parser=function(){n(e,null,[{key:"extractNALu",value:function e(t){var i=0,n=t.byteLength,r=void 0,a=0,o=[],s=void 0;while(i<n){r=t[i++];switch(a){case 0:if(r===0){a=1}break;case 1:if(r===0){a=2}else{a=0}break;case 2:case 3:if(r===0){a=3}else if(r===1&&i<n){if(s){o.push(t.subarray(s,i-a-1))}s=i;a=0}else{a=0}break;default:break}}if(s){o.push(t.subarray(s,n))}return o}},{key:"skipScalingList",value:function e(t,i){var n=8,r=8,a=void 0;for(var o=0;o<i;o++){if(r!==0){a=t.readEG();r=(n+a+256)%256}n=r===0?n:r}}},{key:"readSPS",value:function t(i){var n=new r.ExpGolomb(i);var a=0,o=0,s=0,l=0,u=1,f=void 0,c=void 0,d=void 0,h=void 0,p=void 0,y=void 0,v=void 0,m=void 0;n.readUByte();f=n.readUByte();c=n.readBits(5);n.skipBits(3);d=n.readUByte();n.skipUEG();if(f===100||f===110||f===122||f===244||f===44||f===83||f===86||f===118||f===128){var g=n.readUEG();if(g===3){n.skipBits(1)}n.skipUEG();n.skipUEG();n.skipBits(1);if(n.readBoolean()){m=g!==3?8:12;for(var b=0;b<m;++b){if(n.readBoolean()){if(b<6){e.skipScalingList(n,16)}else{e.skipScalingList(n,64)}}}}}n.skipUEG();var S=n.readUEG();if(S===0){n.readUEG()}else if(S===1){n.skipBits(1);n.skipEG();n.skipEG();h=n.readUEG();for(var w=0;w<h;++w){n.skipEG()}}n.skipUEG();n.skipBits(1);p=n.readUEG();y=n.readUEG();v=n.readBits(1);if(v===0){n.skipBits(1)}n.skipBits(1);if(n.readBoolean()){a=n.readUEG();o=n.readUEG();s=n.readUEG();l=n.readUEG()}if(n.readBoolean()){if(n.readBoolean()){var P=void 0;var C=n.readUByte();switch(C){case 1:P=[1,1];break;case 2:P=[12,11];break;case 3:P=[10,11];break;case 4:P=[16,11];break;case 5:P=[40,33];break;case 6:P=[24,11];break;case 7:P=[20,11];break;case 8:P=[32,11];break;case 9:P=[80,33];break;case 10:P=[18,11];break;case 11:P=[15,11];break;case 12:P=[64,33];break;case 13:P=[160,99];break;case 14:P=[4,3];break;case 15:P=[3,2];break;case 16:P=[2,1];break;case 255:{P=[n.readUByte()<<8|n.readUByte(),n.readUByte()<<8|n.readUByte()];break}}if(P){u=P[0]/P[1]}}if(n.readBoolean()){n.skipBits(1)}if(n.readBoolean()){n.skipBits(4);if(n.readBoolean()){n.skipBits(24)}}if(n.readBoolean()){n.skipUEG();n.skipUEG()}if(n.readBoolean()){var _=n.readUInt();var k=n.readUInt();var D=n.readBoolean();var x=k/(2*_)}}return{width:Math.ceil(((p+1)*16-a*2-o*2)*u),height:(2-v)*(y+1)*16-(v?2:4)*(s+l)}}}]);function e(t){u(this,e);this.remuxer=t;this.track=t.mp4track}n(e,[{key:"parseSPS",value:function t(i){var n=e.readSPS(new Uint8Array(i));this.track.width=n.width;this.track.height=n.height;this.track.sps=[new Uint8Array(i)];this.track.codec="avc1.";var r=new DataView(i.buffer,i.byteOffset+1,4);for(var a=0;a<3;++a){var o=r.getUint8(a).toString(16);if(o.length<2){o="0"+o}this.track.codec+=o}}},{key:"parsePPS",value:function e(t){this.track.pps=[new Uint8Array(t)]}},{key:"parseNAL",value:function e(t){if(!t)return false;var i=false;switch(t.type()){case a.NALU.NDR:i=true;break;case a.NALU.IDR:i=true;break;case a.NALU.PPS:if(!this.track.pps){this.parsePPS(t.getData().subarray(4));if(!this.remuxer.readyToDecode&&this.track.pps&&this.track.sps){this.remuxer.readyToDecode=true}}i=true;break;case a.NALU.SPS:if(!this.track.sps){this.parseSPS(t.getData().subarray(4));if(!this.remuxer.readyToDecode&&this.track.pps&&this.track.sps){this.remuxer.readyToDecode=true}}i=true;break;case a.NALU.AUD:s.log("AUD - ignoing and disable HD mode for live channel");if(this.remuxer.isHDAvail){this.remuxer.isHDAvail=false}break;case a.NALU.SEI:s.log("SEI - ignoing");break;default:}return i}}]);return e}()},function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.AACParser=undefined;var n=function(){function e(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||false;n.configurable=true;if("value"in n)n.writable=true;Object.defineProperty(e,n.key,n)}}return function(t,i,n){if(i)e(t.prototype,i);if(n)e(t,n);return t}}();var r=i(0);var a=o(r);function o(e){if(e&&e.__esModule){return e}else{var t={};if(e!=null){for(var i in e){if(Object.prototype.hasOwnProperty.call(e,i))t[i]=e[i]}}t.default=e;return t}}function s(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var l=void 0;var u=t.AACParser=function(){n(e,null,[{key:"getHeaderLength",value:function e(t){return t[1]&1?7:9}},{key:"getFrameLength",value:function e(t){return(t[3]&3)<<11|t[4]<<3|(t[5]&224)>>>5}},{key:"isAACPattern",value:function e(t){return t[0]===255&&(t[1]&240)===240&&(t[1]&6)===0}},{key:"extractAAC",value:function t(i){var n=0,r=i.byteLength,o=[],s=void 0,u=void 0;if(!e.isAACPattern(i)){a.error("Invalid ADTS audio format");return o}s=e.getHeaderLength(i);if(!l){l=i.subarray(0,s)}while(n<r){u=e.getFrameLength(i);o.push(i.subarray(s,u));i=i.slice(u);n+=u}return o}},{key:"samplingRateMap",get:function e(){return[96e3,88200,64e3,48e3,44100,32e3,24e3,22050,16e3,12e3,11025,8e3,7350]}},{key:"getAACHeaderData",get:function e(){return l}}]);function e(t){s(this,e);this.remuxer=t;this.track=t.mp4track}n(e,[{key:"setAACConfig",value:function t(){var i=void 0,n=void 0,r=void 0,a=new Uint8Array(2),o=e.getAACHeaderData;if(!o)return;i=((o[2]&192)>>>6)+1;n=(o[2]&60)>>>2;r=(o[2]&1)<<2;r|=(o[3]&192)>>>6;a[0]=i<<3;a[0]|=(n&14)>>1;a[1]|=(n&1)<<7;a[1]|=r<<3;this.track.codec="mp4a.40."+i;this.track.channelCount=r;this.track.config=a;this.remuxer.readyToDecode=true}}]);return e}()},function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.BaseRemuxer=undefined;var n=function(){function e(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||false;n.configurable=true;if("value"in n)n.writable=true;Object.defineProperty(e,n.key,n)}}return function(t,i,n){if(i)e(t.prototype,i);if(n)e(t,n);return t}}();var r=i(0);var a=o(r);function o(e){if(e&&e.__esModule){return e}else{var t={};if(e!=null){for(var i in e){if(Object.prototype.hasOwnProperty.call(e,i))t[i]=e[i]}}t.default=e;return t}}function s(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var l=1;var u=t.BaseRemuxer=function(){n(e,null,[{key:"getTrackID",value:function e(){return l++}}]);function e(){s(this,e);this.seq=1}n(e,[{key:"flush",value:function e(){this.seq++;this.mp4track.len=0;this.mp4track.samples=[]}},{key:"isReady",value:function e(){if(!this.readyToDecode||!this.samples.length)return null;return true}}]);return e}()},function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.appendByteArray=n;t.secToTime=r;function n(e,t){var i=new Uint8Array((e.byteLength|0)+(t.byteLength|0));i.set(e,0);i.set(t,e.byteLength|0);return i}function r(e){var t=void 0,i=void 0,n=void 0,r="";t=Math.floor(e);i=parseInt(t/3600,10)%24;n=parseInt(t/60,10)%60;t=t<0?0:t%60;if(i>0){r+=(i<10?"0"+i:i)+":"}r+=(n<10?"0"+n:n)+":"+(t<10?"0"+t:t);return r}},function(e,t,i){e.exports=i(13)},function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.JSPlugin=undefined;var n=function(){function e(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||false;n.configurable=true;if("value"in n)n.writable=true;Object.defineProperty(e,n.key,n)}}return function(t,i,n){if(i)e(t.prototype,i);if(n)e(t,n);return t}}();var r=i(1);var a=d(r);var o=i(3);var s=i(14);var l=i(23);var u=i(24);var f=i(31);var c=i(32);function d(e){return e&&e.__esModule?e:{default:e}}function h(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var p=0;var y=1;var v=40;var m=1024*1024*4;var g=1001;var b=1002;var S=1003;var w=2001;var P=2002;var C=1;var _=function(){function e(t){h(this,e);var i={szId:"playWnd",iWidth:400,iHeight:300,iMaxSplit:4,iCurrentSplit:2,szBasePath:"./",bSupportSound:true,bSupporDoubleClickFull:true,bOnlySupportMSE:false,bOnlySupportJSDecoder:false};this.options=a.default.extend(i,t);var n={border:"#343434",borderSelect:"#FFCC00",background:"#4C4B4B"};n=a.default.extend(n,t.oStyle);this.options.oStyle=n;if(this.options.iCurrentSplit>this.options.iMaxSplit){this.options.iCurrentSplit=this.options.iMaxSplit}this.iCurrentPlayRate=1;this.iCurrentSoundWnd=-1;this.iMaxWndNum=this.options.iMaxSplit*this.options.iMaxSplit;this.bPluginFull=false;this.oVideoWindow=null;this.iCurrentWndIndex=-1;this.fDrawCallback=null;this.szPluginVersion="V1.3.0 build20190821";this.isBrowserVisible=true;this.oStorageManager=new f.StorageManager(this.options.szBasePath+"/transform");this.oStreamClient=new s.StreamClient;this.oJSPlugin=(0,a.default)("#"+this.options.szId);this._listenBrowserVisibility();this._createWindows();this.aWndList=[];for(var r=0;r<this.iMaxWndNum;r++){this.aWndList[r]={};this.aWndList[r].bSelect=false;this.aWndList[r].bPlay=false;this.aWndList[r].bPause=false;this.aWndList[r].bRecord=false;this.aWndList[r].oPlayCtrl=null;if(!/msie/.test(navigator.userAgent.toLowerCase())){this.aWndList[r].oMSE=new u.JMuxmer({node:"playWindow"+r,mode:"video",flushingTime:0})}this.aWndList[r].oPlayCtrlAudio=null;this.aWndList[r].szPlayType="";this.aWndList[r].szStorageUUID="";this.aWndList[r].szStreamUUID="";this.aWndList[r].aHead=[];this.aWndList[r].bLoad=false;this.aWndList[r].windowID="playCanvas"+r;this.aWndList[r].drawID="canvas_draw"+r;this.aWndList[r].iRate=1;this.aWndList[r].bEZoom=false;this.aWndList[r].b3DZoom=false;this.aWndList[r].szSecretKey="";this.aWndList[r].bFrameForward=false;this.aWndList[r].iDecodeType=p;this.aWndList[r].bFirstFrame=false;this.aWndList[r].aSamples=[];this.aWndList[r].iRevBufferTimes=0;this.aWndList[r].nalPackage=[0,0,0,1,97];this.aWndList[r].iMode=0;this.aWndList[r].iCurentMSEOSDTime=-1;this.aWndList[r].oSdpInfo={};this.aWndList[r].iLastDuaration=-1;this.aWndList[r].iCurDuaration=-1;this.aWndList[r].iTimeInterval=-1}this.oTalkPlayCtrl=null;this.aTalkSamples=[];this.aTalkTmp=[];this.aTalkTmp1=[];this.oDrawCanvas=new c.ESCanvas("canvas_draw0");this._initEvent();this.oEventCallback.windowEventSelect(0);this._dealWndSelect(0);this._createPlayCtrl(0)}n(e,[{key:"_listenBrowserVisibility",value:function e(){var t=this;document.addEventListener("visibilitychange",function(){if(document.hidden){t.isBrowserVisible=false;for(var e=0;e<16;e++){if(t.aWndList[e]&&t.aWndList[e].bLoad){t.aWndList[e].oPlayCtrl&&t.aWndList[e].oPlayCtrl.PlayM4_IsVisible(false)}}}else{t.isBrowserVisible=true;for(var i=0;i<16;i++){if(t.aWndList[i]&&t.aWndList[i].bLoad){t.aWndList[i].oPlayCtrl&&t.aWndList[i].oPlayCtrl.PlayM4_IsVisible(true)}}}},false)}},{key:"_createWindows",value:function e(t,i){if(t&&i){this.options.iWidth=t;this.options.iHeight=i}var n=this.options.iWidth%this.options.iCurrentSplit;var r=this.options.iHeight%this.options.iCurrentSplit;var o=(this.options.iWidth-n-this.options.iCurrentSplit*2)/this.options.iCurrentSplit;var s=(this.options.iHeight-r-this.options.iCurrentSplit*2)/this.options.iCurrentSplit;var l=(this.options.iWidth-n)/this.options.iCurrentSplit;var u=(this.options.iHeight-r)/this.options.iCurrentSplit;var f=this.options.iCurrentSplit;this.oJSPlugin=(0,a.default)("#"+this.options.szId);var c='<div class="parent-wnd" style="overflow:hidden;width:100%; height:100%; position: relative;">';for(var d=0;d<this.iMaxWndNum;d++){t=o+(d%f===f-1?n:0);i=s+(d+f>=Math.pow(f,2)?r:0);var h=l+(d%f===f-1?n:0);var p=u+(d+f>=Math.pow(f,2)?r:0);c+='<div style="float:left; background-color: '+this.options.oStyle.background+"; position: relative; width: "+h+"px; height: "+p+'px;">'+'<canvas id="playCanvas'+d+'" class="play-window" wid="'+d+'" width="'+h+'" height="'+p+'"></canvas>'+'<video autoplay muted id="playWindow'+d+'" class="play-window" style="object-fit: fill;" wid="'+d+'" width="'+h+'" height="'+p+'"></video>'+(d===0?'<img style="display:none;" id="playImg'+d+'" src="">':"")+'<canvas id="canvas_draw'+d+'"  class="draw-window" style="border:1px solid '+this.options.oStyle.border+';position:absolute; top:0; left:0;" wid="'+d+'" width='+t+" height="+i+"></canvas>"+"</div>"}c+="</div>";this.oJSPlugin.html(c);this.oJSPlugin.find("video.play-window").hide();this.oJSPlugin.find(".parent-wnd").eq(0).children().eq(0).find(".draw-window").eq(0).css("border","1px solid "+this.options.oStyle.borderSelect)}},{key:"_updateWnd",value:function e(){var t=this.oJSPlugin.find(".parent-wnd").eq(0).children().length;var i=this.options.iWidth%this.options.iCurrentSplit;var n=this.options.iHeight%this.options.iCurrentSplit;var r=(this.options.iWidth-i-this.options.iCurrentSplit*2)/this.options.iCurrentSplit;var a=(this.options.iHeight-n-this.options.iCurrentSplit*2)/this.options.iCurrentSplit;var o=(this.options.iWidth-i)/this.options.iCurrentSplit;var s=(this.options.iHeight-n)/this.options.iCurrentSplit;var l=this.options.iCurrentSplit;for(var u=0;u<t;u++){var f=r+(u%l===l-1?i:0);var c=a+(u+l>=Math.pow(l,2)?n:0);var d=o+(u%l===l-1?i:0);var h=s+(u+l>=Math.pow(l,2)?n:0);this.oJSPlugin.find(".parent-wnd").eq(0).children().eq(u).width(d);this.oJSPlugin.find(".parent-wnd").eq(0).children().eq(u).height(h);this.oJSPlugin.find(".parent-wnd").eq(0).children().eq(u).find(".draw-window").attr("width",f);this.oJSPlugin.find(".parent-wnd").eq(0).children().eq(u).find(".draw-window").attr("height",c);this.oJSPlugin.find(".parent-wnd").eq(0).children().eq(u).find(".play-window").attr("width",d);this.oJSPlugin.find(".parent-wnd").eq(0).children().eq(u).find(".play-window").attr("height",h)}var p=this.iCurrentWndIndex;this.oJSPlugin.find(".parent-wnd").eq(p).find(".draw-window").css("border","1px solid "+this.options.oStyle.border);this.oJSPlugin.find(".parent-wnd").eq(p).children().eq(0).find(".draw-window").eq(0).css("border","1px solid "+this.options.oStyle.borderSelect)}},{key:"_dealWndSelect",value:function e(t){var i=this;if(this.iCurrentWndIndex===t){return}this.iCurrentWndIndex=t;if(this.aWndList[t].bEZoom||this.aWndList[t].b3DZoom){(0,a.default)(".draw-window").unbind();this.oDrawCanvas.setDrawStatus(false);this.oDrawCanvas=null;this.oDrawCanvas=new c.ESCanvas("canvas_draw"+t);this.oDrawCanvas.setShapeType("Rect");this.oDrawCanvas.setDrawStyle("#ff0000","",0);if(this.aWndList[t].bEZoom){this.oDrawCanvas.setDrawStatus(true,function(e){if(e.startPos&&e.endPos){if(e.startPos[0]>e.endPos[0]){i.aWndList[t].oPlayCtrl.PlayM4_SetDisplayRegion&&i.aWndList[t].oPlayCtrl.PlayM4_SetDisplayRegion(null,false)}else{i.aWndList[t].oPlayCtrl.PlayM4_SetDisplayRegion&&i.aWndList[t].oPlayCtrl.PlayM4_SetDisplayRegion({left:e.startPos[0],top:e.startPos[1],right:e.endPos[0],bottom:e.endPos[1]},true)}}})}else if(this.aWndList[t].b3DZoom){this.oDrawCanvas.setDrawStatus(true,function(e){i.fDrawCallback(e)})}}}},{key:"_initCallbackEvent",value:function e(){this.oEventCallback=function(){return{loadEventHandler:function e(){window.loadEventHandler&&window.loadEventHandler()},windowEventSelect:function e(t){window.getSelectWndInfo&&window.getSelectWndInfo(t)},pluginErrorHandler:function e(t,i,n){window.pluginEventHandler&&window.pluginEventHandler(t,i,n)},windowEventOver:function e(t){window.windowEventOver&&window.windowEventOver(t)},windowEventOut:function e(t){window.windowEventOut&&window.windowEventOut(t)},windowEventUp:function e(t){window.windowEventUp&&window.windowEventUp(t)},windowFullCcreenChange:function e(t){window.windowFullCcreenChange&&window.windowFullCcreenChange(t)},firstFrameDisplay:function e(t,i,n){window.firstFrameDisplay&&window.firstFrameDisplay(t,i,n)},performanceLack:function e(){window.performanceLack&&window.performanceLack()},mouseEvent:function e(){}}}()}},{key:"_initEvent",value:function e(){var t=this;this._initCallbackEvent();this.oJSPlugin.find(".parent-wnd").eq(0).children().each(function(e){var i=this;var n=false;(0,a.default)(i).unbind().bind("mousedown",function(){t.oJSPlugin.find(".parent-wnd").eq(0).find(".draw-window").css("border","1px solid "+t.options.oStyle.border);t.oJSPlugin.find(".parent-wnd").eq(0).children().eq(e).find(".draw-window").eq(0).css("border","1px solid "+t.options.oStyle.borderSelect);t.oEventCallback.windowEventSelect(parseInt(t.oJSPlugin.find(".parent-wnd").eq(0).children().eq(e).find(".play-window").eq(0).attr("wid"),10));t._dealWndSelect(parseInt(t.oJSPlugin.find(".parent-wnd").eq(0).children().eq(e).find(".play-window").eq(0).attr("wid"),10))});(0,a.default)(i).bind("mouseover",function(i){t.oEventCallback.windowEventOver(e);i.stopPropagation()});(0,a.default)(i).bind("mouseout",function(i){t.oEventCallback.windowEventOut(e);i.stopPropagation()});(0,a.default)(i).bind("mousedown",function(e){n=true;var i=e.offsetX/t.oDrawCanvas.m_iCanvasWidth;var r=e.offsetY/t.oDrawCanvas.m_iCanvasHeight;if(e.button===2){t.oEventCallback.mouseEvent(4,i,r)}else if(e.button===0){t.oEventCallback.mouseEvent(1,i,r)}e.stopPropagation()});(0,a.default)(i).bind("mousemove",function(e){var i=e.offsetX/t.oDrawCanvas.m_iCanvasWidth;var r=e.offsetY/t.oDrawCanvas.m_iCanvasHeight;if(n){t.oEventCallback.mouseEvent(7,i,r)}else{t.oEventCallback.mouseEvent(6,i,r)}e.stopPropagation()});(0,a.default)(i).bind("mousewheel",function(e){var i=e.offsetX/t.oDrawCanvas.m_iCanvasWidth;var n=e.offsetY/t.oDrawCanvas.m_iCanvasHeight;t.oEventCallback.mouseEvent(8,i,n);e.stopPropagation()});(0,a.default)(i).bind("mouseup",function(i){n=false;t.oEventCallback.windowEventUp(e);var r=i.offsetX/t.oDrawCanvas.m_iCanvasWidth;var a=i.offsetY/t.oDrawCanvas.m_iCanvasHeight;if(i.button===2){t.oEventCallback.mouseEvent(5,r,a)}else if(i.button===0){t.oEventCallback.mouseEvent(3,r,a)}});(0,a.default)(i).bind("dblclick",function(e){var n=t.iCurrentWndIndex;if(!t.aWndList[n].bPlay||!t.options.bSupporDoubleClickFull){return}var r=document.fullscreen||document.webkitIsFullScreen||document.mozFullScreen||false;var o=(0,a.default)(i).get(0);if(!r){if(o.requestFullScreen){o.requestFullScreen()}else if(o.webkitRequestFullScreen){o.webkitRequestFullScreen()}else if(o.mozRequestFullScreen){o.mozRequestFullScreen()}t.oVideoWindow=(0,a.default)(i)}else{if(t.oJSPlugin.find(".parent-wnd").eq(0).width()===(0,a.default)(window).width()){return}if(document.exitFullscreen){document.exitFullscreen()}else if(document.webkitCancelFullScreen){document.webkitCancelFullScreen()}else if(document.mozCancelFullScreen){document.mozCancelFullScreen()}t.oVideoWindow=null;t.bPluginFull=false}var s=e.offsetX/t.oDrawCanvas.m_iCanvasWidth;var l=e.offsetX/t.oDrawCanvas.m_iCanvasHeight;t.oEventCallback.mouseEvent(2,s,l);e.stopPropagation()})});if(typeof document.fullScreen!=="undefined"){document.addEventListener("fullscreenchange",function(){var e=document.fullscreen||false;t.oEventCallback.windowFullCcreenChange(e)})}else if(typeof document.webkitIsFullScreen!=="undefined"){document.addEventListener("webkitfullscreenchange",function(){var e=document.webkitIsFullScreen||false;t.oEventCallback.windowFullCcreenChange(e)})}else if(typeof document.mozFullScreen!=="undefined"){document.addEventListener("mozfullscreenchange",function(){var e=document.mozFullScreen||false;t.oEventCallback.windowFullCcreenChange(e)})}}},{key:"_initAudioPlayCtrl",value:function e(t,i){var n=this;var r=new Uint8Array(i);r[10]=0;r[11]=0;this.aWndList[t].oPlayCtrlAudio.PlayM4_OpenStream(r,v,1024*1024*2);if(this.aWndList[t].szSecretKey!==""){setTimeout(function(){n.aWndList[t].oPlayCtrlAudio.PlayM4_SetSecretKey(1,n.aWndList[t].szSecretKey,128);n.aWndList[t].szSecretKey=""},100)}this.aWndList[t].oPlayCtrlAudio.PlayM4_SetStreamOpenMode(0);this.aWndList[t].oPlayCtrlAudio.PlayM4_SetInputBufSize(m);this.aWndList[t].oPlayCtrlAudio.PlayM4_Play(null)}},{key:"_convertRtpToNal",value:function e(t,i){var n=this;var r=new Uint8Array(i.buf);var a=r[0]&32;var s=r[1]&127;if(s===96){var l="";for(var u=0;u<4;u++){l+=o.oTool.intToHexString(r[4+u])}if(this.aWndList[t].iLastDuaration===-1){this.aWndList[t].iLastDuaration=parseInt(l,16)}this.aWndList[t].iCurDuaration=parseInt(l,16)}if(a===32){var f=r.byteLength;var c=r[f-1];r=r.slice(0,f-c)}if(s===96){var d=parseInt(r[12]&31,10);var h=parseInt(r[12]&96,10);if(h===0){return}if(d<24){if(r[12]===103||r[12]===104){if(r[12]===103){if(this.aWndList[t].aSamples.length>0){var p=(this.aWndList[t].iCurDuaration-this.aWndList[t].iLastDuaration)*1e3/this.aWndList[t].oSdpInfo.iClockFrequency;this.aWndList[t].oMSE.feed({video:new Uint8Array(this.aWndList[t].aSamples),duration:p});this.aWndList[t].iRevBufferTimes=0;this.aWndList[t].aSamples.length=0;this.aWndList[t].iLastDuaration=-1}}var y=r.slice(12);this.aWndList[t].aSamples.push(0);this.aWndList[t].aSamples.push(0);this.aWndList[t].aSamples.push(0);this.aWndList[t].aSamples.push(1);var v=this.aWndList[t].aSamples.length;for(var m=0,g=y.length;m<g;m++){this.aWndList[t].aSamples[v+m]=y[m]}this.aWndList[t].iRevBufferTimes++}else{var b=r.slice(12);this.aWndList[t].aSamples.push(0);this.aWndList[t].aSamples.push(0);this.aWndList[t].aSamples.push(0);this.aWndList[t].aSamples.push(1);var S=this.aWndList[t].aSamples.length;for(var w=0,P=b.length;w<P;w++){this.aWndList[t].aSamples[S+w]=b[w]}this.aWndList[t].iRevBufferTimes++;if(this.aWndList[t].iRevBufferTimes===3){var C=(this.aWndList[t].iCurDuaration-this.aWndList[t].iLastDuaration)*1e3/this.aWndList[t].oSdpInfo.iClockFrequency;this.aWndList[t].oMSE.feed({video:new Uint8Array(this.aWndList[t].aSamples),duration:C});this.aWndList[t].iRevBufferTimes=0;this.aWndList[t].aSamples.length=0;this.aWndList[t].iLastDuaration=-1}}}else if(d===28||d===29){var _=(r[13]&128)>>7;var k=(r[13]&64)>>6;var D=r[12]&224|r[13]&31;if(_===1){this.aWndList[t].nalPackage=[0,0,0,1,D]}var x=r.slice(14);var T=this.aWndList[t].nalPackage.length;for(var M=0,L=x.length;M<L;M++){this.aWndList[t].nalPackage[T+M]=x[M]}if(k===1){var W=this.aWndList[t].aSamples.length;for(var E=0,A=this.aWndList[t].nalPackage.length;E<A;E++){this.aWndList[t].aSamples[W+E]=this.aWndList[t].nalPackage[E]}this.aWndList[t].iRevBufferTimes++;if(this.aWndList[t].iRevBufferTimes===3){var R=(this.aWndList[t].iCurDuaration-this.aWndList[t].iLastDuaration)*1e3/this.aWndList[t].oSdpInfo.iClockFrequency;this.aWndList[t].oMSE.feed({video:new Uint8Array(this.aWndList[t].aSamples),duration:R});this.aWndList[t].iRevBufferTimes=0;this.aWndList[t].aSamples.length=0;this.aWndList[t].iLastDuaration=-1}}}}else if(s===112){if(r[13]===1&&r[16]===64&&r[17]===14){var B=r[22]+2e3;var I=r[23]>>4;var z=(r[23]&15)<<1|r[24]>>7;var O=r[24]>>2&31;var F=(r[24]&3)<<4|r[25]>>4;var N=(r[25]&15)<<2|r[26]>>6;var U=B+"-"+I+"-"+z+" "+O+":"+F+":"+N;clearInterval(this.aWndList[t].iTimeInterval);this.aWndList[t].iCurentMSEOSDTime=new Date(U).getTime();this.aWndList[t].iTimeInterval=setInterval(function(){n.aWndList[t].iCurentMSEOSDTime+=500},500)}return}else{if(this.iCurrentSoundWnd!==-1&&this.options.bSupportSound){this.aWndList[this.iCurrentSoundWnd].oPlayCtrlAudio.PlayM4_InputData(r,r.length)}}}},{key:"_createPlayCtrl",value:function e(t){var i=this;var n=new Promise(function(e){if(!i.aWndList[t].oPlayCtrl){i.aWndList[t].oPlayCtrl=new l.JSPlayCtrl(i.options.szBasePath+"/playctrl/",function(n){if(n.cmd==="loaded"&&!i.aWndList[t].bLoad){i.aWndList[t].bLoad=true;e()}else if(n.cmd==="OnebyOne"){if(!n.status){if(!i.aWndList[t].bPause){i.oStreamClient.pause(i.aWndList[t].szStreamUUID);i.aWndList[t].bPause=true}}else{if(i.aWndList[t].bPause){i.oStreamClient.resume(i.aWndList[t].szStreamUUID);i.aWndList[t].bPause=false}}}else if(n.cmd==="GetFrameData"&&n.errorCode===16){i.oEventCallback.pluginErrorHandler(t,w)}},t)}else{i.aWndList[t].bLoad=true;e()}});return n}},{key:"_createMSE",value:function e(t){var i=this;var n=new Promise(function(e){i.aWndList[t].iCurDuaration=-1;i.aWndList[t].iLastDuaration=-1;if(!i.aWndList[t].oPlayCtrlAudio&&i.options.bSupportSound){i.aWndList[t].oPlayCtrlAudio=new l.JSPlayCtrl(i.options.szBasePath+"/playctrl/",function(t){if(t.cmd==="loaded"){e()}},t)}else{e()}});return n}},{key:"_openStream",value:function e(t,i,n,r,o){var s=this;var l=new Promise(function(e,l){var f=false;if(r&&o){f=true}s.aWndList[n].bLoad=false;s.oStreamClient.openStream(t,i,function(e){if(e.bHead&&!s.aWndList[n].bPlay){s.aWndList[n].bPlay=true;s.aWndList[n].aHead=new Uint8Array(e.buf);if(!s.options.bOnlySupportJSDecoder&&(s.aWndList[n].aHead[11]===1||s.aWndList[n].aHead[10]===1)&&s.aWndList[n].aHead[8]===4||s.options.bOnlySupportMSE){s.aWndList[n].oSdpInfo=e;s._createMSE(n).then(function(){if(s.options.bSupportSound){s._initAudioPlayCtrl(n,s.aWndList[n].aHead)}});var t=(0,a.default)("video.play-window").eq(n);s.aWndList[n].oMSE.destroy();s.aWndList[n].oMSE=new u.JMuxmer({node:"playWindow"+n,mode:"video",flushingTime:0});t.load();t.get(0).play();t.show();setTimeout(function(){s.aWndList[n].bLoad=true},50);(0,a.default)("#playWindow"+n).unbind("loadedmetadata").bind("loadedmetadata",function(){var e=(0,a.default)("video.play-window").eq(n).get(0);var t=e.videoWidth;var i=e.videoHeight;s.oEventCallback.firstFrameDisplay(n,t,i)});s.oJSPlugin.find("video.play-window").eq(n).show();s.oJSPlugin.find("canvas.play-window").eq(n).hide();s.aWndList[n].iMode=1}else{s.oJSPlugin.find("video.play-window").eq(n).hide();s.oJSPlugin.find("canvas.play-window").eq(n).show();s._createPlayCtrl(n).then(function(){s.aWndList[n].oPlayCtrl.PlayM4_OpenStream(s.aWndList[n].aHead,v,1024*1024*2);if(s.aWndList[n].szSecretKey!==""){setTimeout(function(){s.aWndList[n].oPlayCtrl.PlayM4_SetSecretKey(1,s.aWndList[n].szSecretKey,128);s.aWndList[n].szSecretKey=""},100)}if(s.aWndList[n].aHead[8]===4){s.aWndList[n].oPlayCtrl.PlayM4_SetStreamOpenMode(0)}else{s.aWndList[n].oPlayCtrl.PlayM4_SetStreamOpenMode(1)}s.aWndList[n].oPlayCtrl.PlayM4_SetInputBufSize(m);s.aWndList[n].oPlayCtrl.PlayM4_Play(s.aWndList[n].windowID)})}}else{if(!s.aWndList[n].bLoad){return}if(s.aWndList[n].iMode===1){if(!s.isBrowserVisible){return}s._convertRtpToNal(n,e)}else{var i=new Uint8Array(e.buf);var r=s.aWndList[n].oPlayCtrl.PlayM4_GetInputBufSize();var o=s.aWndList[n].oPlayCtrl.PlayM4_GetYUVBufSize();if(o===2&&!s.aWndList[n].bFirstFrame){s.aWndList[n].bFirstFrame=true;s.aWndList[n].oPlayCtrl.PlayM4_GetFrameResolution(function(e,t){s.oEventCallback.firstFrameDisplay(n,e,t)})}var l=s.aWndList[n].oPlayCtrl.PlayM4_GetDecodeFrameType();if(r>m*.5&&r<m*.8&&s.aWndList[n].iRate===1){if(l!==y&&!s.aWndList[n].bFrameForward){s.aWndList[n].oPlayCtrl.PlayM4_SetDecodeFrameType(y);s.oEventCallback.performanceLack()}}else if(r>=m*.8){}if(o>10&&o<15&&!s.aWndList[n].bFrameForward){if(l!==y){s.aWndList[n].oPlayCtrl.PlayM4_SetDecodeFrameType(y);s.oEventCallback.performanceLack()}}else if(o>15){}if(o<10&&r<m*.5){if(l!==p&&s.aWndList[n].iRate===1){s.aWndList[n].oPlayCtrl.PlayM4_SetDecodeFrameType(p)}}if(e.statusString){s.oEventCallback.pluginErrorHandler(n,g,e)}else if(e.type&&e.type==="exception"){s.oEventCallback.pluginErrorHandler(n,b,e)}else{s.aWndList[n].oPlayCtrl.PlayM4_InputData(i,i.length)}}}if(s.aWndList[n].szStorageUUID){s.oStorageManager.inputData(s.aWndList[n].szStorageUUID,e.buf)}e=null},function(){if(s.aWndList[n].bPlay){s.oEventCallback.pluginErrorHandler(n,S);s.aWndList[n].bPlay=false;s.aWndList[n].bFrameForward=false;s.aWndList[n].iRate=1;if(s.aWndList[n].iMode===1){var e=(0,a.default)("video.play-window").eq(n);e.hide();e.get(0).pause&&e.get(0).pause()}else{s.aWndList[n].oPlayCtrl&&s.aWndList[n].oPlayCtrl.PlayM4_Stop();s.aWndList[n].oPlayCtrl&&s.aWndList[n].oPlayCtrl.PlayM4_CloseStream()}}}).then(function(t){s.aWndList[n].szStreamUUID=t;s.oStreamClient.startPlay(t,r,o).then(function(){if(f){s.aWndList[n].szPlayType="playback";s.aWndList[n].iRate=1;s.aWndList[n].oPlayCtrl&&s.aWndList[n].oPlayCtrl.PlayM4_PlayRate(s.aWndList[n].iRate)}else{s.aWndList[n].szPlayType="realplay"}e()},function(e){l(e)})},function(e){l(e)})});return l}},{key:"JS_UpdateWindowStyle",value:function e(t){var i=this;var n=new Promise(function(e){i.options.oStyle=t;i._updateWnd();e()});return n}},{key:"JS_GetPluginVersion",value:function e(){var t=this;var i=new Promise(function(e){e(t.szPluginVersion)});return i}},{key:"JS_SetWindowControlCallback",value:function e(t){var i=this;var n=new Promise(function(e){i.oEventCallback=a.default.extend(i.oEventCallback,t);e()});return n}},{key:"JS_SetSecretKey",value:function e(t,i){var n=this;var r=new Promise(function(e){if(t<0){return-1}if(i===""||typeof i==="undefined"){return-1}n.aWndList[t].szSecretKey=i;e()});return r}},{key:"JS_ArrangeWindow",value:function e(t){var i=this;var n=new Promise(function(e){if(t<i.options.iMaxSplit){i.options.iCurrentSplit=t}else{i.options.iCurrentSplit=i.options.iMaxSplit}if(o.oTool.isFirefox()){for(var n=0;n<i.options.iMaxSplit*i.options.iMaxSplit;n++){if(i.aWndList[n].oPlayCtrl){i.aWndList[n].oPlayCtrl.PlayM4_ClearCanvas&&i.aWndList[n].oPlayCtrl.PlayM4_ClearCanvas()}}}i._updateWnd();i.oEventCallback.windowEventSelect(0);i._dealWndSelect(0);e()});return n}},{key:"JS_Play",value:function e(t,i,n,r,a){var o=this;var s=new Promise(function(e,s){if(n<0||n>o.iMaxWndNum-1){s();return}if(o.aWndList[n].bFrameForward){s();return}if(o.aWndList[n].bPlay){o.JS_Stop(n).then(function(){o.aWndList[n].bFirstFrame=false;o.aWndList[n].iDecodeType=p;o._openStream(t,i,n,r,a).then(function(){e()},function(e){s(e)})})}else{o.aWndList[n].bFirstFrame=false;o.aWndList[n].iDecodeType=p;o._openStream(t,i,n,r,a).then(function(){e()},function(e){s(e)})}});return s}},{key:"JS_Seek",value:function e(t,i,n){var r=this;var o=new Promise(function(e,o){if(t<0||t>r.iMaxWndNum-1){o();return}if(!r.aWndList[t].bPlay){o();return}r.oStreamClient.seek(r.aWndList[t].szStreamUUID,i,n).then(function(){if(r.aWndList[t].iMode===1){var i=(0,a.default)("video.play-window").eq(t);r.aWndList[t].oMSE.destroy();r.aWndList[t].oMSE=new u.JMuxmer({node:"playWindow"+t,mode:"video",flushingTime:0});i.load();i.get(0).playbackRate=r.aWndList[t].iRate;r.aWndList[t].iLastDuaration=-1;r.aWndList[t].iCurDuaration=-1}e()},function(e){o(e)})});return o}},{key:"JS_DestroyWorker",value:function e(){var t=this;var i=new Promise(function(e){t.aWndList.forEach(function(e){if(e.bPlay){e.oPlayCtrl.PlayM4_CloseStream&&e.oPlayCtrl.PlayM4_CloseStream()}if(e.oPlayCtrl){e.oPlayCtrl.PlayM4_Destroy&&e.oPlayCtrl.PlayM4_Destroy();e.oPlayCtrl=null;e.bLoad=false}});e()});return i}},{key:"JS_Stop",value:function e(t){var i=this;var n=new Promise(function(e,n){if(t<0||t>i.iMaxWndNum-1){n();return}if(i.aWndList[t].szStorageUUID){i.JS_StopSave(t)}if(i.aWndList[t].bEZoom){i.JS_DisableZoom(t)}if(i.iCurrentWndIndex===t){i.iCurrentWndIndex=-1}i.oStreamClient.stop(i.aWndList[t].szStreamUUID).then(function(){i.aWndList[t].bPlay=false;i.aWndList[t].bFrameForward=false;i.aWndList[t].iRate=1;if(i.aWndList[t].iMode===1){var n=(0,a.default)("video.play-window").eq(t);n.hide();n.get(0).pause&&n.get(0).pause()}else{if(i.aWndList[t].oPlayCtrl){i.aWndList[t].oPlayCtrl.PlayM4_Stop&&i.aWndList[t].oPlayCtrl.PlayM4_Stop();i.aWndList[t].oPlayCtrl.PlayM4_CloseStream&&i.aWndList[t].oPlayCtrl.PlayM4_CloseStream()}}setTimeout(function(){e()},500)},function(){setTimeout(function(){n()},500)})});return n}},{key:"JS_Pause",value:function e(t){var i=this;var n=new Promise(function(e,n){if(t<0||t>i.iMaxWndNum-1){n();return}if(!i.aWndList[t].bPlay){n();return}if(i.aWndList[t].bFrameForward){n();return}i.oStreamClient.pause(i.aWndList[t].szStreamUUID).then(function(){if(i.aWndList[t].iMode===1){var n=(0,a.default)("video.play-window").eq(t);n.get(0).pause&&n.get(0).pause()}else{i.aWndList[t].oPlayCtrl.PlayM4_Pause&&i.aWndList[t].oPlayCtrl.PlayM4_Pause(true)}i.aWndList[t].bPause=true;e()},function(e){n(e)})});return n}},{key:"JS_Resume",value:function e(t){var i=this;var n=new Promise(function(e,n){if(t<0||t>i.iMaxWndNum-1){n();return}if(!i.aWndList[t].bPlay){n();return}i.oStreamClient.resume(i.aWndList[t].szStreamUUID).then(function(){if(i.aWndList[t].iMode===1){var n=(0,a.default)("video.play-window").eq(t);n.get(0).play&&n.get(0).play()}else{if(i.iCurrentPlayRate!==1){i.aWndList[t].iRate=i.iCurrentPlayRate;i.oStreamClient.setPlayRate(i.aWndList[t].szStreamUUID,i.aWndList[t].iRate);i.aWndList[t].oPlayCtrl.PlayM4_PlayRate&&i.aWndList[t].oPlayCtrl.PlayM4_PlayRate(i.aWndList[t].iRate);if(i.iCurrentPlayRate>1){i.aWndList[t].oPlayCtrl.PlayM4_SetDecodeFrameType&&i.aWndList[t].oPlayCtrl.PlayM4_SetDecodeFrameType(y)}else{i.aWndList[t].oPlayCtrl.PlayM4_SetDecodeFrameType&&i.aWndList[t].oPlayCtrl.PlayM4_SetDecodeFrameType(p)}}if(i.aWndList[t].bFrameForward){i.aWndList[t].oPlayCtrl.PlayM4_Play&&i.aWndList[t].oPlayCtrl.PlayM4_Play(i.aWndList[t].windowID);i.aWndList[t].bFrameForward=false}else{i.aWndList[t].oPlayCtrl&&i.aWndList[t].oPlayCtrl.PlayM4_Pause(false)}}i.aWndList[t].bPause=false;e()},function(e){n(e)})});return n}},{key:"JS_Slow",value:function e(t){var i=this;var n=new Promise(function(e,n){if(t<0||t>i.iMaxWndNum-1){n();return}if(!i.aWndList[t].bPlay){n();return}if(i.aWndList[t].szPlayType!=="playback"){n();return}if(i.aWndList[t].iRate===-8){n();return}if(i.aWndList[t].bFrameForward){n();return}if(i.aWndList[t].iRate<0&&i.aWndList[t].iRate>-8){i.aWndList[t].iRate*=2}if(i.aWndList[t].iRate===1){i.aWndList[t].iRate*=-2}if(i.aWndList[t].iRate>1){i.aWndList[t].iRate/=2}i.oStreamClient.setPlayRate(i.aWndList[t].szStreamUUID,i.aWndList[t].iRate).then(function(){if(i.aWndList[t].iMode===1){var n=(0,a.default)("video.play-window").eq(t).get(0);if(i.aWndList[t].iRate<0){n.playbackRate=1/Math.abs(i.aWndList[t].iRate)}else{n.playbackRate=i.aWndList[t].iRate}}else{if(i.aWndList[t].iRate<2){i.aWndList[t].oPlayCtrl.PlayM4_SetDecodeFrameType&&i.aWndList[t].oPlayCtrl.PlayM4_SetDecodeFrameType(p)}else{i.aWndList[t].oPlayCtrl.PlayM4_SetDecodeFrameType(y);i.aWndList[t].oPlayCtrl.PlayM4_SetIFrameDecInterval&&i.aWndList[t].oPlayCtrl.PlayM4_SetIFrameDecInterval(0)}i.aWndList[t].oPlayCtrl.PlayM4_PlayRate&&i.aWndList[t].oPlayCtrl.PlayM4_PlayRate(i.aWndList[t].iRate)}e()},function(e){n(e)})});return n}},{key:"JS_Fast",value:function e(t){var i=this;var n=new Promise(function(e,n){if(t<0||t>i.iMaxWndNum-1){n();return}if(!i.aWndList[t].bPlay){n();return}if(i.aWndList[t].szPlayType!=="playback"){n();return}if(i.aWndList[t].bFrameForward){n();return}if(i.aWndList[t].iRate===8){n();return}if(i.aWndList[t].iRate===-2){i.aWndList[t].iRate=1}else if(i.aWndList[t].iRate<-2){i.aWndList[t].iRate/=2}else if(i.aWndList[t].iRate>0&&i.aWndList[t].iRate<8){i.aWndList[t].iRate*=2}i.oStreamClient.setPlayRate(i.aWndList[t].szStreamUUID,i.aWndList[t].iRate).then(function(){if(i.aWndList[t].iMode===1){var n=(0,a.default)("video.play-window").eq(t).get(0);if(i.aWndList[t].iRate<0){n.playbackRate=1/Math.abs(i.aWndList[t].iRate)}else{n.playbackRate=i.aWndList[t].iRate}}else{if(i.aWndList[t].iRate<2){i.aWndList[t].oPlayCtrl.PlayM4_SetDecodeFrameType&&i.aWndList[t].oPlayCtrl.PlayM4_SetDecodeFrameType(p)}else{i.aWndList[t].oPlayCtrl.PlayM4_SetDecodeFrameType&&i.aWndList[t].oPlayCtrl.PlayM4_SetDecodeFrameType(y);if(i.aWndList[t].iRate===8){i.aWndList[t].oPlayCtrl.PlayM4_SetIFrameDecInterval&&i.aWndList[t].oPlayCtrl.PlayM4_SetIFrameDecInterval(2)}else{i.aWndList[t].oPlayCtrl.PlayM4_SetIFrameDecInterval&&i.aWndList[t].oPlayCtrl.PlayM4_SetIFrameDecInterval(0)}}i.aWndList[t].oPlayCtrl.PlayM4_PlayRate&&i.aWndList[t].oPlayCtrl.PlayM4_PlayRate(i.aWndList[t].iRate)}e()},function(e){n(e)})});return n}},{key:"JS_Transmission",value:function e(t,i){var n=this;var r=new Promise(function(e,r){if(t<0||t>n.iMaxWndNum-1){r();return}if(!n.aWndList[t].szStreamUUID){r();return}n.oStreamClient.transmission(n.aWndList[t].szStreamUUID,i).then(function(t){e(t)},function(e){r(e)})});return r}},{key:"JS_FrameForward",value:function e(t,i){var n=this;var r=new Promise(function(e,r){if(!i){i=1}if(t<0||t>n.iMaxWndNum-1){r();return}if(!n.aWndList[t].bPlay){r();return}if(n.aWndList[t].iMode===1){r();return}if(n.aWndList[t].iRate!==1){n.aWndList[t].iRate=1;n.iCurrentPlayRate=n.aWndList[t].iRate;n.oStreamClient.setPlayRate(n.aWndList[t].szStreamUUID,n.aWndList[t].iRate).then(function(){n.aWndList[t].oPlayCtrl.PlayM4_PlayRate&&n.aWndList[t].oPlayCtrl.PlayM4_PlayRate(n.aWndList[t].iRate);n.aWndList[t].oPlayCtrl.PlayM4_SetDecodeFrameType&&n.aWndList[t].oPlayCtrl.PlayM4_SetDecodeFrameType(p);n.aWndList[t].oPlayCtrl.PlayM4_OneByOne&&n.aWndList[t].oPlayCtrl.PlayM4_OneByOne(i);n.aWndList[t].bFrameForward=true},function(e){r(e)})}else{n.aWndList[t].oPlayCtrl.PlayM4_PlayRate&&n.aWndList[t].oPlayCtrl.PlayM4_PlayRate(n.aWndList[t].iRate);n.aWndList[t].oPlayCtrl.PlayM4_SetDecodeFrameType&&n.aWndList[t].oPlayCtrl.PlayM4_SetDecodeFrameType(p);n.aWndList[t].oPlayCtrl.PlayM4_OneByOne&&n.aWndList[t].oPlayCtrl.PlayM4_OneByOne(i);n.aWndList[t].bFrameForward=true}e()});return r}},{key:"JS_GetOSDTime",value:function e(t){var i=this;var n=new Promise(function(e,n){if(t<0||t>i.iMaxWndNum-1){n(-1);return}if(!i.aWndList[t].bPlay){n(-1);return}if(i.aWndList[t].iMode===1){e(i.aWndList[t].iCurentMSEOSDTime);return}i.aWndList[t].oPlayCtrl.PlayM4_GetOSDTime&&i.aWndList[t].oPlayCtrl.PlayM4_GetOSDTime(function(t){var i=o.oTool.isSafari()||o.oTool.isEdge()?"/":" ";var n=Date.parse(t.replace(/-/g,i));e(n)})});return n}},{key:"JS_OpenSound",value:function e(t){var i=this;var n=new Promise(function(e,n){if(t<0||t>i.iMaxWndNum-1){n();return}if(!i.aWndList[t].bPlay){n();return}var r=i.iCurrentSoundWnd;if(r===t){n();return}if(i.aWndList[t].iMode===1){}else{if(r!==-1){i.aWndList[r].oPlayCtrl.PlayM4_StopSound&&i.aWndList[r].oPlayCtrl.PlayM4_StopSound()}if(i.aWndList[t].oPlayCtrl.PlayM4_PlaySound&&i.aWndList[t].oPlayCtrl.PlayM4_PlaySound(t)!==C){n();return}}i.iCurrentSoundWnd=t;e()});return n}},{key:"JS_GetVolume",value:function e(t){var i=this;var n=new Promise(function(e){if(i.aWndList[t].iMode===1){var n=(0,a.default)("video.play-window").eq(t).get(0);var r=n.volume*100;e(r)}else{i.aWndList[t].oPlayCtrl.PlayM4_GetVolume&&i.aWndList[t].oPlayCtrl.PlayM4_GetVolume(function(t){e(t)})}});return n}},{key:"JS_SetVolume",value:function e(t,i){var n=this;var r=new Promise(function(e,r){if(n.aWndList[t].iMode===1){var o=(0,a.default)("video.play-window").eq(t).get(0);o.volume=i/100;e()}else{if(n.aWndList[t].oPlayCtrl.PlayM4_SetVolume&&n.aWndList[t].oPlayCtrl.PlayM4_SetVolume(i)!==C){r()}else{e()}}});return r}},{key:"JS_CloseSound",value:function e(){var t=this;var i=new Promise(function(e,i){var n=t.iCurrentSoundWnd;if(n<0||n>t.iMaxWndNum-1){i();return}if(!t.aWndList[n].bPlay){i();return}if(t.aWndList[n].iMode===1){t.iCurrentSoundWnd=-1;e()}else{if(t.aWndList[n].oPlayCtrl.PlayM4_StopSound&&t.aWndList[n].oPlayCtrl.PlayM4_StopSound()!==C){i()}else{t.iCurrentSoundWnd=-1;e()}}});return i}},{key:"JS_EnableZoom",value:function e(t){var i=this;var n=new Promise(function(e,n){if(t<0||t>i.iMaxWndNum-1){n();return}if(!i.aWndList[t].bPlay){n();return}if(i.aWndList[t].iMode===1){n();return}(0,a.default)(".draw-window").unbind();i.oDrawCanvas=new c.ESCanvas("canvas_draw"+t);i.oDrawCanvas.setShapeType("Rect");i.oDrawCanvas.setDrawStyle("#ff0000","",0);i.oDrawCanvas.setDrawStatus(true,function(e){if(e.startPos&&e.endPos){if(e.startPos[0]>e.endPos[0]){i.aWndList[t].oPlayCtrl.PlayM4_SetDisplayRegion&&i.aWndList[t].oPlayCtrl.PlayM4_SetDisplayRegion(null,false)}else{i.aWndList[t].oPlayCtrl.PlayM4_SetDisplayRegion&&i.aWndList[t].oPlayCtrl.PlayM4_SetDisplayRegion({left:e.startPos[0],top:e.startPos[1],right:e.endPos[0],bottom:e.endPos[1]},true)}}});i.aWndList[t].bEZoom=true;e()});return n}},{key:"JS_DisableZoom",value:function e(t){var i=this;var n=new Promise(function(e,n){if(t<0||t>i.iMaxWndNum-1){n();return}if(!i.aWndList[t].bPlay){n();return}if(i.aWndList[t].iMode===1){n();return}i.oDrawCanvas.setDrawStatus(false);if(i.aWndList[t].oPlayCtrl.PlayM4_SetDisplayRegion&&i.aWndList[t].oPlayCtrl.PlayM4_SetDisplayRegion(null,false)!==C){n();return}i.aWndList[t].bEZoom=false;e()});return n}},{key:"JS_Enable3DZoom",value:function e(t,i){var n=this;var r=new Promise(function(e,r){if(t<0||t>n.iMaxWndNum-1){r();return}if(!n.aWndList[t].bPlay){r();return}(0,a.default)(".draw-window").unbind();n.fDrawCallback=i;n.oDrawCanvas=new c.ESCanvas("canvas_draw"+t);n.oDrawCanvas.setShapeType("Rect");n.oDrawCanvas.setDrawStyle("#ff0000","",0);n.oDrawCanvas.setDrawStatus(true,function(e){i(e)});n.aWndList[t].b3DZoom=true;e()});return r}},{key:"JS_Disable3DZoom",value:function e(t){var i=this;var n=new Promise(function(e,n){if(t<0||t>i.iMaxWndNum-1){n();return}if(!i.aWndList[t].bPlay){n();return}i.oDrawCanvas.setDrawStatus(false);i.aWndList[t].b3DZoom=false;e()});return n}},{key:"JS_CapturePicture",value:function e(t,i,n,r){var s=this;var l=new Promise(function(e,l){if(t<0||t>s.iMaxWndNum-1){l();return}if(!s.aWndList[t].bPlay){l();return}if(!n){n="JPEG"}if(s.aWndList[t].iMode===1){var u=".jpeg";if(n==="BMP"){u=".BMP"}var f=(0,a.default)("video.play-window").eq(t).get(0);var c=document.createElement("canvas");c.width=f.videoWidth;c.height=f.videoHeight;c.getContext("2d").drawImage(f,0,0,c.width,c.height);var d=c.toDataURL();var h=o.oTool.dataURLtoBlob(d);if(r){r(h)}else{o.oTool.downloadFile(h,i+u)}}else{if(n==="BMP"){s.aWndList[t].oPlayCtrl.PlayM4_GetBMP&&s.aWndList[t].oPlayCtrl.PlayM4_GetBMP(function(t){if(t===6){l(P)}else{if(r){r(t)}else{o.oTool.downloadFile(t,i+".BMP")}e()}})}else if(n==="JPEG"){s.aWndList[t].oPlayCtrl.PlayM4_GetJPEG&&s.aWndList[t].oPlayCtrl.PlayM4_GetJPEG(function(t){if(t===6){l(P)}else{if(r){r(t)}else{o.oTool.downloadFile(t,i+".jpeg")}e()}})}}});return l}},{key:"JS_StopRealPlayAll",value:function e(){var t=this;var i=new Promise(function(e){t.oStreamClient.stopAll();t.aWndList.forEach(function(e,i){if(e.bPlay){if(e.szStorageUUID){t.JS_StopSave(i)}if(e.bEZoom){t.JS_DisableZoom(i)}if(t.aWndList[i].iMode===1){var n=(0,a.default)("video.play-window").eq(i);n.hide();n.get(0).pause&&n.get(0).pause()}else{e.oPlayCtrl.PlayM4_Stop&&e.oPlayCtrl.PlayM4_Stop();e.oPlayCtrl.PlayM4_CloseStream&&e.oPlayCtrl.PlayM4_CloseStream()}}e.bPlay=false});t.iCurrentSoundWnd=-1;e()});return i}},{key:"JS_StartSave",value:function e(t,i,n){var r=this;var a=new Promise(function(e,a){var o=0;if(n){o=n.iPackage}if(t<0||t>r.iMaxWndNum-1){a();return}if(!r.aWndList[t].bPlay){a();return}if(i.indexOf(".mp4")<0){i=i+".mp4"}var s=r.aWndList[t].aHead;var l=0;if(r.aWndList[t].szPlayType==="playback"){l=1}r.oStorageManager.startRecord(i,s,2,l,{cbEventHandler:function e(i){r.oEventCallback.pluginErrorHandler(t,i)},iPackage:o}).then(function(i){r.aWndList[t].szStorageUUID=i;e()},function(){a()})});return a}},{key:"JS_StartTalk",value:function e(t,i,n,r,a,s){var u=this;var f=new Promise(function(e,f){var c="";var d=false;var h=window.navigator;h.getUserMedia=h.getUserMedia||h.webkitGetUserMedia||h.mozGetUserMedia||h.msGetUserMedia;var p=window.AudioContext||window.webkitAudioContext;var y=new p;var g=false;if(u.oTalkPlayCtrl===null){u.oTalkPlayCtrl=new l.JSPlayCtrl(u.options.szBasePath+"/playctrl/",function(e){if(e.cmd==="loaded"){if(!g){u.oTalkPlayCtrl.PlayM4_CreateAudEncode(n);u.oTalkPlayCtrl.PlayM4_SetAudEncodeParam(a,i,r,s);u.oTalkPlayCtrl.PlayM4_RegisterAudEncodeCB(function(e){var t=u.aTalkTmp.length;for(var i=0,n=e.length;i<n;i++){u.aTalkTmp[t+i]=e[i]}if(u.aTalkTmp.length===96e3){o.oTool.downloadFile(new Uint8Array(u.aTalkTmp),"test.audio")}if(d){u.oStreamClient.transmission(c,e)}})}g=true}},20)}else{g=true;u.oTalkPlayCtrl.PlayM4_CreateAudEncode(n);u.oTalkPlayCtrl.PlayM4_SetAudEncodeParam(a,i,r,s);u.oTalkPlayCtrl.PlayM4_RegisterAudEncodeCB(function(e){if(d){u.oStreamClient.transmission(c,e)}})}var b=t.indexOf("/proxy/")+7;var S=t.indexOf("/dac/talk/");var w=t.substring(b,S);var P=t.replace("wss:","ws:").replace(w,"").replace("/proxy/","");u.oStreamClient.openStream(t,{playURL:P,proxy:w,mode:"media"},function(e){if(e.bHead){var t=new Uint8Array(e.buf);t[10]=0;t[11]=0;u.oTalkPlayCtrl.PlayM4_OpenStream(t,v,1024*1024*2);u.oTalkPlayCtrl.PlayM4_SetStreamOpenMode(0);u.oTalkPlayCtrl.PlayM4_SetInputBufSize(m);u.oTalkPlayCtrl.PlayM4_Play(null)}else{u.oTalkPlayCtrl.PlayM4_InputData(e.buf,e.buf.length)}},function(){}).then(function(t){c=t;u.oStreamClient.startPlay(t,"","","voicetalk").then(function(){d=true;e()},function(e){f(e)})},function(e){f(e)});h.getUserMedia({audio:true},function(e){if(!d){return}var t=y.createMediaStreamSource(e);var i=512;var n=t.context;var r=n.createScriptProcessor(i,1,1);var l=[];r.onaudioprocess=function(e){var t=e.inputBuffer.getChannelData(0);var i=48e3/a;var n=t.length/i;var r=new window.Float32Array(n);var f=0;var c=0;while(f<n){r[f]=t[c];c+=i;f++}var d=r.length;var h=null;if(s===16){var p=new window.ArrayBuffer(d*2);h=new window.DataView(p);var y=0;for(var v=0;v<r.length;v++,y+=2){var m=Math.max(-1,Math.min(1,r[v]));h.setInt16(y,m<0?m*32768:m*32767,true)}}else if(s===8){var b=new window.ArrayBuffer(d);h=new window.DataView(b);var S=0;for(var w=0;w<r.length;w++,S++){var P=Math.max(-1,Math.min(1,r[w]));var C=P<0?P*32768:P*32767;C=parseInt(255/(65535/(C+32768)),10);h.setInt8(S,C,true)}}if(g){var _=new Uint8Array(h.buffer);var k=l.length;for(var D=0,x=_.byteLength;D<x;D++){l[k++]=_[D];if(l.length===640){var T=u.aTalkTmp1.length;for(var M=0,L=l.length;M<L;M++){u.aTalkTmp1[T+M]=l[M]}if(u.aTalkTmp1.length===64e3){o.oTool.downloadFile(new Uint8Array(u.aTalkTmp1),"PCM.audio")}u.oTalkPlayCtrl.PlayM4_InputAudEncodeData(new Uint8Array(l),640);l.length=0;k=0}}}};t.connect(r);r.connect(n.destination)},function(){f()})});return f}},{key:"JS_StopTalk",value:function e(){}},{key:"JS_StopSave",value:function e(t){var i=this;var n=new Promise(function(e,n){if(!i.aWndList[t].szStorageUUID){n();return}i.oStorageManager.stopRecord(i.aWndList[t].szStorageUUID).then(function(){i.aWndList[t].szStorageUUID="";e()},function(e){n(e)})});return n}},{key:"JS_SetDrawStatus",value:function e(t){var i=this;var n=new Promise(function(e,n){if(!i.oDrawCanvas){n();return}i.oDrawCanvas.setDrawStatus(t);e()});return n}},{key:"JS_ClearRegion",value:function e(){var t=this;var i=new Promise(function(e,i){if(!t.oDrawCanvas){i();return}t.oDrawCanvas.clearAllShape();e()});return i}},{key:"JS_SetGridInfo",value:function e(t){var i=this;var n=new Promise(function(e){if(t===null||typeof t==="undefined"){return-1}var n="#ff0000";if(t.drawColor){n=t.drawColor}i.oDrawCanvas.setDrawStyle(n);i.oDrawCanvas.setShapesInfoByType("Grid",[{szGridMap:t.gridMap,iGridColNum:t.gridColNum,iGridRowNum:t.gridRowNum}]);e()});return n}},{key:"JS_GetGridInfo",value:function e(){var t=this;var i=new Promise(function(e){if(!t.oDrawCanvas){e({})}var i=t.oDrawCanvas.getShapesInfoByType("Grid")[0];if(!i){e({iGridRowNum:18,iGridColNum:22,szGridMap:""})}e({gridColNum:i.iGridColNum,gridRowNum:i.iGridRowNum,gridMap:i.szGridMap})});return i}},{key:"JS_SetDrawShapeInfo",value:function e(t,i){var n=this;var r=new Promise(function(e,r){if(typeof t==="undefined"||t===""){r();return}n.oDrawCanvas.setShapeType(t);n.oDrawCanvas.setDrawStyle(i.szDrawColor||"",i.szFillColor||"",i.iTranslucent||0);if(i.iMaxShapeSupport&&i.iMaxShapeSupport>0){n.oDrawCanvas.setMaxShapeSupport(i.iMaxShapeSupport)}if(i.iMaxPointSupport&&i.iMaxPointSupport>0){n.oDrawCanvas.setCurrentShapeInfo({szId:"",szTips:"",iMinClosed:3,iMaxPointNum:i.iMaxPointSupport,iPolygonType:1,szDrawColor:i.szDrawColor||"",szFillColor:i.szFillColor||"",iTranslucent:i.iTranslucent||0})}e()});return r}},{key:"JS_SetPolygonInfo",value:function e(t){var i=this;var n=new Promise(function(e,n){if(typeof t==="undefined"||!t.length){n();return}var r=[];if(t.length>0){for(var a=0,o=t.length;a<o;a++){var s=t[a].aPoint;if(s.length>0){r.push(t[a])}}}if(r.length>0){i.oDrawCanvas.setShapesInfoByType("Polygon",r);e()}else{n()}});return n}},{key:"JS_GetPolygonInfo",value:function e(){var t=this;var i=new Promise(function(e){var i=[];var n=t.oDrawCanvas.getShapesInfoByType("Polygon");for(var r=0,a=n.length;r<a;r++){var o=n[r];var s={aPoint:o.aPoint,bClosed:o.bClosed,szTips:o.szTips};i.push(s)}e(i)});return i}},{key:"JS_SetLineInfo",value:function e(t){var i=this;var n=new Promise(function(e,n){if(typeof t==="undefined"||!t.length){n();return}var r=[];if(t.length>0){for(var a=0,o=t.length;a<o;a++){var s=t[a].aPoint;if(s.length>0){r.push(t[a])}}}if(r.length>0){i.oDrawCanvas.setShapesInfoByType("Line",r);e()}else{n()}});return n}},{key:"JS_GetLineInfo",value:function e(){var t=this;var i=new Promise(function(e){var i=[];var n=t.oDrawCanvas.getShapesInfoByType("Line");for(var r=0,a=n.length;r<a;r++){var o=n[r];var s={iLineType:o.iLineType,aPoint:o.aPoint,szTips:o.szTips};i.push(s)}e(i)});return i}},{key:"JS_SetRectInfo",value:function e(t){var i=this;var n=new Promise(function(e,n){if(typeof t==="undefined"||!t.length){n();return}var r=[];if(t.length>0){for(var a=0,o=t.length;a<o;a++){var s=t[a].aPoint;if(s.length>0){r.push(t[a])}}}if(r.length>0){i.oDrawCanvas.setShapesInfoByType("Rect",r);e()}else{n()}});return n}},{key:"JS_GetRectInfo",value:function e(){var t=this;var i=new Promise(function(e){var i=[];var n=t.oDrawCanvas.getShapesInfoByType("Rect");for(var r=0,a=n.length;r<a;r++){var o=n[r];var s={aPoint:o.aPoint,szTips:o.szTips};i.push(s)}e(i)});return i}},{key:"JS_FullScreenDisplay",value:function e(t){var i=this;var n=new Promise(function(e){if(t){var n=i.oJSPlugin.get(0);if(n.requestFullScreen){n.requestFullScreen()}else if(n.webkitRequestFullScreen){n.webkitRequestFullScreen()}else if(n.mozRequestFullScreen){n.mozRequestFullScreen()}}i.bPluginFull=t;e()});return n}},{key:"JS_FullScreenSingle",value:function e(t){var i=this;var n=new Promise(function(e,n){if(!i.aWndList[t].bPlay){n();return}var r=document.fullscreen||document.webkitIsFullScreen||document.mozFullScreen||false;var o=i.oJSPlugin.find(".parent-wnd").eq(0).children().eq(t).get(0);if(!r){if(o.requestFullScreen){o.requestFullScreen()}else if(o.webkitRequestFullScreen){o.webkitRequestFullScreen()}else if(o.mozRequestFullScreen){o.mozRequestFullScreen()}i.oVideoWindow=i.oJSPlugin.find(".parent-wnd").eq(0).children().eq(t)}else{if(i.oJSPlugin.find(".parent-wnd").eq(0).width()===(0,a.default)(window).width()){n();return}if(document.exitFullscreen){document.exitFullscreen()}else if(document.webkitCancelFullScreen){document.webkitCancelFullScreen()}else if(document.mozCancelFullScreen){document.mozCancelFullScreen()}i.oVideoWindow=null;i.bPluginFull=false}e()});return n}},{key:"JS_Resize",value:function e(t,i){var n=this;var r=new Promise(function(e){setTimeout(function(){var r=document.fullscreen||document.webkitIsFullScreen||document.mozFullScreen||false;if(n.bPluginFull&&r){t=(0,a.default)(window).width();i=(0,a.default)(window).height();n.oJSPlugin.css({width:t,height:i})}else{n.oJSPlugin.css({width:t,height:i})}n.options.iWidth=t;n.options.iHeight=i;if(o.oTool.isFirefox()){for(var s=0;s<n.options.iMaxSplit*n.options.iMaxSplit;s++){if(n.aWndList[s].oPlayCtrl){n.aWndList[s].oPlayCtrl.PlayM4_ClearCanvas&&n.aWndList[s].oPlayCtrl.PlayM4_ClearCanvas()}}}n._updateWnd();if(n.oVideoWindow&&r){t=(0,a.default)(window).width();i=(0,a.default)(window).height();n.oVideoWindow.css({width:t,height:i});n.oVideoWindow.find(".play-window").attr("width",t);n.oVideoWindow.find(".play-window").attr("height",i);n.oVideoWindow.find(".draw-window").attr("width",t-2);n.oVideoWindow.find(".draw-window").attr("height",i-2)}if(!r){n.oVideoWindow=null;n.bPluginFull=false}n.oDrawCanvas.resizeCanvas();n.oDrawCanvas.canvasRedraw();e()},80)});return r}},{key:"JS_WndCreate",value:function e(t,i){var n=this;var r=new Promise(function(e){n._createWindows(t,i);n.oDrawCanvas.updateCanvas("canvas_draw0");n.oDrawCanvas.clearAllShape();n.oEventCallback.windowEventSelect(0);n._dealWndSelect(0);n._initEvent();e()});return r}},{key:"JS_GetWndContainer",value:function e(t){var i=this;var n=new Promise(function(e,n){if(t<0||typeof t==="undefined"||t===null){n();return}e(i.oJSPlugin.find(".parent-wnd").eq(0).children().eq(t)[0])});return n}},{key:"JS_GetWndStatus",value:function e(t){var i=this;var n=new Promise(function(e,n){if(t<0||typeof t==="undefined"||t===null){n();return}var r={bPlay:i.aWndList[t].bPlay,bSound:i.iCurrentSoundWnd===t,bSelect:i.aWndList[t].bSelect,iRate:i.aWndList[t].iRate};e(r)});return n}},{key:"JS_SelectWnd",value:function e(t){var i=this;var n=new Promise(function(e){i.oJSPlugin.find(".parent-wnd").eq(0).children().eq(t).mousedown();e()});return n}},{key:"JS_SetOptions",value:function e(t){var i=this;var n=new Promise(function(e){if(t.bSupportSound===false){i.options.bSupportSound=t.bSupportSound}if(t.bSupporDoubleClickFull===false){i.options.bSupporDoubleClickFull=t.bSupporDoubleClickFull}if(t.bOnlySupportMSE===true){i.options.bOnlySupportMSE=t.bOnlySupportMSE}if(t.bOnlySupportJSDecoder===true){i.options.bOnlySupportJSDecoder=t.bOnlySupportJSDecoder}e()});return n}},{key:"JS_PlayWithImg",value:function e(t){var i=this;var n=new Promise(function(e){var n=(0,a.default)("#playCanvas0");var r=(0,a.default)("#playWindow0");var o=n.width();var s=n.height();n.hide();r.hide();var l=(0,a.default)("#playImg0");l.show();l.css({width:o+"px",height:s+"px",border:"1px solid "+i.options.oStyle.border});l.attr("src",t);e()});return n}},{key:"JS_OpenPlayerSDKPrintLog",value:function e(t,i){var n=this;var r=new Promise(function(e){if(n.aWndList[t].oPlayCtrlAudio){n.aWndList[t].oPlayCtrlAudio.PlayM4_OpenPlayerSDKPrintLog(i)}else if(n.aWndList[t].oPlayCtrl){n.aWndList[t].oPlayCtrl.PlayM4_OpenPlayerSDKPrintLog(i)}e()});return r}},{key:"JS_DownloadYUVdata",value:function e(t){var i=this;var n=new Promise(function(e){i.aWndList[t].oPlayCtrl.PlayM4_DownloadYUVdata();e()});return n}},{key:"JS_DownloadPCMdata",value:function e(t){var i=this;var n=new Promise(function(e){if(i.aWndList[t].oPlayCtrlAudio){i.aWndList[t].oPlayCtrlAudio.PlayM4_DownloadPCMdata()}else if(i.aWndList[t].oPlayCtrl){i.aWndList[t].oPlayCtrl.PlayM4_DownloadPCMdata()}e()});return n}},{key:"JS_SetCurrentFrameNum",value:function e(t,i,n){var r=this;var a=new Promise(function(e){r.aWndList[t].oPlayCtrl.PlayM4_SetCurrentFrameNum(i,n);e()});return a}},{key:"JS_SetDecCallBack",value:function e(t,i){var n=this;var r=new Promise(function(e,r){if(!n.aWndList[t].bLoad){r();return}n.aWndList[t].oPlayCtrl.PlayM4_SetDecCallBack(i);e()});return r}},{key:"JS_SetDisplayCallBack",value:function e(t,i){var n=this;var r=new Promise(function(e,r){if(!n.aWndList[t].bLoad){r();return}n.aWndList[t].oPlayCtrl.PlayM4_SetDisplayCallBack(i);e()});return r}},{key:"JS_SetPCMCallBack",value:function e(t,i){var n=this;var r=new Promise(function(e,r){if(n.aWndList[t].iMode===1){if(!n.aWndList[t].oPlayCtrlAudio){r();return}n.aWndList[t].oPlayCtrlAudio.PlayM4_SetPCMCallBack(i);e()}else{if(!n.aWndList[t].bLoad){r();return}n.aWndList[t].oPlayCtrl.PlayM4_SetPCMCallBack(i);e()}});return r}}]);return e}();t.JSPlugin=_},function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.StreamClient=undefined;var n=function(){function e(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||false;n.configurable=true;if("value"in n)n.writable=true;Object.defineProperty(e,n.key,n)}}return function(t,i,n){if(i)e(t.prototype,i);if(n)e(t,n);return t}}();var r=i(4);var a=f(r);var o=i(17);var s=i(18);var l=i(19);var u=i(22);function f(e){return e&&e.__esModule?e:{default:e}}function c(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var d=function(){var e=new o.DirectDeviceCustom;var t=new s.DirectDevice;var i=new l.LiveMedia;var r=new u.LocalService;var f=function(){function o(){var e=this;c(this,o);this.szProtocolVersion="0.1";this.iCiphersuites=0;this.aWebsocket=[];this.ERRORS={};this.getIndex=function(t){var i=-1;for(var n=0,r=e.aWebsocket.length;n<r;n++){if(e.aWebsocket[n].id===t){i=n;break}}return i}}n(o,[{key:"openStream",value:function n(o,s,l,u){var f=this;var c=false;if(o.indexOf("[")>-1){c=true}var d=o.split("://");var h=d[0];var p="";var y=7681;var v=1;var m=0;if(c){p=d[1].split("]:")[0]+"]";y=Math.floor(d[1].split("]:")[1].split("/")[0]);v=Math.floor(d[1].split("]:")[1].split("/")[1]/100);m=Math.floor(d[1].split("]:")[1].split("/")[1]%100)-1}else{p=d[1].split(":")[0];y=Math.floor(d[1].split(":")[1].split("/")[0]);v=Math.floor(d[1].split(":")[1].split("/")[1]/100);m=Math.floor(d[1].split(":")[1].split("/")[1]%100)-1}if(v===0){m=0}s=s||{};var g="&sessionID=";if(s.token&&!s.playURL){g="&token="}var b=s.sessionID||s.session||(s.playURL?"":s.token)||"";var S=new window.WebSocket(h+"://"+p+":"+y+(s.mode?"/"+s.mode:"")+"?version="+this.szProtocolVersion+"&cipherSuites="+this.iCiphersuites+g+b+(s.proxy?"&proxy="+s.proxy:""));S.binaryType="arraybuffer";var w=a.default.v4();var P=new Promise(function(n,a){S.onopen=function(){if(!s.playURL&&!s.sessionID&&!s.deviceSerial&&!s.token){f.aWebsocket.push(e.createClientObject(S,w,v,m));n(w)}};S.onmessage=function(e){if(typeof e.data==="string"){var o=JSON.parse(e.data);var u=f.getIndex(w);if(o&&o.version&&o.cipherSuite){f.szProtocolVersion=o.version;f.iCiphersuites=parseInt(o.cipherSuite,10);if(o&&o.PKD&&o.rand){f.aWebsocket.push(i.createClientObject(S,w,o.PKD,o.rand,s))}else{var c="live://"+p+":"+y+"/"+v+"/"+m;if(f.iCiphersuites===-1){f.aWebsocket.push(r.createClientObject(S,w,c,s))}else{f.aWebsocket.push(t.createClientObject(S,w,c))}}n(w);return}if(o&&o.sdp){var d=t.getMediaFromSdp(o.sdp);var h=t.getSDPInfo(o.sdp);l({bHead:true,buf:d,iClockFrequency:h.iClockFrequency,iCurrentTime:h.iCurrentTime})}if(o&&o.cmd){if(o.cmd==="end"){l({type:"exception",cmd:o.cmd})}}if(o&&o.statusString){if(o.statusString.toLowerCase()==="ok"){if(f.aWebsocket[u].resolve){f.aWebsocket[u].resolve(o)}}if(o.statusString.toLowerCase()!=="ok"){var g=t.getError(o);if(u>-1){if(f.aWebsocket[u].reject){f.aWebsocket[u].reject(g)}}else{a(g)}}}}else{var b={};var P=new Uint8Array(e.data);if(P.byteLength===64||P.byteLength===40){var C=-1;var _=P.byteLength;for(var k=0;k<_;k++){if(P[k]===73&&P[k+1]===77&&P[k+2]===75&&P[k+3]===72){C=k;break}}if(C!==-1){var D=P.slice(C,C+40);b={bHead:true,buf:D}}else{b={bHead:false,buf:P}}}else{b={bHead:false,buf:P}}l(b);P=null;b=null;e=null}};S.onclose=function(){for(var e=0,t=f.aWebsocket.length;e<t;e++){if(f.aWebsocket[e].id===w){if(f.aWebsocket[e].stoping){f.aWebsocket[e].stoping=false;f.aWebsocket[e].resolve();f.aWebsocket.splice(e,1)}else{setTimeout(function(){u()},1e3)}break}}a()}});return P}},{key:"startPlay",value:function n(a,o,s,l){var u=this;var f=this.getIndex(a);if(o&&s&&this.szProtocolVersion==="0.1"){o=o.replace(/-/g,"").replace(/:/g,"");s=s.replace(/-/g,"").replace(/:/g,"")}var c=new Promise(function(n,a){if(f>-1){u.aWebsocket[f].resolve=n;u.aWebsocket[f].reject=a;var c=null;if(!o||!s){if(u.aWebsocket[f].iCurChannel===0&&u.szProtocolVersion==="0.1"){c=e.zeroPlayCmd(u.aWebsocket[f].iCurChannel,u.aWebsocket[f].iCurStream)}else{if(u.szProtocolVersion!=="0.1"){if(u.iCiphersuites===0){c=i.playCmd(u.aWebsocket[f],l)}else if(u.iCiphersuites===1){c=t.playCmd(u.aWebsocket[f].playURL)}else if(u.iCiphersuites===-1){c=r.playCmd(u.aWebsocket[f])}}else{c=e.playCmd(u.aWebsocket[f].iCurChannel,u.aWebsocket[f].iCurStream)}}}else{if(u.szProtocolVersion!=="0.1"){if(u.iCiphersuites===0){c=i.playbackCmd(u.aWebsocket[f],o,s)}else if(u.iCiphersuites===1){c=t.playbackCmd(o,s,u.aWebsocket[f].playURL)}else if(u.iCiphersuites===-1){c=r.playbackCmd(u.aWebsocket[f],o,s)}}else{c=e.playbackCmd(o,s,u.aWebsocket[f].iCurChannel,u.aWebsocket[f].iCurStream)}}u.aWebsocket[f].socket.send(c);if(u.szProtocolVersion==="0.1"){n()}}else{if(u.szProtocolVersion==="0.1"){a()}}});return c}},{key:"singleFrame",value:function e(){}},{key:"setPlayRate",value:function i(n,r){var a=this;var o=new Promise(function(i,o){for(var s=0,l=a.aWebsocket.length;s<l;s++){if(a.aWebsocket[s].id===n){if(a.szProtocolVersion==="0.1"){var u=e.playRateCmd(r);a.aWebsocket[s].socket.send(u);i();break}else{a.aWebsocket[s].resolve=i;a.aWebsocket[s].reject=o;var f=t.playRateCmd(r);a.aWebsocket[s].socket.send(f)}}}});return o}},{key:"seek",value:function e(t,n,r){var a=this;var o=new Promise(function(e,o){for(var s=0,l=a.aWebsocket.length;s<l;s++){if(a.aWebsocket[s].id===t){a.aWebsocket[s].resolve=e;a.aWebsocket[s].reject=o;var u=i.seekCmd(n,r);a.aWebsocket[s].socket.send(u)}}});return o}},{key:"pause",value:function i(n){var r=this;var a=new Promise(function(i,a){for(var o=0,s=r.aWebsocket.length;o<s;o++){if(r.aWebsocket[o].id===n){if(r.szProtocolVersion==="0.1"){var l=e.pauseCmd();r.aWebsocket[o].socket.send(l);i();break}else{r.aWebsocket[o].resolve=i;r.aWebsocket[o].reject=a;var u=t.pauseCmd();r.aWebsocket[o].socket.send(u)}}}});return a}},{key:"transmission",value:function e(t,i){var n=this;var r=new Promise(function(e,r){for(var a=0,o=n.aWebsocket.length;a<o;a++){if(n.aWebsocket[a].id===t){n.aWebsocket[a].resolve=e;n.aWebsocket[a].reject=r;n.aWebsocket[a].socket.send(i)}}});return r}},{key:"resume",value:function i(n){var r=this;var a=new Promise(function(i,a){for(var o=0,s=r.aWebsocket.length;o<s;o++){if(r.aWebsocket[o].id===n){if(r.szProtocolVersion==="0.1"){var l=e.resumeCmd();r.aWebsocket[o].socket.send(l);i();break}else{r.aWebsocket[o].resolve=i;r.aWebsocket[o].reject=a;var u=t.resumeCmd();r.aWebsocket[o].socket.send(u)}}}});return a}},{key:"stop",value:function e(t){var i=this;var n=new Promise(function(e,n){if(!t){n()}else{var r=-1;for(var a=0,o=i.aWebsocket.length;a<o;a++){if(i.aWebsocket[a].id===t){r=a;i.aWebsocket[a].resolve=e;i.aWebsocket[a].socket.close(1e3,"CLOSE");i.aWebsocket[a].stoping=true;break}}if(r===-1){n()}}});return n}},{key:"stopAll",value:function e(){for(var t=0,i=this.aWebsocket.length;t<i;t++){this.aWebsocket[t].socket.close(1e3,"CLOSE");this.aWebsocket[t].stoping=true}}}]);return o}();return f}();t.StreamClient=d},function(e,t,i){var n=i(5);var r=i(6);var a;var o;var s=0;var l=0;function u(e,t,i){var u=t&&i||0;var f=t||[];e=e||{};var c=e.node||a;var d=e.clockseq!==undefined?e.clockseq:o;if(c==null||d==null){var h=n();if(c==null){c=a=[h[0]|1,h[1],h[2],h[3],h[4],h[5]]}if(d==null){d=o=(h[6]<<8|h[7])&16383}}var p=e.msecs!==undefined?e.msecs:(new Date).getTime();var y=e.nsecs!==undefined?e.nsecs:l+1;var v=p-s+(y-l)/1e4;if(v<0&&e.clockseq===undefined){d=d+1&16383}if((v<0||p>s)&&e.nsecs===undefined){y=0}if(y>=1e4){throw new Error("uuid.v1(): Can't create more than 10M uuids/sec")}s=p;l=y;o=d;p+=122192928e5;var m=((p&268435455)*1e4+y)%4294967296;f[u++]=m>>>24&255;f[u++]=m>>>16&255;f[u++]=m>>>8&255;f[u++]=m&255;var g=p/4294967296*1e4&268435455;f[u++]=g>>>8&255;f[u++]=g&255;f[u++]=g>>>24&15|16;f[u++]=g>>>16&255;f[u++]=d>>>8|128;f[u++]=d&255;for(var b=0;b<6;++b){f[u+b]=c[b]}return t?t:r(f)}e.exports=u},function(e,t,i){var n=i(5);var r=i(6);function a(e,t,i){var a=t&&i||0;if(typeof e=="string"){t=e==="binary"?new Array(16):null;e=null}e=e||{};var o=e.random||(e.rng||n)();o[6]=o[6]&15|64;o[8]=o[8]&63|128;if(t){for(var s=0;s<16;++s){t[a+s]=o[s]}}return t||r(o)}e.exports=a},function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:true});var n=function(){function e(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||false;n.configurable=true;if("value"in n)n.writable=true;Object.defineProperty(e,n.key,n)}}return function(t,i,n){if(i)e(t.prototype,i);if(n)e(t,n);return t}}();function r(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var a=function(){var e=function(){function e(){r(this,e)}n(e,[{key:"createClientObject",value:function e(t,i,n,r){return{socket:t,id:i,iCurChannel:n,iCurStream:r,resolve:null,reject:null,stoping:false}}},{key:"zeroPlayCmd",value:function e(t,i){var n=[0,0,0,44,0,0,0,0,0,0,0,0,0,19,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,t+1,0,0,0,i,0,0,4,0];return new Uint8Array(n)}},{key:"playCmd",value:function e(t,i){var n=[0,0,0,44,0,0,0,0,0,0,0,0,0,3,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,t,0,0,0,i,0,0,4,0];return new Uint8Array(n)}},{key:"playbackCmd",value:function e(t,i,n,r){var a=t.split("T")[0];var o=t.split("T")[1];var s="0"+parseInt(a.substring(0,4),10).toString(16);var l=parseInt(a.substring(4,6),10);var u=parseInt(a.substring(6),10);var f=parseInt(o.substring(0,2),10);var c=parseInt(o.substring(2,4),10);var d=parseInt(o.substring(4,6),10);var h=i.split("T")[0];var p=i.split("T")[1];var y="0"+parseInt(h.substring(0,4),10).toString(16);var v=parseInt(h.substring(4,6),10);var m=parseInt(p.substring(0,2),10);var g=parseInt(p.substring(2,4),10);var b=parseInt(p.substring(4,6),10);var S=[0,0,0,96,0,0,0,0,0,0,0,0,0,3,1,2,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,n,0,0,parseInt(s.substring(0,2),16),parseInt(s.substring(2,4),16),0,0,0,l,0,0,0,u,0,0,0,f,0,0,0,c,0,0,0,d,0,0,parseInt(y.substring(0,2),16),parseInt(y.substring(2,4),16),0,0,0,v,0,0,0,u,0,0,0,m,0,0,0,g,0,0,0,b,0,0,0,0,0,0,0,0,r,0,0,0];return new Uint8Array(S)}},{key:"playRateCmd",value:function e(t){var i=(parseInt(t,10)>>>0).toString(16).toLocaleUpperCase().toString(16);for(var n=i.length;n<8;n++){i="0"+i}var r=[0,0,0,0];for(var a=0,o=i.length;a<o;a=a+2){r[Math.floor(a/2)]=parseInt(i.substring(a,a+2),16)}var s=[0,0,0,36,0,0,0,0,0,0,0,0,0,3,1,47,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,r[0],r[1],r[2],r[3]];return new Uint8Array(s)}},{key:"pauseCmd",value:function e(){var t=[0,0,0,32,0,0,0,0,0,0,0,0,0,3,1,7,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];return new Uint8Array(t)}},{key:"resumeCmd",value:function e(){var t=[0,0,0,32,0,0,0,0,0,0,0,0,0,3,1,8,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];return new Uint8Array(t)}}]);return e}();return e}();t.DirectDeviceCustom=a},function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:true});var n=function(){function e(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||false;n.configurable=true;if("value"in n)n.writable=true;Object.defineProperty(e,n.key,n)}}return function(t,i,n){if(i)e(t.prototype,i);if(n)e(t,n);return t}}();function r(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var a=3001;var o=3002;var s=3003;var l=function(){var e=function(){function e(){r(this,e)}n(e,[{key:"createClientObject",value:function e(t,i,n){return{socket:t,id:i,playURL:n,resolve:null,reject:null,stoping:false}}},{key:"getMediaFromSdp",value:function e(t){var i=t.indexOf("MEDIAINFO=")+10;var n=t.slice(i,i+80);var r=[];for(var a=0,o=n.length/2;a<o;a++){r[a]=parseInt(n.slice(a*2,a*2+2),16)}return new Uint8Array(r)}},{key:"getSDPInfo",value:function e(t){var i=0;var n=t.indexOf("rtpmap:96 H264/")+15;var r=t.slice(n,n+5);i=parseInt(r,10);var a=0;var o=t.indexOf("o=- ")+4;var s=t.slice(o,o+16);a=parseInt(s,10)/1e3;var l=(new Date).getTimezoneOffset()*60*1e3;a=Math.floor(a+l);return{iClockFrequency:i,iCurrentTime:a}}},{key:"playCmd",value:function e(t){var i={sequence:0,cmd:"realplay",url:t};return JSON.stringify(i)}},{key:"playbackCmd",value:function e(t,i,n){var r={sequence:0,cmd:"playback",url:n,startTime:t,endTime:i};return JSON.stringify(r)}},{key:"playRateCmd",value:function e(t){var i={sequence:0,cmd:"speed",rate:t};return JSON.stringify(i)}},{key:"pauseCmd",value:function e(){var t={sequence:0,cmd:"pause"};return JSON.stringify(t)}},{key:"resumeCmd",value:function e(){var t={sequence:0,cmd:"resume"};return JSON.stringify(t)}},{key:"getError",value:function e(t){var i=a;if(t){if(parseInt(t.statusCode,10)===6&&t.subStatusCode==="streamLimit"){i=o}else if(parseInt(t.statusCode,10)===4&&t.subStatusCode==="badAuthorization"){i=s}}return{iErrorNum:i,oError:t}}}]);return e}();return e}();t.DirectDevice=l},function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.LiveMedia=undefined;var n=function(){function e(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||false;n.configurable=true;if("value"in n)n.writable=true;Object.defineProperty(e,n.key,n)}}return function(t,i,n){if(i)e(t.prototype,i);if(n)e(t,n);return t}}();var r=i(20);var a=l(r);var o=i(21);var s=l(o);function l(e){return e&&e.__esModule?e:{default:e}}function u(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var f=function(){var e=function(){function e(){u(this,e)}n(e,[{key:"createClientObject",value:function e(t,i,n,r,a){var o=s.default.AES.encrypt((new Date).getTime().toString(),s.default.enc.Hex.parse("1234567891234567123456789123456712345678912345671234567891234567"),{mode:s.default.mode.CBC,iv:s.default.enc.Hex.parse("12345678912345671234567891234567"),padding:s.default.pad.Pkcs7}).ciphertext.toString();if(o.length<64){o=o+o}var l=s.default.AES.encrypt((new Date).getTime().toString(),s.default.enc.Hex.parse("12345678912345671234567891234567"),{mode:s.default.mode.CBC,iv:s.default.enc.Hex.parse("12345678912345671234567891234567"),padding:s.default.pad.Pkcs7}).ciphertext.toString();return{socket:t,id:i,PKD:n,rand:r,playURL:a.playURL||"",auth:a.auth||"",token:a.token||"",key:o,iv:l,resolve:null,reject:null,stoping:false}}},{key:"playCmd",value:function e(t,i){var n={sequence:0,cmd:i||"realplay",url:t.playURL,key:a.default.encrypt(t.iv+":"+t.key,t.PKD).cipher.split("?")[0],authorization:s.default.AES.encrypt(t.rand+":"+t.auth,s.default.enc.Hex.parse(t.key),{mode:s.default.mode.CBC,iv:s.default.enc.Hex.parse(t.iv),padding:s.default.pad.Pkcs7}).ciphertext.toString(),token:s.default.AES.encrypt(t.token,s.default.enc.Hex.parse(t.key),{mode:s.default.mode.CBC,iv:s.default.enc.Hex.parse(t.iv),padding:s.default.pad.Pkcs7}).ciphertext.toString()};return JSON.stringify(n)}},{key:"playbackCmd",value:function e(t,i,n){var r={sequence:0,cmd:"playback",url:t.playURL,key:a.default.encrypt(t.iv+":"+t.key,t.PKD).cipher.split("?")[0],authorization:s.default.AES.encrypt(t.rand+":"+t.auth,s.default.enc.Hex.parse(t.key),{mode:s.default.mode.CBC,iv:s.default.enc.Hex.parse(t.iv),padding:s.default.pad.Pkcs7}).ciphertext.toString(),token:s.default.AES.encrypt(t.token,s.default.enc.Hex.parse(t.key),{mode:s.default.mode.CBC,iv:s.default.enc.Hex.parse(t.iv),padding:s.default.pad.Pkcs7}).ciphertext.toString(),startTime:i,endTime:n};return JSON.stringify(r)}},{key:"seekCmd",value:function e(t,i){var n={sequence:0,cmd:"seek",startTime:t,endTime:i};return JSON.stringify(n)}}]);return e}();return e}();t.LiveMedia=f},function(e,t,n){"use strict";var r=typeof Symbol==="function"&&typeof Symbol.iterator==="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol==="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};var a={appName:"Netscape",appVersion:40};var o,s=0xdeadbeefcafe,l=(s&16777215)==15715070;function u(e,t,i){e!=null&&("number"==typeof e?this.fromNumber(e,t,i):t==null&&"string"!=typeof e?this.fromString(e,256):this.fromString(e,t))}function f(){return new u(null)}function c(e,t,i,n,r,a){for(;--a>=0;){var o=t*this[e++]+i[n]+r,r=Math.floor(o/67108864);i[n++]=o&67108863}return r}function d(e,t,i,n,r,a){var o=t&32767;for(t>>=15;--a>=0;){var s=this[e]&32767,l=this[e++]>>15,u=t*s+l*o,s=o*s+((u&32767)<<15)+i[n]+(r&1073741823),r=(s>>>30)+(u>>>15)+t*l+(r>>>30);i[n++]=s&1073741823}return r}function h(e,t,i,n,r,a){var o=t&16383;for(t>>=14;--a>=0;){var s=this[e]&16383,l=this[e++]>>14,u=t*s+l*o,s=o*s+((u&16383)<<14)+i[n]+r,r=(s>>28)+(u>>14)+t*l;i[n++]=s&268435455}return r}l&&a.appName=="Microsoft Internet Explorer"?(u.prototype.am=d,o=30):l&&a.appName!="Netscape"?(u.prototype.am=c,o=26):(u.prototype.am=h,o=28);u.prototype.DB=o;u.prototype.DM=(1<<o)-1;u.prototype.DV=1<<o;var p=52;u.prototype.FV=Math.pow(2,p);u.prototype.F1=p-o;u.prototype.F2=2*o-p;var y="0123456789abcdefghijklmnopqrstuvwxyz",m=[],g,b;g="0".charCodeAt(0);for(b=0;b<=9;++b){m[g++]=b}g="a".charCodeAt(0);for(b=10;b<36;++b){m[g++]=b}g="A".charCodeAt(0);for(b=10;b<36;++b){m[g++]=b}function S(e){return y.charAt(e)}function w(e,t){var i=m[e.charCodeAt(t)];return i==null?-1:i}function P(e){for(var t=this.t-1;t>=0;--t){e[t]=this[t]}e.t=this.t;e.s=this.s}function C(e){this.t=1;this.s=e<0?-1:0;e>0?this[0]=e:e<-1?this[0]=e+DV:this.t=0}function _(e){var t=f();t.fromInt(e);return t}function k(e,t){var i;if(t==16)i=4;else if(t==8)i=3;else if(t==256)i=8;else if(t==2)i=1;else if(t==32)i=5;else if(t==4)i=2;else{this.fromRadix(e,t);return}this.s=this.t=0;for(var n=e.length,r=!1,a=0;--n>=0;){var o=i==8?e[n]&255:w(e,n);o<0?e.charAt(n)=="-"&&(r=!0):(r=!1,a==0?this[this.t++]=o:a+i>this.DB?(this[this.t-1]|=(o&(1<<this.DB-a)-1)<<a,this[this.t++]=o>>this.DB-a):this[this.t-1]|=o<<a,a+=i,a>=this.DB&&(a-=this.DB))}if(i==8&&(e[0]&128)!=0)this.s=-1,a>0&&(this[this.t-1]|=(1<<this.DB-a)-1<<a);this.clamp();r&&u.ZERO.subTo(this,this)}function D(){for(var e=this.s&this.DM;this.t>0&&this[this.t-1]==e;){--this.t}}function x(e){if(this.s<0)return"-"+this.negate().toString(e);if(e==16)e=4;else if(e==8)e=3;else if(e==2)e=1;else if(e==32)e=5;else if(e==64)e=6;else if(e==4)e=2;else return this.toRadix(e);var t=(1<<e)-1,i,n=!1,r="",a=this.t,o=this.DB-a*this.DB%e;if(a-- >0){if(o<this.DB&&(i=this[a]>>o)>0)n=!0,r=S(i);for(;a>=0;){o<e?(i=(this[a]&(1<<o)-1)<<e-o,i|=this[--a]>>(o+=this.DB-e)):(i=this[a]>>(o-=e)&t,o<=0&&(o+=this.DB,--a)),i>0&&(n=!0),n&&(r+=S(i))}}return n?r:"0"}function T(){var e=f();u.ZERO.subTo(this,e);return e}function M(){return this.s<0?this.negate():this}function L(e){var t=this.s-e.s;if(t!=0)return t;var i=this.t,t=i-e.t;if(t!=0)return t;for(;--i>=0;){if((t=this[i]-e[i])!=0)return t}return 0}function W(e){var t=1,i;if((i=e>>>16)!=0)e=i,t+=16;if((i=e>>8)!=0)e=i,t+=8;if((i=e>>4)!=0)e=i,t+=4;if((i=e>>2)!=0)e=i,t+=2;e>>1!=0&&(t+=1);return t}function E(){return this.t<=0?0:this.DB*(this.t-1)+W(this[this.t-1]^this.s&this.DM)}function A(e,t){var i;for(i=this.t-1;i>=0;--i){t[i+e]=this[i]}for(i=e-1;i>=0;--i){t[i]=0}t.t=this.t+e;t.s=this.s}function R(e,t){for(var i=e;i<this.t;++i){t[i-e]=this[i]}t.t=Math.max(this.t-e,0);t.s=this.s}function B(e,t){var i=e%this.DB,n=this.DB-i,r=(1<<n)-1,a=Math.floor(e/this.DB),o=this.s<<i&this.DM,s;for(s=this.t-1;s>=0;--s){t[s+a+1]=this[s]>>n|o,o=(this[s]&r)<<i}for(s=a-1;s>=0;--s){t[s]=0}t[a]=o;t.t=this.t+a+1;t.s=this.s;t.clamp()}function I(e,t){t.s=this.s;var i=Math.floor(e/this.DB);if(i>=this.t)t.t=0;else{var n=e%this.DB,r=this.DB-n,a=(1<<n)-1;t[0]=this[i]>>n;for(var o=i+1;o<this.t;++o){t[o-i-1]|=(this[o]&a)<<r,t[o-i]=this[o]>>n}n>0&&(t[this.t-i-1]|=(this.s&a)<<r);t.t=this.t-i;t.clamp()}}function z(e,t){for(var i=0,n=0,r=Math.min(e.t,this.t);i<r;){n+=this[i]-e[i],t[i++]=n&this.DM,n>>=this.DB}if(e.t<this.t){for(n-=e.s;i<this.t;){n+=this[i],t[i++]=n&this.DM,n>>=this.DB}n+=this.s}else{for(n+=this.s;i<e.t;){n-=e[i],t[i++]=n&this.DM,n>>=this.DB}n-=e.s}t.s=n<0?-1:0;n<-1?t[i++]=this.DV+n:n>0&&(t[i++]=n);t.t=i;t.clamp()}function O(e,t){var i=this.abs(),n=e.abs(),r=i.t;for(t.t=r+n.t;--r>=0;){t[r]=0}for(r=0;r<n.t;++r){t[r+i.t]=i.am(0,n[r],t,r,0,i.t)}t.s=0;t.clamp();this.s!=e.s&&u.ZERO.subTo(t,t)}function F(e){for(var t=this.abs(),i=e.t=2*t.t;--i>=0;){e[i]=0}for(i=0;i<t.t-1;++i){var n=t.am(i,t[i],e,2*i,0,1);if((e[i+t.t]+=t.am(i+1,2*t[i],e,2*i+1,n,t.t-i-1))>=t.DV)e[i+t.t]-=t.DV,e[i+t.t+1]=1}e.t>0&&(e[e.t-1]+=t.am(i,t[i],e,2*i,0,1));e.s=0;e.clamp()}function N(e,t,i){var n=e.abs();if(!(n.t<=0)){var r=this.abs();if(r.t<n.t)t!=null&&t.fromInt(0),i!=null&&this.copyTo(i);else{i==null&&(i=f());var a=f(),o=this.s,e=e.s,s=this.DB-W(n[n.t-1]);s>0?(n.lShiftTo(s,a),r.lShiftTo(s,i)):(n.copyTo(a),r.copyTo(i));n=a.t;r=a[n-1];if(r!=0){var l=r*(1<<this.F1)+(n>1?a[n-2]>>this.F2:0),c=this.FV/l,l=(1<<this.F1)/l,d=1<<this.F2,h=i.t,p=h-n,y=t==null?f():t;a.dlShiftTo(p,y);i.compareTo(y)>=0&&(i[i.t++]=1,i.subTo(y,i));u.ONE.dlShiftTo(n,y);for(y.subTo(a,a);a.t<n;){a[a.t++]=0}for(;--p>=0;){var v=i[--h]==r?this.DM:Math.floor(i[h]*c+(i[h-1]+d)*l);if((i[h]+=a.am(0,v,i,p,0,n))<v){a.dlShiftTo(p,y);for(i.subTo(y,i);i[h]<--v;){i.subTo(y,i)}}}t!=null&&(i.drShiftTo(n,t),o!=e&&u.ZERO.subTo(t,t));i.t=n;i.clamp();s>0&&i.rShiftTo(s,i);o<0&&u.ZERO.subTo(i,i)}}}}function U(e){var t=f();this.abs().divRemTo(e,null,t);this.s<0&&t.compareTo(u.ZERO)>0&&e.subTo(t,t);return t}function j(e){this.m=e}function H(e){return e.s<0||e.compareTo(this.m)>=0?e.mod(this.m):e}function q(e){return e}function V(e){e.divRemTo(this.m,null,e)}function J(e,t,i){e.multiplyTo(t,i);this.reduce(i)}function G(e,t){e.squareTo(t);this.reduce(t)}j.prototype.convert=H;j.prototype.revert=q;j.prototype.reduce=V;j.prototype.mulTo=J;j.prototype.sqrTo=G;function Y(){if(this.t<1)return 0;var e=this[0];if((e&1)==0)return 0;var t=e&3,t=t*(2-(e&15)*t)&15,t=t*(2-(e&255)*t)&255,t=t*(2-((e&65535)*t&65535))&65535,t=t*(2-e*t%this.DV)%this.DV;return t>0?this.DV-t:-t}function X(e){this.m=e;this.mp=e.invDigit();this.mpl=this.mp&32767;this.mph=this.mp>>15;this.um=(1<<e.DB-15)-1;this.mt2=2*e.t}function K(e){var t=f();e.abs().dlShiftTo(this.m.t,t);t.divRemTo(this.m,null,t);e.s<0&&t.compareTo(u.ZERO)>0&&this.m.subTo(t,t);return t}function $(e){var t=f();e.copyTo(t);this.reduce(t);return t}function Z(e){for(;e.t<=this.mt2;){e[e.t++]=0}for(var t=0;t<this.m.t;++t){var i=e[t]&32767,n=i*this.mpl+((i*this.mph+(e[t]>>15)*this.mpl&this.um)<<15)&e.DM,i=t+this.m.t;for(e[i]+=this.m.am(0,n,e,t,0,this.m.t);e[i]>=e.DV;){e[i]-=e.DV,e[++i]++}}e.clamp();e.drShiftTo(this.m.t,e);e.compareTo(this.m)>=0&&e.subTo(this.m,e)}function Q(e,t){e.squareTo(t);this.reduce(t)}function ee(e,t,i){e.multiplyTo(t,i);this.reduce(i)}X.prototype.convert=K;X.prototype.revert=$;X.prototype.reduce=Z;X.prototype.mulTo=ee;X.prototype.sqrTo=Q;function te(){return(this.t>0?this[0]&1:this.s)==0}function ie(e,t){if(e>4294967295||e<1)return u.ONE;var i=f(),n=f(),r=t.convert(this),a=W(e)-1;for(r.copyTo(i);--a>=0;){if(t.sqrTo(i,n),(e&1<<a)>0)t.mulTo(n,r,i);else var o=i,i=n,n=o}return t.revert(i)}function ne(e,t){var i;i=e<256||t.isEven()?new j(t):new X(t);return this.exp(e,i)}u.prototype.copyTo=P;u.prototype.fromInt=C;u.prototype.fromString=k;u.prototype.clamp=D;u.prototype.dlShiftTo=A;u.prototype.drShiftTo=R;u.prototype.lShiftTo=B;u.prototype.rShiftTo=I;u.prototype.subTo=z;u.prototype.multiplyTo=O;u.prototype.squareTo=F;u.prototype.divRemTo=N;u.prototype.invDigit=Y;u.prototype.isEven=te;u.prototype.exp=ie;u.prototype.toString=x;u.prototype.negate=T;u.prototype.abs=M;u.prototype.compareTo=L;u.prototype.bitLength=E;u.prototype.mod=U;u.prototype.modPowInt=ne;u.ZERO=_(0);u.ONE=_(1);function re(){var e=f();this.copyTo(e);return e}function ae(){if(this.s<0){if(this.t==1)return this[0]-this.DV;else{if(this.t==0)return-1}}else if(this.t==1)return this[0];else if(this.t==0)return 0;return(this[1]&(1<<32-this.DB)-1)<<this.DB|this[0]}function oe(){return this.t==0?this.s:this[0]<<24>>24}function se(){return this.t==0?this.s:this[0]<<16>>16}function le(e){return Math.floor(Math.LN2*this.DB/Math.log(e))}function ue(){return this.s<0?-1:this.t<=0||this.t==1&&this[0]<=0?0:1}function fe(e){e==null&&(e=10);if(this.signum()==0||e<2||e>36)return"0";var t=this.chunkSize(e),t=Math.pow(e,t),i=_(t),n=f(),r=f(),a="";for(this.divRemTo(i,n,r);n.signum()>0;){a=(t+r.intValue()).toString(e).substr(1)+a,n.divRemTo(i,n,r)}return r.intValue().toString(e)+a}function ce(e,t){this.fromInt(0);t==null&&(t=10);for(var i=this.chunkSize(t),n=Math.pow(t,i),r=!1,a=0,o=0,s=0;s<e.length;++s){var l=w(e,s);l<0?e.charAt(s)=="-"&&this.signum()==0&&(r=!0):(o=t*o+l,++a>=i&&(this.dMultiply(n),this.dAddOffset(o,0),o=a=0))}a>0&&(this.dMultiply(Math.pow(t,a)),this.dAddOffset(o,0));r&&u.ZERO.subTo(this,this)}function de(e,t,i){if("number"==typeof t){if(e<2)this.fromInt(1);else{this.fromNumber(e,i);this.testBit(e-1)||this.bitwiseTo(u.ONE.shiftLeft(e-1),Se,this);for(this.isEven()&&this.dAddOffset(1,0);!this.isProbablePrime(t);){this.dAddOffset(2,0),this.bitLength()>e&&this.subTo(u.ONE.shiftLeft(e-1),this)}}}else{var i=[],n=e&7;i.length=(e>>3)+1;t.nextBytes(i);n>0?i[0]&=(1<<n)-1:i[0]=0;this.fromString(i,256)}}function he(){var e=this.t,t=[];t[0]=this.s;var i=this.DB-e*this.DB%8,n,r=0;if(e-- >0){if(i<this.DB&&(n=this[e]>>i)!=(this.s&this.DM)>>i)t[r++]=n|this.s<<this.DB-i;for(;e>=0;){if(i<8?(n=(this[e]&(1<<i)-1)<<8-i,n|=this[--e]>>(i+=this.DB-8)):(n=this[e]>>(i-=8)&255,i<=0&&(i+=this.DB,--e)),(n&128)!=0&&(n|=-256),r==0&&(this.s&128)!=(n&128)&&++r,r>0||n!=this.s)t[r++]=n}}return t}function pe(e){return this.compareTo(e)==0}function ye(e){return this.compareTo(e)<0?this:e}function ve(e){return this.compareTo(e)>0?this:e}function me(e,t,i){var n,r,a=Math.min(e.t,this.t);for(n=0;n<a;++n){i[n]=t(this[n],e[n])}if(e.t<this.t){r=e.s&this.DM;for(n=a;n<this.t;++n){i[n]=t(this[n],r)}i.t=this.t}else{r=this.s&this.DM;for(n=a;n<e.t;++n){i[n]=t(r,e[n])}i.t=e.t}i.s=t(this.s,e.s);i.clamp()}function ge(e,t){return e&t}function be(e){var t=f();this.bitwiseTo(e,ge,t);return t}function Se(e,t){return e|t}function we(e){var t=f();this.bitwiseTo(e,Se,t);return t}function Pe(e,t){return e^t}function Ce(e){var t=f();this.bitwiseTo(e,Pe,t);return t}function _e(e,t){return e&~t}function ke(e){var t=f();this.bitwiseTo(e,_e,t);return t}function De(){for(var e=f(),t=0;t<this.t;++t){e[t]=this.DM&~this[t]}e.t=this.t;e.s=~this.s;return e}function xe(e){var t=f();e<0?this.rShiftTo(-e,t):this.lShiftTo(e,t);return t}function Te(e){var t=f();e<0?this.lShiftTo(-e,t):this.rShiftTo(e,t);return t}function Me(e){if(e==0)return-1;var t=0;(e&65535)==0&&(e>>=16,t+=16);(e&255)==0&&(e>>=8,t+=8);(e&15)==0&&(e>>=4,t+=4);(e&3)==0&&(e>>=2,t+=2);(e&1)==0&&++t;return t}function Le(){for(var e=0;e<this.t;++e){if(this[e]!=0)return e*this.DB+Me(this[e])}return this.s<0?this.t*this.DB:-1}function We(e){for(var t=0;e!=0;){e&=e-1,++t}return t}function Ee(){for(var e=0,t=this.s&this.DM,i=0;i<this.t;++i){e+=We(this[i]^t)}return e}function Ae(e){var t=Math.floor(e/this.DB);return t>=this.t?this.s!=0:(this[t]&1<<e%this.DB)!=0}function Re(e,t){var i=u.ONE.shiftLeft(e);this.bitwiseTo(i,t,i);return i}function Be(e){return this.changeBit(e,Se)}function Ie(e){return this.changeBit(e,_e)}function ze(e){return this.changeBit(e,Pe)}function Oe(e,t){for(var i=0,n=0,r=Math.min(e.t,this.t);i<r;){n+=this[i]+e[i],t[i++]=n&this.DM,n>>=this.DB}if(e.t<this.t){for(n+=e.s;i<this.t;){n+=this[i],t[i++]=n&this.DM,n>>=this.DB}n+=this.s}else{for(n+=this.s;i<e.t;){n+=e[i],t[i++]=n&this.DM,n>>=this.DB}n+=e.s}t.s=n<0?-1:0;n>0?t[i++]=n:n<-1&&(t[i++]=this.DV+n);t.t=i;t.clamp()}function Fe(e){var t=f();this.addTo(e,t);return t}function Ne(e){var t=f();this.subTo(e,t);return t}function Ue(e){var t=f();this.multiplyTo(e,t);return t}function je(){var e=f();this.squareTo(e);return e}function He(e){var t=f();this.divRemTo(e,t,null);return t}function qe(e){var t=f();this.divRemTo(e,null,t);return t}function Ve(e){var t=f(),i=f();this.divRemTo(e,t,i);return[t,i]}function Je(e){this[this.t]=this.am(0,e-1,this,0,0,this.t);++this.t;this.clamp()}function Ge(e,t){if(e!=0){for(;this.t<=t;){this[this.t++]=0}for(this[t]+=e;this[t]>=this.DV;){this[t]-=this.DV,++t>=this.t&&(this[this.t++]=0),++this[t]}}}function Ye(){}function Xe(e){return e}function Ke(e,t,i){e.multiplyTo(t,i)}function $e(e,t){e.squareTo(t)}Ye.prototype.convert=Xe;Ye.prototype.revert=Xe;Ye.prototype.mulTo=Ke;Ye.prototype.sqrTo=$e;function Ze(e){return this.exp(e,new Ye)}function Qe(e,t,i){var n=Math.min(this.t+e.t,t);i.s=0;for(i.t=n;n>0;){i[--n]=0}var r;for(r=i.t-this.t;n<r;++n){i[n+this.t]=this.am(0,e[n],i,n,0,this.t)}for(r=Math.min(e.t,t);n<r;++n){this.am(0,e[n],i,n,0,t-n)}i.clamp()}function et(e,t,i){--t;var n=i.t=this.t+e.t-t;for(i.s=0;--n>=0;){i[n]=0}for(n=Math.max(t-this.t,0);n<e.t;++n){i[this.t+n-t]=this.am(t-n,e[n],i,0,0,this.t+n-t)}i.clamp();i.drShiftTo(1,i)}function tt(e){this.r2=f();this.q3=f();u.ONE.dlShiftTo(2*e.t,this.r2);this.mu=this.r2.divide(e);this.m=e}function it(e){if(e.s<0||e.t>2*this.m.t)return e.mod(this.m);else if(e.compareTo(this.m)<0)return e;else{var t=f();e.copyTo(t);this.reduce(t);return t}}function nt(e){return e}function rt(e){e.drShiftTo(this.m.t-1,this.r2);if(e.t>this.m.t+1)e.t=this.m.t+1,e.clamp();this.mu.multiplyUpperTo(this.r2,this.m.t+1,this.q3);for(this.m.multiplyLowerTo(this.q3,this.m.t+1,this.r2);e.compareTo(this.r2)<0;){e.dAddOffset(1,this.m.t+1)}for(e.subTo(this.r2,e);e.compareTo(this.m)>=0;){e.subTo(this.m,e)}}function at(e,t){e.squareTo(t);this.reduce(t)}function ot(e,t,i){e.multiplyTo(t,i);this.reduce(i)}tt.prototype.convert=it;tt.prototype.revert=nt;tt.prototype.reduce=rt;tt.prototype.mulTo=ot;tt.prototype.sqrTo=at;function st(e,t){var i=e.bitLength(),n,r=_(1),a;if(i<=0)return r;else n=i<18?1:i<48?3:i<144?4:i<768?5:6;a=i<8?new j(t):t.isEven()?new tt(t):new X(t);var o=[],s=3,l=n-1,u=(1<<n)-1;o[1]=a.convert(this);if(n>1){i=f();for(a.sqrTo(o[1],i);s<=u;){o[s]=f(),a.mulTo(i,o[s-2],o[s]),s+=2}}for(var c=e.t-1,d,h=!0,p=f(),i=W(e[c])-1;c>=0;){i>=l?d=e[c]>>i-l&u:(d=(e[c]&(1<<i+1)-1)<<l-i,c>0&&(d|=e[c-1]>>this.DB+i-l));for(s=n;(d&1)==0;){d>>=1,--s}if((i-=s)<0)i+=this.DB,--c;if(h)o[d].copyTo(r),h=!1;else{for(;s>1;){a.sqrTo(r,p),a.sqrTo(p,r),s-=2}s>0?a.sqrTo(r,p):(s=r,r=p,p=s);a.mulTo(p,o[d],r)}for(;c>=0&&(e[c]&1<<i)==0;){a.sqrTo(r,p),s=r,r=p,p=s,--i<0&&(i=this.DB-1,--c)}}return a.revert(r)}function lt(e){var t=this.s<0?this.negate():this.clone(),e=e.s<0?e.negate():e.clone();if(t.compareTo(e)<0)var i=t,t=e,e=i;var i=t.getLowestSetBit(),n=e.getLowestSetBit();if(n<0)return t;i<n&&(n=i);n>0&&(t.rShiftTo(n,t),e.rShiftTo(n,e));for(;t.signum()>0;){(i=t.getLowestSetBit())>0&&t.rShiftTo(i,t),(i=e.getLowestSetBit())>0&&e.rShiftTo(i,e),t.compareTo(e)>=0?(t.subTo(e,t),t.rShiftTo(1,t)):(e.subTo(t,e),e.rShiftTo(1,e))}n>0&&e.lShiftTo(n,e);return e}function ut(e){if(e<=0)return 0;var t=this.DV%e,i=this.s<0?e-1:0;if(this.t>0)if(t==0)i=this[0]%e;else for(var n=this.t-1;n>=0;--n){i=(t*i+this[n])%e}return i}function ft(e){var t=e.isEven();if(this.isEven()&&t||e.signum()==0)return u.ZERO;for(var i=e.clone(),n=this.clone(),r=_(1),a=_(0),o=_(0),s=_(1);i.signum()!=0;){for(;i.isEven();){i.rShiftTo(1,i);if(t){if(!r.isEven()||!a.isEven())r.addTo(this,r),a.subTo(e,a);r.rShiftTo(1,r)}else a.isEven()||a.subTo(e,a);a.rShiftTo(1,a)}for(;n.isEven();){n.rShiftTo(1,n);if(t){if(!o.isEven()||!s.isEven())o.addTo(this,o),s.subTo(e,s);o.rShiftTo(1,o)}else s.isEven()||s.subTo(e,s);s.rShiftTo(1,s)}i.compareTo(n)>=0?(i.subTo(n,i),t&&r.subTo(o,r),a.subTo(s,a)):(n.subTo(i,n),t&&o.subTo(r,o),s.subTo(a,s))}if(n.compareTo(u.ONE)!=0)return u.ZERO;if(s.compareTo(e)>=0)return s.subtract(e);if(s.signum()<0)s.addTo(e,s);else return s;return s.signum()<0?s.add(e):s}var ct=[2,3,5,7,11,13,17,19,23,29,31,37,41,43,47,53,59,61,67,71,73,79,83,89,97,101,103,107,109,113,127,131,137,139,149,151,157,163,167,173,179,181,191,193,197,199,211,223,227,229,233,239,241,251,257,263,269,271,277,281,283,293,307,311,313,317,331,337,347,349,353,359,367,373,379,383,389,397,401,409,419,421,431,433,439,443,449,457,461,463,467,479,487,491,499,503,509,521,523,541,547,557,563,569,571,577,587,593,599,601,607,613,617,619,631,641,643,647,653,659,661,673,677,683,691,701,709,719,727,733,739,743,751,757,761,769,773,787,797,809,811,821,823,827,829,839,853,857,859,863,877,881,883,887,907,911,919,929,937,941,947,953,967,971,977,983,991,997],dt=67108864/ct[ct.length-1];function ht(e){var t,i=this.abs();if(i.t==1&&i[0]<=ct[ct.length-1]){for(t=0;t<ct.length;++t){if(i[0]==ct[t])return!0}return!1}if(i.isEven())return!1;for(t=1;t<ct.length;){for(var n=ct[t],r=t+1;r<ct.length&&n<dt;){n*=ct[r++]}for(n=i.modInt(n);t<r;){if(n%ct[t++]==0)return!1}}return i.millerRabin(e)}function pt(e){var t=this.subtract(u.ONE),i=t.getLowestSetBit();if(i<=0)return!1;var n=t.shiftRight(i),e=e+1>>1;if(e>ct.length)e=ct.length;for(var r=f(),a=0;a<e;++a){r.fromInt(ct[Math.floor(Math.random()*ct.length)]);var o=r.modPow(n,this);if(o.compareTo(u.ONE)!=0&&o.compareTo(t)!=0){for(var s=1;s++<i&&o.compareTo(t)!=0;){if(o=o.modPowInt(2,this),o.compareTo(u.ONE)==0)return!1}if(o.compareTo(t)!=0)return!1}}return!0}u.prototype.chunkSize=le;u.prototype.toRadix=fe;u.prototype.fromRadix=ce;u.prototype.fromNumber=de;u.prototype.bitwiseTo=me;u.prototype.changeBit=Re;u.prototype.addTo=Oe;u.prototype.dMultiply=Je;u.prototype.dAddOffset=Ge;u.prototype.multiplyLowerTo=Qe;u.prototype.multiplyUpperTo=et;u.prototype.modInt=ut;u.prototype.millerRabin=pt;u.prototype.clone=re;u.prototype.intValue=ae;u.prototype.byteValue=oe;u.prototype.shortValue=se;u.prototype.signum=ue;u.prototype.toByteArray=he;u.prototype.equals=pe;u.prototype.min=ye;u.prototype.max=ve;u.prototype.and=be;u.prototype.or=we;u.prototype.xor=Ce;u.prototype.andNot=ke;u.prototype.not=De;u.prototype.shiftLeft=xe;u.prototype.shiftRight=Te;u.prototype.getLowestSetBit=Le;u.prototype.bitCount=Ee;u.prototype.testBit=Ae;u.prototype.setBit=Be;u.prototype.clearBit=Ie;u.prototype.flipBit=ze;u.prototype.add=Fe;u.prototype.subtract=Ne;u.prototype.multiply=Ue;u.prototype.divide=He;u.prototype.remainder=qe;u.prototype.divideAndRemainder=Ve;u.prototype.modPow=st;u.prototype.modInverse=ft;u.prototype.pow=Ze;u.prototype.gcd=lt;u.prototype.isProbablePrime=ht;u.prototype.square=je;(function(e,t,i,n,a,o,s){function l(e){var t,n,r=this,a=e.length,o=0,s=r.i=r.j=r.m=0;r.S=[];r.c=[];for(a||(e=[a++]);o<i;){r.S[o]=o++}for(o=0;o<i;o++){t=r.S[o],s=s+t+e[o%a]&i-1,n=r.S[s],r.S[o]=n,r.S[s]=t}r.g=function(e){var t=r.S,n=r.i+1&i-1,a=t[n],o=r.j+a&i-1,s=t[o];t[n]=s;t[o]=a;for(var l=t[a+s&i-1];--e;){n=n+1&i-1,a=t[n],o=o+a&i-1,s=t[o],t[n]=s,t[o]=a,l=l*i+t[a+s&i-1]}r.i=n;r.j=o;return l};r.g(i)}function u(e,t,i,n,a){i=[];a=typeof e==="undefined"?"undefined":r(e);if(t&&a=="object")for(n in e){if(n.indexOf("S")<5)try{i.push(u(e[n],t-1))}catch(e){}}return i.length?i:e+(a!="string"?"\0":"")}function f(e,t,n,r){e+="";for(r=n=0;r<e.length;r++){var a=t,o=r&i-1,s=(n^=t[r&i-1]*19)+e.charCodeAt(r);a[o]=s&i-1}e="";for(r in t){e+=String.fromCharCode(t[r])}return e}t.seedrandom=function(r,c){var d=[],h,r=f(u(c?[r,e]:arguments.length?r:[(new Date).getTime(),e,window],3),d);h=new l(d);f(h.S,e);t.random=function(){for(var e=h.g(n),t=s,r=0;e<a;){e=(e+r)*i,t*=i,r=h.g(1)}for(;e>=o;){e/=2,t/=2,r>>>=1}return(e+r)/t};return r};s=t.pow(i,n);a=t.pow(2,a);o=a*2;f(t.random(),e)})([],Math,256,6,52);function yt(){}function vt(e){var t;for(t=0;t<e.length;t++){e[t]=Math.floor(Math.random()*256)}}yt.prototype.nextBytes=vt;function mt(){this.j=this.i=0;this.S=[]}function gt(e){var t,i,n;for(t=0;t<256;++t){this.S[t]=t}for(t=i=0;t<256;++t){i=i+this.S[t]+e[t%e.length]&255,n=this.S[t],this.S[t]=this.S[i],this.S[i]=n}this.j=this.i=0}function bt(){var e;this.i=this.i+1&255;this.j=this.j+this.S[this.i]&255;e=this.S[this.i];this.S[this.i]=this.S[this.j];this.S[this.j]=e;return this.S[e+this.S[this.i]&255]}mt.prototype.init=gt;mt.prototype.next=bt;function St(){return new mt}var wt=256,Pt,Ct,_t;function kt(e){Ct[_t++]^=e&255;Ct[_t++]^=e>>8&255;Ct[_t++]^=e>>16&255;Ct[_t++]^=e>>24&255;_t>=wt&&(_t-=wt)}function Dt(){kt((new Date).getTime())}if(Ct==null){Ct=[];_t=0;var xt;if(a.appName=="Netscape"&&a.appVersion<"5"&&window.crypto){var Tt=window.crypto.random(32);for(xt=0;xt<Tt.length;++xt){Ct[_t++]=Tt.charCodeAt(xt)&255}}for(;_t<wt;){xt=Math.floor(65536*Math.random()),Ct[_t++]=xt>>>8,Ct[_t++]=xt&255}_t=0;Dt()}function Mt(){if(Pt==null){Dt();Pt=St();Pt.init(Ct);for(_t=0;_t<Ct.length;++_t){Ct[_t]=0}_t=0}return Pt.next()}function Lt(e){var t;for(t=0;t<e.length;++t){e[t]=Mt()}}function Wt(){}Wt.prototype.nextBytes=Lt;function Et(e){function t(e,t){var i=(e&65535)+(t&65535);return(e>>16)+(t>>16)+(i>>16)<<16|i&65535}function i(e,t){return e>>>t|e<<32-t}e=function(e){for(var e=e.replace(/\r\n/g,"\n"),t="",i=0;i<e.length;i++){var n=e.charCodeAt(i);n<128?t+=String.fromCharCode(n):(n>127&&n<2048?t+=String.fromCharCode(n>>6|192):(t+=String.fromCharCode(n>>12|224),t+=String.fromCharCode(n>>6&63|128)),t+=String.fromCharCode(n&63|128))}return t}(e);return function(e){for(var t="",i=0;i<e.length*4;i++){t+="0123456789abcdef".charAt(e[i>>2]>>(3-i%4)*8+4&15)+"0123456789abcdef".charAt(e[i>>2]>>(3-i%4)*8&15)}return t}(function(e,n){var r=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],a=[1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225],o=Array(64),s,l,u,f,c,d,h,p,y,v,m,g;e[n>>5]|=128<<24-n%32;e[(n+64>>9<<4)+15]=n;for(y=0;y<e.length;y+=16){s=a[0];l=a[1];u=a[2];f=a[3];c=a[4];d=a[5];h=a[6];p=a[7];for(v=0;v<64;v++){o[v]=v<16?e[v+y]:t(t(t(i(o[v-2],17)^i(o[v-2],19)^o[v-2]>>>10,o[v-7]),i(o[v-15],7)^i(o[v-15],18)^o[v-15]>>>3),o[v-16]),m=t(t(t(t(p,i(c,6)^i(c,11)^i(c,25)),c&d^~c&h),r[v]),o[v]),g=t(i(s,2)^i(s,13)^i(s,22),s&l^s&u^l&u),p=h,h=d,d=c,c=t(f,m),f=u,u=l,l=s,s=t(m,g)}a[0]=t(s,a[0]);a[1]=t(l,a[1]);a[2]=t(u,a[2]);a[3]=t(f,a[3]);a[4]=t(c,a[4]);a[5]=t(d,a[5]);a[6]=t(h,a[6]);a[7]=t(p,a[7])}return a}(function(e){for(var t=[],i=0;i<e.length*8;i+=8){t[i>>5]|=(e.charCodeAt(i/8)&255)<<24-i%32}return t}(e),e.length*8))}var At={hex:function e(t){return Et(t)}};function Rt(e){function t(e,t){return e<<t|e>>>32-t}function i(e){var t="",i,n;for(i=7;i>=0;i--){n=e>>>i*4&15,t+=n.toString(16)}return t}var n,r,a=Array(80),o=1732584193,s=4023233417,l=2562383102,u=271733878,f=3285377520,c,d,h,p,y,e=function(e){for(var e=e.replace(/\r\n/g,"\n"),t="",i=0;i<e.length;i++){var n=e.charCodeAt(i);n<128?t+=String.fromCharCode(n):(n>127&&n<2048?t+=String.fromCharCode(n>>6|192):(t+=String.fromCharCode(n>>12|224),t+=String.fromCharCode(n>>6&63|128)),t+=String.fromCharCode(n&63|128))}return t}(e);c=e.length;var v=[];for(n=0;n<c-3;n+=4){r=e.charCodeAt(n)<<24|e.charCodeAt(n+1)<<16|e.charCodeAt(n+2)<<8|e.charCodeAt(n+3),v.push(r)}switch(c%4){case 0:n=2147483648;break;case 1:n=e.charCodeAt(c-1)<<24|8388608;break;case 2:n=e.charCodeAt(c-2)<<24|e.charCodeAt(c-1)<<16|32768;break;case 3:n=e.charCodeAt(c-3)<<24|e.charCodeAt(c-2)<<16|e.charCodeAt(c-1)<<8|128}for(v.push(n);v.length%16!=14;){v.push(0)}v.push(c>>>29);v.push(c<<3&4294967295);for(e=0;e<v.length;e+=16){for(n=0;n<16;n++){a[n]=v[e+n]}for(n=16;n<=79;n++){a[n]=t(a[n-3]^a[n-8]^a[n-14]^a[n-16],1)}r=o;c=s;d=l;h=u;p=f;for(n=0;n<=19;n++){y=t(r,5)+(c&d|~c&h)+p+a[n]+1518500249&4294967295,p=h,h=d,d=t(c,30),c=r,r=y}for(n=20;n<=39;n++){y=t(r,5)+(c^d^h)+p+a[n]+1859775393&4294967295,p=h,h=d,d=t(c,30),c=r,r=y}for(n=40;n<=59;n++){y=t(r,5)+(c&d|c&h|d&h)+p+a[n]+2400959708&4294967295,p=h,h=d,d=t(c,30),c=r,r=y}for(n=60;n<=79;n++){y=t(r,5)+(c^d^h)+p+a[n]+3395469782&4294967295,p=h,h=d,d=t(c,30),c=r,r=y}o=o+r&4294967295;s=s+c&4294967295;l=l+d&4294967295;u=u+h&4294967295;f=f+p&4294967295}y=i(o)+i(s)+i(l)+i(u)+i(f);return y.toLowerCase()}var Bt={hex:function e(t){return Rt(t)}},It=function e(t){function i(e,t){var i,n,r,a,o;r=e&2147483648;a=t&2147483648;i=e&1073741824;n=t&1073741824;o=(e&1073741823)+(t&1073741823);return i&n?o^2147483648^r^a:i|n?o&1073741824?o^3221225472^r^a:o^1073741824^r^a:o^r^a}function n(e,t,n,r,a,o,s){e=i(e,i(i(t&n|~t&r,a),s));return i(e<<o|e>>>32-o,t)}function r(e,t,n,r,a,o,s){e=i(e,i(i(t&r|n&~r,a),s));return i(e<<o|e>>>32-o,t)}function a(e,t,n,r,a,o,s){e=i(e,i(i(t^n^r,a),s));return i(e<<o|e>>>32-o,t)}function o(e,t,n,r,a,o,s){e=i(e,i(i(n^(t|~r),a),s));return i(e<<o|e>>>32-o,t)}function s(e){var t="",i="",n;for(n=0;n<=3;n++){i=e>>>n*8&255,i="0"+i.toString(16),t+=i.substr(i.length-2,2)}return t}var l=[],u,f,c,d,h,p,y,v,t=function(e){for(var e=e.replace(/\r\n/g,"\n"),t="",i=0;i<e.length;i++){var n=e.charCodeAt(i);n<128?t+=String.fromCharCode(n):(n>127&&n<2048?t+=String.fromCharCode(n>>6|192):(t+=String.fromCharCode(n>>12|224),t+=String.fromCharCode(n>>6&63|128)),t+=String.fromCharCode(n&63|128))}return t}(t),l=function(e){var t,i=e.length;t=i+8;for(var n=((t-t%64)/64+1)*16,r=Array(n-1),a=0,o=0;o<i;){t=(o-o%4)/4,a=o%4*8,r[t]|=e.charCodeAt(o)<<a,o++}r[(o-o%4)/4]|=128<<o%4*8;r[n-2]=i<<3;r[n-1]=i>>>29;return r}(t);h=1732584193;p=4023233417;y=2562383102;v=271733878;for(t=0;t<l.length;t+=16){u=h,f=p,c=y,d=v,h=n(h,p,y,v,l[t+0],7,3614090360),v=n(v,h,p,y,l[t+1],12,3905402710),y=n(y,v,h,p,l[t+2],17,606105819),p=n(p,y,v,h,l[t+3],22,3250441966),h=n(h,p,y,v,l[t+4],7,4118548399),v=n(v,h,p,y,l[t+5],12,1200080426),y=n(y,v,h,p,l[t+6],17,2821735955),p=n(p,y,v,h,l[t+7],22,4249261313),h=n(h,p,y,v,l[t+8],7,1770035416),v=n(v,h,p,y,l[t+9],12,2336552879),y=n(y,v,h,p,l[t+10],17,4294925233),p=n(p,y,v,h,l[t+11],22,2304563134),h=n(h,p,y,v,l[t+12],7,1804603682),v=n(v,h,p,y,l[t+13],12,4254626195),y=n(y,v,h,p,l[t+14],17,2792965006),p=n(p,y,v,h,l[t+15],22,1236535329),h=r(h,p,y,v,l[t+1],5,4129170786),v=r(v,h,p,y,l[t+6],9,3225465664),y=r(y,v,h,p,l[t+11],14,643717713),p=r(p,y,v,h,l[t+0],20,3921069994),h=r(h,p,y,v,l[t+5],5,3593408605),v=r(v,h,p,y,l[t+10],9,38016083),y=r(y,v,h,p,l[t+15],14,3634488961),p=r(p,y,v,h,l[t+4],20,3889429448),h=r(h,p,y,v,l[t+9],5,568446438),v=r(v,h,p,y,l[t+14],9,3275163606),y=r(y,v,h,p,l[t+3],14,4107603335),p=r(p,y,v,h,l[t+8],20,1163531501),h=r(h,p,y,v,l[t+13],5,2850285829),v=r(v,h,p,y,l[t+2],9,4243563512),y=r(y,v,h,p,l[t+7],14,1735328473),p=r(p,y,v,h,l[t+12],20,2368359562),h=a(h,p,y,v,l[t+5],4,4294588738),v=a(v,h,p,y,l[t+8],11,2272392833),y=a(y,v,h,p,l[t+11],16,1839030562),p=a(p,y,v,h,l[t+14],23,4259657740),h=a(h,p,y,v,l[t+1],4,2763975236),v=a(v,h,p,y,l[t+4],11,1272893353),y=a(y,v,h,p,l[t+7],16,4139469664),p=a(p,y,v,h,l[t+10],23,3200236656),h=a(h,p,y,v,l[t+13],4,681279174),v=a(v,h,p,y,l[t+0],11,3936430074),y=a(y,v,h,p,l[t+3],16,3572445317),p=a(p,y,v,h,l[t+6],23,76029189),h=a(h,p,y,v,l[t+9],4,3654602809),v=a(v,h,p,y,l[t+12],11,3873151461),y=a(y,v,h,p,l[t+15],16,530742520),p=a(p,y,v,h,l[t+2],23,3299628645),h=o(h,p,y,v,l[t+0],6,4096336452),v=o(v,h,p,y,l[t+7],10,1126891415),y=o(y,v,h,p,l[t+14],15,2878612391),p=o(p,y,v,h,l[t+5],21,4237533241),h=o(h,p,y,v,l[t+12],6,1700485571),v=o(v,h,p,y,l[t+3],10,2399980690),y=o(y,v,h,p,l[t+10],15,4293915773),p=o(p,y,v,h,l[t+1],21,2240044497),h=o(h,p,y,v,l[t+8],6,1873313359),v=o(v,h,p,y,l[t+15],10,4264355552),y=o(y,v,h,p,l[t+6],15,2734768916),p=o(p,y,v,h,l[t+13],21,1309151649),h=o(h,p,y,v,l[t+4],6,4149444226),v=o(v,h,p,y,l[t+11],10,3174756917),y=o(y,v,h,p,l[t+2],15,718787259),p=o(p,y,v,h,l[t+9],21,3951481745),h=i(h,u),p=i(p,f),y=i(y,c),v=i(v,d)}return(s(h)+s(p)+s(y)+s(v)).toLowerCase()};function zt(e,t){return new u(e,t)}function Ot(e,t){for(var i="",n=0;n+t<e.length;){i+=e.substring(n,n+t)+"\n",n+=t}return i+e.substring(n,e.length)}function Ft(e){return e<16?"0"+e.toString(16):e.toString(16)}function Nt(e,t){if(t<e.length+11)throw"Message too long for RSA (n="+t+", l="+e.length+")";for(var i=[],n=e.length-1;n>=0&&t>0;){var r=e.charCodeAt(n--);r<128?i[--t]=r:r>127&&r<2048?(i[--t]=r&63|128,i[--t]=r>>6|192):(i[--t]=r&63|128,i[--t]=r>>6&63|128,i[--t]=r>>12|224)}i[--t]=0;n=new Wt;for(r=[];t>2;){for(r[0]=0;r[0]==0;){n.nextBytes(r)}i[--t]=r[0]}i[--t]=2;i[--t]=0;return new u(i)}function Ut(){this.n=null;this.e=0;this.coeff=this.dmq1=this.dmp1=this.q=this.p=this.d=null}function jt(e,t){e!=null&&t!=null&&e.length>0&&t.length>0?(this.n=zt(e,16),this.e=parseInt(t,16)):alert("Invalid RSA public key")}function Ht(e){return e.modPowInt(this.e,this.n)}function qt(e){e=Nt(e,this.n.bitLength()+7>>3);if(e==null)return null;e=this.doPublic(e);if(e==null)return null;e=e.toString(16);return(e.length&1)==0?e:"0"+e}Ut.prototype.doPublic=Ht;Ut.prototype.setPublic=jt;Ut.prototype.encrypt=qt;function Vt(e,t){for(var i=e.toByteArray(),n=0;n<i.length&&i[n]==0;){++n}if(i.length-n!=t-1||i[n]!=2)return null;for(++n;i[n]!=0;){if(++n>=i.length)return null}for(var r="";++n<i.length;){var a=i[n]&255;a<128?r+=String.fromCharCode(a):a>191&&a<224?(r+=String.fromCharCode((a&31)<<6|i[n+1]&63),++n):(r+=String.fromCharCode((a&15)<<12|(i[n+1]&63)<<6|i[n+2]&63),n+=2)}return r}function Jt(e,t,i){e!=null&&t!=null&&e.length>0&&t.length>0?(this.n=zt(e,16),this.e=parseInt(t,16),this.d=zt(i,16)):alert("Invalid RSA private key")}function Gt(e,t,i,n,r,a,o,s){e!=null&&t!=null&&e.length>0&&t.length>0?(this.n=zt(e,16),this.e=parseInt(t,16),this.d=zt(i,16),this.p=zt(n,16),this.q=zt(r,16),this.dmp1=zt(a,16),this.dmq1=zt(o,16),this.coeff=zt(s,16)):alert("Invalid RSA private key")}function Yt(e,t){var i=new yt,n=e>>1;this.e=parseInt(t,16);for(var r=new u(t,16);;){for(;;){if(this.p=new u(e-n,1,i),this.p.subtract(u.ONE).gcd(r).compareTo(u.ONE)==0&&this.p.isProbablePrime(10))break}for(;;){if(this.q=new u(n,1,i),this.q.subtract(u.ONE).gcd(r).compareTo(u.ONE)==0&&this.q.isProbablePrime(10))break}if(this.p.compareTo(this.q)<=0){var a=this.p;this.p=this.q;this.q=a}var a=this.p.subtract(u.ONE),o=this.q.subtract(u.ONE),s=a.multiply(o);if(s.gcd(r).compareTo(u.ONE)==0){this.n=this.p.multiply(this.q);this.d=r.modInverse(s);this.dmp1=this.d.mod(a);this.dmq1=this.d.mod(o);this.coeff=this.q.modInverse(this.p);break}}}function Xt(e){if(this.p==null||this.q==null)return e.modPow(this.d,this.n);for(var t=e.mod(this.p).modPow(this.dmp1,this.p),e=e.mod(this.q).modPow(this.dmq1,this.q);t.compareTo(e)<0;){t=t.add(this.p)}return t.subtract(e).multiply(this.coeff).mod(this.p).multiply(this.q).add(e)}function Kt(e){e=this.doPrivate(zt(e,16));return e==null?null:Vt(e,this.n.bitLength()+7>>3)}Ut.prototype.doPrivate=Xt;Ut.prototype.setPrivate=Jt;Ut.prototype.setPrivateEx=Gt;Ut.prototype.generate=Yt;Ut.prototype.decrypt=Kt;var $t=[];$t.sha1="3021300906052b0e03021a05000414";$t.sha256="3031300d060960864801650304020105000420";var Zt=[];Zt.sha1=Bt.hex;Zt.sha256=At.hex;function Qt(e,t,i){t/=4;for(var e=(0,Zt[i])(e),i="00"+$t[i]+e,e="",t=t-4-i.length,n=0;n<t;n+=2){e+="ff"}return sPaddedMessageHex="0001"+e+i}function ei(e,t){var i=Qt(e,this.n.bitLength(),t);return this.doPrivate(zt(i,16)).toString(16)}function ti(e){e=Qt(e,this.n.bitLength(),"sha1");return this.doPrivate(zt(e,16)).toString(16)}function ii(e){e=Qt(e,this.n.bitLength(),"sha256");return this.doPrivate(zt(e,16)).toString(16)}function ni(e,t,i){var n=new Ut;n.setPublic(t,i);return n.doPublic(e)}function ri(e,t,i){return ni(e,t,i).toString(16).replace(/^1f+00/,"")}function ai(e){for(var t in $t){var i=$t[t],n=i.length;if(e.substring(0,n)==i)return[t,e.substring(n)]}return[]}function oi(e,t,i,n){t=ri(t,i,n);i=ai(t);if(i.length==0)return!1;t=i[1];e=(0,Zt[i[0]])(e);return t==e}function si(e,t){var i=zt(e,16);return oi(t,i,this.n.toString(16),this.e.toString(16))}function li(e,t){var t=t.replace(/[ \n]+/g,""),i=this.doPublic(zt(t,16)).toString(16).replace(/^1f+00/,""),n=ai(i);if(n.length==0)return!1;i=n[1];n=(0,Zt[n[0]])(e);return i==n}Ut.prototype.signString=ei;Ut.prototype.signStringWithSHA1=ti;Ut.prototype.signStringWithSHA256=ii;Ut.prototype.verifyString=li;Ut.prototype.verifyHexSignatureForMessage=si;var ui=function(){var e={Sbox:[99,124,119,123,242,107,111,197,48,1,103,43,254,215,171,118,202,130,201,125,250,89,71,240,173,212,162,175,156,164,114,192,183,253,147,38,54,63,247,204,52,165,229,241,113,216,49,21,4,199,35,195,24,150,5,154,7,18,128,226,235,39,178,117,9,131,44,26,27,110,90,160,82,59,214,179,41,227,47,132,83,209,0,237,32,252,177,91,106,203,190,57,74,76,88,207,208,239,170,251,67,77,51,133,69,249,2,127,80,60,159,168,81,163,64,143,146,157,56,245,188,182,218,33,16,255,243,210,205,12,19,236,95,151,68,23,196,167,126,61,100,93,25,115,96,129,79,220,34,42,144,136,70,238,184,20,222,94,11,219,224,50,58,10,73,6,36,92,194,211,172,98,145,149,228,121,231,200,55,109,141,213,78,169,108,86,244,234,101,122,174,8,186,120,37,46,28,166,180,198,232,221,116,31,75,189,139,138,112,62,181,102,72,3,246,14,97,53,87,185,134,193,29,158,225,248,152,17,105,217,142,148,155,30,135,233,206,85,40,223,140,161,137,13,191,230,66,104,65,153,45,15,176,84,187,22],ShiftRowTab:[0,5,10,15,4,9,14,3,8,13,2,7,12,1,6,11]};e.Init=function(){e.Sbox_Inv=Array(256);for(var t=0;t<256;t++){e.Sbox_Inv[e.Sbox[t]]=t}e.ShiftRowTab_Inv=Array(16);for(t=0;t<16;t++){e.ShiftRowTab_Inv[e.ShiftRowTab[t]]=t}e.xtime=Array(256);for(t=0;t<128;t++){e.xtime[t]=t<<1,e.xtime[128+t]=t<<1^27}};e.Done=function(){delete e.Sbox_Inv;delete e.ShiftRowTab_Inv;delete e.xtime};e.ExpandKey=function(t){var i=t.length,n,r=1;switch(i){case 16:n=176;break;case 24:n=208;break;case 32:n=240;break;default:alert("my.ExpandKey: Only key lengths of 16, 24 or 32 bytes allowed!")}for(var a=i;a<n;a+=4){var o=t.slice(a-4,a);if(a%i==0){if(o=[e.Sbox[o[1]]^r,e.Sbox[o[2]],e.Sbox[o[3]],e.Sbox[o[0]]],(r<<=1)>=256)r^=283}else i>24&&a%i==16&&(o=[e.Sbox[o[0]],e.Sbox[o[1]],e.Sbox[o[2]],e.Sbox[o[3]]]);for(var s=0;s<4;s++){t[a+s]=t[a+s-i]^o[s]}}};e.Encrypt=function(t,i){var n=i.length;e.AddRoundKey(t,i.slice(0,16));for(var r=16;r<n-16;r+=16){e.SubBytes(t,e.Sbox),e.ShiftRows(t,e.ShiftRowTab),e.MixColumns(t),e.AddRoundKey(t,i.slice(r,r+16))}e.SubBytes(t,e.Sbox);e.ShiftRows(t,e.ShiftRowTab);e.AddRoundKey(t,i.slice(r,n))};e.Decrypt=function(t,i){var n=i.length;e.AddRoundKey(t,i.slice(n-16,n));e.ShiftRows(t,e.ShiftRowTab_Inv);e.SubBytes(t,e.Sbox_Inv);for(n-=32;n>=16;n-=16){e.AddRoundKey(t,i.slice(n,n+16)),e.MixColumns_Inv(t),e.ShiftRows(t,e.ShiftRowTab_Inv),e.SubBytes(t,e.Sbox_Inv)}e.AddRoundKey(t,i.slice(0,16))};e.SubBytes=function(e,t){for(var i=0;i<16;i++){e[i]=t[e[i]]}};e.AddRoundKey=function(e,t){for(var i=0;i<16;i++){e[i]^=t[i]}};e.ShiftRows=function(e,t){for(var i=[].concat(e),n=0;n<16;n++){e[n]=i[t[n]]}};e.MixColumns=function(t){for(var i=0;i<16;i+=4){var n=t[i+0],r=t[i+1],a=t[i+2],o=t[i+3],s=n^r^a^o;t[i+0]^=s^e.xtime[n^r];t[i+1]^=s^e.xtime[r^a];t[i+2]^=s^e.xtime[a^o];t[i+3]^=s^e.xtime[o^n]}};e.MixColumns_Inv=function(t){for(var i=0;i<16;i+=4){var n=t[i+0],r=t[i+1],a=t[i+2],o=t[i+3],s=n^r^a^o,l=e.xtime[s],u=e.xtime[e.xtime[l^n^a]]^s;s^=e.xtime[e.xtime[l^r^o]];t[i+0]^=u^e.xtime[n^r];t[i+1]^=s^e.xtime[r^a];t[i+2]^=u^e.xtime[a^o];t[i+3]^=s^e.xtime[o^n]}};return e}(),fi=function(){var e={};ui.Init();e.b256to64=function(e){var t,i,n,r="",a=0,o=0,s=e.length;for(n=0;n<s;n++){i=e.charCodeAt(n),o==0?(r+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(i>>2&63),t=(i&3)<<4):o==1?(r+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(t|i>>4&15),t=(i&15)<<2):o==2&&(r+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(t|i>>6&3),a+=1,r+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(i&63)),a+=1,o+=1,o==3&&(o=0)}o>0&&(r+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(t),r+="=");o==1&&(r+="=");return r};e.b64to256=function(e){var t,i,n="",r=0,a=0,o=e.length;for(i=0;i<o;i++){t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".indexOf(e.charAt(i)),t>=0&&(r&&(n+=String.fromCharCode(a|t>>6-r&255)),r=r+2&7,a=t<<r&255)}return n};e.b16to64=function(e){var t,i,n="";e.length%2==1&&(e="0"+e);for(t=0;t+3<=e.length;t+=3){i=parseInt(e.substring(t,t+3),16),n+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(i>>6)+"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(i&63)}t+1==e.length?(i=parseInt(e.substring(t,t+1),16),n+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(i<<2)):t+2==e.length&&(i=parseInt(e.substring(t,t+2),16),n+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(i>>2)+"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt((i&3)<<4));for(;(n.length&3)>0;){n+="="}return n};e.b64to16=function(e){var t="",i,n=0,r;for(i=0;i<e.length;++i){if(e.charAt(i)=="=")break;v="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".indexOf(e.charAt(i));v<0||(n==0?(t+=S(v>>2),r=v&3,n=1):n==1?(t+=S(r<<2|v>>4),r=v&15,n=2):n==2?(t+=S(r),t+=S(v>>2),r=v&3,n=3):(t+=S(r<<2|v>>4),t+=S(v&15),n=0))}n==1&&(t+=S(r<<2));return t};e.string2bytes=function(e){for(var t=[],i=0;i<e.length;i++){t.push(e.charCodeAt(i))}return t};e.bytes2string=function(e){for(var t="",i=0;i<e.length;i++){t+=String.fromCharCode(e[i])}return t};e.blockXOR=function(e,t){for(var i=Array(16),n=0;n<16;n++){i[n]=e[n]^t[n]}return i};e.blockIV=function(){var e=new Wt,t=Array(16);e.nextBytes(t);return t};e.pad16=function(e){var t=e.slice(0),n=(16-e.length%16)%16;for(i=e.length;i<e.length+n;i++){t.push(0)}return t};e.depad=function(e){for(e=e.slice(0);e[e.length-1]==0;){e=e.slice(0,e.length-1)}return e};e.encryptAESCBC=function(t,i){var n=i.slice(0);ui.ExpandKey(n);for(var r=e.string2bytes(t),r=e.pad16(r),a=e.blockIV(),o=0;o<r.length/16;o++){var s=r.slice(o*16,o*16+16),l=a.slice(o*16,o*16+16),s=e.blockXOR(l,s);ui.Encrypt(s,n);a=a.concat(s)}n=e.bytes2string(a);return e.b256to64(n)};e.decryptAESCBC=function(t,i){var n=i.slice(0);ui.ExpandKey(n);for(var t=e.b64to256(t),r=e.string2bytes(t),a=[],o=1;o<r.length/16;o++){var s=r.slice(o*16,o*16+16),l=r.slice((o-1)*16,(o-1)*16+16);ui.Decrypt(s,n);s=e.blockXOR(l,s);a=a.concat(s)}a=e.depad(a);return e.bytes2string(a)};e.wrap60=function(e){for(var t="",i=0;i<e.length;i++){i%60==0&&i!=0&&(t+="\n"),t+=e[i]}return t};e.generateAESKey=function(){var e=Array(16);(new Wt).nextBytes(e);return e};e.generateRSAKey=function(e,t){Math.seedrandom(At.hex(e));var i=new Ut;i.generate(t,"10001");return i};e.publicKeyString=function(e){return pubkey=e.n.toString(16)};e.publicKeyID=function(e){return It(e)};e.publicKeyFromString=function(e){var e=e.split("|")[0],t=new Ut;t.setPublic(e,"10001");return t};e.encrypt=function(t,i,n){var r="";try{var a=e.publicKeyFromString(i);r+=a.encrypt(t)+"?"}catch(e){return{status:"Invalid public key"}}return{status:"success",cipher:r}};e.decrypt=function(e,t){var i=e.split("?"),n=t.decrypt(i[0]);return{status:"success",plaintext:n,signature:"unsigned"}};return e}();e.exports=fi},function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:true});var n=n||function(e,t){var i={},n=i.lib={},r=function e(){},a=n.Base={extend:function e(t){r.prototype=this;var i=new r;t&&i.mixIn(t);i.hasOwnProperty("init")||(i.init=function(){i.$super.init.apply(this,arguments)});i.init.prototype=i;i.$super=this;return i},create:function e(){var t=this.extend();t.init.apply(t,arguments);return t},init:function e(){},mixIn:function e(t){for(var i in t){t.hasOwnProperty(i)&&(this[i]=t[i])}t.hasOwnProperty("toString")&&(this.toString=t.toString)},clone:function e(){return this.init.prototype.extend(this)}},o=n.WordArray=a.extend({init:function e(i,n){i=this.words=i||[];this.sigBytes=n!=t?n:4*i.length},toString:function e(t){return(t||l).stringify(this)},concat:function e(t){var i=this.words,n=t.words,r=this.sigBytes;t=t.sigBytes;this.clamp();if(r%4)for(var a=0;a<t;a++){i[r+a>>>2]|=(n[a>>>2]>>>24-8*(a%4)&255)<<24-8*((r+a)%4)}else if(65535<n.length)for(a=0;a<t;a+=4){i[r+a>>>2]=n[a>>>2]}else i.push.apply(i,n);this.sigBytes+=t;return this},clamp:function t(){var i=this.words,n=this.sigBytes;i[n>>>2]&=4294967295<<32-8*(n%4);i.length=e.ceil(n/4)},clone:function e(){var t=a.clone.call(this);t.words=this.words.slice(0);return t},random:function t(i){for(var n=[],r=0;r<i;r+=4){n.push(4294967296*e.random()|0)}return new o.init(n,i)}}),s=i.enc={},l=s.Hex={stringify:function e(t){var i=t.words;t=t.sigBytes;for(var n=[],r=0;r<t;r++){var a=i[r>>>2]>>>24-8*(r%4)&255;n.push((a>>>4).toString(16));n.push((a&15).toString(16))}return n.join("")},parse:function e(t){for(var i=t.length,n=[],r=0;r<i;r+=2){n[r>>>3]|=parseInt(t.substr(r,2),16)<<24-4*(r%8)}return new o.init(n,i/2)}},u=s.Latin1={stringify:function e(t){var i=t.words;t=t.sigBytes;for(var n=[],r=0;r<t;r++){n.push(String.fromCharCode(i[r>>>2]>>>24-8*(r%4)&255))}return n.join("")},parse:function e(t){for(var i=t.length,n=[],r=0;r<i;r++){n[r>>>2]|=(t.charCodeAt(r)&255)<<24-8*(r%4)}return new o.init(n,i)}},f=s.Utf8={stringify:function e(t){try{return decodeURIComponent(escape(u.stringify(t)))}catch(e){throw Error("Malformed UTF-8 data")}},parse:function e(t){return u.parse(unescape(encodeURIComponent(t)))}},c=n.BufferedBlockAlgorithm=a.extend({reset:function e(){this._data=new o.init;this._nDataBytes=0},_append:function e(t){"string"==typeof t&&(t=f.parse(t));this._data.concat(t);this._nDataBytes+=t.sigBytes},_process:function t(i){var n=this._data,r=n.words,a=n.sigBytes,s=this.blockSize,l=a/(4*s),l=i?e.ceil(l):e.max((l|0)-this._minBufferSize,0);i=l*s;a=e.min(4*i,a);if(i){for(var u=0;u<i;u+=s){this._doProcessBlock(r,u)}u=r.splice(0,i);n.sigBytes-=a}return new o.init(u,a)},clone:function e(){var t=a.clone.call(this);t._data=this._data.clone();return t},_minBufferSize:0});n.Hasher=c.extend({cfg:a.extend(),init:function e(t){this.cfg=this.cfg.extend(t);this.reset()},reset:function e(){c.reset.call(this);this._doReset()},update:function e(t){this._append(t);this._process();return this},finalize:function e(t){t&&this._append(t);return this._doFinalize()},blockSize:16,_createHelper:function e(t){return function(e,i){return new t.init(i).finalize(e)}},_createHmacHelper:function e(t){return function(e,i){return new d.HMAC.init(t,i).finalize(e)}}});var d=i.algo={};return i}(Math);(function(){var e=n,t=e.lib.WordArray;e.enc.Base64={stringify:function e(t){var i=t.words,n=t.sigBytes,r=this._map;t.clamp();t=[];for(var a=0;a<n;a+=3){for(var o=(i[a>>>2]>>>24-8*(a%4)&255)<<16|(i[a+1>>>2]>>>24-8*((a+1)%4)&255)<<8|i[a+2>>>2]>>>24-8*((a+2)%4)&255,s=0;4>s&&a+.75*s<n;s++){t.push(r.charAt(o>>>6*(3-s)&63))}}if(i=r.charAt(64))for(;t.length%4;){t.push(i)}return t.join("")},parse:function e(i){var n=i.length,r=this._map,a=r.charAt(64);a&&(a=i.indexOf(a),-1!=a&&(n=a));for(var a=[],o=0,s=0;s<n;s++){if(s%4){var l=r.indexOf(i.charAt(s-1))<<2*(s%4),u=r.indexOf(i.charAt(s))>>>6-2*(s%4);a[o>>>2]|=(l|u)<<24-8*(o%4);o++}}return t.create(a,o)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="}})();(function(e){function t(e,t,i,n,r,a,o){e=e+(t&i|~t&n)+r+o;return(e<<a|e>>>32-a)+t}function i(e,t,i,n,r,a,o){e=e+(t&n|i&~n)+r+o;return(e<<a|e>>>32-a)+t}function r(e,t,i,n,r,a,o){e=e+(t^i^n)+r+o;return(e<<a|e>>>32-a)+t}function a(e,t,i,n,r,a,o){e=e+(i^(t|~n))+r+o;return(e<<a|e>>>32-a)+t}for(var o=n,s=o.lib,l=s.WordArray,u=s.Hasher,s=o.algo,f=[],c=0;64>c;c++){f[c]=4294967296*e.abs(e.sin(c+1))|0}s=s.MD5=u.extend({_doReset:function e(){this._hash=new l.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function e(n,o){for(var s=0;16>s;s++){var l=o+s,u=n[l];n[l]=(u<<8|u>>>24)&16711935|(u<<24|u>>>8)&4278255360}var s=this._hash.words,l=n[o+0],u=n[o+1],c=n[o+2],d=n[o+3],h=n[o+4],p=n[o+5],y=n[o+6],v=n[o+7],m=n[o+8],g=n[o+9],b=n[o+10],S=n[o+11],w=n[o+12],P=n[o+13],C=n[o+14],_=n[o+15],k=s[0],D=s[1],x=s[2],T=s[3],k=t(k,D,x,T,l,7,f[0]),T=t(T,k,D,x,u,12,f[1]),x=t(x,T,k,D,c,17,f[2]),D=t(D,x,T,k,d,22,f[3]),k=t(k,D,x,T,h,7,f[4]),T=t(T,k,D,x,p,12,f[5]),x=t(x,T,k,D,y,17,f[6]),D=t(D,x,T,k,v,22,f[7]),k=t(k,D,x,T,m,7,f[8]),T=t(T,k,D,x,g,12,f[9]),x=t(x,T,k,D,b,17,f[10]),D=t(D,x,T,k,S,22,f[11]),k=t(k,D,x,T,w,7,f[12]),T=t(T,k,D,x,P,12,f[13]),x=t(x,T,k,D,C,17,f[14]),D=t(D,x,T,k,_,22,f[15]),k=i(k,D,x,T,u,5,f[16]),T=i(T,k,D,x,y,9,f[17]),x=i(x,T,k,D,S,14,f[18]),D=i(D,x,T,k,l,20,f[19]),k=i(k,D,x,T,p,5,f[20]),T=i(T,k,D,x,b,9,f[21]),x=i(x,T,k,D,_,14,f[22]),D=i(D,x,T,k,h,20,f[23]),k=i(k,D,x,T,g,5,f[24]),T=i(T,k,D,x,C,9,f[25]),x=i(x,T,k,D,d,14,f[26]),D=i(D,x,T,k,m,20,f[27]),k=i(k,D,x,T,P,5,f[28]),T=i(T,k,D,x,c,9,f[29]),x=i(x,T,k,D,v,14,f[30]),D=i(D,x,T,k,w,20,f[31]),k=r(k,D,x,T,p,4,f[32]),T=r(T,k,D,x,m,11,f[33]),x=r(x,T,k,D,S,16,f[34]),D=r(D,x,T,k,C,23,f[35]),k=r(k,D,x,T,u,4,f[36]),T=r(T,k,D,x,h,11,f[37]),x=r(x,T,k,D,v,16,f[38]),D=r(D,x,T,k,b,23,f[39]),k=r(k,D,x,T,P,4,f[40]),T=r(T,k,D,x,l,11,f[41]),x=r(x,T,k,D,d,16,f[42]),D=r(D,x,T,k,y,23,f[43]),k=r(k,D,x,T,g,4,f[44]),T=r(T,k,D,x,w,11,f[45]),x=r(x,T,k,D,_,16,f[46]),D=r(D,x,T,k,c,23,f[47]),k=a(k,D,x,T,l,6,f[48]),T=a(T,k,D,x,v,10,f[49]),x=a(x,T,k,D,C,15,f[50]),D=a(D,x,T,k,p,21,f[51]),k=a(k,D,x,T,w,6,f[52]),T=a(T,k,D,x,d,10,f[53]),x=a(x,T,k,D,b,15,f[54]),D=a(D,x,T,k,u,21,f[55]),k=a(k,D,x,T,m,6,f[56]),T=a(T,k,D,x,_,10,f[57]),x=a(x,T,k,D,y,15,f[58]),D=a(D,x,T,k,P,21,f[59]),k=a(k,D,x,T,h,6,f[60]),T=a(T,k,D,x,S,10,f[61]),x=a(x,T,k,D,c,15,f[62]),D=a(D,x,T,k,g,21,f[63]);s[0]=s[0]+k|0;s[1]=s[1]+D|0;s[2]=s[2]+x|0;s[3]=s[3]+T|0},_doFinalize:function t(){var i=this._data,n=i.words,r=8*this._nDataBytes,a=8*i.sigBytes;n[a>>>5]|=128<<24-a%32;var o=e.floor(r/4294967296);n[(a+64>>>9<<4)+15]=(o<<8|o>>>24)&16711935|(o<<24|o>>>8)&4278255360;n[(a+64>>>9<<4)+14]=(r<<8|r>>>24)&16711935|(r<<24|r>>>8)&4278255360;i.sigBytes=4*(n.length+1);this._process();i=this._hash;n=i.words;for(r=0;4>r;r++){a=n[r],n[r]=(a<<8|a>>>24)&16711935|(a<<24|a>>>8)&4278255360}return i},clone:function e(){var t=u.clone.call(this);t._hash=this._hash.clone();return t}});o.MD5=u._createHelper(s);o.HmacMD5=u._createHmacHelper(s)})(Math);(function(){var e=n,t=e.lib,i=t.Base,r=t.WordArray,t=e.algo,a=t.EvpKDF=i.extend({cfg:i.extend({keySize:4,hasher:t.MD5,iterations:1}),init:function e(t){this.cfg=this.cfg.extend(t)},compute:function e(t,i){for(var n=this.cfg,a=n.hasher.create(),o=r.create(),s=o.words,l=n.keySize,n=n.iterations;s.length<l;){u&&a.update(u);var u=a.update(t).finalize(i);a.reset();for(var f=1;f<n;f++){u=a.finalize(u),a.reset()}o.concat(u)}o.sigBytes=4*l;return o}});e.EvpKDF=function(e,t,i){return a.create(i).compute(e,t)}})();n.lib.Cipher||function(e){var t=n,i=t.lib,r=i.Base,a=i.WordArray,o=i.BufferedBlockAlgorithm,s=t.enc.Base64,l=t.algo.EvpKDF,u=i.Cipher=o.extend({cfg:r.extend(),createEncryptor:function e(t,i){return this.create(this._ENC_XFORM_MODE,t,i)},createDecryptor:function e(t,i){return this.create(this._DEC_XFORM_MODE,t,i)},init:function e(t,i,n){this.cfg=this.cfg.extend(n);this._xformMode=t;this._key=i;this.reset()},reset:function e(){o.reset.call(this);this._doReset()},process:function e(t){this._append(t);return this._process()},finalize:function e(t){t&&this._append(t);return this._doFinalize()},keySize:4,ivSize:4,_ENC_XFORM_MODE:1,_DEC_XFORM_MODE:2,_createHelper:function e(t){return{encrypt:function e(i,n,r){return("string"==typeof n?y:p).encrypt(t,i,n,r)},decrypt:function e(i,n,r){return("string"==typeof n?y:p).decrypt(t,i,n,r)}}}});i.StreamCipher=u.extend({_doFinalize:function e(){return this._process(!0)},blockSize:1});var f=t.mode={},c=function t(i,n,r){var a=this._iv;a?this._iv=e:a=this._prevBlock;for(var o=0;o<r;o++){i[n+o]^=a[o]}},d=(i.BlockCipherMode=r.extend({createEncryptor:function e(t,i){return this.Encryptor.create(t,i)},createDecryptor:function e(t,i){return this.Decryptor.create(t,i)},init:function e(t,i){this._cipher=t;this._iv=i}})).extend();d.Encryptor=d.extend({processBlock:function e(t,i){var n=this._cipher,r=n.blockSize;c.call(this,t,i,r);n.encryptBlock(t,i);this._prevBlock=t.slice(i,i+r)}});d.Decryptor=d.extend({processBlock:function e(t,i){var n=this._cipher,r=n.blockSize,a=t.slice(i,i+r);n.decryptBlock(t,i);c.call(this,t,i,r);this._prevBlock=a}});f=f.CBC=d;d=(t.pad={}).Pkcs7={pad:function e(t,i){for(var n=4*i,n=n-t.sigBytes%n,r=n<<24|n<<16|n<<8|n,o=[],s=0;s<n;s+=4){o.push(r)}n=a.create(o,n);t.concat(n)},unpad:function e(t){t.sigBytes-=t.words[t.sigBytes-1>>>2]&255}};i.BlockCipher=u.extend({cfg:u.cfg.extend({mode:f,padding:d}),reset:function e(){u.reset.call(this);var t=this.cfg,i=t.iv,t=t.mode;if(this._xformMode==this._ENC_XFORM_MODE)var n=t.createEncryptor;else n=t.createDecryptor,this._minBufferSize=1;this._mode=n.call(t,this,i&&i.words)},_doProcessBlock:function e(t,i){this._mode.processBlock(t,i)},_doFinalize:function e(){var t=this.cfg.padding;if(this._xformMode==this._ENC_XFORM_MODE){t.pad(this._data,this.blockSize);var i=this._process(!0)}else i=this._process(!0),t.unpad(i);return i},blockSize:4});var h=i.CipherParams=r.extend({init:function e(t){this.mixIn(t)},toString:function e(t){return(t||this.formatter).stringify(this)}}),f=(t.format={}).OpenSSL={stringify:function e(t){var i=t.ciphertext;t=t.salt;return(t?a.create([1398893684,1701076831]).concat(t).concat(i):i).toString(s)},parse:function e(t){t=s.parse(t);var i=t.words;if(1398893684==i[0]&&1701076831==i[1]){var n=a.create(i.slice(2,4));i.splice(0,4);t.sigBytes-=16}return h.create({ciphertext:t,salt:n})}},p=i.SerializableCipher=r.extend({cfg:r.extend({format:f}),encrypt:function e(t,i,n,r){r=this.cfg.extend(r);var a=t.createEncryptor(n,r);i=a.finalize(i);a=a.cfg;return h.create({ciphertext:i,key:n,iv:a.iv,algorithm:t,mode:a.mode,padding:a.padding,blockSize:t.blockSize,formatter:r.format})},decrypt:function e(t,i,n,r){r=this.cfg.extend(r);i=this._parse(i,r.format);return t.createDecryptor(n,r).finalize(i.ciphertext)},_parse:function e(t,i){return"string"==typeof t?i.parse(t,this):t}}),t=(t.kdf={}).OpenSSL={execute:function e(t,i,n,r){r||(r=a.random(8));t=l.create({keySize:i+n}).compute(t,r);n=a.create(t.words.slice(i),4*n);t.sigBytes=4*i;return h.create({key:t,iv:n,salt:r})}},y=i.PasswordBasedCipher=p.extend({cfg:p.cfg.extend({kdf:t}),encrypt:function e(t,i,n,r){r=this.cfg.extend(r);n=r.kdf.execute(n,t.keySize,t.ivSize);r.iv=n.iv;t=p.encrypt.call(this,t,i,n.key,r);t.mixIn(n);return t},decrypt:function e(t,i,n,r){r=this.cfg.extend(r);i=this._parse(i,r.format);n=r.kdf.execute(n,t.keySize,t.ivSize,i.salt);r.iv=n.iv;return p.decrypt.call(this,t,i,n.key,r)}})}();(function(){for(var e=n,t=e.lib.BlockCipher,i=e.algo,r=[],a=[],o=[],s=[],l=[],u=[],f=[],c=[],d=[],h=[],p=[],y=0;256>y;y++){p[y]=128>y?y<<1:y<<1^283}for(var v=0,m=0,y=0;256>y;y++){var g=m^m<<1^m<<2^m<<3^m<<4,g=g>>>8^g&255^99;r[v]=g;a[g]=v;var b=p[v],S=p[b],w=p[S],P=257*p[g]^16843008*g;o[v]=P<<24|P>>>8;s[v]=P<<16|P>>>16;l[v]=P<<8|P>>>24;u[v]=P;P=16843009*w^65537*S^257*b^16843008*v;f[g]=P<<24|P>>>8;c[g]=P<<16|P>>>16;d[g]=P<<8|P>>>24;h[g]=P;v?(v=b^p[p[p[w^b]]],m^=p[p[m]]):v=m=1}var C=[0,1,2,4,8,16,32,64,128,27,54],i=i.AES=t.extend({_doReset:function e(){for(var t=this._key,i=t.words,n=t.sigBytes/4,t=4*((this._nRounds=n+6)+1),a=this._keySchedule=[],o=0;o<t;o++){if(o<n)a[o]=i[o];else{var s=a[o-1];o%n?6<n&&4==o%n&&(s=r[s>>>24]<<24|r[s>>>16&255]<<16|r[s>>>8&255]<<8|r[s&255]):(s=s<<8|s>>>24,s=r[s>>>24]<<24|r[s>>>16&255]<<16|r[s>>>8&255]<<8|r[s&255],s^=C[o/n|0]<<24);a[o]=a[o-n]^s}}i=this._invKeySchedule=[];for(n=0;n<t;n++){o=t-n,s=n%4?a[o]:a[o-4],i[n]=4>n||4>=o?s:f[r[s>>>24]]^c[r[s>>>16&255]]^d[r[s>>>8&255]]^h[r[s&255]]}},encryptBlock:function e(t,i){this._doCryptBlock(t,i,this._keySchedule,o,s,l,u,r)},decryptBlock:function e(t,i){var n=t[i+1];t[i+1]=t[i+3];t[i+3]=n;this._doCryptBlock(t,i,this._invKeySchedule,f,c,d,h,a);n=t[i+1];t[i+1]=t[i+3];t[i+3]=n},_doCryptBlock:function e(t,i,n,r,a,o,s,l){for(var u=this._nRounds,f=t[i]^n[0],c=t[i+1]^n[1],d=t[i+2]^n[2],h=t[i+3]^n[3],p=4,y=1;y<u;y++){var v=r[f>>>24]^a[c>>>16&255]^o[d>>>8&255]^s[h&255]^n[p++],m=r[c>>>24]^a[d>>>16&255]^o[h>>>8&255]^s[f&255]^n[p++],g=r[d>>>24]^a[h>>>16&255]^o[f>>>8&255]^s[c&255]^n[p++],h=r[h>>>24]^a[f>>>16&255]^o[c>>>8&255]^s[d&255]^n[p++],f=v,c=m,d=g}v=(l[f>>>24]<<24|l[c>>>16&255]<<16|l[d>>>8&255]<<8|l[h&255])^n[p++];m=(l[c>>>24]<<24|l[d>>>16&255]<<16|l[h>>>8&255]<<8|l[f&255])^n[p++];g=(l[d>>>24]<<24|l[h>>>16&255]<<16|l[f>>>8&255]<<8|l[c&255])^n[p++];h=(l[h>>>24]<<24|l[f>>>16&255]<<16|l[c>>>8&255]<<8|l[d&255])^n[p++];t[i]=v;t[i+1]=m;t[i+2]=g;t[i+3]=h},keySize:8});e.AES=t._createHelper(i)})();t.default=n},function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:true});var n=function(){function e(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||false;n.configurable=true;if("value"in n)n.writable=true;Object.defineProperty(e,n.key,n)}}return function(t,i,n){if(i)e(t.prototype,i);if(n)e(t,n);return t}}();function r(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var a=function(){var e=function(){function e(){r(this,e)}n(e,[{key:"createClientObject",value:function e(t,i,n,r){return{socket:t,id:i,playURL:n,deviceSerial:r.deviceSerial||"",verificationCode:r.verificationCode||"",resolve:null,reject:null,stoping:false}}},{key:"playCmd",value:function e(t){var i={sequence:0,cmd:"realplay",deviceSerial:t.deviceSerial,verificationCode:t.verificationCode,url:t.playURL};return JSON.stringify(i)}},{key:"playbackCmd",value:function e(t,i,n){var r={sequence:0,cmd:"playback",deviceSerial:t.deviceSerial,verificationCode:t.verificationCode,url:t.playURL,startTime:i,endTime:n};return JSON.stringify(r)}}]);return e}();return e}();t.LocalService=a},function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:true});var n=function(){function e(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||false;n.configurable=true;if("value"in n)n.writable=true;Object.defineProperty(e,n.key,n)}}return function(t,i,n){if(i)e(t.prototype,i);if(n)e(t,n);return t}}();function r(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var a=0;var o=1;var s=2;var l=3;var u=4;var f=5;var c=6;var d=7;var h=11;var p=12;var y=13;var v=14;var m=15;var g=16;var b=17;var S=18;var w=19;var P=20;var C=21;var _=22;var k=24;var D=25;var x=26;var T=27;var M=28;var L=29;var W=30;var E=31;var A=33;var R=34;var B=99;var I=40;var z=41;var O=42;var F=43;var N=44;var U=45;var j=46;var H=47;var q=48;var V=60;var J=61;var G=62;var Y=63;var X=71;var K=72;var $=73;var Z=74;var Q=75;var ee=76;var te=77;var ie=78;var ne=79;var re=100;var ae=101;var oe=1;var se=2;var le=3;var ue=4;var fe=1;var ce=2;var de=8;var he=16;var pe=8e3;var ye=16e3;var ve=24e3;var me=32e3;var ge=48e3;var be=64e3;var Se={AUDIO_G711_U:28944,AUDIO_G711_A:28945,AUDIO_G722_1:29217,AUDIO_G726_U:29280,AUDIO_G726_A:29281,AUDIO_G726_2:29282,AUDIO_AACLC:8193,AUDIO_MPEG:8192,AUDIO_NULL:0};var we=0;var Pe=1;var Ce=1;var _e=256;var ke=28944;var De=28945;var xe=0;var Te=1;var Me="BMP";var Le="JPEG";var We=0;var Ee=1;var Ae=15;var Re=8;var Be=1;var Ie=5;var ze=20;var Oe=5;var Fe=5*1024*1024;var Ne=100;var Ue=5e3;var je=500;var He=100;var qe=20;var Ve=100;var Je=1;var Ge=0;var Ye={left:0,top:0,right:0,bottom:0};var Xe={width:0,height:0,frameNum:0,yuvData:null};var Ke={width:0,height:0,frameNum:0,yuvData:null};var $e={sampleRate:0,channel:0,bitsPerSample:0,length:0,pcmData:null};var Ze=false;var Qe=false;var et=false;var tt={id:null,cmd:null,data:null,errorCode:0,status:null};var it=t.JSPlayCtrl=function(){function e(t,i,n){r(this,e);if(t!=null&&t!==undefined&&typeof t==="string"){this.szBasePath=t}else{return a}if(i&&typeof i==="function"){this.fnCallBack=i}else{return a}this.decodeWorker=null;this.streamOpenMode=null;this.bOpenStream=false;this.iFrameNumOrTime=0;this.bSetPlayPositionType=Ge;this.audioRenderer=null;this.aAudioBuffer=[];this.iAudioBufferSize=0;this.aAudioEncBuffer=[];this.iAudioEncBufferSize=0;this.bWriteAudEnc=true;this.oSuperRender=null;this.aVideoFrameBuffer=[];this.YUVBufferSize=Be;this.szOSDTime=null;this.bWriteYUVData=false;this.iYUV10size=0;this.aVideoYUVBuffer=[];this.bWritePCMData=false;this.iAudioBuffer500Size=0;this.aAudioPCMBuffer=[];this.bWriteRawData=false;this.iRawDataSize=0;this.aRawDataBuffer=[];this.bPlaySound=false;this.bPlay=false;this.bPause=false;this.bOnebyOne=false;this.bPlayRateChange=false;this.bAudioTypeSupport=true;this.audioNum=0;this.videoNum=0;this.SetPlayPositionFlag=false;this.FrameForwardLen=1;this.bOnlyPlaySound=false;this.dataCallBackFun=null;this.YUVBufSizeCBFun=null;this.AudEncodeDataCBFun=null;this.DecCallBackFun=null;this.DisplayCallBackFun=null;this.PCMCallBackFun=null;this.DecInfoYUV=Xe;this.DisplayInfoYUV=Ke;this.DecInfoPCM=$e;this.nWidth=0;this.nHeight=0;this.sCanvasId=null;this.aDisplayBuf=null;this.bVisibility=true;this.nDecFrameType=We;this.iCanvasWidth=0;this.iCanvasHeight=0;this.iZoomNum=0;this.iRatio_x=1;this.iRatio_y=1;this.stDisplayRect={top:0,left:0,right:0,bottom:0};this.bDisRect=false;this.stYUVRect={top:0,left:0,right:0,bottom:0};this.aInputDataLens=[];this.aInputDataBuffer=[];this.bIsGetYUV=false;this.bIsFirstFrame=true;this.iInputMaxBufSize=Fe;this.bIsInput=false;this.bIsInputBufOver=false;this.iInputDataLen=Ue;var l=this;this.setCallBack=function(e,t,i,r,a){var o=tt;o.id=n;o.cmd=t;o.data=i;o.errorCode=r;o.status=a;e.fnCallBack(o)};if(!Ze){Ze=true;var u=document.createElement("script");u.type="text/javascript";u.src=l.szBasePath+"AudioRenderer.js";var f=document.getElementsByTagName("head")[0];f.appendChild(u);u.onload=u.onreadystatechange=function(){if(!this.readyState||this.readyState==="loaded"||this.readyState==="complete"){if(et){console.log(">>>JS AudioRenderer.js load finish!")}}}}if(!Qe){Qe=true;var c=document.createElement("script");c.type="text/javascript";c.src=l.szBasePath+"SuperRender_10.js";var d=document.getElementsByTagName("head")[0];d.appendChild(c);c.onload=c.onreadystatechange=function(){if(!this.readyState||this.readyState==="loaded"||this.readyState==="complete"){if(et){console.log(">>>JS SuperRender_10.js load finish!")}}}}this.convertErrorCode=function(e){switch(e){case 1:return o;case 98:return a;default:return e}};this.arrayBufferCopy=function(e){var t=e.byteLength;var i=new Uint8Array(t);var n=new Uint8Array(e);var r=0;for(r=0;r<t;r++){i[r]=n[r]}return i};this.inputDataFun=function(){var e;var t;var i=0;l.bIsGetYUV=false;if(l.bIsInputBufOver){if(et){console.log(">>>JS inputDataFun over!")}e=new Uint8Array(1);t=new Uint8Array(e);var n={command:"InputData",data:t.buffer,dataSize:0};l.decodeWorker.postMessage(n,[n.data])}else{if(l.bPlay&&(!l.bPause||l.bOnebyOne)||this.bOnlyPlaySound){while(l.aInputDataLens.length>0){i+=l.aInputDataLens.shift();if(i>l.iInputDataLen){break}}e=l.aInputDataBuffer.splice(0,i);if(et){console.log(">>>JS inputDataFun-len:%d",i)}t=new Uint8Array(e);var n={command:"InputData",data:t.buffer,dataSize:i};l.decodeWorker.postMessage(n,[n.data])}}e=null;t=null};this.getPic=function(e,t){if(this.decodeWorker==null||this.oSuperRender==null){return s}if(!this.bPlay){return s}if(e&&typeof e==="function"){this.dataCallBackFun=e}else{return a}if(0===this.iZoomNum){this.stYUVRect.left=0;this.stYUVRect.top=0;this.stYUVRect.right=0;this.stYUVRect.bottom=0}else{if(0===this.iCanvasWidth||0===this.iCanvasHeight){this.stYUVRect.left=0;this.stYUVRect.top=0;this.stYUVRect.right=0;this.stYUVRect.bottom=0}else{var i=this.nWidth/this.iCanvasWidth;var n=this.nHeight/this.iCanvasHeight;this.stYUVRect.left=Math.round(this.stDisplayRect.left*i);this.stYUVRect.top=Math.round(this.stDisplayRect.top*n);this.stYUVRect.right=Math.round(this.stDisplayRect.right*i);this.stYUVRect.bottom=Math.round(this.stDisplayRect.bottom*n)}if(this.stYUVRect.right-this.stYUVRect.left<32||this.stYUVRect.bottom-this.stYUVRect.top<32){return a}}if(this.aDisplayBuf==null){return s}var r=this.arrayBufferCopy(this.aDisplayBuf);var l={command:t,data:r.buffer,width:this.nWidth,height:this.nHeight,rect:this.stYUVRect};if(et){console.log(">>>JS capture nWidth = %d,nWidth = %d",this.nWidth,this.nHeight)}this.decodeWorker.postMessage(l,[l.data]);return o};this.createWorker=function(e){if(window.Worker){if(this.decodeWorker==null){this.decodeWorker=new Worker(l.szBasePath+"DecodeWorker.js");if(this.decodeWorker==null){return V}}this.decodeWorker.onmessage=function(t){var i=null;var n=t.data;switch(n.function){case"printLog":console.log("print JSPlayerSDK log failed");break;case"loaded":i="loaded";e.setCallBack(e,"loaded",0,0,true);break;case"SetStreamOpenMode":i="SetStreamOpenMode";break;case"OpenStream":i="OpenStream";if(1===n.errorCode){l.bOpenStream=true;return}break;case"InputData":i="InputData";if(n.errorCode===h){l.bIsInputBufOver=true;l.inputDataFun();if(et){console.log(">>>JS InputData PLAYM4_BUF_OVER")}}if(n.errorCode===E){l.bIsInputBufOver=false;if(et){console.log(">>>JS InputData PLAYM4_NEED_MORE_DATA")}}break;case"GetFrameData":i="GetFrameData";if(!l.bOnlyPlaySound){if(et){console.log(">>>JS mainthread display GetFrameData 1-1")}if(n.data!=null&&n.frameInfo!=null){var r=n.frameInfo.width;var s=n.frameInfo.height}if(!l.bPlay){return}if(!l.bIsFirstFrame&&n.errorCode===E){l.bIsInputBufOver=false;setTimeout(l.inputDataFun(),5);break}else if(l.bIsInputBufOver){l.inputDataFun()}else{if(n.type==="videoType"){if(l.aInputDataLens.length>0&&l.bIsInput){l.inputDataFun();l.bIsInput=false}else{l.bIsGetYUV=true}l.bIsFirstFrame=false}}}if(et){console.log(">>>JS mainthread display GetFrameData bVisibility:"+l.bVisibility)}if(l.bVisibility){if(o===n.errorCode){switch(n.type){case"videoType":if(et){console.log(">>>JS mainthread display GetFrameData type:"+n.type)}if(n.data==null||n.frameInfo==null){return a}if(l.DecCallBackFun!=null){if(et){console.log(">>>JS MainThread-GetYUVData")}l.DecInfoYUV.height=n.frameInfo.height;l.DecInfoYUV.width=n.frameInfo.width;l.DecInfoYUV.frameNum=n.frameInfo.frameNum;l.DecInfoYUV.yuvData=new Uint8Array(n.data);l.DecCallBackFun(l.DecInfoYUV)}l.bIsFirstFrame=false;e.nWidth=n.frameInfo.width;e.nHeight=n.frameInfo.height;var u=new Object;u.data=n.data;u.osdTime=n.osd;u.nWidth=n.frameInfo.width;u.nHeight=n.frameInfo.height;u.frameNum=n.frameInfo.frameNum;u.timeStamp=n.frameInfo.timeStamp;if(e.bWriteYUVData){var f=new Uint8Array(n.data);var c=e.aVideoYUVBuffer.length;for(var d=0,p=f.length;d<p;d++){e.aVideoYUVBuffer[c+d]=f[d]}e.iYUV10size++;f=null}if(e.bWriteYUVData&&e.iYUV10size>=qe){var y=new Uint8Array(e.aVideoYUVBuffer);e.downloadFile(y,"videoYUV.data");e.aVideoYUVBuffer.splice(0,e.aVideoYUVBuffer.length);e.bWriteYUVData=false;e.iYUV10size=0;y=null}if(et){console.log(">>>JS mainthread getVideoInfo Width:"+u.nWidth+",height:"+u.nHeight+",frameNum:"+u.frameNum)}e.aVideoFrameBuffer.push(u);u=null;var v=e.aVideoFrameBuffer.length;if(v>ze){if(et){console.log(">>>JS render loose frame iYUVNum:%d",v)}if(!e.bOnebyOne){e.aVideoFrameBuffer.splice(0,Oe)}}if(e.SetPlayPositionFlag&&e.bSetPlayPositionType===Je&&(n.frameInfo.frameNum===e.iFrameNumOrTime||n.frameInfo.timeStamp===e.iFrameNumOrTime)){e.setCallBack(e,"SetFrameNum",0,0,false);e.bIsFirstFrame=true;break}if(e.bOnebyOne){if(e.aVideoFrameBuffer.length>=Ae){e.setCallBack(e,"OnebyOne",0,0,false);e.bIsFirstFrame=true;break}}break;case"audioType":if(et){console.log(">>>JS mainthread display GetFrameData 2-3 type:"+n.type+".bOnlyPlaySound:"+l.bOnlyPlaySound)}if(e.bPlaySound&&!e.bPlayRateChange||l.bOnlyPlaySound){if(et){console.log(">>>JS mainthread display GetFrameData 2-4 type"+n.type+"bOnlyPlaySound:"+l.bOnlyPlaySound)}if(l.PCMCallBackFun!=null){if(et){console.log(">>>JS MainThread-GetYUVData")}l.DecInfoPCM.sampleRate=n.frameInfo.samplesPerSec;l.DecInfoPCM.channel=n.frameInfo.channels;l.DecInfoPCM.bitsPerSample=n.frameInfo.bitsPerSample;l.DecInfoPCM.pcmData=new Uint8Array(n.data);l.DecInfoPCM.length=l.DecInfoPCM.pcmData.length;l.PCMCallBackFun(l.DecInfoPCM)}var f=new Uint8Array(n.data);var c=e.aAudioBuffer.length;for(var d=0,p=f.length;d<p;d++){e.aAudioBuffer[c+d]=f[d]}e.iAudioBufferSize++;f=null;if(et){console.log(">>>JS play sound 1 iAudioBufferNum:%d, BufferLen:%d",e.iAudioBufferSize,e.aAudioBuffer.length)}if(e.bWritePCMData){var f=new Uint8Array(n.data);var c=e.aAudioPCMBuffer.length;for(var d=0,p=f.length;d<p;d++){e.aAudioPCMBuffer[c+d]=f[d]}e.iAudioBuffer500Size++;f=null}if(e.bWritePCMData&&e.iAudioBuffer500Size>=He){var m=new Uint8Array(e.aAudioPCMBuffer);e.downloadFile(m,"audioPCM.data");e.aAudioPCMBuffer.splice(0,e.aAudioPCMBuffer.length);e.bWritePCMData=false;e.iAudioBuffer500Size=0;m=null}if(e.iAudioBufferSize>=Ie){if(et){console.log(">>>JS play sound 2 iAudioBufferNum:%d, BufferLen:%d",e.iAudioBufferSize,e.aAudioBuffer.length)}e.audioRenderer.Play(e.aAudioBuffer,e.aAudioBuffer.length,n.frameInfo);if(et){console.log(">>>JS mainthread display Audio push 3 sysTime:"+((new Date).getMonth()+1)+"-"+(new Date).getDate()+" "+(new Date).getHours()+":"+(new Date).getMinutes()+":"+(new Date).getSeconds()+"."+(new Date).getMilliseconds())}e.aAudioBuffer.splice(0,e.aAudioBuffer.length);e.aAudioBuffer.length=0;e.iAudioBufferSize=0}}break;case"privateType":if(et){console.log(">>>JS mainthread display GetFrameData 2-5 type"+n.type+"bOnlyPlaySound:"+l.bOnlyPlaySound)}break;default:if(et){console.log(">>>JS mainthread display GetFrameData 2-6 type:"+n.type+",bOnlyPlaySound:"+l.bOnlyPlaySound)}break}}else{if(et){console.log(">>>JS mainthread GetFrameData is not Ok,errorCode:"+n.errorCode)}}}break;case"GetRawData":i="GetRawData";if(e.bWriteRawData){var b=new Uint8Array(n.data);var S=e.aRawDataBuffer.length;for(var d=0,p=b.length;d<p;d++){e.aRawDataBuffer[S+d]=b[d]}e.iRawDataSize++;b=null}if(e.bWriteRawData&&e.iRawDataSize>=Ve){var w=new Uint8Array(e.aRawDataBuffer);e.downloadFile(w,"rawBuffer.data");e.aRawDataBuffer.splice(0,e.aRawDataBuffer.length);e.bWriteRawData=false;e.iRawDataSize=0;w=null}break;case"PlaySound":i="PlaySound";break;case"SetPlayPosition":console.log(">>>JS mainthread SetPlayPosition error,errorCode:"+n.errorCode);break;case"GetJPEG":i="GetJPEG";var P=n.data;e.dataCallBackFun(P);break;case"GetBMP":i="GetBMP";var C=n.data;e.dataCallBackFun(C);break;case"GetAudEncodeData":if(et){console.log(">>>JS MainThread-GetAudEncodeData 1")}i="GetAudEncodeData";if(o===n.errorCode){if(l.AudEncodeDataCBFun!=null){if(et){console.log(">>>JS MainThread-GetAudEncodeData 1-2")}l.AudEncodeDataCBFun(new Uint8Array(n.data))}if(this.bWriteAudEnc){var f=new Uint8Array(n.data);var c=e.aAudioEncBuffer.length;for(var d=0,p=f.length;d<p;d++){e.aAudioEncBuffer[c+d]=f[d]}e.iAudioEncBufferSize++;f=null}if(this.bWriteAudEnc&&e.iAudioEncBufferSize>=je){e.downloadFile(e.aAudioEncBuffer,audioEnc.data);this.bWriteAudEnc=false;e.iAudioEncBufferSize=0}}else{if(et){console.log(">>>JS MainThread-GetAudEncodeData 2 err")}}break;default:break}if("GetFrameData"!==i||"GetAudEncodeData"!==i){e.setCallBack(e,i,0,e.convertErrorCode(n.errorCode),true)}else{if(g===n.errorCode||ae===n.errorCode||re===n.errorCode){e.setCallBack(e,i,0,e.convertErrorCode(n.errorCode),true)}}}}};this.createWorker(l);this.draw=function(){if(l.bPlay){if(!l.bPause||l.bOnebyOne){requestAnimationFrame(l.draw);var e=l.aVideoFrameBuffer.length;if(l.YUVBufSizeCBFun!=null){l.YUVBufSizeCBFun(e)}if(l.bOnebyOne){if(e<=Re){l.setCallBack(l,"OnebyOne",0,E,true)}if(e<=l.FrameForwardLen+1){l.setCallBack(l,"OnebyOne",0,E,true);return}else{var t=l.FrameForwardLen;while(t>1){var i=l.aVideoFrameBuffer.shift();t--}}l.bOnebyOne=false}if(et){console.log(">>>JS Draw aVideoFrameBuffer.length == "+e)}if(e>l.YUVBufferSize){var n=l.aVideoFrameBuffer.shift();if(l.SetPlayPositionFlag){if(et){console.log(">>>JS SetPlayPositionFlag frameNumOrTime:"+l.iFrameNumOrTime+",currentFrameNum:"+n.frameNum)}if(n.frameNum!=l.iFrameNumOrTime){if(et){console.log(">>>JS frameNum!= frameNumOrTime:"+n.frameNum)}return}if(l.bSetPlayPositionType===Je){l.bPause=true}l.SetPlayPositionFlag=false}if(et){console.log(">>>after display frameNum:"+n.frameNum)}l.aDisplayBuf=n.data;var r=new Uint8Array(l.aDisplayBuf);if((l.nWidth==1920&&l.nHeight==1088||l.nWidth==2688&&l.nHeight==1520)&&!l.bDisRect){var a=document.getElementById(l.sCanvasId).getBoundingClientRect();var o=a.width;var s=a.height-8;l.stDisRect={top:0,left:0,right:o,bottom:s};l.oSuperRender.SR_SetDisplayRect(l.stDisRect)}else if(l.nWidth==640&&l.nHeight==368&&!l.bDisRect){var a=document.getElementById(l.sCanvasId).getBoundingClientRect();var o=a.width;var s=Math.floor(a.height*360/368);l.stDisRect={top:0,left:0,right:o,bottom:s};l.oSuperRender.SR_SetDisplayRect(l.stDisRect)}else if(l.nWidth==368&&l.nHeight==640&&!l.bDisRect){var a=document.getElementById(l.sCanvasId).getBoundingClientRect();var o=Math.floor(a.width*360/368);var s=a.height;l.stDisRect={top:0,left:0,right:o,bottom:s};l.oSuperRender.SR_SetDisplayRect(l.stDisRect)}if(et){console.log(">>>JS SR_DisplayFrameData 1 nWidth:%d, nHeight:%d, nWidth1:%d, nHeight1:%d",n.nWidth,n.nHeight,l.nWidth,l.nHeight)}l.oSuperRender.SR_DisplayFrameData(n.nWidth,n.nHeight,r);if(l.DisplayCallBackFun!=null){if(et){console.log(">>>JS MainThread-DisplayCallBackFun")}l.DisplayInfoYUV.height=n.nHeight;l.DisplayInfoYUV.width=n.nWidth;l.DisplayInfoYUV.frameNum=n.frameNum;l.DisplayInfoYUV.yuvData=new Uint8Array(r);l.DisplayCallBackFun(l.DisplayInfoYUV)}if(et&&l.bOnebyOne){console.log("OnebyOne frameNum:"+n.frameNum)}if(et){console.log(">>>JS SR_DisplayFrameData 2 nWidth:%d, nHeight:%d, nWidth1:%d, nHeight1:%d",n.nWidth,n.nHeight,l.nWidth,l.nHeight)}r=null;l.szOSDTime=n.osdTime;n=null}else{l.setCallBack(l,"Play",0,E,true)}}}else{if(!l.bPlay){if(et){console.log(">>>JS mainThread stop play jsplaySDKInterface")}l.aVideoFrameBuffer.splice(0,l.aVideoFrameBuffer.length);l.aAudioBuffer.splice(0,l.aAudioBuffer.length)}}};this.checkAudioType=function(e){var t=function e(t,i){var n=t[i]&255|(t[i+1]&255)<<8|(t[i+2]&255)<<16|(t[i+3]&255)<<24;return n};var i=[e[12],e[13],0,0];var n=t(i,0);switch(n){case Se.AUDIO_G711_A:case Se.AUDIO_G711_U:case Se.AUDIO_G722_1:case Se.AUDIO_G726_2:case Se.AUDIO_G726_A:case Se.AUDIO_G726_U:case Se.AUDIO_AACLC:case Se.AUDIO_MPEG:return o;default:return g}}}n(e,[{key:"PlayM4_SetCurrentFrameNum",value:function e(t,i){if(t<0){return a}this.PlayM4_SetPlayPosition(0,t,i);return o}},{key:"PlayM4_SetPlayPosition",value:function e(t,i,n){this.SetPlayPositionFlag=true;this.iFrameNumOrTime=i;this.bSetPlayPositionType=n;this.decodeWorker.postMessage({command:"SetPlayPosition",data:this.iFrameNumOrTime,type:t});this.aAudioBuffer.splice(0,this.aAudioBuffer.length);this.iAudioBufferSize=0;this.aVideoFrameBuffer.splice(0,this.aVideoFrameBuffer.length);this.aInputDataBuffer.splice(0,this.aInputDataBuffer.length);this.aInputDataLens.splice(0,this.aInputDataLens.length);this.bIsFirstFrame=true;this.bIsInputBufOver=false}},{key:"PlayM4_OpenPlayerSDKPrintLog",value:function e(t){if(t===true){et=true;this.decodeWorker.postMessage({command:"printLog",data:t})}else{et=false;this.decodeWorker.postMessage({command:"printLog",data:t})}return o}},{key:"PlayM4_DownloadYUVdata",value:function e(){this.bWriteYUVData=true;return o}},{key:"PlayM4_DownloadPCMdata",value:function e(){this.bWritePCMData=true;return o}},{key:"PlayM4_SetDecCallBack",value:function e(t){if(et){console.log(">>>JS MainThread-PlayM4_SetDecCallBack")}if(t&&typeof t==="function"){this.DecCallBackFun=t;return o}else{return a}}},{key:"PlayM4_SetDisplayCallBack",value:function e(t){if(et){console.log(">>>JS MainThread-PlayM4_SetDisplayCallBack")}if(t&&typeof t==="function"){this.DisplayCallBackFun=t;return o}else{return a}}},{key:"PlayM4_SetPCMCallBack",value:function e(t){if(et){console.log(">>>JS MainThread-PlayM4_SetDisplayCallBack")}if(t&&typeof t==="function"){this.PCMCallBackFun=t;return o}else{return a}}},{key:"PlayM4_SetStreamOpenMode",value:function e(t){if(t==null||t===undefined){return a}if(t!==xe&&t!==Te){return a}this.streamOpenMode=t;if(et){console.log(">>>JS PlayM4_SetStreamOpenMode nMode:"+t)}return o}},{key:"PlayM4_OpenStream",value:function e(t,i,n){if(et){console.log(">>>JS PlayM4_OpenStream 1 nSysTime:"+((new Date).getMonth()+1)+"-"+(new Date).getDate()+" "+(new Date).getHours()+":"+(new Date).getMinutes()+":"+(new Date).getSeconds()+"."+(new Date).getMilliseconds())}if(this.decodeWorker==null){return s}if(t==null||i<=0||n<=0){return a}this.bPlay=false;this.bPause=false;this.bOnebyOne=false;this.bIsFirstFrame=true;this.bIsGetYUV=false;this.bIsInput=false;var r=this.checkAudioType(t);if(o!==r){this.bAudioTypeSupport=false}else{this.bAudioTypeSupport=true}this.decodeWorker.postMessage({command:"SetStreamOpenMode",data:this.streamOpenMode});if(et){console.log(">>>JS PlayM4_OpenStream 2 nSysTime:"+((new Date).getMonth()+1)+"-"+(new Date).getDate()+" "+(new Date).getHours()+":"+(new Date).getMinutes()+":"+(new Date).getSeconds()+"."+(new Date).getMilliseconds())}this.decodeWorker.postMessage({command:"OpenStream",data:t,dataSize:i,bufPoolSize:n});if(et){console.log(">>>JS PlayM4_OpenStream 3 nSysTime:"+((new Date).getMonth()+1)+"-"+(new Date).getDate()+" "+(new Date).getHours()+":"+(new Date).getMinutes()+":"+(new Date).getSeconds()+"."+(new Date).getMilliseconds())}this.bOpenStream=true;return o}},{key:"PlayM4_CloseStream",value:function e(){if(et){console.log(">>>JS PlayM4_CloseStream 1 nSysTime:"+((new Date).getMonth()+1)+"-"+(new Date).getDate()+" "+(new Date).getHours()+":"+(new Date).getMinutes()+":"+(new Date).getSeconds()+"."+(new Date).getMilliseconds())}if(this.decodeWorker===null||this.bOpenStream===false){return s}this.bOnlyPlaySound=false;this.PlayM4_Stop();this.decodeWorker.postMessage({command:"CloseStream"});if(this.oSuperRender!==null){this.oSuperRender.SR_Destroy();this.oSuperRender=null}if(this.audioRenderer!==null){this.audioRenderer.Stop();this.audioRenderer=null}this.aAudioBuffer.splice(0,this.aAudioBuffer.length);this.aVideoFrameBuffer.splice(0,this.aVideoFrameBuffer.length);this.aInputDataBuffer.splice(0,this.aInputDataBuffer.length);this.aInputDataLens.splice(0,this.aInputDataLens.length);this.aAudioEncBuffer.splice(0,this.aAudioEncBuffer.length);this.aVideoYUVBuffer.splice(0,this.aVideoYUVBuffer.length);this.aAudioPCMBuffer.splice(0,this.aAudioPCMBuffer.length);this.aRawDataBuffer.splice(0,this.aRawDataBuffer.length);this.bOpenStream=false;this.iAudioBufferSize=0;this.szOSDTime=null;return o}},{key:"PlayM4_Destroy",value:function e(){if(et){console.log(">>>JS PlayM4_Destroy 1 nSysTime:"+((new Date).getMonth()+1)+"-"+(new Date).getDate()+" "+(new Date).getHours()+":"+(new Date).getMinutes()+":"+(new Date).getSeconds()+"."+(new Date).getMilliseconds())}if(this.decodeWorker===null){return o}this.PlayM4_CloseStream();this.decodeWorker.terminate();this.decodeWorker=null;return o}},{key:"PlayM4_InputData",value:function e(t,i){if(this.decodeWorker===null||this.bOpenStream===false){return s}var n=this.aInputDataBuffer.length;if(i===4){var r=new Uint8Array(t.buffer);if(r[0]===1&&r[1]===2&&r[2]===3&&r[3]===4){if(et){console.log(">>>JS PlaySDKInterface PlayM4_InputData:intput end")}if(this.bIsFirstFrame){if(et){console.log(">>>JS inputData FirstTime")}this.inputDataFun()}else{if(this.bIsGetYUV){this.inputDataFun()}else{this.bIsInput=true}}r=null;return o}}if(n+i>this.iInputMaxBufSize){console.log("input over");this.inputDataFun();return h}var a=null;var l=i;switch(this.streamOpenMode){case Te:a=new Uint8Array(t.buffer);this.aInputDataLens.push(i);break;case xe:l=i+4;var u=new Uint32Array([i]);var f=new Uint8Array(u.buffer);a=new Uint8Array(l);a.set(f,0);a.set(t,4);u=null;f=null;this.aInputDataLens.push(i+4);break;default:return g}for(var c=0;c<l;c++){this.aInputDataBuffer[n+c]=a[c]}a=null;if(et){console.log(">>>JS PlayM4_InputData 1： nSize:%d, iInputBufLen:%d, iInputMaxBufSize;%d",i,this.aInputDataBuffer.length,this.iInputMaxBufSize)}if(this.bOnlyPlaySound){if(et){console.log(">>>JS PlayM4_InputData: OnlyPlaySound")}this.inputDataFun()}else{if(this.bIsFirstFrame){if(et){console.log(">>>JS PlayM4_InputData is firstFrame")}this.inputDataFun()}else{if(this.bIsGetYUV){if(et){console.log(">>>JS PlayM4_InputData is GetYUV")}this.inputDataFun()}else{if(et){console.log(">>>JS PlayM4_InputData 1-3 sysTime:"+((new Date).getMonth()+1)+"-"+(new Date).getDate()+" "+(new Date).getHours()+":"+(new Date).getMinutes()+":"+(new Date).getSeconds()+"."+(new Date).getMilliseconds())}this.bIsInput=true}}}return o}},{key:"PlayM4_Play",value:function e(t){if(this.decodeWorker===null){return s}if(et){console.log(">>>JS PlayM4_Play canvasID: "+t)}if(t===null){this.bOnlyPlaySound=true;this.sCanvasId=null}else{if(typeof t!=="string"){return a}if(this.bOnebyOne){this.bPlayRateChange=false;this.bOnebyOne=false;this.bPause=false;this.draw()}if(this.bPlay){return o}if(this.oSuperRender==null){this.oSuperRender=new SuperRender(t,this.szBasePath);if(this.oSuperRender==null){return J}}this.sCanvasId=t;this.bPlay=true;this.bPause=false;this.bOnebyOne=false;this.bPlaySound=false;this.bPlayRateChange=false;this.bOnlyPlaySound=false;this.draw()}if(this.audioRenderer==null){this.audioRenderer=new AudioRenderer;if(this.audioRenderer==null){return J}}return o}},{key:"PlayM4_Stop",value:function e(){if(et){console.log(">>>JS PlayM4_Stop 1")}if(this.decodeWorker==null||this.oSuperRender==null){return s}if(!this.bPlay){return s}if(this.bPlaySound){this.PlayM4_StopSound();this.bPlaySound=true}this.bPlay=false;this.bOnebyOne=false;this.bPause=false;this.oSuperRender.SR_SetDisplayRect(null);this.iZoomNum=0;this.bDisRect=false;this.oSuperRender.SR_DisplayFrameData(this.nWidth,this.nHeight,null);if(et){console.log(">>>JS PlayM4_Stop 2")}return o}},{key:"PlayM4_PlayRate",value:function e(t){if(this.decodeWorker==null){return s}if(t===1){this.bPlayRateChange=false}else{this.bPlayRateChange=true}if(t<1){t=1}this.iInputDataLen=t*Ue;return o}},{key:"PlayM4_Pause",value:function e(t){if(this.decodeWorker==null||this.oSuperRender==null){return s}if(!this.bPlay){return s}if(this.bOnebyOne){return s}if(typeof t!=="boolean"){return a}this.bPause=t;this.bIsFirstFrame=true;if(et){console.log(">>>JS PlayM4_Pause");console.log(t)}if(t){if(this.bPlaySound){this.PlayM4_StopSound();this.bPlaySound=true}}else{if(this.bPlaySound){this.PlayM4_PlaySound()}this.draw()}return o}},{key:"PlayM4_OneByOne",value:function e(t){if(this.decodeWorker==null||this.oSuperRender==null){return s}if(!this.bPlay){return s}if(t>10||t<=0){return a}this.iInputDataLen=Ue;this.FrameForwardLen=t;this.bPause=true;this.bOnebyOne=true;this.bIsFirstFrame=true;this.draw();return o}},{key:"PlayM4_PlaySound",value:function e(t){if(this.decodeWorker===null||this.bOpenStream===false){return s}if(!this.bAudioTypeSupport){return g}if(t<0||t>16){return a}if(this.audioRenderer==null){this.audioRenderer=new AudioRenderer;if(this.audioRenderer==null){return J}}this.audioRenderer.SetWndNum(t);this.bPlaySound=true;return o}},{key:"PlayM4_StopSound",value:function e(){if(this.decodeWorker==null||this.audioRenderer==null){return s}if(!this.bPlaySound){return s}this.bPlaySound=false;return o}},{key:"PlayM4_SetDisplayBuf",value:function e(t){if(this.decodeWorker==null){return s}if(t<=0){return a}this.YUVBufferSize=t;return o}},{key:"PlayM4_SetSecretKey",value:function e(t,i,n){if(this.decodeWorker==null||this.bOpenStream===false){return s}if(i==null){return a}if(Pe===t){if(128===n){if(i==null||i===undefined){return a}}else{return a}}else if(we===t){}else{return a}this.decodeWorker.postMessage({command:"SetSecretKey",data:i,nKeyType:t,nKeyLen:n});return o}},{key:"PlayM4_SetDecodeFrameType",value:function e(t){if(this.decodeWorker==null||this.oSuperRender==null){return s}if(t!==We&&t!==Ee){return a}if(et){console.log(">>>JS PlayM4_SetDecodeFrameType");console.log(t)}this.nDecFrameType=t;this.decodeWorker.postMessage({command:"SetDecodeFrameType",data:t});return o}},{key:"PlayM4_SetIFrameDecInterval",value:function e(t){if(this.nDecFrameType!==Ee){return s}if(t<0){return a}this.decodeWorker.postMessage({command:"SetIFrameDecInterval",data:t});return o}},{key:"PlayM4_SetDisplayRegion",value:function e(t,i){if(et){console.log("PlayM4_SetDisplayRegion ")}if(this.decodeWorker===null||this.bPlay===false||this.oSuperRender===null){return s}if(this.canvasId===null){return s}if(i===true){if(t===null||t===undefined){if(et){console.log("PlayM4_SetDisplayRegion: dipalyRect is null")}return a}if(typeof t.left==="number"&&typeof t.top==="number"&&typeof t.right==="number"&&typeof t.bottom==="number"){if(t.right<0||t.left<0||t.top<0||t.bottom<0){if(et){console.log("PlayM4_SetDisplayRegion: dipalyRect rectParameter is wrong")}return a}var n=t.left;var r=t.right;var l=t.top;var u=t.bottom;var f=document.getElementById(this.sCanvasId).getBoundingClientRect();this.iCanvasWidth=f.width;this.iCanvasHeight=f.height;if(r-n<16||u-l<16||r-n>this.iCanvasWidth||u-l>this.iCanvasHeight){return a}if(this.iZoomNum!==0){n=Math.round(n/this.iRatio_x)+this.stDisplayRect.left;l=Math.round(l/this.iRatio_y)+this.stDisplayRect.top;r=Math.round(r/this.iRatio_x)+this.stDisplayRect.left;u=Math.round(u/this.iRatio_y)+this.stDisplayRect.top}this.stDisplayRect={top:l,left:n,right:r,bottom:u};this.oSuperRender.SR_SetDisplayRect(this.stDisplayRect);this.bDisRect=true;var c=r-n;var d=u-l;this.iRatio_x=this.iCanvasWidth/c;this.iRatio_y=this.iCanvasHeight/d;this.iZoomNum++}else{return a}}else{this.oSuperRender.SR_SetDisplayRect(null);this.iZoomNum=0;this.bDisRect=false}if(this.bPause||this.bOnebyOne||this.bPlayRateChange){this.oSuperRender.SR_DisplayFrameData(this.nWidth,this.nHeight,new Uint8Array(this.aDisplayBuf))}return o}},{key:"PlayM4_GetBMP",value:function e(t){return this.getPic(t,"GetBMP")}},{key:"PlayM4_GetJPEG",value:function e(t){return this.getPic(t,"GetJPEG")}},{key:"PlayM4_SetVolume",value:function e(t){if(this.decodeWorker==null){return s}if(this.audioRenderer==null){return s}if(t<0||t>100){return a}this.audioRenderer.SetVolume(t/100);return o}},{key:"PlayM4_GetVolume",value:function e(t){if(this.decodeWorker==null){return s}if(this.audioRenderer==null){return s}if(t&&typeof t==="function"){var i=this.audioRenderer.GetVolume();if(i===null){return Y}else{t(Math.round(i*10)*10);return o}}else{return a}}},{key:"PlayM4_GetOSDTime",value:function e(t){if(this.decodeWorker==null){return s}if(!this.bPlay){return s}if(t&&typeof t==="function"){t(this.szOSDTime);return o}else{return a}}},{key:"PlayM4_IsVisible",value:function e(t){this.bVisibility=t;if(et){console.log(">>>JS PlayM4_IsVisible visibility:"+t)}return o}},{key:"PlayM4_GetSdkVersion",value:function e(){return"07020139"}},{key:"PlayM4_GetBuildDate",value:function e(){return"20200409"}},{key:"PlayM4_GetInputBufSize",value:function e(){return this.aInputDataBuffer.length}},{key:"PlayM4_SetInputBufSize",value:function e(t){if(t>0){this.iInputMaxBufSize=t;return o}else{return a}}},{key:"PlayM4_GetYUVBufSize",value:function e(){return this.aVideoFrameBuffer.length}},{key:"PlayM4_GetFrameResolution",value:function e(t){if(this.decodeWorker==null){return s}if(t&&typeof t==="function"){t(this.nWidth,this.nHeight);return o}else{return a}}},{key:"PlayM4_RegisterYUVBufSizeCB",value:function e(t){if(t&&typeof t==="function"){this.YUVBufSizeCBFun=t;return o}else{return a}}},{key:"PlayM4_UnRegisterYUVBufSizeCB",value:function e(){if(this.YUVBufSizeCBFun!=null){this.YUVBufSizeCBFun=null}return o}},{key:"PlayM4_ClearCanvas",value:function e(){if(this.oSuperRender==null){return s}this.oSuperRender.SR_DisplayFrameData(this.nWidth,this.nHeight,null);return o}},{key:"PlayM4_ReleaseInputBuffer",value:function e(){if(this.aInputDataBuffer===null){return s}this.aInputDataBuffer.splice(0,this.aInputDataBuffer.length);this.aInputDataLens.splice(0,this.aInputDataLens.length);return o}},{key:"PlayM4_GetDecodeFrameType",value:function e(){return this.nDecFrameType}},{key:"PlayM4_CreateAudEncode",value:function e(t){if(et){console.log(">>>JS JSMainThread-PlayM4_CreateAudEncode")}if(this.decodeWorker==null){return ne}if(t<oe||t>ue){return X}this.decodeWorker.postMessage({command:"CreateAudEncode",encodertype:t});return o}},{key:"PlayM4_SetAudEncodeParam",value:function e(t,i,n,r){if(et){console.log(">>>JS MainThread-PlayM4_SetAudEncodeParam:nSampleRate:%d nChannel:%d nBitRate:%d nBitWidth:%d",t,i,n,r)}if(this.decodeWorker==null){return ne}if(i!=fe||r!=he){return X}this.decodeWorker.postMessage({command:"SetAudEncodeParam",samplerate:t,channel:i,bitrate:n,bitwidth:r});return o}},{key:"PlayM4_InputAudEncodeData",value:function e(t,i){if(et){console.log(">>>JS MainThread-PlayM4_InputAudEncodeData")}if(this.decodeWorker==null){return ne}this.decodeWorker.postMessage({command:"InputAudEncodeData",data:t,dataSize:i});return o}},{key:"PlayM4_DestroyAudEncode",value:function e(){if(et){console.log(">>>JS MainThread-PlayM4_DestroyAudEncode")}if(this.decodeWorker==null){return ne}this.aAudioEncBuffer.splice(0,this.aAudioEncBuffer.length);this.iAudioEncBufferSize=0;this.decodeWorker.postMessage({command:"DestroyAudEncode"});return o}},{key:"PlayM4_GetAudioEmcBuf",value:function e(){return this.aAudioEncBuffer}},{key:"PlayM4_RegisterAudEncodeCB",value:function e(t){if(et){console.log(">>>JS MainThread-PlayM4_RegisterAudEncodeCB")}if(t&&typeof t==="function"){this.AudEncodeDataCBFun=t;return o}else{return a}}},{key:"downloadFile",value:function e(t,i){var n=t;if(!(t instanceof Blob||t instanceof File)){n=new Blob([t])}var r=window.URL.createObjectURL(n);var a=window.document.createElement("a");a.href=r;a.download=i;var o=document.createEvent("MouseEvents");o.initEvent("click",true,true);a.dispatchEvent(o)}}]);return e}()},function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.JMuxmer=undefined;var n=function(){function e(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||false;n.configurable=true;if("value"in n)n.writable=true;Object.defineProperty(e,n.key,n)}}return function(t,i,n){if(i)e(t.prototype,i);if(n)e(t,n);return t}}();var r=i(0);var a=g(r);var o=i(7);var s=i(8);var l=i(9);var u=i(2);var f=m(u);var c=i(26);var d=m(c);var h=i(30);var p=m(h);var y=i(1);var v=m(y);function m(e){return e&&e.__esModule?e:{default:e}}function g(e){if(e&&e.__esModule){return e}else{var t={};if(e!=null){for(var i in e){if(Object.prototype.hasOwnProperty.call(e,i))t[i]=e[i]}}t.default=e;return t}}function b(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}function S(e,t){if(!e){throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return t&&(typeof t==="object"||typeof t==="function")?t:e}function w(e,t){if(typeof t!=="function"&&t!==null){throw new TypeError("Super expression must either be null or a function, not "+typeof t)}e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:false,writable:true,configurable:true}});if(t)Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t}window.MediaSource=window.MediaSource||window.WebKitMediaSource;var P=function(e){w(t,e);n(t,null,[{key:"isSupported",value:function e(t){return window.MediaSource&&window.MediaSource.isTypeSupported(t)}}]);function t(e){b(this,t);var i=S(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,"jmuxer"));window.MediaSource=window.MediaSource||window.WebKitMediaSource;var n={node:"",mode:"both",flushingTime:1500,clearBuffer:true,onReady:null,fps:30,debug:false};i.options=v.default.extend({},n,e);if(i.options.debug){a.setLogger()}if(typeof i.options.node==="string"&&i.options.node==""){a.error("no video element were found to render, provide a valid video element")}if(!i.options.fps){i.options.fps=30}i.frameDuration=1e3/i.options.fps|0;i.node=typeof i.options.node==="string"?document.getElementById(i.options.node):i.options.node;i.sourceBuffers={};i.isMSESupported=!!window.MediaSource;if(!i.isMSESupported){return S(i)}i.setupMSE();i.remuxController=new d.default(i.options.clearBuffer);i.remuxController.addTrack(i.options.mode);i.mseReady=false;i.lastCleaningTime=Date.now();i.keyframeCache=[];i.frameCounter=0;i.remuxController.on("buffer",i.onBuffer.bind(i));i.remuxController.on("ready",i.createBuffer.bind(i));i.startInterval();return i}n(t,[{key:"setupMSE",value:function e(){this.mediaSource=new MediaSource;this.node.src=URL.createObjectURL(this.mediaSource);this.mediaSource.addEventListener("sourceopen",this.onMSEOpen.bind(this));this.mediaSource.addEventListener("sourceclose",this.onMSEClose.bind(this));this.mediaSource.addEventListener("webkitsourceopen",this.onMSEOpen.bind(this));this.mediaSource.addEventListener("webkitsourceclose",this.onMSEClose.bind(this))}},{key:"feed",value:function e(t){var i=false,n=void 0,r=void 0,o=void 0,u={video:[],audio:[]};if(!t||!this.remuxController)return;o=t.duration?parseInt(t.duration):0;if(t.video){n=s.H264Parser.extractNALu(t.video);if(n.length>0){u.video=this.getVideoFrames(n,o);i=true}}if(t.audio){r=l.AACParser.extractAAC(t.audio);if(r.length>0){u.audio=this.getAudioFrames(r,o);i=true}}if(!i){a.error("Input object must have video and/or audio property. Make sure it is not empty and valid typed array");return}this.remuxController.remux(u)}},{key:"getVideoFrames",value:function e(t,i){var n=void 0,r=[],a=[],s=void 0,l=void 0,u=0,f=[];var c=true;var d=false;var h=undefined;try{for(var p=t[Symbol.iterator](),y;!(c=(y=p.next()).done);c=true){n=y.value;s=new o.NALU(n);r.push(s);if(s.type()===o.NALU.IDR||s.type()===o.NALU.NDR){a.push({units:r});r=[];if(this.options.clearBuffer){if(s.type()===o.NALU.IDR){f.push(this.frameCounter)}this.frameCounter++}}}}catch(e){d=true;h=e}finally{try{if(!c&&p.return){p.return()}}finally{if(d){throw h}}}if(i){l=i/a.length|0;u=i-l*a.length}else{l=this.frameDuration}a.map(function(e){e.duration=u>0?l+1:l;if(u!==0){u--}});if(this.options.clearBuffer){f=f.map(function(e){return e*l/1e3});this.keyframeCache=this.keyframeCache.concat(f)}return a}},{key:"getAudioFrames",value:function e(t,i){var n=[],r=void 0,a=void 0,o=0;var s=true;var l=false;var u=undefined;try{for(var f=t[Symbol.iterator](),c;!(s=(c=f.next()).done);s=true){r=c.value;n.push({units:r})}}catch(e){l=true;u=e}finally{try{if(!s&&f.return){f.return()}}finally{if(l){throw u}}}if(i){a=i/n.length|0;o=i-a*n.length}else{a=this.frameDuration}n.map(function(e){e.duration=o>0?a+1:a;if(o!==0){o--}});return n}},{key:"destroy",value:function e(){this.stopInterval();if(this.mediaSource){try{if(this.bufferControllers){this.mediaSource.endOfStream()}}catch(e){a.error("mediasource is not available to end "+e.message)}this.mediaSource=null}if(this.remuxController){this.remuxController.destroy();this.remuxController=null}if(this.bufferControllers){for(var t in this.bufferControllers){this.bufferControllers[t].destroy()}this.bufferControllers=null}this.node=false;this.mseReady=false;this.videoStarted=false}},{key:"createBuffer",value:function e(){if(!this.mseReady||!this.remuxController||!this.remuxController.isReady()||this.bufferControllers)return;this.bufferControllers={};for(var i in this.remuxController.tracks){var n=this.remuxController.tracks[i];if(!t.isSupported(i+'/mp4; codecs="'+n.mp4track.codec+'"')){a.error("Browser does not support codec");return false}var r=this.mediaSource.addSourceBuffer(i+'/mp4; codecs="'+n.mp4track.codec+'"');this.bufferControllers[i]=new p.default(r,i);this.sourceBuffers[i]=r;this.bufferControllers[i].on("error",this.onBufferError.bind(this))}}},{key:"startInterval",value:function e(){var t=this;this.interval=setInterval(function(){if(t.bufferControllers){t.releaseBuffer();t.clearBuffer()}},this.options.flushingTime)}},{key:"stopInterval",value:function e(){if(this.interval){clearInterval(this.interval)}}},{key:"releaseBuffer",value:function e(){for(var t in this.bufferControllers){this.bufferControllers[t].doAppend()}}},{key:"getSafeBufferClearLimit",value:function e(t){var i=this.options.mode==="audio"&&t||0,n=void 0;for(var r=0;r<this.keyframeCache.length;r++){if(this.keyframeCache[r]>=t){break}n=this.keyframeCache[r]}if(n){this.keyframeCache=this.keyframeCache.filter(function(e){if(e<n){i=e}return e>=n})}return i}},{key:"clearBuffer",value:function e(){if(this.options.clearBuffer&&Date.now()-this.lastCleaningTime>1e4){for(var t in this.bufferControllers){var i=this.getSafeBufferClearLimit(this.node.currentTime);this.bufferControllers[t].initCleanup(i)}this.lastCleaningTime=Date.now()}}},{key:"onBuffer",value:function e(t){if(this.bufferControllers&&this.bufferControllers[t.type]){this.bufferControllers[t.type].feed(t.payload)}}},{key:"onMSEOpen",value:function e(){this.mseReady=true;if(typeof this.options.onReady==="function"){this.options.onReady();this.options.onReady=null}this.createBuffer()}},{key:"onMSEClose",value:function e(){this.mseReady=false;this.videoStarted=false}},{key:"onBufferError",value:function e(t){if(t.name=="QuotaExceeded"){this.bufferControllers[t.type].initCleanup(this.node.currentTime);return}if(this.mediaSource.sourceBuffers.length>0&&this.sourceBuffers[t.type]){this.mediaSource.removeSourceBuffer(this.sourceBuffers[t.type])}if(this.mediaSource.sourceBuffers.length==0){try{this.mediaSource.endOfStream()}catch(e){a.error("mediasource is not available to end")}}}}]);return t}(f.default);t.JMuxmer=P},function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:true});var n=function(){function e(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||false;n.configurable=true;if("value"in n)n.writable=true;Object.defineProperty(e,n.key,n)}}return function(t,i,n){if(i)e(t.prototype,i);if(n)e(t,n);return t}}();function r(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var a=t.ExpGolomb=function(){function e(t){r(this,e);this.data=t;this.index=0;this.bitLength=t.byteLength*8}n(e,[{key:"skipBits",value:function e(t){if(this.bitsAvailable<t){return false}this.index+=t}},{key:"readBits",value:function e(t){var i=arguments.length>1&&arguments[1]!==undefined?arguments[1]:true;var n=this.getBits(t,this.index,i);return n}},{key:"getBits",value:function e(t,i){var n=arguments.length>2&&arguments[2]!==undefined?arguments[2]:true;if(this.bitsAvailable<t){return 0}var r=i%8;var a=this.data[i/8|0]&255>>>r;var o=8-r;if(o>=t){if(n){this.index+=t}return a>>o-t}else{if(n){this.index+=o}var s=t-o;return a<<s|this.getBits(s,i+o,n)}}},{key:"skipLZ",value:function e(){var t=void 0;for(t=0;t<this.bitLength-this.index;++t){if(this.getBits(1,this.index+t,false)!==0){this.index+=t;return t}}return t}},{key:"skipUEG",value:function e(){this.skipBits(1+this.skipLZ())}},{key:"skipEG",value:function e(){this.skipBits(1+this.skipLZ())}},{key:"readUEG",value:function e(){var t=this.skipLZ();return this.readBits(t+1)-1}},{key:"readEG",value:function e(){var t=this.readUEG();if(1&t){return 1+t>>>1}else{return-1*(t>>>1)}}},{key:"readBoolean",value:function e(){return this.readBits(1)===1}},{key:"readUByte",value:function e(){var t=arguments.length>0&&arguments[0]!==undefined?arguments[0]:1;return this.readBits(t*8)}},{key:"readUShort",value:function e(){return this.readBits(16)}},{key:"readUInt",value:function e(){return this.readBits(32)}},{key:"bitsAvailable",get:function e(){return this.bitLength-this.index}}]);return e}()},function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:true});var n=function(){function e(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||false;n.configurable=true;if("value"in n)n.writable=true;Object.defineProperty(e,n.key,n)}}return function(t,i,n){if(i)e(t.prototype,i);if(n)e(t,n);return t}}();var r=i(0);var a=h(r);var o=i(27);var s=i(28);var l=i(29);var u=i(11);var f=i(2);var c=d(f);function d(e){return e&&e.__esModule?e:{default:e}}function h(e){if(e&&e.__esModule){return e}else{var t={};if(e!=null){for(var i in e){if(Object.prototype.hasOwnProperty.call(e,i))t[i]=e[i]}}t.default=e;return t}}function p(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}function y(e,t){if(!e){throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return t&&(typeof t==="object"||typeof t==="function")?t:e}function v(e,t){if(typeof t!=="function"&&t!==null){throw new TypeError("Super expression must either be null or a function, not "+typeof t)}e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:false,writable:true,configurable:true}});if(t)Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t}var m=function(e){v(t,e);function t(e){p(this,t);var i=y(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,"remuxer"));i.initialized=false;i.trackTypes=[];i.tracks={};i.mediaDuration=e?Infinity:1e3;return i}n(t,[{key:"addTrack",value:function e(t){if(t==="video"||t==="both"){this.tracks.video=new l.H264Remuxer;this.trackTypes.push("video")}if(t==="audio"||t==="both"){this.tracks.audio=new s.AACRemuxer;this.trackTypes.push("audio")}}},{key:"reset",value:function e(){var t=true;var i=false;var n=undefined;try{for(var r=this.trackTypes[Symbol.iterator](),a;!(t=(a=r.next()).done);t=true){var o=a.value;this.tracks[o].resetTrack()}}catch(e){i=true;n=e}finally{try{if(!t&&r.return){r.return()}}finally{if(i){throw n}}}this.initialized=false}},{key:"destroy",value:function e(){this.tracks={};this.offAll()}},{key:"flush",value:function e(){if(!this.initialized){if(this.isReady()){this.dispatch("ready");var t=true;var i=false;var n=undefined;try{for(var r=this.trackTypes[Symbol.iterator](),s;!(t=(s=r.next()).done);t=true){var l=s.value;var f=this.tracks[l];var c={type:l,payload:o.MP4.initSegment([f.mp4track],this.mediaDuration,f.mp4track.timescale)};this.dispatch("buffer",c)}}catch(e){i=true;n=e}finally{try{if(!t&&r.return){r.return()}}finally{if(i){throw n}}}a.log("Initial segment generated.");this.initialized=true}}else{var d=true;var h=false;var p=undefined;try{for(var y=this.trackTypes[Symbol.iterator](),v;!(d=(v=y.next()).done);d=true){var m=v.value;var g=this.tracks[m];var b=g.getPayload();if(b&&b.byteLength){var S=o.MP4.moof(g.seq,g.dts,g.mp4track);var w=o.MP4.mdat(b);var P=(0,u.appendByteArray)(S,w);var C={type:m,payload:P,dts:g.dts};this.dispatch("buffer",C);var _=(0,u.secToTime)(g.dts/1e3);a.log("put segment ("+m+"): "+g.seq+" dts: "+g.dts+" samples: "+g.mp4track.samples.length+" second: "+_);g.flush()}}}catch(e){h=true;p=e}finally{try{if(!d&&y.return){y.return()}}finally{if(h){throw p}}}}}},{key:"isReady",value:function e(){var t=true;var i=false;var n=undefined;try{for(var r=this.trackTypes[Symbol.iterator](),a;!(t=(a=r.next()).done);t=true){var o=a.value;if(!this.tracks[o].readyToDecode||!this.tracks[o].samples.length)return false}}catch(e){i=true;n=e}finally{try{if(!t&&r.return){r.return()}}finally{if(i){throw n}}}return true}},{key:"remux",value:function e(t){var i=true;var n=false;var r=undefined;try{for(var a=this.trackTypes[Symbol.iterator](),o;!(i=(o=a.next()).done);i=true){var s=o.value;var l=t[s];if(s==="audio"&&this.tracks.video&&!this.tracks.video.readyToDecode)continue;if(l.length>0){this.tracks[s].remux(l)}}}catch(e){n=true;r=e}finally{try{if(!i&&a.return){a.return()}}finally{if(n){throw r}}}this.flush()}}]);return t}(c.default);t.default=m},function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:true});var n=function(){function e(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||false;n.configurable=true;if("value"in n)n.writable=true;Object.defineProperty(e,n.key,n)}}return function(t,i,n){if(i)e(t.prototype,i);if(n)e(t,n);return t}}();function r(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var a=t.MP4=function(){function e(){r(this,e)}n(e,null,[{key:"init",value:function t(){e.types={avc1:[],avcC:[],btrt:[],dinf:[],dref:[],esds:[],ftyp:[],hdlr:[],mdat:[],mdhd:[],mdia:[],mfhd:[],minf:[],moof:[],moov:[],mp4a:[],mvex:[],mvhd:[],sdtp:[],stbl:[],stco:[],stsc:[],stsd:[],stsz:[],stts:[],tfdt:[],tfhd:[],traf:[],trak:[],trun:[],trex:[],tkhd:[],vmhd:[],smhd:[]};var i;for(i in e.types){if(e.types.hasOwnProperty(i)){e.types[i]=[i.charCodeAt(0),i.charCodeAt(1),i.charCodeAt(2),i.charCodeAt(3)]}}var n=new Uint8Array([0,0,0,0,0,0,0,0,118,105,100,101,0,0,0,0,0,0,0,0,0,0,0,0,86,105,100,101,111,72,97,110,100,108,101,114,0]);var r=new Uint8Array([0,0,0,0,0,0,0,0,115,111,117,110,0,0,0,0,0,0,0,0,0,0,0,0,83,111,117,110,100,72,97,110,100,108,101,114,0]);e.HDLR_TYPES={video:n,audio:r};var a=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,12,117,114,108,32,0,0,0,1]);var o=new Uint8Array([0,0,0,0,0,0,0,0]);e.STTS=e.STSC=e.STCO=o;e.STSZ=new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0]);e.VMHD=new Uint8Array([0,0,0,1,0,0,0,0,0,0,0,0]);e.SMHD=new Uint8Array([0,0,0,0,0,0,0,0]);e.STSD=new Uint8Array([0,0,0,0,0,0,0,1]);var s=new Uint8Array([105,115,111,109]);var l=new Uint8Array([97,118,99,49]);var u=new Uint8Array([0,0,0,1]);e.FTYP=e.box(e.types.ftyp,s,u,s,l);e.DINF=e.box(e.types.dinf,e.box(e.types.dref,a))}},{key:"box",value:function e(t){for(var i=arguments.length,n=Array(i>1?i-1:0),r=1;r<i;r++){n[r-1]=arguments[r]}var a=8,o=n.length,s=o,l;while(o--){a+=n[o].byteLength}l=new Uint8Array(a);l[0]=a>>24&255;l[1]=a>>16&255;l[2]=a>>8&255;l[3]=a&255;l.set(t,4);for(o=0,a=8;o<s;++o){l.set(n[o],a);a+=n[o].byteLength}return l}},{key:"hdlr",value:function t(i){return e.box(e.types.hdlr,e.HDLR_TYPES[i])}},{key:"mdat",value:function t(i){return e.box(e.types.mdat,i)}},{key:"mdhd",value:function t(i,n){return e.box(e.types.mdhd,new Uint8Array([0,0,0,0,0,0,0,2,0,0,0,3,i>>24&255,i>>16&255,i>>8&255,i&255,n>>24,n>>16&255,n>>8&255,n&255,85,196,0,0]))}},{key:"mdia",value:function t(i){return e.box(e.types.mdia,e.mdhd(i.timescale,i.duration),e.hdlr(i.type),e.minf(i))}},{key:"mfhd",value:function t(i){return e.box(e.types.mfhd,new Uint8Array([0,0,0,0,i>>24,i>>16&255,i>>8&255,i&255]))}},{key:"minf",value:function t(i){if(i.type==="audio"){return e.box(e.types.minf,e.box(e.types.smhd,e.SMHD),e.DINF,e.stbl(i))}else{return e.box(e.types.minf,e.box(e.types.vmhd,e.VMHD),e.DINF,e.stbl(i))}}},{key:"moof",value:function t(i,n,r){return e.box(e.types.moof,e.mfhd(i),e.traf(r,n))}},{key:"moov",value:function t(i,n,r){var a=i.length,o=[];while(a--){o[a]=e.trak(i[a])}return e.box.apply(null,[e.types.moov,e.mvhd(r,n)].concat(o).concat(e.mvex(i)))}},{key:"mvex",value:function t(i){var n=i.length,r=[];while(n--){r[n]=e.trex(i[n])}return e.box.apply(null,[e.types.mvex].concat(r))}},{key:"mvhd",value:function t(i,n){var r=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,2,i>>24&255,i>>16&255,i>>8&255,i&255,n>>24&255,n>>16&255,n>>8&255,n&255,0,1,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,255,255,255,255]);return e.box(e.types.mvhd,r)}},{key:"sdtp",value:function t(i){var n=i.samples||[],r=new Uint8Array(4+n.length),a,o;for(o=0;o<n.length;o++){a=n[o].flags;r[o+4]=a.dependsOn<<4|a.isDependedOn<<2|a.hasRedundancy}return e.box(e.types.sdtp,r)}},{key:"stbl",value:function t(i){return e.box(e.types.stbl,e.stsd(i),e.box(e.types.stts,e.STTS),e.box(e.types.stsc,e.STSC),e.box(e.types.stsz,e.STSZ),e.box(e.types.stco,e.STCO))}},{key:"avc1",value:function t(i){var n=[],r=[],a,o,s;for(a=0;a<i.sps.length;a++){o=i.sps[a];s=o.byteLength;n.push(s>>>8&255);n.push(s&255);n=n.concat(Array.prototype.slice.call(o))}for(a=0;a<i.pps.length;a++){o=i.pps[a];s=o.byteLength;r.push(s>>>8&255);r.push(s&255);r=r.concat(Array.prototype.slice.call(o))}var l=e.box(e.types.avcC,new Uint8Array([1,n[3],n[4],n[5],252|3,224|i.sps.length].concat(n).concat([i.pps.length]).concat(r))),u=i.width,f=i.height;return e.box(e.types.avc1,new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,u>>8&255,u&255,f>>8&255,f&255,0,72,0,0,0,72,0,0,0,0,0,0,0,1,18,98,105,110,101,108,112,114,111,46,114,117,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,24,17,17]),l,e.box(e.types.btrt,new Uint8Array([0,28,156,128,0,45,198,192,0,45,198,192])))}},{key:"esds",value:function e(t){var i=t.config.byteLength;var n=new Uint8Array(26+i+3);n.set([0,0,0,0,3,23+i,0,1,0,4,15+i,64,21,0,0,0,0,0,0,0,0,0,0,0,5,i]);n.set(t.config,26);n.set([6,1,2],26+i);return n}},{key:"mp4a",value:function t(i){var n=i.audiosamplerate;return e.box(e.types.mp4a,new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,i.channelCount,0,16,0,0,0,0,n>>8&255,n&255,0,0]),e.box(e.types.esds,e.esds(i)))}},{key:"stsd",value:function t(i){if(i.type==="audio"){return e.box(e.types.stsd,e.STSD,e.mp4a(i))}else{return e.box(e.types.stsd,e.STSD,e.avc1(i))}}},{key:"tkhd",value:function t(i){var n=i.id,r=i.duration,a=i.width,o=i.height,s=i.volume;return e.box(e.types.tkhd,new Uint8Array([0,0,0,7,0,0,0,0,0,0,0,0,n>>24&255,n>>16&255,n>>8&255,n&255,0,0,0,0,r>>24,r>>16&255,r>>8&255,r&255,0,0,0,0,0,0,0,0,0,0,0,0,s>>0&255,s%1*10>>0&255,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,a>>8&255,a&255,0,0,o>>8&255,o&255,0,0]))}},{key:"traf",value:function t(i,n){var r=e.sdtp(i),a=i.id;return e.box(e.types.traf,e.box(e.types.tfhd,new Uint8Array([0,0,0,0,a>>24,a>>16&255,a>>8&255,a&255])),e.box(e.types.tfdt,new Uint8Array([0,0,0,0,n>>24,n>>16&255,n>>8&255,n&255])),e.trun(i,r.length+16+16+8+16+8+8),r)}},{key:"trak",value:function t(i){i.duration=i.duration||4294967295;return e.box(e.types.trak,e.tkhd(i),e.mdia(i))}},{key:"trex",value:function t(i){var n=i.id;return e.box(e.types.trex,new Uint8Array([0,0,0,0,n>>24,n>>16&255,n>>8&255,n&255,0,0,0,1,0,0,0,0,0,0,0,0,0,1,0,1]))}},{key:"trun",value:function t(i,n){var r=i.samples||[],a=r.length,o=12+16*a,s=new Uint8Array(o),l,u,f,c,d,h;n+=8+o;s.set([0,0,15,1,a>>>24&255,a>>>16&255,a>>>8&255,a&255,n>>>24&255,n>>>16&255,n>>>8&255,n&255],0);for(l=0;l<a;l++){u=r[l];f=u.duration;c=u.size;d=u.flags;h=u.cts;s.set([f>>>24&255,f>>>16&255,f>>>8&255,f&255,c>>>24&255,c>>>16&255,c>>>8&255,c&255,d.isLeading<<2|d.dependsOn,d.isDependedOn<<6|d.hasRedundancy<<4|d.paddingValue<<1|d.isNonSync,d.degradPrio&240<<8,d.degradPrio&15,h>>>24&255,h>>>16&255,h>>>8&255,h&255],12+16*l)}return e.box(e.types.trun,s)}},{key:"initSegment",value:function t(i,n,r){if(!e.types){e.init()}var a=e.moov(i,n,r),o;o=new Uint8Array(e.FTYP.byteLength+a.byteLength);o.set(e.FTYP);o.set(a,e.FTYP.byteLength);return o}}]);return e}()},function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.AACRemuxer=undefined;var n=function(){function e(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||false;n.configurable=true;if("value"in n)n.writable=true;Object.defineProperty(e,n.key,n)}}return function(t,i,n){if(i)e(t.prototype,i);if(n)e(t,n);return t}}();var r=i(0);var a=l(r);var o=i(9);var s=i(10);function l(e){if(e&&e.__esModule){return e}else{var t={};if(e!=null){for(var i in e){if(Object.prototype.hasOwnProperty.call(e,i))t[i]=e[i]}}t.default=e;return t}}function u(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}function f(e,t){if(!e){throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return t&&(typeof t==="object"||typeof t==="function")?t:e}function c(e,t){if(typeof t!=="function"&&t!==null){throw new TypeError("Super expression must either be null or a function, not "+typeof t)}e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:false,writable:true,configurable:true}});if(t)Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t}var d=t.AACRemuxer=function(e){c(t,e);function t(){u(this,t);var e=f(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));e.readyToDecode=false;e.nextDts=0;e.dts=0;e.timescale=1e3;e.mp4track={id:s.BaseRemuxer.getTrackID(),type:"audio",channelCount:0,len:0,fragmented:true,timescale:e.timescale,duration:e.timescale,samples:[],config:"",codec:""};e.samples=[];e.aac=new o.AACParser(e);return e}n(t,[{key:"resetTrack",value:function e(){this.readyToDecode=false;this.mp4track.codec="";this.mp4track.channelCount="";this.mp4track.config="";this.mp4track.timescale=this.timescale}},{key:"remux",value:function e(t){var i=void 0,n=void 0,r=void 0,a=void 0;var o=true;var s=false;var l=undefined;try{for(var u=t[Symbol.iterator](),f;!(o=(f=u.next()).done);o=true){var c=f.value;a=c.units;r=a.byteLength;this.samples.push({units:a,size:r,duration:c.duration});this.mp4track.len+=r;if(!this.readyToDecode){this.aac.setAACConfig()}}}catch(e){s=true;l=e}finally{try{if(!o&&u.return){u.return()}}finally{if(s){throw l}}}}},{key:"getPayload",value:function e(){if(!this.isReady()){return null}var t=new Uint8Array(this.mp4track.len);var i=0;var n=this.mp4track.samples;var r=void 0,o=void 0;this.dts=this.nextDts;while(this.samples.length){var s=this.samples.shift(),l=s.units;o=s.duration;if(o<=0){a.log("remuxer: invalid sample duration at DTS: "+this.nextDts+" :"+o);this.mp4track.len-=s.size;continue}this.nextDts+=o;r={size:s.size,duration:o,cts:0,flags:{isLeading:0,isDependedOn:0,hasRedundancy:0,degradPrio:0,dependsOn:1}};t.set(s.units,i);i+=s.size;n.push(r)}if(!n.length)return null;return new Uint8Array(t.buffer,0,this.mp4track.len)}}]);return t}(s.BaseRemuxer)},function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.H264Remuxer=undefined;var n=function(){function e(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||false;n.configurable=true;if("value"in n)n.writable=true;Object.defineProperty(e,n.key,n)}}return function(t,i,n){if(i)e(t.prototype,i);if(n)e(t,n);return t}}();var r=i(0);var a=l(r);var o=i(8);var s=i(10);function l(e){if(e&&e.__esModule){return e}else{var t={};if(e!=null){for(var i in e){if(Object.prototype.hasOwnProperty.call(e,i))t[i]=e[i]}}t.default=e;return t}}function u(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}function f(e,t){if(!e){throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return t&&(typeof t==="object"||typeof t==="function")?t:e}function c(e,t){if(typeof t!=="function"&&t!==null){throw new TypeError("Super expression must either be null or a function, not "+typeof t)}e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:false,writable:true,configurable:true}});if(t)Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t}var d=t.H264Remuxer=function(e){c(t,e);function t(){u(this,t);var e=f(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));e.readyToDecode=false;e.nextDts=0;e.dts=0;e.timescale=1e3;e.mp4track={id:s.BaseRemuxer.getTrackID(),type:"video",len:0,fragmented:true,sps:"",pps:"",width:0,height:0,timescale:e.timescale,duration:e.timescale,samples:[]};e.samples=[];e.h264=new o.H264Parser(e);return e}n(t,[{key:"resetTrack",value:function e(){this.readyToDecode=false;this.mp4track.sps="";this.mp4track.pps=""}},{key:"remux",value:function e(t){var i=void 0,n=void 0,r=void 0,a=void 0,o=void 0;var s=true;var l=false;var u=undefined;try{for(var f=t[Symbol.iterator](),c;!(s=(c=f.next()).done);s=true){i=c.value;n=[];a=0;o=false;var d=true;var h=false;var p=undefined;try{for(var y=i.units[Symbol.iterator](),v;!(d=(v=y.next()).done);d=true){r=v.value;if(this.h264.parseNAL(r)){n.push(r);a+=r.getSize();if(!o){o=r.isKeyframe()}}}}catch(e){h=true;p=e}finally{try{if(!d&&y.return){y.return()}}finally{if(h){throw p}}}if(n.length>0&&this.readyToDecode){this.mp4track.len+=a;this.samples.push({units:n,size:a,keyFrame:o,duration:i.duration})}}}catch(e){l=true;u=e}finally{try{if(!s&&f.return){f.return()}}finally{if(l){throw u}}}}},{key:"getPayload",value:function e(){if(!this.isReady()){return null}var t=new Uint8Array(this.mp4track.len);var i=0;var n=this.mp4track.samples;var r=void 0,o=void 0;this.dts=this.nextDts;while(this.samples.length){var s=this.samples.shift(),l=s.units;o=s.duration;if(o<=0){a.log("remuxer: invalid sample duration at DTS: "+this.nextDts+" :"+o);this.mp4track.len-=s.size;continue}this.nextDts+=o;r={size:s.size,duration:o,cts:0,flags:{isLeading:0,isDependedOn:0,hasRedundancy:0,degradPrio:0,isNonSync:s.keyFrame?0:1,dependsOn:s.keyFrame?2:1}};var u=true;var f=false;var c=undefined;try{for(var d=l[Symbol.iterator](),h;!(u=(h=d.next()).done);u=true){var p=h.value;t.set(p.getData(),i);i+=p.getSize()}}catch(e){f=true;c=e}finally{try{if(!u&&d.return){d.return()}}finally{if(f){throw c}}}n.push(r)}if(!n.length)return null;return new Uint8Array(t.buffer,0,this.mp4track.len)}}]);return t}(s.BaseRemuxer)},function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:true});var n=function(){function e(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||false;n.configurable=true;if("value"in n)n.writable=true;Object.defineProperty(e,n.key,n)}}return function(t,i,n){if(i)e(t.prototype,i);if(n)e(t,n);return t}}();var r=i(0);var a=f(r);var o=i(2);var s=u(o);var l=i(11);function u(e){return e&&e.__esModule?e:{default:e}}function f(e){if(e&&e.__esModule){return e}else{var t={};if(e!=null){for(var i in e){if(Object.prototype.hasOwnProperty.call(e,i))t[i]=e[i]}}t.default=e;return t}}function c(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}function d(e,t){if(!e){throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return t&&(typeof t==="object"||typeof t==="function")?t:e}function h(e,t){if(typeof t!=="function"&&t!==null){throw new TypeError("Super expression must either be null or a function, not "+typeof t)}e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:false,writable:true,configurable:true}});if(t)Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t}var p=function(e){h(t,e);function t(e,i){c(this,t);var n=d(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,"buffer"));n.type=i;n.queue=new Uint8Array;n.cleaning=false;n.pendingCleaning=0;n.cleanOffset=30;n.cleanRanges=[];n.sourceBuffer=e;n.sourceBuffer.addEventListener("updateend",function(){if(n.pendingCleaning>0){n.initCleanup(n.pendingCleaning);n.pendingCleaning=0}n.cleaning=false;if(n.cleanRanges.length){n.doCleanup();return}});n.sourceBuffer.addEventListener("error",function(){n.dispatch("error",{type:n.type,name:"buffer",error:"buffer error"})});return n}n(t,[{key:"destroy",value:function e(){this.queue=null;this.sourceBuffer=null;this.offAll()}},{key:"doCleanup",value:function e(){if(!this.cleanRanges.length){this.cleaning=false;return}var t=this.cleanRanges.shift();a.log(this.type+" remove range ["+t[0]+" - "+t[1]+")");this.cleaning=true;this.sourceBuffer.remove(t[0],t[1])}},{key:"initCleanup",value:function e(t){if(this.sourceBuffer.updating){this.pendingCleaning=t;return}if(this.sourceBuffer.buffered&&this.sourceBuffer.buffered.length&&!this.cleaning){for(var i=0;i<this.sourceBuffer.buffered.length;++i){var n=this.sourceBuffer.buffered.start(i);var r=this.sourceBuffer.buffered.end(i);if(t-n>this.cleanOffset){r=t-this.cleanOffset;if(n<r){this.cleanRanges.push([n,r])}}}this.doCleanup()}}},{key:"doAppend",value:function e(){if(!this.queue.length)return;if(this.sourceBuffer.updating){return}try{this.sourceBuffer.appendBuffer(this.queue);this.queue=new Uint8Array}catch(e){if(e.name==="QuotaExceededError"){a.log(this.type+" buffer quota full");this.dispatch("error",{type:this.type,name:"QuotaExceeded",error:"buffer error"});return}a.error("Error occured while appending "+this.type+" buffer -  "+e.name+": "+e.message);this.dispatch("error",{type:this.type,name:"unexpectedError",error:"buffer error"})}}},{key:"feed",value:function e(t){this.queue=(0,l.appendByteArray)(this.queue,t)}}]);return t}(s.default);t.default=p},function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.StorageManager=undefined;var n=function(){function e(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||false;n.configurable=true;if("value"in n)n.writable=true;Object.defineProperty(e,n.key,n)}}return function(t,i,n){if(i)e(t.prototype,i);if(n)e(t,n);return t}}();var r=i(4);var a=u(r);var o=i(3);var s=i(1);var l=u(s);function u(e){return e&&e.__esModule?e:{default:e}}function f(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var c="Web/RecordFiles/";var d="Web/PlaybackFiles/";var h=1e3;var p=1;var y=3001;window.requestFileSystem=window.requestFileSystem||window.webkitRequestFileSystem;window.URL=window.URL||window.webkitURL;var v=function(){function e(t,i,n,r){f(this,e);this.szUUID=t;this.szFileName=i;this.iStreamType=n;this.szPath="";this.bStart=false;this.aStreamList=[];this.options=r}n(e,[{key:"init",value:function e(){var t=this;if(0===this.iStreamType){this.szPath=c}else if(1===this.iStreamType){this.szPath=d}this.szPath+=this.getDateDir();var i=this.szPath.split("/");var n=new Promise(function(e){window.requestFileSystem(window.TEMPORARY,t.options.iFileSize,function(n){t.createDir(n.root,i,function(){e()})},t.errorHandler)});return n}},{key:"getDateDir",value:function e(){return o.oTool.dateFormat(new Date,"yyyy-MM-dd")}},{key:"createDir",value:function e(t,i,n){var r=this;if(i.length){t.getDirectory(i[0],{create:true},function(e){r.createDir(e,i.slice(1),n)},this.errorHandler)}else{n()}}},{key:"errorHandler",value:function e(){}},{key:"writeFileHeader",value:function e(t){var i=this;window.requestFileSystem(window.TEMPORARY,this.options.iFileSize,function(e){e.root.getFile(i.szPath+"/"+i.szFileName,{create:true},function(e){e.createWriter(function(e){e.onwriteend=function(){i.bStart=true;i.writeFile(e)};e.onerror=function(){};e.seek(e.length);var n=new Blob([t]);e.write(n)},i.errorHandler)},i.errorHandler)},this.errorHandler)}},{key:"writeFileContent",value:function e(t){this.aStreamList.push(t)}},{key:"writeFile",value:function e(t){var i=this;if(this.bStart){if(this.aStreamList.length>0){var n=this.aStreamList.shift();t.seek(t.length);if(t.length>=this.options.iFileSize){if(this.options.cbEventHandler){this.options.cbEventHandler(y,this.szUUID)}return}var r=new Blob([n]);t.write(r)}else{setTimeout(function(){i.writeFile(t)},h)}}}},{key:"stopWriteFile",value:function e(){var t=this;this.bStart=false;this.aStreamList.length=0;var i=new Promise(function(e){window.requestFileSystem(window.TEMPORARY,t.options.iFileSize,function(i){i.root.getFile(t.szPath+"/"+t.szFileName,{create:false},function(t){t.file(function(t){e();o.oTool.downloadFile(t,t.name)})},t.errorHandler)},t.errorHandler)});return i}}]);return e}();var m=function(){function e(t,i,n,r,a,o,s){f(this,e);this.szBasePath=t;this.szUUID=i;this.szFileName=n;this.aHeadBuf=new Uint8Array(r);this.iPackType=a;this.iStreamType=o;this.oWorker=null;this.oFileSystem=null;this.options=s;this.bHead=true}n(e,[{key:"init",value:function e(){var t=this;var i=new Promise(function(e,i){t.initFileSystem().then(function(){t.initWorker().then(function(){e(t.szUUID)},function(e){i(e)})},function(e){i(e)})});return i}},{key:"initFileSystem",value:function e(){var t=this;this.oFileSystem=new v(this.szUUID,this.szFileName,this.iStreamType,this.options);var i=new Promise(function(e,i){t.oFileSystem.init().then(function(){e()},function(e){i(e)})});return i}},{key:"initWorker",value:function e(){var t=this;var i=new Promise(function(e){t.oWorker=new Worker(t.szBasePath+"/systemTransform-worker.min.js");t.oWorker.onmessage=function(i){var n=i.data;var r=t.iPackType;if(t.options.iPackage===1){r=12}if("loaded"===n.type){t.oWorker.postMessage({type:"create",buf:t.aHeadBuf.buffer,len:40,packType:r},[t.aHeadBuf.buffer])}else if("created"===n.type){e()}else if("outputData"===n.type){var a=new Uint8Array(n.buf);if(t.options.iPackage===1){if(t.bHead){t.oFileSystem.writeFileHeader(a);t.bHead=false}else{t.oFileSystem.writeFileContent(a)}}else{if(p===n.dType){t.oFileSystem.writeFileHeader(a)}else{t.oFileSystem.writeFileContent(a)}}}}});return i}},{key:"inputData",value:function e(t){if(this.oWorker){var i=new Uint8Array(t);this.oWorker.postMessage({type:"inputData",buf:i.buffer,len:i.length},[i.buffer])}}},{key:"stopRecord",value:function e(){var t=this;var i=new Promise(function(e,i){if(t.oWorker){t.oWorker.postMessage({type:"release"})}else{i()}if(t.oFileSystem){t.oFileSystem.stopWriteFile().then(function(){t.bHead=true;e()},function(){i()})}else{i()}});return i}}]);return e}();var g=function(){var e=function(){function e(t,i){f(this,e);this.szBasePath=t;this.oStorageList={};this.options={iFileSize:1024*1024*1024};l.default.extend(this.options,i)}n(e,[{key:"startRecord",value:function e(t,i,n,r,o){var s=this;var u=a.default.v4();var f=l.default.extend({},this.options,o);var c=new m(this.szBasePath,u,t,i,n,r,f);var d=new Promise(function(e,t){c.init().then(function(t){s.oStorageList[t]=c;e(t)},function(e){t(e)})});return d}},{key:"inputData",value:function e(t,i){var n=this.oStorageList[t];if(n){n.inputData(i)}}},{key:"stopRecord",value:function e(t){var i=this;var n=new Promise(function(e,n){var r=i.oStorageList[t];if(r){r.stopRecord().then(function(){delete i.oStorageList[t];e()},function(){n()})}else{n()}});return n}}]);return e}();return e}();t.StorageManager=g},function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.ESCanvas=undefined;var n=function(){function e(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||false;n.configurable=true;if("value"in n)n.writable=true;Object.defineProperty(e,n.key,n)}}return function(t,i,n){if(i)e(t.prototype,i);if(n)e(t,n);return t}}();var r=i(1);var a=o(r);function o(e){return e&&e.__esModule?e:{default:e}}function s(e,t){if(!e){throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return t&&(typeof t==="object"||typeof t==="function")?t:e}function l(e,t){if(typeof t!=="function"&&t!==null){throw new TypeError("Super expression must either be null or a function, not "+typeof t)}e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:false,writable:true,configurable:true}});if(t)Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t}function u(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var f=function(){var e=null;function t(){e.oContext.clearRect(0,0,e.m_iCanvasWidth,e.m_iCanvasHeight);for(var t=0,i=e.aShapes.length;t<i;t++){e.aShapes[t].draw()}}function i(t){var i=e.aShapes.length;if(i<e.iMaxShapeNumSupport){e.aShapes.push(t)}}function r(){var n=false;var r=0;var a=0;var o="draw";var s=null;function l(){var t=-1;for(var i=0,n=e.aShapes.length;i<n;i++){if(e.aShapes[i].m_bChoosed){t=i;break}}return t}e.oCanvas[0].oncontextmenu=function(){return false};e.oCanvas[0].onselectstart=function(){return false};e.oCanvas.unbind();e.oCanvas.bind("mousedown",function(u){if(u.button===2){if(e.bPolygonDrawing&&s){if(s.m_aPoint.length>=s.m_iMinClosed){s.m_bClosed=true;e.bPolygonDrawing=false;s.setPointInfo(s.m_aPoint);i(s);t();n=false;if(!e.bDrawShapeMultiOneTime){e.bDrawStatus=false}window.onDrawShapeEvent&&window.onDrawShapeEvent(s.m_szType,"onDrawEnd",s.m_szId)}}}else if(u.button===0){r=u.offsetX;a=u.offsetY;o="draw";if(!e.bPolygonDrawing){var c=l();if(c!==-1){if(e.aShapes[c].inArc(u.offsetX,u.offsetY,5)){o="stretch"}}if(o!=="stretch"){for(var h=0,v=e.aShapes.length;h<v;h++){if(e.aShapes[h].inShape(u.offsetX,u.offsetY)){e.aShapes[h].m_bChoosed=true;e.aShapes[h].getMouseDownPoints(u.offsetX,u.offsetY);o="drag";window.onDrawShapeEvent&&window.onDrawShapeEvent(e.aShapes[h].m_szType,"onChoose",e.aShapes[h].m_szId)}else{e.aShapes[h].m_bChoosed=false}}}if(o==="drag"){e.oCanvas[0].style.cursor="move"}else{e.oCanvas[0].style.cursor="default"}if("draw"===o&&1===e.aShapes.length&&1===e.aShapes[0].m_iRedrawMode){e.deleteRepeatPolyonById(e.aShapes[0].m_szId);e.bDrawStatus=true}if(e.bDrawStatus&&!e.bDrawShapeMultiOneTime){o="draw"}}if(o==="draw"){if(e.bDrawStatus){if(e.iMaxShapeNumSupport<=e.aShapes.length&&e.szShapeType!=="Grid"&&e.szShapeType!=="Point"){return}if(e.szShapeType==="Rect"){s=new f}else if(e.szShapeType==="Grid"){if(e.aShapes.length===0){s=new d;i(s)}}else if(e.szShapeType==="Polygon"){if(!e.bPolygonDrawing){e.bPolygonDrawing=true;s=new p;s.m_szId=e.oCurrentShapeInfo.szId||"";s.m_szTips=e.oCurrentShapeInfo.szTips||"";s.m_iMinClosed=e.oCurrentShapeInfo.iMinClosed||3;s.m_iMaxPointNum=e.oCurrentShapeInfo.iMaxPointNum||11;s.m_iPolygonType=e.oCurrentShapeInfo.iPolygonType;s.m_szDrawColor=e.oCurrentShapeInfo.szDrawColor;s.m_szFillColor=e.oCurrentShapeInfo.szFillColor;s.m_iTranslucent=e.oCurrentShapeInfo.iTranslucent;s.m_iRedrawMode=e.oCurrentShapeInfo.iRedrawMode}if(s.m_iPolygonType===1){s.addPoint(r,a);if(s.m_aPoint.length===s.m_iMaxPointNum){s.m_bClosed=true;e.bPolygonDrawing=false;i(s);t();n=false;if(!e.bDrawShapeMultiOneTime){e.bDrawStatus=false}window.onDrawShapeEvent&&window.onDrawShapeEvent(s.m_szType,"onDrawEnd",s.m_szId)}}}else if(e.szShapeType==="Point"){e.clearShapeByType("Point");s=new y;s.m_szId=e.oCurrentShapeInfo.szId||"";s.m_szDrawColor=e.oCurrentShapeInfo.szDrawColor;s.setPointInfo([[r,a]]);i(s);t()}}}n=true}});e.oCanvas.bind("mousemove",function(i){if(!e.bPolygonDrawing){var u=l();if(u>-1){if(n){if(o==="drag"){e.aShapes[u].drag(i.offsetX,i.offsetY);window.onDrawShapeEvent&&window.onDrawShapeEvent(e.aShapes[u].m_szType,"onDrag",e.aShapes[u].m_szId)}else if(o==="stretch"){e.aShapes[u].stretch(i.offsetX,i.offsetY);window.onDrawShapeEvent&&window.onDrawShapeEvent(e.aShapes[u].m_szType,"onStretch",e.aShapes[u].m_szId)}}}else{if(e.bDrawStatus){if(n){if(e.szShapeType==="Rect"){s.move([[r,a],[i.offsetX,i.offsetY]])}else if(e.szShapeType==="Grid"){e.aShapes[0].move(r,a,i.offsetX,i.offsetY)}}}}}else{if(e.bDrawStatus){if(n){if(e.szShapeType==="Polygon"&&s.m_iPolygonType===0){s.m_bClosed=true}t();s.move(i.offsetX,i.offsetY,r,a)}}}});e.oCanvas.bind("mouseup",function(l){e.oCanvas[0].style.cursor="default";if(s!==null&&typeof s!=="undefined"&&o==="draw"){if(e.szShapeType==="Rect"){if(Math.abs(l.offsetX-r)>2&&Math.abs(l.offsetY-a)>2){i(s);if(!e.bDrawShapeMultiOneTime){e.bDrawStatus=false}}if(e.oEventCallback){var u={startPos:[],endPos:[]};if(l.offsetX>r&&l.offsetY>a){u.startPos=s.m_aPoint[0]||[l.offsetX,l.offsetY];u.endPos=s.m_aPoint[2]||[l.offsetX,l.offsetY]}else{u.startPos=s.m_aPoint[2]||[l.offsetX,l.offsetY];u.endPos=s.m_aPoint[0]||[l.offsetX,l.offsetY]}e.oEventCallback&&e.oEventCallback(u);e.clearAllShape()}s=null}else if(e.szShapeType==="Polygon"&&s.m_iPolygonType===0&&e.bPolygonDrawing){if(Math.abs(l.offsetX-r)>2&&Math.abs(l.offsetY-a)>2){i(s);e.bPolygonDrawing=false;if(!e.bDrawShapeMultiOneTime){e.bDrawStatus=false}window.onDrawShapeEvent&&window.onDrawShapeEvent(s.m_szType,"onDrawEnd",s.m_szId)}}}if(!e.bPolygonDrawing){n=false}else{n=true}if(!e.bPolygonDrawing){t()}});e.oCanvas.bind("dblclick",function(){if(e.bDrawStatus){if(e.szShapeType==="Grid"){e.aShapes[0].m_szGridMap="fffffcfffffcfffffcfffffcfffffcfffffcfffffcfffffcfffffcfffffcfffffcfffffcfffffcfffffcfffffcfffffcfffffcfffffc";t()}}});e.oCanvas.bind("mouseout",function(){e.oCanvas[0].style.cursor="default";if(!e.bPolygonDrawing){n=false}else{n=true}})}var o=function(){function i(){u(this,i);this.m_szId="";this.m_aPoint=[];this.m_bChoosed=false;this.m_szDrawColor=e.oShapeStyle.szDrawColor;this.m_szFillColor=e.oShapeStyle.szFillColor;this.m_iTranslucent=e.oShapeStyle.iTranslucent;this.m_iIndexChoosePoint=-1;this.m_iDriftStartX=0;this.m_iDriftStartY=0;this.m_oEdgePoints={top:{x:0,y:0},left:{x:0,y:0},right:{x:0,y:0},bottom:{x:0,y:0}};this.m_szTips="";this.m_iEditType=0;this.m_iMinClosed=3;this.m_iMaxPointNum=11;this.m_bClosed=false;this.m_iRedrawMode=0}n(i,[{key:"draw",value:function e(){}},{key:"drag",value:function i(n,r){var a=this.m_aPoint.length;var o=0;for(o=0;o<a;o++){if(this.m_aPoint[o][0]+n-this.m_iDriftStartX>e.m_iCanvasWidth||this.m_aPoint[o][1]+r-this.m_iDriftStartY>e.m_iCanvasHeight||this.m_aPoint[o][0]+n-this.m_iDriftStartX<0||this.m_aPoint[o][1]+r-this.m_iDriftStartY<0){this.m_iDriftStartX=n;this.m_iDriftStartY=r;return}}for(o=0;o<a;o++){this.m_aPoint[o][0]=this.m_aPoint[o][0]+n-this.m_iDriftStartX;this.m_aPoint[o][1]=this.m_aPoint[o][1]+r-this.m_iDriftStartY}this.m_iDriftStartX=n;this.m_iDriftStartY=r;this.setPointInfo(this.m_aPoint);t()}},{key:"stretch",value:function e(i,n){if(this.m_iEditType===0){if(this.m_iIndexChoosePoint!==-1){this.m_aPoint[this.m_iIndexChoosePoint][0]=i;this.m_aPoint[this.m_iIndexChoosePoint][1]=n}this.setPointInfo(this.m_aPoint);t()}}},{key:"inShape",value:function e(t,i){var n=false;var r=this.m_aPoint.length;for(var a=0,o=r-1;a<r;o=a++){if(this.m_aPoint[a][1]>i!==this.m_aPoint[o][1]>i&&t<(this.m_aPoint[o][0]-this.m_aPoint[a][0])*(i-this.m_aPoint[a][1])/(this.m_aPoint[o][1]-this.m_aPoint[a][1])+this.m_aPoint[a][0]){n=!n}}return n}},{key:"inArc",value:function e(t,i,n){var r=false;for(var a=0,o=this.m_aPoint.length;a<o;a++){var s=Math.sqrt((t-this.m_aPoint[a][0])*(t-this.m_aPoint[a][0])+(i-this.m_aPoint[a][1])*(i-this.m_aPoint[a][1]));if(s<n){r=true;this.m_iIndexChoosePoint=a;break}}return r}},{key:"getMouseDownPoints",value:function e(t,i){this.m_iDriftStartX=t;this.m_iDriftStartY=i}},{key:"getPointInfo",value:function e(){return this.m_aPoint}},{key:"setPointInfo",value:function e(t){if(t!==null&&typeof t!=="undefined"&&t.length>0){this.m_aPoint=t;this.setEdgePoints(t)}}},{key:"addPoint",value:function e(t,i){if(this.m_aPoint.length<this.m_iMaxPointNum){this.m_aPoint.push([t,i])}if(this.m_aPoint.length===this.m_iMaxPointNum){this.setPointInfo(this.m_aPoint)}}},{key:"setEdgePoints",value:function e(t){for(var i=0,n=t.length;i<n;i++){if(i===0){this.m_oEdgePoints.top.x=t[i][0];this.m_oEdgePoints.top.y=t[i][1];this.m_oEdgePoints.left.x=t[i][0];this.m_oEdgePoints.left.y=t[i][1];this.m_oEdgePoints.right.x=t[i][0];this.m_oEdgePoints.right.y=t[i][1];this.m_oEdgePoints.bottom.x=t[i][0];this.m_oEdgePoints.bottom.y=t[i][1]}else{if(t[i][1]<this.m_oEdgePoints.top.y){this.m_oEdgePoints.top.x=t[i][0];this.m_oEdgePoints.top.y=t[i][1]}if(t[i][0]>this.m_oEdgePoints.right.x){this.m_oEdgePoints.right.x=t[i][0];this.m_oEdgePoints.right.y=t[i][1]}if(t[i][1]>this.m_oEdgePoints.bottom.y){this.m_oEdgePoints.bottom.x=t[i][0];this.m_oEdgePoints.bottom.y=t[i][1]}if(t[i][0]<this.m_oEdgePoints.left.x){this.m_oEdgePoints.left.x=t[i][0];this.m_oEdgePoints.left.y=t[i][1]}}}}}]);return i}();var f=function(i){l(r,i);function r(){u(this,r);var e=s(this,(r.__proto__||Object.getPrototypeOf(r)).call(this));e.m_szType="Rect";return e}n(r,[{key:"setPointInfo",value:function e(t){if(t!==null&&typeof t!=="undefined"){var i=t[0][0];var n=t[0][1];var r=t[0][0];var a=t[0][1];for(var o=0,s=t.length;o<s;o++){if(i>t[o][0]){i=t[o][0]}if(n>t[o][1]){n=t[o][1]}if(r<t[o][0]){r=t[o][0]}if(a<t[o][1]){a=t[o][1]}}this.m_aPoint=[[i,n],[r,n],[r,a],[i,a]]}}},{key:"draw",value:function t(){e.oContext.fillStyle=this.m_szFillColor;e.oContext.strokeStyle=this.m_szDrawColor;var i=this.m_aPoint[0][0];var n=this.m_aPoint[0][1];var r=this.m_aPoint[2][0]-i;var a=this.m_aPoint[2][1]-n;e.oContext.globalAlpha=this.m_iTranslucent;e.oContext.fillRect(i,n,r,a);e.oContext.globalAlpha=1;e.oContext.fillText(this.m_szTips,(i+this.m_aPoint[2][0])/2,(n+this.m_aPoint[2][1])/2);if(this.m_bChoosed){var o=Math.round(r/2);var s=Math.round(a/2);if(this.m_iEditType===0){var l=[i,i+o,i+r,i,i+r,i,i+o,i+r];var u=[n,n,n,n+s,n+s,n+a,n+a,n+a];for(var f=0;f<8;f++){e.oContext.beginPath();e.oContext.arc(l[f],u[f],3,0,360,false);e.oContext.fillStyle=this.m_szDrawColor;e.oContext.closePath();e.oContext.fill()}}}e.oContext.strokeRect(i,n,r,a)}},{key:"stretch",value:function e(i,n){if(this.m_iEditType===0){if(this.m_iIndexChoosePoint===0){if(i<this.m_aPoint[2][0]&&n<this.m_aPoint[2][1]){this.m_aPoint[0][0]=i;this.m_aPoint[0][1]=n;this.m_aPoint[3][0]=i;this.m_aPoint[1][1]=n}}else if(this.m_iIndexChoosePoint===1){if(n<this.m_aPoint[2][1]){this.m_aPoint[0][1]=n;this.m_aPoint[1][1]=n}}else if(this.m_iIndexChoosePoint===2){if(i>this.m_aPoint[3][0]&&n<this.m_aPoint[3][1]){this.m_aPoint[1][0]=i;this.m_aPoint[1][1]=n;this.m_aPoint[2][0]=i;this.m_aPoint[0][1]=n}}else if(this.m_iIndexChoosePoint===3){if(i<this.m_aPoint[2][0]){this.m_aPoint[0][0]=i;this.m_aPoint[3][0]=i}}else if(this.m_iIndexChoosePoint===4){if(i>this.m_aPoint[0][0]){this.m_aPoint[1][0]=i;this.m_aPoint[2][0]=i}}else if(this.m_iIndexChoosePoint===5){if(i<this.m_aPoint[1][0]&&n>this.m_aPoint[1][1]){this.m_aPoint[3][0]=i;this.m_aPoint[3][1]=n;this.m_aPoint[0][0]=i;this.m_aPoint[2][1]=n}}else if(this.m_iIndexChoosePoint===6){if(n>this.m_aPoint[1][1]){this.m_aPoint[2][1]=n;this.m_aPoint[3][1]=n}}else if(this.m_iIndexChoosePoint===7){if(i>this.m_aPoint[0][0]&&n>this.m_aPoint[0][1]){this.m_aPoint[2][0]=i;this.m_aPoint[2][1]=n;this.m_aPoint[1][0]=i;this.m_aPoint[3][1]=n}}t()}}},{key:"move",value:function e(i){t();this.m_bChoosed=true;var n=i[0][0];var r=i[0][1];var a=i[1][0];var o=i[1][1];this.setPointInfo([[n,r],[a,r],[a,o],[n,o]]);this.draw()}},{key:"inArc",value:function e(t,i,n){var r=this.m_aPoint[0][0];var a=this.m_aPoint[0][1];var o=this.m_aPoint[2][0]-r;var s=this.m_aPoint[2][1]-a;var l=Math.round(o/2);var u=Math.round(s/2);var f=[r,r+l,r+o,r,r+o,r,r+l,r+o];var c=[a,a,a,a+u,a+u,a+s,a+s,a+s];for(var d=0;d<8;d++){var h=Math.sqrt((t-f[d])*(t-f[d])+(i-c[d])*(i-c[d]));if(h<n){this.m_iIndexChoosePoint=d;return true}}return false}}]);return r}(o);var c=function(i){l(r,i);function r(e,t){u(this,r);var i=s(this,(r.__proto__||Object.getPrototypeOf(r)).call(this));i.m_szType="RectOSD";i.m_szOSDType="overlay-date";i.m_szText=e||"";i.m_szEnabled=t||"";i.m_szDateStyle="";i.m_szClockType="";i.m_szDisplayWeek="";i.m_szId="";i.m_szAlignment="0";return i}n(r,[{key:"draw",value:function t(){if(this.m_szEnabled==="true"){var i=this.m_aPoint[0][0];var n=this.m_aPoint[0][1];var r=this.m_aPoint[2][0]-i;var a=this.m_aPoint[2][1]-n;e.oContext.beginPath();e.oContext.strokeStyle=this.m_szDrawColor;e.oContext.globalAlpha=1;e.oContext.rect(i,n,r,a);e.oContext.font="15px serif";e.oContext.strokeText(this.m_szText,i,n+15);e.oContext.stroke()}}},{key:"drag",value:function i(n,r){var a=this.m_aPoint.length;var o=0;if("0"===this.m_szAlignment){for(o=0;o<a;o++){if(this.m_aPoint[o][1]+r-this.m_iDriftStartY>e.m_iCanvasHeight||this.m_aPoint[o][0]+n-this.m_iDriftStartX<0||this.m_aPoint[o][1]+r-this.m_iDriftStartY<0){this.m_iDriftStartX=n;this.m_iDriftStartY=r;return}}for(o=0;o<a;o++){this.m_aPoint[o][0]=this.m_aPoint[o][0]+n-this.m_iDriftStartX;this.m_aPoint[o][1]=this.m_aPoint[o][1]+r-this.m_iDriftStartY}}else if("1"===this.m_szAlignment||"2"===this.m_szAlignment){for(o=0;o<a;o++){if(this.m_aPoint[o][1]+r-this.m_iDriftStartY>e.m_iCanvasHeight||this.m_aPoint[o][1]+r-this.m_iDriftStartY<0){this.m_iDriftStartX=n;this.m_iDriftStartY=r;return}}for(o=0;o<a;o++){this.m_aPoint[o][1]=this.m_aPoint[o][1]+r-this.m_iDriftStartY}}this.m_iDriftStartX=n;this.m_iDriftStartY=r;this.setEdgePoints(this.m_aPoint);t()}},{key:"stretch",value:function e(){}}]);return r}(o);var d=function(t){l(i,t);function i(){u(this,i);var e=s(this,(i.__proto__||Object.getPrototypeOf(i)).call(this));e.m_szType="Grid";e.m_iGridColNum=22;e.m_iGridRowNum=18;e.m_szGridMap="";e.m_aAddGridMap=[];return e}n(i,[{key:"draw",value:function t(){var i=e.m_iCanvasWidth/this.m_iGridColNum;var n=e.m_iCanvasHeight/this.m_iGridRowNum;var r="";for(var a=0;a<this.m_iGridRowNum;a++){var o=this.m_szGridMap.substring(a*6,a*6+6);var s=parseInt("f"+o,16).toString(2).split("").slice(4);var l="";for(var u=0;u<this.m_iGridColNum;u++){var f="";if(s[u]==="1"){e.oContext.strokeStyle=this.m_szDrawColor;e.oContext.globalAlpha=1;e.oContext.strokeRect(i*u,n*a,i,n);f="1"}else{f="0"}if(this.m_aAddGridMap.length){if(this.m_aAddGridMap[a][u]===1){e.oContext.strokeStyle=this.m_szDrawColor;e.oContext.strokeRect(i*u,n*a,i,n);f="1"}}l+=f}r+=parseInt("1111"+l+"00",2).toString(16).substring(1)}this.m_szGridMap=r}},{key:"move",value:function t(i,n,r,a){var o=e.m_iCanvasWidth/this.m_iGridColNum;var s=e.m_iCanvasHeight/this.m_iGridRowNum;var l=Math.floor(i/o);var u=Math.floor(n/s);var f=Math.floor(Math.abs(r-i)/o);var c=Math.floor(Math.abs(a-n)/s);var d=1;var h=1;if(r-i>0){d=1}else{d=-1}if(a-n>0){h=1}else{h=-1}var p=[];for(var y=0;y<this.m_iGridRowNum;y++){p[y]=[];for(var v=0;v<this.m_iGridColNum;v++){if(d===1){if(h===1){if(y>=u&&y<=u+c&&v>=l&&v<=l+f){p[y][v]=1}else{p[y][v]=0}}else{if(y<=u&&y>=u-c&&v>=l&&v<=l+f){p[y][v]=1}else{p[y][v]=0}}}else{if(h===1){if(y>=u&&y<=u+c&&v<=l&&v>=l-f){p[y][v]=1}else{p[y][v]=0}}else{if(y<=u&&y>=u-c&&v<=l&&v>=l-f){p[y][v]=1}else{p[y][v]=0}}}}}this.m_aAddGridMap=p;this.draw()}}]);return i}(o);var h=function(t){l(i,t);function i(){u(this,i);var e=s(this,(i.__proto__||Object.getPrototypeOf(i)).call(this));e.m_szType="Line";e.m_iLineType=0;e.m_iDirection=0;e.m_iArrowType=0;e.m_aCrossArrowPoint=[];return e}n(i,[{key:"draw",value:function e(){if(this.m_iLineType===0){this.drawNormalLine()}else if(this.m_iLineType===1){this.drawArrowLine()}else if(this.m_iLineType===3){this.drawCrossLine()}else if(this.m_iLineType===4){this.drawLineCount()}}},{key:"drawNormalLine",value:function t(){e.oContext.globalAlpha=1;if(this.m_aPoint.length>0){e.oContext.beginPath();e.oContext.strokeStyle=this.m_szDrawColor;e.oContext.lineWidth=2;e.oContext.moveTo(this.m_aPoint[0][0],this.m_aPoint[0][1]);for(var i=1,n=this.m_aPoint.length;i<n;i++){e.oContext.lineTo(this.m_aPoint[i][0],this.m_aPoint[i][1])}e.oContext.stroke();if(this.m_bChoosed){for(var r=0,a=this.m_aPoint.length;r<a;r++){e.oContext.beginPath();e.oContext.fillStyle=this.m_szDrawColor;e.oContext.arc(this.m_aPoint[r][0],this.m_aPoint[r][1],3,0,Math.PI*2,true);e.oContext.closePath();e.oContext.fill()}}if(this.m_szTips!==""){e.oContext.strokeStyle=this.m_szDrawColor;e.oContext.fillText(this.m_szTips,this.m_aPoint[0][0]+10,this.m_aPoint[0][1]+4)}}}},{key:"drawArrowLine",value:function t(i,n,r,a,o,s,l,u){s=typeof s!=="undefined"?s:30;l=typeof l!=="undefined"?l:10;u=typeof u!=="undefined"?u:1;var f=Math.atan2(r-o,n-a)*180/Math.PI;var c=(f+s)*Math.PI/180;var d=(f-s)*Math.PI/180;var h=l*Math.cos(c);var p=l*Math.sin(c);var y=l*Math.cos(d);var v=l*Math.sin(d);e.oContext.save();e.oContext.beginPath();var m=n-h;var g=r-p;e.oContext.moveTo(m,g);e.oContext.lineTo(n,r);m=n-y;g=r-v;e.oContext.lineTo(m,g);e.oContext.moveTo(n,r);e.oContext.lineTo(a,o);if(i===1){m=a+h;g=o+p;e.oContext.moveTo(m,g);e.oContext.lineTo(a,o);m=a+y;g=o+v;e.oContext.lineTo(m,g)}e.oContext.strokeStyle=this.m_szDrawColor;e.oContext.lineWidth=u;e.oContext.stroke();e.oContext.restore()}},{key:"drawCrossLine",value:function t(){this.drawNormalLine();var i=(this.m_aPoint[0][0]+this.m_aPoint[1][0])/2;var n=(this.m_aPoint[0][1]+this.m_aPoint[1][1])/2;var r=Math.atan2(n-this.m_aPoint[0][1],i-this.m_aPoint[0][0])*180/Math.PI;var a=(r+90)*Math.PI/180;var o=(r-90)*Math.PI/180;var s=25*Math.cos(a);var l=25*Math.sin(a);var u=25*Math.cos(o);var f=25*Math.sin(o);var c=0;var d=0;c=i-s;d=n-l;var h=0;var p=0;if(this.m_iDirection===0){h=-10;p=-15}else if(this.m_iDirection===1){h=10;p=10}else{h=10;p=-15}if(this.m_iDirection!==0){this.drawArrowLine(0,c,d,i,n)}e.oContext.strokeStyle=this.m_szDrawColor;e.oContext.font="8px";e.oContext.strokeText("A",c+h,d+4);c=i-u;d=n-f;if(this.m_iDirection!==1){this.drawArrowLine(0,c,d,i,n)}e.oContext.strokeStyle=this.m_szDrawColor;e.oContext.font="8px";e.oContext.strokeText("B",c+p,d+4)}},{key:"drawLineCount",value:function e(){this.drawNormalLine();var t=(this.m_aPoint[0][0]+this.m_aPoint[1][0])/2;var i=(this.m_aPoint[0][1]+this.m_aPoint[1][1])/2;var n=Math.atan2(i-this.m_aPoint[0][1],t-this.m_aPoint[0][0])*180/Math.PI;var r=(n+90)*Math.PI/180;var a=(n-90)*Math.PI/180;var o=25*Math.cos(r);var s=25*Math.sin(r);var l=25*Math.cos(a);var u=25*Math.sin(a);var f=0;var c=0;f=t-o;c=i-s;if(this.m_iArrowType===1){f=t-l;c=i-u;this.drawArrowLine(0,f,c,t,i)}else if(this.m_iArrowType===0){this.drawArrowLine(0,f,c,t,i)}this.m_aCrossArrowPoint=[[t,i],[f,c]]}},{key:"inShape",value:function e(t,i){var n=false;for(var r=0,a=this.m_aPoint.length-1;r<a;r++){var o=Math.sqrt((this.m_aPoint[r+1][0]-this.m_aPoint[r][0])*(this.m_aPoint[r+1][0]-this.m_aPoint[r][0])+(this.m_aPoint[r+1][1]-this.m_aPoint[r][1])*(this.m_aPoint[r+1][1]-this.m_aPoint[r][1]));var s=Math.sqrt((t-this.m_aPoint[r][0])*(t-this.m_aPoint[r][0])+(i-this.m_aPoint[r][1])*(i-this.m_aPoint[r][1]));var l=Math.sqrt((t-this.m_aPoint[r+1][0])*(t-this.m_aPoint[r+1][0])+(i-this.m_aPoint[r+1][1])*(i-this.m_aPoint[r+1][1]));if(s+l-o<1){n=true}}return n}}]);return i}(o);var p=function(i){l(r,i);function r(){u(this,r);var e=s(this,(r.__proto__||Object.getPrototypeOf(r)).call(this));e.m_szType="Polygon";e.m_iPolygonType=1;return e}n(r,[{key:"setPointInfo",value:function e(t){if(t!==null&&typeof t!=="undefined"){if(this.m_iPolygonType===0){var i=t[0][0];var n=t[0][1];var r=t[0][0];var a=t[0][1];for(var o=0,s=t.length;o<s;o++){if(i>t[o][0]){i=t[o][0]}if(n>t[o][1]){n=t[o][1]}if(r<t[o][0]){r=t[o][0]}if(a<t[o][1]){a=t[o][1]}}this.m_aPoint=[[i,n],[r,n],[r,a],[i,a]]}else if(this.m_iPolygonType===1){this.m_aPoint=t}else{this.m_aPoint=t}this.setEdgePoints(t)}}},{key:"draw",value:function t(){if(this.m_aPoint.length>0){e.oContext.fillStyle=this.m_szFillColor;e.oContext.strokeStyle=this.m_szDrawColor;e.oContext.globalAlpha=1;var i=0;var n=0;if(this.m_bChoosed){for(i=0,n=this.m_aPoint.length;i<n;i++){e.oContext.beginPath();e.oContext.arc(this.m_aPoint[i][0],this.m_aPoint[i][1],3,0,360,false);e.oContext.fillStyle=this.m_szDrawColor;e.oContext.closePath();e.oContext.fill()}}e.oContext.beginPath();e.oContext.moveTo(this.m_aPoint[0][0],this.m_aPoint[0][1]);for(i=0,n=this.m_aPoint.length;i<n;i++){if(i!==0){e.oContext.lineTo(this.m_aPoint[i][0],this.m_aPoint[i][1])}}e.oContext.stroke();if(this.m_bClosed){e.oContext.fillText(this.m_szTips,(this.m_oEdgePoints.left.x+this.m_oEdgePoints.right.x)/2,(this.m_oEdgePoints.top.y+this.m_oEdgePoints.bottom.y)/2);e.oContext.closePath();e.oContext.stroke();e.oContext.globalAlpha=this.m_iTranslucent;e.oContext.fill()}}}},{key:"move",value:function t(i,n,r,a){if(this.m_iPolygonType===1){if(this.m_aPoint.length<this.m_iMaxPointNum&&this.m_aPoint.length>0){e.oContext.fillStyle=this.m_szFillColor;e.oContext.strokeStyle=this.m_szDrawColor;e.oContext.globalAlpha=1;var o=0;var s=0;for(o=0,s=this.m_aPoint.length;o<s;o++){e.oContext.beginPath();e.oContext.arc(this.m_aPoint[o][0],this.m_aPoint[o][1],3,0,360,false);e.oContext.fillStyle=this.m_szDrawColor;e.oContext.closePath();e.oContext.fill()}e.oContext.beginPath();e.oContext.moveTo(this.m_aPoint[0][0],this.m_aPoint[0][1]);for(o=0,s=this.m_aPoint.length;o<s;o++){if(o!==0){e.oContext.lineTo(this.m_aPoint[o][0],this.m_aPoint[o][1])}}e.oContext.lineTo(i,n);e.oContext.closePath();e.oContext.stroke()}}else if(this.m_iPolygonType===0){this.m_bChoosed=true;var l=r;var u=a;var f=i;var c=n;this.setPointInfo([[l,u],[f,u],[f,c],[l,c]]);this.draw()}}},{key:"stretch",value:function e(i,n){if(this.m_iEditType===0){if(this.m_iPolygonType===1){if(this.m_iIndexChoosePoint!==-1){this.m_aPoint[this.m_iIndexChoosePoint][0]=i;this.m_aPoint[this.m_iIndexChoosePoint][1]=n}}else{if(this.m_iIndexChoosePoint===0){if(i<this.m_aPoint[2][0]&&n<this.m_aPoint[2][1]){this.m_aPoint[0][0]=i;this.m_aPoint[0][1]=n;this.m_aPoint[3][0]=i;this.m_aPoint[1][1]=n}}else if(this.m_iIndexChoosePoint===1){if(i>this.m_aPoint[3][0]&&n<this.m_aPoint[3][1]){this.m_aPoint[1][0]=i;this.m_aPoint[1][1]=n;this.m_aPoint[2][0]=i;this.m_aPoint[0][1]=n}}else if(this.m_iIndexChoosePoint===2){if(i>this.m_aPoint[0][0]&&n>this.m_aPoint[0][1]){this.m_aPoint[2][0]=i;this.m_aPoint[2][1]=n;this.m_aPoint[1][0]=i;this.m_aPoint[3][1]=n}}else if(this.m_iIndexChoosePoint===3){if(i<this.m_aPoint[1][0]&&n>this.m_aPoint[1][1]){this.m_aPoint[3][0]=i;this.m_aPoint[3][1]=n;this.m_aPoint[0][0]=i;this.m_aPoint[2][1]=n}}}this.setPointInfo(this.m_aPoint);t()}}}]);return r}(o);var y=function(t){l(i,t);function i(){u(this,i);var e=s(this,(i.__proto__||Object.getPrototypeOf(i)).call(this));e.m_szType="Point";e.m_szId="";return e}n(i,[{key:"draw",value:function t(){e.oContext.beginPath();e.oContext.fillStyle=this.m_szDrawColor;e.oContext.globalAlpha=1;e.oContext.arc(this.m_aPoint[0][0],this.m_aPoint[0][1],10,0,Math.PI*2,true);e.oContext.closePath();e.oContext.fill()}},{key:"drag",value:function e(){}},{key:"stretch",value:function e(){}}]);return i}(o);var v=function(){function o(t){u(this,o);e=this;this.oCanvas=(0,a.default)("#"+t);this.oContext=this.oCanvas[0].getContext("2d");this.aShapes=[];this.bDrawStatus=false;this.szShapeType="Rect";this.iMaxShapeNumSupport=10;this.bDrawShapeMultiOneTime=true;this.oCurrentShapeInfo={};this.oEventCallback=null;this.oShapeStyle={szDrawColor:"#ff0000",szFillColor:"#343434",iTranslucent:.7};this.bPolygonDrawing=false;this.m_iCanvasWidth=this.oCanvas.width();this.m_iCanvasHeight=this.oCanvas.height();this.m_iHorizontalResolution=0;this.m_iVerticalResolution=0;this.m_szDisplayMode="";this.m_szVideoFormat="";r();this.aShapes.length=0}n(o,[{key:"setDrawMutiShapeOneTime",value:function e(t){this.bDrawShapeMultiOneTime=t}},{key:"setMaxShapeSupport",value:function e(t){this.iMaxShapeNumSupport=t}},{key:"getMaxShapeSupport",value:function e(){return this.iMaxShapeNumSupport}},{key:"setDrawStatus",value:function e(t,i){this.bDrawStatus=t;if(i&&t){this.oEventCallback=i}if(!t){this.oEventCallback=null}}},{key:"setShapeType",value:function e(i){this.szShapeType=i;t()}},{key:"setCurrentShapeInfo",value:function e(t){this.oCurrentShapeInfo=t||{szId:"",szTips:"",iMinClosed:3,iMaxPointNum:11,iPolygonType:1,iRedrawMode:0}}},{key:"getShapeType",value:function e(){return this.szShapeType}},{key:"getAllShapesInfo",value:function e(){var t=[];for(var i=0,n=this.aShapes.length;i<n;i++){if(this.aShapes[i].m_szType==="Grid"){t.push({szType:this.aShapes[i].m_szType,szGridMap:this.aShapes[i].m_szGridMap,iGridColNum:this.aShapes[i].m_iGridColNum,iGridRowNum:this.aShapes[i].m_iGridRowNum})}else if(this.aShapes[i].m_szType==="RectOSD"){t.push({szType:this.aShapes[i].m_szType,szText:this.aShapes[i].m_szText,szEnabled:this.aShapes[i].m_szEnabled,szOSDType:this.aShapes[i].m_szOSDType,iPositionX:this.aShapes[i].m_aPoint[0][0],iPositionY:this.aShapes[i].m_aPoint[0][1],szDateStyle:this.aShapes[i].m_szDateStyle,szClockType:this.aShapes[i].m_szClockType,szDisplayWeek:this.aShapes[i].m_szDisplayWeek,szId:this.aShapes[i].m_szId,szAlignment:this.aShapes[i].m_szAlignment})}else{t.push({szType:this.aShapes[i].m_szType,aPoint:this.aShapes[i].m_aPoint,szId:this.aShapes[i].m_szId,bChoosed:this.aShapes[i].m_bChoosed})}}return t}},{key:"deleteRepeatPolyonById",value:function e(t){var i=this.getAllShapesInfo();var n=i.length;if(n>0){for(var r=0;r<n;r++){if(i[r].szType==="Polygon"){if(i[r].szId===t){this.deleteShape(r)}}}}}},{key:"getShapesInfoByType",value:function e(t){var i=[];for(var n=0,r=this.aShapes.length;n<r;n++){if(this.aShapes[n].m_szType===t){if(this.aShapes[n].m_szType==="Grid"){i.push({szType:this.aShapes[n].m_szType,szGridMap:this.aShapes[n].m_szGridMap,iGridColNum:this.aShapes[n].m_iGridColNum,iGridRowNum:this.aShapes[n].m_iGridRowNum})}else if(this.aShapes[n].m_szType==="RectOSD"){i.push({szType:this.aShapes[n].m_szType,szText:this.aShapes[n].m_szText,szEnabled:this.aShapes[n].m_szEnabled,szOSDType:this.aShapes[n].m_szOSDType,iPositionX:this.aShapes[n].m_aPoint[0][0],iPositionY:this.aShapes[n].m_aPoint[0][1],szDateStyle:this.aShapes[n].m_szDateStyle,szClockType:this.aShapes[n].m_szClockType,szDisplayWeek:this.aShapes[n].m_szDisplayWeek,szId:this.aShapes[n].m_szId,szAlignment:this.aShapes[n].m_szAlignment})}else if(t==="Polygon"){i.push({szType:this.aShapes[n].m_szType,szId:this.aShapes[n].m_szId,iPolygonType:this.aShapes[n].m_iPolygonType,iMinClosed:this.aShapes[n].m_iMinClosed,iMaxPointNum:this.aShapes[n].m_iMaxPointNum,iEditType:this.aShapes[n].m_iEditType,aPoint:this.aShapes[n].m_aPoint,bClosed:this.aShapes[n].m_bClosed,szTips:this.aShapes[n].m_szTips,szDrawColor:this.aShapes[n].m_szDrawColor,szFillColor:this.aShapes[n].m_szFillColor,iTranslucent:this.aShapes[n].m_iTranslucent})}else if(t==="Line"){i.push({szType:this.aShapes[n].m_szType,szId:this.aShapes[n].m_szId,aPoint:this.aShapes[n].m_aPoint,szTips:this.aShapes[n].m_szTips,iLineType:this.aShapes[n].m_iLineType,iDirection:this.aShapes[n].m_iDirection,iArrowType:this.aShapes[n].m_iArrowType,szDrawColor:this.aShapes[n].m_szDrawColor,aCrossArrowPoint:this.aShapes[n].m_aCrossArrowPoint})}else if(t==="Rect"){i.push({szType:this.aShapes[n].m_szType,iEditType:this.aShapes[n].m_iEditType,aPoint:this.aShapes[n].m_aPoint,szTips:this.aShapes[n].m_szTips,szDrawColor:this.aShapes[n].m_szDrawColor,szFillColor:this.aShapes[n].m_szFillColor,iTranslucent:this.aShapes[n].m_iTranslucent})}else{i.push({szType:this.aShapes[n].m_szType,aPoint:this.aShapes[n].m_aPoint})}}}return i}},{key:"setShapesInfoByType",value:function e(n,r){if(!r){r=[]}var a=null;if(n==="Rect"||n==="Polygon"||n==="Line"||n==="Point"){for(var o=0,s=r.length;o<s;o++){if(n==="Rect"){a=new f;a.m_iEditType=r[o].iEditType;a.m_szTips=r[o].szTips||"";a.m_szDrawColor=r[o].szDrawColor;a.m_szFillColor=r[o].szFillColor;a.m_iTranslucent=r[o].iTranslucent;a.m_iRedrawMode=r[o].iRedrawMode}else if(n==="Polygon"){a=new p;if(r[o].iPolygonType===0){a.m_bClosed=true}else{a.m_bClosed=r[o].bClosed}a.m_szTips=r[o].szTips||"";a.m_szId=r[o].szId||"";a.m_iPolygonType=r[o].iPolygonType;a.m_iMinClosed=r[o].iMinClosed||3;a.m_iMaxPointNum=r[o].iMaxPointNum||11;a.m_iEditType=r[o].iEditType;a.m_szDrawColor=r[o].szDrawColor;a.m_szFillColor=r[o].szFillColor;a.m_iTranslucent=r[o].iTranslucent;a.m_iRedrawMode=r[o].iRedrawMode}else if(n==="Line"){a=new h;a.m_iLineType=r[o].iLineType;a.m_szTips=r[o].szTips||"";a.m_szId=r[o].szId;a.m_iDirection=r[o].iDirection;a.m_iArrowType=r[o].iArrowType;a.m_szDrawColor=r[o].szDrawColor;a.setPointInfo(r[o].aPoint)}else if(n==="Point"){a=new y;a.m_szId=r[o].szId;a.m_szDrawColor=r[o].szDrawColor;a.setPointInfo(r[o].aPoint)}a.setPointInfo(r[o].aPoint);if(o===0){a.m_bChoosed=true}i(a)}}else if(n==="Grid"){a=new d;a.m_szGridMap=r[0].szGridMap||"";a.m_iGridColNum=r[0].iGridColNum||22;a.m_iGridRowNum=r[0].iGridRowNum||18;i(a)}t()}},{key:"addOSDShape",value:function e(n,r,a,o,s){if(!a&&!o){a=0;o=0}if(!s){s={}}var l=new c(n,r);var u=n.length*10;l.m_szOSDType=s.szOSDType||"";l.m_szDateStyle=s.szDateStyle||"";l.m_szClockType=s.szClockType||"";l.m_szDisplayWeek=s.szDisplayWeek||"";l.m_szId=s.szId||"";l.m_szAlignment=""+s.szAlignment||"0";if("0"===l.m_szAlignment){l.m_aPoint=[[a,o],[u+a,o],[u+a,o+20],[a,o+20]]}else if("1"===l.m_szAlignment){l.m_aPoint=[[0,o],[u,o],[u,o+20],[0,o+20]]}else if("2"===l.m_szAlignment){l.m_aPoint=[[this.m_iCanvasWidth-u,o],[this.m_iCanvasWidth,o],[this.m_iCanvasWidth,o+20],[this.m_iCanvasWidth-u,o+20]]}else{l.m_aPoint=[[a,o],[u+a,o],[u+a,o+20],[a,o+20]]}i(l);t()}},{key:"selectShapeById",value:function i(n,r){for(var a=0,o=e.aShapes.length;a<o;a++){if(n===e.aShapes[a].m_szType){if(r===e.aShapes[a].m_szId){e.aShapes[a].m_bChoosed=true}else{e.aShapes[a].m_bChoosed=false}}}t()}},{key:"setCanvasSize",value:function e(i,n){if(i>0&&n>0){this.m_iCanvasWidth=i;this.m_iCanvasHeight=n;t()}}},{key:"setDrawStyle",value:function e(t,i,n){this.oShapeStyle={szDrawColor:t,szFillColor:i,iTranslucent:n}}},{key:"clearAllShape",value:function i(){this.aShapes.length=0;e.bPolygonDrawing=false;t()}},{key:"clearShapeByType",value:function i(n){var r=this.aShapes.length;for(var a=r;a>0;a--){if(this.aShapes[a-1].m_szType===n){if(n==="Grid"){this.aShapes[a-1].m_szGridMap="";this.aShapes[a-1].m_aAddGridMap=[]}else{this.aShapes.splice(a-1,1)}}}if("Polygon"===n){e.bPolygonDrawing=false}t()}},{key:"deleteShape",value:function e(i){if(this.aShapes.length>i){this.aShapes.splice(i,1)}t()}},{key:"updateCanvas",value:function e(t){this.oCanvas=(0,a.default)("#"+t);this.oContext=this.oCanvas[0].getContext("2d");this.m_iCanvasWidth=this.oCanvas.width();this.m_iCanvasHeight=this.oCanvas.height();r()}},{key:"resizeCanvas",value:function e(){this.m_iCanvasWidth=this.oCanvas.width();this.m_iCanvasHeight=this.oCanvas.height()}},{key:"canvasRedraw",value:function e(){t()}}]);return o}();return v}();t.ESCanvas=f}])});