<template>
  <card title="应急事件">
    <!-- <emergencyList></emergencyList> -->
    <!--  应急事件组件-->
    <emergencyEvent
      :data="emergencyEventData"
      @change="changeEvenType"
    ></emergencyEvent>

    <a-spin :spinning="recordLoading">
      <!-- <Swiper
        v-if="emergencyEventList.length > 0"
        style="height: 180px; margin-top: 20px"
        :slides-per-view="2"
        :mousewheel="true"
        direction="vertical"
        :space-between="0"
        @slideChangeTransitionEnd="slideChange"
      >
        <SwiperSlide
          style="height: 90px"
          v-for="(obj, ind) in emergencyEventList"
          :key="ind + 'fbi'"
        >
          <eventDetailsItem
            :eventType="obj.eventLevel"
            :content="obj.title"
            :status="obj.completeStatus"
            @click="eventItemClick(obj.taskId)"
          ></eventDetailsItem>
        </SwiperSlide>
      </Swiper> -->
      <div
        v-if="emergencyEventList.length > 0"
        class="emerBox"
      >
        <div
          style="height: 90px"
          v-for="(obj, ind) in emergencyEventList"
          :key="ind + 'fbi'"
          @click="eventItemClick(obj.taskId)"
        >
          <eventDetailsItem
            :eventType="obj.eventLevel||1"
            :content="obj.title"
            :status="obj.completeStatus"
          ></eventDetailsItem>
        </div>
      </div>
      <div
        style="height: 180px; margin-top: 20px"
        class="event-details-item-none"
        v-else
      >
        <div class="ch">
          <div class="left-tag">
            <div class="left-tag-text">事件等级</div>
          </div>
          <div class="content">暂无应急任务...</div>
        </div>
      </div>
    </a-spin>
  </card>
</template>

<script>
import card from '@/components/card/index';
import { eventName2TypeMap } from '../../common/data'
import emergencyEvent from '@/components/emergencyEvent/index.vue'
import eventDetailsItem from '@/components/eventDetailsItem/index.vue'
import {
  getTaskDetail,
  emergencyStat,
  taskRecord,
} from '@/api/emergencyDetails/emergencyEvent'
export default {
  name: 'emergencyDetails',
  components: {
    emergencyEvent,
    eventDetailsItem,
    card
  },
  data() {
    return {
      emergencyEventData: {
        事故灾害: 0,
        自然灾害: 0,
        公共安全: 0,
        社区安全: 0,
      },
      emergencyEventDataTypeMap: {
        accident: '社区安全',
        community: '事故灾害',
        nature: '公共安全',
        publicSafety: '自然灾害',
      },
      taskId: '',
      getTaskDetailLoading: false,
      taskDetail: {},
      // 应急事件统计数据
      emergencyEventList: [],
      pageNum: 1,
      pageSize: 10,
      totalPages: Infinity,
      type: 0,
      recordLoading: false,
    }
  },
  mounted() {
    this.getData()
  },
  activated() {
    this.getData()
    this.emergencyEventList = []
    this.pageNum = 1
    getRecord()
  },
  watch: {
    /**
     * 监听事件列表变化 ==> 变化后进行任务详情的重新获取
     * 首次加载后及类型切换后都会重新获取数据
     */
    emergencyEventList: {
      handler(list) {
        if (list.length > 0) {
          this.taskId = list[0].taskId
        } else {
          this.taskId = ''
        }
      },
      immediate: true,
      deep: true,
    },
    taskDetail: {
      handler(detail) {
        console.log('改变的数据', detail)
        this.$emit('detailChange', detail)
      },
      deep: true,
    },
    taskId: {
      handler() {
        if(!this.taskId)return
        this.getInfo()
      },
      deep: true,
    },
    type: {
      handler(newType) {
        if (this.recordLoading) return
        this.emergencyEventList = []
        this.pageNum = 1
        this.getRecord()
      },
    },
    pageNum: {
      handler(newPageNum) {
        if (this.recordLoading) return
        this.getRecord()
      },
    },
  },
  methods: {
    /**
     * 事件列表滚动事件
     */
    slideChange(ev) {
      if (ev.activeIndex + 2 === this.pageNum * 10) {
        this.pageNum += 1
      }
    },
    /**
     * 事件类型改变
     * @param {Stirng} name 当前选中的事件名称
     */
    changeEvenType(name) {
      this.type = eventName2TypeMap[name]
    },
    /**
     * 事件列表项点击事件
     */
    eventItemClick(id) {
      this.taskId = id
    },
    /**
     * 获取应急任务分类统计
     */
    getData() {
      emergencyStat()
        .then((res) => {
          if (!res) throw new Error('请求失败')
          const { data } = res
          if (data.code === 200) {
            for (const key in data.data) {
              if (Object.hasOwnProperty.call(data.data, key)) {
                const val = data.data[key]
                this.emergencyEventData[this.emergencyEventDataTypeMap[key]] = val
              }
            }
          }
        })
        .catch((err) => {
          console.log('获取数据失败', err)
        })
        .finally(() => {
          console.log('获取数据请求完成---1')
        })
    },
    /**
     * 获取应急任务分类统计
     */
    getInfo() {
      // if (!taskId) return
      this.getTaskDetailLoading = true
      getTaskDetail(this.taskId)
        .then((res) => {
          if (!res) throw new Error('请求事件详情失败,无返回数据')
          const { data } = res
          console.log(data.data, '详情数据----222')
          if (data.code === 200) {
            this.taskDetail = data.data
          } else {
            this.taskDetail = {
              departmentExecutors: [],
              excutor: [],
              maxTime: null,
              minTime: null,
              task: {},
              taskId: '',
              taskTimeShaft: [],
            }
          }
        })
        .catch((err) => {
          console.log('获取事件详情数据失败', err)
        })
        .finally(() => {
          // console.log('获取事件详情数据请求完成')
          this.getTaskDetailLoading = false
        })
    },
    /**
   * 获取应急任务列表
   * @param {Object} 请求参数 
   * {
   * pageNum, pageSize, type: 1: '社区安全',
    2: '公共安全',
    3: '自然灾害',
    4: '事故灾害',
   * }
    @example
    getRecord({pageNum: 1, pageSize: 10, type: 1})
   */
    getRecord() {
      // if (this.pageNum >= this.totalPages) return
      this.recordLoading = true
      taskRecord({
        pageNum: this.pageNum || 1,
        pageSize: this.pageSize || 10,
        query: {
          emergencyEventType: this.type,
        },
      })
        .then((res) => {
          if (!res) throw new Error('请求失败')
          const { data } = res
          // console.log(data,'----------------266');
          if (data.code === 200) {
            this.emergencyEventList = this.emergencyEventList.concat(
              data.data.records
            )
            this.totalPages = data.data.pages
          }
        })
        .catch((err) => {
          console.log('获取数据失败', err)
        })
        .finally(() => {
          console.log('获取数据请求完成')
          this.recordLoading = false
        })
    },
  },
}
</script>


<style lang="less" scoped>
.emerBox{
  height: 180px;
  margin-top: 20px;
  overflow-y: auto;
  &::-webkit-scrollbar-track {
    border-radius: 10px;
    background: rgba(57, 177, 255, 0);
  }
  &::-webkit-scrollbar-thumb {
    border-radius: 10px;
    width: 1px;
    background: rgba(21, 62, 105, 0.5);
  }
}
.event-details-item-none {
  font-size: 16px;
  cursor: pointer;
  // margin: 15px 0;
  .ch {
    width: 100%;
    // height: 45px;
    padding: 15px 20px;
    display: flex;
    background-color: rgba(17, 55, 104, 0.5);
    border-bottom: 1px solid #164789;
  }
  .left-tag {
    width: 75px;
    height: 25px;
    background-image: linear-gradient(to right top, #d9a607, #705e1b);
    text-align: center;
    color: #000;
    line-height: 25px;
    transform: skewX(-25deg);
    margin-right: 20px;
    .left-tag-text {
      transform: skewX(25deg);
      font-size: 14px;
      font-weight: bold;
    }
  }
  .content {
    margin-right: 30px;
    flex: 1;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    color: #fff;
  }
}
</style>
