<style lang="less" scoped>
.login-container {
  position: relative;
  width: 100%;
  height: 100%;
  .login-bg {
    display: block;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 100%;
    margin: auto;
  }
  .show-sign {
    position: absolute;
    top: 40%;
    left: 8.6rem;
  }
  .login-title {
    position: absolute;
    top: 32%;
    left: 8.6rem;
  }
  .login-input {
    width: 3rem;
    height: 0.52rem;
    background: rgba(255, 255, 255, 1);
    font-size: 0.2rem;
    ::v-deep .ant-input {
      height: 0.52rem !important;
    }
  }
  .login-btn {
    height: 0.52rem;
    background: #007eff;
    width: 0.8rem;
    font-size: 0.17rem;
    border-radius: 0.08rem;
  }
  .login-checkBox {
    font-size: 0.17rem;
    color: rgba(255, 255, 255, 1);
    float: right;
    margin-top: 0.15rem;
    margin-right: 0.15rem;
  }
  .bottom {
    width: 100%;
    text-align: center;
    color: #aac7ff;
    font-size: 3px;
    position: absolute;
    letter-spacing: 2px;
    cursor: pointer;
  }
}
</style>
<style lang="less">
.login-container {
  .ant-input-affix-wrapper .ant-input:not(:first-child) {
    padding-left: 0.6rem !important;
    font-size: 0.2rem !important;
    border-radius: 0.1rem !important;
    border: none !important;
    outline: none !important;
  }
  .ant-input-affix-wrapper {
    border-radius: 0.08rem !important;
  }
  .has-error
    .ant-input-affix-wrapper:hover
    .ant-input:not(.ant-input-disabled) {
    font-size: 0.2rem !important;
    border: none !important;
    outline: none !important;
  }
  .ant-checkbox + span {
    margin-left: 0.03rem;
  }
}
</style>

<template>
  <section class="login-container">
    <!-- 背景 -->
    <!-- <img class="login-bg" src="@/assets/login-bg.gif" alt="login" /> -->
    <img class="login-bg" src="@/assets/login_bg_new.gif" alt="login" />
    <!-- time&sign -->
    <img class="login-title" src="@/assets/login_title.png" alt="login_title" />
    <!-- 登录 -->
    <div class="show-sign" key="show-sign">
      <!-- :form="form" -->
      <!-- <a-form
              layout="inline"
              :wrapper-col="{ span: 24 }"
              @submit="handleSubmit"
            > -->
      <a-form layout="inline" :wrapper-col="{ span: 24 }">
        <a-form-item>
          <a-input class="login-input" v-model="userName" placeholder="用户名">
            <img
              slot="prefix"
              src="@/assets/<EMAIL>"
              style="width: 0.39rem; height: 0.39rem"
            />
          </a-input>
        </a-form-item>
        <a-form-item>
          <a-input-password
            class="login-input"
            v-model="password"
            type="password"
            v-decorator="[
              'password',
              {
                rules: [{ required: true, message: '请输入密码' }],
              },
            ]"
            placeholder="密码"
            @pressEnter="handleSubmit"
          >
            <img
              slot="prefix"
              src="@/assets/<EMAIL>"
              style="width: 0.39rem; height: 0.39rem"
            />
          </a-input-password>
        </a-form-item>
        <a-form-item>
          <a-button type="primary" @click="handleSubmit" class="login-btn"
            >登录</a-button
          >
        </a-form-item>
      </a-form>
      <a-checkbox
        @change="onChange"
        class="login-checkBox"
        v-model="loginChecked"
        >记住密码</a-checkbox
      >
    </div>
    <div ref="BottmText" class="bottom" @click="handleJump()">
      金牛区智慧环保平台 蜀ICP备2020030595号-1
    </div>
  </section>
</template>

<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator'
import {
  Input,
  Icon,
  Form,
  Button,
  Checkbox,
  Avatar,
  message,
} from 'ant-design-vue'
import moment from 'moment'
import { setToken } from '@/utils/authority'
import { login, getUserInfo, getSystemUser } from '@/api/login'
import crypto from "@/utils/crypto"

@Component({
  components: {
    AInput: Input,
    AForm: Form,
    AFormItem: Form.Item,
    AIcon: Icon,
    AInputPassword: Input.Password,
    AButton: Button,
    ACheckbox: Checkbox,
    AAvatar: Avatar,
  },
})
export default class Login extends Vue {
  $refs!: {
    BottmText: HTMLFormElement
  }
  // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
  // @ts-ignore
  //   form: any = this.$form.createForm(this);
  userName: any = localStorage.userName ? localStorage.userName : ''
  password: any = localStorage.password ? localStorage.password : ''
  loginChecked = localStorage.loginChecked ? true : false
  @Watch('$route', { immediate: true })
  routechange(to: any, from: any) {
    // this.getUrlToken()
    this.unAuth()
  }
  mounted() {
    this.$refs.BottmText['style']["top"] = `${document.documentElement.clientHeight -40}px`
    window.addEventListener('resize',()=>{
      if (document.body.scrollHeight === window.screen.height && document.body.scrollWidth === window.screen.width) {
          this.$refs.BottmText['style']["top"] = `${document.documentElement.clientHeight -40}px`
        }else{
          this.$refs.BottmText['style']["top"] = `${document.documentElement.clientHeight -40}px`
        }
    })
  }
  getUrlToken() {
    const href = window.location.href
    if (href.indexOf('?') > -1) {
      const obj: any = this.getUrlkey(href)
      if (obj.token == '0e023658-b4e5-4770-9bc4-bd36eb2cf91e') {
        setToken(obj.token)
        const source: any = 2
        const formData = new FormData()
        formData.append('account', '***********')
        formData.append('password', '***********')
        formData.append('source', source)
        login(formData).then((res: any) => {
          console.log('res.data.data', res.data.data)
          // setToken(res.headers['x-auth-token'])
          setToken(res.data.data.token)
          localStorage.setItem('UserInfor', JSON.stringify(res.data.data.customizeUserDetails))
          this.$router.push('/homeMap')
        })
        // getUserInfo().then(res => {
        //   localStorage.setItem("UserInfor", JSON.stringify(res.data.data));
        // })
        // this.$router.push("/homeMap")
      }
    }
  }
  async unAuth(){
    const href = window.location.href
    if (href.indexOf('?') > -1) {
      const obj: any = this.getUrlkey(href)
      if(!obj.token) return 
      try{
        const {data} = await getSystemUser(obj.token)
        const {userName,password} = data.data
        const source: any = 2
        const formData = new FormData()
        formData.append('account', crypto.encrypt(userName))
        formData.append('password', crypto.encrypt(password))
        formData.append('source', crypto.encrypt(source))
        login(formData).then((res: any) => {
          setToken(res.data.data.token)
          localStorage.setItem('UserInfor', JSON.stringify(res.data.data.customizeUserDetails))
          this.$router.push('/homeMap')
        })
      }catch(e){
        
      }
    }
  }
  //地址栏转jSON对象
  getUrlkey(url: any) {
    var params = {}
    var urls = url.split('?') //分割地址和参数
    var arr = urls[1].split('&') //通过"&"并切割?后面的地址切割字符串
    for (var i = 0, l = arr.length; i < l; i++) {
      var a = arr[i].split('=')
      params[a[0]] = a[1]
    }
    // “=”左边和右边组成键值对，
    return params
  }
   handleSubmit(e: Event) {
    // 登录成功需添加自定义逻辑然后设置token
    if (this.userName !== '') {
      if (this.password !== '') {
        const source: any = 2
        const formData = new FormData()
        formData.append('account', crypto.encrypt(this.userName.trim()))
        formData.append('password', crypto.encrypt(this.password.trim()))
        localStorage.setItem('ue',window.btoa('admin'))
        localStorage.setItem('pd',window.btoa('EpAdmin123@'))
         formData.append('source', crypto.encrypt(source))
        login(formData).then((res: any) => {
          
          // setToken(res.headers['x-auth-token'])
          // localStorage.setItem('UserInfor', JSON.stringify(res.data.data))
          setToken(res.data.data.token)
          localStorage.setItem('UserInfor', JSON.stringify(res.data.data.customizeUserDetails))
          localStorage.removeItem('currentRoute')
          this.$router.push('/homeMap')
          if (this.loginChecked) {
            localStorage.userName = this.userName
            localStorage.password = this.password
            localStorage.loginChecked = 1
          }
        })
      } else {
        message.error('请输入密码')
      }
    } else {
      message.error('请输入用户名')
    }

    // }
    // });
  }
   onChange(e: any) {
    this.loginChecked = e.target.checked
  }
   handleJump() {
    window.open('https://beian.miit.gov.cn/#/Integrated/index')
  }
}
</script>

