<template>
  <div class="dialogElectronicGunItem-container flex items-center">
    <div class="left">
      <div class="title">{{ showData.name || '—' }}</div>
      <div class="content flex items-center justify-between">
        <span>{{ showData.content[0] || '—' }}</span>
        <span>{{ showData.content[1] || '—' }}</span>
      </div>
    </div>
    <div class="right flex flex-wrap justify-center items-center">
      <div class="percent">
        <span>{{ showData.percent || '—' }}</span>%
      </div>
      <div class="name">{{showData.percentName || '—' }}</div>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'dialogElectronicGunItem',
    props:{
      data: {
        type: Object,
        default: () => ({}),
      },
    },
    computed:{
      showData(){
        const { data } = this
        return {
          name: data.name,
          content: Array.isArray(data.content) ? data.content : [],
          percent: data.percent || '—',
          percentName: data.percentName || '气液比'
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  .dialogElectronicGunItem-container {
    width: 100%;

    .left {
      position: relative;
      padding: 18px 13px;
      background: linear-gradient(
        90deg,
        #082f4c 0%,
        rgba(2, 28, 44, 0.43) 100%
      );
      border-radius: 1px;
      flex: 1;

      &::before {
        content: ' ';
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 3px;
        background: linear-gradient(0deg, #0baec1 0%, #3784ab 100%);
        border-radius: 1px;
      }

      .title {
        font-size: 16px;
        color: #b7ebfe;
        position: absolute;
        top: 2px;
      }

      .content {
        font-size: 14px;
        color: #91a9b7;
        margin-top: 13px;

        span {
          flex: 1;
        }
      }
    }

    .right {
      width: 68px;
      height: 68px;
      background: url('../../../../../assets/images/<EMAIL>')
        no-repeat center center;
      background-size: 100% 100%;
      margin: 0 10px;

      .percent {
        display: inline-block;
        font-size: 13px;
        color: #04d3ff;
        width: 100%;
        text-align: center;
        span {
          font-size: 21px;
          color: #04d3ff;
        }
      }

      .name {
        display: inline-block;
        font-size: 13px;
        color: #a1c5da;
        margin-top: -20px;
        width: 100%;
        text-align: center;
      }
    }
  }
  .flex{
    display: flex;
  }
  .flex-wrap{
    flex-wrap: wrap;
  }
  .justify-between{
    justify-content: space-between;
  }
  .justify-center{
    justify-content: center;
  }
  .items-center {
    align-items: center;
  }
</style>
