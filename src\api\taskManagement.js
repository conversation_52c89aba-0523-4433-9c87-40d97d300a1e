/* eslint-disable */
import request from '@/utils/request'
// import qs from 'qs'

// 创建子任务 分组: 任务管理 负责人: 张金
export function createMicroTask(taskParentId, accountId, userName, departmentName, departmentId, content, annexUrl, startTime, endTime, isLinear, descriptionList, totalTaskId, taskSource, pollutantType) {
  return request({
     url: '/task/create-child',
    method: 'post',
    timeout: 0,
    data: {
      taskParentId,
      accountId,
      userName,
      departmentName,
      departmentId,
      content,
      baseAnnexList: annexUrl,
      startTime,
      endTime,
      isLinear,
      descriptionList,
      totalTaskId,
      taskSource,
      pollutantType
    }
  })
}

// 关闭任务 分组: 任务管理 负责人: 张金
export function closeTask(taskId, userId, completeRemark) {
  return request({
     url: '/task/close',
    method: 'post',
    data: {
      taskId,
      userId,
      completeRemark
    }
  })
}

// 完成任务 分组: 任务管理 负责人: 杨郁沛
export function completeTask(taskId, userId, completeRemark, taskAnnexList) {
  return request({
     url: '/task/complete',
    method: 'post',
    data: {
      taskId,
      userId,
      completeRemark,
      taskAnnexList
    }
  })
}

// 开始任务 分组: 任务管理 负责人: 张金
export function startTask(accountId, userName, taskId, arg4) {
  return request({
     url: '/task/start',
    method: 'post',
    data: {
      accountId,
      userName,
      taskId
    }
  })
}

// 任务详情 分组: 任务管理 负责人: 杨郁沛
export function taskDetail(taskId, userId) {
  return request({
    url: `/task/getTaskDetail`,
    method: 'get',
    params: {
      taskId,
      userId
    }
  })
}

// 任务列表 分组: 任务管理 负责人: 张金
export function taskList(pageNum, pageSize, accountId, type, queryStartTime, queryEndTime) {
  return request({
     url: '/task/all_task/list',
    method: 'post',
    data: {
      pageNum,
      pageSize,
      accountId,
      type,
      queryStartTime,
      queryEndTime
    }
  })
}

// 更新任务 分组: 任务管理 负责人: 张金
export function updateTask(taskId, accountId, userName, startTime, endTime, isLinear, descriptionList) {
  return request({
     url: '/task',
    method: 'put',
    data: {
      taskId,
      accountId,
      userName,
      startTime,
      endTime,
      isLinear,
      descriptionList
    }
  })
}

// 新增任务 分组: 任务管理 负责人: 张金
export function addNewTask(accountId, departmentId, taskSource, title, baseAnnexList, content, startTime, endTime, isLinear, descriptionList, copyToList, id, address, latitude, longitude, pollutantType) {
  return request({
     url: '/task',
    method: 'post',
    timeout: 300000,
    data: {
      accountId,
      departmentId,
      title,
      taskSource,
      baseAnnexList,
      content,
      startTime,
      endTime,
      isLinear,
      descriptionList,
      cc: copyToList,
      id,
      address,
      longitude,
      latitude,
      pollutantType
    }
  })
}

// 我的任务分页列表 分组: 任务管理 负责人: 张金
export function myTaskList(accountId, queryStartTime, queryEndTime, taskStatus, type, pageSize, pageNum, keywords) {
  return request({
     url: '/task/list',
    method: 'post',
    data: {
      accountId,
      queryStartTime,
      queryEndTime,
      taskStatus,
      type,
      pageSize,
      pageNum,
      keywords
    }
  })
}

// 评论任务 分组: 任务管理 负责人: 张金
export function commentTask(taskId, accountId, commentContent, userName) {
  return request({
     url: '/task/comment-task',
    method: 'post',
    data: {
      taskId,
      accountId,
      commentContent,
      userName
    }
  })
}

// 所有任务分页列表 分组: 任务管理 负责人: 张金
export function allTaskList(accountId, departmentId, pageSize, pageNum, queryStartTime, queryEndTime, departmentName, taskStatus, keywords) {
  return request({
     url: '/task/all_task/list',
    method: 'post',
    data: {
      accountId,
      departmentId,
      pageSize,
      pageNum,
      queryStartTime,
      queryEndTime,
      departmentName,
      taskStatus,
      keywords
    }
  })
}

// 获取任务指派详情 分组: 任务管理 负责人: 张金
export function assignTaskDetail(taskId, arg2, arg3, arg4) {
  return request({
    url: `/task/processDetails/${taskId}`,
    method: 'get'
  })
}

// 新增任务(子任务)执行部门选择列表 分组: 任务管理 负责人: 张金
export function taskDepartmentOption(departmentId, deptId, arg4) {
  return request({
    url: `/system/department/getTaskExecutorList?departmentId=${departmentId}`,
    method: 'get',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}

// 获取任务流程 分组: 任务管理 负责人: 张金
export function fetchTaskProcessFlow(tid, arg2, arg3, arg4) {
  return request({
    url: `/task/taskProcessFlow/${tid}`,
    method: 'get'
  })
}

// 任务详情 页面 处理日志列表 分组: 任务管理 负责人: 张金
export function taskLogList(taskId, arg2, arg3, arg4) {
  return request({
    url: `/task/task_log_list/${taskId}`,
    method: 'get'
  })
}

// 评论内容列表 分组: 任务管理 负责人: 张金
export function taskCommentList(taskId, accountId, arg3, arg4) {
  return request({
     url: '/task/task-comment/list',
    method: 'post',
    data: {
      taskId,
      accountId
    }
  })
}

//
export function fetchTaskFinishedDetail(taskId, arg2, arg3, arg4) {
  return request({
    url: `/task/${taskId}`,
    method: 'get'
  })
}

// 模板
export function _functionName1(arg1, arg2, arg3, arg4) {
  return request({
    url: '',
    method: 'post',
    data: {

    }
  })
}
// 新增任务 分组: 任务管理 负责人: 杨郁沛
export function addTask(data) {
  return request({
    url: `/task/createTask`,
    method: 'post',
    timeout: 300000,
    data
  })
}
// 获取任务分页列表 分组: 任务管理 负责人: 杨郁沛
export function getTaskPage(pageNum, pageSize, taskType, completeStatus, myTaskType, userId, startDate, endDate, keywords, overdueStatus, myCompleteStatus) {
  return request({
    url: `/task/getTaskPage`,
    method: 'get',
    params: {
      pageNum,
      pageSize,
      taskType,
      completeStatus,
      myTaskType,
      userId,
      startDate,
      endDate,
      keywords,
      overdueStatus,
      myCompleteStatus
    }
  })
}
// 获取局内执行者列表 分组: 任务管理 负责人: 杨郁沛
export function getPersonList(departmentId, withStreet) {
  return request({
    url: `/system/department/getTaskExecutorList`,
    method: 'get',
    params: {
      departmentId,
      withStreet
    }
  })
}
// 完成节点 分组: 任务管理 负责人: 杨郁沛
export function completeNodeTask(taskNodeId, operatorId, completeRemark, taskAnnexList) {
  return request({
    url: `/task/node/complete`,
    method: 'post',
    timeout: 30000,
    data: {
      taskNodeId,
      operatorId,
      completeRemark,
      taskAnnexList
    }
  })
}
// 关闭节点 分组: 任务管理 负责人: 杨郁沛
export function closeNodeTask(taskNodeId, operatorId, completeRemark, taskAnnexList) {
  return request({
    url: `/task/node/close`,
    method: 'post',
    data: {
      taskNodeId,
      operatorId,
      completeRemark,
      taskAnnexList
    }
  })
}
// 驳回节点 分组: 任务管理 负责人: 杨郁沛
export function rejectNodeTask(taskNodeId, operatorId, completeRemark) {
  return request({
    url: `/task/node/reject`,
    method: 'post',
    data: {
      taskNodeId,
      operatorId,
      completeRemark
    }
  })
}
// 指派节点 分组: 任务管理 负责人: 杨郁沛
export function assignNodeTask(data) {
  return request({
    url: `/task/node/assign`,
    method: 'post',
    // 设置延迟时间
    timeout: 30000,
    data
  })
}
// 获取预案列表 分组: 任务管理 负责人: 杨郁沛
export function getPlanList(planType, stationTypeId, userId, stationId) {
  return request({
    url: `/task/plan/getPlanList`,
    method: 'get',
    params: {
      planType,
      stationTypeId,
      userId,
      stationId
    }
  })
}
/**
 * @method functionName
 * @param {taskType} 任务类型
 * @param {completeStatus} 任务状态
 * @param {myTaskType} 处理类型
 * @param {userId} 用户id
 * @param {startDate} 开始日期
 * @param {endDate} 结束日期
 * @param {keywords} 关键词
 * @param {overdueStatus} 逾期状态
 * @description 导出我的任务表格
 */
export function exportExcel(taskType, completeStatus, myTaskType, userId, startDate, endDate, keywords, overdueStatus, myCompleteStatus) {
  return request({
    url: `/task/export`,
    method: 'get',
    responseType: 'blob',
    params: {
      taskType,
      completeStatus,
      myTaskType,
      userId,
      startDate,
      endDate,
      keywords,
      overdueStatus,
      myCompleteStatus
    }
  })
}
export function exportExcelTable(params) {
  return request({
    url: `/task/export`,
    method: 'get',
    responseType: 'blob',
    params
  })
}
// 获取预案列表 分组: 任务管理 负责人: 杨郁沛
export function getTopTaskExecutorList() {
  return request({
    url: `/system/department/getTopTaskExecutorList`,
    method: 'get',
  })
}
/**
 * 导出任务详情
 */
 export function exportTaskDetail(params) {
  return request({
    url: `/task/exportTaskDetail`,
    method: 'get',
    responseType: 'blob',
    params
  })
}
/**
 * 删除任务
 */
 export function removeTask(data) {
  return request({
    url: `/task/delete`,
    method: 'post',
    data
  })
}
