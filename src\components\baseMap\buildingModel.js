import { PolygonLayer, LineLayer } from '@antv/l7'

// 引入地图数据
import line1 from '@/data/waterline1.json'
import line2 from '@/data/waterline2.json'
import bridge from '@/data/bridge.json'
import waterStation from '@/data/waterStation.json'
import waterPng from '@/assets/waterPng/water.png';

/**
 * 初始化桥面数据
 * @param scene 场景
 */
export function initBridge(scene) {
  const layer = new PolygonLayer({
    zIndex: 3,
    minZoom: 16,
  })
    .source(bridge)
    .shape('extrude')
    .size(1)
    .color('#92918c')
    .style({
      opacity: 1.0,
    })
  scene.addLayer(layer)
}

/**
 *
 * @param scene 初始化水五厂模型
 */
export function initWaterStationBuilding(scene) {
  const layer = new PolygonLayer({
    zIndex: 1,
    minZoom: 16,
  })
    .source(waterStation)
    .shape('extrude')
    .size(10)
    .color('#467bc8')
    .style({
      opacity: 1.0,
    })
  scene.addLayer(layer)
}

/**
 * 初始化河流效果
 * @param {Object} scene 场景对象
 */
export function initRiver(scene) {
  const imgPath = '/water.png'

  scene.addImage('road', waterPng)

  const lineLayer1 = new LineLayer({
    zIndex: 0,
  })
    .source(line1)
    .size(10)
    .shape('line')
    .texture('road')
    .color('rgb(12, 182, 183)')
    .animate({
      interval: 1, // 间隔
      duration: 2, // 持续时间，延时
      trailLength: 2, // 流线长度
    })
    .style({
      lineTexture: true, // 开启线的贴图功能
      iconStep: 100, // 设置贴图纹理的间距
      // textureBlend: 'replace',
    })

  const lineLayer2 = new LineLayer({
    zIndex: 1,
  })
    .source(line2)
    .size(15)
    .shape('line')
    .texture('road')
    .color('rgb(12, 182, 183)')
    .animate({
      interval: 1, // 间隔
      duration: 5, // 持续时间，延时
      trailLength: 2, // 流线长度
    })
    .style({
      lineTexture: true, // 开启线的贴图功能
      iconStep: 100, // 设置贴图纹理的间距
      // textureBlend: 'replace',
    })

  scene.addLayer(lineLayer1)
  scene.addLayer(lineLayer2)
  // console.log('初始化河流效果',lineLayer1,imgPath,scene)
}
