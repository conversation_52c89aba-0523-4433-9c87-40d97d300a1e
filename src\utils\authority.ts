import Cookies from "js-cookie";

const TokenKey = "auth-token";

export function getToken(): string | undefined | null {
  // return Cookies.get(TokenKey);
  return sessionStorage.getItem(TokenKey);
}

export function setToken(token: string): void {
  // return Cookies.set(To<PERSON><PERSON><PERSON>, token, { expires: 1, sameSite: "strict" });
  sessionStorage.setItem(TokenKey, token)
}

export function removeToken(): void {
  // return Cookies.remove(TokenKey);
  sessionStorage.removeItem(TokenKey);
}
