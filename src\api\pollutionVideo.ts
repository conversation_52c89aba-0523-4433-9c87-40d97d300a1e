import request from "@/utils/request";
import { AxiosPromise } from "axios";

/**
 * @method getPollutionVideo
 * @param {type} data 说明
 * @description 获取污染源监控列表
 */
export function getPollutionVideo(params:any): AxiosPromise<any> {
  return request({
    url: `/water/waste-solid-camera/pageList`,
    method: "get",
    params
  });
}
/**
 * @method fetchCameraUrl
 * @param {number} stationId 站点id
 * @description 获取监控直播地址
 */
export function pollutionCameraUrl(channelId?: string): AxiosPromise<any> {
  return request({
    url: `/water/waste-solid-camera/live/${channelId}`,
    method: "get",
  });
}
/**
 * @method getPollutionVideo
 * @param {type} data 说明
 * @description 获取雷达监控列表
 */
export function getRandaVideo(params:any): AxiosPromise<any> {
  return request({
    url: `/air/bigData/lidar-camera/pageList`,
    method: "get",
    params
  });
}
/**
 * @method fetchCameraUrl
 * @param {number} stationId 站点id
 * @description 获取雷达监控直播地址
 */
export function randaCameraUrl(channelId?: string): AxiosPromise<any> {
  return request({
    url: `/air/bigData/lidar-camera/getLiveAddress/${channelId}`,
    method: "get",
  });
}