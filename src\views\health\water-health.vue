<template>
  <div class="water-health">
    <div class="left">
      <div class="top-chart">
        <div class="title">
          <span>区域河流水质情况占比</span>
          <PeriodTimeSelect @getPeriodData="getPeriodData" />
        </div>
        <div class="sub-title">
          <img src="@/assets/biaoti.png" alt class="title-img" />
        </div>
        <HealthRosePie
          id="warter-health-pie"
          :propData="chartData"
          width="410px"
          height="350px"
        />
      </div>

      <div class="bottom-info">
        <div class="title">
          <span>水域功能区标准</span>
        </div>
        <div class="sub-title">
          <img src="@/assets/biaoti.png" alt class="title-img" />
        </div>

        <div class="info-list">
          <div class="info-title">
            <div>水质类别</div>
            <div>水质功能</div>
          </div>
          <div class="info-item" v-for="item in infoList" :key="item.key">
            <div :style="{ color: item.color, background: item.bgColor }">
              {{ item.name }}
            </div>
            <div>{{ item.info }}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="right">
      <div class="legend">
        <div class="legend-title">图例</div>
        <div class="legend-content">
          <div class="legend-list">
            <div v-for="item in typeList" :key="item">{{ item }}</div>
          </div>
          <div class="legend-bar">
            <div class="line" v-for="item in typeList" :key="item"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { rivers } from "@/assets/map-geojson/rivers";
import markerTitle from "@/assets/health/title-icon.png";
import { riverCount, riverList } from "@/api/health";
import HealthRosePie from "@/components/Charts/HealthRosePie.vue";
import PeriodTimeSelect from "@/components/periodTimeSelect/periodTimeSelect.vue";

export default {
  name: "WaterHealth",
  components: {
    HealthRosePie,
    PeriodTimeSelect,
  },
  props: {
    map: {
      required: true,
    },
    AMap: {
      required: true,
    },
  },
  watch: {
    map(newVal) {
      if (newVal) {
        this.$nextTick(() => {
          this.drawRiver();
        });
      }
    },
  },
  data() {
    return {
      polylineList: [],
      markerList: [],
      periodList: [
        { label: "年度", value: 1 },
        { label: "季度", value: 2 },
        { label: "月度", value: 3 },
      ],
      date: "",
      quarterList: [
        { label: "一季度", value: "01-01,3-31" },
        { label: "二季度", value: "04-01,6-30" },
        { label: "三季度", value: "07-01,9-30" },
        { label: "四季度", value: "10-01,12-31" },
      ],
      periodValue: 1,
      infoList: [
        {
          key: 0,
          color: "#2CC3EC",
          bgColor: "#02305B",
          name: "Ⅰ-Ⅱ类",
          info:
            "饮用水源地一级保护区、珍稀水生生物栖息地、 鱼虾 类产卵场、仔稚幼鱼的索饵场等",
        },
        {
          key: 1,
          color: "#6EBE44",
          bgColor: "#18342E",
          name: "Ⅲ类",
          info:
            "饮用水源地二级保护区、鱼虾类越冬场、洄游 通道、 水产养殖区、游泳区",
        },
        {
          key: 2,
          color: "#EAE84D",
          bgColor: "#283736",
          name: "Ⅳ类",
          info: "一般工业用水和人体非直接接触的娱乐用水",
        },
        {
          key: 3,
          color: "#F69331",
          bgColor: "#2A2629",
          name: "Ⅴ类",
          info: "农业用水及一般景观用水",
        },
        {
          key: 4,
          color: "#FC602A",
          bgColor: "#2B2231",
          name: "劣Ⅴ类",
          info: "除调节局部气候外，使用功能较差",
        },
      ],
      typeList: ["Ⅰ类", "Ⅱ类", "Ⅲ类", "Ⅳ类", "Ⅴ类", "劣Ⅴ类"].reverse(),
      chartData: [],
      riverList: [],
      waterColorMap: {
        Ⅰ: "#BCE5EB",
        Ⅱ: "#37C6EC",
        Ⅲ: "#77C14E",
        Ⅳ: "#EAE956",
        Ⅴ: "#F68E30",
        劣Ⅴ: "#EE2B29",
      },
    };
  },
  created() {
    this.getRiverList();
  },
  beforeDestroy() {
    this.map.clearMap();
  },
  methods: {
    drawRiver() {
      rivers.forEach((river) => {
        const waterType = this.riverList.find(
          (item) => item.riverId == river.riverId
        )?.waterType;
        const polyline = new AMap.Polyline({
          path: river.paths,
          isOutline: false,
          strokeColor: this.waterColorMap[waterType],
          strokeOpacity: 0.7,
          strokeWeight: 12,
          lineJoin: "round",
          lineCap: "round",
          extData: {
            riverId: river.riverId,
          },
        });
        this.polylineList.push(polyline);
        this.map.add(polyline);

        polyline.on("mouseover", (e) => {
          e.target.setOptions({ strokeOpacity: 0.5 });
        });
        polyline.on("mouseout", (e) => {
          e.target.setOptions({ strokeOpacity: 0.7 });
        });

        if (river.center) {
          const marker = new AMap.Marker({
            position: new AMap.LngLat(river.center[0], river.center[1]),
            // icon: icon,
            content: `<div class="river-content">
              <div>
                <img src="${markerTitle}" /> <span>${river.name}</span>
              </div>
              <div class="warter-type">
                <span>目标水质:</span> 
                <span style="color:${
                  this.waterColorMap[
                    this.riverList.find(
                      (item) => item.riverId === river.riverId
                    )?.targetWaterType
                  ]
                }"> ${
              this.riverList.find((item) => item.riverId === river.riverId)
                ?.targetWaterType
            }</span>
                <span style="margin-left: 5px;">当前水质:</span>
                <span style="color:${
                  this.waterColorMap[
                    this.riverList.find(
                      (item) => item.riverId === river.riverId
                    )?.waterType
                  ]
                }">${
              this.riverList.find((item) => item.riverId === river.riverId)
                ?.waterType
            }</span>
              </div>
            </div>`,
            offset: new AMap.Pixel(-86, -100),
            extData: {
              riverId: river.riverId,
            },
          });

          marker.on("mouseover", (e) => {
            console.log(e);
            this.polylineList
              .find(
                (item) =>
                  item.getExtData().riverId === e.target.getExtData().riverId
              )
              .setOptions({ strokeOpacity: 0.5 });
          });
          marker.on("mouseout", (e) => {
            this.polylineList
              .find(
                (item) =>
                  item.getExtData().riverId === e.target.getExtData().riverId
              )
              .setOptions({ strokeOpacity: 0.7 });
          });

          this.map.add(marker);
          this.markerList.push(marker);
        }
      });
    },
    getRiverCount() {
      riverCount(this.startDate, this.endDate).then((res) => {
        this.chartData = res.data.data.typeList.map((item) => ({
          name: item.name + "类",
          value: item.count,
          rate: item.rate,
        }));
      });
    },
    getRiverList() {
      riverList().then((res) => {
        this.riverList = res.data.data;

        if (this.map && this.polylineList.length === 0) {
          this.drawRiver();
        }
      });
    },
    getPeriodData(data) {
      this.startDate = data.startDate;
      this.endDate = data.endDate;
      this.getRiverCount();
    },
  },
};
</script>
<style lang="less" scoped>
.water-health {
  display: flex;
  justify-content: space-between;
  width: 100vw;
  height: 0;
}
.right {
  width: 480px;
  height: calc(100vh - 100px);
  pointer-events: none;
}
.left {
  padding: 0 30px;
  box-sizing: border-box;
  width: 480px !important;
  height: calc(100vh - 100px);
  background: linear-gradient(
    to right,
    #000719,
    #000719d9,
    #000719d9,
    #000719d9,
    #0007190d
  );
}
.title {
  margin-top: 40px;
  font-family: Source Han Sans CN;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0 0 5px blue, 0 0 5px blue;
  font-size: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 420px;
  .selects {
    display: flex;
    flex-wrap: nowrap;
  }
}
.sub-title {
  margin-bottom: 20px;
}
</style>
<style lang="less">
// 高德地图去掉左下角logo
.amap-logo {
  display: none !important;
}
.amap-copyright {
  opacity: 0;
}
.title-select-period,
.title-select-date {
  display: flex;
  justify-content: space-between;
  .ant-select-selection {
    display: flex;
    justify-content: center;
    width: 70px;
    height: 23px;
    // background: rgba(14, 139, 255, 0.32);
    border: none;
    border-radius: unset;
    // clip-path: polygon(100% 0, 100% 100%, 12% 100%, 0% 70%, 0 0);
    background: url(../../assets/<EMAIL>);
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }
  .ant-select-selection-selected-value {
    color: rgba(0, 234, 255, 1);
    font-size: 12px;
    height: 23px;
    line-height: 23px;
    overflow: hidden; //超出的文本隐藏
    text-overflow: ellipsis; //溢出用省略号显示
    white-space: nowrap; //溢出不换行
  }

  .ant-select-focused .ant-select-selection,
  .ant-select-selection:focus,
  .ant-select-selection:active {
    border-color: #40a9ff;
    border-right-width: 0 !important;
    outline: 0;
    box-shadow: none;
  }
}
.title-select-date {
  margin-left: 10px;
  .ant-select-selection {
    width: 80px;
  }
}
.ant-select-dropdown-menu-item {
  font-size: 12px !important;
}

.info-title {
  color: #37c6eb;
  display: flex;
  div:nth-child(1) {
    width: 100px;
  }
}
.info-item {
  display: flex;
  align-items: center;
  padding: 15px 30px 15px 0;
  margin-top: 10px;
  &:nth-child(even) {
    background: linear-gradient(
      to right,
      transparent,
      #04254db3,
      #04254d,
      #04254db3,
      transparent
    );
    border: 1px solid;
    border-image: linear-gradient(to right, transparent, #074068, transparent) 1;
  }
  &:nth-child(odd) {
    background: linear-gradient(
      to right,
      transparent,
      #041630b3,
      #041630,
      #041630b3,
      transparent
    );
    border: 1px solid;
    border-image: linear-gradient(to right, transparent, #052046, transparent) 1;
  }
  border-right: none !important;
  border-left: none !important;
  div:nth-child(1) {
    width: 70px;
    flex-shrink: 0;
    height: 24px;
    margin-right: 30px;
    border-radius: 5px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
.right {
  position: relative;
  .legend {
    height: 225px;
    width: 86px;
    padding: 10px;
    background: rgba(0, 4, 18, 0.5);
    border: 1px solid #033d78;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    position: absolute;
    bottom: 20px;
    right: 20px;
  }
}
.legend-bar {
  width: 21px;
  height: 183px;
  background: linear-gradient(
    0deg,
    #c1e6eb 0%,
    #2cc3ec 20%,
    #6ebe44 40%,
    #eae84d 60%,
    #f69331 79%,
    #ee2a29 100%
  );
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding-top: 5px;
  box-sizing: border-box;
  .line {
    width: 10px;
    height: 1px;
    border-bottom: 1px solid #fff;
    &:last-child {
      margin-bottom: 12px;
    }
  }
}
.legend-title {
  font-size: 14px;
  margin-bottom: 10px;
  color: #50a9ff;
  text-align: center;
}
.legend-list {
  height: 183px;
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: space-between;
  div {
    font-size: 12px;
    color: #ffffff;
  }
}
.legend-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
</style>
<style lang="less">
.river-content {
  width: 172px;
  height: 142px;
  font-size: 12px;
  color: #21edff;
  background: url(../../assets/health/riverName.png);
  padding: 10px 6px 10px 12px !important;
  .warter-type {
    color: #cae0f1;
    margin-top: 3px;
  }
}
</style>
