<template>
  <div class="left-thrid-container" v-if="patrolList && patrolList.length">
    <div class="item" :class="{active: item.taskId === taskId}" v-for="(item, index) in patrolList" :key="index" @click="handleClick(item)">
      <img class="icon" src="@/assets/patrolKanban/<EMAIL>" alt="" />
      <div class="right_box">
        <div class="title">
          <span class="text">{{item.title || '--'}}</span>
          <span class="status" :class="[item.completeStatus === 0 ? 'progress' :  item.completeStatus === 1 ? 'finish' : 'completed']">{{compaleteStatusObj[item.completeStatus] || '--'}}</span>
        </div>
        <div class="time">{{item.startTime || '--'}}</div>
      </div>
    </div>
  </div>
  <div class="left-thrid-container" style="display: flex; justify-content: center; align-items: center" v-else>
    暂无数据
  </div>
</template>

<script>
export default {
  name: "",
  data() {
    return {
      compaleteStatusObj: {
        0: '进行中',
        1: '已完成',
        2: '已关闭'
      }
    };
  },
  props: {
    patrolList: {
      type: Array,
      default: () => {
        return [];
      },
    },
    taskId: {
      type: String,
      default: ''
    }
  },
  created() {},
  methods: {
    handleClick(item) {
      console.log(item)
      this.$emit('clickMarker', item.taskId)
    }
  },
  computed: {},
  watch: {},
  components: {},
};
</script>

<style lang="less" scoped>
.left-thrid-container {
  box-sizing: border-box;
  padding-top: 20px;
  height: 320px;
  overflow: auto;
  .item {
    width: 376px;
    height: 76px;
    background: url("~@/assets/patrolKanban/bg1.png") no-repeat center;
    background-size: contain;
    display: flex;
    box-sizing: border-box;
    padding: 18px 0 0 18px;
    margin-bottom: 20px;
    &.active {
       background: url("~@/assets/patrolKanban/bg2.png") no-repeat center;
       background-size: contain;
    }
    .icon {
      width: 24px;
      height: 28px;
      margin-right: 10px;
    }
    .right_box {
      flex: 1;
      .title {
        display: flex;
        justify-content: space-between;
        font-size: 14px;
        font-family: PingFang SC;
        font-weight: 500;
        color: #c9dcea;
        box-sizing: border-box;
        padding-right: 20px;
        .text{
          width: 240px;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }
        .status {
          // width: 60px;
          flex-shrink: 0;
        }
        .progress {
            color: #DEAF1E;
        }
        .finish {
            color: #18D5B5;
        }
        .completed {
            color: #B3D5E8;
        }
      }
      .time {
        font-size: 12px;
        font-family: PingFang SC;
        font-weight: 400;
        color: #0d9cd6;
        margin-top: 10px;
      }
    }
  }
}
</style>
