import router from "./router";
import NProgress from "nprogress";
import "nprogress/nprogress.css";
import { getToken } from "@/utils/authority";
import setting from "./settings";
import Cookies from "js-cookie";
import { setToken } from "@/utils/authority";
function getPageTitle(pageTitle: string): string {
  if (pageTitle) {
    return `${pageTitle} - ${setting.title}`;
  }
  return `${setting.title}`;
}

import { message } from "ant-design-vue";

NProgress.configure({
  showSpinner: false,
  easing: "ease",
  speed: 500,
  trickle: false
});

const whiteList = ["/login", "/401", "/404"];

let repaly = localStorage.getItem('repaly') === "true";

function reload() {
  if(repaly){
    localStorage.setItem('repaly', 'false')
    return;
  }
  console.log('刷新页面');
  
  window.location.reload();
  localStorage.setItem('repaly', 'true')
}
router.beforeEach((to:any, from, next) => {
  NProgress.start();
  // set page title
  document.title = getPageTitle(to.meta.title);
  // auth token
  const hasToken = getToken();
  if (hasToken) {
    // has token
    next();
    NProgress.done();
    // reload()
  } else {
    // not has token
    if (whiteList.includes(to.path)) {
      next();
      NProgress.done();
      // reload()
    } else {
      next(`/login`);
      NProgress.done();
    }
  }
  // message.success(`成功进入${to.path}`);
});

router.afterEach(() => {
  NProgress.done();
});
