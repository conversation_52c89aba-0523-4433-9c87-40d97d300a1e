import request from "@/utils/request";
import { AxiosPromise } from "axios";

/**
 * @method functionName
 * @param {type} data 说明
 * @description
 */
export function login(data: any): AxiosPromise<any> {
  return request({
    url: "/auth/login/handler",
    method: "post",
    headers: {
      'Content-Type': 'multipart/form-data',
      isToken: false
    },
    // headers: {
    //   'Content-Type': 'application/x-www-form-urlencoded'
    // },
    data: data
  });
}
/**
 * @description 获取部门列表
 * @method getDepartment
 */
export function getDepartment() {
  return request({
    url: '/system/department/getDepartmentListByIds?ids=11,12,13,14,16,17',
    method: 'get'
  })
}
// 获取局内执行者列表 分组: 任务管理 负责人: 杨郁沛
export function getPersonList(departmentId:any, withStreet:any) {
  return request({
    url: `/system/department/getTaskExecutorList`,
    method: 'get',
    params: {
      departmentId,
      withStreet
    }
  })
}
export function getTopTaskExecutorList() {
  return request({
    url: `/system/department/getTopTaskExecutorList`,
    method: 'get',
  })
}
export function getUserInfo() {
  return request({
    url: `/session/user/get`,
    method: 'get',
  })
}
/** 根据token 获取账号密码 */
export function getSystemUser(token:any) {
  return request({
    url: `/open/getSystemUser`,
    method: 'get',
    headers: { 'open-token': token},
  })
}
