<template>
  <div
    class="marker"
    :class="[
      iconList[icon].name === '摄像头' || iconList[icon].name === '音视频'
        ? 'marker2'
        : '',
    ]"
    ref="marker"
  >
    <img :src="iconList[icon].icon" alt="" />
  </div>
</template>
<script>
// import yj_maker_sxt from '@/assets/images/<EMAIL>';
import yj_maker_sxt from '@/assets/images/jk_new.png'
// import yj_maker_sgbj from '@/assets/images/<EMAIL>';
import yj_maker_sgbj from '@/assets/images/<EMAIL>'
// import yj_maker_ysp from '@/assets/images/<EMAIL>';
import yj_maker_ysp from '@/assets/images/yz_new.png'
import yj_maker_wzck from '@/assets/images/<EMAIL>'
import yj_maker_zlsc from '@/assets/images/<EMAIL>'
import yj_maker_jzgd from '@/assets/images/<EMAIL>'
import yj_maker_qxhy from '@/assets/images/<EMAIL>'
import yj_maker_cyyy from '@/assets/images/<EMAIL>'
import yj_maker_jyz from '@/assets/images/<EMAIL>'
import yj_maker_ysqy from '@/assets/images/<EMAIL>'
import yj_maker_yljg from '@/assets/images/<EMAIL>'
export default {
  name: 'markerItem',
  props: {
    icon: {
      type: String,
      default: '摄像头',
    },
  },
  data() {
    return {
      iconList: {
        摄像头: {
          name: '摄像头',
          icon: yj_maker_sxt,
          selected: false,
        },
        声光报警器: {
          name: '声光报警器',
          icon: yj_maker_sgbj,
          selected: false,
        },
        音视频: {
          name: '音视频',
          icon: yj_maker_ysp,
          selected: false,
        },
        物资仓库: {
          name: '物资仓库',
          icon: yj_maker_wzck,
          selected: false,
        },
        // 重污企业: {
        //   name: '重污企业',
        //   icon: new URL(
        //     '/src/assets/images/<EMAIL>',
        //     import.meta.url,
        //   ).href,
        //   selected: false,
        // },
        自来水厂: {
          name: '自来水厂',
          icon: yj_maker_zlsc,
          selected: false,
        },
        建筑工地: {
          name: '建筑工地',
          icon: yj_maker_jzgd,
          selected: false,
        },
        汽修行业: {
          name: '汽修行业',
          icon: yj_maker_qxhy,
          selected: false,
        },
        餐饮油烟: {
          name: '餐饮油烟',
          icon: yj_maker_cyyy,
          selected: false,
        },
        加油站: {
          name: '加油站',
          icon: yj_maker_jyz,
          selected: false,
        },
        印刷企业: {
          name: '印刷企业',
          icon: yj_maker_ysqy,
          selected: false,
        },
        医疗机构: {
          name: '医疗机构',
          icon: yj_maker_yljg,
          selected: false,
        },
      },
    }
  },
}
</script>

<style lang="less" scoped>
.marker {
  width: 68px;
  height: 68px;
  cursor: pointer;
  pointer-events: auto;

  img {
    width: 100%;
    height: 100%;
  }
}
.marker2 {
  width: 48px;
  height: 48px;
}
</style>
