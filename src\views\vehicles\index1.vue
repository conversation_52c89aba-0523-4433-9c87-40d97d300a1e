<style lang="less" scoped>
@import './index.less';
/*.common-main {*/
/*    background-image: url("../../assets/department/task_bg.png");*/
/*    background-size: 100% 100%;*/
/*}*/
.container {
  position: relative;
  overflow: hidden;
  width: 100%;
  padding: 0;
  .center-map {
    position: absolute;
    height: calc(1080px - 0.94rem);
    width: 100%;
    > div {
      width: 100%;
      height: 100%;
    }
  }
  .container-bg {
    position: absolute;
    pointer-events: none;
    z-index: 2;
    width: 100%;
    height: 100%;
    background-size: 100% 100%;
    background-image: url('../../assets/department/task_bg.png');
    .left-region {
      width: 100%;
      height: 100%;
      .left-region-top {
        height: 100%;
        width: 100%;
        position: relative;
        // margin-bottom: 0.35rem;
        // margin-top: 0.4rem;
        .conner-left-top {
          position: absolute;
          top: -2px;
          left: -2px;
          width: 0.1rem;
          height: 0.1rem;
          border-top: 2px solid #00eaff;
          border-left: 2px solid #00eaff;
        }
        .conner-right-top {
          position: absolute;
          top: -2px;
          right: -2px;
          width: 0.1rem;
          height: 0.1rem;
          border-top: 2px solid #00eaff;
          border-right: 2px solid #00eaff;
        }
        .conner-left-bot {
          position: absolute;
          left: -2px;
          bottom: -2px;
          width: 0.1rem;
          height: 0.1rem;
          border-bottom: 2px solid #00eaff;
          border-left: 2px solid #00eaff;
        }
        .conner-right-bot {
          position: absolute;
          right: -2px;
          bottom: -2px;
          width: 0.1rem;
          height: 0.1rem;
          border-right: 2px solid #00eaff;
          border-bottom: 2px solid #00eaff;
        }
        .center-map {
          width: 1920px;
          height: 1080px;
          position: relative;
          .type-car {
            margin-top: 0.1rem;
            position: absolute;
            pointer-events: auto;
            left: 0;
            top: 0;
            box-sizing: border-box;
            padding: 0.2rem 0.15rem 0.1rem 0.5rem;
            width: 2.5rem;
            .count {
              font-size: 0.24rem;
              font-weight: bold;
            }
            .line {
              font-size: 0.24rem;
            }
            > div {
              display: flex;
              align-items: center;
              justify-content: left;
              margin-bottom: 0.1rem;
              width: 2rem;
              > div {
                height: 0.5rem;
              }
              > div:nth-child(1) {
                width: 0.65rem;
                height: 0.65rem;
                display: flex;
                align-items: center;
                justify-content: center;
                background-image: url('../../assets/carbox.png');
                background-size: 100% 100%;
              }
              div.car-active {
                background-image: url('../../assets/car-selected.png');
              }
              .circle {
                align-self: center;
                // border-radius: 50%;
                margin-right: 0.24rem;
                img {
                  width: 0.3rem;
                  // height: 0.2rem;
                }
              }
              // .circle1 {
              //   background: rgba(4, 186, 25, 1);
              // }
              // .circle2 {
              //   background: rgba(230, 0, 18, 1);
              // }
              // .circle3 {
              //   background: rgba(255, 172, 40, 1);
              // }
              // .circle4 {
              //   border: 0.02rem solid rgba(196, 40, 229, 1);
              //   box-sizing: border-box;
              // }
            }
            .legend {
              font-size: 0.15rem;
              color: #19f1f9;
            }
          }
        }
        .img-top-right {
          position: absolute;
          background: url(../../assets/<EMAIL>);
          background-size: 100% 100%;
          background-repeat: no-repeat;
          width: 3rem;
          height: 0.08rem;
          top: -0.2rem;
          right: 0;
        }
        .img-top-left {
          position: absolute;
          background: url(../../assets/<EMAIL>);
          background-size: 100% 100%;
          background-repeat: no-repeat;
          width: 3rem;
          height: 0.08rem;
          top: -0.2rem;
          left: 0;
        }
        .img-bottom-right {
          position: absolute;
          background: url(../../assets/<EMAIL>);
          background-size: 100% 100%;
          background-repeat: no-repeat;
          width: 1.73rem;
          height: 0.08rem;
          bottom: -0.2rem;
          right: 0;
        }
        .img-bottom-left {
          position: absolute;
          background: url(../../assets/<EMAIL>);
          background-size: 100% 100%;
          background-repeat: no-repeat;
          width: 1.73rem;
          height: 0.08rem;
          bottom: -0.2rem;
          left: 0;
        }
        .img-left-top {
          position: absolute;
          background: url(../../assets/<EMAIL>);
          background-size: 100% 100%;
          background-repeat: no-repeat;
          width: 0.08rem;
          height: 0.83rem;
          top: 0;
          left: -0.2rem;
        }
        .img-left-bottom {
          position: absolute;
          background: url(../../assets/<EMAIL>);
          background-size: 100% 100%;
          background-repeat: no-repeat;
          width: 0.08rem;
          height: 0.83rem;
          bottom: 0;
          left: -0.2rem;
        }
        .img-right-top {
          position: absolute;
          background: url(../../assets/<EMAIL>);
          background-size: 100% 100%;
          background-repeat: no-repeat;
          width: 0.08rem;
          height: 0.83rem;
          top: 0;
          right: -0.2rem;
        }
        .img-right-bottom {
          position: absolute;
          background: url(../../assets/<EMAIL>);
          background-size: 100% 100%;
          background-repeat: no-repeat;
          width: 0.08rem;
          height: 0.83rem;
          bottom: 0;
          right: -0.2rem;
        }
      }
    }
  }
}
</style>
<style lang="less">
.box-title {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  .select-main {
    width: 1.2rem;
    display: flex;
    justify-content: space-between;
    .ant-select-selection {
      height: 0.3rem;
      width: 100%;
      border: none;
      outline: none;
      border-radius: unset;
      background: url(../../assets/<EMAIL>);
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
    .ant-select-selection-selected-value {
      color: rgba(0, 234, 255, 1);
    }
    .ant-select-focused .ant-select-selection,
    .ant-select-selection:focus,
    .ant-select-selection:active {
      border: none;
      border-right-width: 0 !important;
      outline: none;
      box-shadow: none;
    }
  }
}
.ant-select-dropdown-menu-item {
  font-size: 0.14rem !important;
}
.video-js.vjs-fluid {
  z-index: 999;
}
.right-part-vehicle {
  pointer-events: auto;
  position: absolute;
  top: 0;
  right: 0;
  height: calc(1080px - 1rem);
  width: 4.8rem;
  padding: 0.5rem 0.6rem 0.6rem 0.2rem;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  transition: all 1.5s ease-out;
  .task-title {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    padding-right: 0.15rem;
    span {
      &:nth-child(1) {
        font-size: 0.19rem;
        font-weight: 500;
        color: #ffffff;
        text-shadow: 0 0 5px blue, 0 0 5px blue;
      }
      &:nth-child(2) {
        color: #86c6ff;
        font-size: 0.14rem;
      }
    }
  }
  .task-vihicle {
    .task-vehicle-echarts {
      width: 100%;
      height: 2.1rem;
    }
  }
  .task-attendance {
    .no-task-list {
      width: 100%;
      height: 1.75rem;
      line-height: 2.45rem;
      text-align: center;
      font-size: 0.18rem;
      color: #ffffff;
    }
    .task-list {
      width: 3.87rem;
      .task-header {
        height: 0.35rem;
        display: flex;
        align-items: center;
        background: rgba(15, 36, 94, 0.8);
        span {
          display: inline-block;
          width: 25%;
          text-align: center;
          line-height: 0.35rem;
          color: #40deff;
          font-size: 0.14rem;
        }
      }
      .task-body {
        height: 1.4rem;
        .task-lists {
          &:nth-child(even) {
            background: rgba(15, 36, 94, 0.8);
          }
          span {
            &:nth-child(1) {
              background-image: url('../../assets/department/<EMAIL>');
              background-size: 0.22rem 0.22rem;
              background-repeat: no-repeat;
              background-position: center;
              color: #ffffff;
              font-size: 0.12rem;
            }
            display: inline-block;
            width: 25%;
            line-height: 0.35rem;
            font-size: 0.14rem;
            font-weight: 500;
            text-align: center;
            color: #97b6e4;
            overflow: hidden; //超出的文本隐藏
            text-overflow: ellipsis; //溢出用省略号显示
            white-space: nowrap; //溢出不换行
          }
        }
      }
    }
  }
}
.right-to-right {
  right: -4.8rem;
  opacity: 0.4;
}
.task-vihcle-live {
  .task-title {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    padding-right: 0.15rem;
    span {
      &:nth-child(1) {
        font-size: 0.19rem;
        font-weight: 500;
        color: #ffffff;
        text-shadow: 0 0 5px blue, 0 0 5px blue;
      }
      &:nth-child(2) {
        color: #86c6ff;
        font-size: 0.14rem;
      }
    }
  }
}
</style>
<template>
  <!-- 车辆管理 -->
  <section class="container">
    <section class="center-map">
      <!-- <keep-alive> -->
      <gao-de-map
        ref="map"
        :mapStyle="mapStyle"
        :mapZoom="13"
        :enterType="3"
        :locationRecordList="locationRecordList"
        :oldCarPoint="oldCarPoint"
        :newCarPoint="newCarPoint"
        :viewMode="'2D'"
        :mapMarker="mapMarker"
        @showTaskDetail="showTaskDetail"
        @percent="percent"
        :percentage="Percentage"
        :showPlayBack="showPlayBack"
        :refuseStationList="refuseStationList"
      ></gao-de-map>
      <!-- </keep-alive> -->
    </section>
    <div class="container-bg">
      <!-- 左侧部分 -->
      <section class="left-region">
        <section
          :class="{
            'left-region-top': true,
            'display-state-map': displayState
          }"
        >
          <div class="map-box">
            <section class="center-map">
              <section class="type-car" v-if="carTypeList.length > 0">
                <div
                  v-for="(item, index) in carTypeList"
                  :key="index"
                  @click="checkCarType(item.typeId, item.name)"
                >
                  <div
                    class="circle"
                    style="cursor: pointer"
                    :class="{ 'car-active': item.name == carTypeText }"
                  >
                    <img :src="item.icon" alt />
                  </div>
                  <div
                    :style="{
                      color: item.name == carTypeText ? '#19F1F9' : '#ffffff'
                    }"
                  >
                    <div>{{ item.name }}</div>
                    <div>
                      <span class="count">{{ item.online }}</span>
                      <span class="line">/</span>
                      {{ item.value || 0 }}辆
                    </div>
                  </div>
                </div>
                <div class="legend">注：作业中车辆 / 车辆总数</div>
              </section>
            </section>
          </div>
          <section class="quanpin" @click="fullScreen">
            <img src="@/assets/quanping.png" alt />
          </section>
        </section>
        <section class="work-car">
          <div class="work-type" @click="handleOnline(1)">
            <span>工作中</span>
            <span
              >{{
                JSON.stringify(currOnlineObj) === '{}'
                  ? 0
                  : currOnlineObj.online
              }}辆</span
            >
            <span class="circle"
              ><span :class="{ active: online === 1 ? 'active' : '' }"></span
            ></span>
          </div>
          <div class="work-type" @click="handleOnline(2)">
            <span>未出工</span>
            <span
              >{{
                JSON.stringify(currOnlineObj) === '{}'
                  ? 0
                  : currOnlineObj.value - currOnlineObj.online
              }}辆</span
            >
            <span class="circle"
              ><span :class="{ active: online === 2 ? 'active' : '' }"></span
            ></span>
          </div>
        </section>
      </section>
      <!-- 中间搜索框 -->
      <section class="enterprise-search">
        <div>
          <input
            type="text"
            placeholder="输入车牌号搜索"
            v-model="enterpriseSearch"
            @keyup.enter="enterpriseSearchBtn"
          />
          <a-icon
            type="close-circle"
            style="font-size: 0.2rem; cursor: pointer; margin-right: 0.15rem"
            v-if="enterpriseSearch != ''"
            @click="clearSearch"
          />
          <div v-if="carListName.length" class="fetch-input">
            <div
              v-for="(item, index) in carListName"
              :key="index"
              @click="handleClick(item)"
            >
              {{ item }}
            </div>
          </div>
        </div>
        <div>
          <img src="@/assets/search.png" alt @click="enterpriseSearchBtn" />
        </div>
      </section>
      <!-- 右侧部分 -->
      <section
        :class="{ 'right-part': true }"
        style="
          width: 4.1rem;
          justify-content: left;
          right: 0.8rem;
          position: absolute;
          margin-top: 0.1rem;
          padding: 0.2rem;
          transition: all 1s;
          top: 0;
          pointer-events: auto;
        "
        :style="{
          opacity: hasTaskDetail ? '1' : '0',
          pointerEvents: hasTaskDetail ? 'auto' : 'none'
        }"
      >
        <div
          class="common-title left-part-one"
          style="width: 3.85rem; position: relative"
        >
          <div
            style="
              width: 0.5rem;
              height: 0.5rem;
              position: absolute;
              top: 0.1rem;
              right: 0.2rem;
              cursor: pointer;
            "
            @click="closeCarTaskDetail"
          >
            <a-icon type="close-circle" style="font-size: 0.2rem" />
          </div>
          <div class="title">{{ `作业信息` }}</div>
          <div class="sub-title" style="margin-bottom: 0">
            <img src="@/assets/biaoti.png" alt class="title-img" />
          </div>
          <div style="width: 4rem; border: none">
            <div class="task">
              <div class="task-top">
                <div
                  style="
                    border-bottom: 1px solid rgba(255, 255, 255, 0.35);
                    padding-bottom: 0.05rem;
                    margin-bottom: 0.08rem;
                  "
                >
                  <div style="display: flex; align-items: center">
                    <img src="@/assets/zycl.png" style="width: 0.3rem" />
                    <div style="color: #00eaff; margin-left: 0.2rem">
                      作业车辆
                    </div>
                  </div>
                </div>
                <div>
                  <div>
                    <p>
                      <span class="task-top-name">车辆类型：</span>
                      <span class="task-top-text">
                        {{ carDetails.typeName ? carDetails.typeName : '' }}
                      </span>
                    </p>
                    <p>
                      <span class="task-top-name">车牌号码：</span>
                      <span class="task-top-text">
                        {{ carDetails.license ? carDetails.license : '' }}
                      </span>
                    </p>
                    <p>
                      <span class="task-top-name">所属公司/街道：</span>
                      <span class="task-top-text">
                        {{ carDetails.deptName ? carDetails.deptName : '' }}
                      </span>
                    </p>
                    <p>
                      <span class="task-top-name">当前车速：</span>
                      <span class="task-top-text">
                        {{ carDetails.speed + ' km/h' }}
                      </span>
                    </p>
                    <p>
                      <span class="task-top-name">更新时间：</span>
                      <span class="task-top-text">{{ carDetails.time }}</span>
                    </p>
                    <p style="display: flex; align-items: center">
                      <span class="task-top-name">车辆里程：</span>
                      <span class="mileage">
                        <div
                          v-if="!mileLoadingSuccess"
                          class="task-top-text"
                          style="margin-right: 0.1rem"
                        >
                          <a-icon type="loading" />获取数据中...
                        </div>
                        <div
                          v-else
                          v-for="(item, index) in mileCycle == '日'
                            ? carDetails.todayMileStringList
                            : carDetails.totalMileStringList"
                          :key="index + 'num'"
                          :class="index === 4 ? 'mile2' : 'mile'"
                        >
                          {{ item }}
                        </div>
                        <div>km</div>
                        <div
                          class="mile-cycle cycle-of-day"
                          :class="{ 'mile-cycle-selected': mileCycle == '日' }"
                          @click="mileCycle = '日'"
                        >
                          日
                        </div>
                        <div
                          class="mile-cycle"
                          :class="{ 'mile-cycle-selected': mileCycle == '总' }"
                          @click="mileCycle = '总'"
                        >
                          总
                        </div>
                      </span>
                    </p>
                  </div>
                </div>
                <div class="playback-box">
                  <img src="@/assets/<EMAIL>" alt />
                  <div class="playback">
                    <div
                      @click="
                        showPlayBack = true
                        displayState = true
                      "
                    >
                      <img src="@/assets/gjhf.png" alt />
                      <span>轨迹回放</span>
                    </div>
                  </div>
                </div>
              </div>
              <div
                v-if="showPlayBack"
                class="task-bottom playback-form"
                :class="showPlayBack ? 'show-playback' : ''"
              >
                <a-form-model
                  layout="inline"
                  :model="form"
                  ref="form"
                  style="display: flex; align-items: center; flex-wrap: wrap"
                >
                  <a-form-model-item class="form-item" prop="startTime">
                    <a-date-picker
                      placeholder="默认今日0点"
                      :disabled-date="disabledDate"
                      show-time
                      :allowClear="false"
                      v-model="form.startTime"
                      valueFormat="YYYY-MM-DD HH:mm"
                      format="YYYY-MM-DD HH:mm"
                    />
                  </a-form-model-item>
                  <span>至</span>
                  <a-form-model-item class="form-item" prop="endTime">
                    <a-date-picker
                      placeholder="默认当前时间"
                      v-model="form.endTime"
                      :allowClear="false"
                      show-time
                      :disabled-date="disabledDate2"
                      valueFormat="YYYY-MM-DD HH:mm"
                      format="YYYY-MM-DD HH:mm"
                    />
                  </a-form-model-item>
                  <div class="query-button">
                    <a-button
                      type="primary"
                      :loading="loading"
                      @click="getPlaybackTrajectory"
                      >{{ loading ? '数据获取中' : '查询' }}</a-button
                    >
                  </div>
                  <div
                    class="progress"
                    style="position: relative; width: 100%; height: 20px"
                  >
                    <progressBar
                      :percent="percentage"
                      :locationRecordList="locationRecordList"
                      style="position: absolute; left: 0"
                      @SetOpacityConfig="SetOpacityConfig"
                    />
                  </div>
                  <div class="multiple-speed">
                    <div
                      class="button"
                      :class="{ 'button-selected': disabledButton === 0.5 }"
                      @click="multipleSpeed(0.5)"
                    >
                      0.5x
                    </div>
                    <div
                      class="button"
                      :class="{ 'button-selected': disabledButton === 1 }"
                      @click="multipleSpeed(1)"
                    >
                      1x
                    </div>
                    <div
                      class="button"
                      :class="{ 'button-selected': disabledButton === 2 }"
                      @click="multipleSpeed(2)"
                    >
                      2x
                    </div>
                    <div
                      class="button"
                      :class="{ 'button-selected': disabledButton === 4 }"
                      @click="multipleSpeed(4)"
                    >
                      4x
                    </div>
                  </div>
                  <div class="play-ctr">
                    <a-button
                      type="primary"
                      :disabled="locationRecordList.length === 0"
                      @click="playbackControl"
                      >{{ buttonText }}</a-button
                    >
                    <a-button type="primary" @click="stopPlayback"
                      >结束</a-button
                    >
                  </div>
                </a-form-model>
              </div>
            </div>
          </div>
        </div>
        <section class="task-vihcle-live" style="margin-top: 0.2rem">
          <div class="task-title" style="padding-right: 0">
            <span @click="showMoreVideo" style="cursor: pointer"
              >任务车实况巡查》</span
            >
            <div class="box-title">
              <a-select
                v-show="hasVideo"
                v-model="carStreet"
                class="select-main"
                @change="changeStreet"
                style="width: 1.1rem"
              >
                <a-icon
                  slot="suffixIcon"
                  type="caret-down"
                  style="color: rgba(0, 234, 255, 1); width: 14px; height: 8px"
                />
                <a-select-option
                  :value="item.deptId"
                  v-for="item in deptStreetList"
                  :key="item.deptId"
                  >{{ item.deptName }}</a-select-option
                >
              </a-select>
              <a-select
                v-show="hasVideo"
                v-model="carId"
                @change="changeCar"
                class="select-main"
                style="margin-left: 0.06rem; width: 1rem"
              >
                <a-icon
                  slot="suffixIcon"
                  type="caret-down"
                  style="color: rgba(0, 234, 255, 1); width: 14px; height: 8px"
                />
                <a-select-option
                  :value="item.carId"
                  v-for="item in deptCarList"
                  :key="item.carId"
                  >{{ item.license }}</a-select-option
                >
              </a-select>
            </div>
          </div>
          <div class="sub-title" style="margin-bottom: 0.15rem">
            <img
              src="@/assets/biaoti.png"
              style="width: 100%"
              alt
              class="title-img"
            />
          </div>
          <div
            v-show="flvPlayer === null"
            style="
              background: rgba(8, 45, 120, 0.5);
              width: 3.85rem;
              height: 2.18rem;
              text-align: center;
              padding-top: 0.5rem;
              box-sizing: border-box;
            "
          >
            <img src="@/assets/noCamera.png" alt />
            <br />该摄像头不在线
          </div>
          <div
            v-show="liveTimeOut"
            style="
              background: rgba(8, 45, 120, 0.5);
              width: 410px;
              height: 205px;
              text-align: center;
              padding-top: 0.5rem;
              box-sizing: border-box;
            "
          >
            <img src="@/assets/noCamera.png" alt />
            <br />
            <br />观看已超时，请切换摄像头或刷新
          </div>
          <div
            v-show="flvPlayer !== null && !liveTimeOut"
            style="position: relative"
          >
            <video
              v-show="hasTaskDetail"
              width="400"
              id="videoElement"
              style="width: 3.85rem; height: 2.18rem; background: #000"
              controls="true"
            ></video>
            <div class="change-camera">
              <span
                :class="cameraDirection == 0 ? 'change-camera-selected' : ''"
                @click="changeCameraDirection(0)"
                >前</span
              >
              <img src="@/assets/<EMAIL>" width="2" height="25" />
              <span
                :class="cameraDirection == 1 ? 'change-camera-selected' : ''"
                @click="changeCameraDirection(1)"
                >后</span
              >
            </div>
          </div>
        </section>
      </section>
      <section
        class="right-part-vehicle"
        :class="{ 'right-to-right': hasTaskDetail ? 'right-to-right' : '' }"
      >
        <section class="task-vihicle">
          <div class="task-title">
            <span>任务车工况分析</span>
            <span>数据更新：{{ updateTime }}</span>
          </div>
          <div class="sub-title">
            <img
              src="@/assets/biaoti.png"
              style="width: 100%"
              alt
              class="title-img"
            />
          </div>
          <div class="task-vehicle-echarts">
            <vehicleCondition
              :id="'vehicleCondition'"
              :width="'4.3rem'"
              :height="'2.1rem'"
              :propData="taskData"
              :smooth="true"
            />
          </div>
        </section>
        <section class="task-attendance">
          <div class="task-title">
            <span>任务出勤统计</span>
            <div class="box-title">
              <a-select
                v-model="carType"
                class="select-main"
                @change="getTstAnalysis"
              >
                <a-icon
                  slot="suffixIcon"
                  type="caret-down"
                  style="color: rgba(0, 234, 255, 1); width: 14px; height: 8px"
                />
                <a-select-option
                  :value="item.typeId"
                  v-for="item in carTypeList"
                  :key="item.typeId"
                  >{{ item.name }}</a-select-option
                >
              </a-select>
              <a-select
                v-model="cycle"
                @change="getTstAnalysis"
                class="select-main"
                style="margin-left: 0.2rem; width: 0.8rem"
              >
                <a-icon
                  slot="suffixIcon"
                  type="caret-down"
                  style="color: rgba(0, 234, 255, 1); width: 14px; height: 8px"
                />
                <a-select-option
                  :value="item.id"
                  v-for="item in cycleList"
                  :key="item.id"
                  >{{ item.name }}</a-select-option
                >
              </a-select>
            </div>
          </div>
          <div class="sub-title" style="margin-bottom: 0.15rem">
            <img
              src="@/assets/biaoti.png"
              style="width: 100%"
              alt
              class="title-img"
            />
          </div>
          <div v-if="carNums.length" class="task-list">
            <div class="task-header">
              <span>排名</span>
              <span>所属街道</span>
              <span>出勤次数</span>
              <span>里程(KM)</span>
            </div>
            <swiper
              ref="swiper"
              :options="swiperOptionCarNums"
              class="task-body"
            >
              <swiper-slide
                v-for="(item, index) in carNums"
                :key="index"
                class="task-lists"
              >
                <span>{{ index + 1 }}</span>
                <span :title="item.deptName">{{ item.deptName }}</span>
                <span>{{ item.attendanceCount || 0 }}</span>
                <span>{{ item.mileage || 0 }}</span>
              </swiper-slide>
            </swiper>
          </div>
          <div v-else class="no-task-list">数据未更新</div>
        </section>
        <section class="task-vihcle-live">
          <div class="task-title">
            <span @click="showMoreVideo" style="cursor: pointer"
              >任务车实况巡查》</span
            >
            <div class="box-title">
              <a-select
                v-show="hasVideo"
                v-model="carStreet"
                class="select-main"
                @change="changeStreet"
              >
                <a-icon
                  slot="suffixIcon"
                  type="caret-down"
                  style="color: rgba(0, 234, 255, 1); width: 14px; height: 8px"
                />
                <a-select-option
                  :value="item.deptId"
                  v-for="item in deptStreetList"
                  :key="item.deptId"
                  >{{ item.deptName }}</a-select-option
                >
              </a-select>
              <a-select
                v-show="hasVideo"
                v-model="carId"
                @change="changeCar"
                class="select-main"
                style="margin-left: 0.06rem; width: 1.05rem"
              >
                <a-icon
                  slot="suffixIcon"
                  type="caret-down"
                  style="color: rgba(0, 234, 255, 1); width: 14px; height: 8px"
                />
                <a-select-option
                  :value="item.carId"
                  v-for="item in deptCarList"
                  :key="item.carId"
                  >{{ item.license }}</a-select-option
                >
              </a-select>
            </div>
          </div>
          <div class="sub-title" style="margin-bottom: 0.15rem">
            <img
              src="@/assets/biaoti.png"
              style="width: 100%"
              alt
              class="title-img"
            />
          </div>
          <div
            v-show="flvPlayer === null"
            style="
              background: rgba(8, 45, 120, 0.5);
              width: 3.85rem;
              height: 2.18rem;
              text-align: center;
              padding-top: 0.5rem;
              box-sizing: border-box;
            "
          >
            <img src="@/assets/noCamera.png" alt />
            <br />该摄像头不在线
          </div>
          <div
            v-show="liveTimeOut"
            style="
              background: rgba(8, 45, 120, 0.5);
              width: 410px;
              height: 205px;
              text-align: center;
              padding-top: 0.5rem;
              box-sizing: border-box;
            "
          >
            <img src="@/assets/noCamera.png" alt />
            <br />
            <br />观看已超时，请切换摄像头或刷新
          </div>
          <div
            v-show="flvPlayer !== null && !liveTimeOut"
            style="position: relative"
          >
            <video
              width="400"
              v-show="!hasTaskDetail"
              id="videoElement1"
              style="width: 3.85rem; height: 2.18rem; background: #000"
              controls="true"
            ></video>
            <div class="change-camera">
              <span
                :class="cameraDirection == 0 ? 'change-camera-selected' : ''"
                @click="changeCameraDirection(0)"
                >前</span
              >
              <img src="@/assets/<EMAIL>" width="2" height="25" />
              <span
                :class="cameraDirection == 1 ? 'change-camera-selected' : ''"
                @click="changeCameraDirection(1)"
                >后</span
              >
            </div>
          </div>
        </section>
      </section>
    </div>
  </section>
</template>

<script lang="ts">
interface InData {
  name: string
  dataList: string[]
}
/* eslint-disable @typescript-eslint/ban-ts-ignore */
//@ts-ignore
import { Component, Vue, Watch } from 'vue-property-decorator'
import flvjs from 'flv.js'
import GaoDeMap from '@/components/GaoDeMap/index.vue'
import RotateBarSolid from '@/components/Charts/RotateBarSolid.vue'
import { Swiper, SwiperSlide } from 'vue-awesome-swiper'
import PieChart from '@/components/Charts/PieChart.vue'
import laji from '@/assets/ljqyc.png'
import sashui from '@/assets/<EMAIL>'
import xisao from '@/assets/<EMAIL>'
import zhifa from '@/assets/<EMAIL>'
import wupao from '@/assets/<EMAIL>'
import canchu from '@/assets/<EMAIL>'
import carquanbu from '@/assets/carquanbu.png'
import { getCarName } from '@/api/vehicles.ts'
import progressBar from './progress.vue'
import {
  Table,
  Icon,
  Empty,
  Avatar,
  Select,
  DatePicker,
  TimePicker,
  Input,
  Button,
  message,
  FormModel
} from 'ant-design-vue'
import DashboardChart from '@/components/Charts/DashboardChart.vue'
import LineChartDashed from '@/components/Charts/LineChartDashed.vue'
import vehicleCondition from '@/components/Charts/vehicleCondition.vue'
import moment from 'moment'
import { getRefuseStation, keepAlive } from '@/api/vehicles'
import 'video.js/dist/video-js.css'
import 'videojs-contrib-hls'
import 'videojs-flash'
import { parse } from 'querystring'
import { socketUrl } from '@/utils/index'
interface TaskData {
  type: number
  showNumber: number
  number: number
  name: string
}
@Component({
  name: 'VehicleManage',
  components: {
    GaoDeMap,
    LineChartDashed,
    RotateBarSolid,
    Swiper,
    SwiperSlide,
    PieChart,
    ATable: Table,
    DashboardChart,
    AIcon: Icon,
    AEmpty: Empty,
    AAvatar: Avatar,
    ASelect: Select,
    ASelectOption: Select.Option,
    ADatePicker: DatePicker,
    ATimePicker: TimePicker,
    AInput: Input,
    AButton: Button,
    AFormModel: FormModel,
    AFormModelItem: FormModel.Item,
    vehicleCondition,
    progressBar
  }
})
export default class extends Vue {
  @Watch('form', { immediate: true, deep: true })
  private onFormChange(newValue: any, oldValue: any) {
    if (newValue) {
      if (
        moment(newValue.startTime).add(2, 'days') < moment(this.form.endTime)
      ) {
        newValue.endTime =
          moment(newValue.startTime).format('YYYY-MM-DD') + ' 23:59'
      }
    }
  }
  @Watch('enterpriseSearch', { immediate: true, deep: true })
  private onEnterpriseSearch(newValue: any, oldValue: any) {
    if (newValue && !this.isClick) {
      getCarName(newValue).then((res) => {
        this.carListName = res.data.data || []
        this.carListName = this.carListName.slice(0, 10)
      })
    } else {
      this.isClick = false
      this.carListName = []
    }
  }
  get swipers() {
    // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
    // @ts-ignore
    return this.$refs.swiper.$swiper
  }
  private isClick: any = false
  private handleClick(item: any) {
    this.isClick = true
    this.enterpriseSearch = item
    this.carListName = []
    this.enterpriseSearchBtn()
  }
  private carListName: any = []
  public cycle = 1
  private currOnlineObj: any = {}
  private updateTime: any = ''
  public carType = 'all'
  private cycleList = [
    { id: 1, name: '本日' },
    { id: 2, name: '本周' },
    { id: 3, name: '本月' }
  ]
  private cameraDirection = 0
  private mapStyle = 'amap://styles/0739a6632d849ae1b5e6f1c52f7d12c3'
  private tableType = 'day'
  private form = {
    startTime: moment(new Date()).format('YYYY-MM-DD') + ' 00:00',
    endTime: moment(new Date()).format('YYYY-MM-DD HH:mm'),
    animationTime: 30
  }
  // 获取车辆任务分析列表
  private getTstAnalysis() {
    console.log(321312312)
    this.socket.send(
      JSON.stringify({
        code: 9,
        typeId: this.carType === 'all' ? undefined : this.carType,
        cycle: this.cycle
      })
    )
  }
  // 鼠标移入，swiper停止自动滚动
  private stopAutoPlay(type: string) {
    // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
    // @ts-ignore
    this.swipers.autoplay.stop()
  }
  // 鼠标移出，swiper恢复自动滚动
  private startAutoPlay(type: string) {
    // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
    // @ts-ignore
    this.swipers.autoplay.start()
  }
  private disabledDate(current: any) {
    return current >= moment().endOf('day')
  }
  private disabledDate2(current: any) {
    return (
      current >= moment().endOf('day') ||
      current > moment(this.form.startTime).add(2, 'days') ||
      current < moment(this.form.startTime)
    )
  }
  private mileCycle = '日'
  private moment = moment
  private dateFormat = 'YYYY-MM-DD'
  private timeFormat = 'HH:mm'
  private swiperOption = {
    direction: 'vertical',
    slidesPerView: 4,
    slidesPerGroup: 1,
    loop: true,
    autoplay: {
      delay: 1500,
      disableOnInteraction: true
    }
  }
  private mapMarker: any = []
  private startPlayState: any = false
  private buttonText = '开始'
  private loading = false
  private enterpriseSearch = ''
  // 搜索车辆
  enterpriseSearchBtn() {
    const licenseReg =
      /^([京津晋冀蒙辽吉黑沪苏浙皖闽赣鲁豫鄂湘粤桂琼渝川贵云藏陕甘青宁新][ABCDEFGHJKLMNPQRSTUVWXY][1-9DF][1-9ABCDEFGHJKLMNPQRSTUVWXYZ]\d{3}[1-9DF]|[京津晋冀蒙辽吉黑沪苏浙皖闽赣鲁豫鄂湘粤桂琼渝川贵云藏陕甘青宁新][ABCDEFGHJKLMNPQRSTUVWXY][\dABCDEFGHJKLNMxPQRSTUVWXYZ]{5})$/
    if (!licenseReg.test(this.enterpriseSearch)) {
      message.error('请输入正确的车牌号')
      return
    }
    for (const item of this.mapMarker) {
      console.log(item.license.indexOf(this.enterpriseSearch))
      if (item.license.indexOf(this.enterpriseSearch) != -1) {
        //@ts-ignore
        this.$refs.map.showCarDetail(item)
        this.carTypeText = '所有车辆'
        return
      }
    }
    message.error('未查询到车辆')
  }
  // 清除搜索
  clearSearch() {
    this.enterpriseSearch = ''
    this.checkCarType('all', '所有车辆')
    this.hasTaskDetail = false
    /* eslint-disable @typescript-eslint/ban-ts-ignore */
    //@ts-ignore
    if (this.$refs.map.carItem) {
      /* eslint-disable @typescript-eslint/ban-ts-ignore */
      //@ts-ignore
      this.$refs.map.clearCarSelected()
    }
  }
  // 点击偏离电子围栏记录
  showAlarmPlayBack(data: any) {
    for (const item of this.mapMarker) {
      if (item.license.indexOf(data.license) != -1) {
        //@ts-ignore
        this.$refs.map.showCarDetail(item)
        // this.showTaskDetail(item);
        const alarmTimeStr = +new Date().getFullYear() + '/' + data.dateFormat
        /* eslint-disable @typescript-eslint/ban-ts-ignore */
        //@ts-ignore
        this.form.startTime = moment(alarmTimeStr)
          .subtract(3, 'hours')
          .format('YYYY-MM-DD HH:mm')
        /* eslint-disable @typescript-eslint/ban-ts-ignore */
        //@ts-ignore
        this.form.endTime = moment(alarmTimeStr).format('YYYY-MM-DD HH:mm')
        this.disabledButton = 0.5
        return
      }
    }
    message.error('未查询到车辆')
  }
  // 查询回放轨迹
  private getPlaybackTrajectory(): void {
    const timeDifference =
      +new Date(this.form.endTime) - +new Date(this.form.startTime)
    if (timeDifference <= 1800000) {
      // 结束时间比开始时间少30分钟
      message.error('回放结束时间应大于开始时间30分钟以上')
      return
    }
    this.loading = true
    this.startPlayState = true
    console.log('send,send')
    this.socket.send(
      JSON.stringify({
        code: 7,
        license: this.license,
        startTime: this.form.startTime,
        endTime: this.form.endTime
      })
    )
  }
  // 回放状态控制
  playbackControl() {
    if (this.buttonText === '开始') {
      // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
      // @ts-ignore
      this.$refs.map.startPlayback()
      this.buttonText = '暂停'
    } else if (this.buttonText === '暂停') {
      // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
      // @ts-ignore
      this.$refs.map.suspendPlayback()
      this.buttonText = '继续'
    } else if (this.buttonText === '继续') {
      // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
      // @ts-ignore
      this.$refs.map.resumePlayback()
      this.buttonText = '暂停'
    }
  }
  private disabledButton = 1
  // 倍速播放
  multipleSpeed(multiple: number) {
    if (this.locationRecordList.length === 0 || this.buttonText == '开始') {
      return
    }
    // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
    // @ts-ignore
    this.$refs.map.multipleSpeed(multiple)
    this.disabledButton = multiple
  }
  // 停止轨迹回放
  private stopPlayback(): void {
    this.buttonText = '开始'
    this.percentage = 0
    this.startPlayState = false
    this.loading = false
    this.showPlayBack = false
    this.displayState = false
    // @ts-ignore
    this.$refs.map.stopPlayback()
    this.disabledButton = 1
  }
  private oldCarPoint = {}
  private showPlayBack = false
  private newCarPoint = {}
  private locationRecordList = []
  // 默认选中摄像头
  private defaultCameraValue = ''
  private hasTaskDetail = false
  // 部门车次排行
  private swiperOptionCarNums = {
    direction: 'vertical',
    slidesPerView: 4,
    slidesPerGroup: 1,
    loop: true,
    autoplay: {
      delay: 1500,
      disableOnInteraction: false
    }
  }
  private taskData = {
    dataList: [],
    dataList1: [],
    // dataList2: [],
    bottomList: []
  }
  // 部门车次排行
  private carNums = []
  private carTypeData: any = {
    name: '',
    dataList: [],
    colorList: [
      '#56CCFF',
      '#6875F5',
      '#67FF9A',
      '#00CCCC',
      '#CE91F9',
      '#BCFDF7'
    ]
  }
  private colorList: Array<string> = [
    '#56CCFF',
    '#6875F5',
    '#67FF9A',
    '#00CCCC',
    '#CE91F9',
    '#BCFDF7'
  ]
  private monitor = []
  // pricls
  private license = ''
  private task = {}
  private hasVideo = true
  private displayState = false
  private fullScreen() {
    this.displayState = !this.displayState
  }
  private socket: any = null
  private videoCameraSerial = ''
  async mounted() {
    this.getRefuseStation()
    this.connect()
  }
  beforeDestroy() {
    this.socket.close()
    clearInterval(this.requestTime)
    if (this.flvPlayer) {
      this.flvPlayer.pause()
      this.flvPlayer.unload()
      this.flvPlayer.detachMediaElement()
      this.flvPlayer.destroy()
      this.flvPlayer = null
    }
  }
  private carList: any[] = []
  private carDetails: any = {
    totalMileStringList: [],
    todayMileStringList: []
  }
  private percentage: any = 0
  private percent(data: any) {
    this.percentage = data
    if (this.percentage == 100) {
      this.buttonText = '开始'
    }
  }
  private Percentage: any = 0
  private SetOpacityConfig(data: any) {
    this.Percentage = data
    console.log(this.Percentage, 'Percentage')
  }
  private showTaskDetail(data: any) {
    // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
    // @ts-ignore
    this.carList.find((item: any, index: number) => {
      if (data.license == item.license) {
        console.log(item, 99999999)
        this.carDetails = item
      }
    })
    // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
    // @ts-ignore
    this.carDetails.time = moment().format('YYYY-MM-DD HH:mm')
    this.license = data.license
    this.carId = data.carId
    // 获取车辆直播
    this.getCarCamera()
    let needBreak = false
    for (const i in this.deptStreetList) {
      if (needBreak) {
        break
      }
      for (const j in this.deptStreetList[i].carList) {
        if (this.deptStreetList[i].carList[j].license === data.license) {
          this.carStreet = this.deptStreetList[i].deptId
          this.deptCarList = this.deptStreetList[i].carList
          this.carId = this.deptStreetList[i].carList[j].carId
          console.log(this.carStreet, this.deptCarList, this.carId, data)
          this.hasVideo = true
          needBreak = true
          break
        } else {
          this.hasVideo = false
        }
      }
    }
    this.socket.send(
      JSON.stringify({
        code: 8,
        license: data.license,
        type: 1,
        mileageDate:
          new Date().getFullYear() +
          '-' +
          (new Date().getMonth() + 1 < 10
            ? '0' + (new Date().getMonth() + 1)
            : new Date().getMonth() + 1) +
          '-' +
          (new Date().getDate() < 10
            ? '0' + new Date().getDate()
            : new Date().getDate())
      })
    )
    this.hasTaskDetail = true
    // 获取任务
    this.socket.send(JSON.stringify({ code: 5, license: data.license }))
  }
  // 切换摄像头前后方向
  changeCameraDirection(direction: number) {
    console.log(this.cameraSerial)
    if (this.cameraSerial[direction]) {
      this.liveTimeOut = false
      this.count = 0
      this.cameraDirection = direction
      this.socket.send(
        JSON.stringify({
          code: 4,
          // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
          // @ts-ignore
          cameraSerial: this.cameraSerial[direction]
        })
      )
      this.videoCameraSerial = this.cameraSerial[direction]
    } else {
      message.error('暂无该方向摄像头')
    }
  }

  // 建立连接
  private connect() {
    // this.socket = new WebSocket("ws://*************:22000/ws");
    // this.socket = new WebSocket("ws://ep.vankeytech.com:8835/ws");
    // this.socket = new WebSocket("ws://www.jinnq.com:8834/ws");
    this.socket = new WebSocket(socketUrl())
    // 监听socket连接
    this.socket.onopen = this.open
    // 监听socket错误信息
    this.socket.onerror = this.error
    // 监听socket消息
    this.socket.onmessage = this.getMessage
    window.onbeforeunload = () => {
            this.socket.onopen = () => {}
            this.socket.onerror = () => {}
            this.socket.onmessage = () => {}
            this.socket.close();
        };
  }
  private open() {
    console.log('socket连接成功')
    this.send()
  }
  private error() {
    console.error('系统连接错误')
  }
  private carMarker: any = []
  private carTypeList: any = []
  private flvPlayer: any = null
  private cameraSerial: any = []
  private requestTime: any = ''
  private liveTimeOut = false
  private mileLoadingSuccess = false
  private count = 0
  // 获得数据
  private getMessage(msg: any) {
    const res: any = JSON.parse(msg.data)
    if (res.code == -2) {
      const mapMarker: any = []
      this.carList = JSON.parse(msg.data).carList
      for (const item of JSON.parse(msg.data).carList) {
        if (item.typeId) {
          mapMarker.push({
            type: item.typeId,
            longitude: item.gps.gcLng,
            latitude: item.gps.gcLat,
            license: item.license,
            carId: item.carId,
            deptId: item.deptId,
            online: item.online
          })
        }
      }
      // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
      // @ts-ignore
      this.mapMarker = mapMarker
      this.carMarker = mapMarker
    }
    if (res.code == -4) {
      this.liveTimeOut = false
      this.count = 0
      if (this.flvPlayer) {
        this.flvPlayer.pause()
        this.flvPlayer.unload()
        this.flvPlayer.detachMediaElement()
        this.flvPlayer.destroy()
        this.flvPlayer = null
      }
      if (!res.liveAddress.online) {
        return
      }
      if (flvjs.isSupported()) {
        const videoElement =  this.hasTaskDetail ? document.getElementById('videoElement') : document.getElementById('videoElement1')
        this.flvPlayer = flvjs.createPlayer({
          type: 'flv',
          isLive: true,
          hasVideo: true,
          // hasAudio: true,
          url: res.liveAddress.flv
        })
        this.flvPlayer.attachMediaElement(videoElement)
        this.flvPlayer.load()
        this.flvPlayer.play()
        this.flvPlayer.on('error', (err: any) => {
          console.log('err', err)
          message.error('播放失败请重试或切换其他视频')
          this.flvPlayer.pause()
          this.flvPlayer.unload()
          this.flvPlayer.detachMediaElement()
          this.flvPlayer.destroy()
          this.flvPlayer = null
        })
        // this.flvPlayer.on(flvjs.ErrorDetails.NETWORK_EXCEPTION, (err: any) => {
        //   console.log("err", err);
        //   message.error("超市");
        //   this.flvPlayer.pause();
        //   this.flvPlayer.unload();
        //   this.flvPlayer.detachMediaElement();
        //   this.flvPlayer.destroy();
        //   this.flvPlayer = null;
        // });
      }
      clearInterval(this.requestTime)
      this.requestTime = setInterval(() => {
        // this.count++;
        // if (this.count > 8) {
        //   clearInterval(this.requestTime);
        //   this.liveTimeOut = true;
        //   this.flvPlayer.pause();
        //   this.flvPlayer.unload();
        //   this.flvPlayer.detachMediaElement();
        //   this.flvPlayer.destroy();
        //   return;
        // }
        // console.log(this.count, 66666666);
        keepAlive(this.videoCameraSerial)
      }, 10 * 1000)
    }
    if (res.code == 3) {
      // 车辆移动
      for (const i in this.mapMarker) {
        const marker = this.mapMarker[i]
        // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
        // @ts-ignore
        if (marker.carId == res.car.carId && marker.type) {
          // @ts-ignore
          this.oldCarPoint = marker
          this.newCarPoint = {
            type: res.car.typeId,
            longitude: res.car.gps.gcLng,
            latitude: res.car.gps.gcLat,
            license: res.car.license,
            carId: res.car.carId,
            deptId: res.car.deptId
          }
        }
      }
    }
    if (res.code == -5) {
      // 车辆任务
      if (res.task) {
        this.task = res.task
      }
    }
    if (res.code == -6) {
      // 车辆类型
      this.carTypeData.dataList = []
      let value: any = 0
      let online: any = 0
      res.carTypeList.forEach((element: any) => {
        let icon: any = ''
        if (element.typeName == '垃圾清运车') {
          icon = laji
        } else if (element.typeName == '雾炮车') {
          icon = wupao
        } else if (element.typeName == '餐厨清运车') {
          icon = canchu
        } else if (element.typeName == '洒水车') {
          icon = sashui
        } else if (element.typeName == '清扫车') {
          icon = xisao
        } else if (element.typeName == '执法车') {
          icon = zhifa
        }
        this.carTypeData.dataList.push({
          value: element.count,
          name: element.typeName,
          online: element.online,
          icon,
          typeId: element.typeId,
          hasTaskCount: element.online
        })
        this.carTypeList.push({
          value: element.count,
          name: element.typeName,
          online: element.online,
          icon,
          typeId: element.typeId
        })
        value += element.count
        online += element.online
      })
      this.taskData = {
        dataList: this.carTypeData.dataList.map(
          (item: any) => item.hasTaskCount
        ),
        dataList1: this.carTypeData.dataList.map((item: any) => item.value),
        // dataList2: this.carTypeData.dataList.map((item:any) => item.value - item.hasTaskCount),
        bottomList: this.carTypeData.dataList.map((item: any) =>
          item.name.substr(0, item.name.length - 1)
        )
      }
      console.log(this.taskData, 'taskData')
      this.carTypeList.unshift({
        value,
        name: '所有车辆',
        online,
        icon: carquanbu,
        typeId: 'all'
      })
      this.updateTime = res.updateTime.replace(/T/g, ' ').substr(5, 11)
      this.currOnlineObj = this.carTypeList[0]
    }
    if (res.code == -7) {
      if (res.locationRecordList.length !== 0) {
        this.locationRecordList = res.locationRecordList
        this.buttonText = '开始'
        this.loading = false
      } else {
        this.loading = false
        message.warning('该车辆该时段暂无轨迹信息')
      }
    }
    if (res.code == -8) {
      this.mileLoadingSuccess = false
      // 里程处理
      const totalMileStringList = res.countMileage.toString().split('')
      const todayMileStringList = res.mileage.toString().split('')
      const numberOfLoop1 = 5 - totalMileStringList.length
      const numberOfLoop2 = 5 - todayMileStringList.length
      for (let i = 0; i < numberOfLoop1; i++) {
        totalMileStringList.unshift('0')
      }
      for (let i = 0; i < numberOfLoop2; i++) {
        todayMileStringList.unshift('0')
      }
      this.carDetails.totalMileStringList = totalMileStringList
      this.carDetails.todayMileStringList = todayMileStringList
      this.mileLoadingSuccess = true
    }
    if (res.code === -9) {
      // 任务统计
      this.carNums = res.deptCarMileageList
    }
    if (res.code === -10) {
      // 电子围栏报警列表
      this.monitor = res.carAlarmList
    }
    if (res.code === 10 && this.monitor.length > 0) {
      // 电子围栏新消息
      // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
      // @ts-ignore
      this.monitor.unshift(res.alarm)
      this.monitor.pop()
    }
    if (res.code === -11) {
      // 车辆监控列表
      this.deptStreetList = res.cameraMap.carList
      console.log(this.deptStreetList, 'this.deptStreetList')
      this.cameraList = res.cameraMap.cameraList
      for (const i in this.deptStreetList) {
        for (const j in this.deptStreetList[i].carList) {
          // if (this.deptStreetList[i].carList[j].license == '川AY3222') {
          this.carStreet = this.deptStreetList[i].deptId
          this.deptCarList = this.deptStreetList[i].carList
          this.carId = this.deptCarList[j].carId
          this.cameraSerial.push(this.deptStreetList[0].cameraSerial)
          // }
        }
      }
      for (const item of this.cameraList) {
        console.log(item.carId, this.carId, 66666)
        if (item.carId === this.carId && item.positionType === 0) {
          this.cameraSerial[0] = item.cameraSerial
        }
        if (item.carId === this.carId && item.positionType === 1) {
          this.cameraSerial[1] = item.cameraSerial
        }
      }
      console.log(this.cameraSerial)
      this.socket.send(
        JSON.stringify({
          code: 4,
          // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
          // @ts-ignore
          cameraSerial: this.cameraSerial[this.cameraDirection]
        })
      )
      this.videoCameraSerial = this.cameraSerial[this.cameraDirection]
    }
  }
  private deptStreetList: any = []
  private deptCarList: any = []
  private cameraList: any = []
  private carId: any = ''
  private carStreet: any = ''

  // 切换车辆视频的街道
  private changeStreet() {
    this.deptCarList = this.deptStreetList.filter((item: any) => {
      return item.deptId === this.carStreet
      // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
      // @ts-ignore
    })[0].carList
    this.carId = this.deptCarList[0].carId
    this.changeCar()
  }
  // 连选切换车辆
  changeCar() {
    this.liveTimeOut = false
    this.count = 0
    for (const item of this.mapMarker) {
      if (item.carId === this.carId) {
        //@ts-ignore
        this.$refs.map.showCarDetail(item)
        console.log(item)
        this.carTypeText = '所有车辆'
      }
    }
  }
  // 获取车辆视频地址
  private getCarCamera() {
    this.cameraSerial = []
    for (const item of this.cameraList) {
      if (item.carId === this.carId && item.positionType === 0) {
        this.cameraSerial[0] = item.cameraSerial
      }
      if (item.carId === this.carId && item.positionType === 1) {
        this.cameraSerial[1] = item.cameraSerial
      }
    }
    if (this.cameraSerial.length > 0) {
      this.socket.send(
        JSON.stringify({
          code: 4,
          cameraSerial: this.cameraSerial[this.cameraDirection]
        })
      )
      this.videoCameraSerial = this.cameraSerial[this.cameraDirection]
    } else {
      if (this.flvPlayer) {
        this.flvPlayer.pause()
        this.flvPlayer.unload()
        this.flvPlayer.detachMediaElement()
        this.flvPlayer.destroy()
        this.flvPlayer = null
      }
    }
  }
  private send() {
    this.socket.send(JSON.stringify({ code: 1, token: 'hello1234' }))
    this.socket.send(JSON.stringify({ code: 2 }))
    this.socket.send(JSON.stringify({ code: 6 }))
    this.socket.send(JSON.stringify({ code: 10 }))
    this.socket.send(JSON.stringify({ code: 11 }))
    this.socket.send(
      JSON.stringify({
        code: 9,
        typeId: this.carType === 'all' ? undefined : this.carType,
        cycle: this.cycle
      })
    )
    // this.socket.send(JSON.stringify({ code: 5, license: "川A200Z3" }));
  }
  private close() {
    console.log('socket已经关闭')
  }
  // 关闭车辆详情
  private closeCarTaskDetail() {
    this.hasVideo = false
    this.buttonText = '开始'
    // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
    // @ts-ignore
    this.$refs.map.stopPlayback()
    this.disabledButton = 1
    this.startPlayState = false
    this.hasTaskDetail = false
    this.showPlayBack = false
    this.displayState = false
    console.log(this.hasVideo, 66666666666)
    if (!this.hasVideo) {
      this.socket.send(JSON.stringify({ code: 11 }))
      this.carStreet = this.deptStreetList[0].deptId
      this.deptCarList = this.deptStreetList.filter((item: any) => {
        return item.deptId === this.carStreet
        // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
        // @ts-ignore
      })[0].carList
      this.carId = this.deptCarList[0].carId
      this.liveTimeOut = false
      this.count = 0
      this.hasVideo = true
      this.getCarCamera()
    }
    /* eslint-disable @typescript-eslint/ban-ts-ignore */
    //@ts-ignore
    if (this.startPlayState) {
      this.stopPlayback()
    }
    /* eslint-disable @typescript-eslint/ban-ts-ignore */
    //@ts-ignore
    if (this.$refs.map.carItem) {
      /* eslint-disable @typescript-eslint/ban-ts-ignore */
      //@ts-ignore
      this.$refs.map.clearCarSelected()
    }
    /* eslint-disable @typescript-eslint/ban-ts-ignore */
    //@ts-ignore
    const map = this.$refs.map.maps
    /* eslint-disable @typescript-eslint/ban-ts-ignore */
    //@ts-ignore
    if (this.$refs.map.Polyline) {
      /* eslint-disable @typescript-eslint/ban-ts-ignore */
      //@ts-ignore
      map.remove(this.$refs.map.Polyline)
    }
    /* eslint-disable @typescript-eslint/ban-ts-ignore */
    //@ts-ignore
    if (this.$refs.map.startPoint) {
      /* eslint-disable @typescript-eslint/ban-ts-ignore */
      //@ts-ignore
      map.remove(this.$refs.map.startPoint)
    }
    /* eslint-disable @typescript-eslint/ban-ts-ignore */
    //@ts-ignore
    if (this.$refs.map.endPoint) {
      /* eslint-disable @typescript-eslint/ban-ts-ignore */
      //@ts-ignore
      map.remove(this.$refs.map.endPoint)
    }
  }
  // 查看更多视频
  showMoreVideo() {
    this.$router.push('/vehiclesVideo')
  }
  private refuseStationList: any[] = []
  // 获取垃圾站信息
  private getRefuseStation() {
    getRefuseStation().then((res: any) => {
      this.refuseStationList = res.data.data
    })
  }
  private carTypeText: any = '所有车辆'
  private checkCarType(typeId: any, name: any) {
    this.stopPlayback()
    this.enterpriseSearch = ''
    this.online = 0
    this.mapMarker = this.carMarker
    this.carTypeText = name
    this.currOnlineObj = this.carTypeList.find(
      (item: any) => item.name === name
    )
    if (typeId == 'all') {
      this.currOnlineObj
      return
    }
    const mapMarker: any = []
    for (const item of this.mapMarker) {
      if (item.type == typeId) {
        mapMarker.push(item)
      }
    }
    this.mapMarker = mapMarker
  }
  private online: any = 0
  private handleOnline(params: number) {
    this.stopPlayback()
    let mapMarker =
      this.carTypeText === '所有车辆'
        ? this.carMarker
        : this.carMarker.filter(
            (item: any) => item.type === this.currOnlineObj.typeId
          )
    console.log(this.currOnlineObj, mapMarker, 'mapMarker')
    switch (params) {
      case 1:
        this.online = this.online === 1 ? 0 : 1
        mapMarker = this.mapMarker = this.online
          ? mapMarker.filter((item: any) => item.online == 1)
          : mapMarker
        break
      case 2:
        this.online = this.online === 2 ? 0 : 2
        this.mapMarker =
          this.online === 2
            ? mapMarker.filter((item: any) => item.online == 0)
            : mapMarker
        break
    }
  }
  private handleChange() {
    console.log(this.enterpriseSearch, 'enterpriseSearch')
  }
}
</script>

