<template>
  <div class="taskFeedbackContent">
    <div
      class="back-text"
      :class="{ 'show-text': showText }"
    >
      <!-- <span :class="content.length > 16?'til':''">回复内容： </span><div :class="{'oneline': !showText}">{{ content }}</div>
      <div v-if="content.length > 30" class="more-text" @click="showMoreText">{{!showText?'更多':'收起'}}</div>  -->
      <div class="title">回复内容：</div>
      <div class="content">
        <span v-if="content && content.length < 30">{{content}}</span>
        <span v-else>
          <span v-if="!showText">{{content && content.substring(0, 27)}}...</span>
          <span v-else>{{ content }}</span>
          <span class="more-text" @click="showMoreText">{{ !showText ? '更多' : '收起' }}</span>
        </span>
      </div>
    </div>
    <div class="img-box">
      <div
        class="img"
        :class="imgarr.length === 1?'one_pic':''"
        v-for="(item, index) in imgarr"
        :key="index + 'taskimg'"
        :style="{
          display: imgFlag ? '' : index > 1 ? 'none' : '',
        }"
      >
        <div
          class="more-image"
          v-if="
            index == 1 && !imgFlag && imgarr.length > 2
          "
          >+{{ imgarr.length - 2 }}</div
        >
        <ComImage
          style="width: 120px; height: 120px"
          :src="item" 
          fit="cover"
          :preview-src-list="imgarr">
        </ComImage>
      </div>
    </div>
  </div>
</template>

<script>
import ComImage from '@/components/image/src/main.vue'
  export default {
    components: {
      ComImage
    },
    props: {
      content:{
        type: String,
        default: ''
      },
      imgarr: {
        type: Array,
        default: () => { return [] }
      }
    },
    created() {
      console.log(this.imgarr)
    },
    data() {
      return {
        // content: '',
        // imgarr: [],
        imgFlag: false,
        showText: false
      }
    },
    methods:{
      moreImageClick() {
        imgFlag = true
      },
      showMoreText() {
        this.showText = !this.showText
      }
    }
  }
</script>

<style lang="less" scoped>
  .taskFeedbackContent {
    padding: 15px;
    padding-left: 20px;
    background-image: linear-gradient(
      to right,
      rgba(10, 66, 74, 0.5),
      rgba(10, 66, 74, 0)
    );
    .back-text {
      display: flex;
      align-items: flex-start;
      margin-bottom: 15px;
      font-size: 14px;
      color: #d6f4ff;
      // overflow: hidden;
      // text-overflow: ellipsis;
      // -webkit-box-orient: vertical;
      // -webkit-line-clamp: 3;
      .title {
        width: 70px;
      }
      .content {
        flex: 1;
      }
      cursor: pointer;
      // .til{
      //   margin-top: -22px;
      // }
      // .oneline {
      //   width: 13em;
      //   display: -webkit-box;
      //   -webkit-line-clamp: 2;     /*多少行数之后显示为省略...*/
      //   word-wrap: break-word;
      //   word-break: break-all;
      //   overflow: hidden;
      //   text-overflow: ellipsis;
      //   -webkit-box-orient: vertical;
      // }

      .more-text {
        font-size: 14px;
        color: #00aaff;
        margin-bottom: -24px;
      }
    }
    // .show-text {
    //   display: block;
    // }
    .img-box {
      display: flex;
      flex-wrap: wrap;
      overflow-x: scroll;
      margin-top: 24px;
      // height:80px;
      &::-webkit-scrollbar {
        display: none;
      }
      .img {
        width: 120px;
        height: 120px;
        overflow: hidden;
        // max-width: 130px;
        // max-height: 80px;
        margin-right: 10px;
        margin-bottom: 10px;
        position: relative;
        .more-image {
          position: absolute;
          width: 100%;
          height: 100%;
          background-color: rgba(0, 0, 0, 0.5);
          display: flex;
          justify-content: center;
          align-items: center;
          color: #fff;
          font-size: 42px;
          z-index: 2;
          cursor: pointer;
          pointer-events: none;
        }
      }
      img {
        width: 100%;
        height: 100%;
      }
      .one_pic{
        width: 130px;
        height: 86px;
      }
    }
  }
  /deep/ .ant-modal-body {
    padding: 0 !important;
    background: transparent !important;
  }
  video {
    width: 120px;
    height: 120px;
  }
</style>
<style scoped>
/* For demo */
.ant-carousel /deep/ .slick-slide {
  text-align: center;
  background: #364d79;
  overflow: hidden;
}

.ant-carousel /deep/ .custom-slick-arrow {
  width: 25px;
  height: 25px;
  font-size: 25px;
  color: #fff;
  background-color: rgba(31, 45, 61, 0.11);
  opacity: 0.3;
}
.ant-carousel /deep/ .custom-slick-arrow:before {
  display: none;
}
.ant-carousel /deep/ .custom-slick-arrow:hover {
  opacity: 0.5;
}

.ant-carousel /deep/ .slick-slide h3 {
  color: #fff;
}
</style>
<style lang="less">
.imgListModal {
  height: 100%;
}
 .imgListModal .ant-modal-content {
  background-color: transparent !important;
}
</style>