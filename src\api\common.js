import request from '@/utils/request'
/**
 * 获取沙河下所有站点
 * @returns promise
 */
export function getWaterStation() {
  return request({
    url: '/water/station/shaheStation',
    method: 'GET'
  })
}

/**
 * 文件上传接口
 * @param {FormData} data 文件数据 {
 * file: 文件数据
 * }
 * @param {Function} onProgress 进行进度监控的方法
 * @returns request
 */
export function uploadFile(data, onProgress) {
  return request({
    url: '/task/file/upload',
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    onUploadProgress: (progress) => {
      try {
        if (onProgress) {
          onProgress({ percent: (progress.loaded * 100) / progress.total })
        }
      } catch (error) {
        console.log('获取进度失败', error)
      }
    },
    timeout: 5 * 60 * 1000,
    data
  })
}

/**
 * 获取水源地污染类型
 */
export const getWaterPolluteType = [
  { value: 1, label: '企事业单位', icon: 'water-qsydw' },
  { value: 2, label: '加油站', icon: 'water-jyz' },
  { value: 3, label: '餐饮油烟', icon: 'water-cyyy' },
  { value: 4, label: '汽修企业', icon: 'water-qxhy' },
  { value: 5, label: '印刷企业', icon: 'water-yshy' },
  { value: 6, label: '重污企业', icon: 'water-zwqy' },
  { value: 7, label: '在建工地', icon: 'water-zjgd' }
]

export default {}
