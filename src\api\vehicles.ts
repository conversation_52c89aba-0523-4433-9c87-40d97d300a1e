import request from "@/utils/request";
import { AxiosPromise } from "axios";

/**
 * @method functionName
 * @param {type} data 获取垃圾站信息
 * @description
 */
export function getRefuseStation(): AxiosPromise<any> {
  return request({
    url: "/water/refuse-station/list",
    method: "get"
  });
}

/**
 * @method functionName
 * @param {type} data 获取车辆摄像头列表
 * @description
 */
export function getCameraList(carId?:any): AxiosPromise<any> {
  return request({
    url: "/car/camera/list",
    method: "get",
    params:{
      carId
    }
  });
}

/**
 * @method functionName
 * @param {type} data 获取摄像头播放地址
 * @description
 */
export function getCameraSerial(cameraSerial:any): AxiosPromise<any> {
  return request({
    url: `/car/camera/live/${cameraSerial}`,
    method: "get"
  });
}

/**
 * @method functionName
 * @param {type} data 获取摄像头播放地址
 * @description
 */
export function keepAlive(cameraSerial:any): AxiosPromise<any> {
  return request({
    url: `/car/camera/keepAlive/${cameraSerial}`,
    method: "get"
  });
}

export function getStreetList(): AxiosPromise<any> {
  return request({
    url: "/web/iot/car/getDeptList",
    method: "get",
  });
}
/**
 * @method getCarName
 * @description 获取车牌列表
 * @param {keyWord} 关键词
 */
export function getCarName(keyWord:any): AxiosPromise<any> {
  return request({
    url: 'web/iot/car/carBrand/index',
    method: 'get',
    params: {
      keyWord: keyWord
    }
  })
}
