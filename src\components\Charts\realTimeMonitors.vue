<style lang="less" scoped>
.no-data {
  font-size: 0.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
<template>
  <div
    :id="id"
    :style="{ height: height, width: width }"
  />
</template>

<script lang="ts">
import * as echarts from 'echarts'
import { Component, Prop, Watch } from 'vue-property-decorator'
import { mixins } from 'vue-class-component'
import ResizeMixin from './mixins/resize'
interface AirData {
  bottomList: string[]
  dataList: string[]
  dataList1: string[]
  standard: number | string
  max: number | string
  unit: string
  name: string
}
@Component({
  name: 'LineChartDashed'
})
export default class extends mixins(ResizeMixin) {
  // @Prop({ default: "chart" }) private className!: string;
  @Prop({ default: 'chart' }) private id!: string
  @Prop({ default: '200px' }) private width!: string
  @Prop({ default: '200px' }) private height!: string
  @Prop({ required: true }) private propData!: AirData
  @Prop({ required: false, default: 'rgba(14,156,255,1)' })
  private bgColor!: string
  @Prop({ required: false, default: false }) private smooth!: boolean
  @Prop({ required: false, default: true }) private bgColorState!: boolean
  @Prop({ required: false, default: '' }) private xText!: string
  @Watch('propData', { immediate: true, deep: true }) public onMsgChanged(
    newValue: AirData,
    oldValue: AirData
  ) {
    if (this.propData) {
      if (this.chart) {
        this.chart.clear()
        this.chart.dispose()
        this.chart = null
      }
      this.$nextTick(() => {
        this.initChart()
      })
    }
  }
  mounted() {
    if (this.propData) {
      this.initChart()
    }
  }
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.clear()
    this.chart.dispose()
    this.chart = null
  }

  private initChart() {
    const isNoZero = (this.propData.dataList || []).filter((item:any) => item !== 0)
    if (this.chart === null || this.chart === undefined) {
      const chartDom = document.getElementById(this.id) as HTMLDivElement
      if (!chartDom) return
      this.chart = echarts.init(chartDom)
    }
    // SO2:1 NO2:1 O3:1 CO:1 PM10:1 PM:2.5
    this.chart.setOption({
      backgroundColor: 'transparent',
      legend: {
        right: 30,
        textStyle: {
          color: '#7BB7ED',
          fontSize: 12
        },
        itemHeight: 12
      },
      grid: {
        height: '80%',
        left: '2%',
        right: 0,
        top: '20%',
        bottom: '5%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        name: '',
        nameTextStyle: {
          color: '#fff',
          lineHeight: -30,
          align: 'center',
          verticalAlign: 'bottom'
        },
        data: this.propData.bottomList,
        axisLabel: {
          textStyle: {
            fontSize: 12,
            color: 'white'
          }
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        }
      },
      yAxis: {
        type: 'value',
        name: '单位：' + this.propData.unit,
        min: isNoZero.length ? 0 : 10,
        max: this.propData.max,
        nameTextStyle: {
          color: '#fff',
          shadowOffsetX: 50
          // align: "center"
        },
        axisLabel: {
          textStyle: {
            fontSize: 12,
            color: 'white'
          }
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        splitLine: {
          lineStyle: {
            // type: "dashed"
            color: 'RGBA(2, 39, 75, 1)'
          }
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            backgroundColor: '#6a7985'
          }
        }
      },
      series: [
        {
          name: '告警阈值',
          type: 'line',
          markLine: {
            symbol: 'none',
            label: {
              show: false
            },
            data: [
              {
                silent: false, //鼠标悬停事件  true没有，false有
                lineStyle: {
                  //警戒线的样式  ，虚实  颜色
                  type: 'dotted',
                  color: 'red'
                },
                yAxis: this.propData.standard
              }
            ]
          }
        },
        {
          // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
          // @ts-ignore
          name: this.propData.name,
          data: this.propData.dataList,
          type: 'line',
          smooth: this.smooth,
          lineStyle: {
            color: '#15FEFE' //改变折线颜色
          },
          symbol: 'circle',
          symbolSize: 5,
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: this.bgColorState
                    ? 'RGBA(21,180,254, 0.7)'
                    : 'transparent' // 0% 处的颜色
                },
                {
                  offset: 1,
                  color: 'RGBA(25,252,255, 0)' // 100% 处的颜色
                }
              ],
              global: false
            },
            shadowColor: 'rgba(0,85,250,0)',
            shadowBlur: 20
          },
          itemStyle: {
            color: (params:any) => {
              return Number(params.value) > Number(this.propData.standard) ? '#F75353 ' : '#15FEFE'
            }, //改变折线点的颜色
            borderColor: '#fff'
          }
        },
        {
          // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
          // @ts-ignore
          name: this.propData.name1,
          data: this.propData.dataList1,
          type: 'line',
          smooth: this.smooth,
          lineStyle: {
            color: '#15FEFE' //改变折线颜色
          },
          symbol: 'circle',
          symbolSize: 5,
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: this.bgColorState
                      ? 'RGBA(21,254,254, 0.7)'
                      : 'transparent' // 0% 处的颜色
                },
                {
                  offset: 1,
                  color: 'RGBA(1, 208, 254, 0)' // 100% 处的颜色
                }
              ],
              global: false
            },
            shadowColor: 'rgba(0,85,250,0)',
            shadowBlur: 20
          },
          itemStyle: {
            color: '#15FEFE', //改变折线点的颜色
            borderColor: '#fff'
          }
        }
      ]
    } as EChartOption<EChartOption>)
  }
}
</script>



