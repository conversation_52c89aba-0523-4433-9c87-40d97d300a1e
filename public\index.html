<!DOCTYPE html>
<html lang="zh">

<head>
  <meta charset="utf-8">
  <meta
    http-equiv="X-UA-Compatible"
    content="IE=edge"
  >
  <meta
    name="viewport"
    content="width=device-width,initial-scale=1.0"
  >
  <!-- <meta http-equiv="refresh" content="1800"> -->
  <link
    rel="icon"
    href="<%= BASE_URL %>favicon.ico"
  >
  <title><%= webpackConfig.name %></title>
</head>

<body>
  <noscript>
    <strong>We're sorry but <%= webpackConfig.name %> doesn't work properly without JavaScript
      enabled. Please enable it to continue.</strong>
  </noscript>
  <div id="app"></div>
  <script
    src="https://webapi.amap.com/maps?v=1.4.15&key=777fec7ef3cc29281d60ae900fa33925&plugin=AMap.DistrictSearch&plugin=AMap.Heatmap&AMap.ControlBar&plugin=AMap.Object3DLayer&plugin=Map3D&plugin=AMap.Geocoder&plugin=AMap.CircleMarker&plugin=AMap.MouseTool"
  >
  </script>
  <script src="//webapi.amap.com/loca?v=1.3.2&key=777fec7ef3cc29281d60ae900fa33925"></script>
  <script src="https://webapi.amap.com/ui/1.0/main.js?v=1.0.11"></script>
  <link
    rel="stylesheet"
    href="https://g.alicdn.com/de/prismplayer/2.9.1/skins/default/aliplayer-min.css"
  />
  <script
    type="text/javascript"
    charset="utf-8"
    src="https://g.alicdn.com/de/prismplayer/2.9.1/aliplayer-min.js"
  ></script>
  <script src="./static/liveplayer-lib.min.js"></script>
  <script src="/jessibuca/index.js"></script>
  <script src="./static/amap-wind.js"></script>
  <script>
    (function () {
      change();

      function debounce(func, wait, immediate) {
        let timeout, args, context, timestamp, result
        const later = function () {
          // 据上一次触发时间间隔
          const last = +new Date() - timestamp
          // 上次被包装函数被调用时间间隔 last 小于设定时间间隔 wait
          if (last < wait && last > 0) {
            timeout = setTimeout(later, wait - last)
          } else {
            timeout = null
            // 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用
            if (!immediate) {
              result = func.apply(context, args)
              if (!timeout) context = args = null
            }
          }
        }
        return function (...args) {
          context = this
          timestamp = +new Date()
          const callNow = immediate && !timeout
          // 如果延时不存在，重新设定延时
          if (!timeout) timeout = setTimeout(later, wait)
          if (callNow) {
            result = func.apply(context, args)
            context = args = null
          }
          return result
        }
      }

      function change() {
        // document.documentElement.style.fontSize = document.documentElement.clientWidth*100/1920 + 'px';
        document.documentElement.style.fontSize = '100px';
      }
      /* 监听窗口大小发生改变时 */
      window.addEventListener('DOMContentLoaded', change, false);
      window.addEventListener('orientationchange', change, false);
      window.addEventListener('resize', debounce(change, 300, true), false);
    })()
  </script>
  <!-- built files will be auto injected -->
</body>

</html>
