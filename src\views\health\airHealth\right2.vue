<template>
  <div v-loading="loading" class="right2">
    <header class="update-time" >更新时间：{{ data.pushDate }}</header>
    <section class="section use-bg">
      <header class="sectionHeader">
        <aside class="item">
          <div class="value">{{ data.temperature }}</div>
          <div class="label">气温</div>
        </aside>
        <aside class="item">
          <div class="value">{{ data.windLevel }}</div>
          <div class="label">风力等级</div>
        </aside>
      </header>
      <div class="center">
        <div class="value">
          <img v-if="data.weatherImg" :src="data.weatherImg" alt="" class="weatherImg">
        </div>
        <div class="label">{{ data.weather }}</div>
      </div>
      <footer class="sectionFooter">
        <aside class="item">
          <div class="value">{{ data.humidity }}</div>
          <div class="label">湿度</div>
        </aside>
        <aside class="item">
          <div class="value">{{ data.air }}</div>
          <div class="label">空气质量</div>
        </aside>
      </footer>
    </section>
    <div class="content">
      <div class="item">
        <div class="value">{{ data.exerciseIndex }}</div>
        <div class="label">运动适宜指数</div>
      </div>
      <div class="item">
        <div class="value">{{ data.suitability }}</div>
        <div class="label">适宜度</div>
      </div>
    </div>
    <footer class="footer">
      <header class="header use-bg">
        <span>运动建议</span>
      </header>
      <section class="content">
        <div class="mainContent">{{ data.sportsAdvice }}</div>
        <footer class="footer">
          <span class="require">*</span>
          <span>指数≥76为适宜(一级)、指数61-75为较适宜(二级)、指数40-60为较不适宜(三级)、指数< 39为不适宜(四级)</span>
        </footer>
      </section>
    </footer>
  </div>
</template>
<script>
import {getNationalSportsIndex} from '@/api/health'
import gif01 from '@/assets/sunlight.gif'
import gif02 from '@/assets/cloudy.gif'
import gif03 from '@/assets/overcast.gif'
import gif04 from '@/assets/rain.gif'
import gif05 from '@/assets/xue.gif'
import gif06 from '@/assets/wu.png'

export default {
  name: "AirHealthRight2",
  components: {
  },
  props: {

  },
  data() {
    return {
      loading: false,
      data: {},
    };
  },
  created() {
    this.handleQuery()
  },
  methods: {
    handleQuery() {
      this.loading = true
      // 使用数组定义天气类型和图片的映射关系，按优先级排序
      const weatherImageList = [
        { keyword: "雪", image: gif05 },
        { keyword: "雨", image: gif04 },
        { keyword: "雾", image: gif06 },
        { keyword: "云", image: gif02 },
        { keyword: "阴", image: gif03 },
        { keyword: "晴", image: gif01 }
      ]
      getNationalSportsIndex().then(res => {
        const data = res.data.data || {}
        // 如果天气字段存在，按优先级查找匹配的天气类型
        if (data.weather) {
          const matchedWeather = weatherImageList.find(item => data.weather.includes(item.keyword))
          data.weatherImg = matchedWeather.image
        }
        this.data = data
      }).finally(() => {
        this.loading = false
      })
    }
  },
  }
</script>
<style lang="less" scoped>
.right2 {
  .update-time {
    display: flex;
    justify-content: flex-end;
    color: #739fb8;
    font-size: 12px;
    white-space: nowrap;
    margin-top: 10px;
  }

  .section {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    width: 100%;
    height: 240px;
    background-image: url(~@/assets/health/right/<EMAIL>);
    padding: 20px 0 15px 0;
    line-height: 30px;
    .sectionHeader,
    .sectionFooter {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .item {
        .value {
          font-size: 20px;
          color: #FFFFFF;
          font-family: YouSheBiaoTiHei;
        }
        .label {
          font-size: 15px;
          color: #0EC3FF;
        }
      }
      & > :last-child {
        text-align: right;
      }
    }
    .center {
      text-align: center;
      .weatherImg {
        width: 37px;
        height: 30px;
      }
    }
  }

  > .content {
    display: flex;
    gap: 40px;
    > .item {
      flex: 1;
      height: 120px;
      background-image: url(~@/assets/health/right/<EMAIL>);
      text-align: center;
      .value {
        padding: 27px 0 19px 0;
        font-weight: bold;
        font-size: 32px;
        color: #FFC320;
        line-height: 26px;
        font-style: italic;
      }
      .label {
        font-size: 16px;
        color: #6CB9DB;
        line-height: 26px;
      }
    }
  }

 > .footer {
    margin-top: 31px;
    > .header {
      height: 38px;
      line-height: 34px;
      background-image: url(~@/assets/health/right/title.webp);
      color: #2FCCFF;
      padding-left: 35px;
      > span {
          background: linear-gradient(0deg, #60BAFF 0%, #F4FAFF 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
      }
    }
    > .content {
      min-height: 240px;
      padding: 0 30px;
      background: linear-gradient(0deg, rgba(9, 38, 78, 0.15) 0%, rgba(35, 97, 255, 0.15) 100%);
      .mainContent {
        padding: 30px 0;
        color: #E0E6F2;
        font-size: 15px;
        line-height: 26px;
      }
      > .footer {
        font-size: 14px;
        color: #9FB5DD;
        line-height: 26px;
        .require {
          color: #FFA443;
        }
      }
    }

  }
}
</style>
