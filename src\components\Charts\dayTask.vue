<style lang="less" scoped>
    .no-data {
        font-size: 0.2rem;
        display: flex;
        align-items: center;
        justify-content: center;
    }
</style>
<template>
    <div
            :id="id"
            :style="{ height: height, width: width }"
            v-if="propData.dataList.length != 0"
    />
    <div v-else class="no-data" :style="{ height: height, width: width }">暂无联动任务</div>
</template>

<script lang="ts">
import * as echarts from 'echarts'
import { Component, Prop, Watch } from "vue-property-decorator";
import { mixins } from "vue-class-component";
import ResizeMixin from "./mixins/resize";
interface LineData {
    bottomList: string[];
    dataList: string[];
    unit: '',
    name: ''
}
@Component({
    name: "LineChart"
})
export default class extends mixins(ResizeMixin) {
    @Prop({ default: "chart" }) private id!: string;
    @Prop({ default: "200px" }) private width!: string;
    @Prop({ default: "200px" }) private height!: string;
    @Prop({ required: true }) private propData!: LineData;
    // private chart: any = null;
    private option: any = {};
    @Watch("propData", { immediate: true, deep: true }) public onMsgChanged(
        newValue: LineData,
        oldValue: LineData
    ) {
        console.log(this.propData, 'propData1')
        this.propData = newValue;
        if (newValue.dataList.length) {
            if (this.chart) {
                this.chart.clear();
                this.chart.dispose();
                this.chart = null;
            }
            this.$nextTick(() => {
                this.initChart();
            });
        }
    }
    mounted() {
        setTimeout(() => {
            this.initChart();
        }, 2000);
    }
    beforeDestroy() {
        if (!this.chart) {
            return;
        }
        this.chart.clear();
        this.chart.dispose();
        this.chart = null;
    }
    private initChart() {
      const isNoZero = this.propData.dataList.filter((item:any) => item !== 0)
        if(this.chart === null || this.chart === undefined) {
            this.chart = echarts.init(
                document.getElementById(this.id) as HTMLDivElement
            );
        }
        const option = {
            grid: [{
                top: '15%',
                left: '12%',
                right: '5%',
                bottom: '18%'
            }],
            tooltip: {
                trigger: 'axis'
            },
            xAxis: {
                type: 'value',
                axisTick: {
                    show: false
                },
                axisLabel: {
                    textStyle: {
                        color: '#86C6FF'
                    }
                },
                axisLine: {
                    show: false
                },
                splitLine: {
                    show: true,
                    lineStyle: {
                        type: 'dashed',
                        color: 'rgba(255,255,255, .3)'
                    }
                },
                min: isNoZero.length ? 0 : 10
            },
            yAxis: {
                type: 'category',
                axisTick: {
                    show: false
                },
                axisLabel: {
                    textStyle: {
                        color: '#86C6FF'
                    }
                },
                data: this.propData.bottomList,
                splitLine: {
                    show: false,
                },
                axisLine: {
                    lineStyle: {
                        color: 'rgba(255,255,255, .3)'
                    }
                }
            },
            series: [
                {
                    name: '每日任务处理',
                    type: 'bar',
                    label: {
                        show: true,
                        position: 'right',
                        distance: 5,
                        formatter: function (params:any) {
                            return params.value ? '{hr|}' : ''
                        },
                        rich: {
                            hr: {
                                backgroundColor: '#16D0FF',
                                borderRadius: 5,
                                width: 5,
                                height: 5,
                                shadowColor: '#fff',
                                shadowBlur: 10,
                                padding: [5, 5, 0, -20],
                            }
                        }
                    },
                    barWidth: 6,
                    itemStyle: {
                        normal: {
                            color: new echarts.graphic.LinearGradient(1, 0, 0, 0, [{
                                offset: 0,
                                color: "#2DDCFF" // 0% 处的颜色
                            }, {
                                offset: 0.7,
                                color: "#376BAE" // 32% 处的颜色
                            }, {
                                offset: 0.99,
                                color: "#001830" // 100% 处的颜色
                            }], false)
                        }
                    },
                    data: this.propData.dataList
                }
            ]
        }
        // @ts-ignore
        this.chart.setOption(option as EChartOption<EChartOption>);
    }
}
</script>

