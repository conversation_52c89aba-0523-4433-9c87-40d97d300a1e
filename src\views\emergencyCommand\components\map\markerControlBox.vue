<template>
  <div class="marker-control-box">
    <!-- 按钮 S -->
    <div
      class="flex items-center marker-control-btn"
      @click="showIconList = !showIconList"
    >
      <div class="marker-control-btn-icon"></div>
      <span>地图图层</span>
    </div>
    <!-- 按钮 E -->

    <!-- 图标选择弹窗 S -->

    <transition
      :enter-active-class="enterClass"
      :leave-active-class="leaveClass"
    >
      <div v-show="showIconList" class="icon-selecte-box">
        <a-checkbox
          :checked="allSelectItem"
          class="check-item"
          @click="selectAllChange"
        >
          <div class="flex items-center">
            <span class="name flex flex-1">全选</span>
          </div>
        </a-checkbox>
        <a-checkbox
          v-for="(item,i) in iconList"
          :key="item.name"
          :checked="item.selected"
          class="check-item"
          @click="SelChange(i)"
        >
          <div class="flex items-center">
            <img :src="item.icon" />
            <span class="name flex flex-1">{{ item.name }}</span>
          </div>
        </a-checkbox>
      </div>
    </transition>
    <!-- 图标选择弹窗 E -->
  </div>
</template>
<script>
import yj_icon_sxt from '@/assets/images/<EMAIL>'
import yj_maker_sgbj from '@/assets/images/<EMAIL>'
import yj_icon_ysp from '@/assets/images/<EMAIL>'
import yj_icon_wzck from '@/assets/images/<EMAIL>'
import yj_icon_zlsc from '@/assets/images/<EMAIL>'
import yj_icon_jzgd from '@/assets/images/<EMAIL>'
import yj_icon_qxhy from '@/assets/images/<EMAIL>'
import yj_icon_cyyy from '@/assets/images/<EMAIL>'
import yj_icon_jyz from '@/assets/images/<EMAIL>'
import yj_icon_ysqy from '@/assets/images/<EMAIL>'
import yj_icon_yljg from '@/assets/images/<EMAIL>'
export default {
  name: 'markerControlBox',
  data() {
    return {
      iconList: [
        {
          name: '摄像头',
          icon: yj_icon_sxt,
          selected: true,
        },
        {
          name: '声光报警器',
          icon: yj_maker_sgbj,
          selected: true,
        },
        {
          name: '音视频',
          icon: yj_icon_ysp,
          selected: true,
        },
        {
          name: '物资仓库',
          icon: yj_icon_wzck,
          selected: true,
        },
        // {
        //   name: '重污企业',
        //   icon: new URL(
        //     '/src/assets/images/<EMAIL>',
        //     import.meta.url,
        //   ).href,
        //   selected: true,
        // },
        {
          name: '自来水厂',
          icon: yj_icon_zlsc,
          selected: true,
        },
        {
          name: '建筑工地',
          icon: yj_icon_jzgd,
          selected: true,
        },
        {
          name: '汽修行业',
          icon: yj_icon_qxhy,
          selected: true,
        },
        {
          name: '餐饮油烟',
          icon: yj_icon_cyyy,
          selected: true,
        },
        {
          name: '加油站',
          icon: yj_icon_jyz,
          selected: true,
        },
        {
          name: '印刷企业',
          icon: yj_icon_ysqy,
          selected: true,
        },
        {
          name: '医疗机构',
          icon: yj_icon_yljg,
          selected: true,
        },
      ],
      allSelectItem: true,
      enterClass: 'animate__animated animate__fadeInRight',
      leaveClass: 'animate__animated animate__fadeOutRight',
      showIconList: false,
    }
  },
  mounted() {
    const res = []
    this.iconList.forEach((ele) => {
      if (ele.selected) {
        res.push(ele.name)
      }
    })
    this.$emit('change', res)
  },
  watch: {
    iconList: {
      handler(newV) {
        console.log('数据变化', newV)
        const res = []
        this.allSelectItem = newV.every((item) => item.selected)
        newV.forEach((ele) => {
          if (ele.selected) {
            res.push(ele.name)
          }
        })
        this.$emit('change', res)
      },
      deep: true,
    },
  },
  methods: {
    SelChange(i){
      this.iconList.forEach((ele,idx) => {
        if(idx==i)ele.selected = !ele.selected
      })
    },
    selectAllChange() {
      this.iconList.forEach((item) => {
        item.selected = !this.allSelectItem
      })
    },
  },
}
</script>

<style lang="less" scoped>
.marker-control-box {
  position: fixed;
  width: 150px;
  height: 40px;
  right: 505px;
  top: 115px;
  background: url('../../../../assets/images/<EMAIL>') no-repeat center center;
  background-size: 100% 100%;
  padding: 12px 32px;
  z-index: 9;
  .flex{
    display: flex;
  }
  .flex-1{
    flex: 1;
  }
  .items-center{
    align-items: center
  }
  .marker-control-btn {
    width: 100%;
    height: 100%;
    cursor: pointer;

    span {
      font-size: 14px;
      font-weight: bold;
      color: #ffffff;
    }
  }

  .marker-control-btn-icon {
    width: 17px;
    height: 15px;
    background: url('../../../../assets/images/<EMAIL>') no-repeat center
      center;
    background-size: 100% 100%;
    margin-right: 9px;
  }

  .icon-selecte-box {
    position: absolute;
    padding: 5px 15px 5px 20px;
    margin-top: 20px;
    left: -9px;
    width: 180px;
    background-color: rgba(0, 4, 18, 0.56);
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    border-top: 2px solid #3883df;
    border-bottom: 2px solid #3883df;
  }
}

.check-item {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  // margin-left: 0;
  margin: 10px 0;

  img {
    width: 16px;
    height: 14px;
    margin: 0 10px 0 10px;
  }

  .name {
    display: inline-block;
    color: #caf6ff;
    font-size: 14px;
  }
}
</style>
