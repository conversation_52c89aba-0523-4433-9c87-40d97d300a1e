import request from "@/utils/request";
import { AxiosPromise } from "axios";
// 功能区达标率统计
export function complianceRateByType(): AxiosPromise<any> {
    return request({
        url: "/water/noise-days/complianceRateByType",
        method: "get",
    });
}
// 金牛区 噪声总体评价
export function getOne(): AxiosPromise<any> {
    return request({
        url: "/water/noise-county-days/getOne",
        method: "get"
    });
}

export function noiseLevelCountNew(): AxiosPromise<any> {
    return request({
        url: "/water/noise/bigdata/complianceRateByType",
        method: "get"
    });
}

// 功能区单站点达标率
export function complianceRateByStation(): AxiosPromise<any> {
    return request({
        url: "/water/noise/bigdata/complianceRateByStation",
        method: "get",
    });
}

// 功能区国控站点达标率
export function complianceRateByNationalStation(): AxiosPromise<any> {
    return request({
        url: "/water/noise/bigdata/complianceRateByGk",
        method: "get",
    });
}


// 金牛区 功能区监测点位信息
export function listFunction(): AxiosPromise<any> {
    return request({
        url: "/water/noise/listFunction",
        method: "get"
    });
}

// 金牛区 噪声分布统计
export function noiseDistributionCount(): AxiosPromise<any> {
    return request({
        url: "/water/noise-hours/noiseDistributionCount",
        method: "get"
    });
}


// 噪声 天监测趋势 新
export function bigDataDaysAnalyzeNew(data:any): AxiosPromise<any> {
    return request({
        url: "/water/noise/bigdata/daysAnalyzeNew",
        method: "get",
        params:data
    });
}

// 噪声 天监测趋势 新
export function calendar(data:any): AxiosPromise<any> {
    return request({
        url: "/water/noise/calendar",
        method: "get",
        params:data
    });
}
