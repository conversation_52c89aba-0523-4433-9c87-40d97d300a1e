<style lang="less" scoped>
.no-data-randa {
  font-size: 0.2rem;
  text-align: center;
  padding-top: .6rem;
}
</style>
<template>
  <div
    v-if="propData.dataList&&propData.dataList.length > 0"
    :id="id"
    :style="{ height: height, width: width }"
  />
  <div v-else class="no-data-randa" :style="{ height: height, width: width }">
    <img src="../../../assets/heavy-corrupt/<EMAIL>" alt="">
    <br>
    暂无数据
  </div>
</template>

<script lang="ts">
import * as echarts from 'echarts'
import { Component, Prop, Watch } from 'vue-property-decorator'
import { mixins } from 'vue-class-component'
import ResizeMixin from '@/components/Charts/mixins/resize'
import moment from 'moment'
interface AirData {
  bottomList: string[]
  dataList: string[]
  standard: number | string
  max: number | string
  unit: string
  name: string
}
@Component({
  name: 'LineChartDashed'
})
export default class extends mixins(ResizeMixin) {
  // @Prop({ default: "chart" }) private className!: string;
  @Prop({ default: 'chart' }) private id!: string
  @Prop({ default: '200px' }) private width!: string
  @Prop({ default: '200px' }) private height!: string
  @Prop({ required: true }) private propData!: AirData
  @Prop({ required: false, default: 'rgba(14,156,255,1)' })
  private bgColor!: string
  @Prop({ required: false, default: false }) private smooth!: boolean
  @Prop({ required: false, default: true }) private bgColorState!: boolean
  @Prop({ required: false, default: '' }) private xText!: string
  @Prop({ required: false, default: '' }) private subTabName!: string
  @Watch('propData', { immediate: true, deep: true }) public onMsgChanged(
    newValue: AirData,
    oldValue: AirData
  ) {
    if (this.propData) {
      if (this.chart) {
        this.chart.clear()
        this.chart.dispose()
        this.chart = null
      }
      this.$nextTick(() => {
        this.initChart()
      })
    }
  }
  mounted() {
    if (this.propData) {
      this.initChart()
    }
  }
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.clear()
    this.chart.dispose()
    this.chart = null
  }
  private dataPre(pre:any,next:any){
    if(pre>next){
      return next - (next*0.05)
    }else if(pre<next){
      return next + (next*0.05)
    }else{
      return next
    }
  }
  private initChart() {
    if (this.chart === null || this.chart === undefined) {
      const chartDom = document.getElementById(this.id) as HTMLDivElement
      if (!chartDom) return
      this.chart = echarts.init(chartDom)
    }
    // SO2:1 NO2:1 O3:1 CO:1 PM10:1 PM:2.5
    const Xdata = this.propData.bottomList.map((v) => v.slice(11, 13)+':00')
    const pruNum = +Xdata[Xdata.length-1].slice(0, 2)
    const XdataPre = pruNum+1>9?pruNum+1:'0'+pruNum+1
    const dataList = this.propData.dataList.map((item,i)=>[Xdata[i],item])||[]
    const dataListPre = this.dataPre(+(dataList[dataList.length-2][1]), +(dataList[dataList.length-1][1]))
    // console.log([[XdataPre+':00',dataListPre]].unshift(dataList[dataList.length - 1]), dataList[dataList.length - 1]);

    this.chart.setOption({
        backgroundColor: 'transparent',
        color: ['#1DCCFF', '#15FEFE'],
        // legend: {
        //   right: 30,
        //   textStyle: {
        //     color: '#7BB7ED',
        //     fontSize: 12
        //   },
        //   itemHeight: 12
        // },
        grid: {
          height: '80%',
          left: '1%',
          right: '3%',
          top: '15%',
          bottom: '5%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          name: '',
          nameTextStyle: {
            color: '#fff',
            lineHeight: -30,
            align: 'center',
            verticalAlign: 'bottom'
          },
          data: [...Xdata,XdataPre+':00'],
          axisLabel: {
            textStyle: {
              fontSize: 12,
              color: 'white'
            }
          },
          axisLine: {
            show: false,
            lineStyle: {
              type: 'dotted'
            }
          },
          axisTick: {
            show: false
          }
        },
        yAxis: {
          type: 'value',
          name: '',
          min: 0,
          // max: this.propData.max,
          nameTextStyle: {
            color: '#fff',
            shadowOffsetX: 50
            // align: "center"
          },
          axisLabel: {
            textStyle: {
              fontSize: 12,
              color: 'white'
            }
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          splitLine: {
            lineStyle: {
              // type: "dashed"
              color: 'RGBA(2, 39, 75, 1)'
            }
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              show: false,
              backgroundColor: '#6a7985'
            }
          },
          confine: true,
          formatter: (params:any) => {
            const curDay=moment().format('YYYY-MM-DD')
            let str=`
            <div style="border-radius: 0.06rem;padding: 0.1rem 0.13rem; background-color: rgba(11, 11, 11, .4);color: #fff;">
              <div style="font-size: 0.14rem;"><i class="el-icon-time"></i>
              监测时间: ${
                params[0].seriesIndex==0
                ?this.propData.bottomList[params[0].dataIndex]
                :this.propData.bottomList[params[0].dataIndex].slice(0,10)+' '+params[0].value[0]
              }
              </div>
              <div style="font-size: 0.14rem;margin-top: 0.12rem;">
                <i class="el-icon-stopwatch"></i> ${this.subTabName}： <span>${params[0].value[1]} ${this.subTabName=='消光系数'? 'KM-1':this.subTabName=='退偏振比'?'':'μg/m3'}</span>
              </div>
            </div>
          `
            return str
          }
        },
        series: [
          // {
          //   name: '告警阈值',
          //   type: 'line',
          //   markLine: {
          //     symbol: 'none',
          //     label: {
          //       show: true,
          //       // formatter:()=>{
          //       //   return '告警阈值'
          //       // },
          //       // distance: [-13,0]
          //     },
          //     data: [
          //       {
          //         silent: false, //鼠标悬停事件  true没有，false有
          //         lineStyle: {
          //           //警戒线的样式  ，虚实  颜色
          //           type: 'dotted',
          //           color: 'red'
          //         },
          //         yAxis: this.propData.standard
          //       }
          //     ]
          //   }
          // },
          {
            // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
            // @ts-ignore
            name: '',
            data: dataList,
            type: 'line',
            smooth: this.smooth,
            lineStyle: {
              color: '#15B4FE' //改变折线颜色
            },
            symbol: 'circle',
            symbolSize: 5,
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: this.bgColorState
                      ? 'RGBA(21,180,254, 0.7)'
                      :'transparent' // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: 'RGBA(25,252,255, 0)' // 100% 处的颜色
                  }
                ],
                global: false
              },
              shadowColor: 'rgba(0,85,250,0)',
              shadowBlur: 20
            },
            itemStyle: {
              color: (params: any) => {
                // return params.value < this.propData.standard ? '#1DCCFF'  : 'red'
                return (params.value[1]<=this.propData.standard||this.propData.standard==null)? '#1DCCFF':'red'
              },
              borderColor: '#fff'
            }
          },
          {
            name: '',
            type: 'line',
            smooth: this.smooth,
            lineStyle: {
              color: 'red', //改变折线颜色
              type: 'dotted',
            },
            symbol: 'none',
            data: [dataList[dataList.length - 1],[XdataPre+':00',dataListPre]]
          }
        ]
      } as unknown as EChartOption<EChartOption>)
  }
}
</script>
