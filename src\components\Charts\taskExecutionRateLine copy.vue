<style lang="less" scoped>
.no-data {
  font-size: 0.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
<template>
  <div :id="id" :style="{ height: height, width: width }" />
</template>

<script lang="ts">
import * as echarts from 'echarts'
import { Component, Prop, Watch } from "vue-property-decorator";
import { mixins } from "vue-class-component";
import ResizeMixin from "./mixins/resize";
interface AirData {
  bottomList: string[];
  dataList: string[];
  dataList1: string[];
  dataList2: string[];
  dataList3: string[];
}
@Component({
  name: "LineChartDashedcopy",
})
export default class extends mixins(ResizeMixin) {
  // @Prop({ default: "chart" }) private className!: string;
  @Prop({ default: "chart" }) private id!: string;
  @Prop({ default: "200px" }) private width!: string;
  @Prop({ default: "200px" }) private height!: string;
  @Prop({ required: true }) private propData!: AirData;
  @Prop({ required: false, default: "rgba(14,156,255,1)" })
  private bgColor!: string;
  @Prop({ required: false, default: false }) private smooth!: boolean;
  @Prop({ required: false, default: true }) private bgColorState!: boolean;
  @Prop({ required: false, default: "" }) private xText!: string;

  @Watch("propData", { immediate: true, deep: true }) public onMsgChanged(
    newValue: AirData,
    oldValue: AirData
  ) {
    if (this.propData) {
      if (this.chart) {
        this.chart.clear();
        this.chart.dispose();
        this.chart = null;
      }
      this.$nextTick(() => {
        this.initChart();
      });
    }
  }
  mounted() {
    // if (this.propData) {
    //   this.initChart()
    // }
    if (this.propData) {
      if (this.chart) {
        this.chart.clear();
        this.chart.dispose();
        this.chart = null;
      }
      this.$nextTick(() => {
        this.initChart();
      });
    }
  }
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.clear();
    this.chart.dispose();
    this.chart = null;
  }

  private initChart() {
    if (this.chart === null || this.chart === undefined) {
      const chartDom = document.getElementById(this.id) as HTMLDivElement;
      if (!chartDom) return;
      this.chart = echarts.init(chartDom);
    }
    // SO2:1 NO2:1 O3:1 CO:1 PM10:1 PM:2.5
    this.chart.setOption({
      backgroundColor: "transparent",
      color: ["#1DCCFF", "#15FEFE"],
      legend: {
        show: false,
        right: 30,
        textStyle: {
          color: "#7BB7ED",
          fontSize: 12,
        },
        itemHeight: 12,
      },
      grid: {
        // height: '80%',
        left: "5%",
        top: "6%",
        bottom: "0%",
        right: "5%",
        containLabel: true,
      },
      xAxis: {
        show: true,
        type: "category",
        data: this.propData.bottomList,
        axisLabel: {
          show: true,
          textStyle: {
            fontSize: 12,
            color: "white",
          },
        },
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
      },
      yAxis: [
        {
          type: "value",
          // name: '单位：%',
          min: 0,
          max: 100,
          nameTextStyle: {
            color: "#86C6FF",
          },
          axisLabel: {
            textStyle: {
              fontSize: 12,
              color: "#86C6FF",
            },
            formatter: "{value}%", //刻度标签的内容格式器，支持字符串模板和回调函数两种形式，按照自己需求设置
          },
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          splitLine: false,
          scale: true,
        },
        {
          type: "value",
          scale: true,
          name: "Order",
          // max: 1200,
          position: "right",
          min: 0,
          // boundaryGap: [0.2, 0.2],
          axisLabel: {
            textStyle: {
              fontSize: 12,
              color: "#86C6FF",
            },
          },
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          splitLine: false,
        },
      ],
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "cross",
          label: {
            backgroundColor: "#6a7985",
          },
        },
        formatter: (params: Array<any>): string => {
          let ret = `${params[0].name}`;
          let value = `${params[0].value}`;
          let index = `${params[0].dataIndex}`;
          ret += `<br/>完成率：${value}%<br/>已完成：${this.propData.dataList1[index]}<br/>全部：${this.propData.dataList3[index]}`;
          return ret;
        },
      },
      animation: true,
      // animationDuration: 5000,
      series: [
        {
          // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
          // @ts-ignore
          name: "",
          data: this.propData.dataList,
          type: "line",
          smooth: this.smooth,
          lineStyle: {
            color: "#15B4FE", //改变折线颜色
          },
          // yAxisIndex: 0,
          symbol: "circle",
          symbolSize: 5,
          areaStyle: {
            color: {
              type: "linear",
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: this.bgColorState
                    ? "RGBA(21,180,254, 0.7)"
                    : "transparent", // 0% 处的颜色
                },
                {
                  offset: 1,
                  color: "RGBA(25,252,255, 0)", // 100% 处的颜色
                },
              ],
              global: false,
            },
            shadowColor: "rgba(0,85,250,0)",
            shadowBlur: 20,
          },
          itemStyle: {
            // color: (params:any) => {
            //   return params.value < this.propData.standard ? '#1DCCFF'  : 'red'
            // }, //改变折线点的颜色 '#1DCCFF'
            borderColor: "#fff",
          },
        },
        {
          // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
          // @ts-ignore
          name: "任务总数",
          type: "bar",
          // yAxisIndex: 0,
          data: this.propData.dataList1,
        },
      ],
    } as EChartOption<EChartOption>);
  }
}
</script>



