<template>
  <div class="swiperHome_dialog" v-show="analysisData && JSON.stringify(analysisData) !== '{}'">
    <div class="title">
      <span>污染源特征雷达分析</span>
    </div>
    <div class="left_box">
      <h2 class="result">
        分析结果：<span>{{ analysis || "--" }}</span>
      </h2>
      <div class="color_list">
        <div class="color_item">
          <div class="label" style="background: #EC808D"></div>
          <div class="value">标准值上限</div>
        </div>
        <div class="color_item">
          <div class="label" style="background: #C280FF"></div>
          <div class="value">标准值</div>
        </div>
        <div class="color_item">
          <div class="label" style="background: #00BFBF"></div>
          <div class="value">标准值下限</div>
        </div>
        <div class="color_item">
          <div class="label" style="background: #00EAFF"></div>
          <div class="value">特征值</div>
        </div>
      </div>
    </div>
    <div class="echart_box" ref="ehart"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { listStationPollutantMonitorAnalysis } from "@/api/homeTable";
export default {
  name: "",
  props: {
    analysisData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      chart: null,
      analysis: "",
    };
  },
  watch: {
    analysisData: {
      handler(newVal) {
        if (newVal && JSON.stringify !== "{}") {
          if (this.$refs.ehart) {
            this.initCharts();
          }
        }
      },
      deep: true,
      immediate: true,
    },
  },
  created() {},
  mounted() {
    this.initCharts();
  },
  methods: {
    handleGetListStationPollutantMonitorAnalysis(params) {
        console.log('params', params)
      //   const params = {
      //     findDate: "2024-01-05 00:00:00",
      //     pollutantCode: "aqi",
      //     timeType: 1,
      //   };
      listStationPollutantMonitorAnalysis(params).then((res) => {
        console.log("res11111", res);
        this.characteristicRadarChart = res.data.data.characteristicRadarChart;
      });
    },
    initCharts() {
      const data = {
        no2CvMin: 0.7947991705593622,
        no2CvMax: 1.2052008294406378,
        no2CvStan: 1,
        no2Cv: 1.315,

        pm10CvMin: 0.9388633451043803,
        pm10CvStan: 1,
        pm10CvMax: 1.0611366548956196,
        pm10Cv: 0.918,

        pm25CvMin: 0.9468439344907709,
        pm25CvMax: 1.0531560655092291,
        pm25CvStan: 1,
        pm25Cv: 0.915,

        so2CvMin: 0.7254498911856376,
        so2CvMax: 1.2745501088143623,
        so2CvStan: 1,
        so2Cv: 1.121,

        coCvMin: -1.645751311064591,
        coCvMax: 3.645751311064591,
        coCvStan: 1,
        coCv: 8,
        analysis: "其他类型",
      };
      const {
        no2CvMin,
        no2CvMax,
        no2CvStan,
        no2Cv,
        pm10CvMin,
        pm10CvStan,
        pm10CvMax,
        pm10Cv,
        pm25CvMin,
        pm25CvMax,
        pm25CvStan,
        pm25Cv,
        so2CvMin,
        so2CvMax,
        so2CvStan,
        so2Cv,
        coCvMin,
        coCvMax,
        coCvStan,
        coCv,
        analysis,
      } = this.analysisData;
      const minList = [pm25CvMin, pm10CvMin, so2CvMin, no2CvMin, coCvMin];
      const maxList = [pm25CvMax, pm10CvMax, so2CvMax, no2CvMax, coCvMax];
      const stanList = [pm25CvStan, pm10CvStan, so2CvStan, no2CvStan, coCvStan];
      const cvList = [pm25Cv, pm10Cv, so2Cv, no2Cv, coCv];
      this.analysis = analysis;

      if (this.chart === null || this.chart === undefined) {
        this.chart = echarts.init(this.$refs.ehart);
      }
      const option = {
        backgroundColor: "transparent",
        title: {
          show: false,
          text: "Basic Radar Chart",
        },
        legend: {
          show: false,
          data: ["Allocated Budget", "Actual Spending"],
        },
        grid: {
          top: "5%",
        },

        tooltip: {},
        radar: {
          // shape: 'circle',
          radius: "65%",
          indicator: [
            { name: "PM₂.₅" },
            { name: "PM₁₀" },
            { name: "SO₂" },
            { name: "NO₂" },
            { name: "CO" },
          ],
          grid: {
            show: false,
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "rgba(255,255,255,0.5)",
            },
          },
          splitArea: {
            show: false,
          },
          nameGap: 5
        },
        series: [
          {
            name: "标准值上限",
            type: "radar",
            symbolSize: 0,
            z: 4,
            data: [
              {
                value: maxList,
                lineStyle: {
                  normal: {
                    color: "#EC808D",
                    // width: 1,
                  },
                },
              },
            ],
          },
          {
            name: "标准值",
            type: "radar",
            symbolSize: 0,
            z: 3,
            data: [
              {
                value: stanList,
                lineStyle: {
                  normal: {
                    color: "#C280FF",
                    // width: 1,
                  },
                },
              },
            ],
          },
          {
            name: "标准值下限",
            type: "radar",
            symbolSize: 0,
            z: 2,
            data: [
              {
                value: minList,
                lineStyle: {
                  normal: {
                    color: "#00BFBF",
                    // width: 1,
                  },
                },
              },
            ],
          },
          {
            name: "特征值",
            type: "radar",
            // symbolSize: 0,
            z: 1,
            symbol: "circle",
            data: [
              {
                value: cvList,
                lineStyle: {
                  normal: {
                    color: "#00EAFF",
                    borderColor: "rgba(87,201,255,0.2)",
                    borderWidth: 0,
                    // width: 1,
                  },
                },
                areaStyle: {
                  normal: {
                    color: "rgba(0,234,255,0.3)",
                  },
                },
              },
            ],
          },
        ],
      };
      this.chart.setOption(option);
    },
  },
  computed: {},
  components: {},
};
</script>

<style lang="less" scoped>
.swiperHome_dialog {
  width: 402px;
  height: 222px;
  background: url("~@/assets/images/swiperHomeChart.png") no-repeat;
  background-size: contain;
  position: relative;
  .title {
    position: absolute;
    left: 25px;
    top: 28px;
    font-family: PingFang SC;
    font-weight: 600;
    font-size: 18px;
    line-height: 34px;
    span {
      color: #ffffff;
      text-shadow: 0px 3px 4px rgba(2, 17, 20, 0.15);
      font-style: italic;
      //   background: linear-gradient(0deg, rgba(11, 155, 255, 0.46) 0%);
      //   -webkit-background-clip: text;
      //   -webkit-text-fill-color: transparent;
    }
  }
  .echart_box {
    width: 180px;
    height: 180px;
    position: absolute;
    right: 17px;
    top: 25px;
  }
  .left_box {
    position: absolute;
    left: 30px;
    top: 65px;
    .result {
      font-family: PingFang SC;
      font-weight: 500;
      font-size: 14px;
      color: #91b4cd;
      span {
        color: #faaf4e;
      }
    }
    .color_list {
      .color_item {
        display: flex;
        align-items: center;
        .label {
          width: 20px;
          height: 2px;
          background-color: #ec808d;
          margin-right: 5px;
        }
        .value {
          font-family: PingFang SC;
          font-weight: 400;
          font-size: 11px;
          color: #afd6f2;
          line-height: 16px;
        }
      }
    }
  }
}
</style>
