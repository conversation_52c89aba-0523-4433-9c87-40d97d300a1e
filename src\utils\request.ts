import axios, { AxiosRequestConfig, AxiosResponse, AxiosError } from "axios";
import { message } from "ant-design-vue";
import { getToken, removeToken } from "@/utils/authority";
import router from "@/router";
// message.success("");
const { CancelToken } = axios;
const source = CancelToken.source();
let params = {};
if (process.env.VUE_APP_ENV === "development") {
  params = {
    timeout: 30000,
  };
} else {
  params = {
    baseURL: process.env.VUE_APP_BASE_API,
    timeout: 30000,
  };
}
const httpService = axios.create(params);

httpService.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    // 是否需要设置 token
    const isToken = (config.headers || {}).isToken === false;
    if (getToken() && !isToken) {
      config.headers["x-auth-token"] = getToken();
      config.cancelToken = source.token;
    }
    return config;
  },
  (error: AxiosError) => {
    return Promise.reject(error);
  }
);

httpService.interceptors.response.use(
  (response: AxiosResponse) => {
    if (response.data.code !== 200) {
      // message.error(response.data.msg, 3);
      console.log(response.data.code);
      // if (response.data.code == 403 || response.data.code == 10020) {
      //   removeToken();
      //   // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
      //   // @ts-ignore
      //   // this.$router.replace("/login");
      //   // 跳转到登录页
      //   location.href = "/login";
      // }
    }
    return response;
  },
  (error: AxiosError) => {
    console.log(error.response);
    // switch ((error.toJSON() as any).code) {
    // case "ECONNABORTED":
    //   message.warning("请求超时，请检查网络状况后重试！", 3);
    //   break;
    // case "Network Error":
    //   message.warning("网络连接异常，请检查网络状况后重试！", 3);
    //   break;
    //   default:
    //     break;
    // }]
    if ((error.response as any).status == 403) {
      source.cancel();
      message.error("token无效,请重新登录", 3);
      removeToken();
      // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
      // @ts-ignore
      router.push(`login`);
    }
    if ((error.response as any).config.url == "/auth/login/handler") {
      if ((error.response as any).data.code === 10401) {
        message.error("账号或密码错误", 3);
      } else if ((error.response as any).data.code === 10400) {
        message.error("账号未注册,请联系管理员注册", 3);
      } else {
        message.error(
          (error.response as any).data.msg ||
          "未分配大数据菜单权限，请联系管理员",
          3
        );
      }
    }
    return Promise.reject(error);
  }
);
export default httpService;
