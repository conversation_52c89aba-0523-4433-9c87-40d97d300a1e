<template>
  <div id="grid3DUp"></div>
</template>

<script>
import * as echarts from 'echarts'
import 'echarts-gl'
import dayjs from 'dayjs'
// import dayjs from 'dayjs'
export default {
  name: 'cardBox',
  props: {
    chartData: {
      type: Array,
      default: [],
    },
  },
  data() {
    return {
      thisYear: dayjs().format('YYYY') + '年',
      lastYear: Number(dayjs().format('YYYY') - 1) + '年',
      bottomArr: [],
      timer: null,
    }
  },
  watch: {
    chartData: {
      handler(newVal) {
        let categories = []
        let thisYearData = []
        let lastYearData = []

        newVal.forEach((v) => {
          categories.push(v.name)
          thisYearData.push(v.thisYearRate)
          lastYearData.push(v.lastYearRate)
        })
        this.creatEharts(categories, thisYearData, lastYearData)
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {
    let categories = []
    let thisYearData = []
    let lastYearData = []

    this.chartData.forEach((v) => {
      categories.push(v.name)
      thisYearData.push(v.thisYearRate)
      lastYearData.push(v.lastYearRate)
    })

    this.creatEharts(categories, thisYearData, lastYearData)
  },
  methods: {
    creatEharts(categories, thisYearData, lastYearData) {
      let option = {}
      let myChart = echarts.init(document.getElementById('grid3DUp'))
      option = {
        backgroundColor: '',
        textStyle: {
          color: '#c0c3cd',
          fontSize: 14,
        },
        darkMode: false,
        tooltip: {
          backgroundColor: 'rgba(50,50,50,0.7)',
          borderColor: 'rgba(51,51,51)',
          textStyle: {
            color: '#ffffff',
          },
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
          formatter: (params) => {
            let result = params[0].name + '<br/>'
            let dataIndex = params[0].dataIndex
            let currentData = this.chartData[dataIndex]

            if (currentData) {
              // 将达标率和平均值融合在一行显示
              params.forEach((item, index) => {
                let avgValue = index === 0 ? currentData.thisYearAvg || 0 : currentData.lastYearAvg || 0
                result += item.marker + item.seriesName + ': ' + item.value + '% 数值: ' + avgValue + 'dB(A)<br/>'
              })
            } else {
              // 如果没有对应数据，只显示达标率
              params.forEach((item) => {
                result += item.marker + item.seriesName + ': ' + item.value + '%<br/>'
              })
            }

            return result
          },
        },
        legend: {
          data: [this.lastYear, this.thisYear],
          textStyle: {
            color: '#A6CBE2',
            fontSize: 12,
          },
          top: '5%',
          right: '0%',
          itemWidth: 13,
          itemHeight: 5,
        },
        color: ['#00D7E9', '#FF6B6B'],
        grid: {
          left: '3%',
          right: '-1%',
          bottom: '3%',
          top: '18%',
          containLabel: true,
        },
        xAxis: [
          {
            type: 'category',
            data: categories,
            axisLine: {
              show: true,
              lineStyle: {
                color: '#385982',
                width: 1,
                type: 'dashed',
              },
            },
            axisTick: {
              show: true,
            },
            axisLabel: {
              show: true,
              fontSize: 12,
              textStyle: {
                color: '#A6CBE2',
              },
              // interval: 'auto', // 自动计算标签间隔，避免重叠
              rotate: 45,
            },
          },
        ],
        yAxis: [
          {
            name: '达标率(%)',
            nameTextStyle: {
              color: '#A6CBE2',
              fontSize: 12,
            },
            min: 0,
            max: 100,
            axisLine: {
              show: false,
              lineStyle: {
                color: '#494e54',
              },
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: '#385982',
                type: 'dashed',
              },
            },
            axisLabel: {
              color: '#B6D0D8',
            },
          },
        ],
        series: [
          {
            name: this.lastYear,
            data: thisYearData,
            type: 'bar',
            barMaxWidth: 'auto',
            barGap: '50%',
            barWidth: 12,
            itemStyle: {
              color: {
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                type: 'linear',
                colorStops: [
                  {
                    offset: 0,
                    color: '#59F7CA',
                  },
                  {
                    offset: 1,
                    color: '#0A6B6A',
                  },
                ],
              },
            },
            label: {
              show: true,
              position: 'top',
              distance: 0,
              color: 'white',
              formatter: '{c}%',
              fontSize: 8,
              backgroundColor: 'rgba(89,247,202,0.5)',
              padding: 3,
              borderRadius: 2,
            },
          },
          {
            name: this.thisYear,
            data: lastYearData,
            type: 'bar',
            barMaxWidth: 'auto',
            barWidth: 12,
            barGap: '50%',
            itemStyle: {
              color: {
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                type: 'linear',
                colorStops: [
                  {
                    offset: 0,
                    color: '#42A8FF',
                  },
                  {
                    offset: 1,
                    color: '#1C4B7C',
                  },
                ],
              },
            },
            label: {
              show: true,
              position: 'top',
              distance: 0,
              color: 'white',
              formatter: '{c}%',
              fontSize: 8,
              backgroundColor: 'rgba(66,168,255,0.5)',
              padding: 3,
              borderRadius: 2,
            },
          },
        ],
      }
      if (option) {
        myChart.setOption(option)
        this.lunboEcharts(myChart, categories.length)
      }
    },
    lunboEcharts(echartsId, dataLen, currentIndex = -1) {
      this.timer = setInterval(() => {
        echartsId.dispatchAction({
          type: 'downplay',
          seriesIndex: 0,
          dataIndex: currentIndex,
        })
        echartsId.dispatchAction({
          type: 'downplay',
          seriesIndex: 1,
          dataIndex: currentIndex,
        })
        currentIndex = (currentIndex + 1) % dataLen
        echartsId.dispatchAction({
          type: 'highlight',
          seriesIndex: 0,
          dataIndex: currentIndex,
        })
        echartsId.dispatchAction({
          type: 'highlight',
          seriesIndex: 1,
          dataIndex: currentIndex,
        })
        echartsId.dispatchAction({
          type: 'showTip',
          seriesIndex: 0,
          dataIndex: currentIndex,
        })
      }, 3000)
    },
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer)
    }
  },
}
</script>

<style lang="less">
#grid3DUp {
  height: 245px;
  width: 100%;
}
</style>
