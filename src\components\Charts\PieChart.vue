<template>
  <div
    :id="id"
    :class="className"
    :style="{ height: height, width: width }"
  ></div>
</template>

<script lang="ts">
import * as echarts from 'echarts'
import { Component, Prop } from "vue-property-decorator";
import { mixins } from "vue-class-component";
import ResizeMixin from "./mixins/resize";
interface InData {
  name: string;
  dataList: string[];
  colorList: string[];
}
@Component({
  name: "ProgressPieChart"
})
export default class extends mixins(ResizeMixin) {
  @Prop({ default: "chart" }) private className!: string;
  @Prop({ default: "chart" }) private id!: string;
  @Prop({ default: "400px" }) private width!: string;
  @Prop({ default: "200px" }) private height!: string;
  @Prop({ required: true }) private PieChartData!: InData;
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
  }

  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.clear();
    this.chart.dispose();
    this.chart = null;
  }

  private initChart() {

    if(this.chart === null || this.chart === undefined) {
      this.chart = echarts.init(
        document.getElementById(this.id) as HTMLDivElement
      );
    }
    // '#1D9DFF', '#FF9F7F', '#FB7293', '#E7BCF3', '#8378EA', '#1D9DFF', '#32C5E9', '#9FE6B8', '#FFDB5C'
    const colors: Array<string> = this.PieChartData.colorList;
    this.chart.setOption({
      title: {
        text: this.PieChartData.name,
        textStyle: {
          color: "#fff",
          fontSize: 12
        }
      },
      tooltip: {
        show: true,
        trigger: "item"
      },
      color: colors,
      series: [
        {
          // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
          // @ts-ignore
          name: this.PieChartData.name,
          type: "pie",
          radius: [50, 70],
          itemStyle: {
            normal: {
              label: {
                show: true
              },
              labelLine: {
                show: true
              }
            }
          },
          hoverAnimation: true,
          data: this.PieChartData.dataList
        }
      ],
      animationEasing: "elasticOut",
      animationEasingUpdate: "elasticOut",
      animationDelay(idx: number) {
        return idx * 20;
      },
      animationDelayUpdate(idx: number) {
        return idx * 20;
      }
    } as EChartOption);
  }
}
</script>
