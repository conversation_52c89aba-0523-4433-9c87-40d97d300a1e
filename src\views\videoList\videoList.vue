<template>
  <div class="video-list">
    <!-- S 主体结构 -->
    <!-- S 筛选栏 -->
    <div class="filter">
      <div class="back" @click="Backs">&lt;&lt;返回</div>
    </div>
    <div class="main">
      <div class="left-part">
        <!-- <LivePlayer
              :video-url="item.url"
              :poster="item.imageUrl"
              live
              autoplay
              fluent
              stretch
            /> -->
            <div style="width:1320px;">
              <player
                v-if="rightVideoUrl"
                :video-url="rightVideoUrl"
                :has-audio="false"
                height="820px"
                :isFullResize="false"
                :isResize="false"
                :autoplay="true"
                :index="index"
                muted
              />
              <div v-else style="width:100%">
                <img :src="bgcc" alt="" style="width:100%;height:820px">
              </div>
            </div>
      </div>
      <div class="right-part">
        <!-- 时间选择 -->
        <div class="selectTime">
          <div class="selectBox">
            <el-date-picker
              v-model="date"
              type="date"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              placeholder="选择日期"
              :picker-options="pickerOptions"
              @change="dateChange"
            />
            <img
              slot="prefix"
              src="../../assets/video_list_right_rili.png"
              alt
              class="rili"
            />
          </div>
        </div>
        <!-- 录像列表 -->
        <div
          v-loading="selectload"
          class="vedioList"
          element-loading-background="rgba(0, 0, 0, 0)"
          element-loading-text="拼命加载中"
        >
          <div class="tabhead">
            <div>录像时间</div>
            <div>操作</div>
          </div>
          <div :options="swiperOption" class="swiperList">
            <div
              v-for="item in playbackList"
              :key="item.endTime"
              class="tr"
            >
              <div class="tiem">{{ item.startTime }} — {{ item.endTime }}</div>
              <div class="btnDiv">
                <div style="cursor: pointer" @click="playback(item)">播放</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- E 主体结构 -->

    <!-- 直播选择弹框 -->
    <div v-show="showDialog" class="dialog">
      <div class="content">
        <div>
          <img src="../../assets/video_list_dialog_title.png" class="title" />
          <img
            src="../../assets/video_list_dialog_close.png"
            class="close"
            @click="showDialog = false"
          />
        </div>
        <el-input placeholder="关键词搜索" style="width: 240px">
          <i slot="suffix" class="el-icon-search el-input__icon"></i>
        </el-input>
        <div class="tablehead">
          <div>景区名称</div>
          <div>设备名称</div>
          <div>设备地址</div>
          <div>设备状态</div>
          <div>操作</div>
        </div>
        <div
          v-loading="loading"
          class="tablebody"
          element-loading-text="加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.8)"
        >
          <div
            v-for="(item, index) in videoList"
            :key="item.deviceId"
            class="tr"
          >
            <div>{{ item.scenicSpotName }}</div>
            <div>{{ item.deviceName }}</div>
            <!-- <el-tooltip effect="dark" :content="item.address" placement="top"> -->
              <div class="address">{{ item.address }}</div>
            <!-- </el-tooltip> -->
            <!-- <div :style="{ color: item.status ? '' : '#DE6464' }">
              {{ item.status ? '在线' : '离线' }}
            </div> -->
            <div>
              <div class="table-button" @click="chooseCamera(item, index)">
                选择
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// import { getCameraDevice, getPlaybackList, playbackStart } from '@/api/videoList'
import { fetchCameraUrl, playbackListApi, playbackStartApi } from '@/api/water';
import { Swiper, SwiperSlide } from "vue-awesome-swiper";
// import { liveAddress } from '@/api/home'
import player from '@/components/jessibucaPlayer/jessibuca'
// import { formatDate } from '@/utils'
import moment from 'moment'
// import LivePlayer from '@liveqing/liveplayer'
import bgcc from '@/assets/bgcc.png';

export default {
  name: 'VideoList',
  components: {
    // LivePlayer
    player,
    Swiper,
    SwiperSlide
  },
  data() {
    return {
      bgcc:bgcc,
      screenCount: 1,
      date: new Date(),
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        },
      },
      btnimage: require('../../assets/video_list_icon_play.png'),
      selectload: false,
      swiperOption: {
        direction: 'vertical',
        observer: true, // 修改swiper自己或子元素时，自动初始化swiper
        observeParents: true, // 修改swiper的父元素时，自动初始化swiper
        slidesPerView: 10,
        slidesPerGroup: 1,
        loop: false,
        // autoplay: {
        //   delay: 2000,
        //   disableOnInteraction: false,
        // },
        on: {
          click: (v) => {},
        },
      },
      showDialog: false,
      videoList: [],
      screenVideoList: [],
      rightVideo: undefined,
      rightVideoUrl: '',
      playbackList: [],
      showRightVideo: false,
      videoIndex: undefined,
      loading: false,
      fullScreen: false,
      channelId:'',
      pages: 1,
    }
  },
  computed: {
    videoWidth() {
      return `${1160 / Math.sqrt(this.screenCount) - 20}px`
    },
    videoHeight() {
      return `${((1160 / Math.sqrt(this.screenCount) - 20) * 9) / 16}px`
    },
  },
  mounted() {
    this.pages = this.$route.query.pageNum
    console.log(this.$route.query.pageNum);
    // this.date = formatDate(new Date(), 'yyyy-MM-dd')
    this.getCameraDevice()
    // window.addEventListener('resize', () => {
    //   if (!this.checkFull() && this.fullScreen) {
    //     this.fullScreen = false
    //   }
    //   console.log('resize')
    // })
  },
  methods: {
    reload() {
      this.$forceUpdate()
    },
    /**
     * 返回
     */
    Backs() {
      this.$router.push(`/waterVideo?pages=${this.pages}`)
    },
    toList() {
      // this.$router.push('/videoListFull')
      const dom = document.documentElement
      if (!this.fullScreen) {
        dom.requestFullscreen()
        this.$router.push('/videoListFull')
      }
      this.fullScreen = !this.fullScreen
    },
    checkFull() {
      let isFull =
        document.fullscreenElement ||
        document.mozFullScreenElement ||
        document.webkitFullscreenElement ||
        document.msFullscreenElement
      if (!isFull) {
        isFull = false
      } else {
        isFull = true
      }
      return isFull
    },
    /**
     * 获取所有摄像头列表
     */
    async getCameraDevice() {
      this.channelId = this.$route.query.id

      const params ={
        channelId: this.channelId,
        startTime: moment().format('YYYY-MM-DD')+' 00:00:00',
        endTime: moment().format('YYYY-MM-DD HH:mm:ss'),
        pageSize: 1000,
        pageNum: 1
      }
      const res = await playbackListApi(params)
      console.log(res)
      this.playbackList = res.data.data&&res.data.data.recordList.map(item=>{
        return {
          ...item,
          startTime: item.startTime.replace('T', ' '),
          endTime: item.endTime.replace('T', ' ')
        }
      })
      fetchCameraUrl(this.channelId).then(res=>{
        this.rightVideoUrl = res.data.data.flvHttps
        this.showRightVideo = true
      })
    },
    // 选择日期
    async dateChange(time){
      const params ={
        channelId: this.channelId,
        startTime: moment(time).format('YYYY-MM-DD')+' 00:00:00',
        endTime: moment(time).format('YYYY-MM-DD')+' 23:59:59',
        pageSize: 1000,
        pageNum: 1
      }
      const res = await playbackListApi(params)
      this.playbackList = res.data.data&&res.data.data.records.map(item=>{
        return {
          ...item,
          startTime: item.startTime.replace('T', ' '),
          endTime: item.endTime.replace('T', ' ')
        }
      })
    },
    /**
     * 获取回放列表
     */
    async getPlaybackList(data) {
      if (data) {
        this.rightVideo = data
      }
      const params = {
        channelId: this.rightVideo.deviceId,
        startDate: this.date,
        endDate: this.date,
      }
      const res = await getPlaybackList(params)
      this.playbackList = res.data.data.recordList
    },
    /**
     * 回放
     */
    async playback(data) {
      const params = {
        channelId: this.channelId,
        startTime: data.startTime,
        endTime: data.endTime,
      }
      this.showRightVideo = false
      const res = await playbackStartApi(params)
      this.rightVideoUrl = res.data.data.flvHttps
      this.showRightVideo = true
    },
    async chooseCamera(data) {
      this.loading = true
      const result = await liveAddress(data.deviceId)
      data.url = result.data.data ? result.data.data.flvHttps : ''
      this.screenVideoList.splice(this.videoIndex, 1, data)
      this.loading = false
      this.showDialog = false
    },
  },
}
</script>

<style lang="less">
.selectBox {
  .el-input__inner {
    font-size: 20px !important;
    width: 220px;
    background: rgb(17, 45, 80, 1) !important;
    border: 2px solid rgb(33, 88, 145, 1);
    color: #fff;
  }
  .el-range-separator {
    color: #fff;
  }
  .el-range-input {
    background: none !important;
    color: #fff;
  }
  .el-input__icon {
    opacity: 0;
  }
}
.el-picker-panel {
  background-color: rgb(17, 45, 80, 1) !important;
  border-color: rgb(14, 41, 75);
  color: #fff;
  /deep/ .normal {
    background-color: rgb(13, 39, 71) !important;
  }
}
  .popper__arrow{
   display: none !important;
  }
</style>
<style lang="less" scoped>
.filter {
  position: absolute;
  left: 40px;
  top: 104px;
  display: flex;
  align-items: center;
  cursor: pointer;
  color: #23a3fd;
  > div {
    margin-right: 15px;
  }
}
.video-list {
  background: #061224;
  font-size: 20px;
  .main {
    .left-part {
      position: absolute;
      left: 30px;
      top: 140px;
      width: 1320px;
      height: 880px;
      background: url('../../assets/vide_list_left_bg.png');
      background-size: 100% 100%;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      padding: 30px;
      box-sizing: border-box;
      .video-box {
        .video {
          border: 1px solid #226da2;
          position: relative;
        }
        .name {
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          text-align: center;
          margin: 12px 0 16px;
          color: #24a4ff;
        }
      }
    }
    .right-part {
      position: absolute;
      right: 30px;
      top: 140px;
      width: 540px;
      height: 880px;
      background: url('../../assets/vide_list_right_bg.png');
      background-size: 100% 100%;
      padding: 30px;
      box-sizing: border-box;
      .selectTime {
        .rili {
          right: 15px;
          top: 11px;
          width: 13px;
          height: 15px;
          position: absolute;
        }
        .selectBox {
          position: relative;
        }
        display: flex;
        align-items: center;
        margin-top: 20px;
      }
      .vedioList {
        margin-top: 25px;
        height: calc(420px + 302px);
        overflow: auto !important;
        .tabhead {
          font-size: 18px;
          font-family: PingFang SC;
          font-weight: 400;
          color: #9ed5ff;
          display: flex;
          align-items: center;
          height: 38px;
          background: RGBA(13, 28, 55, 0.8);
          > div:nth-child(1) {
            padding-left: 10px;
            box-sizing: border-box;
            width: 85%;
          }
          > div:nth-child(2) {
            width: 15%;
          }
        }
        .swiperList {
          height: calc(360px - calc(900px - 1080px) / 4);
          .tr:nth-child(even) {
            background: RGBA(13, 28, 55, 0.8);
          }
          .tr {
            padding-left: 5px;
            height: 40px;
            display: flex;
            align-items: center;
            font-size: 16px;
            .tiem {
              font-family: PingFang SC;
              font-weight: 400;
              color: #4da3cf;
              width: 76%;
            }
            .btnDiv {
              width: 24%;
              display: flex;
              align-items: center;
              justify-content: center;
              > div {
                width: 70px;
                height: 30px;
                font-size: 15px;
                font-family: PingFang SC;
                font-weight: 400;
                color: #4bfbff;
                line-height: 30px;
                text-align: center;
                background: url(../../assets/video_list_right_button.png)
                  no-repeat;
                background-position: center;
                background-size: 100% 100%;
              }
            }
          }
        }
      }
    }
  }
  .dialog {
    position: absolute;
    z-index: 2222;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 1920px;
    height: 1080px;
    background: rgba(0, 0, 0, 0.38);
    .content {
      position: relative;
      padding: 60px;
      box-sizing: border-box;
      width: 900px;
      height: 760px;
      background: url('../../assets/video_list_dialog_bg.png');
      background-size: 100% 100%;
      .title {
        width: 120px;
        height: 35px;
        position: relative;
        top: -10px;
      }
      .close {
        position: absolute;
        top: 60px;
        right: 70px;
        width: 18px;
        cursor: pointer;
      }
      .tablehead {
        margin-top: 20px;
        font-size: 18px;
        font-family: PingFang SC;
        font-weight: 400;
        color: #02c8ea;
        display: flex;
        align-items: center;
        height: 60px;
        background: #112347;
        > div {
          text-align: center;
        }
        > div:nth-child(1),
        > div:nth-child(2) {
          padding-left: 10px;
          box-sizing: border-box;
          width: 20%;
        }
        > div:nth-child(3) {
          width: 25%;
        }
        > div:nth-child(4),
        > div:nth-child(5) {
          width: 15%;
        }
      }
      .tablebody {
        height: 480px;
        overflow-y: scroll;
        .tr {
          display: flex;
          height: 60px;
          align-items: center;
          &:nth-child(even) {
            background: #0c1f3a;
          }
          &:nth-child(odd) {
            background: #09152a;
          }
          > div {
            text-align: center;
            font-size: 16px;
            color: #9ccfe2;
          }
          > div:nth-child(1),
          > div:nth-child(2) {
            padding-left: 10px;
            box-sizing: border-box;
            width: 20%;
          }
          > div:nth-child(3) {
            width: 25%;
          }
          > div:nth-child(4),
          > div:nth-child(5) {
            width: 15%;
          }
          .address {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
        .table-button {
          width: 66px;
          height: 30px;
          display: flex;
          justify-content: center;
          align-items: center;
          background: url('../../assets/video_list_right_button.png');
          background-size: 100% 100%;
          color: #4bfbff;
          cursor: pointer;
          margin: 0 auto;
        }
      }
    }
  }
}
</style>
