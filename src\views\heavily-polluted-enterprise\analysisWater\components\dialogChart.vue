<style lang="less" scoped>
.no-data {
  font-size: 0.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
<template>
  <div
    v-if="propData.value && propData.value.length > 0"
    :id="id"
    :style="{ height: height, width: width }"
  />
  <div v-else class="no-data" :style="{ height: height, width: width }">
    暂无数据
  </div>
</template>

<script lang="ts">
import * as echarts from 'echarts'
import { Component, Prop, Watch } from 'vue-property-decorator'
import { mixins } from 'vue-class-component'
import ResizeMixin from '@/components/Charts/mixins/resize'

@Component({
  name: 'analysisAirLineChartDashed'
})
export default class extends mixins(ResizeMixin) {
  // @Prop({ default: "chart" }) private className!: string;
  @Prop({ default: 'chart' }) private id!: string
  @Prop({ default: '200px' }) private width!: string
  @Prop({ default: '200px' }) private height!: string
  @Prop({ required: true }) private propData!: any
  @Prop({ required: false, default: 'rgba(14,156,255,1)' })
  private bgColor!: string
  @Prop({ required: false, default: false }) private smooth!: boolean
  @Prop({ required: false, default: true }) private bgColorState!: boolean
  @Prop({ required: false, default: '' }) private xText!: string
  @Prop({ default: ''}) private itemType!: string
  @Watch('propData', { immediate: true, deep: true }) public onMsgChanged(
    newValue: any,
    oldValue: any
  ) {
    if (this.propData) {
      if (this.chart) {
        this.chart.clear()
        this.chart.dispose()
        this.chart = null
      }
      this.$nextTick(() => {
        this.initChart()
      })
    }
  }
  mounted() {
    if (this.propData) {
      this.initChart()
    }
  }
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.clear()
    this.chart.dispose()
    this.chart = null
  }
  private colorsList:any = {
        余氯量: '#3BB66F',
        化学需氧量: '#F0A727',
        pH: '#61A2DA',
        水温: '#9B72BE',
        流量: '#D87115',
        '悬浮物(SS)': '#3554BA',
        总氮: '#2B9CAD',
        总磷: '#31247F',
        氨氮: '#61A2DA'
      }
   private unitList:any = {
        余氯量: 'mg/L',
        化学需氧量: 'mg/L',
        pH: '',
        水温: '℃',
        流量: 'm³',
        '悬浮物(SS)': 'mg/L',
        总氮: 'mg/L',
        总磷: 'mg/L',
        氨氮: 'mg/L'
      }
  private initChart() {
    console.log('this.propData', this.propData)
    if (this.chart === null || this.chart === undefined) {
      const chartDom = document.getElementById(this.id) as HTMLDivElement
      if (!chartDom) return
      this.chart = echarts.init(chartDom)
    }
    if(!this.propData.value || this.propData.value.length === 0) return
    this.chart.setOption({
      backgroundColor: 'transparent',
      color:[this.colorsList[this.itemType]],
      legend: {
        right: 30,
        textStyle: {
          color: '#7BB7ED',
          fontSize: 12
        },
        itemHeight: 12
      },
      grid: {
        // height: '80%',
        left: '5%',
        top: '5%',
        right: '5%',
        bottom: '5%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        name: '',
        nameTextStyle: {
          fontSize: 20,
          color: '#fff',
          lineHeight: -30,
          align: 'center',
          verticalAlign: 'bottom'
        },
        data: this.propData.x,
        axisLabel: {
          textStyle: {
            fontSize: 12,
            color: 'white'
          }
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        }
      },
      yAxis: {
        type: 'value',
        // name: '单位：mg/m³',
        name: this.unitList[this.itemType],
        min: 0,
        nameTextStyle: {
          color: '#fff',
          shadowOffsetX: 50
          // align: "center"
        },
        axisLabel: {
          textStyle: {
            fontSize: 12,
            color: 'white'
          }
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        splitLine: {
          lineStyle: {
            // type: "dashed"
            color: 'RGBA(2, 39, 75, 1)'
          }
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            backgroundColor: '#6a7985'
          }
        }
      },
      series: [
        {
          // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
          // @ts-ignore
          name: this.itemType,
          data: this.propData.value.map((it: any) => {
            //  0 大于  1 范围外 2 范围内 3 小于 (超标)
            const {
              valueType, alarmMaxValue, alarmMinValue, alarmValue
            } = this.propData
            let isAlarm = false
            if (valueType === 0 && this.isValue(alarmValue)) {
              if (this.isValue(it) && it > alarmValue) {
                isAlarm = true
              } else {
                isAlarm = false
              }
            } else if (valueType === 0 && this.isNotValue(alarmValue)) {
              isAlarm = false
            }
            if (valueType === 1 && this.isValue(alarmMinValue) && this.isValue(alarmMaxValue)) {
              if (this.isValue(it) && (it < alarmMinValue || it > alarmMaxValue)) {
                isAlarm = true
              } else {
                isAlarm = false
              }
            } else if (
              valueType === 1 && (this.isNotValue(alarmMinValue) || this.isNotValue(alarmMaxValue))
            ) {
              isAlarm = false
            }
            if (valueType === 2 && this.isValue(alarmMinValue) && this.isValue(alarmMinValue)) {
              if (this.isValue(it) && (it > alarmMinValue && it < alarmMaxValue)) {
                isAlarm = true
              } else {
                isAlarm = false
              }
            } else if (
              valueType === 2 && (this.isValue(alarmMinValue) || this.isNotValue(alarmMaxValue))
            ) {
              isAlarm = false
            }
            if (valueType === 3 && this.isValue(alarmValue)) {
              if (this.isValue(it) && it < alarmValue) {
                isAlarm = true
              } else {
                isAlarm = false
              }
            } else if (valueType === 3 && this.isNotValue(alarmValue)) {
              isAlarm = false
            }
            return {
              value: it,
              itemStyle: {
                color: isAlarm ? 'red'
                  : this.colorsList[this.itemType],
                borderWidth: 2
              }
            }
          }),
          type: 'line',
          smooth: this.smooth,
          lineStyle: {
            color: this.colorsList[this.itemType] //改变折线颜色
          },
          symbol: 'circle',
          symbolSize: 5,
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 1,
                  color: 'transparent' // 0% 处的颜色
                },
                {
                  offset: 0,
                  color: this.colorsList[this.itemType] // 100% 处的颜色
                }
              ],
              global: false
            },
            shadowColor: 'rgba(0,85,250,0)',
            shadowBlur: 20
          }
        }
      ]
    } as EChartOption<EChartOption>)
  }
  // 判断是否有值 或者 0 或者 '0'
  private isValue(value: any) {
    return value || value === 0 || value === '0'
  }
  private isNotValue(value: any) {
    return value === null || value === undefined
  }
}
</script>



