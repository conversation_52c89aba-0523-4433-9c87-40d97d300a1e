import request from "@/utils/request";
import { AxiosPromise } from "axios";

/**
 * @method dataFileList
 * @param {type} data 说明
 * @description 指定日期数据
 */
export function dataFileList(params:any): AxiosPromise<any> {
  return request({
    url: `/air/lidar-scanning-record/dataFileList`,
    method: "get",
    params
  });
}
/**
 * @method getByGroupFileName
 * @param {number} 
 * @description 初始获取雷达图
 */
export function getByGroupFileName(params:any): AxiosPromise<any> {
  return request({
    url: `/air/lidar-scanning-record/getByGroupFileName`,
    method: "get",
    params
  });
}
/**
 * @method getByAngleAndGroupFileName
 * @param {number} 
 * @description 获取雷达角度图
 */
export function getByAngleAndGroupFileName(params:any): AxiosPromise<any> {
  return request({
    url: `/air/lidar-scanning-record/getByAngleAndGroupFileName`,
    method: "get",
    params
  });
}
/**
 * @method findPointData
 * @param {number} 
 * @description 获取雷达点位数据
 */
export function findPointData(params:any): AxiosPromise<any> {
  return request({
    url: `/air/bigData/lidar-scanning-record/findPoint`,
    method: "get",
    params
  });
}
/**
 * @method randaExistDate
 * @param {number} 
 * @description 获取有雷达数据的日期
 */
export function randaExistDate(params:any): AxiosPromise<any> {
  return request({
    url: `/air/bigData/lidar-scanning-record/dataFileList`,
    method: "get",
    params
  });
}
/**
 * @method randaListStation
 * @param {number} 
 * @description 获取有雷达点位指纹库
 */
export function randaListStation(params:any): AxiosPromise<any> {
  return request({
    url: `/air/bigData/lidar-scanning-record/listStation`,
    method: "get",
    params
  });
}
/**
 * @method randaMonitorTrend
 * @param {number} 
 * @description 获取有雷达监测趋势
 */
export function randaMonitorTrend(params:any): AxiosPromise<any> {
  return request({
    url: `/air/bigData/lidar-scanning-record/monitorTrend`,
    method: "get",
    params
  });
}
/**
 * @method randaAlarmCount
 * @param {number} 
 * @description 获取雷达告警历史
 */
export function randaAlarmCount(params:any): AxiosPromise<any> {
  return request({
    url: `/air/bigData/lidar-scanning-record/alarmCount`,
    method: "get",
    params
  });
}
/**
 * @method getStationMonitorItem
 * @param {number} 
 * @description 获取雷达指纹库详情弹窗
 */
export function getStationMonitorItem(params:any): AxiosPromise<any> {
  return request({
    url: `/air/bigData/lidar-scanning-record/getStationMonitorItem`,
    method: "get",
    params
  });
}
/**
 * @method getLastView
 * @param {number} 
 * @description 获取最新的雷达图new
 */
export function getLastView(params:any): AxiosPromise<any> {
  return request({
    url: `/air/bigData/lidar-scanning-record/getLastView`,
    method: "get",
    params
  });
}