<style lang="less" scoped>
p {
  margin-bottom: 0 !important;
}
.emergency-command {
  width: 100%;
  height: 100%;
  // background: url("../../assets/emergencyCommand.png") no-repeat;
  background-size: 100%;
  position: relative;
  .emergency-command-content {
    position: relative;
    .center-map {
      width: 100%;
      height: calc(1080px - 0.94rem);
    }
    .title {
      font-size: 0.2rem;
      font-family: Source Han Sans CN;
      font-weight: 500;
      text-shadow: 0 0 5px blue, 0 0 5px blue;
    }
    .emergency-command-bottom {
      transition: 1.5s;
      position: absolute;
      bottom: 0;
      height: 4rem;
      width: 100%;
      background-image: url("../../assets/bottomBg.png");
      background-size: 100% 100%;
      display: flex;
      justify-content: space-between;
      padding-left: 0.25rem;
      padding-right: 0.5rem;
      .content {
        width: 13.5rem;
        margin-left: 0.5rem;
        height: 4rem;
        .timeLine {
          display: flex;
          justify-content: space-between;
          .timeLine-text {
            font-size: 0.16rem;
            color: rgba(255, 255, 255, 1);
            width: 1.8rem;
            text-align: right;
            > div {
              line-height: 0.25rem;
            }
          }
          .timeLine-text-line {
            width: calc(100% - 1.8rem);
            overflow: hidden;
            .line-one {
              height: 0.02rem;
              background-color: #2188ff;
              width: 100%;
              position: absolute;
              top: -1rem;
            }
            .line-two {
              height: 0.02rem;
              background-color: #2188ff;
              width: 100%;
              position: absolute;
              top: -0.75rem;
            }
            .line-three {
              height: 0.02rem;
              background-color: #2188ff;
              width: 100%;
              position: absolute;
              top: -0.5rem;
            }
            .line-four {
              height: 0.02rem;
              background-color: #2188ff;
              width: 100%;
              position: absolute;
              top: -0.25rem;
            }
            .line-five {
              height: 2rem;
              background-color: #ffffff;
              width: 0.01rem;
              position: absolute;
              top: -1.1rem;
              z-index: 9999;
              right: 0.33rem;
            }
            .line-scrool {
              height: 1.5rem;
              background: transparent;
              width: 100%;
              position: absolute;
              top: -1rem;
            }
          }
        }
        .emergency-chart {
          margin-left: 1.8rem;
          .radio-type {
            margin-top: 0.15rem;
          }
        }
      }
      .task-detail {
        box-shadow: 0rem 0rem 0.15rem #2281a5 inset;
        width: 3.9rem;
        height: 2.8rem;
        background: rgba(13, 29, 74, 0.66);
        border: 1px solid rgba(34, 129, 165, 1);
        border-radius: 0.15rem;
        padding: 0.25rem;
        .task-detail-top {
          border-bottom: 1px solid rgba(12, 97, 196, 1);
          padding-bottom: 0.05rem;
          margin-bottom: 0.13rem;
          display: flex;
          justify-content: space-between;
          .task-detail-left {
            > :nth-of-type(1) {
              img {
                width: 0.25rem;
                height: 0.23rem;
                margin-right: 0.1rem;
              }
              font-size: 0.22rem;
              display: flex;
              align-items: center;
              margin-bottom: 0.07rem;
            }
            > :nth-of-type(2) {
              color: #cccccc;
              font-size: 0.18rem;
              padding-left: 0.1rem;
            }
          }
          .task-detail-right {
            img {
              width: 0.72rem;
              height: 0.59rem;
            }
          }
        }
        .task-detail-bottom {
          font-size: 0.2rem;
          span {
            color: #09f1ff;
          }
          line-height: 0.35rem;
          > :nth-of-type(1) {
            margin-bottom: 0.15rem;
          }
        }
      }
    }
    .emergency-command-left {
      transition: 1.5s;
      position: absolute;
      left: 0.3rem;
      top: 0.2rem;
      .content-detail {
        padding: 0.25rem;
        background-image: url(../../assets/left-kuang.png);
        background-size: 100% 100%;
        width: 4.09rem;
        height: 3rem;
        font-size: 0.18rem;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        > :nth-of-type(1) {
          > div {
            margin-bottom: 0.03rem;
          }
        }
        > :nth-of-type(2) {
          background-image: url(../../assets/bg-color.png);
          background-size: 100% 100%;
          height: 0.41rem;
          line-height: 0.41rem;
          text-align: center;
        }
      }
    }
    .emergency-command-right {
      transition: 1.5s;
      position: absolute;
      right: 0.3rem;
      top: 0.2rem;
      .content-detail {
        padding: 0.25rem;
        background-image: url(../../assets/left-kuang.png);
        background-size: 100% 100%;
        width: 4.09rem;
        height: 3rem;
        font-size: 0.18rem;
        > div {
          margin-bottom: 0.1rem;
        }
      }
    }
    .quanpin {
      position: absolute;
      top: -0.88rem;
      right: 0.5rem;
      z-index: 99;
    }
    .transform-left {
      transform: scale(0); // translateX(-4rem)
      transition: 1.5s;
      opacity: 0;
      visibility: hidden;
    }
    .transform-right {
      transform: scale(0); // translateX(4rem)
      transition: 1.5s;
      opacity: 0;
      visibility: hidden;
    }
    .transform-bottom {
      transform: scale(0); // translateY(4rem)
      transition: 1.5s;
      opacity: 0;
      visibility: hidden;
    }
  }
  .content-text {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }
  .content-one {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    line-height: 0.28rem;
  }
  .content-two {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    line-height: 0.28rem;
  }
}
.yjzhbg {
  position: absolute;
  background-image: url("../../assets/yjzhbg.png");
  background-size: 100% 100%;
  width: 7.23rem;
  height: 4.44rem;
  z-index: 10;
  left: 50%;
  right: 50%;
  transform: translate(-50%, 10%) scale(0.8);
  transition: 1.5s;
  padding: 1.14rem 0.6rem 0 0.6rem;
  > div {
    display: flex;
    justify-content: space-between;
    position: relative;
  }

  .right {
    flex: 1;
    position: relative;
    overflow: hidden;

    .right-top {
      font-size: 0.28rem;
      border-bottom: 1px solid #0c61c4;
      img {
        width: 0.3rem;
        height: 0.28rem;
      }
      > div:nth-of-type(1) {
        display: flex;
        align-items: center;
      }
      .right-time {
        color: #cccccc;
        font-size: 0.22rem;
        margin-bottom: 0.07rem;
      }
    }
    .right-bottom {
      font-size: 0.26rem;
      > div {
        margin-top: 0.1rem;
      }
      span {
        color: #09f1ff;
      }
      > div:nth-of-type(1) {
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
      }
      > div:nth-of-type(2) {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
  .right-close {
    position: absolute;
    top: -0.5rem;
    right: -0.15rem;
  }
  .right-dataTime {
    font-size: 0.5rem;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: rgba(253, 21, 21, 1);
    display: flex;
    justify-content: center;
  }
  .left {
    margin-right: 0.32rem;
    width: 1.9rem;
    height: 2.2rem;
    background: rgba(42, 79, 173, 1);
    position: relative;
  }
  .yjzhbg-swiper {
    width: 1.9rem;
    height: 2.2rem;
  }
  .right-arrow {
    position: absolute;
    cursor: pointer;
    width: 0.24rem;
    height: 0.3rem;
    background: #99abd8;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 99;
  }
  .left-arrow {
    position: absolute;
    cursor: pointer;
    width: 0.24rem;
    height: 0.3rem;
    background: #99abd8;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 99;
  }
  .svg-arrow {
    width: 0.25rem;
    height: 0.25rem;
  }
}
.yjzhbg-hide {
  transform: translate(-50%, 10%) scale(0);
  transition: 1.5s;
}
</style>
<style lang="less">
.emergency-command {
  .timeLine {
    overflow: hidden;
    .tttt {
      // background: #0b3684;
      height: 0.5rem;
      cursor: move;
      position: relative;
    }

    #times {
      width: 100%;
      height: 0.5rem;
      // background-color: #0b3684;
      font-size: 0.1rem;
      color: #6aa1cb;
      margin-top: 1.1rem;
      -moz-user-select: none;
      /*火狐*/
      -webkit-user-select: none;
      /*webkit浏览器*/
      -ms-user-select: none;
      /*IE10*/
      -khtml-user-select: none;
      /*早期浏览器*/
      user-select: none;
      padding-left: 0.1rem;
    }

    #time {
      height: 0.02rem;
      // background: #b4f7fd;
      position: relative;
      cursor: pointer;
      top: 0.18rem;
      // left: -0.8rem;
    }

    #time li {
      float: left;
      background: #b4f7fd;
      height: 0.06rem;
      width: 0.01rem;
      position: absolute;
      list-style: none;
    }

    #time > .time1 {
      width: 0.12rem;
      height: 0.12rem;
      background: linear-gradient(
        0deg,
        rgba(0, 150, 81, 1),
        rgba(16, 162, 95, 1),
        rgba(0, 208, 112, 1)
      );
      border-radius: 50%;
      z-index: 10;
      position: absolute;
    }
    #time > .time1-active {
      width: 0.16rem;
      height: 0.16rem;
      background: linear-gradient(
        0deg,
        rgba(242, 142, 38, 1),
        rgba(253, 100, 79, 1)
      );
      border-radius: 50%;
      z-index: 10;
      position: absolute;
    }
  }
  .content {
    .water-monitor-tab {
      height: 0.3rem;
      line-height: 0.3rem;
      font-size: 0.16rem;
      color: rgba(255, 255, 255, 1);
      background: rgba(12, 39, 92, 1);
      border-radius: 0;
      padding: 0 0.1rem;
      border: none;
    }
    .ant-radio-group {
      width: 100%;
      display: flex;
      justify-content: space-around;
      background-color: #0f245e;
    }
    .ant-radio-group-solid
      .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
      background: #0084ff;
      border: none;
      padding: 0 1rem;
    }
    .ant-radio-button-wrapper:not(:first-child)::before {
      background: transparent;
    }
    .ant-radio-button-wrapper-checked::before {
      background: transparent !important;
    }
    .ant-radio-button-wrapper-checked {
      z-index: 1;
      border-color: #0084ff !important;
      -webkit-box-shadow: -1px 0 0 0 #0084ff;
      box-shadow: -1px 0 0 0 #0084ff;
    }
    .ant-radio-group-small .ant-radio-button-wrapper {
      // width: 14.28%;
      // width: 16.66%;
      text-align: center;
    }
    .ant-radio-group {
      display: flex;
    }
    .ant-radio-group-small .ant-radio-button-wrapper {
      padding: 0;
    }
  }
}
</style>
<template>
  <section>
    <section class="emergency-command">
      <section class="emergency-command-content">
        <section class="yjzhbg" :class="{ 'yjzhbg-hide': alertState }">
          <div>
            <img
              src="../../assets/<EMAIL>"
              class="right-close"
              @click="alertState = true"
            />
            <div class="left">
              <!-- 左侧箭头 -->
              <div class="left-arrow">
                <svgicon
                  name="left"
                  color="#FFFFFF"
                  class="svg-arrow"
                ></svgicon>
              </div>
              <swiper class="yjzhbg-swiper" :options="swiperOption">
                <swiper-slide v-for="(item, i) in 2" :key="i">
                  <img
                    src="https://oss-chengdu.vankeytech.com:9000/vankeytech-ep/emergency/annex/0fc7b5ca-7ddc-4330-8cab-4040265767c8.jpg"
                  />
                </swiper-slide>
              </swiper>
              <!-- 右侧箭头 -->
              <div class="right-arrow">
                <svgicon
                  name="right"
                  color="#FFFFFF"
                  class="svg-arrow"
                ></svgicon>
              </div>
            </div>
            <div class="right">
              <div class="right-top">
                <div>
                  <img src="../../assets/<EMAIL>" alt="" />农业和水务局
                </div>
                <div class="right-time">2020-03-27 20:01</div>
              </div>
              <div class="right-bottom">
                <div title="">
                  <span>任务内容：</span>查看附近水质监测站点 数据指标是否正常。
                </div>
                <div title=""><span>反馈内容：</span>数据正常。</div>
              </div>
            </div>
          </div>
          <div class="right-dataTime">{{ alertTime }}S</div>
        </section>
        <section class="center-map">
          <!-- <keep-alive> -->
            <gao-de-map
              :mapStyle="mapStyle"
              :enterType="8"
              :mapZoom="13.3"
              :viewMode="'2D'"
              :emergencyList="emergencyList"
              :emergencyStationList="emergencyStationList"
              :reportDetails="reportDetails"
            ></gao-de-map>
          <!-- </keep-alive> -->
        </section>
        <section
          class="emergency-command-bottom"
          :class="{ 'transform-bottom': displayState }"
        >
          <section class="content">
            <div class="timeLine">
              <div class="timeLine-text">
                <div>生态环境局</div>
                <div>综合行政执法局</div>
                <div>农业和水务局</div>
                <div>住房建设和交通运输局</div>
              </div>
              <div class="timeLine-text-line">
                <div id="times">
                  <div class="tttt">
                    <ul id="time"></ul>
                    <div class="line-one"></div>
                    <div class="line-two"></div>
                    <div class="line-three"></div>
                    <div class="line-four"></div>
                    <div class="line-five"></div>
                    <div class="line-scrool"></div>
                  </div>
                </div>
              </div>
            </div>
            <!-- 空气 -->
            <div class="emergency-chart" v-if="siteType == 1">
              <LineEmergencyCommand
                :id="'Command-emergency'"
                :width="'100%'"
                :height="'1.75rem'"
                :propData="airData"
                :smooth="true"
              />
              <div class="radio-type">
                <a-radio-group
                  v-model="airType"
                  size="small"
                  buttonStyle="solid"
                  style="width:100%;"
                  @change="airTypeChange"
                >
                  <a-radio-button
                    v-for="item in airTypes"
                    :key="item.code"
                    :value="item.code"
                    :class="{
                      'water-monitor-tab': true
                    }"
                    >{{ item.name }}</a-radio-button
                  >
                </a-radio-group>
              </div>
            </div>
            <!-- 水 -->
            <div class="emergency-chart" v-if="siteType == 2">
              <LineEmergencyCommand
                :id="'Command-emergency111'"
                :width="'100%'"
                :height="'1.75rem'"
                :propData="waterMonitor"
                :smooth="true"
              />
              <div class="radio-type">
                <a-radio-group
                  v-model="airType"
                  size="small"
                  buttonStyle="solid"
                  style="width:100%;"
                  @change="waterMonitorSelectChange"
                >
                  <a-radio-button
                    v-for="(item, index) in airTypes"
                    :key="index"
                    :value="`${item.code}`"
                    class="water-monitor-tab"
                    >{{ item.name }}</a-radio-button
                  >
                </a-radio-group>
              </div>
            </div>
            <!-- 重污 -->
            <div class="emergency-chart" v-if="siteType == 3">
              <LineEmergencyCommand
                :id="'Command-emergency2222'"
                :width="'100%'"
                :height="'1.75rem'"
                :propData="airData"
                :smooth="true"
              />
            </div>
          </section>
          <section class="task-detail" v-if="departmentName != ''">
            <div class="task-detail-top">
              <div class="task-detail-left">
                <div>
                  <img src="@/assets/<EMAIL>" alt="" />
                  <span>{{ departmentName ? departmentName : "" }}</span>
                </div>
                <div>{{ creatTime ? creatTime : "" }}</div>
              </div>
              <div class="task-detail-right">
                <!-- <img src="@/assets/<EMAIL>" alt="" /> -->
              </div>
            </div>
            <div class="task-detail-bottom">
              <div class="content-one">
                <span>任务内容：</span
                >{{ content && content != "null" ? content : "" }}
              </div>
              <div class="content-two">
                <span>反馈内容：</span>{{ replyContent ? replyContent : "" }}
              </div>
            </div>
          </section>
        </section>
        <section
          class="emergency-command-left"
          :class="{ 'transform-left': displayState }"
        >
          <div class="title title-top">
            <span>{{ `上报详情` }}</span>
          </div>
          <div class="sub-title">
            <img
              src="@/assets/biaoti.png"
              class="title-img"
              style="width: 4rem;"
            />
          </div>
          <div class="content-detail">
            <div>
              <div class="content-text">
                报告内容：<span
                  style="font-size:0.18rem;"
                  :title="reportDetails.content ? reportDetails.content : ''"
                  >{{
                    reportDetails.content ? reportDetails.content : ""
                  }}</span
                >
              </div>
              <div>
                报告单位：{{
                  reportDetails.reportDept ? reportDetails.reportDept : ""
                }}
              </div>
              <div>
                报告时间：{{
                  reportDetails.createTime ? reportDetails.createTime : ""
                }}
              </div>
              <div>
                报告分类：{{
                  reportDetails.reportType ? reportDetails.reportType : ""
                }}
              </div>
              <div
                style="overflow: hidden;text-overflow: ellipsis;white-space: nowrap;"
                :title="reportDetails.address ? reportDetails.address : ''"
              >
                事故位置：{{
                  reportDetails.address ? reportDetails.address : ""
                }}
              </div>
            </div>
            <div>
              {{
                reportDetails.levelId ? reportDetails.levelId : ""
              }}级突发环境污染事件
            </div>
          </div>
        </section>
        <section
          class="emergency-command-right"
          :class="{ 'transform-right': displayState }"
        >
          <div class="title title-top">
            <span>{{ `任务概览` }}</span>
          </div>
          <div class="sub-title">
            <img
              src="@/assets/biaoti.png"
              class="title-img"
              style="width: 4rem;"
            />
          </div>
          <div class="content-detail">
            <div>
              任务耗时：{{
                taskOverview.useTime ? taskOverview.useTime : ""
              }}小时
            </div>
            <div>
              下发任务：{{ taskOverview.taskNum ? taskOverview.taskNum : "" }}个
            </div>
            <div>
              完成任务：{{
                taskOverview.completeNum ? taskOverview.completeNum : ""
              }}个
            </div>
            <div>
              参与单位：{{
                taskOverview.departmentName ? taskOverview.departmentName : ""
              }}
            </div>
            <div>
              协同单位：{{
                taskOverview.cooperatingUnit ? taskOverview.cooperatingUnit : ""
              }}
            </div>
          </div>
        </section>
        <section class="quanpin" @click="fullScreen">
          <img src="@/assets/quanping.png" alt />
        </section>
      </section>
    </section>
  </section>
</template>

<script lang="ts">
/* eslint-disable @typescript-eslint/ban-ts-ignore */
//@ts-ignore
import AMap from "AMap";
import { Component, Vue, Watch } from "vue-property-decorator";
import GaoDeMap from "@/components/GaoDeMap/index.vue";
import { Table, Icon, Empty, Avatar, Radio } from "ant-design-vue";
import "video.js/dist/video-js.css";
import "videojs-contrib-hls";
import "videojs-flash";
import jq from "jquery";
import { aqiQualityTrend } from "@/api/air";
import { getMonitorItemDetailRecord } from "@/api/water";
import moment from "moment";
import {
  getReportDetails,
  getTaskOverview,
  getTaskRemark,
  getEmergencyStationList
} from "@/api/emergency";
import {
  getEnforcement,
  getEnforcementTaskId,
  getAqiQuality,
  getTwentyFourHour
} from "@/api/task-management";
import LineEmergencyCommand from "@/components/Charts/LineEmergencyCommand.vue";
import { Swiper, SwiperSlide } from "vue-awesome-swiper";
@Component({
  name: "EmergencyCommand",
  components: {
    GaoDeMap,
    ATable: Table,
    AIcon: Icon,
    AEmpty: Empty,
    AAvatar: Avatar,
    LineEmergencyCommand,
    ARadioGroup: Radio.Group,
    ARadioButton: Radio.Button,
    Swiper,
    SwiperSlide
  }
})
export default class extends Vue {
  @Watch("airType", { immediate: true, deep: true })
  public onAirType(newValue: any, oldValue: any) {
    if (newValue) {
      if (this.siteType == 2) {
        this.getMonitorItemDetailRecord();
      } else if (this.siteType == 1) {
        this.getAqiTrend();
      }
    }
  }
  @Watch("alertState", { immediate: true, deep: true })
  public onAlertState(newValue: any, oldValue: any) {
    if (newValue) {
      console.log(newValue);
      clearInterval(this.alertTimers);
      this.alertTime = 30;
    }
  }
  private swiperOption: any = {
    loop: true,
    loopFillGroupWithBlank: true,
    pagination: {
      el: ".swiper-pagination",
      clickable: true
    },
    autoplay: {
      delay: 3000,
      disableOnInteraction: false
    },
    navigation: {
      nextEl: ".right-arrow",
      prevEl: ".left-arrow"
    }
  };
  // private mapStyle = "amap://styles/0739a6632d849ae1b5e6f1c52f7d12c3";
  private mapStyle = "amap://styles/0739a6632d849ae1b5e6f1c52f7d12c3";
  private emergencyList: any = [];
  private alertState = true;
  private alertTime = 30;
  private taskList: any = [
    {
      timec: 1564503166555,
      time: 1564533166555,
      top: "-1.235rem"
    },
    {
      timec: 1564503166555,
      time: 1564538366555,
      top: "-0.985rem"
    },
    {
      timec: 1564503166555,
      time: 1564536166555,
      top: "-0.48rem"
    },
    {
      timec: 1564503166555,
      time: 1564535166555,
      top: "-0.735rem"
    }
  ];
  private alertTimers: any = null;
  mounted() {
    this.getEmergencyStationList();
    if (!this.alertState) {
      this.alertTimers = setInterval(() => {
        this.alertTime -= 1;
        if (this.alertTime == 0) {
          clearInterval(this.alertTimers);
          this.alertState = true;
          this.alertTime = 30;
        }
      }, 1000);
    }
    (window as any)._that = this;
    this.getReportDetails();
    this.getTaskOverview();
    this.getTaskRemark();
    // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
    //@ts-ignore
    // eslint-disable-next-line no-undef
    this.$bus.on("stationType", (res: any) => {
      this.displayState = false;
      this.siteType = res[0].type;
      this.stationId = res[0].stationId;
      if (this.siteType == 1) {
        this.airTypes = [
          {
            code: "102",
            name: "O₃"
          },
          {
            code: "101",
            name: "NO₂"
          },
          {
            code: "104",
            name: "PM₁₀"
          },
          {
            code: "105",
            name: "PM₂.₅"
          },
          {
            code: "100",
            name: "SO₂"
          },
          {
            code: "103",
            name: "CO"
          }
        ];
      } else if (this.siteType == 2) {
        this.airTypes = [
          {
            code: "002",
            name: "氨氮"
          },
          {
            code: "003",
            name: "COD"
          },
          {
            code: "004",
            name: "总磷"
          },
          {
            code: "005",
            name: "挥发酚"
          }
        ];
      }
      if (this.siteType == 2) {
        this.airType = this.airTypes[0].code;
        this.getMonitorItemDetailRecord();
      } else if (this.siteType == 1) {
        this.getAqiTrend();
      } else if (this.siteType == 3) {
        this.getTwentyFourHour();
      }
    });
    this.timer = setInterval(() => {
      this.getReportDetails();
      this.getTaskOverview();
      this.getTaskRemark();
      if (this.siteType == 1) {
        this.airTypes = [
          {
            code: "102",
            name: "O₃"
          },
          {
            code: "101",
            name: "NO₂"
          },
          {
            code: "104",
            name: "PM₁₀"
          },
          {
            code: "105",
            name: "PM₂.₅"
          },
          {
            code: "100",
            name: "SO₂"
          },
          {
            code: "103",
            name: "CO"
          }
        ];
      } else if (this.siteType == 2) {
        this.airTypes = [
          {
            code: "002",
            name: "氨氮"
          },
          {
            code: "003",
            name: "COD"
          },
          {
            code: "004",
            name: "总磷"
          },
          {
            code: "005",
            name: "挥发酚"
          }
        ];
      }
      if (this.siteType == 2) {
        this.airType = this.airTypes[0].code;
        this.getMonitorItemDetailRecord();
      } else if (this.siteType == 1) {
        this.getAqiTrend();
      } else if (this.siteType == 3) {
        this.getTwentyFourHour();
      }
    }, 15 * 60 * 1000);
    // setInterval(() => {
    //   this.getTaskRemark();
    // }, 1500);
  }
  beforeDestroy() {
    clearInterval(this.timer);
    clearInterval(this.alertTimers);
    // @ts-ignore
    this.$bus.off("stationType")
    (window as any)._that = null;
  }
  private timer: any = null;
  private stationId: any = "";
  private siteType: any = 1;
  private htmlLoad(startTime: any, endTime: any) {
    let _move = false;
    let _x: any;
    jq(".tttt")
      .click(function() {
        // 1;
      })
      .mousedown(function(e: any) {
        _move = true;
        _x = e.pageX - parseInt(jq("#time").css("left"));
        jq("#time").fadeTo(20, 1);
      });
    jq(document)
      .mousemove(function(e: any) {
        if (_move) {
          const x = e.pageX - _x;
          jq("#time").css({
            left: x
          });
          if (x > 0) {
            jq("#time").css({
              left: 0
            });
          } else if (jq("#time").width() - jq("#times").width() + x < 0) {
            jq("#time").css({
              left: -(jq("#time").width() - jq("#times").width())
            });
          }
        }
      })
      .mouseup(function() {
        _move = false;
        jq(".tttt").fadeTo("fast", 1);
      });
    this.time(startTime, endTime);
  }
  private time(hour: any, endTime: any) {
    const date = endTime;
    const timess = (date - hour) / 1000 / 60;
    for (let i = 0; i < Math.floor(timess); i++) {
      let time1: any = `<li style='left:${i * 0.04}rem;'></li>`;
      const house: any =
        new Date(hour + i * 60000).getHours() >= 10
          ? new Date(hour + i * 60000).getHours()
          : "0" + new Date(hour + i * 60000).getHours();
      const minutes: any =
        new Date(hour + i * 60000).getMinutes() >= 10
          ? new Date(hour + i * 60000).getMinutes()
          : "0" + new Date(hour + i * 60000).getMinutes();
      const sxsx = house + ":" + minutes;
      if (i % 30 == 0) {
        time1 = `<li style='left:${i *
          0.04}rem;top:-0.04rem;height:0.2rem;line-height:0.5rem;text-indent:-0.04rem;'>${sxsx}</li>`;
      }
      jq("#time").append(time1);
    }
    jq("#time").css({
      width: Math.floor(timess) * 0.04 + "rem"
    });
  }
  private addtimes(data: any) {
    const str: any = `'${data.departmentName}---${data.time}---${data.content}---${data.replyContent}'`;
    const timess: any = Math.floor((data.time - data.timec) / 1000 / 60);
    const time1: any = `<li class='time1' style='left:${timess * 0.04}rem;top:${
      data.top
    }' onclick=taskDetail(${str})></li>`;
    jq("#time").append(time1);
    jq("#time").css({
      left: "-" + (timess * 0.04 - 11.2) + "rem"
    });
  }
  // 空气质量趋势
  private airData: any = {
    bottomList: [],
    dataList: [],
    unit: "",
    colorType: ""
  };
  // 空气质量趋势type
  private airTypes: any[] = [
    {
      code: "102",
      name: "O₃"
    },
    {
      code: "101",
      name: "NO₂"
    },
    {
      code: "104",
      name: "PM₁₀"
    },
    {
      code: "105",
      name: "PM₂.₅"
    },
    {
      code: "100",
      name: "SO₂"
    },
    {
      code: "103",
      name: "CO"
    }
  ];
  private airType = "102";
  // 24小时空气质趋势
  private getAqiTrend() {
    getAqiQuality(this.stationId, this.airType).then((res: any) => {
      const airData: any = {
        bottomList: [],
        dataList: [],
        unit: "",
        colorType: ""
      };
      for (const item in res.data.data) {
        airData.bottomList.push(item.replace("_", "") + "时");
        airData.dataList.push(res.data.data[item]);
        if (this.airType == "103") {
          airData.unit = "mg/m³";
        } else {
          airData.unit = "μg/m³";
        }
        if (this.airType == "100") {
          airData.colorType = "SO2";
        } else if (this.airType == "101") {
          airData.colorType = "NO2";
        } else if (this.airType == "102") {
          airData.colorType = "O3";
        } else if (this.airType == "103") {
          airData.colorType = "CO";
        } else if (this.airType == "104") {
          airData.colorType = "PM10";
        } else if (this.airType == "105") {
          airData.colorType = "PM25";
        }
      }
      this.airData = airData;
    });
  }
  // 切换标签
  private airTypeChange(e: any) {
    this.airType = e.target.value;
  }
  private displayState = false;
  private fullScreen() {
    this.displayState = !this.displayState;
  }
  // 上报详情
  private reportDetails: any = {};
  private getReportDetails() {
    getReportDetails().then((res: any) => {
      if (res.data.data.levelId == 1) {
        res.data.data.levelId = "一";
      } else if (res.data.data.levelId == 2) {
        res.data.data.levelId = "二";
      } else if (res.data.data.levelId == 3) {
        res.data.data.levelId = "三";
      } else if (res.data.data.levelId == 4) {
        res.data.data.levelId = "四";
      }
      const geocoder = new AMap.Geocoder({
        city: "全国"
      });
      geocoder.getAddress(
        [res.data.data.longitude, res.data.data.latitude],
        (status: any, result: any) => {
          if (status === "complete" && result.regeocode) {
            const address = result.regeocode.formattedAddress;
            res.data.data.address = address;
            this.reportDetails = res.data.data;
          }
        }
      );
    });
  }
  // 任务概览
  private taskOverview: any = {};
  private getTaskOverview() {
    getTaskOverview().then((res: any) => {
      this.taskOverview = res.data.data;
    });
  }
  // 任务反馈时间轴显示
  private taskRemark: any = {};
  private getTaskRemark() {
    getTaskRemark().then((res: any) => {
      this.taskRemark = res.data.data;
      const taskList: any[] = [];
      for (const item of res.data.data) {
        if (
          !item.departmentName.indexOf("生态") ||
          !item.departmentName.indexOf("环境")
        ) {
          // top: "-1.235rem"  生态环境局
          for (const task of item.remarkList) {
            task.time = new Date(task.createTime).getTime();
            task.top = "-1.235rem";
            task.departmentName = item.departmentName;
            taskList.push(task);
          }
        } else if (
          !item.departmentName.indexOf("综合") ||
          !item.departmentName.indexOf("执法")
        ) {
          // top: "-0.985rem"   综合行政执法局
          for (const task of item.remarkList) {
            task.time = new Date(task.createTime).getTime();
            task.top = "-0.985rem";
            task.departmentName = item.departmentName;
            taskList.push(task);
          }
        } else if (
          !item.departmentName.indexOf("农业") ||
          !item.departmentName.indexOf("水务")
        ) {
          // top: "-0.735rem"  农业和水务局
          for (const task of item.remarkList) {
            task.time = new Date(task.createTime).getTime();
            task.top = "-0.735rem";
            task.departmentName = item.departmentName;
            taskList.push(task);
          }
        } else if (
          !item.departmentName.indexOf("住房") ||
          !item.departmentName.indexOf("交通")
        ) {
          // top: "-0.48rem"  住房建筑和交通运输局
          for (const task of item.remarkList) {
            task.time = new Date(task.createTime).getTime();
            task.top = "-0.48rem";
            task.departmentName = item.departmentName;
            taskList.push(task);
          }
        }
      }
      if (taskList.length !== 0) {
        const sort: any = taskList.sort((a, b) => {
          return a.time - b.time;
        });
        const startTime: any = sort[0].time - 1000 * 10 * 60 * 30;
        const endTime: any = sort.slice(-1)[0].time + 1000 * 10 * 60 * 2;
        for (const item of taskList) {
          item.timec = startTime;
        }
        this.taskList = taskList;
        jq(".time1").remove();
        this.$nextTick(() => {
          this.htmlLoad(startTime, endTime);
          for (const item of this.taskList) {
            this.addtimes(item);
          }
        });
      }
    });
  }
  private creatTime: any = "";
  private departmentName: any = "";
  private content: any = "";
  // 水质监测数据
  private waterMonitor: any = {
    bottomList: [],
    dataList: [],
    unit: "mg/L"
  };
  // 水质实时监测数据btn选择
  private waterMonitorSelectChange(e: any): void {
    this.airType = e.target.value;
    // let currentSelect!: any;
    // this.airTypes.forEach((record: any, index: number) => {
    //   if (record.pollutantCode == e.target.value) {
    //     currentSelect = record;
    //   }
    // });
    // this.waterMonitor.unit = currentSelect.unit;
    // this.getMonitorItemDetailRecord(this.taskDetails.stationId, e.target.value);
  }
  // 获取监测项详情记录
  private getMonitorItemDetailRecord(): void {
    getMonitorItemDetailRecord(this.stationId, this.airType).then(res => {
      if (res.data.data) {
        const { hourMonitorItems } = res.data.data;
        this.waterMonitor.bottomList = [];
        this.waterMonitor.dataList = [];
        // 24小时水质质量趋势
        if (hourMonitorItems.length > 0) {
          const sortedHourMonitorItems = hourMonitorItems.sort(
            (a: any, b: any) => {
              return a.hour - b.hour;
            }
          );
          if (sortedHourMonitorItems[0].alarmValue) {
            this.waterMonitor.warnValue = sortedHourMonitorItems[0].alarmValue;
          } else {
            this.waterMonitor.miniValue =
              sortedHourMonitorItems[0].alarmMinValue;
            this.waterMonitor.maxValue =
              sortedHourMonitorItems[0].alarmMaxValue;
          }
          sortedHourMonitorItems.forEach((record: any) => {
            this.waterMonitor.bottomList.push(record.hour + "时");
            this.waterMonitor.dataList.push(record.monitorValue || "-");
          });
        }
      }
    });
  }
  // 获取重点污染源
  private getTwentyFourHour() {
    getTwentyFourHour(this.stationId).then((res: any) => {
      const airData: any = {
        bottomList: [],
        dataList: [],
        unit: "",
        colorType: ""
      };
      for (const item in res.data.data) {
        airData.bottomList.push(item.replace("_", "") + "时");
        airData.dataList.push(res.data.data[item]);
        if (this.airType == "103") {
          airData.unit = "mg/m³";
        } else {
          airData.unit = "μg/m³";
        }
        if (this.airType == "100") {
          airData.colorType = "SO2";
        } else if (this.airType == "101") {
          airData.colorType = "NO2";
        } else if (this.airType == "102") {
          airData.colorType = "O3";
        } else if (this.airType == "103") {
          airData.colorType = "CO";
        } else if (this.airType == "104") {
          airData.colorType = "PM10";
        } else if (this.airType == "105") {
          airData.colorType = "PM25";
        }
      }
      this.airData = airData;
    });
  }
  // 获取地图的站点信息
  private emergencyStationList: any[] = [];
  private getEmergencyStationList() {
    getEmergencyStationList(1, 2).then((res: any) => {
      this.emergencyStationList = res.data.data;
      for (const item of res.data.data) {
        if (item.stationType == 1) {
          this.stationId = item.stationCode;
        }
      }
      for (let index = 0; index < res.data.data.length; index++) {
        if (
          res.data.data[index].stationType == 1 &&
          res.data.data[index].airType == 3
        ) {
          this.stationId = res.data.data[index].stationCode;
          break;
        }
      }
      this.getAqiTrend();
    });
  }
}
(window as any).taskDetail = (data: any) => {
  for (const item of document.querySelectorAll(".time1-active")) {
    item.className = "time1";
  }
  // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
  //@ts-ignore
  // eslint-disable-next-line no-undef
  _that.departmentName = data.split("---")[0];
  // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
  //@ts-ignore
  // eslint-disable-next-line no-undef
  _that.creatTime = moment(new Date(Number(data.split("---")[1]))).format(
    "YYYY-MM-DD HH:mm"
  );
  // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
  //@ts-ignore
  // eslint-disable-next-line no-undef
  _that.content = data.split("---")[2];
  // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
  //@ts-ignore
  // eslint-disable-next-line no-undef
  _that.replyContent = data.split("---")[3];
  // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
  //@ts-ignore
  event.target.className = "time1-active";
};
</script>
