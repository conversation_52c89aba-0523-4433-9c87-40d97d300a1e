<template>
  <div class="noise-health">
    <div class="left">
      <div class="title">
        <span>街道区域环境噪声等级</span>
        <PeriodTimeSelect @getPeriodData="getPeriodData" />
      </div>
      <div class="sub-title">
        <img src="@/assets/biaoti.png" alt class="title-img" />
      </div>

      <div class="street_box">
        <div
          class="street"
          @click="handleClick(item, index)"
          :class="{ active: index === activeIndex }"
          v-for="(item, index) in dataList"
          :key="item.streetCode"
        >
          <div class="db">
            <div class="db-num" :class="{ active: index === activeIndex }">
              {{ isDay ? item.noiseList.sd : item.noiseList.sn }}
            </div>
            <div class="db-text">{{ isDay ? "昼间" : "夜间" }}dB(A)</div>
          </div>

          <div class="street-info">
            <div class="street-info-top">
              <div class="street-name">{{ item.streetName }}</div>
              <div
                class="person-count"
                :class="{ active: index === activeIndex }"
              >
                人口数：{{ item.personCount }}
              </div>
              <i
                class="el-icon-location-outline"
                :class="{ active: index === activeIndex }"
              ></i>
            </div>
            <div class="street-info-bottom">
              噪声水平等级：<span class="noise-level"
                >{{
                  isDay ? item.noiseList.sdLevel : item.noiseList.snLevel
                }}</span
              >
              <span v-if="Object.keys(item.noiseList).length">({{
                  isDay
                      ? item.noiseList.sdLevelRemark
                      : item.noiseList.snLevelRemark
                }})</span>
            </div>
          </div>
        </div>
        <div v-if="!dataList.length" class="nodata">
          <img src="@/assets/recheck/<EMAIL>" alt="" />
          <div class="no_data_text">暂无数据</div>
        </div>
      </div>
    </div>
    <div class="right">
      <div class="title">
        <span>街道区域环境噪声等级水平占比</span>
      </div>
      <div class="sub-title">
        <img src="@/assets/biaoti.png" alt class="title-img" />
      </div>

      <HealthRosePie
        :propData="chartData"
        id="noise-health-pie"
        unit="个"
        width="410px"
        height="350px"
      />
    </div>

    <!-- 昼夜切换按钮 -->
    <div class="DandN" @click="changeDay">
      <div class="sun" :class="{ moon: isDay }">
        <img src="~@/assets/<EMAIL>" alt="" />
      </div>
      <div class="sun" :class="{ moon: !isDay }">
        <img src="~@/assets/<EMAIL>" alt="" />
      </div>
    </div>
    <!-- 昼夜切换按钮 -->
  </div>
</template>
<script>
import PeriodTimeSelect from "@/components/periodTimeSelect/periodTimeSelect.vue";
import vueSeamlessScroll from "vue-seamless-scroll";
import HealthRosePie from "@/components/Charts/HealthRosePie.vue";
import { noiseStreetCount } from "@/api/health";
import jinniuStreet from "@/assets/map-geojson/jinniu_street.json";
import markerTitle from "@/assets/health/title-icon.png";

export default {
  name: "NoiseHealth",
  components: {
    PeriodTimeSelect,
    vueSeamlessScroll,
    HealthRosePie,
  },
  props: {
    map: {
      required: true,
    },
    AMap: {
      required: true,
    },
  },
  data() {
    return {
      startDate: "",
      endDate: "",
      dataList: [],
      chartData: [],
      loading: false,
      activeIndex: undefined,
      activeCode: undefined,
      isDay: true,
      classOption: {
        step: 0.2,
      },
      levelMap: {
        一级: "好",
        二级: "较好",
        三级: "一般",
        四级: "较差",
        五级: "差",
      },
      colorMap: {
        一级: "#C1E6EB",
        二级: "#2CC3EC",
        三级: "#6EBE44",
        四级: "#EAE84D",
        五级: "#F69331",
      },
      polygonList: [],
      markerList: [],
      mapGeoJson: null,
    };
  },
  watch: {
    map(newVal) {
      if (newVal) {
        this.drawStreet();
      }
    },
  },
  beforeDestroy() {
    this.map.clearMap();
  },
  methods: {
    getPeriodData(data) {
      this.startDate = data.startDate;
      this.endDate = data.endDate;
      this.getNoiseStreetCount();
    },
    getNoiseStreetCount() {
      this.loading = true;
      noiseStreetCount({
        startDate: this.startDate,
        endDate: this.endDate,
      })
        .then((res) => {
          this.dataList = res.data.data.map(item => ({...item, noiseList: item.noiseList || {}}))
          this.chartData = this.getChartData();
          // this.dataList = [
          //   ...res.data.data,
          //   ...res.data.data,
          //   ...res.data.data,
          //   ...res.data.data,
          //   ...res.data.data,
          // ];
          this.loading = false;

          if (this.map) {
            this.drawStreet();
          }
        })
        .catch(() => {
          this.loading = false;
        });
    },
    getChartData() {
      const arr = [
        { name: "一级", value: 0 },
        { name: "二级", value: 0 },
        { name: "三级", value: 0 },
        { name: "四级", value: 0 },
        { name: "五级", value: 0 },
      ];
      this.dataList.forEach((item) => {
        const arrItem = arr.find((i) => {
          const noiseLevel = this.isDay
            ? item.noiseList.sdLevel
            : item.noiseList.snLevel;
          if (i.name === noiseLevel) {
            return i;
          }
        });
        if (arrItem) {
          arrItem.value++;
        }
      });
      arr.forEach((item) => {
        item.rate = Math.round((item.value / this.dataList.length) * 100);
        item.name = `${item.name}(${this.levelMap[item.name]})`;
      });
      return arr;
    },
    changeDay() {
      this.isDay = !this.isDay;
      this.chartData = this.getChartData()
      this.map.clearMap()
      this.drawStreet()
    },
    drawStreet() {
      const _this = this;
      // 清除之前数据
      if (this.mapGeoJson) {
        this.mapGeoJson.setMap(null)
        this.polygonList.forEach((item) => {
          this.map.remove(item)
        })
        this.markerList.forEach((item) => {
          this.map.remove(item)
        })
        this.polygonList = []
        this.markerList = []

      }
      this.mapGeoJson = new AMap.GeoJSON({
        geoJSON: jinniuStreet,
        // 还可以自定义getMarker和getPolyline
        getPolygon: function(geojson, lnglats) {
          const polygon = new AMap.Polygon({
            path: lnglats,
            fillColor: "rgb(47, 110, 238)",
            strokeOpacity: 1,
            fillOpacity: 0.5,
            strokeColor: "#0087F9",
            strokeWeight: 2,
            // extrusionHeight: 300,
            strokeStyle: "dashed",
            strokeDasharray: [5, 5],
            extData: {
              streetCode: geojson.properties.code,
            },
          });
          _this.polygonList.push(polygon);
          _this.map.add(polygon);
          polygon.on("mouseover", () => {
            polygon.setOptions({
              fillOpacity: 0.7,
              fillColor: "rgb(47, 110, 238)",
            });
          });
          polygon.on("mouseout", () => {
            polygon.setOptions({
              fillOpacity: 0.5,
              fillColor: "rgb(47, 110, 238)",
            });
          });

          if (geojson.properties.name !== "金牛区") {
            const noiseLevel = _this.dataList.find(
              (item) => item.streetCode === geojson.properties.code
            )?.noiseList[_this.isDay ? "sdLevel" : "snLevel"];
            const marker = new AMap.Marker({
              position: new AMap.LngLat(
                geojson.properties.center.lng,
                geojson.properties.center.lat
              ),
              // icon: icon,
              content: `<div class="noise river-content">
              <div>
                <img src="${markerTitle}" />
                <span id="${geojson.properties.code}">${
                geojson.properties.name
              }</span>
              </div>
              <div class="warter-type">
                <span>噪声等级：<span style="color: ${
                  _this.colorMap[noiseLevel]
                };"> ${noiseLevel || "--"}</span></span>
                <span> </span>
              </div>
            </div>`,
              offset: new AMap.Pixel(-86, -100),
              extData: {
                streetCode: geojson.properties.code,
              },
            });

            _this.map.add(marker);
            _this.markerList.push(marker);

            marker.on("mouseover", (e) => {
              console.log(e.target.getExtData());
              const streetPolygon = _this.polygonList.find(
                (item) =>
                  item.getExtData().streetCode ===
                  e.target.getExtData().streetCode
              );
              streetPolygon.setOptions({
                fillOpacity: 0.7,
                fillColor: "rgb(47, 110, 238)",
              });
            });
            marker.on("mouseout", (e) => {
              const streetPolygon = _this.polygonList.find(
                (item) =>
                  item.getExtData().streetCode ===
                  e.target.getExtData().streetCode
              );
              streetPolygon.setOptions({
                fillOpacity: 0.5,
                fillColor: "rgb(47, 110, 238)",
              });
            });
          }
        },
      });
    },
    handleClick(item, index) {
      document
        .getElementById(this.activeCode)
        ?.classList.remove("marker-street-name");

      this.activeCode = item.streetCode;
      this.activeIndex = index;

      document
        .getElementById(item.streetCode)
        .classList.add("marker-street-name");

      this.map.panTo(new AMap.LngLat(item.lng, item.lat));
    },
  },
};
</script>
<style lang="less" scoped>
.nodata {
  // height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  img {
    width: 100%;
  }
  .no_data_text {
    font-size: 16px;
    font-family: PingFang SC;
    font-weight: 500;
    color: #d8e8fe;
    line-height: 30px;
    text-shadow: 0 0 5px rgb(63, 63, 221), 0 0 5px rgb(13, 13, 218);
  }
}
.DandN {
  position: absolute;
  top: 40px;
  right: 500px;
  width: 190px;
  height: 42px;
  background: #00162b;
  border: 2px solid #194fa8;
  border-radius: 21px;
  box-shadow: 0 0 6px 3px #4073c47a;
  display: flex;
  .sun {
    width: 50%;
    height: 100%;
    border-radius: 95px;
    > img {
      display: block;
      width: 33px;
      height: 33px;
      margin: 0 auto;
      margin-top: 3px;
    }
  }
  .moon {
    background-color: #2599f5;
  }
}
.noise-health {
  width: 100vw;
  height: 0;
}
.right,
.left {
  position: absolute;
  padding: 0 30px;
  width: 480px;
  height: calc(100vh - 100px);
}
.left {
  background: linear-gradient(
    to right,
    #000719,
    #000719d9,
    #000719d9,
    #000719d9,
    #0007190d
  );
}
.right {
  right: 0;
  background: linear-gradient(
    to left,
    #000719,
    #000719d9,
    #000719d9,
    #000719d9,
    #0007190d
  );
}
.title {
  margin-top: 40px;
  font-family: Source Han Sans CN;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0 0 5px blue, 0 0 5px blue;
  font-size: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 420px;
  .selects {
    display: flex;
    flex-wrap: nowrap;
  }
}
.sub-title {
  margin-bottom: 20px;
}

.street {
  width: 400px;
  height: 80px;
  background: url("~@/assets/health/<EMAIL>") no-repeat center center;
  background-size: 100% 100%;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  padding: 20px 12px;
  cursor: pointer;
  &:hover,
  &.active {
    background: url("~@/assets/health/<EMAIL>") no-repeat center center !important;
    background-size: 100% 100%;
  }
  .db {
    font-size: 12px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 66px;
    .db-num {
      font-size: 20px;
      font-weight: bold;
    }
    .db-text {
      color: #86add5;
    }
    .db-num {
      color: #27bfff;
      &.active {
        color: #fff;
      }
    }
  }
  .street-info {
    flex: 1;
    padding-left: 20px;
    .street-info-top {
      width: 100%;
      display: flex;
      align-items: center;
      .street-name {
        font-size: 16px;
        width: 150px;
        color: #c9dcea;
      }
      .el-icon-location-outline {
        font-size: 20px;
        margin-left: auto;
        color: #6796c0;
        &.active {
          color: #01aae9;
        }
      }
      .person-count {
        width: 105px;
        height: 20px;
        background: #0c3972;
        border-radius: 2px;
        font-size: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: auto;
        &.active {
          background: #1a63ae;
        }
      }
    }
    .street-info-bottom {
      margin-top: 3px;
      width: 100%;
      display: flex;
      align-items: center;
      color: #c0d7de;
      .noise-level {
        color: #26b8f7;
      }
    }
  }
}
.street_box {
  height: 820px;
  overflow-y: scroll;
}
::-webkit-scrollbar-thumb {
  background: transparent;
}
::-webkit-scrollbar-track {
  background-color: transparent; /* 轨道背景颜色 */
}
</style>
<style>
.noise.river-content {
  width: 138px !important;
  height: 138px !important;
  background-size: 100% 100% !important;
}
</style>
