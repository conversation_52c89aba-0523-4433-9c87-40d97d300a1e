<template>
  <div :id="id" :style="{ height: height, width: width }" />
</template>
<script lang="ts">
import * as echarts from 'echarts'
import { Component, Prop, Watch } from "vue-property-decorator";
import { mixins } from "vue-class-component";
import ResizeMixin from "./mixins/resize";
// eslint-disable-next-line @typescript-eslint/class-name-casing
interface radarData {
  name: string;
  indicator: { name: string; max: number }[];
  dataList: number[];
}
@Component({
  name: "Radar<PERSON><PERSON>"
})
export default class extends mixins(ResizeMixin) {
  @Prop({ default: "chart" }) private id!: string;
  @Prop({ default: "200px" }) private width!: string;
  @Prop({ default: "200px" }) private height!: string;
  @Prop({ required: false }) private propData!: radarData;
  private renderIndex = -7;
  @Watch("propData", { immediate: true, deep: true }) public onMsgChanged(
    newValue: radarData,
    oldValue: radarData
  ) {
    if (this.propData) {
      this.$nextTick(() => {
        this.initChart();
      });
    }
  }
  mounted() {
    this.initChart();
  }
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.clear();
    this.chart.dispose();
    this.chart = null;
  }
  private initChart() {

    if(this.chart === null || this.chart === undefined) {
      this.chart = echarts.init(
        document.getElementById(this.id) as HTMLDivElement
      );
    }
    this.chart.setOption({
      title: [
        {
          text: this.propData.name,
          textStyle: {
            color: "white",
            fontSize: "14",
            fontWeight: "normal"
          },
          bottom: 0,
          left: "29%",
          backgroundColor: "#014DB8",
          padding: [5, 30]
        }
      ],
      radar: [
        {
          radius: "60%",
          center: ["53%", "42%"],
          nameGap: "7",
          axisLine: {
            show: false
          },
          splitLine: {
            lineStyle: {
              color: "#014DB8"
            }
          },
          splitArea: {
            areaStyle: {
              color: "transparent"
            }
          },
          name: {
            color: "white",
            textStyle: {},
            formatter: (a: any, b: any) => {
              this.renderIndex += 1;
              return `{a|${a}} ${this.propData.dataList[this.renderIndex]}`;
            },
            rich: {
              a: {
                color: "#00EAFF"
              }
            }
          },
          indicator: this.propData.indicator
        }
      ],
      series: [
        {
          // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
          // @ts-ignore
          name: "车辆类型分布",
          type: "radar",
          symbol: "circle",
          symbolSize: 1,
          itemStyle: {
            color: "transparent"
          },
          // label: {
          //   show: true,
          //   color: "white",
          //   position: "inside"
          // },
          areaStyle: {
            opacity: 1,
            color: {
              type: "linear",
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: "#1C65F6" // 0% 处的颜色
                },
                {
                  offset: 1,
                  color: "#59C5FD" // 100% 处的颜色
                }
              ],
              global: false // 缺省为 false
            }
          },
          data: [
            {
              value: this.propData.dataList
            }
          ]
        }
      ]
    } as {});
  }
}
</script>
