<template>
  <div ref="container" class="lin-video-item-box">
    <ezuikitPlayer
      v-if="ezopen"
      :id="'EZUIKitPlayer'"
      :accessToken="accessToken"
      :url="ezopen"
    />

    <LivePlayer
      v-else-if="videoUrl.includes('.m3u8')"
      :vidoeUrl="videoUrl || ''"
      :online="online"
      :playerId="12"
    />
    <ALivePlayer
      v-else
      :video-url="videoUrl"
      fluent
      autoplay
      live
      :stretch="true"
    />
    <!-- <player
      v-else
      ref="videoPlayer"
      :video-url="videoUrl || ''"
      :error="videoError"
      :height="height"
      :has-audio="false"
      fluent
      :autoplay="false"
      live
      muted
    /> -->

    <div class="info-box" v-if="!ezopen">
      <div class="name">{{ name }}</div>
      <div class="positon"
        ><i class="el-icon-location-information" /> {{ positon }}</div
      >
      <div class="state" :class="{ offline: !online }">{{
        online ? '在线' : '离线'
      }}</div>
    </div>
  </div>
</template>

<script>
import player from '@/components/jessibucaPlayer/jessibuca.vue'
import ezuikitPlayer from '@/components/EZUIKitJs.vue'
import LivePlayer from './alivePlay.vue'
import ALivePlayer from '@liveqing/liveplayer'

export default {
  name: 'VideoItem',
  components: { ezuikitPlayer, player, LivePlayer, ALivePlayer },
  props: {
    videoUrl: {
      type: String,
      default: '',
    },
    name: {
      type: String,
      default: '',
    },
    positon: {
      type: String,
      default: '',
    },

    online: {
      type: Boolean,
      default: false,
    },

    // 萤石云播放地址
    ezopen: {
      type: String,
      default: '',
    },
    accessToken: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      height: 0,
    }
  },
  watch:{
    videoUrl:{
      handler(url){
        console.log(url,'---url----85');
      },
      deep: true,
      immediate: true
    }
  },
  mounted() {
    // console.log('初始化视频容器', this.$refs.container.clientHeight)
    this.height = this.$refs.container.clientHeight
    /* setTimeout(() => {
      this.height = this.$refs.container.clientHeight
    }, 2000) */
  },
  beforeUnmount() {
    try {
      window.EZUIKitPlayer.stop()
      delete window['EZUIKitPlayer']
    } catch (error) {}

    // 销毁全局挂载的容器
    delete window['EZUIKit']['video-container']
    // 销毁全局挂载的js播放插件
    delete window['JSPlugin']

    if (this.$refs.videoPlayer) {
      this.$refs.videoPlayer.destroy()
    }
  },
  methods: {
    /**
     * 视频播放错误
     */
    videoError(e) {
      console.log(`播放器错误：${JSON.stringify(e)}`)
    },
    /**
     * 截图功能
     */
    screenshot() {
      this.$refs.videoPlayer.jessibuca.screenshot('截图', 'png', 0.5)
    },
  },
}
</script>

<style lang="less" scoped>
.lin-video-item-box {
  height: 100%;
  width: 100%;
  position: relative;
  // border-radius: 8px;
  overflow: hidden;

  &:hover {
    .info-box {
      display: flex;
    }
  }

  .info-box {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    display: flex;
    align-items: center;
    color: #fff;
    background-color: rgba(0, 0, 0, 0.3);
    // font-size: 15%;
    padding: 2% 3%;
    display: none;

    & > div {
      max-width: 33%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .positon {
      margin-left: 8%;
    }

    .state {
      margin-right: 0;
      margin-left: auto;
      display: flex;
      align-items: center;

      &::before {
        content: '';
        width: 10px;
        height: 10px;
        border-radius: 50%;
        background-color: var(--panGreen);
        margin-right: 20px;
      }

      &.offline::before {
        background-color: var(--warn);
      }
    }
  }
}
</style>
