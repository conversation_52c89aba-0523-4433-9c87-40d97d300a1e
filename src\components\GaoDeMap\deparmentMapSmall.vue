<template>
  <div :ref="id" style="width: 100%; height: 100%"></div>
</template>

<script lang="ts">
//@ts-ignore
// import AMap from 'AMap'
//@ts-ignore
import jinniuStreet from '@/assets/map-geojson/jinniu_street'
import { Vue, Component, Prop, Watch } from 'vue-property-decorator'
import { Icon } from 'ant-design-vue'
import { webglcontextlostHandle } from '@/utils/index'
import moment from 'moment'
import districtRiver from "@/assets/jinniu-river4.png"
import waterStationJson from './waterJSON.js'

// 地图实例
let smallMaps: any = null

@Component({
  name: 'DepartmentBgMap',
  components: {
    AIcon: Icon,
  },
})
export default class extends Vue {
  // private smallMaps: any = "";
  @Prop({
    required: false,
    type: Number,
    default: 14,
  })
  private mapZoom!: number
  @Prop({ required: false }) smallMapstyle!: string
  @Prop({ required: false, default: 'smallMap' }) id!: string
  @Prop({ required: false, default: '2D' }) viewMode!: string
  @Prop({ required: false, default: true }) heat!: string
  @Prop({ required: false, default: 300 }) heathMapMax!: string
  @Prop({ required: false, default: () => ({}) }) maxPoint!: any
  @Prop({ required: false, default: 'air' }) pageType!: string
  @Watch('pageType', { immediate: false })
  private pageTypeChange() {
    if (this.pageType == 'air') {
      smallMaps.remove(this.riverLayer)
      smallMaps.remove(this.polylinesList)
      this.creatGeojson()
    } else {
      this.clearHeatMapLayer()
      smallMaps.remove(this.polygonList)
      this.drawWaterMap()
    }
  }
  @Prop({ required: true, default: () => [] }) heatmapData!: any
  @Watch('heatmapData', { immediate: true, deep: true })
  heatmapDataChange(list: any) {
    if (list.length && smallMaps) {
      // if(this.pageType,'-----57')
      if(this.pageType == 'air'){
        this.drawHeatMap()
      }else{
        this.drawWaterLine()
      }
    } else {
      this.clearHeatMapLayer()
      if(!smallMaps)return
      smallMaps.remove(this.polylinesList)
    }
  }
  @Watch('mapZoom', { immediate: true, deep: false })
  private onmapZoomChange(newValue: number, oldValue: number) {
    if (newValue && smallMaps) {
      // smallMaps.setZoom(newValue);
    }
  }

  private heatMapLayer: any = null
  private riverLayer:any = null
  private polygonList:any = []
  private polylinesList:any = []
  mounted() {
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    setTimeout((_) => {
      this.map()
      smallMaps.on('complete', () => {
        this.creatGeojson()
        // this.drawHeatMap()
        if(this.pageType == 'air'){
          this.drawHeatMap()
        }else{
          this.drawWaterLine()
        }
      })
    }, 1500)
  }
  created() {

  }
  beforeDestroy() {
    if(!smallMaps)return
    smallMaps.clearMap()

    // 销毁地图
    smallMaps.destroy()
    smallMaps = null
  }
  // 高德地图
  private map(): void {
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const _this = this
    // 初始化地图
//@ts-ignore
    const map = new AMap.Map(this.$refs[this.id], {
      center: [104.04, 30.73],
      position: [104.04, 30.73],
      zoom: 12.5,
      viewMode: '2D',
      showLabel: false,
      pitch: 0,
      zoomEnable: false,
      dragEnable: false,
    })

    // 处理webgl上下文丢失事件
    webglcontextlostHandle.call(this)

    // 设置地图样式
    map.setMapStyle('amap://styles/0739a6632d849ae1b5e6f1c52f7d12c3')
    smallMaps = map
  }
  // 地图热力图图层
  private drawHeatMap() {
    if (!this.heat) return
    const that = this
    const heathMapMax = this.heathMapMax
    const { latitude, longitude } = this.maxPoint
    let heatMapData: any = this.heatmapData.map((v: any) => {
      return {
        lng: v.longitude,
        lat: v.latitude,
        count: v.concentration,
      }
    })
    if (heatMapData.every((o: any) => o.count == undefined)) {
      heatMapData = []
    }
    smallMaps.plugin(['AMap.Heatmap'], function () {
      if (that.heatMapLayer) {
        that.heatMapLayer.setMap(null)
      }
      //初始化heatmap对象
      that.heatMapLayer = new AMap['Heatmap'](smallMaps, {
        radius: 35, //给定半径
        opacity: [0, 0.8],
        gradient: {
          0: 'rgba(0,228,0,0.1)',
          0.1: 'rgba(0,228,0,0.4)',
          0.2: 'rgba(255,255,0,0.1)',
          0.3: 'rgba(255,255,0,0.4)',
          0.4: 'rgba(255,126,0,0.1)',
          0.5: 'rgba(255,126,0,0.4)',
          0.6: 'rgba(255,0,0,0.1)',
          0.7: 'rgba(255,0,0,0.4)',
          0.8: 'rgba(153,0,76,0.1)',
          0.9: 'rgba(153,0,76,0.4)',
          1: 'rgba(126,0,35,0.4)',
        },
      })
      //设置数据集
      that.heatMapLayer.setDataSet({
        data: heatMapData,
        max: heathMapMax,
      })
      const waterCenterPosition = new AMap.LngLat(longitude, latitude)
      smallMaps.setCenter(waterCenterPosition)
      smallMaps.setZoom(12.5)
    })
  }
  private clearHeatMapLayer() {
    if(!this.heatMapLayer)return
    this.heatMapLayer.setMap(null)
  }
  // 地图水系图
  private drawWaterMap(){
    const bounds = new AMap.Bounds([103.9363,30.662957], [104.160597,30.807361]);
    const imageLayer = new AMap.ImageLayer({
//@ts-ignore
      url: districtRiver, // districtRiver
      bounds: bounds,
      zooms: [3, 18],
      opacity: 1
    });
    imageLayer.setMap(smallMaps);
    this.riverLayer = imageLayer

  }
    // 地图添加站点水路图
  private drawWaterLine() {
    smallMaps.remove(this.polylinesList)
    const { latitude, longitude } = this.maxPoint
    this.heatmapData.forEach((item: any) => {
      let strokeColor =
        item.waterLevel == 'Ⅰ' || item.waterLevel == 'Ⅱ' ||item.waterLevel == 'Ⅲ'
          ?  ['rgb(0, 228, 0)', 'rgb(36, 230, 36)', 'rgb(69, 230, 69)', 'rgb(92, 228, 92)', 'rgb(123, 226, 123)']
          : ['rgb(255, 0, 0)', 'rgb(253, 28, 28)', 'rgb(253, 69, 69)', 'rgb(252, 98, 98)', 'rgb(252, 134, 134)']
      for (let i = 0; i < 5; i++) {
        if (waterStationJson[item.stationName+i]) {
          let polyline= new AMap.Polyline({
            path: waterStationJson[item.stationName+i].path,
            isOutline: true,
            outlineColor: 'rgba(0,0,0,0)',
            borderWeight: 0,
            strokeColor: strokeColor[i],
            strokeOpacity: 0.8,
            strokeWeight: 6,
            // 折线样式还支持 'dashed'
            strokeStyle: 'solid',
            // strokeStyle是dashed时有效
            // strokeDasharray: [10, 5],
            lineJoin: 'round',
            lineCap: 'round',
            extData: item.stationName+i
            // zIndex: 50,
          })
          polyline.setMap(smallMaps)
          this.polylinesList.push(polyline)
        }
      }
    })
    const waterCenterPosition = new AMap.LngLat(longitude, latitude)
    smallMaps.setCenter(waterCenterPosition)
    smallMaps.setZoom(14)
  }

  // 添加街道划分区域地图数据
  private creatGeojson() {
    const map = smallMaps
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const that = this
    // 添加金牛区地理信息数据 1
    const geojson = new AMap.GeoJSON({
      geoJSON: jinniuStreet,
      getPolygon: function (geojson: any, lnglats: any) {
        AMap.convertFrom(
          geojson.geometry.coordinates[0],
          'gps',
          function (status: any, result: any) {
            if (result.info === 'ok'&&geojson.properties.name !== "金牛区") {
              const polygon = new AMap.Polygon({
                path: result.locations,
                strokeColor: '#5dc0dc',
                strokeWeight: 3,
                strokeOpacity: 0.5,
                // fillOpacity: 0.5, // 多边形填充透明度
                fillColor: 'rgba(0,228,0, 0)',
                zIndex: 10,
              })
              that.polygonList.push(polygon)
              map.add(polygon)
            }
          }
        )
        return geojson
      },
    })

    // 添加金牛区地理信息数据 2
    geojson.setMap(map)
  }
}
</script>

<style lang="less">
// @import url(./map.less);
</style>
<style lang="less" scoped>
</style>
