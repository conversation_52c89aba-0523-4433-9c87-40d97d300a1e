<style lang="less" scoped>
.home-map-top {
  .home-map-left {
    .title {
      color: #cccccc;
      font-size: 0.2rem;
      margin-bottom: 0.2rem;
    }
    .sub-title-content {
      height: 2rem;
      // border: 1px solid #fff;
    }
    .air-quality-flex {
      display: flex;
      justify-content: space-between;
      .air-quality-type {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        padding: 0.1rem 0.25rem;
        > div {
          display: flex;
          justify-content: space-between;
          font-size: 0.18rem;
          p {
            margin-bottom: 0;
            color: rgba(180, 180, 178, 1);
          }

          .unit {
            color: rgba(154, 155, 156, 1);
            font-size: 0.1rem;
            margin-left: 0.14rem;
          }
        }
      }
    }
    .air-check-charts {
      height: 2.8rem;
      > div:nth-of-type(1) {
        display: flex;
        justify-content: space-between;
      }
      > div:nth-of-type(2) {
        display: flex;
        flex-direction: column;
        .check-air-day {
          display: flex;
          width: 100%;
          align-items: center;
          white-space: nowrap;
        }
      }
    }
    .site-type {
      height: 1.7rem;
      display: flex;
      justify-content: space-between;
    }
  }
  .check-air-table {
    .check-air-th {
      width: 100%;
      height: 0.24rem;
      line-height: 0.24rem;
      background: rgba(23, 66, 190, 1);
      margin-bottom: 0.05rem;
    }
    .check-air-tr {
      > :nth-of-type(odd) {
        height: 0.34rem;
        line-height: 0.34rem;
        background: rgba(21, 44, 110, 1);
      }
      > :nth-of-type(even) {
        height: 0.34rem;
        line-height: 0.34rem;
        background: rgba(3, 20, 59, 1);
      }
    }
    .check-air-th,
    .check-air-tr {
      display: flex;
      justify-content: space-between;
      > div {
        width: 25%;
        text-align: center;
        font-size: 0.14rem;
      }
    }
  }
}
section.air-page {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: calc(100% - 0.5rem);
}
</style>
<template>
  <section class="air-page">
    <div class="comment-content">
      <div class="title">
        站点种类分布<span style="font-size:0.14rem;">（共计 {{total}}个）</span>
      </div>
      <div class="sub-title-content site-type">
        <SitePie
          :id="'site-type-s'"
          :width="'1.5rem'"
          :height="'1.5rem'"
          :propData="siteTypeNumberCity"
        />
        <SitePie
          :id="'site-type-q'"
          :width="'1.5rem'"
          :height="'1.5rem'"
          :propData="siteTypeNumberArea"
        />
        <SitePie
          :id="'site-type-w'"
          :width="'1.5rem'"
          :height="'1.5rem'"
          :propData="siteTypeNumberTiny"
        />
      </div>
    </div>
    <div class="comment-content">
      <div class="title">金牛区空气质量</div>
      <div class="sub-title-content air-quality-flex">
        <MapHomeAqi
          :id="'aqi-quality'"
          :width="'2rem'"
          :height="'1.8rem'"
          :propData="aqiCharts"
        />
        <div class="air-quality-type">
          <div
            v-for="(item, index) in airTypeNumber"
            :key="index"
            v-show="index != 0"
          >
            <p>
              <span style="min-width:0.5rem;display:inline-block;">{{
                item.name
              }}</span>
              <span class="unit">{{ item.unit }}</span>
            </p>
            <p :style="{ color: item.bgColor }">{{ item.number }}</p>
          </div>
        </div>
      </div>
    </div>
    <div class="comment-content">
      <div class="title">空气质量考核目标</div>
      <div class="sub-title-content air-check-charts">
        <!-- <div style="text-align: right;">{{ airCheckList.now }}截止</div> -->
        <div style="display: flex;justify-content: space-between;">
          <RotateBarSolid
            :id="'airCheck9'"
            :width="'2.85rem'"
            :height="'1.8rem'"
            :airData="airCheckList1"
          ></RotateBarSolid>
          <airPieChart
            :id="'airCheck6'"
            :width="'1.8rem'"
            :height="'1.8rem'"
            :PieChartData="aqiDay"
          ></airPieChart>
        </div>
        <div class="check-air-table">
          <div class="check-air-thead">
            <div class="check-air-th">
              <div>指标项</div>
              <div>数值</div>
              <div>同比</div>
              <div>目标</div>
            </div>
          </div>
          <div class="check-air-tbody">
            <div class="check-air-tr">
              <div>优良天数</div>
              <div>{{ airCheckList.thisYearGoodDay }}天</div>
              <div>
                <img
                  v-if="airCheckList.YearOnYearQuality >= 0"
                  :src="ssaqi"
                  alt
                  style="height:.25rem"
                />
                <img
                  v-if="airCheckList.YearOnYearQuality < 0"
                  :src="xjaqi"
                  alt
                  style="height:.25rem"
                />
                <span
                  v-if="airCheckList.YearOnYearQuality >= 0"
                  style="color:rgb(0, 255, 0);"
                  >{{ airCheckList.YearOnYearAQI }}天</span
                ><span
                  v-if="airCheckList.YearOnYearQuality < 0"
                  style="color:red;"
                  >{{ airCheckList.YearOnYearAQI }}天</span
                >
              </div>
              <div>{{ airCheckList.thisYearTagDay }}天</div>
            </div>
            <div class="check-air-tr">
              <div>PM₂.₅</div>
              <div>{{ airCheckList.thisYearFineParticulateMatter }}ug/m³</div>
              <div>
                <img
                  v-if="airCheckList['YearOnYearPM2.5'] >= 0"
                  :src="ss1"
                  alt
                  style="height:.25rem"
                />
                <img
                  v-if="airCheckList['YearOnYearPM2.5'] < 0"
                  :src="xj1"
                  alt
                  style="height:.25rem"
                />
                <span
                  v-if="airCheckList['YearOnYearPM2.5'] >= 0"
                  style="color:red;"
                >
                  {{ airCheckList.YearOnYearPM }}%
                </span>
                <span
                  v-if="airCheckList['YearOnYearPM2.5'] < 0"
                  style="color:rgb(0, 255, 0);"
                >
                  {{ airCheckList.YearOnYearPM }}%
                </span>
              </div>
              <div>
                {{ airCheckList.thisYearFineParticulateMatterTag }}ug/m³
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>
<script lang="ts">
interface HeatMapData {
  lng: number;
  lat: number;
  count: number;
}
interface AirData {
  bottomList: string[];
  dataList: string[];
}
interface AirContrast {
  monthList: string | number[];
  thisYear: string | number[];
  lastYear: string | number[];
  thisYearName?: string;
  lastYearName?: string;
}
interface MapCenter {
  lng: number;
  lat: number;
}
interface SiteTypeData {
  name: string;
  number: number;
  total: number;
  startColor: string;
  endColor: string;
}
interface TaskData {
  type: number;
  showNumber: number;
  name: string;
  number: number;
  bgColor?: string;
}
import { Component, Vue, Watch } from "vue-property-decorator";
import SitePie from "@/components/Charts/SitePie.vue";
import MapHomeAqi from "@/components/Charts/MapHomeAqi.vue";
import AirDoublePie from "@/components/Charts/AirDoublePie.vue";
import RotateBarSolid from "@/components/Charts/RotateBarSolid.vue";
import AQIChart from "@/components/Charts/AQIChart.vue";
import xj from "@/assets/xj.png";
import ss from "@/assets/ss.png";
import ssaqi from "@/assets/ssaqi.png";
import xjaqi from "@/assets/xjaqi.png";
import airPieChart from "@/components/Charts/airPieChart.vue";
import { airCheck, airIndexAverage, allAirStation } from "@/api/air";
import { getStatistics } from "@/api/homeTable";
@Component({
  name: "AirPage",
  components: {
    SitePie,
    MapHomeAqi,
    AirDoublePie,
    RotateBarSolid,
    AQIChart,
    airPieChart
  }
})
export default class extends Vue {
  // 站点种类分布
  private siteTypeNumberCity: SiteTypeData = {
    name: "国/市控站",
    number: 2,
    total: 58,
    startColor: "rgba(103, 182, 240, 1)",
    endColor: "rgba(72, 109, 213, 1)"
  };
  // 站点种类分布
  private siteTypeNumberArea: SiteTypeData = {
    name: "区控站",
    number: 12,
    total: 58,
    startColor: "rgba(158, 158, 158, 1)",
    endColor: "rgba(255, 255, 255, 1)"
  };

  // 站点种类分布
  private siteTypeNumberTiny: SiteTypeData = {
    name: "微控站",
    number: 44,
    total: 58,
    startColor: "rgba(158, 158, 158, 1)",
    endColor: "rgba(255, 255, 255, 1)"
  };
  // 金牛区空气质量
  private aqiCharts: any = {
    type: 1,
    showNumber: 56 / 500,
    number: 56,
    name: "AQI",
    bgColor: "rgba(6, 240, 184, 1)"
  };
  private ss1 = ss;
  private xj1 = xj;
  private ssaqi = ssaqi;
  private xjaqi = xjaqi;
  // 污染天数
  private airCheckList1 = {};
  private airCheckList = {};
  private replaceFields = {
    children: "child",
    title: "name"
  };
  private aqiDay: any = {};
  private mapMarker: any = [];
  private airTypeNumber = [
    {
      code: "aqi",
      name: "AQI",
      number: "0"
    },
    {
      code: "101",
      name: "NO₂",
      unit: "(μg/m³)",
      number: "0"
    },
    {
      code: "105",
      name: "PM₂.₅",
      unit: "(μg/m³)",
      number: "0"
    },
    {
      code: "104",
      name: "PM₁₀",
      unit: "(μg/m³)",
      number: "0"
    },
    {
      code: "102",
      name: "O₃",
      unit: "(μg/m³)",
      number: "0"
    },
    {
      code: "100",
      name: "SO₂",
      unit: "(μg/m³)",
      number: "0"
    },
    {
      code: "103",
      name: "CO",
      unit: "(mg/m³)",
      number: "0"
    }
  ];
  // AQI
  readonly AQIAirColors: Array<any> = [
    {
      color: "rgb(0,255,0)",
      text: "优",
      type: 1,
      min: 0,
      max: 50
    },
    {
      color: "rgb(255,255,0)",
      text: "良",
      type: 2,
      min: 51,
      max: 100
    },
    {
      color: "rgb(255,126,0)",
      text: "轻度污染",
      type: 3,
      min: 101,
      max: 150
    },
    {
      color: "rgb(255,0,0)",
      text: "中度污染",
      type: 4,
      min: 151,
      max: 200
    },
    {
      color: "rgb(153,0,76)",
      text: "重度污染",
      type: 5,
      min: 201,
      max: 300
    },
    {
      color: "rgb(126,0,35)",
      text: "严重污染",
      type: 6,
      min: 300,
      max: 999
    }
  ];
  // SO2
  readonly SO2AirColors: Array<any> = [
    {
      color: "rgb(0,255,0)",
      type: 1,
      min: 0,
      max: 150
    },
    {
      color: "rgb(255,255,0)",
      type: 2,
      min: 151,
      max: 500
    },
    {
      color: "rgb(255,126,0)",
      type: 3,
      min: 501,
      max: 650
    },
    {
      color: "rgb(255,0,0)",
      type: 4,
      min: 651,
      max: 800
    },
    {
      color: "rgb(153,0,76)",
      type: 5,
      min: 801,
      max: 1600
    },
    {
      color: "rgb(126,0,35)",
      type: 6,
      min: 1601,
      max: 2620
    }
  ];
  // NO2
  readonly NO2AirColors: Array<any> = [
    {
      color: "rgb(0,255,0)",
      type: 1,
      min: 0,
      max: 100
    },
    {
      color: "rgb(255,255,0)",
      type: 2,
      min: 101,
      max: 200
    },
    {
      color: "rgb(255,126,0)",
      type: 3,
      min: 201,
      max: 700
    },
    {
      color: "rgb(255,0,0)",
      type: 4,
      min: 701,
      max: 1200
    },
    {
      color: "rgb(153,0,76)",
      type: 5,
      min: 1201,
      max: 2340
    },
    {
      color: "rgb(126,0,35)",
      type: 6,
      min: 2341,
      max: 3090
    }
  ];
  // PM10
  readonly PM10AirColors: Array<any> = [
    {
      color: "rgb(0,255,0)",
      type: 1,
      min: 0,
      max: 50
    },
    {
      color: "rgb(255,255,0)",
      type: 2,
      min: 51,
      max: 150
    },
    {
      color: "rgb(255,126,0)",
      type: 3,
      min: 151,
      max: 250
    },
    {
      color: "rgb(255,0,0)",
      type: 4,
      min: 251,
      max: 350
    },
    {
      color: "rgb(153,0,76)",
      type: 5,
      min: 351,
      max: 420
    },
    {
      color: "rgb(126,0,35)",
      type: 6,
      min: 421,
      max: 999
    }
  ];
  // PM2.5
  readonly PM25AirColors: Array<any> = [
    {
      color: "rgb(0,255,0)",
      type: 1,
      min: 0,
      max: 35
    },
    {
      color: "rgb(255,255,0)",
      type: 2,
      min: 36,
      max: 75
    },
    {
      color: "rgb(255,126,0)",
      type: 3,
      min: 76,
      max: 115
    },
    {
      color: "rgb(255,0,0)",
      type: 4,
      min: 116,
      max: 150
    },
    {
      color: "rgb(153,0,76)",
      type: 5,
      min: 151,
      max: 250
    },
    {
      color: "rgb(126,0,35)",
      type: 6,
      min: 251,
      max: 999
    }
  ];
  // CO
  readonly COAirColors: Array<any> = [
    {
      color: "rgb(0,255,0)",
      type: 1,
      min: 0,
      max: 5
    },
    {
      color: "rgb(255,255,0)",
      type: 2,
      min: 6,
      max: 10
    },
    {
      color: "rgb(255,126,0)",
      type: 3,
      min: 11,
      max: 35
    },
    {
      color: "rgb(255,0,0)",
      type: 4,
      min: 36,
      max: 60
    },
    {
      color: "rgb(153,0,76)",
      type: 5,
      min: 61,
      max: 90
    },
    {
      color: "rgb(126,0,35)",
      type: 6,
      min: 91,
      max: 999
    }
  ];
  // O3
  readonly O3AirColors: Array<any> = [
    {
      color: "rgb(0,255,0)",
      type: 1,
      min: 0,
      max: 160
    },
    {
      color: "rgb(255,255,0)",
      type: 2,
      min: 161,
      max: 200
    },
    {
      color: "rgb(255,126,0)",
      type: 3,
      min: 201,
      max: 300
    },
    {
      color: "rgb(255,0,0)",
      type: 4,
      min: 301,
      max: 400
    },
    {
      color: "rgb(153,0,76)",
      type: 5,
      min: 401,
      max: 800
    },
    {
      color: "rgb(126,0,35)",
      type: 6,
      min: 801,
      max: 9999
    }
  ];
  private total = 0
  mounted() {
    this.getAirCheck();
    this.getAirIndexAverage();
    this.getAirStation();
    this.getStatistics()
  }
  // 站点统计
  private getStatistics() {
    const params = {
      districtCode: ""
    }
    getStatistics(params).then(res => {
      const { data } = res.data
      this.total = data.wk + data.qk + data.sk + data.gk
      this.siteTypeNumberTiny.number = data.wk
      this.siteTypeNumberTiny.total = data.wk + data.qk + data.sk
      this.siteTypeNumberArea.number = data.qk
      this.siteTypeNumberArea.total = data.wk + data.qk + data.sk
      this.siteTypeNumberCity.number = data.sk + data.gk
      this.siteTypeNumberCity.total = data.wk + data.qk + data.sk
    })
  }
  // 污染天数
  private getAirCheck() {
    airCheck().then((res: any) => {
      if (res.data.data) {
        // 饼状图
        const aqiDay: any = {
          name: "空气优良天数",
          dataList: [
            {
              name: "优",
              value: res.data.data.pollutionDegreeCount.excellentDays
            },
            {
              name: "良",
              value: res.data.data.pollutionDegreeCount.goodDays
            },
            {
              name: "轻度污染",
              value: res.data.data.pollutionDegreeCount.lightPollutionDays
            },
            {
              name: "中度污染",
              value: res.data.data.pollutionDegreeCount.moderatelyPollutedDays
            },
            {
              name: "重度污染",
              value: res.data.data.pollutionDegreeCount.heavyPollutionDays
            },
            {
              name: "严重污染",
              value: res.data.data.pollutionDegreeCount.seriousPollutionDays
            }
          ],
          colorList: [
            "rgb(0,255,0)",
            "rgb(255,255,0)",
            "rgb(255,126,0)",
            "rgb(255,0,0)",
            "rgb(153,0,76)",
            "rgb(126,0,35)"
          ]
        };
        const newDataList: any = [];
        const newColorList: any = [];
        aqiDay.dataList.forEach((item: any, index: any) => {
          if (item.value != 0) {
            newDataList.push(item);
            newColorList.push(aqiDay.colorList[index]);
          }
        });
        aqiDay.dataList = newDataList;
        aqiDay.colorList = newColorList;
        this.aqiDay = aqiDay;
        res.data.data.now = res.data.data.now.slice(0, 11);
        // AQI
        res.data.data.YearOnYearAQI =
          res.data.data.YearOnYearQuality < 0
            ? String(res.data.data.YearOnYearQuality).replace("-", "")
            : res.data.data.YearOnYearQuality;
        // PM2.5
        res.data.data.YearOnYearPM =
          res.data.data["YearOnYearPM2.5"] < 0
            ? String(res.data.data["YearOnYearPM2.5"]).replace("-", "")
            : res.data.data["YearOnYearPM2.5"];
        this.airCheckList = res.data.data;
        // 柱状图
        const list: any = {
          bottomList: ["O₃", "NO₂", "PM₂.₅", "PM₁₀", "CO", "SO₂"],
          dataList: [
            res.data.data.pollutantCount.ozoneEightCount,
            res.data.data.pollutantCount.nitrogenDioxideCount,
            res.data.data.pollutantCount.fineParticulateMatterCount,
            res.data.data.pollutantCount.inhalableParticlesCount,
            res.data.data.pollutantCount.carbonMonoxideCount,
            res.data.data.pollutantCount.sulfurDioxideCount
          ],
          colorList: []
        };
        this.airCheckList1 = list;
      }
    });
  }
  // 首页污染物数据展示
  private getAirIndexAverage() {
    airIndexAverage().then((res: any) => {
      this.airTypeNumber[0].number = res.data.data["aqi"];
      this.airTypeNumber[1].number = res.data.data["NO₂"];
      this.airTypeNumber[2].number = res.data.data["PM₂.₅"];
      this.airTypeNumber[3].number = res.data.data["PM₁₀"];
      this.airTypeNumber[4].number = res.data.data["O₃"];
      this.airTypeNumber[5].number = res.data.data["SO₂"];
      this.airTypeNumber[6].number = res.data.data["CO"];
      // aqi
      for (const item of this.AQIAirColors) {
        // aqi
        if (
          this.airTypeNumber[0].number >= item.min &&
          this.airTypeNumber[0].number <= item.max
        ) {
          (this.airTypeNumber[0] as any).bgColor = item.color;
        }
      }
      const aqiCharts: any = {
        type: 1,
        showNumber: Number(this.airTypeNumber[0].number) / 500,
        number: Number(this.airTypeNumber[0].number),
        name: "AQI",
        // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
        // @ts-ignore
        bgColor: this.airTypeNumber[0].bgColor,
        aqiText: ""
      };
      for (const item1 of this.AQIAirColors) {
        if (aqiCharts.number >= item1.min && aqiCharts.number <= item1.max) {
          aqiCharts.aqiText = item1.text;
        }
      }
      this.aqiCharts = aqiCharts;
      // NO2
      for (const item of this.NO2AirColors) {
        // NO2
        if (
          this.airTypeNumber[1].number >= item.min &&
          this.airTypeNumber[1].number <= item.max
        ) {
          (this.airTypeNumber[1] as any).bgColor = item.color;
        }
      }
      // PM2.5
      for (const item of this.PM25AirColors) {
        // PM2.5
        if (
          this.airTypeNumber[2].number >= item.min &&
          this.airTypeNumber[2].number <= item.max
        ) {
          (this.airTypeNumber[2] as any).bgColor = item.color;
        }
      }
      // PM10
      for (const item of this.PM10AirColors) {
        // PM10
        if (
          this.airTypeNumber[3].number >= item.min &&
          this.airTypeNumber[3].number <= item.max
        ) {
          (this.airTypeNumber[3] as any).bgColor = item.color;
        }
      }
      // O3
      for (const item of this.O3AirColors) {
        // O3
        if (
          this.airTypeNumber[4].number >= item.min &&
          this.airTypeNumber[4].number <= item.max
        ) {
          (this.airTypeNumber[4] as any).bgColor = item.color;
        }
      }
      // SO2
      for (const item of this.SO2AirColors) {
        // SO2
        if (
          this.airTypeNumber[5].number >= item.min &&
          this.airTypeNumber[5].number <= item.max
        ) {
          (this.airTypeNumber[5] as any).bgColor = item.color;
        }
      }
      // CO
      for (const item of this.COAirColors) {
        // CO
        if (
          this.airTypeNumber[6].number >= item.min &&
          this.airTypeNumber[6].number <= item.max
        ) {
          (this.airTypeNumber[6] as any).bgColor = item.color;
        }
      }
    });
  }
  // 获取地图站点和热力图
  private getAirStation() {
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const _this = this;
    // this.stationType.join()
    // const heatMapData: HeatMapData[] = [];
    allAirStation({
      stationTypeId: "1,2,3,4",
      pollutionCode: "aqi"
    }).then((res: any) => {
      this.mapMarker = res.data.data;
      /* eslint-disable @typescript-eslint/ban-ts-ignore */
      //@ts-ignore
      this.$bus.emit("sendMapMarker", this.mapMarker);
    });
  }
}
</script>
