import request from "@/utils/request";
import { AxiosPromise } from "axios";

/**
 * @method functionName
 * @param {type} data 说明
 * @description 获取用电站点统计
 */
export function getElectricitySite(params:any): AxiosPromise<any> {
  return request({
    url: `/water/electricity/stationStatusStatistics`,
    method: "get",
    params
  });
}
/**
 * @method functionName
 * @param {type} data 说明
 * @description 获取用电站点列表
 */
export function getElectricityMapList(params:any): AxiosPromise<any> {
  return request({
    url: `/water/electricity/listStation`,
    method: "get",
    params
  });
}
/**
 * @method functionName
 * @param {type} data 说明
 * @description 获取用电站点天数据
 */
export function getDayRecord(params:any): AxiosPromise<any> {
  return request({
    url: `/water/enterprise-electricity-day/bigDataListRecord`,
    method: "get",
    params
  });
}
/**
 * @method functionName
 * @param {type} data 说明
 * @description 获取用电站点小时数据
 */
export function getDayHourRecord(params:any): AxiosPromise<any> {
  return request({
    url: `/water/electricity/record/bigDataHourListRecord`,
    method: "get",
    params
  });
}
/**
 * @method functionName
 * @param {type} data 说明
 * @description 获取用电站点用电达标率
 */
export function getDataComplianceRateE(params:any): AxiosPromise<any> {
  return request({
    url: `/water/electricity/record/bigData/dataComplianceRate`,
    method: "get",
    params
  });
}