<template>
  <card title="事件响应">
    <div class="w-full content-box">
      <div class="flex items-center top">
        <digitalClock :start="start" :end="end"></digitalClock>
        <levelBox :value="level"></levelBox>
      </div>

      <!-- <div class="flex items-center w-full bottom">
        <div class="icon"></div>
        <div class="max-w-xs truncate"
          >预案名称：金牛区自然灾害应急预案</div
        >
      </div> -->
    </div>
  </card>
</template>

<script>
  import card from '@/components/card/index';
  import digitalClock from '@/components/digitalClock/index.vue'
  import levelBox from './levelBox.vue'
  export default {
    name: 'responseStatus',
    components: {
      card,
      digitalClock,
      levelBox
    },
    props: {
      level: {
        type: [Number],
        default: 0,
      },
      start: {
        type: [String, Date],
        default: '',
      },
      end: {
        type: [String, Date],
        default: '',
      },
    },
  }
</script>

<style lang="less" scoped>
  .top {
    padding: 0px 20px 20px 20px;
  }
.w-full{
  width: 100%;
}
.items-center{
  align-items:center;
}
.flex{
  display: flex;
}
  .bottom {
    padding: 10px 20px;
    font-size: 16px;
    color: #dbf5ff;
    .icon {
      width: 25px;
      height: 25px;
      background: url('../../../../assets/images/<EMAIL>') no-repeat
        center center;
      background-size: 100% 100%;
      margin-right: 20px;
    }
    height: 48px;
    background: url('../../../../assets/images/yjzh_yuan_bg2x.png') no-repeat
      center center;
    background-size: 100% 100%;
  }
</style>
