<template>
  <div class="analysisAir-container">
    <div class="container-bg"></div>
    <analysisMap
      class="center-map"
      :markerList="markerList"
      :distance="distance"
      :stationList="stationList"
      :drainList="drainList"
      :currentCompany="currentCompany"
      @handleClickCompanyMarker="handleClickMore"
      @handleChangeStation="handleChangeStation"
      @handleChangeRange="handleChangeRange"
      @handleRemoveStationList="handleRemoveStationList"
    ></analysisMap>
    <div class="right-container-box">
      <!-- 监测趋势 -->
      <div style="height: 36%" class="right-item-box right-to-left">
        <div class="video-main polluted-video" style="height:100%">
          <div class="common-title box-title" style="position: relative">
            <div class="title">{{ `监测趋势` }}</div>
          </div>
          <div class="sub-title" style="margin: 0">
            <img src="@/assets/biaoti.png" alt class="title-img" />
          </div>
          <template v-if="subTabs && subTabs.length">
              <div
              class="sub_tabs1"
              v-if="subTabs && subTabs.length"
              style="margin-top: 0.1rem"
            >
              <div
                v-for="tab in subTabs"
                :key="tab.itemCode"
                class="tab"
                @click="itemCode = tab.itemCode"
                :class="{
                  active: tab.itemCode === itemCode,
                }"
                :title="tab.name"
              >
                {{ tab.name }}
              </div>
            </div>
            <chart
              :propData="propData"
              :id="'HCLineC'"
              :width="'4rem'"
              :height="'2.45rem'"
              :smooth="true"
            />
            </template>
            <template v-else>
              <div
                style="
                  width: 100%;
                  height: 100%;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                "
              >
                <div style="width: 100%; text-align: center">
                  <img src="@/assets/<EMAIL>" alt="" />
                  <p
                    style="
                      height: 15px;
                      font-size: 14px;
                      font-family: PingFang SC;
                      font-weight: 500;
                      color: #d8e8fe;
                    "
                  >
                    暂无数据
                  </p>
                </div>
              </div>
            </template>
        </div>
      </div>
      <!-- 成因分析 -->
      <div style="height: 30%" class="right-item-box left-to-right">
        <div class="video-main polluted-video">
          <div class="common-title box-title" style="position: relative">
            <div class="title">{{ `成因分析` }}</div>
          </div>
          <div class="sub-title" style="margin: 0">
            <img src="@/assets/biaoti.png" alt class="title-img" />
          </div>
          <div class="cause-analysis-container">
            <div
              class="cause-analysis-box"
              v-if="analyseInfo.analysisBasis || analyseInfo.analysisConclusion"
            >
              <p class="cause-analysis-title">分析依据</p>
              <div
                class="cause-analysis-content"
                v-html="analyseInfo.analysisBasis || '--'"
              ></div>
              <p class="cause-analysis-title">分析结论</p>
              <p class="cause-analysis-content">
                {{ analyseInfo.analysisConclusion || "--" }}
              </p>
            </div>
            <div style="width: 100%; text-align: center" v-else>
              <img src="@/assets/<EMAIL>" alt="" />
              <p
                style="
                  height: 15px;
                  font-size: 14px;
                  font-family: PingFang SC;
                  font-weight: 500;
                  color: #d8e8fe;
                "
              >
                暂无数据
              </p>
            </div>
          </div>
        </div>
      </div>
      <!-- 涉污企业指纹库 -->
      <div style="height: 36%" class="right-item-box right-to-left">
        <div class="video-main polluted-video">
          <div class="common-title box-title" style="position: relative">
            <div class="title">涉污企业指纹库</div>
            <!-- <a-select
              style="width: 1rem"
              @change="changeDistance"
              v-model="distance"
              class="select-main"
            >
              <a-icon
                slot="suffixIcon"
                type="caret-down"
                style="color: rgba(0, 234, 255, 1); width: 14px; height: 8px"
              />
              <a-select-option
                v-for="item in distanceList"
                :key="item.name"
                :value="item.value"
              >
                {{ item.name }}
              </a-select-option>
            </a-select> -->
          </div>
          <div class="sub-title" style="margin: 0">
            <img src="@/assets/biaoti.png" alt class="title-img" />
          </div>
          <div class="fingerprint-container" ref="fingerprintContainer">
            <template v-if="[...concentDrainList, ...stationList].length">
              <div
                class="fingerprint-item"
                v-for="(item, index) in [...concentDrainList, ...stationList]"
                :key="item.stationId"
                @click="handleClickMore(item)"
              >
                <div class="fingerprint-title">
                  <div class="serial-number">
                    <span>{{
                      index >= 9 ? index + 1 : "0" + (index + 1)
                    }}</span>
                  </div>
                  <!-- :style="{
                      color: !item.online
                        ? ''
                        : item.isAlarm
                        ? '#F5542F'
                        : '#26FFED',
                    }" -->
                  <div
                    class="title"
                    :title="item.stationName"
                  >
                    {{ item.stationName }}
                  </div>
                  <div v-if="item.type === 8 || item.type === '排口'"  class="more">
                    更多>>
                  </div>
                </div>
                <div class="fingerprint-content">
                  <div class="title">
                    <!-- <span>{{ item.manItem.name || "--" }}：</span>
                    <span
                      :style="{
                        color:
                          item.online === 1
                            ? ''
                            : item.isAlarm
                            ? '#F5542F'
                            : '#26FFED',
                        fontFamily: 'YouSheBiaoTiHei',
                      }"
                      >{{
                        item.manItem.value ||
                        item.manItem.value === 0 ||
                        item.manItem.value === "0"
                          ? item.manItem.value
                          : "--"
                      }}</span
                    >
                    <span>{{ item.manItem.unit }}</span> -->
                  </div>
                  <div class="position">
                    <img src="@/assets/position.png" alt="" />
                    <span style="margin: 0 5px">距此</span>
                    <span>{{ item.distance }}</span>
                    <span>km</span>
                  </div>
                </div>
              </div>
            </template>
            <template v-else>
              <div
                style="
                  width: 100%;
                  height: 100%;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                "
              >
                <div style="width: 100%; text-align: center">
                  <img src="@/assets/<EMAIL>" alt="" />
                  <p
                    style="
                      height: 15px;
                      font-size: 14px;
                      font-family: PingFang SC;
                      font-weight: 500;
                      color: #d8e8fe;
                    "
                  >
                    暂无数据
                  </p>
                </div>
              </div>
            </template>
          </div>
        </div>
      </div>
    </div>
    <chartDialog
      :visibles="visibles"
      :currentStation="currentStation"
      @handleClose="handleClose"
    ></chartDialog>
    <el-dialog
      title=""
      :visible.sync="dialogVisible"
      class="waterOutletDialog"
      :append-to-body="false"
      :modal="false"
      width="100%"
      top="80px"
    >
      <waterOutlet v-if="dialogVisible" @handleClickClose="handleClickClose" :drainId="drainId"></waterOutlet>
    </el-dialog>
  </div>
</template>

<script>
// import AMap from 'AMap'
import AMapLoader from '@amap/amap-jsapi-loader'
import analysisMap from './components/map.vue'
import chart from './components/chart.vue'
import chartDialog from './components/dialog.vue'
import { Select, Icon } from 'ant-design-vue'
import waterOutlet from './components/water-outlet.vue'
import { getAllAirStationList, listStationItem, stationItemMonitorTrendHour, listStationRangeEnterprise, originAnalyse, listDrain } from '@/api/analysisWater'
export default {
  name: '',
  data() {
    return {
        subTabs: [],
        itemCode: '',
        HCLineType: null,
        markerList: [],
        stationId: null,
        propData:{
            thisStation: [],
            upStation: [],
            downStation: [],
            itemName: 'AQI',
            unit: ''
        },
        stationList: [],
        distanceList: [
            {name: '0.5公里', value: '500' },
            {name: '1公里', value: '1000' },
            {name: '1.5公里', value: '1500' },
            {name: '2公里', value: '2000' },
        ],
        distance: '500',
        analyseInfo: {},
        visibles: false,
        currentStation: null,
        drainList: [],
        concentDrainList:[],
        dialogVisible: false,
        drainId: null,
        currentCompany: {}
    }
  },
  created() {
    AMapLoader.reset()
    // // load 加载
    AMapLoader.load({
      "key": "777fec7ef3cc29281d60ae900fa33925",              // 申请好的Web端开发者Key，首次调用 load 时必填
      "version": "1.4.15",   // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
      "plugins": ['AMap.DistrictSearch','AMap.Heatmap','AMap.ControlBar','AMap.Object3DLayer','Map3D','AMap.Geocoder','AMap.CircleMarker','AMap.MouseTool'],           // 需要使用的的插件列表，如比例尺'AMap.Scale'等
      "AMapUI": {             // 是否加载 AMapUI，缺省不加载
        "version": '1.0',   // AMapUI 版本
        // "plugins":['overlay/SimpleMarker'],       // 需要加载的 AMapUI ui插件
      },
      "Loca":{                // 是否加载 Loca， 缺省不加载
        "version": '1.3.2'  // Loca 版本
      },
    }).then((AMap)=>{

    }).catch(e => {
      console.log(e);
    })
    this.getListDrain()
    this.getList()
  },
  methods: {
    // 获取排口列表
    getListDrain() {
      listDrain().then(res => {
            if(res.data.code === 200) {
                this.drainList = res.data.data.map(item => {
                  item.type = '排口'
                  return item
                }) || []
            } else {
                this.drainList = []
            }
        }).catch((_) => {
            this.drainList = []
        })
    },
    // 获取所有站点列表
    getList() {
      getAllAirStationList().then(res => {
          if(res.data.code === 200) {
              this.markerList = res.data.data || []
          } else {
              this.markerList = []
          }
      }).catch((_) => {
          this.markerList = []
      })
    },
    // 切换站点
    handleChangeStation(station) {
      const lnglat = new AMap.LngLat(station.lng, station.lat)
      const { stationId } = station
      this.concentDrainList = this.drainList.filter(item => {
        return item.riverId === station.riverId
      }).map(it=> {
        it.stationName = it.drainName
        it.distance = (lnglat.distance([it.lng, it.lat]) / 1000).toFixed(2)
        it.type = '排口'
        return it
      })
        // this.stationList = [...drainList, ...this.stationList]
        this.stationId = stationId
        this.getListStationRangeEnterprise()
        this.getOriginAnalyse()
        const params = {
            stationId: stationId
        }
        listStationItem(params).then(res=> {
           if(res.data.code === 200) {
            this.subTabs = res.data.data || []
            if(this.subTabs.length) {
                this.itemCode = this.subTabs[0].itemCode
                this.getStationItemMonitorTrendHour()
            }
           } else {
            this.subTabs = []
           }
        }).catch((_) => {
            this.subTabs = []
        })
    },
    // 获取站点24小时站点监测列表
    getStationItemMonitorTrendHour() {
        const params = {
            stationId: this.stationId,
            itemCode: this.itemCode
        }
        stationItemMonitorTrendHour(params).then(res=> {
            if(res.data.code === 200) {
              console.log('监测趋势', res.data.data)
                this.propData.thisStation = res.data.data.thisStation || []
                this.propData.upStation = res.data.data.upStation || []
                this.propData.downStation = res.data.data.downStation || []
            } else {
                this.propData.thisStation = []
                this.propData.upStation =  []
                this.propData.downStation =  []
            }
        }).catch((_) => {
            this.propData.thisStation = []
            this.propData.upStation =  []
            this.propData.downStation =  []
        })
    },
    // 获取指定站点附近污染企业站点列表
    getListStationRangeEnterprise() {
      return
        const params = {
            stationId: this.stationId,
            distance: this.distance,
            // enterpriseType: '5,6,7'
        }
        listStationRangeEnterprise(params).then(res=> {
            if(res.data.code === 200) {
                this.stationList = res.data.data || []
            } else {
                this.stationList = []
            }
        }).catch((_) => {
            this.stationList = []
        }).finally(_=> {
          this.scrollTOTop()
        })
    },
    // 切换距离
    changeDistance() {
        this.getListStationRangeEnterprise()
    },
    // 切换查询范围
    handleChangeRange(locationList) {
      const data = {
        stationId: this.stationId,
        locationList: locationList
      }
      listStationRangeEnterprise(data).then(res=> {
          if(res.data.code === 200) {
              this.stationList = res.data.data || []
          } else {
              this.stationList = []
          }
      }).catch((_) => {
          this.stationList = []
      }).finally(_=> {
        this.scrollTOTop()
      })
    },
    // 清除站点
    handleRemoveStationList() {
      this.stationList = []
    },
    // 空气站点成因分析
    getOriginAnalyse() {
      const params = {
        stationId: this.stationId
      }
      originAnalyse(params).then(res=> {
            if(res.data.code === 200) {
                this.analyseInfo = res.data.data || {}
                if(this.analyseInfo.analysisBasis){
                  this.analyseInfo.analysisBasis = this.analyseInfo.analysisBasis.replace(/\n\n/gi, '\n').replace(/\n/gi, '<br/>');
                }
            } else {
                this.analyseInfo = {}
            }
        }).catch((_) => {
            this.analyseInfo = []
        })
    },
    // 滚动到顶部
    scrollTOTop() {
      const fingerprintContainer = this.$refs.fingerprintContainer
      fingerprintContainer.scrollTop = 0
    },
    handleClickMore(item) {
      console.log('type', item)
      if(item.type === 8) {
        this.currentStation = item
        this.visibles = true
      }
      else if(item.type === '排口') {
        this.drainId = item.waterDrainId
        this.dialogVisible = true
      }
      else {
        this.currentCompany = {
          time: new Date().valueOf(),
          ...item
        }
      }
    },
    handleClose() {
      this.visibles = false
    },
    handleClickClose() {
      this.dialogVisible = false
    }
  },
  computed: {},
  watch: {
    itemCode() {
        this.getStationItemMonitorTrendHour()
        this.propData.itemName = this.subTabs.find(item => item.itemCode === this.itemCode).name
        this.propData.unit = this.subTabs.find(item => item.itemCode === this.itemCode).concentrationUnit
    }
  },
  components: {
    analysisMap,
    chart,
    ASelect: Select,
    AIcon: Icon,
    ASelectOption: Select.Option,
    chartDialog,
    waterOutlet
  }
}
</script>

<style lang="less" scoped>
.analysisAir-container {
  width: 100%;
  height: 100%;
  position: relative;
  .container-bg {
    position: absolute;
    pointer-events: none;
    z-index: 2;
    width: 100%;
    height: 100%;
    background-size: 100% 100%;
    opacity: 0.8;
    background-image: url("~@/assets/department/<EMAIL>");
  }
  .center-map {
    position: absolute;
    height: calc(1080px - 0.94rem);
    // margin-top: 0.15rem;
    > div {
      width: 100%;
      height: 100%;
    }
  }
  .right-container-box {
    position: absolute;
    pointer-events: auto;
    overflow: hidden;
    z-index: 9;
    top: 0;
    right: 0;
    height: calc(1080px - 0.94rem);
    width: 4.6rem;
    padding: 0.3rem 0.3rem 0;
    background-image: url(~@/assets/air_bg.png) !important;
    background-size: 100% 100% !important;
    // width: 200px;
    // background-color: pink;
    .right-item-box {
      .sub-title {
        > img {
          width: 50%;
          height: 0.1rem;
        }
      }
      .sub_tabs1 {
        display: flex;
        height: 0.3rem;
        line-height: 0.26rem;
        border: 0.5px solid #0e2344;
        box-sizing: border-box;
        width: 100%;
        margin-top: 0.1rem;
        margin-bottom: 0.05rem;
        .tab {
          font-size: 0.12rem;
          font-family: PingFang SC;
          font-weight: 500;
          color: #ffffff;
          background: #0a204a;
          cursor: pointer;
          text-align: center;
          overflow: hidden; // 超出边框外隐藏
          text-overflow: ellipsis; // 属性规定当文本溢出包含元素时发生的事情。
          white-space: nowrap; // 规定段落中的文本不进行换行
          flex: 1 auto;
        }
        .active {
          background-color: #2e8ef0;
        }
      }
    }
  }

  .cause-analysis-container {
    background: url("~@/assets/<EMAIL>") 100% 100% no-repeat;
    // border-image:url("~@/assets/<EMAIL>") 100 100 stretch;
    margin-top: 20px;
    padding: 20px 30px;
    width: 402px;
    height: 179px;
    .cause-analysis-box {
      width: 100%;
      height: 100%;
      overflow-y: auto;
      box-sizing: border-box;
      padding-right: 10px;
      &::-webkit-scrollbar-track {
        background: rgb(239, 239, 239);
        background: transparent;
        border-radius: 2px;
      }

      &::-webkit-scrollbar-thumb {
        background: #469fe78f;
        border-radius: 0.05rem;
      }

      &::-webkit-scrollbar-thumb:hover {
        background: #469fe7;
      }

      &::-webkit-scrollbar-corner {
        background: #469fe7;
      }
    }
    p {
      margin: 0;
    }
    .cause-analysis-title {
      font-family: PingFangSC-Medium;
      color: #38bafe;
      font-size: 14px;
      font-weight: bold;
    }
    .cause-analysis-content {
      font-size: 14px;
      color: #c2e2fa;
    }
  }
  .fingerprint-container {
    width: 100%;
    height: 260px;
    overflow-y: auto;
    &::-webkit-scrollbar {
      display: none;
    }
    .fingerprint-item {
      cursor: pointer;
      margin-bottom: 10px;
      width: 100%;
      // border: 1px solid #0E73BA;
      background: url("~@/assets/<EMAIL>") 100% 100% no-repeat;
      padding: 8px 14px;
      .fingerprint-title {
        margin-left: 20px;
        display: flex;
        position: relative;
        .serial-number {
          width: 30px;
          height: 24px;
          display: flex;
          justify-content: center;
          align-items: center;
          background: url("~@/assets/<EMAIL>");
          span {
            font-size: 16px;
            font-family: YouSheBiaoTiHei;
            font-weight: 400;
            color: #dd4545;
            background: linear-gradient(0deg, #43bcf4 0%, #9ed5ff 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
        }
        .title {
          flex: 1;
          font-size: 14px;
          font-family: Source Han Sans CN;
          font-weight: 500;
          color: #c2e2fa;
          margin-left: 12px;
          white-space: nowrap;
          width: 100%;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .more {
          font-size: 10px;
          font-family: PingFang SC;
          font-weight: 400;
          color: #738999;
          cursor: pointer;
        }
      }
      .fingerprint-content {
        display: flex;
        .title {
          flex: 1;
          margin-left: 62px;
          font-size: 14px;
          font-family: PingFang SC;
          font-weight: 500;
          color: #8dabc0;
        }
        .position {
          font-size: 12px;
          font-family: PingFang SC;
          font-weight: 400;
          color: #abc9e0;
          display: flex;
          align-items: center;
        }
      }
    }
  }
}
// 右到左
.right-to-left {
  animation: pu-randa-r2l 1.5s linear;
}
@keyframes pu-randa-r2l {
  0% {
    transform: translate(120%, 0);
  }
  50% {
    transform: translate(120%, 0);
  }
  100% {
    transform: translate(0, 0);
  }
}
// 左到右
.left-to-right {
  animation: pu-randa-l2r 1.5s linear;
}
@keyframes pu-randa-l2r {
  0% {
    transform: translate(-120%, 0);
  }
  50% {
    transform: translate(-120%, 0);
  }
  100% {
    transform: translate(0, 0);
  }
}
/deep/ .el-dialog__wrapper.waterOutletDialog {
  z-index: 100000 !important;
  .el-dialog {
    background: transparent !important;
    margin-top: 80px;

  }
  .el-dialog__body {
    padding: 0;
  }
  .el-dialog__header {
      display: none;
    }
}

</style>
