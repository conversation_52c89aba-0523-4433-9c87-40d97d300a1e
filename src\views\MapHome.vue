<style lang="less" scoped>
.home-map {
  display: flex;
  justify-content: space-between;
  flex-direction: column;
  background-image: url('../assets/bg.png');
  background-size: 100% 100%;
  overflow: hidden;
  .home-map-top {
    position: relative;
    flex: 10 1 auto;
    display: flex;
    justify-content: space-between;
    .home-map-left {
      height: 100%;
      width: 4.5rem;
      padding: 0.5rem 0 0 0.5rem;
      box-sizing: border-box;
      position: absolute;
      top: 0;
      left: 0;
      opacity: 1;
      scale: 1;
      z-index: 998;
      flex: 2 1 0;
      display: flex;
      justify-content: space-between;
      background-image: url(../assets/air_bg_left.png) !important;
      background-size: 100% 100% !important;
      flex-direction: column;
      transition: all 1.5s ease-out;
    }
    .left-panel-to-left {
      scale: 1.2;
      left: -4.5rem;
      opacity: 0.4;
    }
    .home-map-center {
      flex: 1;
      position: relative;
      > div {
        position: absolute;
        top: 0;
        left: 0;
      }
    }
    .home-map-right {
      padding: 0.5rem 0.5rem 0 0.15rem;
      box-sizing: border-box;
      // background: rgba(11, 21, 44, 0.4);
      background-image: url(../assets/air_bg.png) !important;
      background-size: 100% 100% !important;
      height: 100%;
      width: 4.5rem;
      position: absolute;
      top: 0;
      scale: 1.2;
      opacity: 0.4;
      right: -4.5rem;
      flex: 2 1 0;
      z-index: 998;
      display: flex;
      justify-content: space-between;
      flex-direction: column;
      transition: all 1.5s ease-out;
    }
    .right-panel-to-left {
      scale: 1;
      right: 0;
      opacity: 1;
    }
    .comment-content {
      // flex: 1 1 0;
      // border: 0.01rem solid #f53;
    }
  }
  .home-map-bot {
    position: absolute;
    width: 100%;
    height: 1.5rem;
    line-height: 1.5rem;
    background-image: url('../assets/<EMAIL>');
    background-size: 100% 100%;
    bottom: 0;
    flex: 1 1 0;
    display: flex;
    justify-content: center;
    // .composite-command {
    //   text-align: center;
    //   font-size: 0.24rem;
    //   cursor: pointer;
    //   position: absolute;
    //   right: 1.7rem;
    //   width: 1.8rem;
    //   height: 0.8rem;
    //   background-image: url("./../assets/<EMAIL>");
    //   background-repeat: no-repeat;
    //   background-size: 100% 100%;
    // }
    .btn-group {
      display: flex;
      justify-content: space-between;
      align-items: center;
      z-index: 100;
      .btn-common {
        // cursor: pointer;
        width: 1.2rem;
        height: 0.36rem;
        line-height: 0.36rem;
        text-align: center;
        font-size: 0.16rem;
        // background: linear-gradient(45deg, #1e39ff, #0ed8e2);
        // background: linear-gradient(45deg, #faad14, #961805);
        margin-right: 0.5rem;
        transition:
          background 0.5s ease-in,
          transfrom 0.5s ease-out;
        &:nth-last-child(1) {
          margin-right: 0rem;
        }
      }
      .btn-with-bg {
        box-shadow: 0px 0px 8px 2px rgba(123, 201, 255, 0.65);
        background: rgba(49, 76, 161, 0.5);
        border: 2px solid rgba(110, 161, 196, 1);
      }
    }
  }
  .command-mode {
    position: relative;
    height: 100%;
    // border: 0.01rem solid #f12;
    background-image: url('./../assets/-s-bg.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    display: flex;
    justify-content: center;
    .exit-mode {
      position: absolute;
      width: 1.5rem;
      height: 1.2rem;
      right: 0.5rem;
      bottom: 0.15rem;
      background-image: url('./../assets/<EMAIL>');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      cursor: pointer;
    }
    .center-jinniu {
      position: absolute;
      width: 1.2rem;
      height: 1.2rem;
      padding: 0.58rem 0.2rem 0.45rem;
      top: 40%;
      background-image: url('./../assets/<EMAIL>');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      cursor: pointer;
      transition: transform 1s ease;
      animation:
        bounceUpDown2 0.8s ease 0.5s 1 normal forwards,
        rotateInfinite 1s linear 2s infinite alternate none;
    }
    .bottom-time {
      display: flex;
      align-items: center;
      position: absolute;
      justify-content: center;
      width: 8rem;
      height: 2.1rem;
      // width: 1.2rem;
      // height: 1.2rem;
      padding: 0.58rem 0.2rem 0.45rem;
      top: 2.6rem;
      // background-image: url("./../assets/<EMAIL>");
      background-image: url('./../assets/<EMAIL>');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      cursor: pointer;
      transition: transform 1s ease;
      animation: bounceUpDown 0.8s ease 0.5s 1 normal forwards;
      //   rotateInfinite 1s linear 2s infinite alternate none;
      .time,
      .date {
        text-align: center;
        // font-family: CAI978;
        height: 0.75rem;
        line-height: 0.75rem;
      }
      .time {
        font-size: 0.5rem;
        .number {
          display: inline-block;
          margin-right: 0.06rem;
          width: 0.6rem;
          height: 0.7rem;
          line-height: 0.72rem;
          background: url('../assets/<EMAIL>') no-repeat center;
          background-size: 100% 100%;
          box-shadow: 0rem 0rem 1rem 0rem rgba(1, 9, 96, 0.5);
        }
      }
      .date {
        font-size: 0.36rem;
        height: 0.4rem;
        line-height: 0.4rem;
        letter-spacing: 4px;
        margin-top: 0.05rem;
      }
    }
    .left-circle {
      position: absolute;
      // width: 6rem;
      // height: 1.1rem;
      width: 6.54rem;
      height: 2rem;
      top: 3rem;
      left: 0.82rem;
      background-image: url('./../assets/four.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      transition: opacity 1s ease;
      animation: fadeIn 1.5s ease 0.5 normal forwards;
    }
    .right-circle {
      position: absolute;
      // width: 6rem;
      // height: 1.1rem;
      width: 6.54rem;
      height: 2rem;
      top: 3rem;
      right: 0.82rem;
      background-image: url('./../assets/four.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      transition: opacity 1s ease;
      animation: fadeIn 1.5s ease 0.5 normal forwards;
    }
    .left-water {
      position: absolute;
      width: 1.3rem;
      height: 1.5rem;
      top: 6.3rem;
      left: 9.3rem;
      background-image: url('./../assets/<EMAIL>');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      transition: opacity 1s ease-in;
      animation: leftWater 1.5s ease 0.5s forwards;
      cursor: pointer;
      &:hover {
        transform: scale(1.2);
      }
    }
    .left-air {
      position: absolute;
      width: 1.5rem;
      height: 1.5rem;
      opacity: 0;
      top: 1.3rem;
      left: 9.3rem;
      background-image: url('./../assets/<EMAIL>');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      transition: opacity 1s ease-in;
      animation: leftAir 1.5s ease 0.5s forwards;
      cursor: pointer;
      &:hover {
        transform: scale(1.2);
      }
    }
    .left-pollution {
      position: absolute;
      width: 1.3rem;
      height: 1.5rem;
      opacity: 0;
      top: 6.3rem;
      left: 9.3rem;
      // background-image: url('./../assets/<EMAIL>');
      background-image: url('./../assets/wryjg.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      transition: opacity 1s ease-in;
      animation: leftPollution 1.5s ease 0.5s forwards;
      cursor: pointer;
      &:hover {
        transform: scale(1.2);
      }
    }
    .left-equipment {
      position: absolute;
      width: 1.3rem;
      height: 1.5rem;
      opacity: 0;
      top: 6.3rem;
      left: 9.3rem;
      background-image: url('./../assets/shenghuanjin.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      transition: opacity 1s ease-in;
      animation: leftEquipment 1.5s ease 0.5s forwards;
      // cursor: not-allowed;
      cursor: pointer;
      &:hover {
        transform: scale(1.2);
      }
    }
    .right-situation {
      position: absolute;
      width: 1.3rem;
      height: 1.5rem;
      opacity: 0;
      top: 6.3rem;
      right: 9.3rem;
      background-image: url('./../assets/<EMAIL>');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      transition: opacity 1s ease-in;
      animation: rightSituation 1.5s ease 0.5s forwards;
      cursor: pointer;
      &:hover {
        transform: scale(1.2);
      }
    }

    .right-yanlan {
      position: absolute;
      width: 1.3rem;
      height: 1.5rem;
      opacity: 0;
      top: 6.3rem;
      right: 9.3rem;
      background-image: url('./../assets/<EMAIL>');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      transition: opacity 1s ease-in;
      animation: yanlan 1.5s ease 0.5s forwards;
      cursor: pointer;
      &:hover {
        transform: scale(1.2);
      }
    }
    .right-health {
      position: absolute;
      width: 1.3rem;
      height: 1.5rem;
      opacity: 0;
      top: 6.3rem;
      right: 9.3rem;
      background-image: url('./../assets/<EMAIL>');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      transition: opacity 1s ease-in;
      animation: health 1.5s ease 0.5s forwards;
      cursor: pointer;
      &:hover {
        transform: scale(1.2);
      }
    }
    .right-task {
      position: absolute;
      width: 1.3rem;
      height: 1.5rem;
      opacity: 0;
      top: 6.3rem;
      right: 9.3rem;
      background-image: url('./../assets/<EMAIL>');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      transition: opacity 1s ease-in;
      animation: rightTask 1.5s ease 0.5s forwards;
      cursor: pointer;
      &:hover {
        transform: scale(1.2);
      }
    }
    .right-mail {
      position: absolute;
      width: 1.3rem;
      height: 1.5rem;
      opacity: 0;
      top: 6.3rem;
      right: 9.3rem;
      background-image: url('./../assets/<EMAIL>');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      transition: opacity 1s ease-in;
      animation: rightMail 1.5s ease 0.5s forwards;
      cursor: pointer;
      &:hover {
        transform: scale(1.2);
      }
    }
    .right-composite {
      position: absolute;
      opacity: 0;
      width: 1.3rem;
      height: 1.5rem;
      top: 6.3rem;
      right: 9.3rem;
      background-image: url('./../assets/<EMAIL>');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      transition: opacity 1s ease-in;
      animation: rightComposite 1.5s ease 0.5s forwards;
      cursor: pointer;
      &:hover {
        transform: scale(1.2);
      }
    }
  }
  @keyframes bounceUpDown {
    0% {
      top: 1.8rem;
    }
    40% {
      top: 6.3rem;
    }
    65% {
      top: 5.5rem;
    }
    100% {
      top: 75%;
    }
  }
  @keyframes bounceUpDown2 {
    0% {
      top: 1.8rem;
    }
    40% {
      top: 5.3rem;
    }
    65% {
      top: 4.5rem;
    }
    100% {
      top: 49%;
    }
  }
  @keyframes rotateInfinite {
    0% {
      transform: rotateY(0deg);
    }
    100% {
      transform: rotateY(360deg);
    }
  }
  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
  @keyframes leftWater {
    from {
      top: 6.3rem;
      left: 9.3rem;
    }
    to {
      top: 4.2rem;
      left: 2.2rem;
    }
  }
  @keyframes leftAir {
    from {
      opacity: 0;
      top: 6.3rem;
      left: 9.3rem;
    }
    to {
      opacity: 1;
      top: 4.2rem;
      left: 4.6rem;
    }
  }
  @keyframes leftPollution {
    from {
      opacity: 0;
      top: 6.3rem;
      left: 9.3rem;
    }
    to {
      opacity: 1;
      top: 2.4rem;
      left: 1.55rem;
    }
  }
  @keyframes leftEquipment {
    from {
      opacity: 0;
      top: 6.3rem;
      left: 9.3rem;
    }
    to {
      opacity: 1;
      top: 2.45rem;
      left: 5.3rem;
    }
  }
  @keyframes rightSituation {
    from {
      opacity: 0;
      top: 6.3rem;
      right: 9.3rem;
    }
    to {
      opacity: 1;
      top: 4.2rem;
      right: 2.2rem;
    }
  }

  @keyframes yanlan {
    from {
      opacity: 0;
      top: 6.3rem;
      right: 9.3rem;
    }
    to {
      opacity: 1;
      top: 2.45rem;
      right: 5.4rem;
    }
  }
  @keyframes health {
    from {
      opacity: 0;
      top: 6.3rem;
      right: 9.3rem;
    }
    to {
      opacity: 1;
      top: 2.2rem;
      right: 3.5rem;
    }
  }
  @keyframes rightTask {
    from {
      opacity: 0;
      top: 6.3rem;
      right: 9.3rem;
    }
    to {
      opacity: 1;
      top: 3.3rem;
      right: 5.9rem;
    }
  }
  @keyframes rightMail {
    from {
      opacity: 0;
      top: 6.3rem;
      right: 9.3rem;
    }
    to {
      opacity: 1;
      top: 2.4rem;
      right: 1.6rem;
    }
  }
  @keyframes rightComposite {
    from {
      opacity: 0;
      top: 6.3rem;
      right: 9.3rem;
    }
    to {
      opacity: 1;
      top: 4.2rem;
      right: 4.6rem;
    }
  }
  .left-panel-to-right {
    animation: leftToRight 1.5s linear 0s forwards;
  }
  .move-to-right {
    animation: rightSwift 1.5s linear 0s forwards;
  }
  .left-toggle-show {
    animation: leftSwiftShow 1.5s linear 0s forwards;
  }
  .right-toggle-show {
    animation: rightSwiftShow 1.5s linear 0s forwards;
  }
  .swiper-text {
    font-size: 0.44rem;
    display: flex;
    align-items: center;
    span {
      &:nth-child(1) {
        margin-right: 0.27rem;
      }
    }
    .number {
      display: flex;
      justify-content: center;
      align-items: center;
      // display: inline-block;
      margin-right: 0.06rem;
      width: 0.6rem;
      height: 0.7rem;
      text-align: center;
      line-height: 0.72rem;
      font-size: 0.6rem;
      font-family: '300-CAI978';
      background: url('../assets/<EMAIL>') no-repeat center;
      background-size: 100% 100%;
      box-shadow: 0rem 0rem 1rem 0rem rgba(1, 9, 96, 0.5);
    }
  }
  .map-bg {
    width: 100%;
    height: 100%;
    z-index: 2;
    opacity: 0;
    position: absolute;
  }
}
</style>
<style lang="less">
.home-map {
  display: flex;
  justify-content: flex-start;
  position: relative;
  align-items: top;
  .control-panel-main {
    flex: 1;
    padding: 0.2rem 0.3rem;
    overflow-y: auto;
    .control-panel-tree {
      color: #cccccc;
      font-size: 0.2rem;
      > span {
        display: inline-block;
        vertical-align: middle;
      }
      > ul {
        li > {
          display: flex;
        }
      }
    }
  }
  .bottom-time {
    .swiper-slide {
      display: flex;
      justify-content: center;
    }
  }
}
</style>

<template>
  <section class="home-map">
    <section v-show="!isCommandMode" class="home-map-top">
      <!-- 左侧 -->
      <section class="home-map-left" :class="currentIndex % 2 == 1 ? 'left-panel-to-left' : ''">
        <!-- currentIndex -->
        <component :is="BtnGroup[currentIndex].code" v-if="currentIndex % 2 == 0" />
      </section>
      <!-- 右侧 -->
      <section class="home-map-right" :class="currentIndex % 2 == 1 ? 'right-panel-to-left' : ''">
        <component :is="BtnGroup[currentIndex].code" v-if="currentIndex % 2 == 1" />
      </section>
      <section class="home-map-center">
        <gao-de-map
          ref="map"
          :mapZoom="13.2"
          :style="{
            opacity: currentIndex !== 1 && currentIndex !== 3 ? '1' : '0',
            'z-index': currentIndex != 1 && currentIndex !== 3 ? '1' : '0',
          }"
          style="transition: all 1s linear"
          :mapStyle="mapStyle"
          :viewCenter="currentViewCenter"
          :typeColor="'aqi'"
          :enterType="4"
          :buildingMarker="currentIndex == 2 || currentIndex == 1 ? heavyPollutionEnterpriseList : []"
          :mapIndex="currentIndex"
          :companyList="companyList"
          :printFactoryList="printFactoryList"
          :garageList="garageList"
          :gasList="gasList"
          :restaurantList="restaurantList"
          :mapMarker="mapMarkerAir"
          :taskMarkerList="taskMarkerList"
        ></gao-de-map>
        <gao-de-map-water
          ref="map2"
          :mapZoom="12.8"
          :style="{
            opacity: currentIndex == 1 ? '1' : '0',
            'z-index': currentIndex == 1 ? '1' : '0',
          }"
          style="transition: all 1s linear"
          :mapStyle="mapStyle"
          :viewCenter="{
            lng: 104.1,
            lat: 30.725,
          }"
          :enterType="2"
          :buildingMarker="currentIndex == 2 || currentIndex == 1 ? heavyPollutionEnterpriseList : []"
          :mapIndex="currentIndex"
          :mapMarker="mapMarkerWater"
        ></gao-de-map-water>
        <gao-de-map-vachi
          :style="{
            opacity: currentIndex == 3 ? '1' : '0',
            'z-index': currentIndex == 3 ? '1' : '0',
          }"
          ref="map3"
          :mapStyle="mapStyle"
          :viewCenter="{ lng: 104.1, lat: 30.725 }"
          :mapZoom="13"
          :enterType="3"
          :oldCarPoint="oldCarPoint"
          :newCarPoint="newCarPoint"
          :viewMode="'2D'"
          :mapIndex="currentIndex"
          :mapMarker="mapMarker"
        ></gao-de-map-vachi>
      </section>
      <section class="map-bg"></section>
    </section>
    <section v-if="!isCommandMode" class="home-map-bot">
      <div class="btn-group">
        <div
          v-for="(btn, index) in BtnGroup"
          :key="index"
          class="btn-common"
          :class="index === currentIndex ? 'btn-with-bg' : ''"
          :style="{
            border: index === currentIndex ? '1px solid rgba(110,161,196,1)' : '1px solid rgba(255,255,255,1)',
            transform: index === currentIndex ? 'scale(1.15)' : 'scale(0.9)',
          }"
        >
          <!-- @click="changeCurrentIndex(index)" -->
          {{ btn.name }}
        </div>
      </div>
      <!-- <div class="composite-command" @click="toggleCommandMode"></div> -->
    </section>
    <section v-if="isCommandMode" class="command-mode">
      <!-- <div class="exit-mode" @click="toggleCommandMode"></div> -->
      <div class="center-jinniu"></div>
      <div class="bottom-time">
        <!-- <div class="time">
          <span class="number">{{ dateTime.hour.split("")[0] }}</span>
          <span class="number">{{ dateTime.hour.split("")[1] }}</span>
          :
          <span class="number">{{ dateTime.minute.split("")[0] }}</span>
          <span class="number">{{ dateTime.minute.split("")[1] }}</span>
          :
          <span class="number">{{ dateTime.second.split("")[0] }}</span>
          <span class="number">{{ dateTime.second.split("")[1] }}</span>
        </div>
        <div class="date">
          {{ `${dateTime.year}-${dateTime.month}-${dateTime.day}` }}
        </div> -->
        <swiper :options="warnList">
          <swiper-slide>
            <div class="swiper-text">
              <span>今日空气站点告警数量</span>
              <span class="number">{{ airAlarmCount ? airAlarmCount[0] : 0 }}</span>
              <span class="number">{{ airAlarmCount ? airAlarmCount[1] : 0 }}</span>
              <span class="number">{{ airAlarmCount ? airAlarmCount[2] : 0 }}</span>
            </div>
          </swiper-slide>
          <swiper-slide>
            <div class="swiper-text">
              <span>今日水质站点告警数量</span>
              <span class="number">{{ waterAlarmCount ? waterAlarmCount[0] : 0 }}</span>
              <span class="number">{{ waterAlarmCount ? waterAlarmCount[1] : 0 }}</span>
              <span class="number">{{ waterAlarmCount ? waterAlarmCount[2] : 0 }}</span>
            </div>
          </swiper-slide>
        </swiper>
      </div>
      <div class="left-circle"></div>
      <div class="right-circle"></div>
      <div
        class="left-water"
        @click="
          routeDistrbute({
            name: '水环境质量',
            value: '/waterQuality',
          })
        "
      ></div>
      <div
        class="left-air"
        @click="
          routeDistrbute({
            name: '大气环境质量',
            value: '/airQuality',
          })
        "
      ></div>
      <div
        class="left-pollution"
        @click="
          routeDistrbute({
            name: '污染源监管',
            value: '/otherPages',
          })
        "
      ></div>
      <div
        class="left-equipment"
        @click="
          routeDistrbute({
            name: '声环境质量',
            value: '/SoundEnvironment',
          })
        "
      ></div>
      <!-- <div
        class="left-equipment"
        @click="
          routeDistrbute({
            name: '设备工况',
            value: '/equipmentCondition'
          })
        "
      ></div> -->
      <div
        class="right-yanlan"
        @click="
          routeDistrbute({
            name: '演变复盘',
            value: '/rehearseAnalyse',
          })
        "
      ></div>
<!--      <div-->
<!--        class="right-health"-->
<!--        @click="-->
<!--          routeDistrbute({-->
<!--            name: '环境健康',-->
<!--            value: '/env-health',-->
<!--          })-->
<!--        "-->
<!--      ></div>-->
      <div
        class="right-situation"
        @click="
          routeDistrbute({
            name: '数据统计',
            value: '/homeTable',
          })
        "
      ></div>
      <!-- <div
        class="right-task"
        @click="
          routeDistrbute({
            name: '任务调度',
            value: '/taskManagement'
          })
        "
      ></div> -->
      <div
        class="right-mail"
        @click="
          routeDistrbute({
            name: '应急指挥',
            value: '/emergencyCommand',
          })
        "
      ></div>
      <div
        class="right-composite"
        @click="
          routeDistrbute({
            name: '部门联动',
            value: '/taskManagement',
          })
        "
      ></div>
    </section>
  </section>
</template>

<script lang="ts">
interface MapCenter {
  lng: number
  lat: number
}
import { Component, Vue, Watch } from 'vue-property-decorator'
import { Swiper, SwiperSlide } from 'vue-awesome-swiper'
import Header from '@/layout/modules/Header.vue'
import GaoDeMap from '@/components/GaoDeMap/index-old.vue'
import GaoDeMapWater from '@/components/GaoDeMap/index-water.vue'
import GaoDeMapVachi from '@/components/GaoDeMap/index-vachi.vue'
import { Tree, Icon } from 'ant-design-vue'
import LineChartDashed from '@/components/Charts/LineChartDashed.vue'
import LineChartColor from '@/components/Charts/LineChartColor.vue'
import ProportionChart from '@/components/Charts/ProportionChart.vue'
import SitePie from '@/components/Charts/SitePie.vue'
import MapHomeAqi from '@/components/Charts/MapHomeAqi.vue'
import AirDoublePie from '@/components/Charts/AirDoublePie.vue'
import RotateBarSolid from '@/components/Charts/RotateBarSolid.vue'
import AirPage from '@/components/MapHomeCommon/AirPage.vue'
import VehiclesPage from '@/components/MapHomeCommon/VehiclesPage.vue'
import TaskPage from '@/components/MapHomeCommon/TaskPage.vue'
import PollutionPage from '@/components/MapHomeCommon/PollutionPage.vue'
import WarterPage from '@/components/MapHomeCommon/WarterPage.vue'
import { countThisAirDay, countThisWaterDay } from '@/api/homeMap'
import { socketUrl } from '@/utils/index'
/* eslint-disable @typescript-eslint/ban-ts-ignore */
//@ts-ignore
// import AMap from 'AMap'
import AMapLoderMix from '@/mixins/AMap.js'
import AMapLoader from '@amap/amap-jsapi-loader'
import { watch } from 'fs'
@Component({
  name: 'HomeMap',
  mixins: [AMapLoderMix],
  components: {
    Header,
    GaoDeMap,
    GaoDeMapWater,
    GaoDeMapVachi,
    ATree: Tree,
    ATreeNode: Tree.TreeNode,
    AIcon: Icon,
    LineChartDashed,
    LineChartColor,
    ProportionChart,
    SitePie,
    MapHomeAqi,
    AirDoublePie,
    RotateBarSolid,
    AirPage,
    VehiclesPage,
    TaskPage,
    PollutionPage,
    WarterPage,
    Swiper,
    SwiperSlide,
  },
})
export default class extends Vue {
  private warnList: any = {
    direction: 'horizontal',
    loop: true,
    autoplay: {
      delay: 5000,
      disableOnInteraction: true,
    },
  }
  readonly mapStyle: string = 'amap://styles/0739a6632d849ae1b5e6f1c52f7d12c3'

  private currentViewCenter: MapCenter = {
    lng: 104.1,
    lat: 30.9,
  }
  // 地图标记点
  private mapMarkerAir: any = []
  private mapMarkerWater: any = []
  private taskMarkerList: any[] = []
  // 指挥模式
  private isCommandMode = false
  private togglePanel = true
  // 定时器
  private toggleTimer: any = null
  private timeTimer: any = null
  private mapMoveTimer: any = null
  private dateTime: any = {}
  private currentIndex = 0
  readonly BtnGroup: Array<any> = [
    {
      name: '大气环境',
      code: 'AirPage',
    },
    {
      name: '水环境',
      code: 'WarterPage',
    },
    {
      name: '污染源',
      code: 'PollutionPage',
    },
    {
      name: '综合执法',
      code: 'VehiclesPage',
    },
    {
      name: '任务调度',
      code: 'TaskPage',
    },
    // {
    //   name: "数据统计",
    //   code: "VehiclesPage"
    // }
  ]
  // 车辆相关
  private socket: any = null
  private mapMarker: any = []
  private newCarPoint = {}
  private oldCarPoint = {}
  private connect() {
    // this.socket = new WebSocket("ws://192.168.0.135:22000/ws");
    // this.socket = new WebSocket("ws://ep.vankeytech.com:8835/ws");
    // this.socket = new WebSocket("ws://www.jinnq.com:8834/ws");
    this.socket = new WebSocket(socketUrl())
    // 监听socket连接
    this.socket.onopen = this.open
    // 监听socket错误信息
    this.socket.onerror = this.error
    // 监听socket消息
    this.socket.onmessage = this.getMessage
    window.onbeforeunload = () => {
      this.socket.onopen = () => {}
      this.socket.onerror = () => {}
      this.socket.onmessage = () => {}
      this.socket.close()
    }
  }
  private open() {
    this.send()
  }
  private error() {
    console.log('连接错误')
  }
  private getMessage(msg: any) {
    const res: any = JSON.parse(msg.data)
    const mapMarker: any = []
    if (res.code == -2) {
      const mapMarker: any = []
      for (const item of JSON.parse(msg.data).carList) {
        // 高德地图坐标转换
        AMap.convertFrom([item.gps.lng, item.gps.lat], 'gps', (status: string, result: any) => {
          mapMarker.push({
            type: item.typeId,
            longitude: result.locations[0].lng,
            latitude: result.locations[0].lat,
            license: item.license,
            carId: item.carId,
            deptId: item.deptId,
          })
        })
      }
      // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
      // @ts-ignore
      this.mapMarker = mapMarker
    }
    if (res.code == 3) {
      // 车辆移动
      for (const i in this.mapMarker) {
        const marker = this.mapMarker[i]
        // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
        // @ts-ignore
        if (marker.carId == res.car.carId) {
          // @ts-ignore
          this.oldCarPoint = marker
          // 高德地图坐标转换
          AMap.convertFrom([res.car.gps.lng, res.car.gps.lat], 'gps', (status: string, result: any) => {
            // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
            // @ts-ignore
            this.newCarPoint = {
              type: res.car.typeId,
              longitude: result.locations[0].lng,
              latitude: result.locations[0].lat,
              license: res.car.license,
              carId: res.car.carId,
              deptId: res.car.deptId,
            }
          })
        }
      }
    }
  }
  private send() {
    this.socket.send(JSON.stringify({ code: 1, token: 'hello123' }))
    this.socket.send(JSON.stringify({ code: 2, data: {} }))
  }

  // 获取重污染企业信息
  private heavyPollutionEnterpriseList: any = []
  // 刷新页面定时器
  private pageReloadTimer: any = null
  mounted() {
    AMapLoader['reset']()
    // // load 加载
    AMapLoader.load({
      key: '777fec7ef3cc29281d60ae900fa33925', // 申请好的Web端开发者Key，首次调用 load 时必填
      version: '1.4.15', // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
      plugins: [
        'AMap.DistrictSearch',
        'AMap.Heatmap',
        'AMap.ControlBar',
        'AMap.Object3DLayer',
        'Map3D',
        'AMap.Geocoder',
        'AMap.CircleMarker',
        'AMap.MouseTool',
      ], // 需要使用的的插件列表，如比例尺'AMap.Scale'等
      AMapUI: {
        // 是否加载 AMapUI，缺省不加载
        version: '1.0', // AMapUI 版本
        // "plugins":['overlay/SimpleMarker'],       // 需要加载的 AMapUI ui插件
      },
      Loca: {
        // 是否加载 Loca， 缺省不加载
        version: '1.3.2', // Loca 版本
      },
    })
      .then((amps) => {
        ;(window as any).AMap = amps
        console.log(window.AMap, 'map-home-----------------------1059')
      })
      .catch((e) => {
        console.log(e)
      })
    // 如果在这个页面停留了4个小时重启当前页面
    this.pageReloadTimer = setTimeout(() => location.reload(), 4 * 60 * 60 * 1000)
    this.$once('hook:beforeDestroy', () => {
      clearTimeout(this.pageReloadTimer)
    })
    this.getAlarmCount()
    /* eslint-disable @typescript-eslint/ban-ts-ignore */
    //@ts-ignore
    this.$bus.on('toggleMode', (arg: any) => {
      this.isCommandMode = arg[0]
      if (this.isCommandMode) {
        this.currentIndex = 0
        clearInterval(this.toggleTimer)
        clearInterval(this.mapMoveTimer)
        this.timeTimer = setInterval(() => {
          this.dateTime = this.getTime()
        }, 1000)

        this.$once('hook:beforeDestroy', () => {
          clearInterval(this.timeTimer)
        })
      } else {
        clearInterval(this.timeTimer)
        this.toggleTimer = setInterval(() => {
          this.mapMarkerWater = []
          this.mapMarkerAir = []
          this.heavyPollutionEnterpriseList = []
          this.companyList = []
          this.printFactoryList = []
          this.garageList = []
          this.gasList = []
          this.restaurantList = []
          this.taskMarkerList = []
          this.currentIndex < this.BtnGroup.length - 1 ? this.currentIndex++ : (this.currentIndex = 0)
        }, 10 * 1000)

        this.$once('hook:beforeDestroy', () => {
          clearInterval(this.toggleTimer)
        })
      }
    })
    // 空气站点
    /* eslint-disable @typescript-eslint/ban-ts-ignore */
    //@ts-ignore
    this.$bus.on('sendMapMarker', (res: any) => {
      this.mapMarkerAir = res[0]
    })
    // 水质站点
    /* eslint-disable @typescript-eslint/ban-ts-ignore */
    //@ts-ignore
    this.$bus.on('sendWaterMapMarker', (res: any) => {
      this.mapMarkerWater = res[0]
    })
    // 获取重污染企业信息
    /* eslint-disable @typescript-eslint/ban-ts-ignore */
    //@ts-ignore
    this.$bus.on('sendPollutingMapMarker', (res: any) => {
      this.heavyPollutionEnterpriseList = res[0]
    })
    // 获取重污染企业信息
    /* eslint-disable @typescript-eslint/ban-ts-ignore */
    //@ts-ignore
    this.$bus.on('sendCompanyList', (res: any) => {
      this.companyList = res[0]
    })
    // 获取重污染企业信息
    /* eslint-disable @typescript-eslint/ban-ts-ignore */
    //@ts-ignore
    this.$bus.on('sendTaskMarkerList', (res: any) => {
      this.taskMarkerList = res[0]
    })
    // 获取车辆
    /* eslint-disable @typescript-eslint/ban-ts-ignore */
    //@ts-ignore
    this.$bus.on('sendCarList', (res: any) => {
      this.mapMarker = res[0]
    })

    this.currentViewCenter = {
      lng: 104.01,
      lat: 30.7,
    }
    // 获取重污信息
    /* eslint-disable @typescript-eslint/ban-ts-ignore */
    //@ts-ignore
    this.$bus.on('sendHeavilyDtaa', (res: any) => {
      this.printFactoryList = res[0].printFactoryList
      this.garageList = res[0].garageList
      this.restaurantList = res[0].restaurantList
      this.gasList = res[0].gasList
    })
    this.toggleTimer = setInterval(() => {
      this.mapMarkerWater = []
      this.mapMarkerAir = []
      this.heavyPollutionEnterpriseList = []
      this.companyList = []
      this.printFactoryList = []
      this.garageList = []
      this.gasList = []
      this.restaurantList = []
      this.taskMarkerList = []
      this.currentIndex < this.BtnGroup.length - 1 ? this.currentIndex++ : (this.currentIndex = 0)
    }, 10 * 1000)
    // this.connect();
    // this.loadHtml = setInterval(() => {
    //   location.reload();
    // }, 15 * 1000 * 60);
  }

  private loadHtml: any = null
  beforeDestroy() {
    clearInterval(this.toggleTimer)
    clearInterval(this.timeTimer)
    clearInterval(this.mapMoveTimer)
    clearInterval(this.loadHtml)
    // @ts-ignore
    this.$bus.off('toggleMode')
    // @ts-ignore
    this.$bus.off('sendMapMarker')
    // @ts-ignore
    this.$bus.off('sendWaterMapMarker')
    // @ts-ignore
    this.$bus.off('sendPollutingMapMarker')
    // @ts-ignore
    this.$bus.off('sendCompanyList')
    // @ts-ignore
    this.$bus.off('sendTaskMarkerList')
    // @ts-ignore
    this.$bus.off('sendCarList')
    // @ts-ignore
    this.$bus.off('sendHeavilyDtaa')
    ;(window as any).AMap = null
  }
  generateRandomNumber(max: number): number {
    return Number((Math.random() * max).toFixed(2))
  }
  private activeTimer: any = null
  private companyList: any[] = []
  private printFactoryList: any = []
  private garageList: any = []
  private gasList: any = []
  private restaurantList: any = []
  private changeCurrentIndex(index: number): void {
    this.mapMarkerWater = []
    this.mapMarkerAir = []
    this.heavyPollutionEnterpriseList = []
    this.companyList = []
    this.printFactoryList = []
    this.garageList = []
    this.gasList = []
    this.restaurantList = []
    this.taskMarkerList = []
    clearInterval(this.toggleTimer)
    clearInterval(this.mapMoveTimer)
    clearTimeout(this.activeTimer)
    this.currentIndex = index
    this.activeTimer = setTimeout(() => {
      this.toggleTimer = setInterval(() => {
        // this.mapMarkerWater = [];
        // this.mapMarkerAir = [];
        // this.heavyPollutionEnterpriseList = [];
        // this.companyList = [];
        this.currentIndex < this.BtnGroup.length - 1 ? this.currentIndex++ : (this.currentIndex = 0)
      }, 10 * 1000)
    }, 20 * 1000)
  }
  private routeDistrbute(to: any): void {
    /* eslint-disable @typescript-eslint/ban-ts-ignore */
    //@ts-ignore
    this.$bus.emit('showHeaderTime', '')
    localStorage.setItem('currentRoute', JSON.stringify(to))
    if (to.value == '/emergencyCommand') {
      window.location.reload()
      return
    } else {
      this.$router.push(to.value)
    }
  }
  private getTime(): void {
    const date = new Date()
    const year = date.getFullYear()
    let month: any = date.getMonth() + 1
    let day: any = date.getDate()
    let hour: any = date.getHours()
    let minute: any = date.getMinutes()
    let second: any = date.getSeconds()
    if (month < 10) {
      month = '0' + month
    }
    if (day < 10) {
      day = '0' + day
    }
    if (hour < 10) {
      hour = '0' + hour
    }
    if (minute < 10) {
      minute = '0' + minute
    }
    if (second < 10) {
      second = '0' + second
    }
    /* eslint-disable @typescript-eslint/ban-ts-ignore */
    //@ts-ignore
    return {
      year: year.toString(),
      month: month.toString(),
      day: day.toString(),
      hour: hour.toString(),
      minute: minute.toString(),
      second: second.toString(),
    }
  }
  private airAlarmCount: any = ''
  private waterAlarmCount: any = ''
  private getAlarmCount() {
    countThisAirDay().then((res: any) => {
      if (String(res.data.data).length === 1) {
        res.data.data = '00' + String(res.data.data)
      } else if (String(res.data.data).length === 2) {
        res.data.data = '0' + String(res.data.data)
      } else if (String(res.data.data).length === 3) {
        res.data.data = String(res.data.data)
      }
      this.airAlarmCount = res.data.data
    })
    countThisWaterDay().then((res: any) => {
      if (String(res.data.data).length === 1) {
        res.data.data = '00' + String(res.data.data)
      } else if (String(res.data.data).length === 2) {
        res.data.data = '0' + String(res.data.data)
      } else if (String(res.data.data).length === 3) {
        res.data.data = String(res.data.data)
      }
      this.waterAlarmCount = res.data.data
    })
  }
}
</script>
