import request from "@/utils/request";
import { AxiosPromise } from "axios";

/**
 * @method functionName
 * @param {type} data 说明
 * @description 获取重污企业站点列表
 */
export function getHeavyCorruptSite(params:any): AxiosPromise<any> {
  return request({
    url: `/water/sewage/enterprise/stationStatusStatistics`,
    method: "get",
    params
  });
}
/**
 * @method functionName
 * @param {type} data 说明
 * @description 获取重污企业站点列表
 */
export function getHeavyCorruptMapList(params:any): AxiosPromise<any> {
  return request({
    url: `/water/sewage/enterprise/list`,
    method: "get",
    params
  });
}
/**
 * @method functionName
 * @param {type} data 说明
 * @description 获取重污企业站点右侧小时数据
 */
export function getHeavyCorruptHour(params:any): AxiosPromise<any> {
  return request({
    url: `/water/heavily-polluting-water/hour/bigData/ListRecord`,
    method: "get",
    params
  });
}
/**
 * @method functionName
 * @param {type} data 说明
 * @description 获取重污企业站点右侧天数据
 */
export function getHeavyCorruptDay(params:any): AxiosPromise<any> {
  return request({
    url: `/water/heavily-polluting-water/day/bigData/ListRecord`,
    method: "get",
    params
  });
}
/**
 * @method functionName
 * @param {type} data 说明
 * @description 获取重污企业站点告警类型统计
 */
export function getHeavyCorruptPie(params:any): AxiosPromise<any> {
  return request({
    url: `/water/heavily-polluting-water/hour/bigData/alarmItemCount`,
    method: "get",
    params
  });
}
/**
 * @method functionName
 * @param {type} data 说明
 * @description 获取餐饮油烟排名
 */
export function getCaterRank(params:any): AxiosPromise<any> {
  return request({
    url: `/water/restaurant/record/ranking`,
    method: "get",
    params
  });
}