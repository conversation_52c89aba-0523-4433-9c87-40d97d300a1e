import { CityBuildingLayer } from '@antv/l7'

import geo from '@/data/mapRangeData.json'

/**
 * 初始化建筑
 * @param scene  场景对象
 */
export function initBuilding(scene) {
  /* geo.features.forEach((e) => {
        e.properties.floor = Math.ceil(Math.random() * 6)
      }) */

  // 添加城市灯光图层
  const layer = new CityBuildingLayer({
    zIndex: 0,
  })
  layer
    .source(geo)
    .size('floor', [10, 50])
    .color('rgba(242,246,250,0.5)')
    .animate({
      enable: true,
    })
    .style({
      opacity: 1.0,
      baseColor: '#13161d',
      windowColor: '#0e0220',
      brightColor: '#08faee',
    })
  scene.addLayer(layer)
}
