<style lang="less" scoped>
.empty {
  width: 4.5rem;
  height: 2.2rem;
  font-size: 0.18rem;
  display: flex;
  justify-content: center;
  align-items: center;
}
.common-main {
  .fold-module {
    position: absolute;
    top: 0.05rem;
    right: 0.35rem;
    transition: 1.5s;
    z-index: 99;
  }
  overflow: hidden;
  width: 100%;
  padding: 0;
  display: flex;
  justify-content: flex-end;
  .left-part,
  .right-part {
    transition: transform 1s ease-out;
  }
  .left-part {
    position: absolute;
    height: calc(1080px - 0.94rem);
    /*width: 4.7rem;*/
    /*top: 0.94rem;*/
    // position: relative;
    // background-image: linear-gradient(
    //   to left,
    //   rgba(0, 0, 0, 0.5),
    //   rgba(0, 15, 79, 0.5)
    // );
    background-image: url(../../assets/water-bg-mb.png) !important;
    background-size: 100% 100% !important;
    background-repeat: no-repeat;
    margin: 0;
  }
  .content-width {
    width: 4.5rem;
  }
  .video-div {
    height: 2rem;
    margin-top: 0.1rem;
    width: 4.5rem;
    display: flex;
    justify-content: space-between;
    .video-player {
      // width: 50%;
      height: calc(100% - 0.45rem) !important;
      .video-js.vjs-fluid {
        height: 100% !important;
      }
    }
    .person-infor {
      width: 48%;
      display: inline-block;
      vertical-align: top;
      // margin-left: 0.5rem;
      .person-infor-line {
        // margin-bottom: 0.2rem;
        &:nth-last-child(1) {
          margin-bottom: 0;
          position: relative;
          img {
            height: 1.05rem;
            width: 100%;
          }
          .person-infor-line-card {
            position: absolute;
            width: 0.3rem;
            height: 1.05rem;
            background: rgba(25, 84, 167, 1);
            text-align: center;
            top: 0;
            left: 0;
            display: flex;
            align-items: center;
          }
        }
        font-size: 0.16rem;
        > span:nth-child(1) {
          font-family: Source Han Sans CN;
          font-weight: 400;
          color: #00fcf9;
          font-size: 0.16rem;
        }
      }
      .person-infor-name {
        background: #3496ea;
        padding: 0.04rem 0.1rem;
      }
    }
  }
  .title-warp {
    display: flex;
    justify-content: space-between;
  }
  .right-part {
    .header-title,
    .disc-content {
      display: flex;
      justify-content: space-around;
      align-items: center;
      > div {
        flex: 1;
        text-align: center;
      }
    }
    .disc-content {
      height: 0.5rem;
      > div:nth-child(1) {
        text-align: center;
        display: flex;
        justify-content: center;
        > span {
          display: block;
          width: 0.3rem;
          height: 0.2rem;
          border-radius: 0.06rem;
          background: #0084ff;
        }
      }
    }
    .odd {
      background: rgba(15, 36, 94, 1);
    }
    .even {
      background: rgba(4, 20, 51, 1);
    }
    .person-pic {
      display: inline-block;
      font-family: Source Han Sans CN;
      font-weight: 500;
      color: rgba(255, 255, 255, 1);
      > img {
        display: block;
        width: 1.2rem;
        height: 1.5rem;
      }
      > div {
        width: 100%;
        height: 0.25rem;
        background-color: #3a528a;
        text-align: center;
        line-height: 0.25rem;
      }
    }
    .person-infor {
      display: inline-block;
      vertical-align: top;
      margin-left: 0.5rem;
      .person-infor-line {
        margin-bottom: 0.2rem;
        &:nth-last-child(1) {
          margin-bottom: 0;
        }
        > span:nth-child(1) {
          font-family: Source Han Sans CN;
          font-weight: 400;
          color: #00fcf9;
          font-size: 0.12rem;
        }
      }
      .person-infor-name {
        background: #3496ea;
        padding: 0.04rem 0.1rem;
      }
    }
    .station-addr {
      display: flex;
      align-items: center;
      margin-bottom: 0.05rem;
      padding-left: 0.05rem;
      font-family: Source Han Sans CN;
      font-weight: 400;
      > img {
        width: 0.15rem;
        height: 0.15rem;
        margin-right: 0.05rem;
      }
    }
    .common-title .sub-title {
      margin: 0 0 0.05rem;
    }
  }
  .left-side-three {
    pointer-events: auto;
    position: absolute;
    z-index: 2;
    width: 2rem;
    height: 1.24rem;
    bottom: 0.86rem;
    left: 0.84rem;
    box-sizing: border-box;
    padding: 0.35rem;
    background-image: url('../../assets/heavily/<EMAIL>');
    background-size: 100% 100%;
    .list {
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: space-between;
      &:last-child {
        margin-top: 0.24rem;
      }
      .circle {
        width: 0.2rem;
        height: 0.2rem;
        display: inline-flex;
        border: 1px solid #97b6e4;
        justify-content: center;
        border-radius: 50%;
        position: relative;
        align-items: center;
        .active {
          width: 0.1rem;
          height: 0.1rem;
          display: inline-block;
          background: #2afff8;
          border-radius: 50%;
        }
      }
    }
  }
  .right-select-type {
    pointer-events: auto;
    position: absolute;
    z-index: 2;
    bottom: 1rem;
    right: 5rem;
    cursor: pointer;
    transition: 1.5s;
    .list {
      display: flex;
      align-items: center;
      margin-bottom: 0.2rem;
      div:nth-of-type(1) {
        margin: 0 0.1rem;
      }
    }
  }
}
.air-video {
  width: 3.67rem;
  height: 2.64rem;
  position: absolute;
  bottom: 0.45rem;
  left: 0.5rem;
  transform: translate3d(0px, 0px, 0px);
  background-image: url('../../assets/<EMAIL>');
  background-size: 100% 100%;
  transition: 1.5s;
  padding: 0.2rem;
  box-sizing: border-box;
  .video-title {
    display: flex;
    align-items: center;
    position: relative;
    img {
      width: 0.17rem;
      margin-left: 0.18rem;
      height: 0.21rem;
    }
    span {
      font-size: 0.18rem;
      font-family: PingFang SC;
      font-weight: bold;
      color: #dbf5ff;
      margin-left: 0.16rem;
    }
    .close {
      position: absolute;
      right: 0;
      width: inherit;
      height: inherit;
      cursor: pointer;
    }
  }
  .air-video-area {
    width: 100%;
    height: 1.86rem;
    margin-top: 0.15rem;
  }
}
.water-table {
  width: 3.82rem;
  position: absolute;
  top: 1.2rem;
  left: 0.5rem;
  transform: translate3d(0px, 0px, 0px);
  transition: 1.5s;
  .water-table-thead {
    background: rgba(23, 66, 190, 1);
    .water-table-th {
      font-size: 0.12rem;
      > div {
        border: 1px solid rgba(11, 141, 211, 1);
      }
    }
  }
  .water-table-tbody {
    .water-table-tr {
      font-size: 0.14rem;
      > div {
        border: 1px solid rgba(11, 141, 211, 1);
      }
      > div:nth-child(odd) {
        background: rgba(15, 36, 94, 1);
      }
      > div:nth-child(even) {
        background: rgba(3, 20, 59, 1);
      }
    }
  }
  .water-table-bottom {
    line-height: 0.35rem;
    height: 0.35rem;
    text-align: center;
    border: 1px solid rgba(11, 141, 211, 1);
    border-top-style: none;
    cursor: pointer;
  }

  .water-table-th,
  .water-table-tr {
    display: flex;
    height: 0.35rem;
    > div {
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      width: 20%;
    }
  }
}
.center-search {
  position: absolute;
  top: 1.2rem;
  right: 50%;
  pointer-events: auto;
  display: flex;
  align-items: center;
  width: 7.5rem;
  height: 0.6rem;
  margin-right: -3.5rem;
  > :nth-of-type(1) {
    display: flex;
    align-items: center;
    width: 6.5rem;
    height: 0.6rem;
    background: rgba(5, 7, 95, 0.7);
    border: 1px solid #034fa8;
    input {
      width: 100%;
      margin-left: 0.25rem;
      // margin-right: 0.25rem;
      background: transparent;
      border: none;
      outline: none;
      font-size: 0.22rem;
    }
    input::-webkit-input-placeholder {
      color: #ffffff;
    }
    input::-moz-placeholder {
      /* Mozilla Firefox 19+ */
      color: #ffffff;
    }
    input:-moz-placeholder {
      /* Mozilla Firefox 4 to 18 */
      color: #ffffff;
    }
    input:-ms-input-placeholder {
      /* Internet Explorer 10-11 */
      color: #ffffff;
    }
  }
  > :nth-of-type(2) {
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(5, 7, 95, 0.7);
    border: 1px solid #034fa8;
    height: 0.6rem;
    width: 1rem;
    img {
      cursor: pointer;
    }
  }
}
.center-search {
  .fetch-input {
    height: 300px;
    overflow-y: auto;
    position: absolute;
    width: 6.5rem;
    cursor: pointer;
    top: 0.6rem;
    font-size: 0.22rem;
    > div {
      color: #8eb3f6;
      background: #012474;
      padding: 0.1rem 0.25rem;
      &:hover {
        color: #ffffff;
        background-color: #0061c6;
      }
    }
  }
}
.center-map {
  position: absolute;
  height: calc(1080px - 0.94rem);
  width: 100%;
  // margin-top: 0.15rem;
  > div {
    width: 100%;
    height: 100%;
  }
}
.video {
  position: relative;
  .video-img {
    width: 100%;
    height: calc(100% - 0.42rem);
  }
  .video-btn {
    position: absolute;
    left: 50%;
    top: 50%;
    margin-left: -0.35rem;
    margin-top: -0.35rem;
    width: 0.7rem;
    height: 0.7rem;
  }
  .video-text {
    position: absolute;
    width: 100%;
    bottom: 0;
    text-align: center;
    background: rgba(22, 52, 73, 1);
    height: 0.42rem;
    line-height: 0.42rem;
  }
}
.heavy-pollution-table {
  width: 100%;
  border: 1px solid rgba(54, 218, 234, 0.42);
  .heavy-pollution-name {
    font-size: 0.12rem;
    width: 0.76rem;
    background: rgba(15, 36, 94, 1);
    text-align: center;
  }
  .heavy-pollution-text {
    padding-left: 0.06rem;
  }
  td {
    font-size: 0.12rem;
    height: 0.34rem;
    color: #ffffff;
    font-weight: 500;
  }
}
.monitor {
  display: flex;
  color: #ffffff;
  padding: 0.12rem;
  height: 0.6rem;
  align-items: center;
  .spot {
    width: 0.08rem;
    height: 0.08rem;
    background: rgba(242, 83, 22, 1);
    border-radius: 50%;
    margin-right: 0.12rem;
  }
  .content {
    flex: 1;
    display: flex;
  }
  .line {
    width: 1px;
    height: 0.48rem;
    background: rgba(0, 234, 255, 1);
    margin-right: 0.12rem;
    margin-left: 0.12rem;
  }
  .keyword {
    color: #f25316;
    margin-left: 0.05rem;
    margin-right: 0.05rem;
  }
}
.monitor:nth-child(odd) {
  background: rgba(15, 36, 94, 1);
}
.monitor:nth-child(even) {
  background: rgba(4, 20, 51, 1);
}
.type-radio {
  .water-monitor-tabs {
    display: flex;
    align-items: center;
    overflow-x: auto;
    scrollbar-width: thin;
    scrollbar-color: rgba(14, 139, 255, 0.5) rgba(12, 39, 92, 0.3);
  }
  .water-monitor-tab {
    height: 0.24rem;
    font-size: 0.12rem;
    color: rgba(255, 255, 255, 1);
    background: rgba(12, 39, 92, 1);
    border-radius: 0;
    padding: 0 0.1rem;
    border: none;
    flex-shrink: 0;
    min-width: max-content;
  }
  .ant-radio-group-solid .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
    background: #0084ff;
    border: none;
  }
  .ant-radio-button-wrapper:not(:first-child)::before {
    background: transparent;
  }
  .ant-radio-button-wrapper-checked::before {
    background: transparent !important;
  }
  .ant-radio-button-wrapper-checked {
    z-index: 1;
    border-color: #0084ff !important;
    -webkit-box-shadow: -1px 0 0 0 #0084ff;
    box-shadow: -1px 0 0 0 #0084ff;
  }
  .ant-radio-group-small .ant-radio-button-wrapper {
    flex: 1;
    text-align: center;
    padding: 0 10px !important;
  }
  .ant-radio-group {
    display: flex;
  }
}
.margin-r15 {
  margin-right: 0.15rem;
}
.marginBot10 {
  margin-bottom: 0.1rem;
}
.common-frame {
  position: relative;
  // background: rgba(11, 21, 44, 0.6);
  // border: 1px solid rgba(114, 139, 202, 1);
  // box-shadow: 0px 0px 10px 0px rgba(14, 252, 255, 0.77);
  padding: 0.1rem;
  // margin-bottom: 0.1rem;
  &:nth-last-child(1) {
    margin-bottom: 0;
  }
  .conner-left-top {
    position: absolute;
    top: 0;
    left: 0;
    width: 0.1rem;
    height: 0.1rem;
    border-top: 1px solid #00eaff;
    border-left: 1px solid #00eaff;
  }
  .conner-right-top {
    position: absolute;
    top: 0;
    right: 0;
    width: 0.1rem;
    height: 0.1rem;
    border-top: 1px solid #00eaff;
    border-right: 1px solid #00eaff;
  }
  .conner-left-bot {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 0.1rem;
    height: 0.1rem;
    border-bottom: 1px solid #00eaff;
    border-left: 1px solid #00eaff;
  }
  .conner-right-bot {
    position: absolute;
    right: 0;
    bottom: 0;
    width: 0.1rem;
    height: 0.1rem;
    border-right: 1px solid #00eaff;
    border-bottom: 1px solid #00eaff;
  }
}
.prefix {
  text-indent: 0.2rem;
  position: relative;
  &::before {
    position: absolute;
    display: inline-block;
    top: 0.05rem;
    left: 0.02rem;
    content: '';
    width: 0.04rem;
    height: 0.2rem;
    background-color: #fff;
  }
}
.paddingTop {
  padding: 0.2rem 0;
}
.title-flex {
  text-indent: 0 !important;
  display: flex;
  align-items: center;
  justify-content: space-between;
  > div {
    display: flex;
    font-size: 0.14rem;
    font-weight: 400;

    > div {
      width: 0.8rem;
      height: 0.28rem;
      line-height: 0.3rem;
      color: #0e8bff;
      // background: rgba(14, 139, 255, 0.32);
      // border: 1px solid rgba(14, 139, 255, 1);
      text-align: center;
      // clip-path: polygon(100% 0, 100% 100%, 12% 100%, 0% 70%, 0 0);
      background: url(../../assets/<EMAIL>);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      cursor: pointer;
    }
    div.type-active {
      background: url(../../assets/<EMAIL>);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      color: rgba(0, 234, 255, 1);
    }
    :nth-of-type(2) {
      margin-left: 0.1rem;
    }
  }
}
.video-top {
  background: url(../../assets/card-top.png);
  background-size: 100% 100%;
  height: 0.36rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  padding: 0 0.2rem;
  font-size: 0.16rem;
  cursor: pointer;
}
.no-video {
  margin-top: 0.03rem;
  margin-left: 0.05rem;
  width: calc(100% - 0.1rem);
  height: calc(100% - 0.4rem);
  background-image: url('../../assets/heavily/<EMAIL>');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  display: flex;
  position: relative;
  align-items: center;
  .no-text {
    font-size: 14px;
    font-weight: 400;
    color: #c8dcee;
    line-height: 22px;
    position: absolute;
    text-align: center;
    display: inline-block;
    width: 100%;
    bottom: 20%;
  }
}
.card-top {
  font-size: 0.16rem;
  box-sizing: border-box;
  padding: 0 0.2rem;
  background: url(../../assets/card-top.png);
  background-size: 100% 100%;
  height: 0.36rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.card-bottom {
  background: url(../../assets/card-bottom.png);
  background-size: 100% 100%;
  height: calc(100% - 0.35rem) !important;
  > div {
    box-sizing: border-box;
    padding: 0 0.1rem;
    padding-bottom: 0.1rem;
    div {
      font-size: 0.14rem !important;
    }
  }
  .no-image {
    height: 100%;
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    img {
      margin-bottom: 0.2rem;
    }
  }
}
.water-types {
  display: flex;
  align-items: center;
  .water-types-top {
    display: flex;
    align-items: center;
    font-size: 0.2rem;
    text-shadow:
      0 0 0.1rem #2e73d6,
      0 0 0.1rem #2e73d6;
    margin-right: 0.15rem;
    img {
      margin-right: 0.07rem;
    }
  }
}
.water-right-div {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: calc(100% - 0.3rem);
  margin-top: 0.15rem;
  margin-right: 0.2rem;
}
.water-right-divs {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: calc(100% - 0.3rem);
  margin-top: 0.15rem;
  margin-right: 0.46rem;
  .title-img {
    width: 4.2rem;
  }
  .title-warp {
    padding-right: 0.2rem;
  }
  .region {
    padding-right: 0.2rem;
    width: 4.2rem;
    .btn {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      span {
        display: inline-block;
        padding: 0.05rem 0.22rem;
        background: #0c275c;
        font-size: 0.12rem;
        font-family: PingFang SC;
        font-weight: 500;
        cursor: pointer;
        color: #ffffff;
        &:first-child {
          border-radius: 0.04rem 0 0 0.04rem;
        }
        &:last-child {
          border-radius: 0 0.04rem 0.04rem 0;
        }
      }
      .btn-choose {
        background: #0084ff;
      }
    }
    .chart-area {
      height: calc(2.2rem - (10.8rem - 1080px) / 3);
      margin-top: 0.1rem;
      .no-data {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.2rem;
        height: 100%;
      }
    }
    .no-datas {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 0.2rem;
      height: calc(2.2rem - (10.8rem - 1080px) / 3);
    }
    .drain-alarm {
      min-height: calc(2.15rem - calc(10.8rem - 1080px) / 2);
      background-image: url('../../assets/gjbg.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      margin-top: 0.1rem;
      box-sizing: border-box;
      padding: 0.15rem 0.25rem 0.1rem;
      .drain-time {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .drain-list {
          display: flex;
          align-items: center;
          span {
            font-size: 0.14rem;
            font-weight: 500;
            &:nth-child(1) {
              color: #5abefe;
            }
            &:nth-child(2) {
              color: #dcf0ff;
            }
          }
        }
      }
      .drain-title {
        font-size: 0.14rem;
        font-weight: 500;
        color: #5abefe;
      }
      .drain-device {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .device-list {
          display: flex;
          align-items: center;
          span {
            font-size: 0.14rem;
            font-weight: 400;
            color: #99cbeb;
          }
          img {
            margin-left: 0.1rem;
            width: 0.18rem;
            height: 0.18rem;
          }
        }
      }
      .drain-title {
        span {
          font-size: 0.14rem;
          font-weight: 400;
          color: #99cbeb;
          &:nth-child(1) {
            display: inline-block;
            width: 0.7rem;
            text-align-last: justify;
          }
        }
      }
      .monitor-table {
        /*border-top: 1px solid rgba(21,51,116, .6);*/
        /*border-bottom: 1px solid rgba(21,51,116, .6);*/
        background-image: url('../../assets/outlet/<EMAIL>');
        background-size: 100% 100%;
        min-height: calc(0.54rem - calc(10.8rem - 1080px) / 10);
        padding: calc(0.07rem - calc(10.8rem - 1080px) / 20) calc(0.05rem - calc(10.8rem - 1080px) / 20);
        position: relative;
        .monitor-list {
          display: flex;
          align-items: center;
          &:nth-child(1) {
            span {
              color: #5abefe !important;
              font-size: 0.14rem;
            }
          }
          &:not(first-child) {
            margin-top: 0.04rem;
            span {
              &:nth-child(2) {
                color: #ff4343;
                span {
                  color: #ff4343;
                }
              }
            }
          }
          span {
            color: #ffffff;
            font-size: 0.14rem;
            line-height: 0.14rem;
            flex: 1;
            text-align: center;
            position: relative;
            &:nth-child(2),
            &:nth-child(3) {
              flex: 1.2;
            }
          }
          .line {
            width: 1px;
            height: 0.81rem;
            position: absolute;
            right: 0;
          }
        }
        .lines {
          position: absolute;
          top: 0;
          display: flex;
          align-items: center;
          width: 100%;
          height: 100%;
          div {
            width: 18.5%;
            height: 100%;
            display: flex;
            padding: 0.1rem 0;
            justify-content: flex-end;
            &:nth-child(2),
            &:nth-child(3) {
              width: 22%;
            }
          }
        }
      }
      .relation {
        margin-top: 0.1rem;
        .relation-title {
          font-size: 0.14rem;
          font-weight: 500;
          color: #5abefe;
          line-height: 0.14rem;
        }
        .realtion-area {
          display: flex;
          align-items: center;
          margin-top: 0.05rem;
          width: 100%;
          justify-content: space-between;
          .relation-list {
            display: flex;
            align-items: center;
            span {
              font-size: 0.14rem;
              font-weight: 400;
              color: #99cbeb;
              display: inline-block;
              line-height: 0.14rem;
            }
            img {
              width: 0.18rem;
              height: 0.18rem;
              margin-left: 0.16rem;
            }
          }
        }
      }
    }
    .drain-alarms {
      height: 2.15rem;
      background-image: url('../../assets/gjbg.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      margin-top: 0.1rem;
      box-sizing: border-box;
      padding: 0.15rem 0.22rem;
      .drain-top {
        display: flex;
        align-items: center;
        .right {
          width: 1.22rem;
          height: 0.68rem;
          margin-left: 0.08rem;
          background-size: 100% 100%;
        }
        .left {
          width: calc(100% - 1.3rem);
          .drain-list {
            display: flex;
            align-items: center;
            span {
              font-size: 0.14rem;
              font-weight: 500;
              &:nth-child(1) {
                color: #5abefe;
              }
              &:nth-child(2) {
                color: #dcf0ff;
              }
            }
          }
        }
      }
      .drain-bottom {
        margin-top: 0.1rem;
        min-height: 1.12rem;
        padding: 0.09rem 0.12rem;
        background-image: url('../../assets/outlet/<EMAIL>');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        box-sizing: border-box;
        .drain-anysis {
          display: flex;
          align-items: center;
          .ansyis-list {
            display: flex;
            align-items: center;
            flex: 1;
            span {
              font-size: 0.14rem;
              font-weight: 400;
              &:nth-child(1) {
                color: #99cbeb;
              }
              &:nth-child(2) {
                color: #d3e6f5;
                margin-left: 0.2rem;
              }
            }
            .ansyis-area {
              color: #ff4343 !important;
            }
            img {
              width: 0.18rem;
              margin-left: 0.2rem;
              height: 0.18rem;
            }
          }
        }
        .color-area {
          margin: 0.05rem 0;
          display: flex;
          .color-title {
            font-size: 0.14rem;
            font-weight: 400;
            color: #99cbeb;
          }
          .color-left {
            width: 0.88rem;
            height: 0.44rem;
            background-image: url('../../assets/outlet/<EMAIL>');
            background-size: 100% 100%;
            margin: 0 0.08rem;
            padding: 0.08rem 0.05rem;
            .color-params {
              font-size: 0.12rem;
              line-height: 0.14rem;
              font-weight: 400;
              color: #d3e6f5;
              text-align: center;
            }
          }
          .color-right {
            width: calc(100% - 1.6rem);
            height: 100%;
          }
          .color-list-area {
            width: 100%;
            display: flex;
            flex-direction: column;
            .color-list {
              display: flex;
              align-items: center;
              font-size: 0.12rem;
              font-weight: 400;
              color: #d3e6f5;
              span {
                flex: 1;
                text-align: center;
                &:nth-child(1) {
                  flex: 1.5;
                }
                &:nth-child(4),
                &:nth-child(5) {
                  flex: 0.8;
                }
              }
            }
          }
        }
      }
    }
    .drain-video {
      height: calc(2.2rem - (10.8rem - 1080px) / 3);
      background-image: url('../../assets/heavily/<EMAIL>');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      padding: 0.16rem 0.2rem;
      box-sizing: border-box;
      .not-online {
        width: 100%;
        height: 100%;
        background-size: 100% 100%;
        text-align: center;
        line-height: calc(1.88rem - (10.8rem - 1080px) / 3);
        font-size: 0.16rem;
        color: #000000;
      }
    }
    .table-site {
      width: 100%;
      margin-top: 0.1rem;
      border: 1px solid rgba(54, 218, 234, 0.42);
      .table-name {
        font-size: 0.12rem;
        width: 0.96rem;
        background: rgba(15, 36, 94, 1);
        text-align: center;
      }
      .table-text {
        text-align: center;
        .state {
          width: 0.44rem;
          height: 0.18rem;
          background: #071123;
          border: 0px solid #11f7da;
          box-shadow: 0px 0px 0.1rem 0px #0bc5ae inset;
          text-align: center;
          display: inline-block;
          line-height: 0.18rem;
          border-radius: 0.02rem;
          color: #11f7da;
          font-size: 0.12rem;
        }
        .state-err {
          width: 0.44rem;
          height: 0.18rem;
          background: #071123;
          border: 0px solid #ff4343;
          box-shadow: 0px 0px 10px 0px #b61111 inset;
          text-align: center;
          display: inline-block;
          line-height: 0.18rem;
          border-radius: 0.02rem;
          color: #ff4343;
          font-size: 0.12rem;
        }
      }
      td {
        font-size: 0.12rem;
        height: calc(0.34rem - calc(10.8rem - 1080px) / 20);
        color: #ffffff;
        font-weight: 500;
        text-align: center;
      }
    }
  }
  .regions {
    .swiper-area {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 4rem;
      height: 0.3rem;
      .left-arrow {
        cursor: pointer;
        width: 0.4rem;
        height: 0.3rem;
        display: flex;
        align-items: center;
        justify-content: center;
        border: none !important;
        .svg-arrow {
          width: 0.2rem !important;
          height: 0.2rem !important;
        }
      }
      .middle {
        width: 3.2rem;
        .btn {
          width: 100%;
          .single-slide {
            width: 0.8rem !important;
            text-align: center;
            padding: 0.05rem 0;
            background: #0c275c;
            font-size: 0.12rem;
            font-weight: 500;
            margin: 0 !important;
            cursor: pointer;
            color: #ffffff;
            &:first-child {
              border-radius: 0.04rem 0 0 0.04rem;
            }
            &:last-child {
              border-radius: 0 0.04rem 0.04rem 0;
            }
          }
          .btn-choose {
            background: #0084ff;
          }
        }
      }
      .right-arrow {
        cursor: pointer;
        width: 0.4rem;
        height: 0.3rem;
        display: flex;
        border: none !important;
        align-items: center;
        justify-content: center;
        .svg-arrow {
          width: 0.2rem !important;
          height: 0.2rem !important;
        }
      }
    }
  }
  .fingerprint {
    display: flex;
    padding: 0 0.14rem;
    align-items: center;
    justify-content: space-between;
    .left {
      display: flex;
      align-items: center;
      img {
        height: 0.42rem;
        width: 0.37rem;
      }
      span {
        margin-left: 0.15rem;
        font-size: 0.2rem;
        line-height: 0.2rem;
        font-weight: 400;
        color: #ffffff;
      }
    }
    .right {
      span {
        font-size: 0.14rem;
        font-weight: 400;
        color: #dcf0ff;
      }
    }
  }
  .fingerprint-area {
    padding: 0.15rem 0.14rem 0;
  }
  .fingerprint-bg {
    height: 1rem;
    width: 100%;
    background-image: url('../../assets/outlet/<EMAIL>');
    background-size: 100% 100%;
    display: flex;
    align-items: center;
    div {
      display: flex;
      flex-direction: column;
      justify-content: center;
      flex: 1;
      span {
        text-align: center;
        &:nth-child(1) {
          font-size: 0.14rem;
          font-weight: 400;
          color: #56b6f5;
        }
        &:nth-child(2) {
          margin-top: 0.15rem;
          font-size: 0.22rem;
          font-weight: 400;
          color: #ffffff;
        }
      }
    }
  }
  .fingerprint-swiper {
    height: 1.6rem;
  }
  .fingerprint-swiper-area {
    height: 4.8rem;
  }
}
.water-right-div-area {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}
.law-vehicle {
  width: 0.9rem;
  height: 0.91rem;
  position: absolute;
  pointer-events: auto;
  z-index: 99;
  top: 0.1rem;
  right: 5rem;
  cursor: pointer;
  background-image: url('../../assets/department/<EMAIL>');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  .vehicle-text {
    font-size: 0.14rem;
    font-weight: bold;
    color: #ffffff;
    top: 0;
  }
  .within-vehicle {
    position: absolute;
    width: 100%;
    height: 100%;
    background-image: url('../../assets/department/<EMAIL>');
  }
  @-webkit-keyframes rotation {
    from {
      -webkit-transform: rotate(0deg);
    }
    to {
      -webkit-transform: rotate(360deg);
    }
  }
  .Rotation {
    -webkit-transform: rotate(360deg);
    animation: rotation 2s linear infinite;
    -moz-animation: rotation 2s linear infinite;
    -webkit-animation: rotation 2s linear infinite;
    -o-animation: rotation 2s linear infinite;
  }
}
.law-vehicles {
  background-image: url('../../assets/<EMAIL>');
  .within-vehicles {
    background-image: url('../../assets/<EMAIL>');
  }
}
</style>

<style lang="less">
.ant-modal-water {
  top: 1.85rem !important;
  right: calc(50% - 600px) !important;
  .ant-modal-content {
    width: 12rem !important;
    height: 6.9rem !important;
    padding: 0.7rem 0.95rem 0.84rem;
    box-sizing: border-box;
    background-color: transparent !important;
    background-image: url('../../assets/<EMAIL>') !important;
    background-size: 100% 100% !important;
  }
  .ant-modal-footer {
    border: none !important;
  }
  .ant-modal-close {
    top: 0.49rem !important;
    right: 0.86rem !important;
    svg {
      font-size: 0.2rem !important;
      color: #42adfb;
    }
  }
  .ant-modal-close-x {
    background: RGBA(12, 39, 94, 1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    width: 0.29rem;
    height: 0.29rem;
    justify-content: center;
    border: 0.01rem solid #358fd3;
  }
  .ant-modal-body {
    padding: 0;
    .title {
      // display: flex;
      // justify-content: center;
      text-align: center;
      font-size: 0.25rem;
      font-family: YouSheBiaoTiHei;
      font-weight: 400;
      color: #dcf0ff;
      text-shadow:
        0 0 0.03rem rgba(220, 240, 255, 0.3),
        0 0 0.03rem rgb(220, 240, 255, 0.3);
      // .bg {
      //   width: 5.91rem;
      //   height: 0.39rem;
      //   background-image: url('../../assets/<EMAIL>');
      //   background-size: 100% 100%;
      // }
    }
    .table-container {
      margin-top: 0.2rem;
      &:last-child {
        margin-top: 0.3rem;
      }
      color: #c4dbfb;
      font-size: 0.16rem;
      width: 100%;
      text-align: center;
      border-color: rgba(11, 156, 229, 0.2);
      tr {
        th {
          background: rgba(14, 45, 126, 0.7);
        }
      }
      tr:nth-child(2n) {
        td {
          background-color: rgba(14, 45, 126, 0.2);
        }
      }
    }
  }
}
.air-video-area {
  .player-wrapper {
    width: 100% !important;
    height: 100% !important;
    .video-wrapper {
      width: 100% !important;
      height: 100% !important;
    }
  }
}
.ant-modal-alarms {
  top: 2.6rem !important;
  width: 7.28rem !important;
  .ant-modal-content {
    width: 7.28rem;
    min-height: 5.7rem;
    background: url('../../assets/outlet/<EMAIL>');
    background-size: 100% 100%;
    .close {
      position: absolute;
      right: 0.5rem;
      cursor: pointer;
      top: 0.45rem;
    }
    .content {
      margin-top: 0.8rem;
      padding: 0 0.34rem;
      .alarm-content {
        .alarm-image {
          display: flex;
          align-items: center;
          .left {
            display: flex;
            flex-direction: column;
            width: calc(100% - 2.02rem);
            .list {
              margin-top: 0.2rem;
              &:nth-child(1) {
                margin-top: 0;
              }
            }
            span {
              &:nth-child(1) {
                font-size: 0.2rem;
                font-family: PingFang SC;
                font-weight: 500;
                color: #5abefe;
              }
              &:nth-child(2) {
                font-size: 0.2rem;
                font-weight: 400;
                color: #dcf0ff;
              }
            }
          }
          .right {
            width: 2.02rem;
            height: 1.22rem;
            img {
              width: 100%;
              height: 100%;
            }
          }
        }
        .alarm-area {
          margin-top: 0.2rem;
          height: 1.82rem;
          background-image: url('../../assets/outlet/<EMAIL>');
          background-size: 100% 100%;
          padding: 0.2rem;
          .alarm-color-name {
            display: flex;
            align-items: center;
            .list {
              display: flex;
              align-items: center;
              flex: 1;
              span {
                &:nth-child(1) {
                  font-size: 0.18rem;
                  font-weight: 400;
                  color: #5abefe;
                }
                &:nth-child(2) {
                  font-size: 0.18rem;
                  font-weight: 400;
                  color: #d3e6f5;
                  margin-left: 0.25rem;
                }
              }
              img {
                width: 0.3rem;
                height: 0.3rem;
                margin-left: 0.25rem;
              }
            }
          }
          .color-area {
            width: 100%;
            display: flex;
            margin: 0.1rem 0;
            .left {
              display: inline-block;
              width: 0.9rem;
              font-size: 0.18rem;
              font-weight: 400;
              color: #5abefe;
            }
            .middle {
              width: 1.44rem;
              height: 0.71rem;
              margin: 0 0.18rem;
              background-image: url('../../assets/outlet/<EMAIL>');
              background-size: 100% 100%;
              padding: 0.1rem;
              div {
                font-size: 0.17rem;
                font-weight: 400;
                color: #d3e6f5;
                text-align: center;
              }
            }
            .right {
              display: flex;
              flex-direction: column;
              width: calc(100% - 2.7rem);
              .color-list {
                display: flex;
                align-items: center;
                span {
                  font-size: 0.17rem;
                  flex: 1;
                  font-weight: 400;
                  color: #d3e6f5;
                  text-align: center;
                }
              }
            }
          }
          .color-lists {
            display: flex;
            align-items: center;
            span {
              &:nth-child(1) {
                font-size: 0.18rem;
                font-weight: 400;
                color: #5abefe;
              }
              &:nth-child(2) {
                font-size: 0.18rem;
                font-weight: 400;
                color: #d3e6f5;
                margin-left: 0.25rem;
              }
            }
          }
        }
      }
      .alarm-btn {
        margin: 0.2rem auto 0;
        width: 1.4rem;
        height: 0.36rem;
        background: rgba(7, 62, 116, 0.78);
        text-align: center;
        font-size: 0.14rem;
        line-height: 0.36rem;
        cursor: pointer;
        color: #ffffff;
        span {
          &:nth-child(2) {
            margin-left: 0.1rem;
          }
        }
      }
      .drain-time {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 0.3rem;
        .drain-list {
          display: flex;
          align-items: center;
          span {
            font-size: 0.2rem;
            font-weight: 500;
            &:nth-child(1) {
              color: #5abefe;
            }
            &:nth-child(2) {
              color: #dcf0ff;
            }
          }
        }
      }
      .drain-title {
        margin-top: 0.1rem;
        padding: 0 0.3rem;
        font-size: 0.2rem;
        font-weight: 500;
        color: #5abefe;
      }
      .drain-device {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 0.1rem;
        padding: 0 0.3rem;
        .device-list {
          display: flex;
          align-items: center;
          span {
            font-size: 0.2rem;
            font-weight: 400;
            color: #99cbeb;
          }
          img {
            margin-left: 0.3rem;
            width: 0.3rem;
            height: 0.3rem;
          }
        }
      }
      .drain-title {
        span {
          font-size: 0.2rem;
          font-weight: 400;
          color: #99cbeb;
          &:nth-child(1) {
            display: inline-block;
            text-align-last: justify;
          }
        }
      }
      .monitor-table {
        background-image: url('../../assets/outlet/<EMAIL>');
        background-size: 100% 100%;
        min-height: 0.54rem;
        padding: 0.15rem 0.3rem;
        margin: 0.1rem 0.4rem 0;
        position: relative;
        .monitor-list {
          display: flex;
          align-items: center;
          line-height: 0.2rem;
          &:nth-child(1) {
            span {
              color: #5abefe !important;
              font-size: 0.2rem;
            }
          }
          &:not(first-child) {
            margin-top: 0.1rem;
            span {
              &:nth-child(2) {
                color: #ff4343;
                span {
                  color: #ff4343;
                }
              }
            }
          }
          span {
            color: #ffffff;
            font-size: 0.2rem;
            line-height: 0.14rem;
            flex: 1;
            text-align: center;
            position: relative;
            &:nth-child(2),
            &:nth-child(3) {
              span {
                font-size: 0.12rem;
              }
            }
          }
          .line {
            width: 1px;
            height: 0.81rem;
            position: absolute;
            right: 0;
          }
        }
        .lines {
          position: absolute;
          top: 0;
          display: flex;
          align-items: center;
          width: 100%;
          height: 100%;
          left: 0;
          padding: 0 0.3rem;
          div {
            width: 20%;
            height: 100%;
            display: flex;
            padding: 0.1rem 0;
            justify-content: flex-end;
          }
        }
      }
      .relation {
        margin-top: 0.1rem;
        padding: 0 0.3rem;
        .relation-title {
          font-size: 0.2rem;
          font-weight: 500;
          color: #5abefe;
          line-height: 0.14rem;
        }
        .realtion-area {
          display: flex;
          align-items: center;
          margin-top: 0.05rem;
          width: 100%;
          justify-content: space-between;
          .relation-list {
            display: flex;
            align-items: center;
            span {
              font-size: 0.2rem;
              font-weight: 400;
              color: #99cbeb;
              display: inline-block;
              line-height: 0.14rem;
            }
            img {
              width: 0.3rem;
              height: 0.3rem;
              margin-left: 0.3rem;
            }
          }
        }
      }
    }
  }
  .ant-modal-close {
    display: none !important;
  }
  .ant-modal-footer {
    border: none !important;
  }
}
.common-main {
  .type-radio .ant-radio-group-small .ant-radio-button-wrapper {
    padding: 0;
  }
}
.title-warp {
  .title-select-waters {
    .ant-select-selection {
      width: 2rem !important;
    }
    .ant-select-selection__rendered {
      width: 80%;
    }
  }
  .title-select-water {
    display: flex;
    justify-content: space-between;
    .ant-select-selection {
      display: flex;
      justify-content: center;
      width: 1.5rem;
      height: 0.3rem;
      // background: rgba(14, 139, 255, 0.32);
      border: none;
      border-radius: unset;
      // clip-path: polygon(100% 0, 100% 100%, 12% 100%, 0% 70%, 0 0);
      background: url(../../assets/<EMAIL>);
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
    .ant-select-selection-selected-value {
      color: rgba(0, 234, 255, 1);
      font-size: 0.17rem;
      overflow: hidden; //超出的文本隐藏
      text-overflow: ellipsis; //溢出用省略号显示
      white-space: nowrap; //溢出不换行
    }

    .ant-select-focused .ant-select-selection,
    .ant-select-selection:focus,
    .ant-select-selection:active {
      border-color: #40a9ff;
      border-right-width: 0 !important;
      outline: 0;
      box-shadow: none;
    }
  }
  .title-select-waterss {
    .ant-select-selection {
      width: 2rem;
    }
  }
  .wide-select-water {
    display: flex;
    justify-content: space-between;
    .ant-select-selection {
      width: 2rem;
      height: 0.3rem;
      // background: rgba(14, 139, 255, 0.32);
      border: none;
      border-radius: unset;
      // clip-path: polygon(100% 0, 100% 100%, 12% 100%, 0% 70%, 0 0);
      background: url(../../assets/<EMAIL>);
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
    .ant-select-selection-selected-value {
      color: rgba(0, 234, 255, 1);
    }
    .ant-select-focused .ant-select-selection,
    .ant-select-selection:focus,
    .ant-select-selection:active {
      border-color: #40a9ff;
      border-right-width: 0 !important;
      outline: 0;
      box-shadow: none;
    }
  }
}
.video-div {
  .vjs-poster {
    // background-image: url(../../assets/video_bg.png);
    background-size: 100% 100%;
  }
  .video-player {
    .video-js.vjs-fluid {
      height: 100% !important;
    }
    .vjs-big-play-button {
      left: 32% !important;
    }
  }
}
</style>

<template>
  <!-- 水质监测 -->
  <section class="common-main">
    <!-- 中间部分 -->
    <section class="center-map">
      <!-- <keep-alive> -->
      <water-map
        ref="mapRef"
        :mapStyle="mapStyle"
        :mapZoom="mapViewZoom"
        :enterType="2"
        :mapMarker="mapWaterStations"
        :mapWaterStationsVideo="mapWaterStationsVideo"
        :waterSourceMonitorDevices="waterSourceMonitorDevices"
        :drainMonitorDevices="drainMonitorDevices"
        :buildingMarker="heavyPollutionEnterpriseList"
        :viewCenter="mapViewCenter"
        :currentSelectedStationMarker="currentSelectedStationMarker"
        :currentSelectedStationObj="currentSelectedStationObj"
        :drain-list-data="drainListData"
        :is-show-water="isShowWater"
        :is-show-drain="isShowDrain"
        :is-show-monitor="isShowMonitor"
        :is-show-water-source-monitor="isShowWaterSourceMonitor"
        :is-show-drain-monitor="isShowDrainMonitor"
        :drain-person-list="drainPersonList"
        :default-drain="defaultDrainStationDetailValue"
        :curr-select="currSelect"
        :airData="airData"
        @waterStationSelectChange="mapStationSelect"
        @waterBuildingSelectChange="mapBuildingSelect"
        @cameraChange="cameraSelect"
        @drainSelect="drainSelect"
        @drainPersonSelect="drainPersonSelect"
        @airVideo="airVideoSelect"
        @alarmDetail="showDrainDialog"
      />
      <!-- </keep-alive> -->
    </section>
    <section class="center-search">
      <div>
        <input type="text" placeholder="请输入关键字查询" v-model="searchWords" @keyup.enter="search" />
        <a-icon type="close-circle" style="font-size: 0.2rem; cursor: pointer; margin-right: 0.15rem" v-if="searchWords != ''" @click="clearSearch" />
        <div class="fetch-input" v-if="list.length">
          <div v-for="(item, index) in list" :key="index" @click="handleChoose(item)">
            {{ item.name }}
          </div>
        </div>
      </div>
      <div class="icon">
        <img src="@/assets/search.png" alt="" @click="search" />
      </div>
    </section>
    <!-- 表格 -->
    <div
      class="water-table"
      :style="{
        transform: `${isFold ? 'translate3d(-9.62rem, 0, 0 )' : 'translate3d(0,0,0)'}`,
      }"
    >
      <div class="water-table-thead">
        <div class="water-table-th">
          <div>所属河流</div>
          <div>断面名称</div>
          <div>{{ currYearName }}年考核目标</div>
          <div>{{ currYearName }}年</div>
          <div>{{ currYearName - 1 }}年</div>
        </div>
      </div>
      <div class="water-table-tbody">
        <div class="water-table-tr" v-for="(item, index) in waterAssesData" :key="index">
          <div>{{ item.riverName || '--' }}</div>
          <div>{{ item.crossSection || '--' }}</div>
          <div>{{ item.thisYearAssessmentGoal || '--' }}</div>
          <div>{{ item.waterTypeThisYear || '--' }}</div>
          <div>{{ item.waterTypeLastYear || '--' }}</div>
        </div>
      </div>
      <div class="water-table-bottom" @click="getMore" style="background-color: #03143b">查看更多</div>
    </div>
    <!-- 无人机 -->
    <div
      v-if="isAirShow"
      class="air-video"
      :style="{
        transform: `${isFold ? 'translate3d(-9.62rem, 0, 0 )' : 'translate3d(0,0,0)'}`,
      }"
    >
      <div class="video-title">
        <img src="../../assets/<EMAIL>" alt="icon" />
        <span>立体防控</span>
        <img src="../../assets/guanbi.png" alt="" class="close" @click="isAirShow = false" />
      </div>
      <div class="air-video-area">
        <LivePlayer :video-url="'http://222.209.208.150:28000/live/watersource.flv'" :aspect="'327:186'" fluent autoplay live :stretch="true" />
      </div>
    </div>
    <!--    筛选  -->
    <!--    <section class="left-side-three">-->
    <!--      <div class="list" @click="changeSelect(1)">-->
    <!--        <span>水质监测</span>-->
    <!--        <span>{{waterStationList.length}}个</span>-->
    <!--        <span class="circle"-->
    <!--        ><span :class="{ active: isShowWater ? 'active' : '' }"></span-->
    <!--        ></span>-->
    <!--      </div>-->
    <!--      <div class="list" @click="changeSelect(2)">-->
    <!--        <span>排口监测</span>-->
    <!--        <span>{{drainListLength}}个</span>-->
    <!--        <span class="circle"-->
    <!--        ><span :class="{ active: isShowDrain ? 'active' : '' }"></span-->
    <!--        ></span>-->
    <!--      </div>-->
    <!--    </section>-->
    <section
      class="right-select-type"
      :style="{
        transform: `${isFold || (!isShowWater && !isShowDrain && !isShowWaterSourceMonitor && !isShowDrainMonitor) ? 'translate3d(4.5rem, 0, 0 )' : 'translate3d(0,0,0)'}`,
      }"
    >
      <div class="list" @click="changeSelect(1)">
        <img src="@/assets/heavily/<EMAIL>" alt="" class="gou" v-if="!isShowWater" />
        <img src="@/assets/gou_active.png" alt="" class="gou" v-if="isShowWater" />
        <div>水质监测</div>
        <div>{{ waterStationList.length }}个</div>
<!--        <div>(离线: {{ waterStationList.filter(item => item.online === 0).length }}个)</div>-->

      </div>
      <div class="list" @click="changeSelect(2)">
        <img src="@/assets/heavily/<EMAIL>" alt="" class="gou" v-if="!isShowDrain" />
        <img src="@/assets/gou_active.png" alt="" class="gou" v-if="isShowDrain" />
        <div>排口监测</div>
        <div>{{ drainListLength }}个</div>
      </div>
      <div class="list" @click="changeSelect(3)">
        <img src="@/assets/heavily/<EMAIL>" alt="" class="gou" v-if="!isShowWaterSourceMonitor" />
        <img src="@/assets/gou_active.png" alt="" class="gou" v-if="isShowWaterSourceMonitor" />
        <div>水源地监控</div>
        <div>{{ waterSourceMonitorDevices.length }}个</div>
        <div>(离线: {{ waterSourceMonitorOfflineCount.length }}个)</div>
      </div>
      <div class="list" @click="changeSelect(4)">
        <img src="@/assets/heavily/<EMAIL>" alt="" class="gou" v-if="!isShowDrainMonitor" />
        <img src="@/assets/gou_active.png" alt="" class="gou" v-if="isShowDrainMonitor" />
        <div>入河排口监控</div>
        <div>{{ drainMonitorDevices.length }}个</div>
        <div>(离线: {{ drainMonitorOfflineCount.length }}个)</div>

      </div>
    </section>
    <div :title="isFold ? '展开' : '收起'" class="fold-module" @click="toggleFoldModule">
      <img src="@/assets/quanping.png" alt />
    </div>
    <!-- 右侧部分 -->
    <section
      class="left-part margin-r15"
      :style="{
        transform: `${isShowKind ? (isFold || !isShowWater ? 'translate3d(9.62rem, 0, 0 )' : 'translate3d(0,0,0)') : 'translate3d(9.62rem, 0, 0 )'}`,
      }"
    >
      <a
        :href="'http://water-data.jinnq.com/?' + ue + '&' + pd + '?'"
        tagert="_self"
        class="law-vehicle"
        :class="{ 'law-vehicles': isAlarm ? 'law-vehicles' : '' }"
      >
        <div class="within-vehicle Rotation" :class="{ 'within-vehicles': isAlarm ? 'within-vehicles' : '' }"></div>
        <div class="vehicle-text">金牛</div>
        <div class="vehicle-text">水源</div>
      </a>
      <div class="common-title left-part-one marginBot10 water-right-div">
        <div>
          <div class="title-warp">
            <div class="title prefix">水质监测</div>
            <div class="water-types">
              <div class="water-types-top">
                <img src="@/assets/shuilei.png" alt="" />
                <span>{{ waterType }}类</span>
              </div>
              <div class="water-types-bottom">
                <a-select v-model="defaultStationDetailValue" class="title-select-water" @change="waterStationDetailChange">
                  <a-icon slot="suffixIcon" type="caret-down" style="color: rgba(0, 234, 255, 1); width: 14px; height: 8px" />
                  <a-select-option :value="item.stationId" v-for="(item, index) in waterStationList" :key="index"> {{ item.stationName }}</a-select-option>
                </a-select>
              </div>
            </div>
          </div>
          <div class="sub-title">
            <img src="@/assets/biaoti.png" alt class="title-img" />
          </div>
          <div class="common-content type-radio adjust-width content-width" style="border: none">
            <a-radio-group
              v-model="waterMonitorCuttentValue"
              size="small"
              buttonStyle="solid"
              style="width: 100%"
              class="water-monitor-tabs"
              @change="waterMonitorSelectChange"
            >
              <a-radio-button v-for="(item, index) in waterMonitorButtonList" :key="index" :value="`${item.id}`" class="water-monitor-tab">{{
                item.name
              }}</a-radio-button>
            </a-radio-group>

            <LineChartDashed :id="'waterMonitor' + new Date().getTime()" :width="'4.5rem'" :height="'1.8rem'" :propData="waterMonitor" :smooth="true" />
          </div>
        </div>

        <div class="common-title left-part-two">
          <div class="title prefix title-flex">
            <span style="text-indent: 0.2rem">水质变化分析</span>
            <div>
              <div :class="{ 'type-active': airQualityType === '同比' }" @click="airQualityType = '同比'">同比</div>
              <div :class="{ 'type-active': airQualityType === '环比' }" @click="airQualityType = '环比'">环比</div>
            </div>
          </div>
          <!-- <div class="sub-title">
            <img src="@/assets/biaoti.png" alt class="title-img" />
          </div>-->
          <div class="common-content adjust-width content-width" style="border: none">
            <LineAndBarChart
              v-if="airQualityType == '同比'"
              id="waterchange"
              width="4.5rem"
              height="2.2rem"
              :LineAndBarData="waterchange"
              style="margin-top: 0.1rem"
            />
            <LineAndBarChartOne
              v-if="airQualityType == '环比' && chainRatio.monthList.length > 0"
              :id="'LineAndBarChartOne-3'"
              :width="'4.5rem'"
              :height="'2.2rem'"
              :propData="chainRatio"
              style="margin-top: 0.1rem"
            />
            <div class="empty" v-if="airQualityType == '环比' && chainRatio.monthList.length == 0">暂无数据</div>
          </div>
        </div>
        <div>
          <div class="title-warp">
            <div class="title prefix">水站监控信息</div>
            <!--            <a-select-->
            <!--              v-model="defaultStationValue"-->
            <!--              class="title-select-water"-->
            <!--              @change="waterStationChange"-->
            <!--            >-->
            <!--              <a-icon-->
            <!--                slot="suffixIcon"-->
            <!--                type="caret-down"-->
            <!--                style="color:rgba(0, 234, 255, 1);width:14px;height:8px;"-->
            <!--              />-->
            <!--              <a-select-option-->
            <!--                :value="item.stationId"-->
            <!--                v-for="(item, index) in waterStationList"-->
            <!--                :key="index"-->
            <!--              >-->
            <!--                {{ item.stationName }}</a-select-option-->
            <!--              >-->
            <!--            </a-select>-->
            <a-select v-model="defaultCameraValue" class="title-select-water title-select-waters" @change="waterCameraChange">
              <a-icon slot="suffixIcon" type="caret-down" style="color: rgba(0, 234, 255, 1); width: 14px; height: 8px" />
              <a-select-option :value="item.monitorId" v-for="(item, index) in currentCameraList" :key="index"> {{ item.monitorName }}</a-select-option>
            </a-select>
          </div>
          <div class="sub-title">
            <img src="@/assets/biaoti.png" alt class="title-img" />
          </div>
          <div class="station-addr">
            <img src="@/assets/<EMAIL>" alt style="width: 0.2rem" />
            <span>{{ currentStationAddress }}</span>
          </div>
          <div class="common-content video adjust-width video-div">
            <div style="width: 50%">
              <div class="video-top">
                <span>视频监控</span>
                <span style="cursor: pointer; text-decoration: underline; color: rgba(0, 234, 255, 1)" @click="waterVideo">更多视频</span>
              </div>
              <!-- <player
                v-if="currentCameraList.length && playerOptions.sources.length == 2"
                ref="videoPlayer"
                :video-url="playerOptions.sources[1].src"
                :error="videoError"
                :message="videoError"
                :height="155"
                :has-audio="false"
                :index="new Date().getTime()"
                fluent
                :autoplay="false"
                :show="false"
                live
                muted
              /> -->
              <video-player
                v-if="currentCameraList.length"
                style="margin-top: 0.05rem"
                class="vjs-custom-skin"
                ref="livePlayer"
                :playsinline="true"
                @statechanged="playerStateChanged($event)"
                :options="playerOptions"
              ></video-player>
              <div v-else class="no-video">
                <span class="no-text">该站点暂未绑定监控</span>
              </div>
            </div>
            <div class="person-infor">
              <div class="card-top">
                <span>巡岗上报</span>
                <span style="cursor: pointer; text-decoration: underline; color: rgba(0, 234, 255, 1)" @click="stationPatrolRecord">更多记录</span>
              </div>
              <div class="card-bottom">
                <div v-if="this.patrolDetail && JSON.stringify(patrolDetail) !== '{}'">
                  <div class="person-infor-line">
                    <span>巡岗人员：</span>
                    <span>{{ patrolDetail.userName }}</span>
                  </div>
                  <div class="person-infor-line">
                    <span>打卡时间：</span>
                    <span>{{ patrolDetail.time }}</span>
                  </div>
                  <div class="person-infor-line">
                    <img v-if="patrolDetail.annexUrlList && patrolDetail.annexUrlList.length" :src="patrolDetail.annexUrlList[0]" alt />
                    <div class="person-infor-line-card" style="font-size: 0.16rem !important">巡站图片</div>
                  </div>
                </div>
                <div v-else class="no-image">
                  <img src="../../assets/heavily/<EMAIL>" alt="" />
                  <span>该站点暂无巡岗记录</span>
                </div>
              </div>
            </div>
            <!-- <div class="video-text">监测点排污口</div> -->
          </div>
        </div>
      </div>
    </section>
    <section
      v-show="!(isFold || isShowDrain) || isDrainFirst"
      class="left-part margin-r15"
      :style="{
        transform: `${isShowKind ? (isFold || !isShowDrain || isShowWater ? 'translate3d(9.62rem, 0, 0)' : 'translate3d(0,0,0)') : 'translate3d(0,0,0)'}`,
      }"
    >
      <div
        class="common-title left-part-one marginBot10 water-right-divs"
        :class="{
          'water-right-div-area': isDrainPerson ? 'water-right-div-area' : '',
        }"
      >
        <div v-show="!isDrainPerson">
          <div class="title-warp">
            <div class="title">{{ `排口信息` }}</div>
            <div class="water-types-bottom">
              <a-select v-model="defaultDrainStationDetailValue" class="title-select-water" @change="drainStationDetailChange">
                <a-icon slot="suffixIcon" type="caret-down" style="color: rgba(0, 234, 255, 1); width: 14px; height: 8px" />
                <a-select-option :value="item.waterDrainId" v-for="(item, index) in drainListData" :key="index"> {{ item.drainName }}</a-select-option>
              </a-select>
            </div>
          </div>
          <div class="sub-title">
            <img src="@/assets/biaoti.png" alt class="title-img" />
          </div>
          <div class="region">
            <div class="btn">
              <span :class="{ 'btn-choose': type == 1 ? 'btn-choose' : '' }" @click="type = 1">基础信息</span>
              <span :class="{ 'btn-choose': type == 2 ? 'btn-choose' : '' }" @click="type = 2">告警信息</span>
            </div>
            <table v-if="type == 1" border="1" class="table-site">
              <tr>
                <td class="table-name">排口编号</td>
                <td colspan="3" class="table-text" style="text-align: left; padding-left: 0.2rem">
                  {{ drainDetail.drainName ? drainDetail.drainName : '--' }}
                </td>
              </tr>
              <tr>
                <td class="table-name">排口地址</td>
                <td colspan="3" class="table-text" style="text-align: left; padding-left: 0.2rem">
                  {{ drainDetail.address ? drainDetail.address : '--' }}
                </td>
              </tr>
              <tr>
                <td class="table-name">经纬度</td>
                <td colspan="3" class="table-text" style="text-align: left; padding-left: 0.2rem">{{ drainDetail.lng }},{{ drainDetail.lat }}</td>
              </tr>
              <tr>
                <td class="table-name">河流名称</td>
                <td class="table-text">
                  {{ drainDetail.riverName ? drainDetail.riverName : '--' }}
                </td>
                <td class="table-name">排口类型</td>
                <td class="table-text">
                  {{ drainDetail.drainTypeName ? drainDetail.drainTypeName : '--' }}
                </td>
              </tr>
              <tr>
                <td class="table-name">上游站点</td>
                <td class="table-text">
                  {{ drainDetail.upWaterStationName ? drainDetail.upWaterStationName : '--' }}
                </td>
                <td class="table-name">上游站点</td>
                <td class="table-text">
                  {{ drainDetail.downWaterStationName ? drainDetail.downWaterStationName : '--' }}
                </td>
              </tr>
              <tr></tr>
              <tr>
                <td class="table-name">监测设备</td>
                <td class="table-text">
                  <span
                    class="state"
                    :class="{
                      'state-err': !drainDetail.deviceStatus ? 'state-err' : '',
                    }"
                    >{{ drainDetail.deviceStatus ? '在线' : '离线' }}</span
                  >
                </td>
                <td class="table-name">采样设备</td>
                <td class="table-text">
                  <span
                    class="state"
                    :class="{
                      'state-err': !drainDetail.deviceStatus ? 'state-err' : '',
                    }"
                    >{{ drainDetail.deviceStatus ? '在线' : '离线' }}</span
                  >
                </td>
              </tr>
            </table>

            <div v-if="type == 2 && drainDetail.alarm && drainDetail.alarm.alarmType == 2" class="drain-alarm">
              <div class="drain-time">
                <div class="drain-list">
                  <span>告警时间：</span>
                  <span>{{ drainDetail.alarm.alarmTime ? drainDetail.alarm.alarmTime.substr(5, 11) : '--' }}</span>
                </div>
                <div class="drain-list">
                  <span>告警类型：</span>
                  <span>{{ drainDetail.alarm.alarmTypeStr }}</span>
                </div>
              </div>
              <div class="drain-title">智能分析：</div>
              <div class="drain-device">
                <div class="device-list">
                  <span>设备状态</span>
                  <img :src="drainDetail.alarm.deviceStatus ? gou : cha" alt="" />
                </div>
                <div class="device-list">
                  <span>样品抽取</span>
                  <img :src="drainDetail.alarm.isSample ? gou : cha" alt="" />
                </div>
                <div class="device-list">
                  <span>是否排水</span>
                  <img :src="drainDetail.alarm.isDrain ? gou : cha" alt="" />
                </div>
              </div>
              <div class="drain-title">
                <span>异变量：</span><span v-if="drainDetail.alarm.drainAlarmList && !drainDetail.alarm.drainAlarmList.length">--</span>
              </div>
              <div class="monitor-table" v-if="drainDetail.alarm.drainAlarmList && drainDetail.alarm.drainAlarmList.length">
                <div class="monitor-list">
                  <span>异变量</span>
                  <span>监测值</span>
                  <span>阈值</span>
                  <span>超标比例</span>
                  <span>变化趋势</span>
                </div>
                <div class="monitor-list" v-for="(item, index) in drainDetail.alarm.drainAlarmList" :key="index">
                  <span>{{ item.alarmParamTypeStr }}</span>
                  <span
                    >{{ item.alarmValue }}<span>{{ item.unit }}</span></span
                  >
                  <span
                    >{{ item.alarmThreshold }}<span>{{ item.unit }}</span></span
                  >
                  <span>{{ item.rate ? item.rate : '--' }}{{ item.rate ? '%' : '' }}</span>
                  <span>{{ item.trend }}</span>
                </div>
                <div class="lines">
                  <div>
                    <img src="../../assets/<EMAIL>" alt="" class="line" />
                  </div>
                  <div>
                    <img src="../../assets/<EMAIL>" alt="" class="line" />
                  </div>
                  <div>
                    <img src="../../assets/<EMAIL>" alt="" class="line" />
                  </div>
                  <div>
                    <img src="../../assets/<EMAIL>" alt="" class="line" />
                  </div>
                </div>
              </div>
              <div class="relation">
                <div class="relation-title">关联执行：</div>
                <div class="realtion-area">
                  <div class="relation-list" v-if="!(drainDetail.alarm.drainAlarmList && drainDetail.alarm.drainAlarmList.length)">
                    <span>持续监测</span>
                    <img src="../../assets/outlet/<EMAIL>" alt="" />
                  </div>
                  <div class="relation-list" v-if="drainDetail.alarm.drainAlarmList && drainDetail.alarm.drainAlarmList.length">
                    <span>留样核验</span>
                    <img src="../../assets/outlet/<EMAIL>" alt="" />
                  </div>
                  <div class="relation-list" v-if="drainDetail.alarm.drainAlarmList && drainDetail.alarm.drainAlarmList.length">
                    <span>启动追溯</span>
                    <img src="../../assets/outlet/<EMAIL>" alt="" />
                  </div>
                  <div class="relation-list" style="width: 0.8rem"></div>
                  <div class="relation-list" style="width: 0.8rem" v-if="!(drainDetail.alarm.drainAlarmList && drainDetail.alarm.drainAlarmList.length)"></div>
                </div>
              </div>
            </div>
            <div v-if="type == 2 && drainDetail.alarm && drainDetail.alarm.alarmType == 1" class="drain-alarms">
              <div class="drain-top">
                <div class="left">
                  <div class="drain-list">
                    <span>告警时间：</span>
                    <span>{{ drainDetail.alarm.alarmTime ? drainDetail.alarm.alarmTime.substr(5, 11) : '--' }}</span>
                  </div>
                  <div class="drain-list">
                    <span>告警类型：</span>
                    <span>{{ drainDetail.alarm.alarmTypeStr }}</span>
                  </div>
                  <div class="drain-list">
                    <span>智能分析：</span>
                  </div>
                </div>
                <div
                  class="right"
                  :style="{
                    backgroundImage: 'url(' + drainDetail.alarm.smartSnap.snapImage + ')',
                  }"
                ></div>
              </div>
              <div class="drain-bottom">
                <div class="drain-anysis">
                  <div class="ansyis-list">
                    <span>单帧采集</span>
                    <img :src="drainDetail.alarm.smartSnap && drainDetail.alarm.smartSnap.gatheringType ? gou : cha" alt="" />
                  </div>
                  <div class="ansyis-list">
                    <span>特征抽取</span>
                    <span>{{
                      drainDetail.alarm.smartSnap && drainDetail.alarm.smartSnap.gatheringType ? drainDetail.alarm.smartSnap.gatheringType : '--'
                    }}</span>
                  </div>
                </div>
                <div class="color-area">
                  <div class="color-title">颜色解析</div>
                  <div class="color-left">
                    <div class="color-params">
                      透明度:{{ drainDetail.alarm.smartSnap && drainDetail.alarm.smartSnap.chromaAnalysis ? drainDetail.alarm.smartSnap.chromaAnalysis : '--' }}
                    </div>
                    <div class="color-params">
                      深浅度:{{ drainDetail.alarm.smartSnap && drainDetail.alarm.smartSnap.depthAnalysis ? drainDetail.alarm.smartSnap.depthAnalysis : '--' }}
                    </div>
                  </div>
                  <div
                    class="color-right"
                    v-if="
                      drainDetail.alarm.smartSnap &&
                      drainDetail.alarm.smartSnap.waterColorAnalysisList &&
                      drainDetail.alarm.smartSnap.waterColorAnalysisList.length
                    "
                  >
                    <div class="color-list-area">
                      <div class="color-list" v-for="(item, index) in drainDetail.alarm.smartSnap.waterColorAnalysisList" :key="index">
                        <span>1.H: {{ item.hue ? item.hue : '--' }} </span>
                        <span>S:{{ item.saturation ? item.saturation : '--' }} </span>
                        <span>V:{{ item.lightness ? item.lightness : '--' }}</span>
                        <span>{{ item.proportion ? item.proportion : '--' }}{{ item.proportion ? '%' : '' }}</span>
                        <span>{{ item.chineseName ? item.chineseName : '--' }}</span>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="drain-anysis">
                  <div class="ansyis-list">
                    <span>漂浮物</span>
                    <span>{{ drainDetail.alarm.smartSnap && drainDetail.alarm.smartSnap.floating ? drainDetail.alarm.smartSnap.floating : '--' }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-show="!isDrainPerson">
          <div class="title-warp">
            <div class="title">{{ `排口监测` }}</div>
          </div>
          <div class="sub-title">
            <img src="@/assets/biaoti.png" alt class="title-img" />
          </div>
          <div class="region regions">
            <div class="swiper-area" v-if="drainDetail.monitorMap && drainDetail.monitorMap.length">
              <!-- 左侧箭头 -->
              <div class="left-arrow">
                <svgicon name="left" color="#00D9D5" class="svg-arrow"></svgicon>
              </div>
              <div class="middle">
                <swiper class="btn" :options="swiperOptions" v-if="drainDetail.monitorMap && drainDetail.monitorMap.length > 4">
                  <swiper-slide
                    class="single-slide"
                    :class="{
                      'btn-choose': pollutionType == index ? 'btn-choose' : '',
                    }"
                    v-for="(item, index) in drainDetail.monitorMap"
                    :key="index"
                    :data-index="index"
                    >{{ item.name }}</swiper-slide
                  >
                </swiper>
                <div class="btn" v-else>
                  <div
                    class="single-slide"
                    :class="{
                      'btn-choose': pollutionType == index ? 'btn-choose' : '',
                    }"
                    v-for="(item, index) in drainDetail.monitorMap"
                    :key="index"
                    @click="changeType(index)"
                  >
                    {{ item.name }}
                  </div>
                </div>
              </div>
              <div class="right-arrow">
                <svgicon name="right" color="#00D9D5" class="svg-arrow"></svgicon>
              </div>
            </div>
            <div class="chart-area" v-if="drainDetail.monitorMap && drainDetail.monitorMap.length && (!isShowWater || !isShowKind)">
              <LinessChartDasheds
                :id="'Liness' + new Date().getTime()"
                :width="'100%'"
                :height="'100%'"
                :propData="drainDetail.monitorMap[pollutionType].lineData"
                :smooth="false"
                v-if="drainDetail.monitorMap[pollutionType].lineData.dataList.length"
              />
              <div v-else class="no-data">暂未开通</div>
            </div>
            <div v-if="!drainDetail.monitorMap || !drainDetail.monitorMap.length" class="no-datas">暂未开通</div>
          </div>
        </div>
        <div v-show="!isDrainPerson">
          <div class="title-warp">
            <div class="title">监控视频</div>
            <div class="water-types">
              <div class="water-types-bottom">
                <a-select v-model="defaultDrainCamera" class="title-select-water title-select-waterss" @change="drainCameraChange">
                  <a-icon slot="suffixIcon" type="caret-down" style="color: rgba(0, 234, 255, 1); width: 14px; height: 8px" />
                  <a-select-option :value="item.monitorId" v-for="(item, index) in drainDetail.drainCameraList" :key="index">
                    {{ item.monitorName }}</a-select-option
                  >
                </a-select>
              </div>
            </div>
          </div>
          <div class="sub-title">
            <img src="@/assets/biaoti.png" alt class="title-img" />
          </div>
          <div class="region">
            <div class="drain-video">
              <LivePlayer v-if="videoUrl" :video-url="videoUrl" fluent autoplay live :stretch="true" />
              <div v-else class="not-online" :style="{ backgroundImage: 'url(' + snapUrl + ')' }">暂无监控视频</div>
            </div>
          </div>
        </div>
        <div v-show="isDrainPerson">
          <div class="title-warp">
            <div class="title">{{ `排户信息` }}</div>
          </div>
          <div class="sub-title">
            <img src="@/assets/biaoti.png" alt class="title-img" />
          </div>
          <div class="region">
            <table border="1" class="table-site">
              <tr>
                <td class="table-name">排户名称</td>
                <td colspan="3" class="table-text" style="text-align: left; padding-left: 0.2rem">
                  {{ drainPersonDetail.merchantName ? drainPersonDetail.merchantName : '--' }}
                </td>
              </tr>
              <tr>
                <td class="table-name">指纹编号</td>
                <td colspan="3" class="table-text" style="text-align: left; padding-left: 0.2rem">
                  {{ drainPersonDetail.merchantNumber ? drainPersonDetail.merchantNumber : '--' }}
                </td>
              </tr>
              <tr>
                <td class="table-name">排户地址</td>
                <td colspan="3" class="table-text" style="text-align: left; padding-left: 0.2rem">
                  {{ drainPersonDetail.merchantAddress ? drainPersonDetail.merchantAddress : '--' }}
                </td>
              </tr>
              <tr>
                <td class="table-name">经纬度</td>
                <td colspan="3" class="table-text" style="text-align: left; padding-left: 0.2rem">
                  {{ drainPersonDetail.merchantLng }},{{ drainPersonDetail.merchantLat }}
                </td>
              </tr>
              <tr>
                <td class="table-name">排户类型</td>
                <td class="table-text">
                  {{ drainPersonDetail.merchantTypeName ? drainPersonDetail.merchantTypeName : '--' }}
                </td>
                <td class="table-name">排水类型</td>
                <td class="table-text">
                  {{ drainPersonDetail.drainType ? drainPersonDetail.drainType : '--' }}
                </td>
              </tr>
              <tr>
                <td class="table-name">关联排口</td>
                <td colspan="3" class="table-text" style="text-align: left; padding-left: 0.2rem">
                  {{ drainPersonDetail.drainNames ? drainPersonDetail.drainNames : '--' }}
                </td>
              </tr>
              <tr>
                <td class="table-name">联系人</td>
                <td class="table-text">
                  {{ drainPersonDetail.personName ? drainPersonDetail.personName : '--' }}
                </td>
                <td class="table-name">联系方式</td>
                <td class="table-text">
                  {{ drainPersonDetail.phoneNumber ? drainPersonDetail.phoneNumber : '--' }}
                </td>
              </tr>
            </table>
          </div>
        </div>
        <div v-show="isDrainPerson" style="margin-top: 0.15rem">
          <div class="title-warp">
            <div class="title">{{ `指纹信息` }}</div>
          </div>
          <div class="sub-title">
            <img src="@/assets/biaoti.png" alt class="title-img" />
          </div>
          <div class="fingerprint-swiper-area">
            <swiper
              class="swiper-wrapper"
              :options="drainPersonDetail.drainMerchantSampleRecordList && drainPersonDetail.drainMerchantSampleRecordList.length > 3 ? options : optionss"
              v-if="drainPersonDetail.drainMerchantSampleRecordList && drainPersonDetail.drainMerchantSampleRecordList.length"
            >
              <swiper-slide class="fingerprint-swiper" v-for="(item, index) in drainPersonDetail.drainMerchantSampleRecordList" :key="index">
                <div class="fingerprint">
                  <div class="left">
                    <img src="../../assets/outlet/<EMAIL>" alt="" />
                    <span>第{{ dataNumber[index] }}次取样</span>
                  </div>
                  <div class="right">
                    <span>{{ item.recordTime ? item.recordTime.substr(5, 11) : '--' }}</span>
                  </div>
                </div>
                <div class="fingerprint-area">
                  <div class="fingerprint-bg">
                    <div>
                      <span>总磷(mg/L)</span>
                      <span>{{ item.totalPhosphorus ? item.totalPhosphorus : '--' }}</span>
                    </div>
                    <div>
                      <span>氨氮(mg/L)</span>
                      <span>{{ item.ammoniaNitrogen ? item.ammoniaNitrogen : '--' }}</span>
                    </div>
                    <div>
                      <span>COD(mg/L)</span>
                      <span>{{ item.cod ? item.cod : '--' }}</span>
                    </div>
                    <div>
                      <span>水质类型</span>
                      <span>{{ item.waterType ? item.waterType : '--' }}</span>
                    </div>
                  </div>
                </div>
              </swiper-slide>
            </swiper>
          </div>
        </div>
      </div>
    </section>
    <!-- 弹框部分-->
    <a-modal :visible="visible" class="ant-modal-water" :footer="null" :destroyOnClose="true" @cancel="handleCancel">
      <div class="title">
        {{ currYearName }}年成都市水环境质量考核断面水质目标完成情况
        <div style="font-size: 0.16rem; font-family: PingFang SC; font-weight: 400; color: #ffffff">
          数据更新周期：{{ UpdateC.startDate }}~{{ UpdateC.endDate }}
        </div>
        <!-- <div class="bg" /> -->
      </div>
      <div>
        <table class="table-container" border="1px" cellpadding="5" cellspacing="0" align="center">
          <thead>
            <tr style="height: 0.49rem">
              <th>区域</th>
              <th>断面性质</th>
              <th>{{ currYearName }}考核目标</th>
              <th>{{ currYearName }}年</th>
              <th>{{ currYearName - 1 }}年</th>
              <th>同比</th>
            </tr>
          </thead>
          <tbody>
            <tr style="height: 0.49rem" v-for="(item, index) in waterAssesData" :key="index">
              <td>{{ item.riverName || '--' }}</td>
              <td>{{ item.crossSection || '--' }}</td>
              <td>{{ item.thisYearAssessmentGoal || '--' }}</td>
              <td>{{ item.waterTypeThisYear || '--' }}</td>
              <td>{{ item.waterTypeLastYear || '--' }}</td>
              <td>{{ item.waterTypeYearOnYear || '--' }}</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div>
        <table class="table-container" border="1px" cellpadding="5" cellspacing="0" align="center">
          <thead>
            <tr style="height: 0.49rem">
              <th rowspan="2">所属河流</th>
              <th rowspan="2">断面名称</th>
              <th colspan="3">氨氮（mg/L）</th>
              <th colspan="3">总磷（mg/L）</th>
              <th colspan="3">化学需氧量（mg/L）</th>
            </tr>
            <tr style="height: 0.49rem">
              <th>{{ currYearName }}年</th>
              <th>{{ currYearName - 1 }}年</th>
              <th>同比</th>
              <th>{{ currYearName }}年</th>
              <th>{{ currYearName - 1 }}年</th>
              <th>同比</th>
              <th>{{ currYearName }}年</th>
              <th>{{ currYearName - 1 }}年</th>
              <th>同比</th>
            </tr>
          </thead>
          <tbody>
            <tr style="height: 0.49rem" v-for="(item, index) in waterAssesData" :key="index">
              <td>{{ item.riverName || '--' }}</td>
              <td>{{ item.crossSection || '--' }}</td>
              <td>{{ item.nh3nThisYear || '--' }}</td>
              <td>{{ item.nh3nLastYear || '--' }}</td>
              <td>
                {{ item.nh3nYearOnYear ? item.nh3nYearOnYear + '%' : '--' }}
                <img :src="item.nh3nYearOnYear < 0 ? xia : !item.nh3nYearOnYear || item.nh3nYearOnYear == 0 ? '' : shang" alt="" />
              </td>
              <td>{{ item.totalPhosphorusThisYear || '--' }}</td>
              <td>{{ item.totalPhosphorusLastYear || '--' }}</td>
              <td>
                {{ item.totalPhosphorusYearOnYear ? item.totalPhosphorusYearOnYear + '%' : '--' }}
                <img
                  :src="item.totalPhosphorusYearOnYear < 0 ? xia : !item.totalPhosphorusYearOnYear || item.totalPhosphorusYearOnYear == 0 ? '' : shang"
                  alt=""
                />
              </td>
              <td>{{ item.dissolvedOxygenThisYear || '--' }}</td>
              <td>{{ item.dissolvedOxygenLastYear || '--' }}</td>
              <td>
                {{ item.dissolvedOxygenYearOnYear ? item.dissolvedOxygenYearOnYear + '%' : '--' }}
                <img
                  :src="item.dissolvedOxygenYearOnYear < 0 ? xia : !item.dissolvedOxygenYearOnYear || item.dissolvedOxygenYearOnYear == 0 ? '' : shang"
                  alt=""
                />
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </a-modal>
    <a-modal :visible="visible1" v-if="alarmInfo" class="ant-modal-alarms" @cancel="handleCancelAlarm">
      <div v-if="alarmInfo.alarmType == 1" class="content">
        <div class="alarm-content">
          <div class="alarm-image">
            <div class="left">
              <div class="list">
                <span>告警时间：</span>
                <span>{{ alarmInfo.alarmTime ? alarmInfo.alarmTime.substr(5, 11) : '--' }}</span>
              </div>
              <div class="list">
                <span>告警类别：</span>
                <span>{{ alarmInfo.alarmTypeStr ? alarmInfo.alarmTypeStr : '--' }}</span>
              </div>
              <div class="list">
                <span>智能分析：</span>
              </div>
            </div>
            <div class="right">
              <img :src="alarmInfo.smartSnap && alarmInfo.smartSnap.snapImage" alt="" />
            </div>
          </div>
          <div class="alarm-area">
            <div class="alarm-color-name">
              <div class="list">
                <span>单帧采集：</span>
                <img :src="alarmInfo.smartSnap && alarmInfo.smartSnap.gatheringType ? gou : cha" alt="" />
              </div>
              <div class="list">
                <span>特征抽取：</span>
                <span>{{ alarmInfo.smartSnap && alarmInfo.smartSnap.featureExtraction ? alarmInfo.smartSnap.featureExtraction : '--' }}</span>
              </div>
            </div>
            <div class="color-area">
              <span class="left">色度解析：</span>
              <div class="middle">
                <div>透明度：{{ alarmInfo.smartSnap && alarmInfo.smartSnap.chromaAnalysis ? alarmInfo.smartSnap.chromaAnalysis : '--' }}</div>
                <div>深浅度：{{ alarmInfo.smartSnap && alarmInfo.smartSnap.depthAnalysis ? alarmInfo.smartSnap.depthAnalysis : '--' }}</div>
              </div>
              <div class="right" v-if="alarmInfo.smartSnap && alarmInfo.smartSnap.waterColorAnalysisList && alarmInfo.smartSnap.waterColorAnalysisList.length">
                <div class="color-list" v-for="(item, index) in alarmInfo.smartSnap.waterColorAnalysisList" :key="index">
                  <span>1.H: {{ item.hue ? item.hue : '--' }} </span>
                  <span>S:{{ item.saturation ? item.saturation : '--' }} </span>
                  <span>V:{{ item.lightness ? item.lightness : '--' }}</span>
                  <span>{{ item.proportion ? item.proportion : '--' }}{{ item.proportion ? '%' : '' }}</span>
                </div>
              </div>
            </div>
            <div class="color-lists">
              <span>漂浮物：</span>
              <span>{{ alarmInfo.smartSnap && alarmInfo.smartSnap.floating ? alarmInfo.smartSnap.floating : '--' }}</span>
            </div>
          </div>
        </div>
        <div class="alarm-btn" @click="handleCancelAlarm">
          <span>关闭</span><span>({{ alarmTime }}s)</span>
        </div>
      </div>
      <div v-else class="content">
        <div class="drain-time">
          <div class="drain-list">
            <span>告警时间：</span>
            <span>{{ alarmInfo.alarmTime ? alarmInfo.alarmTime.substr(5, 11) : '--' }}</span>
          </div>
          <div class="drain-list">
            <span>告警类型：</span>
            <span>{{ alarmInfo.alarmTypeStr ? alarmInfo.alarmTypeStr : '--' }}</span>
          </div>
        </div>
        <div class="drain-title">智能分析：</div>
        <div class="drain-device">
          <div class="device-list">
            <span>设备状态</span>
            <img :src="alarmInfo.deviceStatus ? gou : cha" alt="" />
          </div>
          <div class="device-list">
            <span>样品抽取</span>
            <img :src="alarmInfo.isSample ? gou : cha" alt="" />
          </div>
          <div class="device-list">
            <span>是否排水</span>
            <img :src="alarmInfo.isDrain ? gou : cha" alt="" />
          </div>
        </div>
        <div class="drain-title"><span>异变量：</span><span v-if="alarmInfo.drainAlarmList && !alarmInfo.drainAlarmList.length">--</span></div>
        <div class="monitor-table" v-if="alarmInfo.drainAlarmList && alarmInfo.drainAlarmList.length">
          <div class="monitor-list">
            <span>异变量</span>
            <span>监测值</span>
            <span>阈值</span>
            <span>超标比例</span>
            <span>变化趋势</span>
          </div>
          <div class="monitor-list" v-for="(item, index) in alarmInfo.drainAlarmList" :key="index">
            <span>{{ item.alarmParamTypeStr }}</span>
            <span
              >{{ item.alarmValue }}<span>{{ item.unit }}</span></span
            >
            <span
              >{{ item.alarmThreshold }}<span>{{ item.unit }}</span></span
            >
            <span>{{ item.rate ? item.rate : '--' }}{{ item.rate ? '%' : '' }}</span>
            <span>{{ item.trend }}</span>
          </div>
          <div class="lines">
            <div>
              <img src="../../assets/<EMAIL>" alt="" class="line" />
            </div>
            <div>
              <img src="../../assets/<EMAIL>" alt="" class="line" />
            </div>
            <div>
              <img src="../../assets/<EMAIL>" alt="" class="line" />
            </div>
            <div>
              <img src="../../assets/<EMAIL>" alt="" class="line" />
            </div>
          </div>
        </div>
        <div class="relation">
          <div class="relation-title">关联执行：</div>
          <div class="realtion-area">
            <div class="relation-list" v-if="!(alarmInfo.drainAlarmList && alarmInfo.drainAlarmList.length)">
              <span>持续监测</span>
              <img src="../../assets/outlet/<EMAIL>" alt="" />
            </div>
            <div class="relation-list" v-if="alarmInfo.drainAlarmList && alarmInfo.drainAlarmList.length">
              <span>留样核验</span>
              <img src="../../assets/outlet/<EMAIL>" alt="" />
            </div>
            <div class="relation-list" v-if="alarmInfo.drainAlarmList && alarmInfo.drainAlarmList.length">
              <span>启动追溯</span>
              <img src="../../assets/outlet/<EMAIL>" alt="" />
            </div>
            <div class="relation-list"></div>
            <div class="relation-list" v-if="!(alarmInfo.drainAlarmList && alarmInfo.drainAlarmList.length)"></div>
          </div>
        </div>
        <div class="alarm-btn" @click="handleCancelAlarm">
          <span>关闭</span><span>({{ alarmTime }}s)</span>
        </div>
      </div>
      <div slot="footer" class="footer" />
      <!--      <img-->
      <!--        src="../../assets/guanbi.png"-->
      <!--        alt=""-->
      <!--        class="close"-->
      <!--        @click="handleCancelAlarm"-->
      <!--      />-->
    </a-modal>
    <MonitorDetail
      v-if="dialogVisible"
      :monitor-url="monitorUrl"
      :visibles="dialogVisible"
      @cancel="dialogVisible = false"
      :data="formData"
      :monitor-loading="monitorLoading"
    />
    <DrainDetailComp v-if="drainDialogVisible" :visibles="drainDialogVisible" @cancel="drainDialogVisible = false" :data="currentDrain" />
  </section>
</template>

<script lang="ts">
import MonitorDetail from './monitorDetail/MonitorDetail.vue'
import DrainDetailComp from './drainDetail/DrainDetail.vue'

interface LineChartData {
  bottomList: string[]
  dataList: string[]
  unit?: string
  warnValue?: number
  miniValue?: number
  maxValue?: number
  valueType?: null | number
}

interface WaterChageData {
  bottomList: string[]
  currentYearName: string
  currentYearDataList: string[]
  beforeOneYearName: string
  beforeOneYearDataList: string[]
  unit?: string
}

interface EnterPriseInformation {
  name: string
  district: string
  type: string
  address: string
  status: string
  kind: string
  principal: string
  principalTel: string
  licence: string
  emergencyPlan: string
}

interface RankList {
  rank: number
  type: string
  release: string
}
interface lineData {
  dataList: []
  unit: string
  bottomList: []
  isAlarm: []
  name: ''
}

interface MapCenter {
  lng: number
  lat: number
}
//@ts-ignore
import LivePlayer from '@liveqing/liveplayer'
import { Component, Vue, Watch } from 'vue-property-decorator'
import WaterMap from '@/components/GaoDeMap/waterMap.vue'
import LineChartDashed from '@/components/Charts/LineChartDashed.vue'
import LineAndBarChart from '@/components/Charts/LineAndBarChart.vue'
import LineAndBarChartOne from '@/components/Charts/LineAndBarChartOne.vue'
import LinessChartDasheds from '@/components/Charts/LinessChartDasheds.vue'
import { Table, Radio, Select, Icon, Modal } from 'ant-design-vue'
import { socketUrl3 } from '../../utils'
import { Swiper, SwiperSlide } from 'vue-awesome-swiper'
import 'video.js/dist/video-js.css'
import 'videojs-contrib-hls'
import 'videojs-flash'
import player from '@/components/jessibucaPlayer/jessibuca.vue'
import moment from 'moment'
import xia from '@/assets/fengxiang/xia.png'
import shang from '@/assets/fengxiang/shang.png'
import {
  fetchStationWithRecord,
  getTodayMonitorItemRecord,
  fetchRecentAlarm,
  fetchHeavilyPollutingEnterpriseList, // deprecated
  getHeavilyPollutingEnterpriseList,
  getStationList,
  fetchCameraList,
  fetchCameraUrl,
  getMonitorItemDetailRecord,
  getAllStationRealTimeRecordList,
  getPunch,
  getlatestRecord,
  getCameraList,
  getDrainList,
  getDrainDetail,
  getDrainPerson,
  getDrainPersonDetail,
  getAssesThisYear,
} from '@/api/water'
@Component({
  name: 'WaterQuality',
  components: {
    MonitorDetail,
    DrainDetailComp,
    WaterMap,
    LineChartDashed,
    LineAndBarChart,
    ATable: Table,
    ARadioGroup: Radio.Group,
    ARadioButton: Radio.Button,
    ASelect: Select,
    AIcon: Icon,
    ASelectOption: Select.Option,
    Swiper,
    SwiperSlide,
    LineAndBarChartOne,
    AModal: Modal,
    LinessChartDasheds,
    LivePlayer,
    player,
  },
})
export default class extends Vue {
  // 空气质量对比
  // @Watch("airQualityType", { immediate: true, deep: true })
  // public onAirQualityType(newValue: any, oldValue: any) {
  //   if (newValue) {
  //     this.getYearOnYear();
  //   }
  // }
  @Watch('alarmInfo', { immediate: true, deep: true })
  public onAlarmInfo(newValue: any, oldValue: any) {
    if (newValue) {
      this.handleCancelAlarm()
      this.isAlarm = true
      if (this.isShowDrain) {
        this.initTime()
      }
    }
  }
  @Watch('searchWords', { immediate: true, deep: true })
  public onsearchWords(newValue: any, oldValue: any) {
    if (newValue && !this.isClick) {
      if (this.isShowWater) {
        this.getStationLists()
      } else if (this.isShowDrain) {
        this.getDrainLists()
      } else if (this.isShowWaterSourceMonitor || this.isShowDrainMonitor) {
        this.getCameraLists()
      }
    } else {
      this.isClick = false
      this.list = []
    }
  }
  private xia = xia
  private shang = shang
  private UpdateC: any = {}
  private ue: string = ''
  private pd: string = ''
  private currentSelectedStationMarker: any = null
  private currentSelectedStationObj: any = {
    target: {
      w: {
        data: {},
      },
    },
  }
  private type: any = 1
  private pollutionType: any = 0
  private isClick: any = false
  private searchWords: any = ''
  private list: any = []
  private swiperOptions = {
    slidesPerView: 4,
    slidesPerGroup: 1,
    loop: false,
    loopFillGroupWithBlank: true,
    pagination: {
      el: '.swiper-pagination',
      clickable: true,
    },
    on: {
      click: function (e: any) {
        if ((window as any)._this) {
          // @ts-ignore
          ;(window as any)._this.pollutionType = Number(e.target.getAttribute('data-index'))
        }
      },
    },
    autoplay: false,
    navigation: {
      nextEl: '.right-arrow',
      prevEl: '.left-arrow',
    },
  }
  private videoUrl: any = ''
  private defaultDrainCamera: any = ''
  private gou: any = require('../../assets/outlet/<EMAIL>')
  private cha: any = require('../../assets/outlet/<EMAIL>')
  // 弹框
  private visible = false
  private visible1 = false
  private airQualityType = '同比'
  private mapStyle = 'amap://styles/0739a6632d849ae1b5e6f1c52f7d12c3'
  // 重污染企业
  private heavyPollution: Array<any> = []
  // 实时监测预警
  private monitor = []
  // 此时水质监测数据项
  private waterMonitorCuttentValue = ''
  private isAlarm = false
  // swiper配置项
  private swiperOption = {
    direction: 'vertical',
    slidesPerView: 3,
    slidesPerGroup: 1,
    loop: true,
    autoplay: {
      delay: 2000,
      disableOnInteraction: false,
    },
  }
  private waterTable: any = [
    {
      subordinate: '府河',
      name: '高桥',
      target: 'III',
      year1: 'II',
      year2: 'II',
    },
    {
      subordinate: '沙河',
      name: '泰宏桥',
      target: 'IV',
      year1: 'II',
      year2: 'IV',
    },
    {
      subordinate: '东风渠',
      name: '大湾桥',
      target: 'III',
      year1: 'II',
      year2: 'II',
    },
  ]
  // 重污染企业swiper配置项
  private swiperOptionPollution = {
    effect: 'flip',
    grabCursor: true,
    slidesPerView: 1,
    slidesPerGroup: 1,
    loop: true,
    autoplay: {
      delay: 30 * 1000,
      disableOnInteraction: false,
    },
  }
  // 水质实时监测button列表数据
  private waterMonitorButtonList: Array<any> = []
  // 水质实时监测Chart数据
  private waterMonitor: LineChartData = {
    bottomList: [],
    dataList: [],
    unit: 'mg/L',
    valueType: null,
  }
  // 水质变化Chart数据
  private waterchange: any = {
    bottomList: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'],
    currentYearName: '',
    currentYearDataList: [],
    beforeOneYearName: '',
    beforeOneYearDataList: [],
    unit: '',
    sameRatioName: '同比率',
    sameRatio: [],
    unit1: '同比率(%)',
  }
  // 重污染企业排放量
  private sewageDischarge: LineChartData = {
    bottomList: [],
    dataList: [],
  }
  // 水质监控站点数据
  private todayMonitorItemRecord: Array<any> = []
  // 水质监测站点
  private WaterStations: Array<any> = []
  // 地图展示用水质监测站点
  private mapWaterStations: Array<any> = []
  // 水质监控
  private playerOptions: any = {
    //playbackRates: [0.7, 1.0, 1.5, 2.0], //播放速度
    autoplay: true, //如果true,浏览器准备好时开始回放。
    muted: true, // 默认情况下将会消除任何音频。
    loop: false, // 导致视频一结束就重新开始。
    preload: 'auto', // 建议浏览器在<video>加载元素后是否应该开始下载视频数据。auto浏览器选择最佳行为,立即开始加载视频（如果浏览器支持）
    language: 'zh-CN',
    languages: {
      'zh-CN': {
        Play: '播放',
        Pause: '暂停',
        'Current Time': '当前时间',
        Duration: '时长',
        'Remaining Time': '剩余时间',
        'Stream Type': '媒体流类型',
        LIVE: '直播',
        Loaded: '加载完毕',
        Progress: '进度',
        Fullscreen: '全屏',
        'Non-Fullscreen': '退出全屏',
        Mute: '静音',
        Unmute: '取消静音',
        'Playback Rate': '播放速度',
        Subtitles: '字幕',
        'subtitles off': '关闭字幕',
        Captions: '内嵌字幕',
        'captions off': '关闭内嵌字幕',
        Chapters: '节目段落',
        'Close Modal Dialog': '关闭弹窗',
        Descriptions: '描述',
        'descriptions off': '关闭描述',
        'Audio Track': '音轨',
        'You aborted the media playback': '视频播放被终止',
        'A network error caused the media download to fail part-way.': '网络错误导致视频下载中途失败。',
        'The media could not be loaded, either because the server or network failed or because the format is not supported.':
          '视频因格式不支持或者服务器或网络的问题无法加载。',
        'The media playback was aborted due to a corruption problem or because the media used features your browser did not support.':
          '由于视频文件损坏或是该视频使用了你的浏览器不支持的功能，播放终止。',
        'No compatible source was found for this media.': '无法找到此视频兼容的源。',
        'The media is encrypted and we do not have the keys to decrypt it.': '视频已加密，无法解密。',
        'Play Video': '播放视频',
        Close: '关闭',
        'Modal Window': '弹窗',
        'This is a modal window': '这是一个弹窗',
        'This modal can be closed by pressing the Escape key or activating the close button.': '可以按ESC按键或启用关闭按钮来关闭此弹窗。',
        ', opens captions settings dialog': ', 开启标题设置弹窗',
        ', opens subtitles settings dialog': ', 开启字幕设置弹窗',
        ', opens descriptions settings dialog': ', 开启描述设置弹窗',
        ', selected': ', 选择',
        'captions settings': '字幕设定',
        'Audio Player': '音频播放器',
        'Video Player': '视频播放器',
        Replay: '重播',
        'Progress Bar': '进度小节',
        'Volume Level': '音量',
        'subtitles settings': '字幕设定',
        'descriptions settings': '描述设定',
        Text: '文字',
        White: '白',
        Black: '黑',
        Red: '红',
        Green: '绿',
        Blue: '蓝',
        Yellow: '黄',
        Magenta: '紫红',
        Cyan: '青',
        Background: '背景',
        Window: '视窗',
        Transparent: '透明',
        'Semi-Transparent': '半透明',
        Opaque: '不透明',
        'Font Size': '字体尺寸',
        'Text Edge Style': '字体边缘样式',
        None: '无',
        Raised: '浮雕',
        Depressed: '压低',
        Uniform: '均匀',
        Dropshadow: '下阴影',
        'Font Family': '字体库',
        'Proportional Sans-Serif': '比例无细体',
        'Monospace Sans-Serif': '单间隔无细体',
        'Proportional Serif': '比例细体',
        'Monospace Serif': '单间隔细体',
        Casual: '舒适',
        Script: '手写体',
        'Small Caps': '小型大写字体',
        Reset: '重启',
        'restore all settings to the default values': '恢复全部设定至预设值',
        Done: '完成',
        'Caption Settings Dialog': '字幕设定视窗',
        'Beginning of dialog window. Escape will cancel and close the window.': '开始对话视窗。离开会取消及关闭视窗',
        'End of dialog window.': '结束对话视窗',
      },
    },
    aspectRatio: '448:196', // 将播放器置于流畅模式，并在计算播放器的动态大小时使用该值。值应该代表一个比例 - 用冒号分隔的两个数字（例如"16:9"或"4:3"）
    fluid: true, // 当true时，Video.js player将拥有流体大小。换句话说，它将按比例缩放以适应其容器。
    //application/x-mpegURL-m3u8 video/mp4-mp4 rtmp/mp4-rtmp flv-application/octet-stream-flv
    sources: [
      // {
      //   type: "application/x-mpegURL",
      //   src:
      //     "http://60.255.36.154:10000/sms/34020000002020000001/hls/51010600001320000005_51010600001320000001/51010600001320000005_51010600001320000001_live.m3u8"
      // }
    ],
    // poster: "http://60.255.36.154:10000/snap/51010600001320000005/51010600001320000001.jpg?t=1586164395847422586", //你的封面地址
    poster: '',
    width: document.documentElement.clientWidth,
    notSupportedMessage: '该站点暂未设置监控，请查看其它站点', //允许覆盖Video.js无法播放媒体源时显示的默认信息。
    // controls: false,
    // controlBar: {
    //   timeDivider: false,
    //   durationDisplay: false,
    //   remainingTimeDisplay: false,
    //   fullscreenToggle: false, //全屏按钮
    // },
  }
  // 水质监测轮播timer
  private monitorTimer: any
  // 重污染企业轮播timer
  private switchEnterpriseTimerStore: any
  // 重污染企业Chart-timer
  private heavilyPollutingEnterpriseTimer: any
  // 水质监测轮播count
  private monitorTimerCount = 0
  // 水质站点列表
  private waterStationList: Array<any> = []
  // 水质站点详情默认选中站点
  private defaultStationDetailValue = ''
  private defaultDrainStationDetailValue = ''
  // 默认选中水质站点
  private defaultStationValue = ''
  // 选中的水质站点地址
  private currentStationAddress = ''
  // 站点摄像头列表
  private currentCameraList: Array<any> = []
  // 默认选中摄像头
  private defaultCameraValue = ''
  // 重污企业排放排行
  private enterpriseRankList: Array<RankList> = [
    {
      rank: 2,
      type: '污水排放',
      release: '128吨',
    },
    {
      rank: 4,
      type: '废气排放',
      release: '320m^3',
    },
    {
      rank: 3,
      type: '废料排放',
      release: '5吨',
    },
  ]
  private currYearName: number = new Date().getFullYear()
  private currMonthName: number = new Date().getMonth()
  // 排口监测数据
  private lineData: lineData = {
    dataList: [],
    unit: '',
    bottomList: [],
    isAlarm: [],
    name: '',
  }
  // 重污染企业select展示
  private heavyPollutionEnterpriseList: Array<any> = []
  // 重污染企业store
  private heavyPollutionEnterpriseStrore: Array<any> = []
  // 默认选中重污染企业
  private defaultEnterpriseValue = ''
  // 当前展示的重污染企业信息
  private currentEnterpriseInfor: EnterPriseInformation = {
    name: '',
    district: '',
    type: '',
    address: '',
    status: '',
    kind: '',
    principal: '',
    principalTel: '',
    licence: '',
    emergencyPlan: '',
  }
  private airData: any = []
  private isAirShow = false
  // 折叠图表数据面板
  private isFold = false
  // 地图视图中心点
  private mapViewCenter: MapCenter = {
    lng: 104.08,
    lat: 30.725,
  }
  private patrolDetail: any = {}
  // 地图缩放大小
  private mapViewZoom = 13.3
  private mapWaterStationsVideo: any[] = []
  private fetchTimer: any = null

  // 计算属性：水源地监控设备 (type: 1,2)
  get waterSourceMonitorDevices() {
    return this.mapWaterStationsVideo.filter((item) => item.type === 1 || item.type === 2)
  }

  // 水源地监控设备离线数
  get waterSourceMonitorOfflineCount() {
    return this.mapWaterStationsVideo.filter((item) => (item.type === 1 || item.type === 2) && !item.online)
  }

  // 计算属性：入河排口监控设备 (type: 3,4,5)
  get drainMonitorDevices() {
    return this.mapWaterStationsVideo.filter((item) => item.type === 3 || item.type === 4 || item.type === 5)
  }

  // 入河排口监控设备离线数
  get drainMonitorOfflineCount() {
    return this.mapWaterStationsVideo.filter((item) => (item.type === 3 || item.type === 4 || item.type === 5) && !item.online)
  }

  mounted() {
    ;(window as any)._this = this
    ;(window as any).pollutionType = 0
    this.ue = localStorage.getItem('ue') || ''
    this.pd = localStorage.getItem('pd') || ''
    this.pollutionType = (window as any).pollutionType
    // this.$bus.emit("testBus", "test");
    // this.fetchStationWithRecord(); //deprecated
    // this.getTodayMonitorItemRecord(); //deprecated
    this.getCameraList()
    this.getAllStationRealTimeRecordList()
    this.fetchTimer = setInterval(
      () => {
        this.getAllStationRealTimeRecordList()
      },
      10 * 60 * 1000
    )
    // this.fetchRecentAlarm();
    // this.fetchHeavilyPollutingEnterpriseList(); //deprecated
    this.getHeavilyPollutingEnterpriseList()
    this.getStationList()
    this.getAssesThisYear()
    this.connect()
    this.initSocketTime()
    this.getDrainList()
    this.getDrainPerson()
  }
  private changeSelect(params: any) {
    if (params == 1) {
      this.isShowWater = !this.isShowWater
      this.isDrainFirst = true
      if (this.isShowWater) {
        const item = this.mapWaterStations.find((item) => item.stationId == this.defaultStationDetailValue)
        this.currentSelectedStationObj.target.w.data = item || {}
        this.switchTimer(true)
      } else {
        this.currentSelectedStationObj = {
          target: {
            w: {
              data: {},
            },
          },
        }
        if (this.monitorTimer) {
          clearInterval(this.monitorTimer)
        }
      }
    } else if (params == 2) {
      this.isShowDrain = !this.isShowDrain
      if (!this.isShowDrain) {
        this.isShowKind = true
      }
    } else if (params == 3) {
      this.isShowWaterSourceMonitor = !this.isShowWaterSourceMonitor
    } else if (params == 4) {
      this.isShowDrainMonitor = !this.isShowDrainMonitor
    }
  }
  private socket: any
  private alarmTimer: any = null
  private alarmTime: number = 10
  private initTime() {
    this.alarmTime = 10
    this.visible1 = true
    this.alarmTimer = setInterval(() => {
      if (this.alarmTime > 1) {
        this.alarmTime = this.alarmTime - 1
      } else {
        clearInterval(this.alarmTimer)
        this.visible1 = false
      }
    }, 1000)
  }
  private socketTimer: any = null
  private initSocketTime() {
    this.socketTimer = setInterval(() => {
      if (this.socket) {
        this.socket.send(JSON.stringify({ code: 0 }))
      }
    }, 20000)
  }
  private connect() {
    this.socket = new WebSocket(socketUrl3())
    // 监听socket连接
    this.socket.onopen = this.open
    // 监听socket错误信息
    this.socket.onerror = this.error
    // 监听socket消息
    this.socket.onmessage = this.getMessage
    // 关闭消息
    // this.socket.onclose = this.close;
    window.onbeforeunload = () => {
      this.socket.onopen = () => {}
      this.socket.onerror = () => {}
      this.socket.onmessage = () => {}
      this.socket.close()
    }
  }
  private open() {
    this.send()
  }
  private userId: any = ''
  private send() {
    const UserInfor = localStorage.getItem('UserInfor')
    this.userId = UserInfor ? JSON.parse(UserInfor).user.userId : undefined
    this.socket.send(JSON.stringify({ code: 1, token: this.userId }))
  }
  private error() {}
  private alarmInfo: any = null
  private getMessage(msg: any) {
    const data = JSON.parse(msg.data)
    if (data.code === 51) {
      this.alarmInfo = data.drainAlarm
    }
  }
  private drainListData: any = []
  private drainListLength: any = 0
  private isFirst: any = true
  // 获取排口站点列表
  private getDrainList() {
    getDrainList(this.searchWords).then((res: any) => {
      this.drainListData = res.data.data || []
      if (this.isFirst) {
        this.drainListLength = this.drainListData.length
        this.isFirst = false
      }
      this.defaultDrainStationDetailValue = this.drainListData.length ? this.drainListData[0].waterDrainId : ''
      if (!this.drainListData.length) {
        this.drainDetail = {}
        this.pollutionType = 0
        this.type = 1
        this.defaultDrainCamera = ''
      }
    })
  }
  private changeType(index: any) {
    this.pollutionType = index
  }
  private drainPersonList: any = []
  // 获取排户列表
  private getDrainPerson() {
    getDrainPerson(this.searchWords).then((res: any) => {
      this.drainPersonList = res.data.data || []
    })
  }
  private isShowDrain: any = true
  private isDrainFirst: any = false
  private isShowWater: any = true
  private isShowMonitor: any = true // 控制监控摄像头显示
  private isShowWaterSourceMonitor: any = true // 控制水源地监控显示 (type: 1,2)
  private isShowDrainMonitor: any = true // 控制入河排口监控显示 (type: 3,4,5)
  private isShowKind: any = true
  private isDrainPerson: any = false
  private options: any = {
    direction: 'vertical',
    slidesPerView: 3,
    slidesPerGroup: 1,
    loop: true,
    autoplay: {
      delay: 3000,
      disableOnInteraction: false,
    },
  }
  private optionss: any = {
    direction: 'vertical',
  }
  private dataNumber: any = ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十']
  // 排口站点点击
  private drainSelect(args: any) {
    this.defaultDrainStationDetailValue = args.waterDrainId
    this.isDrainFirst = true
    this.isShowKind = false
    this.getDrainDetail()
    this.isDrainPerson = false
  }
  private drainPersonDetail: any = {}
  private drainPersonSelect(args: any) {
    this.isDrainFirst = true
    this.isDrainPerson = true
    this.isShowKind = false
    getDrainPersonDetail(args).then((res) => {
      this.drainPersonDetail = res.data.data || {}
    })
  }
  private airVideoSelect() {
    this.isAirShow = true
  }
  private toVideoDetail() {
    // this.$router.push('/waterRealVideo')
    this.$router.push('/waterOutlet')
  }
  private waterAssesData: any = []
  private getAssesThisYear() {
    getAssesThisYear().then((res: any) => {
      this.waterAssesData = res.data.data.list || []
      const { endDate, startDate } = res.data.data
      const sDate = moment(startDate).format('LL')
      const eDate = moment(endDate).format('LL')
      const iS = +sDate.indexOf('月') + 1
      const iE = +eDate.indexOf('月') + 1
      this.UpdateC = {
        startDate: sDate.slice(0, iS),
        endDate: eDate.slice(0, iE),
      }
      // this.UpdateC ={startDate:moment(startDate).format('LL'),endDate:moment(endDate).format('LL')}
    })
  }

  beforeDestroy() {
    clearInterval(this.monitorTimer)
    clearInterval(this.switchEnterpriseTimerStore)
    clearInterval(this.fetchTimer)
    clearInterval(this.alarmTimer)
    clearInterval(this.socketTimer)
    this.socket = null
    this.isAlarm = false
    // @ts-ignore
    this.$refs.livePlayer?.dispose()
  }
  private getMore() {
    this.visible = true
  }
  // 获取水质摄像头
  getCameraList() {
    getCameraList().then((res) => {
      this.mapWaterStationsVideo = res.data.data
    })
  }
  // 获取水质监测站点数据 deprecated
  private fetchStationWithRecord(): void {
    fetchStationWithRecord()
      .then((res) => {
        if (res.data.data) {
          this.WaterStations = res.data.data
        }
      })
      .catch((err) => {})
  }

  // 水质实时监测数据 deprecated
  private getTodayMonitorItemRecord(): void {
    getTodayMonitorItemRecord()
      .then((res) => {
        if (res.data.data && res.data.data.length > 0) {
          this.todayMonitorItemRecord = res.data.data
          this.waterMonitorButtonList = this.todayMonitorItemRecord.map((record: any) => {
            return {
              id: record.itemId + '',
              name: record.name,
              unit: record.concentrationUnit,
            }
          })
          const timeChartsData = this.todayMonitorItemRecord[0].clockAvgList.sort((a: any, b: any): number => {
            return a.clock - b.clock
          })
          this.waterMonitor.bottomList = []
          this.waterMonitor.dataList = []
          this.waterMonitor.unit = this.todayMonitorItemRecord[0].concentrationUnit
          this.waterMonitor.valueType = this.todayMonitorItemRecord[0].valueType
          timeChartsData.forEach((clock: any) => {
            this.waterMonitor.bottomList.push(`${clock.clock}时`)
            this.waterMonitor.dataList.push(clock.avg)
          })
          this.switchTimer(true)
        }
      })
      .catch((err) => {})
  }
  private handleCancel() {
    this.visible = false
  }
  private handleCancelAlarm() {
    this.visible1 = false
    clearInterval(this.alarmTimer)
  }
  // 获取金牛区所有站点和对应列表
  private getAllStationRealTimeRecordList() {
    getAllStationRealTimeRecordList()
      .then((res) => {
        if (res.data.data) {
          // 监控marker
          // this.mapWaterStationsVideo = res.data.data.map((list: any) => {
          //   const retMap = list.station;
          //   list.realtimeMonitorRecords.forEach((item: any) => {
          //     switch (item.itemCode) {
          //       case "003":
          //         retMap["cod"] = item.value;
          //         retMap["codAlarm"] = item.alarmStatus === 1;
          //         break;
          //       case "002":
          //         retMap["nh3n"] = item.value;
          //         retMap["nh3nAlarm"] = item.alarmStatus === 1;
          //         break;
          //       case "004":
          //         retMap["totalPhosphorus"] = item.value;
          //         retMap["totalPhosphorusAlarm"] = item.alarmStatus === 1;
          //         break;
          //       case "007":
          //         retMap["ph"] = item.value;
          //         retMap["phAlarm"] = item.alarmStatus === 1;
          //         break;
          //     }
          //   });
          //   retMap.time =
          //     list.realtimeMonitorRecords.length === 0
          //       ? ""
          //       : list.realtimeMonitorRecords[0].time
          //       ? list.realtimeMonitorRecords[0].time
          //       : "";
          //   return retMap;
          // });
          // 水质站点
          this.mapWaterStations = res.data.data.map((list: any) => {
            const retMap = list.station
            list.realtimeMonitorRecords.forEach((item: any) => {
              switch (item.itemCode) {
                case '003':
                  retMap['cod'] = item.value
                  retMap['codAlarm'] = item.alarmStatus === 1
                  break
                case '002':
                  retMap['nh3n'] = item.value
                  retMap['nh3nAlarm'] = item.alarmStatus === 1
                  break
                case '004':
                  retMap['totalPhosphorus'] = item.value
                  retMap['totalPhosphorusAlarm'] = item.alarmStatus === 1
                  break
                case '007':
                  retMap['ph'] = item.value
                  retMap['phAlarm'] = item.alarmStatus === 1
                  break
              }
            })
            retMap.time = list.realtimeMonitorRecords.length === 0 ? '' : list.realtimeMonitorRecords[0].time ? list.realtimeMonitorRecords[0].time : ''
            return retMap
          })
          this.currentSelectedStationMarker = this.mapWaterStations[0].stationId
          this.currentSelectedStationObj.target.w.data = this.mapWaterStations[0]
        }
      })
      .catch((err) => {})
  }

  // 获取最新监测报警
  private fetchRecentAlarm(): void {
    fetchRecentAlarm()
      .then((res) => {
        if (res.data.data) {
          this.monitor = res.data.data.map((alarm: any): Record<string, string> => {
            return {
              keyword: alarm.keyWord,
              stationName: alarm.stationName,
              time: alarm.waterAlarmTime,
            }
          })
        }
      })
      .catch((err) => {})
  }

  // 水质实时监测数据btn选择
  private waterMonitorSelectChange(e: any): void {
    let currentSelect!: any
    this.waterMonitorButtonList.forEach((record: any, index: number) => {
      if (record.id === e.target.value) {
        currentSelect = record
        if (!e.target.mechanical) {
          this.monitorTimerCount = index
        }
      }
    })
    this.waterMonitor.unit = currentSelect.unit
    this.getMonitorItemDetailRecord(this.defaultStationDetailValue, e.target.value)
  }

  // 获取重污染企业信息 deprecated
  private fetchHeavilyPollutingEnterpriseList(): void {
    fetchHeavilyPollutingEnterpriseList()
      .then((res) => {
        if (res.data.data && res.data.data.length > 0) {
          this.heavyPollutionEnterpriseStrore = res.data.data
          this.heavyPollution = res.data.data.map((enterprise: any) => {
            return {
              name: enterprise.heavilyPollutingEnterpriseName,
              district: enterprise.administrativeDivision,
              type: enterprise.heavilyPollutingEnterpriseType,
              address: enterprise.heavilyPollutingEnterpriseAddress,
              status: enterprise.heavilyPollutingEnterpriseStatus,
              kind: enterprise.industryCategory,
              principal: enterprise.principal,
              principalTel: enterprise.principalPhoneNumber,
              licence: enterprise.haveSewageDischargePermission ? '拥有' : '无',
              emergencyPlan: enterprise.haveEnvironmentalEmergencyPlan,
            }
          })
          this.heavyPollutionEnterpriseList = res.data.data.map((enterprise: any) => {
            return {
              name: enterprise.heavilyPollutingEnterpriseName,
              id: enterprise.heavilyPollutingEnterpriseId,
              lng: enterprise.lng,
              lat: enterprise.lat,
            }
          })
          this.switchEnterpriseTimer()
        }
      })
      .catch((err) => {})
  }

  // 获取重污染企业列表信息
  private getHeavilyPollutingEnterpriseList(): void {
    getHeavilyPollutingEnterpriseList().then((res) => {
      if (res.data.data && res.data.data.length > 0) {
        this.heavyPollutionEnterpriseStrore = res.data.data
        const enterprise = res.data.data[0]
        this.currentEnterpriseInfor = {
          name: enterprise.heavilyPollutingEnterpriseName,
          district: enterprise.administrativeDivision,
          type: enterprise.heavilyPollutingEnterpriseType,
          address: enterprise.heavilyPollutingEnterpriseAddress,
          status: enterprise.heavilyPollutingEnterpriseStatus,
          kind: enterprise.industryCategory,
          principal: enterprise.principal,
          principalTel: enterprise.principalPhoneNumber,
          licence: enterprise.haveSewageDischargePermission ? '拥有' : '无',
          emergencyPlan: enterprise.haveEnvironmentalEmergencyPlan,
        }
        this.enterpriseRankList = []
        this.enterpriseRankList.push({
          rank: enterprise.sewageDischargeRanking,
          type: '污水排放',
          release: enterprise.sewageDischarge > 0 ? `${enterprise.sewageDischarge}吨` : '-',
        })
        this.enterpriseRankList.push({
          rank: enterprise.exhaustEmissionsRanking,
          type: '废气排放',
          release: enterprise.exhaustEmissions > 0 ? `${enterprise.exhaustEmissions}立方米` : '-',
        })
        this.enterpriseRankList.push({
          rank: enterprise.wasteDischargeRanking,
          type: '废料排放',
          release: enterprise.wasteDischarge > 0 ? `${enterprise.wasteDischarge}吨` : '-',
        })
        this.defaultEnterpriseValue = res.data.data[0].heavilyPollutingEnterpriseId
        this.heavyPollutionEnterpriseList = res.data.data.map((enterprise: any) => {
          return {
            name: enterprise.heavilyPollutingEnterpriseNickname,
            value: enterprise.heavilyPollutingEnterpriseId,
            lng: enterprise.lng,
            lat: enterprise.lat,
          }
        })
        this.switchEnterpriseSelectTimer()
      }
    })
  }

  // select切换重污染企业项
  private heavilyPollutingEnterpriseChage(value: any): void {
    let currentSelect: any
    this.defaultEnterpriseValue = value
    for (const enterprise of this.heavyPollutionEnterpriseStrore) {
      if (enterprise.heavilyPollutingEnterpriseId === value) {
        currentSelect = enterprise
        break
      }
    }
    this.currentEnterpriseInfor = {
      name: currentSelect.heavilyPollutingEnterpriseName,
      district: currentSelect.administrativeDivision,
      type: currentSelect.heavilyPollutingEnterpriseType,
      address: currentSelect.heavilyPollutingEnterpriseAddress,
      status: currentSelect.heavilyPollutingEnterpriseStatus,
      kind: currentSelect.industryCategory,
      principal: currentSelect.principal,
      principalTel: currentSelect.principalPhoneNumber,
      licence: currentSelect.haveSewageDischargePermission ? '拥有' : '无',
      emergencyPlan: currentSelect.haveEnvironmentalEmergencyPlan,
    }
    this.enterpriseRankList = []
    this.enterpriseRankList.push({
      rank: currentSelect.sewageDischargeRanking,
      type: '污水排放',
      release: currentSelect.sewageDischarge > 0 ? `${currentSelect.sewageDischarge}吨` : '-',
    })
    this.enterpriseRankList.push({
      rank: currentSelect.exhaustEmissionsRanking,
      type: '废气排放',
      release: currentSelect.exhaustEmissions > 0 ? `${currentSelect.exhaustEmissions}立方米` : '-',
    })
    this.enterpriseRankList.push({
      rank: currentSelect.wasteDischargeRanking,
      type: '废料排放',
      release: currentSelect.wasteDischarge > 0 ? `${currentSelect.wasteDischarge}吨` : '-',
    })
  }

  // 重污染企业Select轮播处理
  private switchEnterpriseSelectTimer(): void {
    const total = this.heavyPollutionEnterpriseStrore.length
    let count = 1
    if (this.switchEnterpriseTimerStore) {
      clearInterval(this.switchEnterpriseTimerStore)
    }
    this.switchEnterpriseTimerStore = setInterval(() => {
      this.defaultEnterpriseValue = this.heavyPollutionEnterpriseStrore[count].heavilyPollutingEnterpriseId
      count < total - 1 ? count++ : (count = 0)
      this.heavilyPollutingEnterpriseChage(this.defaultEnterpriseValue)
    }, 30 * 1000)
  }

  // 重污染企业Chart轮播处理
  private switchEnterpriseTimer(): void {
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const _this = this
    const total = Math.ceil(this.heavyPollutionEnterpriseStrore.length && this.heavyPollutionEnterpriseStrore.length / 5)
    let count = 0
    if (this.heavilyPollutingEnterpriseTimer) {
      clearInterval(this.heavilyPollutingEnterpriseTimer)
    }
    // 立即执行一次
    if (this.sewageDischarge.bottomList.length === 0) {
      // 初始展示五家企业
      this.heavyPollutionEnterpriseStrore.slice(0, 5).forEach((enterprise: any) => {
        this.sewageDischarge.bottomList.push(enterprise.heavilyPollutingEnterpriseName)
        this.sewageDischarge.dataList.push(enterprise.sewageDischarge ? enterprise.sewageDischarge + '' : '0')
      })
      count++
    }
    this.heavilyPollutingEnterpriseTimer = setInterval(() => {
      _this.sewageDischarge.bottomList = []
      _this.sewageDischarge.dataList = []
      _this.heavyPollutionEnterpriseStrore.slice(count * 5, (count + 1) * 5).forEach((enterprise: any) => {
        _this.sewageDischarge.bottomList.push(enterprise.heavilyPollutingEnterpriseName)
        _this.sewageDischarge.dataList.push(enterprise.sewageDischarge ? enterprise.sewageDischarge + '' : '0')
      })
      count < total - 1 ? count++ : (count = 0)
    }, 3 * 1000)
  }

  // 水质监测轮播timer处理
  private switchTimer(immediate: boolean): void {
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const _this = this
    if (this.monitorTimer) {
      clearInterval(this.monitorTimer)
    }
    // 立即执行一次
    if (immediate) {
      this.waterMonitorCuttentValue = (this.waterMonitorButtonList[this.monitorTimerCount] as any).id
      this.monitorTimerCount < this.waterMonitorButtonList.length ? this.monitorTimerCount++ : (this.monitorTimerCount = 0)
      this.waterMonitorSelectChange({
        target: {
          value: _this.waterMonitorCuttentValue,
          mechanical: true,
        },
      })
    }
    this.monitorTimer = setInterval(() => {
      _this.waterMonitorCuttentValue = (_this.waterMonitorButtonList[_this.monitorTimerCount] as any).id
      _this.monitorTimerCount < _this.waterMonitorButtonList.length - 1 ? _this.monitorTimerCount++ : (_this.monitorTimerCount = 0)
      _this.waterMonitorSelectChange({
        target: {
          value: _this.waterMonitorCuttentValue,
          mechanical: true,
        },
      })
    }, 30 * 1000)
  }
  private videoError(e: any) {}
  private waterType: any = 'II'
  // 获取站点列表
  private getStationList(): void {
    getStationList('')
      .then((res) => {
        if (res.data.data) {
          // this.mapWaterStations = res.data.data;
          this.WaterStations = res.data.data
          this.waterStationList = res.data.data
          this.defaultStationValue = this.waterStationList[0].stationId
          this.defaultStationDetailValue = this.waterStationList[0].stationId
          this.waterType = this.waterStationList[0].waterType
          // this.currentStationAddress = this.waterStationList[0].stationAddress;
          this.fetchCameraList(this.defaultStationValue)
          this.waterMonitorButtonList = this.waterStationList[0].monitorItems.map((record: any) => {
            return {
              id: record.itemCode + '',
              name: record.name,
              unit: record.concentrationUnit,
            }
          })
          if (this.waterMonitorButtonList.length > 0) {
            this.switchTimer(true)
          }
        }
      })
      .finally(() => {
        this.airData = [
          {
            lng: 104.069947,
            lat: 30.714114,
          },
        ]
      })
  }
  private getlatestRecord(value: any) {
    getlatestRecord(value).then((res: any) => {
      this.patrolDetail = res.data.data || {}
      if (JSON.stringify(this.patrolDetail) !== '{}') {
        this.patrolDetail.annexUrlList = (this.patrolDetail.taskAnnexList || []).map((item: any) => item.annexUrl)
        this.patrolDetail.time = JSON.stringify(this.patrolDetail) === '{}' ? '' : this.patrolDetail.createTime.substr(5, 11)
      }
    })
  }
  private search() {
    if (!this.searchWords) {
      return
    }
    if (this.isShowWater) {
      this.getStationLists()
    } else if (this.isShowDrain) {
      this.getDrainLists()
    } else if (this.isShowWaterSourceMonitor || this.isShowDrainMonitor) {
      this.getCameraLists()
    }
  }
  private getStationLists() {
    this.list = []
    getStationList(this.searchWords)
      .then((res) => {
        ;(res.data.data || []).forEach((item: any) => {
          item.id = item.stationId
          item.name = item.stationName
          item.type = 1
          this.list.push(item)
        })
      })
      .finally(() => {
        this.getDrainLists()
      })
  }
  private handleChoose(params: any) {
    this.searchWords = params.name
    this.isClick = true
    this.list = []
    if (params.type == 1) {
      let currentStation: any
      for (const station of this.waterStationList) {
        if (station.stationId === params.stationId) {
          currentStation = station
        }
      }
      for (const station of this.mapWaterStations) {
        if (station.stationId === params.stationId) {
          this.currentSelectedStationMarker = station.stationId
          this.currentSelectedStationObj.target.w.data = station
        }
      }
      this.defaultStationDetailValue = currentStation.stationId
      this.waterType = currentStation.waterType
      this.waterMonitorButtonList = currentStation.monitorItems.map((record: any) => {
        return {
          id: record.itemCode + '',
          name: record.name,
          unit: record.concentrationUnit,
        }
      })
      if (this.waterMonitorButtonList.length > 0) {
        this.monitorTimerCount = 0
        this.switchTimer(true)
      }
    } else if (params.type == 4) {
      // 处理监控设备的选择
      // 显示右侧面板
      this.isShowKind = true
      // 根据监控设备的type来决定激活哪种监控类型
      const cameraData = this.mapWaterStationsVideo.find((item: any) => item.monitorId === params.id)
      if (cameraData) {
        if (cameraData.type === 1 || cameraData.type === 2) {
          this.isShowWaterSourceMonitor = true
        } else if (cameraData.type === 3 || cameraData.type === 4 || cameraData.type === 5) {
          this.isShowDrainMonitor = true
        }
        // this.cameraSelect(cameraData)
        this.$refs.mapRef.focusOnMarkerById(cameraData.id)
      }
      this.isFold = false
    } else {
      this.currSelect = params
    }
  }
  private currSelect: any = {}
  getDrainLists() {
    if (!this.isShowWater) {
      this.list = []
    }
    getDrainList(this.searchWords)
      .then((res: any) => {
        ;(res.data.data || []).forEach((item: any) => {
          item.id = item.waterDrainId
          item.name = item.drainName
          item.type = 2
          this.list.push(item)
        })
      })
      .finally(() => {
        this.getDrainPersons()
      })
  }
  getDrainPersons() {
    getDrainPerson(this.searchWords).then((res: any) => {
      ;(res.data.data || []).forEach((item: any) => {
        item.id = item.merchantId
        item.name = item.merchantName
        item.type = 3
        this.list.push(item)
      })
    })
  }

  // 获取监控设备列表
  getCameraLists() {
    this.list = []
    // 根据当前激活的监控类型确定要搜索的设备列表
    let devicesToSearch: any[] = []
    if (this.isShowWaterSourceMonitor && this.isShowDrainMonitor) {
      devicesToSearch = this.mapWaterStationsVideo
    } else if (this.isShowWaterSourceMonitor) {
      devicesToSearch = this.waterSourceMonitorDevices
    } else if (this.isShowDrainMonitor) {
      devicesToSearch = this.drainMonitorDevices
    }

    // 搜索监控数据
    if (devicesToSearch && devicesToSearch.length > 0) {
      devicesToSearch.forEach((item: any) => {
        // 如果设备名称或地址包含搜索关键字，就添加到列表中
        if ((item.monitorName && item.monitorName.includes(this.searchWords)) || (item.address && item.address.includes(this.searchWords))) {
          item.id = item.monitorId
          item.name = item.monitorName
          item.type = 4 // 类型4表示监控设备
          this.list.push(item)
        }
      })
    }
  }

  private clearSearch() {
    this.searchWords = ''
    this.isClick = false
    this.list = []
  }
  // 排口筛选
  private drainStationDetailChange() {
    // defaultDrainStationDetailValue
    const item = this.drainListData.find((item: any) => item.waterDrainId == this.defaultDrainStationDetailValue)
    item.type = 2
    this.currSelect = item
    // this.getDrainDetail()
  }
  private drainDetail: any = {}
  private getDrainDetail() {
    getDrainDetail(this.defaultDrainStationDetailValue).then((res) => {
      this.pollutionType = 0
      this.type = 1
      this.drainDetail = res.data.data
      this.drainDetail.monitorMap = (this.drainDetail.monitorMap || []).map((item: any) => {
        item.lineData = {
          dataList: (item.valueList || []).map((items: any) => items.value),
          unit: item.concentrationUnit,
          bottomList: (item.valueList || []).map((items: any) => items.time.substr(0, items.time.length - 3)),
          isAlarm: (item.valueList || []).map((items: any) => items.isAlarm),
          name: item.name,
        }
        return item
      })
      this.drainDetail.drainCameraList = this.drainDetail.drainCameraList || []
      if (this.drainDetail.drainCameraList.length) {
        this.defaultDrainCamera = this.drainDetail.drainCameraList[0].monitorId
        this.snapUrl = this.drainDetail.drainCameraList[0].snapUrl
        if (this.drainDetail.drainCameraList[0].online) {
          this.getvideoUrl(this.defaultDrainCamera)
        }
      } else {
        this.defaultDrainCamera = ''
        this.videoUrl = ''
      }
    })
  }
  private getvideoUrl(monitorId: any) {
    fetchCameraUrl(monitorId).then((res: any) => {
      if (res.data.data) {
        this.videoUrl = res.data.data.wsFlvHttps
      }
    })
  }
  private snapUrl: any = ''
  private drainCameraChange() {
    const item = (this.drainDetail.drainCameraList || []).find((item: any) => item.monitorId == this.defaultDrainCamera)
    this.snapUrl = item.snapUrl
    if (item.online) {
      this.getvideoUrl(item.monitorId)
    } else {
      this.videoUrl = ''
    }
  }
  // 水质监测站点详情change
  private waterStationDetailChange(value: string): void {
    let currentStation: any
    for (const station of this.waterStationList) {
      if (station.stationId === value) {
        currentStation = station
      }
    }
    for (const station of this.mapWaterStations) {
      if (station.stationId === value) {
        this.currentSelectedStationMarker = station.stationId
        this.currentSelectedStationObj.target.w.data = station
      }
    }
    this.defaultStationDetailValue = currentStation.stationId
    this.waterType = currentStation.waterType
    // currentStation.time =
    //   currentStation.realtimeMonitorRecords.length === 0
    //     ? ""
    //     : currentStation.realtimeMonitorRecords[0].time
    //     ? currentStation.realtimeMonitorRecords[0].time
    //     : "";
    this.waterMonitorButtonList = currentStation.monitorItems.map((record: any) => {
      return {
        id: record.itemCode + '',
        name: record.name,
        unit: record.concentrationUnit,
      }
    })
    if (this.waterMonitorButtonList.length > 0) {
      this.monitorTimerCount = 0
      this.switchTimer(true)
    }
  }
  private chainRatio: any = {}
  // 获取监测项详情记录
  private getMonitorItemDetailRecord(stationId: string, itemCode: string): void {
    getMonitorItemDetailRecord(stationId, itemCode).then((res) => {
      if (res.data.data) {
        const chainRatio: any = {
          bottomList: [],
          monthName: '',
          monthList: [],
          ringRatioName: '环比率',
          ringRatioList: [],
          unit: '',
          unit1: '环比率(%)',
        }
        const { hourMonitorItems, lastYear, thisYear } = res.data.data
        this.waterMonitor.bottomList = []
        this.waterMonitor.dataList = []
        // 24小时水质质量趋势
        if (hourMonitorItems.length > 0) {
          const sortedHourMonitorItems = hourMonitorItems.sort((a: any, b: any) => {
            return a.hour - b.hour
          })
          if (sortedHourMonitorItems[0].alarmValue) {
            this.waterMonitor.warnValue = sortedHourMonitorItems[0].alarmValue
          } else {
            this.waterMonitor.miniValue = sortedHourMonitorItems[0].alarmMinValue
            this.waterMonitor.maxValue = sortedHourMonitorItems[0].alarmMaxValue
          }
          this.waterMonitor.valueType = sortedHourMonitorItems[0].valueType
          // this.waterMonitor.unit = sortedHourMonitorItems[0].unit
          sortedHourMonitorItems.forEach((record: any) => {
            this.waterMonitor.bottomList.push(record.hour + '时')
            this.waterMonitor.dataList.push(record.monitorValue || '-')
          })
        }

        this.waterchange.beforeOneYearDataList = []
        this.waterchange.currentYearDataList = []
        this.waterchange.sameRatio = []

        // 水质质量对比 lastYear
        if (lastYear.length > 0) {
          const sortedLastYear = lastYear.sort((a: any, b: any) => {
            return a.month - b.month
          })
          this.waterchange.beforeOneYearName = sortedLastYear[0].year + ''
          this.waterchange.unit = this.waterMonitor.unit
          chainRatio.unit = this.waterMonitor.unit
          this.waterchange.bottomList.forEach((m: any, index: number) => {
            let currentMonth: any = null
            sortedLastYear.forEach((yearLucy: any) => {
              if (index + 1 === yearLucy.month) {
                currentMonth = yearLucy
              }
            })
            if (currentMonth) {
              this.waterchange.beforeOneYearDataList.push(currentMonth.value || '-')
            } else {
              this.waterchange.beforeOneYearDataList.push('-')
            }
          })
        }

        // 水质质量对比 thisYear
        if (thisYear.length > 0) {
          const sortedThisYear = thisYear.sort((a: any, b: any) => {
            return a.month - b.month
          })
          this.waterchange.currentYearName = sortedThisYear[0].year + ''
          this.waterchange.bottomList.forEach((m: any, index: number) => {
            let currentMonth: any = null
            sortedThisYear.forEach((yearLucky: any) => {
              if (index + 1 === yearLucky.month) {
                currentMonth = yearLucky
              }
            })
            if (currentMonth) {
              this.waterchange.currentYearDataList.push(currentMonth.value || '-')
            } else {
              this.waterchange.currentYearDataList.push('-')
            }
          })
        }
        for (const item of res.data.data.yoyList) {
          ;(this.waterchange.sameRatio as any).push(item.over)
        }
        // 环比
        if (res.data.data.momList.length !== res.data.data.beforeYear.length) {
          const leng = res.data.data.beforeYear.length - res.data.data.momList.length
          for (let index = 0; index < leng; index++) {
            chainRatio.ringRatioList.push('-')
          }
        }
        for (const item of res.data.data.momList) {
          chainRatio.ringRatioList.push(item.over)
        }
        for (const item of res.data.data.beforeYear) {
          chainRatio.monthList.push(item.value)
          chainRatio.bottomList.push(item.year + '-' + item.month)
        }
        this.chainRatio = chainRatio
      }
    })
  }

  // 水质监控站点切换
  private waterStationChange(value: string): void {
    this.fetchCameraList(value)
    this.monitorTimerCount = 0
    this.defaultStationValue = value + ''
    for (const station of this.waterStationList) {
      if (value === station.stationId) {
        // this.currentStationAddress = station.stationAddress;
      }
    }
    // 重置一次摄像头数据
    this.currentCameraList = []
    this.defaultCameraValue = ''
    this.playerOptions.sources = [
      {
        src: null,
        type: null,
      },
    ]
  }

  // 获取摄像头列表
  private fetchCameraList(station: string): void {
    this.getlatestRecord(station)
    fetchCameraList(station).then((res) => {
      if (res.data.data && res.data.data.length > 0) {
        this.currentCameraList = res.data.data
        this.currentStationAddress = this.currentCameraList[0].address
        this.defaultCameraValue = this.currentCameraList[0].monitorId
        this.fetchCameraUrl(this.defaultCameraValue, this.currentCameraList[0].snapUrl)
      } else {
        this.currentStationAddress = ''
        this.currentCameraList = []
        this.defaultCameraValue = ''
        this.playerOptions.sources = [
          {
            src: '',
            type: 'application/x-mpegURL',
          },
        ]
        this.playerOptions.poster = ''
      }
    })
  }

  // 水质监控站点摄像头切换
  private waterCameraChange(value: any): void {
    const camera = this.currentCameraList.find((item: any, index: any) => {
      if (item.monitorId == value) {
        return item
      }
    })
    this.currentStationAddress = camera.address
    this.fetchCameraUrl(value, camera.snapUrl)
  }

  private cameraSelect(data: any) {
    this.showDialog(data)
    this.isShowKind = true
    this.currentCameraList = [data]
    this.currentStationAddress = this.currentCameraList[0].address
    this.defaultCameraValue = this.currentCameraList[0].monitorId
    this.fetchCameraUrl(this.defaultCameraValue, this.currentCameraList[0].snapUrl)
    // this.currentStationAddress = data.address
    // this.fetchCameraUrl(data.monitorId, data.snapUrl)
  }

  // 获取摄像头播放地址
  private monitorLoading = false
  private fetchCameraUrl(channelId: string, snapUrl: any): void {
    this.monitorLoading = true
    fetchCameraUrl(channelId)
      .then((res: any) => {
        this.monitorUrl = res.data.data.wsFlvHttps
        // 清除上一次地址
        this.playerOptions.sources = [
          {
            src: '',
            type: 'application/x-mpegURL',
          },
        ]
        if (res.data.data && res.data.data.hlsHttps) {
          this.playerOptions.sources.push({
            type: 'application/x-mpegURL',
            src: res.data.data.hlsHttps,
          })
        } else if (res.data.data && res.data.data.flvHttps) {
          this.playerOptions.sources.push({
            type: 'flv-application/octet-stream',
            src: res.data.data.flvHttps,
          })
        } else if (res.data.data && res.data.data.rtmp) {
          this.playerOptions.sources.push({
            type: 'rtmp/mp4',
            src: res.data.data.rtmp,
          })
        } else {
          this.playerOptions.notSupportedMessage = res.data.msg
          this.playerOptions.sources = [
            {
              src: '',
              type: 'application/x-mpegURL',
            },
          ]
        }
        if (snapUrl) {
          this.playerOptions.poster = snapUrl
        } else {
          this.playerOptions.poster = ''
        }
      })
      .finally(() => {
        this.monitorLoading = false
      })
  }

  // 播放状态更改
  private playerStateChanged(playerCurrentState: any) {
    if (playerCurrentState.error) {
      this.waterCameraChange(this.defaultCameraValue)
    }
  }
  private last: any = ''
  // 切换图表数据面板展示控制
  private toggleFoldModule(): void {
    this.isFold = !this.isFold
    if (this.isFold && !this.last) {
      this.last = this.isShowKind
      this.isShowKind = true
    } else if (!this.isFold && !this.last) {
      this.isShowKind = false
    } else {
      this.isShowKind = true
    }
    // if(!this.isFold && !this.isShowKind) {
    //   this.isShowKind = false
    // } else {
    //   this.isShowKind = true
    // }
    this.mapViewCenter = this.isFold
      ? {
          lng: 104.04,
          lat: 30.71,
        }
      : {
          lng: 104.08,
          lat: 30.725,
        }
    this.mapViewZoom = this.isFold ? 13.4 : 13.3
  }

  // 地图选择水质监测站点处理
  private mapStationSelect(stationId: string): void {
    this.isShowKind = true
    this.waterStationDetailChange(stationId)
    this.waterStationChange(stationId)
  }

  // 地图重污染企业选中处理
  private mapBuildingSelect(BuildingId: string): void {
    this.heavilyPollutingEnterpriseChage(BuildingId)
  }
  private waterCard: any = {}
  // 获取水质打卡信息
  fetchGetPunch(stationId: any) {
    getPunch({ stationId }).then((res: any) => {
      res.data.data.punchTime = res.data.data.punchTime.slice(5, 16)
      this.waterCard = res.data.data
    })
  }
  // 更多视频
  private waterVideo() {
    this.currentCameraList = []
    this.defaultCameraValue = ''
    this.playerOptions.sources = [
      {
        src: '',
        type: 'application/x-mpegURL',
      },
    ]
    this.playerOptions.poster = ''
    this.$router.push('/waterVideo')
  }
  // 更多打卡记录
  private stationPatrolRecord() {
    this.$router.push('/StationPatrolRecord')
  }

  /**
   * 地图监控设备详情弹窗
   * */
  private dialogVisible = false
  private formData
  private monitorUrl = null
  private showDialog(row) {
    this.monitorUrl = null
    this.formData = { ...row }
    this.dialogVisible = true
  }

  /**
   * 排口设备详情弹窗
   * */
  private currentDrain = null
  private drainDialogVisible = false
  private showDrainDialog(args) {
    this.currentDrain = { ...args }
    this.drainDialogVisible = true
  }
}
</script>
