<template>
  <div :id="id" :style="{ height: height, width: width }" />
</template>

<script lang="ts">
import * as echarts from 'echarts'
import { Component, Prop, Watch } from "vue-property-decorator";
import { mixins } from "vue-class-component";
import ResizeMixin from "./mixins/resize";
interface AirContrast {
  monthList: string | number[];
  thisYear: string | number[];
  lastYear: string | number[];
  thisYearName?: string;
  lastYearName?: string;
}
@Component({
  name: "proportionChart"
})
export default class extends mixins(ResizeMixin) {
  // @Prop({ default: "chart" }) private className!: string;
  @Prop({ default: "chart" }) private id!: string;
  @Prop({ default: "200px" }) private width!: string;
  @Prop({ default: "200px" }) private height!: string;
  @Prop({ required: true }) private propData!: AirContrast;
  @Watch("propData", { immediate: true, deep: true })
  public onAirType(newValue: any, oldValue: any) {
    this.$nextTick(() => {
      this.initChart();
    });
  }
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
  }

  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.clear();
    this.chart.dispose();
    this.chart = null;
  }

  private initChart() {

    if(this.chart === null || this.chart === undefined) {
      this.chart = echarts.init(
        document.getElementById(this.id) as HTMLDivElement
      );
    }
    this.chart.setOption({
      backgroundColor: "transparent",
      grid: {
        height: 130,
        bottom: 30
      },
      legend: {
        textStyle: {
          color: "#fff"
        },
        right: 30
      },
      xAxis: {
        type: "category",
        data: this.propData.monthList,
        axisLabel: {
          textStyle: {
            fontSize: 12,
            color: "white"
          }
        },
        axisLine: {
          lineStyle: {
            color: "white"
          }
        },
        axisTick: {
          show: false
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: "RGBA(2, 39, 75, 1)"
          }
        }
      },
      yAxis: {
        type: "value",
        axisLabel: {
          textStyle: {
            fontSize: 12,
            color: "white"
          }
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        splitLine: {
          show: false,
          lineStyle: {
            type: "dashed"
          }
        }
      },
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "line",
          label: {
            backgroundColor: "#6a7985"
          }
        }
      },
      series: [
        {
          // 去年
          name: this.propData.lastYearName,
          data: this.propData.lastYear,
          type: "line",
          areaStyle: {
            //线性渐变，前4个参数分别是x0,y0,x2,y2(范围0~1);相当于图形包围盒中的百分比。如果最后一个参数是‘true’，则该四个值是绝对像素位置。
            color: new echarts.graphic.LinearGradient(
              0,
              0,
              0,
              1,
              [
                {
                  offset: 0,
                  color: "RGBA(1, 208, 254, 0.5)"
                },
                {
                  offset: 1,
                  color: "RGBA(1, 208, 254, 0)"
                }
              ],
              false
            ),
            // shadowColor: "rgba(53,142,215, 0.9)", //阴影颜色
            shadowBlur: 20 //shadowBlur设图形阴影的模糊大小。配合shadowColor,shadowOffsetX/Y, 设置图形的阴影效果。
          },
          smooth: true,
          lineStyle: {
            color: "RGBA(1, 208, 254, 1)" //改变折线颜色
          },
          symbol: "circle",
          symbolSize: 0, // 8
          itemStyle: {
            color: "RGBA(1, 208, 254, 1)", //改变折线点的颜色
            borderColor: "RGBA(1, 208, 254, 1)",
            borderWidth: 2
          }
        },
        {
          // 今年
          name: this.propData.thisYearName,
          data: this.propData.thisYear,
          type: "line",
          areaStyle: {
            //线性渐变，前4个参数分别是x0,y0,x2,y2(范围0~1);相当于图形包围盒中的百分比。如果最后一个参数是‘true’，则该四个值是绝对像素位置。
            color: new echarts.graphic.LinearGradient(
              0,
              0,
              0,
              1,
              [
                {
                  offset: 0,
                  color: "RGBA(45, 188, 113, 1)"
                },
                {
                  offset: 1,
                  color: "RGBA(45, 188, 113, 0)"
                }
              ],
              false
            ),
            // shadowColor: "rgba(53,142,215, 0.9)", //阴影颜色
            shadowBlur: 20 //shadowBlur设图形阴影的模糊大小。配合shadowColor,shadowOffsetX/Y, 设置图形的阴影效果。
          },
          smooth: true,
          lineStyle: {
            color: "RGBA(45, 188, 113, 1)" //改变折线颜色
          },
          symbol: "circle",
          symbolSize: 0, // 8
          itemStyle: {
            color: "RGBA(45, 188, 113, 1)", //改变折线点的颜色
            borderColor: "RGBA(45, 188, 113, 1)",
            borderWidth: 2
          }
        }
      ]
    } as any);
  }
}
</script>
