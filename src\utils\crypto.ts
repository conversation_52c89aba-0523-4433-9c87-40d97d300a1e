
const CryptoJS = require('crypto-js')

// 密钥
const SECRET_KEY = CryptoJS.enc.Utf8.parse('5NwkVm2is6Ge6B82pUy6ZHpw2eaqJFif')
// 偏移量
const SECRET_IV = CryptoJS.enc.Utf8.parse('j2wQgCN9WuPujQ90')
 export default {
    // 加密
 encrypt(word: string | number) {
    const srcs = CryptoJS.enc.Utf8.parse(word)
    const encrypted = CryptoJS.AES.encrypt(
      srcs,
      SECRET_KEY,
      { iv: SECRET_IV, mode: CryptoJS.mode.CBC, padding: CryptoJS.pad.Pkcs7 }
    )
    return encrypted.toString()
  },
  // 解密
   decrypt(word: string | number) {
    const decrypt1 = CryptoJS.AES.decrypt(
      word,
      SECRET_KEY,
      { iv: SECRET_IV, mode: CryptoJS.mode.CBC, padding: CryptoJS.pad.Pkcs7 }
    )
    console.log('decrypt1', CryptoJS.enc.Utf8.stringify(decrypt1))
    return CryptoJS.enc.Utf8.stringify(decrypt1).toString()
  }
 }
