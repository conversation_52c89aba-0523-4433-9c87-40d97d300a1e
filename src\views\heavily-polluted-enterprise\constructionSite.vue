<style lang="less" scoped>
.waste_date_picker {
  /deep/.el-input__inner {
    background-color: transparent;
    border: #034fa8 1px solid;
    padding: 0;
    color: #04c6e7;
    font-size: 15px;
    cursor: pointer;
    padding-left: 10px;
    padding-right: 10px;
  }
  /deep/.el-input__suffix {
    color: #04c6e7;
  }
}
.title_select {
  position: absolute;
  right: 0;
  top: 0;
  display: flex;
  .title_select_item {
    width: 0.85rem;
    height: 0.32rem;
    background: url("../../assets/noise/shaixuanc <EMAIL>") no-repeat;
    background-size: 100%;
    text-align: center;
    line-height: 0.32rem;
    font-size: 0.14rem;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #0c6dc3;
    cursor: pointer;
  }
  .title_select_item_select {
    background: url("../../assets/noise/<EMAIL>") no-repeat;
    color: #3adbff;
  }
}
.common-main {
  position: relative;
  overflow: hidden;
  width: 100%;
  padding: 0;
  .left-part {
    > div {
      width: 4rem;
    }

    position: absolute;
    top: 0.5rem;
    left: 0.5rem;
    .title {
      font-size: 0.2rem;
      cursor: pointer;
    }
    .flex-number {
      cursor: pointer;
      display: flex;
      margin-top: 0.18rem;
      > div {
        border: 1px solid RGBA(33, 109, 253, 1);
        background-color: RGBA(35, 211, 255, 0.6);
        font-size: 0.4rem;
        margin-right: 0.08rem;
        text-align: center;
        padding: 0rem 0.12rem;
        font-family: "300-CAI978";
      }
    }
    .video-main {
      margin-top: 3rem;
      .sub-title {
        > img {
          width: 50%;
          height: 0.1rem;
        }
      }
      .box-title {
        justify-content: start;
        .title {
          margin-right: 0.5rem;
        }
        .unit {
          color: #7bb7ed;
          font-size: 14px;
        }
      }
      .title-video {
        display: flex;
        align-items: center;
      }
      img {
        width: 1.2rem;
      }
    }
  }
  .center-map {
    position: absolute;
    height: calc(1080px - 0.94rem);
    width: 100%;
    // margin-top: 0.15rem;
    > div {
      width: 100%;
      height: 100%;
    }
  }
  .right-part {
    position: absolute;
    top: 0;
    right: 0;
    height: calc(1080px - 1rem);
    width: 4.6rem;
    padding: 0.3rem 0.25rem 0;
    /*background-image: url(../../assets/air_bg.png) !important;*/
    background-size: 100% 100% !important;
    > .enterprise {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      // justify-content: space-between;
      > .enterprise-main {
        width: 100%;
        height: 32%;
      }
      > .pollute-main {
        height: 42%;
      }
      > .discharge-main {
        height: 26%;
      }
      // > .construction-site {
      // }
      // > .monitor-main {
      // }
    }
    > .construction-plant {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      // > div:first-child {
      // }
      > div:last-child {
        margin-top: 0.2rem;
      }
    }
    .randaSel {
      position: absolute;
      left: -190%;
      bottom: 0.9rem;
      display: flex;
      width: 600px;
      justify-content: space-around;
      .randaSel_item {
        height: 0.34rem;
        width: 1rem;
        text-align: center;
        line-height: 0.32rem;
        color: #a6c0d5;
        font-size: 0.2rem;
        box-sizing: border-box;
        font-family: PingFang SC;
        font-weight: 500;
        cursor: pointer;
        background: url("../../assets/randa/<EMAIL>") no-repeat;
        background-size: 100%;
      }
      .randaSel_item_active {
        width: 1.97rem;
        height: 0.53rem;
        font-size: 0.24rem;
        color: #fefefe;
        line-height: 0.44rem;
        background: url("../../assets/randa/<EMAIL>") no-repeat;
        background-size: 100%;
      }
    }
    .sub-title {
      margin: 0 0 0.05rem;
      .sub_tabs {
        display: flex;
        height: 0.3rem;
        line-height: 0.26rem;
        border: 0.5px solid #0e2344;
        box-sizing: border-box;
        width: 100%;
        margin-top: 0.1rem;
        margin-bottom: 0.05rem;
        .tab {
          font-size: 0.12rem;
          font-family: PingFang SC;
          font-weight: 500;
          color: #ffffff;
          background: #0a204a;
          cursor: pointer;
          flex: 1;
          text-align: center;
          overflow: hidden; // 超出边框外隐藏
          text-overflow: ellipsis; // 属性规定当文本溢出包含元素时发生的事情。
          white-space: nowrap; // 规定段落中的文本不进行换行
        }
        .active {
          background-color: #2e8ef0;
        }
      }
      .sub_tabs1 {
        display: flex;
        height: 0.3rem;
        line-height: 0.26rem;
        border: 0.5px solid #0e2344;
        box-sizing: border-box;
        width: 100%;
        margin-top: 0.1rem;
        margin-bottom: 0.05rem;
        .tab {
          font-size: 0.12rem;
          font-family: PingFang SC;
          font-weight: 500;
          color: #ffffff;
          background: #0a204a;
          cursor: pointer;
          text-align: center;
          overflow: hidden; // 超出边框外隐藏
          text-overflow: ellipsis; // 属性规定当文本溢出包含元素时发生的事情。
          white-space: nowrap; // 规定段落中的文本不进行换行
        }
        .active {
          background-color: #2e8ef0;
        }
        .else {
          flex: 1;
        }
        .mult {
          width: 80px !important;
        }
      }
    }
    .pollution-main {
      .update-time-pollution {
        font-size: 0.12rem;
        color: white;
        text-align: right;
      }
      .flex-box {
        width: 100%;
        display: flex;
        justify-content: space-between;
        font-size: 0.14rem;
        margin-top: 0.05rem;
        > div {
          .type-card {
            background: url("../../assets/card-top.png") no-repeat center;
            background-size: 100% 100%;
            height: 0.32rem;
            line-height: 0.32rem;
            text-align: center;
            margin-bottom: 0.05rem;
          }
        }
        > div:first-child {
          width: 40%;
          > div {
            margin-top: 0.3rem;
            .name {
              line-height: 0.12rem;
            }
            .number {
              font-size: 0.24rem;
              color: RGBA(0, 229, 255, 1);
              margin-right: 0.06rem;
              font-family: "300-CAI978";
            }
          }
          > div:first-child {
            margin-top: 0;
          }
          > div:nth-child(2) {
            margin-top: 0.08rem;
          }
        }
        > div:last-child {
          width: 60%;
          .change-box {
            width: 115%;
            display: flex;
            box-sizing: border-box;
            > div {
              width: 1.2rem;
              height: 0.2rem;
              -webkit-clip-path: polygon(25% 0%, 100% 0%, 75% 100%, 0% 100%);
              clip-path: polygon(25% 0%, 100% 0%, 75% 100%, 0% 100%);
              background-color: RGBA(6, 58, 114, 1);
              text-align: center;
              height: 0.24rem;
              line-height: 0.24rem;
              cursor: pointer;
            }
            > div:nth-child(2) {
              position: relative;
              right: 0.2rem;
            }
            > div:last-child {
              -webkit-clip-path: polygon(25% 0%, 100% 0%, 100% 100%, 0% 100%);
              clip-path: polygon(25% 0%, 100% 0%, 100% 100%, 0% 100%);
              position: relative;
              right: 0.4rem;
            }
            > .active {
              background-color: RGBA(0, 132, 255, 1);
            }
          }
        }
      }
    }
    .heavy-pollution-table,
    .table-site {
      width: 100%;
      border: 1px solid rgba(54, 218, 234, 0.42);
      .table-name {
        font-size: 0.12rem;
        width: 0.76rem;
        background: rgba(15, 36, 94, 1);
        text-align: center;
      }
      .table-text {
        padding-left: 0.06rem;
      }
      td {
        font-size: 0.12rem;
        height: 0.34rem;
        color: #ffffff;
        font-weight: 500;
      }
    }
    .table-site {
      width: 100%;
      .PM10-detail {
        display: flex;
        align-items: center;
        > span:first-child {
          color: rgb(51, 231, 51);
          font-size: 0.2rem;
        }
        > span:nth-child(2) {
          margin: 0 0.1rem;
        }
        > span:last-child {
          background: rgb(51, 231, 51);
          border-radius: 1px;
          padding: 0 0.05rem;
        }
      }
    }
    .table-data1 {
      cursor: pointer;
      width: 100%;
      .table-data-thead {
        .tr {
          display: flex;
          .th {
            color: #ffffff;
            padding: 0;
            text-align: center;
            border: none;
            font-size: 0.16rem;
          }
          > :nth-of-type(1) {
            width: 20%;
          }
          > :nth-of-type(2) {
            width: 40%;
            text-align: left;
          }
          > :nth-of-type(3) {
            width: 20%;
          }
          > :nth-of-type(4) {
            width: 20%;
          }
        }
      }
      .table-data-tbody {
        height: 1.36rem;
        overflow: hidden;
        .tr {
          display: flex;
          .td {
            color: #ffffff;
            padding: 0;
            font-size: 0.14rem;
            text-align: center;
            height: 0.34rem;
            line-height: 0.34rem;
            border: none;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          > :nth-of-type(1) {
            width: 20%;
            div {
              margin-top: 0.07rem;
            }
          }
          > :nth-of-type(2) {
            width: 40%;
            text-align: left;
          }
          > :nth-of-type(3) {
            width: 20%;
          }
          > :nth-of-type(4) {
            width: 20%;
          }
        }
      }
      .table-data-tbody .tr:nth-child(odd) {
        background: #0f245e;
      }
      .table-data-tbody .tr:nth-child(even) {
        background: transparent;
      }
      .tdBefore {
        //前三
        width: 0.26rem;
        display: block;
        text-align: center;
        height: 0.2rem;
        line-height: 0.2rem;
        background: rgba(255, 133, 9, 1);
        border-radius: 0.06rem;
        margin: 0 auto;
      }
      .tdAfter {
        //前三外
        width: 0.26rem;
        display: block;
        text-align: center;
        height: 0.2rem;
        line-height: 0.2rem;
        background: rgba(18, 136, 226, 1);
        border-radius: 0.06rem;
        margin: 0 auto;
      }
    }
    .Rd_his_title {
      display: flex;
      width: 100%;
      justify-content: space-around;
      margin-bottom: 25px;
      margin-top: 15px;
      &_item {
        width: 33%;
        display: flex;
        img {
          width: 60px;
          height: 60px;
          margin-right: 5px;
        }
        &c_label {
          font-size: 13px;
          font-family: PingFang SC;
          font-weight: 400;
          color: #a6c0d5;
        }
        &_count {
          font-size: 25px;
          font-family: DIN;
          font-weight: 500;
          color: #d7f5ff;
        }
      }
    }
    .Rd_his_list {
      overflow-y: auto;
      &::-webkit-scrollbar-thumb {
        border-radius: 10px;
        width: 1px;
        background: rgba(57, 177, 255, 0.16);
      }
      &::-webkit-scrollbar-track {
        border-radius: 10px;
        background: rgba(57, 177, 255, 0);
      }
      &_item {
        height: 0.36rem;
        background: #001d34;
        display: flex;
        line-height: 0.34rem;
        text-align: center;
        img {
          width: 0.22rem;
          height: 0.25rem;
          margin: 0.05rem 0 0 0.15rem;
        }
        &_time,
        &_split,
        &_alarm {
          font-family: DIN;
          font-weight: 500;
          color: #a6c0d5;
          font-size: 0.14rem;
        }
        &_time {
          width: 1.3rem;
        }
        &_split {
          width: 0.75rem;
        }
        &_alarm {
          width: 1.7rem;
          text-align: left;
          padding-left: 0.05rem;
        }
      }
    }
  }
}
.enterprise-search {
  position: absolute;
  top: 0.5rem;
  right: 50%;
  display: flex;
  align-items: center;
  width: 7.5rem;
  height: 0.6rem;
  margin-right: -3.5rem;
  > :nth-of-type(1) {
    display: flex;
    align-items: center;
    width: 6.5rem;
    height: 0.6rem;
    background: rgba(5, 7, 95, 0.7);
    border: 1px solid #034fa8;
    input {
      width: 100%;
      margin-left: 0.25rem;
      // margin-right: 0.25rem;
      background: transparent;
      border: none;
      outline: none;
      font-size: 0.22rem;
    }
    input::-webkit-input-placeholder {
      color: #ffffff;
    }
    input::-moz-placeholder {
      /* Mozilla Firefox 19+ */
      color: #ffffff;
    }
    input:-moz-placeholder {
      /* Mozilla Firefox 4 to 18 */
      color: #ffffff;
    }
    input:-ms-input-placeholder {
      /* Internet Explorer 10-11 */
      color: #ffffff;
    }
  }
  > :nth-of-type(2) {
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(5, 7, 95, 0.7);
    border: 1px solid #034fa8;
    height: 0.6rem;
    width: 1rem;
    img {
      cursor: pointer;
    }
  }
}
.type-marker {
  position: absolute;
  bottom: 0.7rem;
  left: 0.5rem;
  width: 3.25rem;
  display: flex;
  justify-content: center;
  flex-direction: column;
  height: 1.15rem;
  padding: 0.2rem 0.3rem;
  background: rgba(1, 69, 154, 0.5);
  > div {
    display: flex;
    align-items: center;
    > :nth-of-type(1) {
      width: 0.4rem;
      display: flex;
      justify-content: center;
    }
    > :nth-of-type(2) {
      margin-right: 0.25rem;
      margin-left: 0.05rem;
    }
  }
}
.tabs {
  width: 8rem;
  position: absolute;
  bottom: 0.2rem;
  left: 50%;
  right: 50%;
  transform: translateX(-50%) translateY(-50%);
}
.title-flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
  > div {
    display: flex;
    font-size: 0.14rem;
    font-weight: 400;

    > div {
      margin-right: 0.1rem;
      width: 0.8rem;
      height: 0.28rem;
      line-height: 0.3rem;
      color: #0e8bff;
      text-align: center;
      background: url(../../assets/<EMAIL>);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      cursor: pointer;
    }
    div.type-active {
      background: url(../../assets/<EMAIL>);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      color: rgba(0, 234, 255, 1);
    }
    // :nth-of-type(2) {
    //   // margin-left: 0.1rem;
    // }
  }
}
.pollutant-table {
  width: 100%;
  text-align: center;
  font-weight: normal;
  border: 1px solid rgba(11, 141, 211, 1);
  thead {
    background: rgba(15, 56, 171, 1);
    > tr {
      height: 0.36rem;
    }
  }
  tbody {
    tr {
      height: 0.29rem;
      td:nth-of-type(odd) {
        background: #0f245e;
      }
    }
  }
}
.no-data {
  height: 2rem;
  font-size: 0.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
.gongditable {
  width: 100%;
  .table-header {
    display: flex;
    text-align: center;
    align-items: center;
    height: 0.4rem;
    line-height: 0.4rem;
    color: #3be6ff;
    font-size: 0.16rem;
    > div {
      width: 33%;
    }
  }
  .table-header1 {
    > div {
      width: 50%;
    }
  }
  .table-body {
    display: flex;
    align-items: center;
    font-size: 0.14rem;
    text-align: center;
    height: 0.4rem;
    line-height: 0.4rem;
    &:nth-child(odd) {
      background: #0f245e;
    }
    div {
      &:nth-child(3n + 1) {
        padding-left: 0.15rem;
        box-sizing: border-box;
      }
      width: 33%;
      text-align: center;
      overflow: hidden; //超出的文本隐藏
      text-overflow: ellipsis; //溢出用省略号显示
      white-space: nowrap; //溢出不换行
    }
  }

  .table-body1 {
    display: flex;
    align-items: center;
    font-size: 0.14rem;
    text-align: center;
    height: 0.4rem;
    line-height: 0.4rem;
    &:nth-child(odd) {
      background: #0f245e;
    }
    div {
      &:nth-child(3n + 1) {
        padding-left: 0.15rem;
        box-sizing: border-box;
      }
      width: 50%;
      text-align: center;
      overflow: hidden; //超出的文本隐藏
      text-overflow: ellipsis; //溢出用省略号显示
      white-space: nowrap; //溢出不换行
      &:nth-child(1) {
        padding-left: 0.15rem;
        box-sizing: border-box;
        text-align: left;
      }
    }
  }
  .thead,
  .tbody {
    font-size: 0.14rem;
    text-align: left;
    height: 0.43rem;
    line-height: 0.43rem;
  }
  .tbody {
    .td:nth-of-type(1) {
      width: 48%;
      overflow: hidden; //超出的文本隐藏
      text-overflow: ellipsis; //溢出用省略号显示
      white-space: nowrap; //溢出不换行
      display: flex;
      align-items: center;
    }
    .td:nth-of-type(2) {
      width: 19%;
      display: flex;
      align-items: center;
    }
    .td:nth-of-type(3) {
      width: 0.02rem;
      height: 0.3rem;
      margin-top: 0.05rem;
      background: rgba(0, 234, 255, 1);
      margin-right: 0.05rem;
    }
    .td:nth-of-type(4) {
      width: 30%;
      display: flex;
      align-items: center;
    }
  }
  .tbody:nth-child(odd) {
    background: #082f61;
  }
  // .tbody:nth-child(even) {
  //   // background: #041433;
  // }
}
#HPBox {
  box-sizing: border-box;
  overflow: auto;
  height: 2.9rem;
  padding-bottom: 0.2rem;
  &::-webkit-scrollbar-track {
    border-radius: 6px;
    background: transparent;
  }
  &::-webkit-scrollbar-thumb {
    /*滚动条里面小方块*/
    border-radius: 5px;
    background-color: transparent;
  }
}
.randa_left_top {
  height: 89%;
  overflow-y: auto;
  &::-webkit-scrollbar-thumb {
    border-radius: 10px;
    width: 1px;
    background: rgba(57, 177, 255, 0.16);
  }
  &::-webkit-scrollbar-track {
    border-radius: 10px;
    background: rgba(57, 177, 255, 0);
  }
  .item {
    display: flex;
    box-sizing: border-box;
    margin: 0.1rem 0.1rem;
    padding: 0.13rem 0.12rem;
    position: relative;
    background: url("../../assets/randa/<EMAIL>") no-repeat;
    background-size: 100%;
    cursor: pointer;
    // &::after{
    //   content: '';
    //   width: 0.02rem;
    //   height: 0.52rem;
    //   border-radius: 0.01rem;
    //   background-color: #28B7FF;
    //   position: absolute;
    //   left: 0;
    //   top: 0.22rem;
    // }
    .more {
      position: absolute;
      right: 0.06rem;
      top: 0;
      font-size: 0.1rem;
      font-family: PingFang SC;
      font-weight: 400;
      color: #5a7081;
      cursor: pointer;
    }
    .title {
      font-size: 0.14rem;
      font-family: PingFang SC;
      font-weight: 300;
      color: #d2e6f6;
      margin-left: 0.06rem;
      overflow: hidden; // 超出边框外隐藏
      text-overflow: ellipsis; // 属性规定当文本溢出包含元素时发生的事情。
      white-space: nowrap; // 规定段落中的文本不进行换行
      width: 100%;
    }
    .time {
      font-size: 0.12rem;
      font-family: PingFang SC;
      font-weight: 300;
      color: #d7dbe5;
      line-height: 0.2rem;
      margin-left: 0.06rem;
    }
    .area {
      font-size: 0.28rem;
      font-family: DIN;
      font-weight: 500;
      color: #d2e6f6;
      line-height: 0.42rem;
      flex: 1;
      text-align: center;
    }
    &:hover {
      background: url("../../assets/randa/<EMAIL>") no-repeat;
      .title,
      .area {
        color: #10beff;
      }
    }
  }
}
// 右到左
.pu-randa-r2l {
  animation: pu-randa-r2l 1.5s linear;
}
@keyframes pu-randa-r2l {
  0% {
    transform: translate(120%, 0);
  }
  50% {
    transform: translate(120%, 0);
  }
  100% {
    transform: translate(0, 0);
  }
}
// 左到右
.pu-randa-l2r {
  animation: pu-randa-l2r 1.5s linear;
}
@keyframes pu-randa-l2r {
  0% {
    transform: translate(-120%, 0);
  }
  50% {
    transform: translate(-120%, 0);
  }
  100% {
    transform: translate(0, 0);
  }
}
// 凸显
.pu-randa-scale {
  animation: pu-randa-scale 1.5s linear;
}
@keyframes pu-randa-scale {
  0% {
    transform: scale(0);
  }
  100% {
    transform: scale(1);
  }
}
.tatle_img {
  font-size: 14px;
  font-family: Source Han Sans CN;
  font-weight: 500;
  color: #bdcde4;
  img {
    width: 25px;
    height: 25pcx;
  }
}
.shichang {
  font-size: 14px;
  font-family: Source Han Sans CN;
  font-weight: 500;
  color: #f4bc13;
}
.weigui {
  font-size: 12px;
  font-family: Source Han Sans CN;
  font-weight: 500;
  color: #bdcde4;
  span {
    font-size: 22px;
    font-family: YouSheBiaoTiHei;
    font-weight: 400;
    color: #ffffff;
  }
}
.video_coont {
  width: 395px;
  background: url(../../assets/<EMAIL>);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  margin-top: 25px;
  padding: 19px;
  .video_coont1 {
    width: 100%;
    border: 1px solid #1678b6;
  }
}
</style>
<style lang="less">
.picker_class_waste {
  background: #052f61;
  border: none;
  color: #cddfff;
  .el-date-picker__header-label,
  .el-picker-panel__icon-btn,
  .el-month-table td .cell,
  .el-year-table td .cell {
    color: #cddfff;
  }
  .popper__arrow {
    border-top-color: #052f61 !important;
    border-bottom-color: #052f61 !important;
    &::after {
      border-top-color: #052f61 !important;
      border-bottom-color: #052f61 !important;
    }
  }
  .el-date-table th {
    border: none;
    color: #cddfff;
  }
  .el-date-picker__header--bordered {
    border: none;
  }
  .el-date-table td.disabled div,
  .el-month-table td.disabled .cell,
  .el-year-table td.disabled .cell {
    background-color: #000a38;
  }
}
.lian {
  background: url(../../assets/<EMAIL>);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  width: 100px;
  height: 32px;
  margin-right: 10px;
  color: rgba(0, 234, 255, 1);
  font-size: 14px;
  text-align: center;
  line-height: 32px;
}

.box-title {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  .select-main {
    display: flex;
    justify-content: space-between;
    .ant-select-selection {
      width: 100%;
      border: none;
      outline: none;
      border-radius: unset;
      background: url(../../assets/<EMAIL>);
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
    .ant-select-selection-selected-value {
      color: rgba(0, 234, 255, 1);
    }
    .ant-select-focused .ant-select-selection,
    .ant-select-selection:focus,
    .ant-select-selection:active {
      border: none;
      border-right-width: 0 !important;
      outline: none;
      box-shadow: none;
    }
  }
}
.polluted-video {
  .video-player {
    width: 100%;
    .video-js.vjs-fluid {
      width: 100% !important;
    }
    // .video-js .vjs-big-play-button {
    //   // left: 31% !important;
    // }
    .video-js.vjs-fluid {
      height: 2.5rem !important;
    }
  }
}
.ant-modal-hpe {
  top: 1.65rem !important;
  right: calc(50% - 600px) !important;
  .ant-modal-content {
    width: 12rem !important;
    min-height: 6.9rem !important;
    max-height: 8.5rem !important;
    padding: 0.7rem 0.95rem 0.84rem;
    box-sizing: border-box;
    background-color: transparent !important;
    background-image: url("../../assets/<EMAIL>") !important;
    background-size: 100% 100% !important;
  }
  .ant-modal-footer {
    border: none !important;
  }
  .ant-modal-close {
    top: 0.49rem !important;
    right: 0.86rem !important;
    svg {
      font-size: 0.2rem !important;
      color: #42adfb;
    }
  }
  .ant-modal-close-x {
    background: RGBA(12, 39, 94, 1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    width: 0.29rem;
    height: 0.29rem;
    justify-content: center;
    border: 0.01rem solid #358fd3;
  }
  .ant-modal-body {
    padding: 0;
    .title {
      text-align: center;
      font-size: 0.25rem;
      font-family: YouSheBiaoTiHei;
      font-weight: 400;
      color: #dcf0ff;
      text-shadow: 0 0 0.03rem rgba(220, 240, 255, 0.3),
        0 0 0.03rem rgb(220, 240, 255, 0.3);
    }
    .table-container {
      margin-top: 0.2rem;
      color: #c4dbfb;
      font-size: 0.16rem;
      width: 100%;
      text-align: center;
      border-color: rgba(11, 156, 229, 0.2);
      tr {
        th:nth-child(2n-1) {
          // background: rgba(14, 45, 126, 0.7);
          background: rgba(10, 38, 103, 0.7);
        }
        td:nth-child(2n-1) {
          // background-color: rgba(14, 45, 126, 0.2);
          background-color: rgba(10, 38, 103, 0.7);
        }
      }
      .online {
        width: 69px;
        height: 35px;
        border-radius: 2px;
        font-size: 14px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        display: inline-block;
        padding: 2px 6px;
        line-height: 30px;
      }
      .yes_line {
        background: url("../../assets/randa/<EMAIL>") no-repeat;
        background-size: 100%;
        color: #15dad3;
      }
      .no_line {
        background: url("../../assets/randa/<EMAIL>") no-repeat;
        background-size: 100%;
        color: #93a1b7;
      }
      .tb_no_data {
        position: absolute;
        font-size: 22px;
        top: 1.8rem;
        left: 4.8rem;
      }
    }
  }
}
.el-dialog__wrapper {
  z-index: 1000000 !important;
}
.el-dialog__headerbtn {
  position: absolute !important;
  right: 44px !important;
  top: 55px !important;
  width: 43px !important;
  height: 43px !important;
  background: #0c275c !important;
  border-radius: 50% !important;
  border: 2px solid #01dff5 !important;
  color: #01dff5 !important;
  font-size: 25px !important;
  font-weight: bold;
}

.el-dialog {
  width: 1132px;
  height: 789px;
  background: url(../../assets/kuangD.png) no-repeat;
  background-size: 100% 100%;
  z-index: 1000000 !important;
  opacity: 1 !important;
}

.suggestPagetion {
  display: flex;
  justify-content: flex-end;
  .firstPage,
  .lastPage {
    span {
      cursor: pointer;
      display: block;
      text-align: center;
      line-height: 26px;
      width: 50px;
      height: 26px;
      background: rgba(255, 255, 255, 0);
      border: 1px solid #1a2d3d;
      border-radius: 4px;
      margin-top: 10px;
      color: #fff;
    }
  }
  .billPagenation {
    text-align: right;
    margin-top: 10px;
    margin-right: 85px;
    .btn-prev,
    .btn-next {
      background: rgba(255, 255, 255, 0);
      border: 1px solid #1a2d3d;
      border-radius: 4px;
      padding: 0 6px;
      font-family: PingFang SC;
      color: #fff !important;
    }
    .number,
    .more {
      background: rgba(255, 255, 255, 0) !important;
      border: 1px solid #1a2d3d;
      border-radius: 4px;
      font-family: PingFang SC;
      font-weight: 400;
      color: #fff !important;
    }
    .active {
      background: #0a3c6c !important;
      border: 1px solid #0a3c6c;
      border-radius: 4px;
    }
  }
}

.tablesss {
  margin: 0 auto;
  width: 900px;

  .el-table__body-wrapper {
    overflow: auto;
    background-color: #08101f;
  }
  .el-table tbody tr:hover > td {
    background-color: transparent !important;
  }
  .el-table--enable-row-transition .el-table__body td,
  .el-table .cell {
    background-color: transparent;
  }

  .warning-row {
    background: linear-gradient(
      90deg,
      rgba(2, 26, 47) 0%,
      rgb(2, 39, 70) 50%,
      rgba(2, 26, 47) 100%
    );
  }
  .normal-row {
    background-color: #02111d;
  }
  .el-table td.el-table__cell,
  .el-table th.el-table__cell.is-leaf {
    border-bottom: none;
  }
  .el-table::before {
    left: 0;
    bottom: 0;
    width: 100%;
    height: 0px;
  }
  .el-table th.el-table__cell {
    background-color: #031525;
  }
}
.el-table td.el-table__cell div {
  color: #d2eaf6;
}

.titleimg {
  width: 190px;
  height: 38px;
  margin-left: 88px;
  margin-top: 10px;
  img {
    width: 100%;
    height: 100%;
  }
}
.titleTopp {
  background: url(../../assets/<EMAIL>) no-repeat;
  background-size: 100% 100%;
  width: 900px;
  height: 121px;
  margin: 0 auto;
  margin-top: -20px;
  font-size: 24px;
  font-family: YouSheBiaoTiHei;
  font-weight: 400;
  color: #ffeeee;
  text-align: center;
  line-height: 110px;
  text-shadow: 0 0 20px #cddfff;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.video_cont {
  width: 1088px;
  height: auto;
  border: 2px solid red;
  border-radius: 6px;
  overflow: hidden;
  background-color: rgb(0, 0, 0) !important;
  box-sizing: border-box;
  .video {
    margin: 0 !important;
  }
}
.video_dialog {
  .el-dialog__headerbtn {
    z-index: 111111111 !important;
    position: absolute;
    right: 10px !important;
    top: 40px !important;
    border: 2px solid red !important;
  }
}
.center_btn {
  position: relative;
  .vjs-big-play-button {
    position: absolute !important;
    margin: 0 auto !important;
    left: 0 !important;
    top: 45% !important;
    right: 0 !important;
    // bottom: 0 !important;
  }
}
</style>
<template>
  <section class="right-part animate__fadeIn">
    <div class="enterprise" v-if="type === 5">
      <div class="enterprise-main">
        <div class="common-title box-title">
          <div class="title">{{ `企业信息` }}</div>
          <a-select
            style="width: 2rem"
            v-model="defaultEnterpriseValue"
            class="select-main"
            @change="heavilyPollutingEnterpriseChage"
          >
            <a-icon
              slot="suffixIcon"
              type="caret-down"
              style="color: rgba(0, 234, 255, 1); width: 14px; height: 8px"
            />
            <a-select-option
              :value="item.value"
              v-for="(item, index) in heavyPollutionEnterpriseList"
              :key="index"
            >
              {{ item.name }}</a-select-option
            >
          </a-select>
        </div>
        <div class="sub-title">
          <img src="@/assets/biaoti.png" alt class="title-img" />
        </div>
        <table border="1" class="heavy-pollution-table">
          <tr>
            <td class="table-name">企业名称</td>
            <td colspan="3" class="table-text">
              {{ currentEnterpriseInfor.name }}
            </td>
          </tr>
          <tr>
            <td class="table-name">行政区划</td>
            <td class="table-text">
              {{ currentEnterpriseInfor.district }}
            </td>
            <td class="table-name">企业类型</td>
            <td class="table-text">
              {{ currentEnterpriseInfor.type }}
            </td>
          </tr>
          <tr>
            <td class="table-name">单位地址</td>
            <td colspan="3" class="table-text">
              {{ currentEnterpriseInfor.address }}
            </td>
          </tr>
          <tr>
            <td class="table-name">企业状态</td>
            <td class="table-text">
              {{ currentEnterpriseInfor.status }}
            </td>
            <td class="table-name">行业类别</td>
            <td class="table-text">
              {{ currentEnterpriseInfor.kind }}
            </td>
          </tr>
          <tr>
            <td class="table-name">环保负责人</td>
            <td class="table-text">
              {{ currentEnterpriseInfor.principal }}
            </td>
            <td class="table-name">负责人电话</td>
            <td class="table-text">
              {{ currentEnterpriseInfor.principalTel }}
            </td>
          </tr>
          <tr>
            <td class="table-name">排污许可证</td>
            <td class="table-text">
              {{ currentEnterpriseInfor.licence }}
            </td>
            <td class="table-name">环境应急预案</td>
            <td class="table-text">
              {{ currentEnterpriseInfor.emergencyPlan }}
            </td>
          </tr>
        </table>
      </div>
      <template
        v-if="enterpriseText !== '水五(七)厂' && enterpriseText !== null"
      >
        <div class="pollute-main">
          <div class="common-title box-title">
            <div class="title">{{ `污染源监测` }}</div>
            <div class="update-time-pollution">
              {{ pollutionAllCount.updateTime }}更新
            </div>
          </div>
          <div class="sub-title" style="margin: 0">
            <img src="@/assets/biaoti.png" alt class="title-img" />
          </div>
          <div class="pollution-main">
            <div class="flex-box">
              <div>
                <div class="type-card">污染源排放年总量</div>
                <div>
                  <div class="name">废水排放总量：</div>
                  <div>
                    <span class="number">{{
                      pollutionAllCount.countOfWastewater
                    }}</span
                    >千克
                  </div>
                </div>
                <div>
                  <div class="name">废气排放总量：</div>
                  <div>
                    <span class="number">{{
                      pollutionAllCount.countOfGas
                    }}</span
                    >m³
                  </div>
                </div>
                <div>
                  <div class="name">废料排放总量：</div>
                  <div>
                    <span class="number">{{
                      pollutionAllCount.countOfWaste
                    }}</span
                    >吨
                  </div>
                </div>
              </div>
              <div>
                <div class="type-card">污染源排放监测</div>
                <div class="change-box">
                  <div
                    :class="{ active: curPollutionIndex == 0 }"
                    @click="changePollutionType(0)"
                  >
                    废水监测
                  </div>
                  <div
                    :class="{ active: curPollutionIndex == 1 }"
                    @click="changePollutionType(1)"
                  >
                    废气监测
                  </div>
                  <div
                    :class="{ active: curPollutionIndex == 2 }"
                    @click="changePollutionType(2)"
                  >
                    废料监测
                  </div>
                </div>
                <div>
                  <PollutionSource
                    :width="'2.46rem'"
                    :height="'2.2rem'"
                    :id="'PollutionSource'"
                    :propData="pollutionSourceList[curPollutionIndex]"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="discharge-main">
          <div class="common-title box-title title-flex">
            <div class="title">{{ `企业排污排行` }}</div>
            <div>
              <div
                :class="{ 'type-active': pollutionType === 's' }"
                @click="pollutionStationChange('s')"
              >
                废水
              </div>
              <div
                :class="{ 'type-active': pollutionType === 'e' }"
                @click="pollutionStationChange('e')"
              >
                废气
              </div>
              <div
                :class="{ 'type-active': pollutionType === 'w' }"
                @click="pollutionStationChange('w')"
              >
                废料
              </div>
            </div>
            <!-- <a-select
                          :defaultValue="pollutionType"
                          class="select-main"
                          @change="pollutionStationChange"
                        >
                          <a-icon
                            slot="suffixIcon"
                            type="caret-down"
                            style="color:rgba(0, 234, 255, 1);width:14px;height:8px;"
                          />
                          <a-select-option value="s" key="s">废水</a-select-option>
                          <a-select-option value="e" key="e">废气</a-select-option>
                          <a-select-option value="w" key="w">废料</a-select-option>
                        </a-select> -->
          </div>
          <div class="sub-title" style="margin: 0">
            <img src="@/assets/biaoti.png" alt class="title-img" />
          </div>
          <div class="table-data1">
            <div class="table-data-thead">
              <div class="tr">
                <div class="th">排行</div>
                <div class="th">企事业单位</div>
                <div class="th">
                  排放量({{
                    pollutionType === "s"
                      ? "kg"
                      : pollutionType == "e"
                      ? "m³"
                      : "吨"
                  }})
                </div>
                <div class="th">行业</div>
              </div>
            </div>
            <swiper
              :options="swiperOptionPollution"
              class="table-data-tbody"
              v-if="heavyPollution.length > 5"
            >
              <swiper-slide
                class="tr"
                v-for="(item, index) in heavyPollution"
                :key="index"
              >
                <div class="td">
                  <div class="tdAfter">{{ index + 1 }}</div>
                </div>
                <div class="td">
                  <a-tooltip>
                    <template slot="title">
                      {{ item.heavilyPollutingEnterpriseName }}
                    </template>
                    {{ item.heavilyPollutingEnterpriseNickname }}
                  </a-tooltip>
                </div>
                <div class="td" v-if="pollutionType == 's'">
                  {{
                    item.wastewaterDischarge ? item.wastewaterDischarge : "-"
                  }}
                </div>
                <div class="td" v-if="pollutionType == 'e'">
                  {{ item.exhaustEmissions ? item.exhaustEmissions : "-" }}
                </div>
                <div class="td" v-if="pollutionType == 'w'">
                  {{ item.wasteDischarge ? item.wasteDischarge : "-" }}
                </div>
                <div class="td">
                  <a-tooltip>
                    <template slot="title">
                      {{ item.industryCategory }}
                    </template>
                    {{ item.industryCategory }}
                  </a-tooltip>
                </div>
              </swiper-slide>
            </swiper>
            <div class="table-data-tbody" v-else>
              <div
                class="tr"
                v-for="(item, index) in heavyPollution"
                :key="index"
              >
                <div class="td">
                  <div class="tdAfter">{{ index + 1 }}</div>
                </div>
                <div class="td">
                  <a-tooltip>
                    <template slot="title">
                      {{ item.heavilyPollutingEnterpriseName }}
                    </template>
                    {{ item.heavilyPollutingEnterpriseNickname }}
                  </a-tooltip>
                </div>
                <div class="td" v-if="pollutionType == 's'">
                  {{ item.sewageDischarge ? item.sewageDischarge : "-" }}
                </div>
                <div class="td" v-if="pollutionType == 'e'">
                  {{ item.exhaustEmissions ? item.exhaustEmissions : "-" }}
                </div>
                <div class="td" v-if="pollutionType == 'w'">
                  {{ item.wasteDischarge ? item.wasteDischarge : "-" }}
                </div>
                <div class="td">
                  <a-tooltip>
                    <template slot="title">
                      {{ item.industryCategory }}
                    </template>
                    {{ item.industryCategory }}
                  </a-tooltip>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
      <template v-if="enterpriseText === '水五(七)厂'">
        <div>
          <div class="common-title box-title">
            <div class="title">{{ `监测信息` }}</div>
          </div>
          <div class="sub-title" style="margin: 0">
            <img src="@/assets/biaoti.png" alt class="title-img" />
          </div>
          <table border="1" class="pollutant-table">
            <thead>
              <tr>
                <th>指标项</th>
                <th>数值</th>
                <th>单位</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(item, index) in polluteDetails" :key="index">
                <td>{{ item.codeName }}</td>
                <td :style="{ color: '#76F573' }">
                  {{
                    item.value && item.value != 0.0 && item.value != 0
                      ? item.value
                      : "-"
                  }}
                </td>
                <td>{{ item.unit ? item.unit : "-" }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </template>
    </div>
    <div class="construction-plant" v-if="type === 0">
      <div style="height: 31%" class="pu-randa-l2r">
        <div class="common-title box-title">
          <div class="title">{{ `工地信息` }}</div>
          <a-select
            style="width: 2rem"
            v-model="curSite"
            class="select-main"
            @change="curSiteChange"
          >
            <a-icon
              slot="suffixIcon"
              type="caret-down"
              style="color: rgba(0, 234, 255, 1); width: 14px; height: 8px"
            />
            <a-select-option
              :value="item.companyId"
              v-for="(item, index) in companyList"
              :key="index"
            >
              {{ item.alias }}
            </a-select-option>
          </a-select>
        </div>
        <div class="sub-title">
          <img src="@/assets/biaoti.png" alt class="title-img" />
        </div>
        <table border="1" class="table-site">
          <tr>
            <td class="table-name">工程名称</td>
            <td colspan="3" class="table-text">
              {{
                companyList.length
                  ? companyList[curSiteIndex] &&
                    companyList[curSiteIndex].projectName
                  : ""
              }}
            </td>
          </tr>
          <tr
            v-if="
              companyList[curSiteIndex] &&
                companyList[curSiteIndex].monitorValue &&
                false
            "
          >
            <td class="table-name">PM10监测</td>
            <td colspan="3" class="table-text">
              <div class="PM10-detail">
                <span
                  :style="{
                    color: companyList[curSiteIndex].pollutantState
                      ? 'rgba(232, 14, 14, 1)'
                      : '',
                  }"
                  >{{ companyList[curSiteIndex].monitorValue }}</span
                >
                <span>ug/m³</span>
                <span
                  :style="{
                    background: companyList[curSiteIndex].pollutantState
                      ? 'rgba(232, 14, 14, 1)'
                      : '',
                  }"
                  >{{
                    companyList[curSiteIndex].pollutantState ? "超标" : "未超标"
                  }}</span
                >
              </div>
            </td>
          </tr>
          <tr>
            <td class="table-name">工程地址</td>
            <td colspan="3" class="table-text">
              {{
                companyList.length
                  ? companyList[curSiteIndex] &&
                    companyList[curSiteIndex].constructionSiteAddress
                  : ""
              }}
            </td>
          </tr>
          <tr>
            <td class="table-name">工程状态</td>
            <td class="table-text">
              {{
                companyList.length
                  ? companyList[curSiteIndex] &&
                    companyList[curSiteIndex].projectState
                    ? "在建"
                    : ""
                  : ""
              }}
            </td>
            <td class="table-name">工程类别</td>
            <td class="table-text">
              {{
                companyList.length
                  ? companyList[curSiteIndex] &&
                    companyList[curSiteIndex].projectType
                  : ""
              }}
            </td>
          </tr>
          <tr>
            <td class="table-name">备案时间</td>
            <td class="table-text">
              {{
                companyList.length && companyList[curSiteIndex].reviewDate
                  ? companyList[curSiteIndex].reviewDate.substr(
                      0,
                      companyList[curSiteIndex].reviewDate.length - 3
                    )
                  : ""
              }}
            </td>
            <td class="table-name">备案号</td>
            <td class="table-text">
              {{
                companyList.length
                  ? companyList[curSiteIndex].safeRecordNum
                  : ""
              }}
            </td>
          </tr>
          <tr>
            <td class="table-name">施工单位</td>
            <td colspan="3" class="table-text">
              {{
                companyList.length
                  ? companyList[curSiteIndex].constructionCompanyName
                  : ""
              }}
            </td>
          </tr>
          <tr>
            <td class="table-name">负责人</td>
            <td class="table-text">
              {{
                companyList.length ? companyList[curSiteIndex].principal : ""
              }}
            </td>
            <td class="table-name">联系方式</td>
            <td class="table-text">
              {{
                companyList.length ? companyList[curSiteIndex].linkPhone : ""
              }}
            </td>
          </tr>
        </table>
      </div>
      <div v-if="!isGD" :style="`height: 30%;`" class="pu-randa-l2r">
        <div class="video-main polluted-video">
          <div class="common-title box-title">
            <div class="title">工地PM10监测趋势</div>
          </div>

          <div class="sub-title" style="margin: 0">
            <img src="@/assets/biaoti.png" alt class="title-img" />
            <real-time-monitor
              :id="'realTimeMonitor' + new Date().getTime()"
              :width="'4rem'"
              :height="'2.35rem'"
              :propData="realTimeData"
              :smooth="true"
            />
          </div>
        </div>
      </div>
      <!-- 引领性工地 -->
      <div v-if="isGD" style="height: 30%" class="pu-randa-r2l" key="gd">
        <div class="video-main polluted-video">
          <div class="common-title box-title">
            <div class="title">{{ `今日违规统计` }}</div>
            <div class="unit" style="color：#5E85A8" @click="getMore">
              更多 >>
            </div>
          </div>

          <div class="sub-title" style="margin: 0">
            <img src="@/assets/biaoti.png" alt class="title-img" />
            <div v-if="projectData" class="gongditable">
              <div class="table-header table-header1">
                <div>违规现象</div>
                <!-- <div>持续时长</div> -->
                <div>违规次数</div>
              </div>
              <swiper :options="swiperOptionAlert" style="height: 2rem">
                <swiper-slide
                  class="table-body1"
                  v-for="(val, key, index) in projectData"
                  :key="index"
                >
                  <div class="tatle_img">
                    <img :src="nan" alt="" /> {{ key }}
                  </div>
                  <!-- <div class="shichang">
                    累计{{ item.alarmValue || '--' }}秒
                  </div> -->
                  <div class="weigui">
                    <span>{{ val || 0 }}</span> 次
                  </div>
                </swiper-slide>
              </swiper>
            </div>
            <div v-if="!projectData" class="no-data">今日暂无违规统计</div>
          </div>
        </div>
      </div>
      <!-- 引领性工地 -->
      <div v-if="!isGD" style="height: 39%" class="pu-randa-r2l">
        <div class="video-main polluted-video">
          <div class="common-title box-title">
            <div class="title">{{ `工地实时告警` }}</div>
            <div class="unit">单位：ug/m³</div>
          </div>

          <div class="sub-title" style="margin: 0">
            <img src="@/assets/biaoti.png" alt class="title-img" />
            <div v-if="companyAlarmList.length" class="gongditable">
              <div class="table-header">
                <div>工地名称</div>
                <div>PM10告警值</div>
                <div>发生时间</div>
              </div>
              <swiper :options="swiperOptionAlert" style="height: 2rem">
                <swiper-slide
                  class="table-body"
                  v-for="(item, index) in companyAlarmList"
                  :key="index"
                >
                  <div :title="item.alarmContent">{{ item.alarmContent }}</div>
                  <div>{{ item.alarmValue }}</div>
                  <div>{{ item.alarmTime }}</div>
                </swiper-slide>
              </swiper>
            </div>
            <div v-else class="no-data">暂无告警信息</div>
          </div>
        </div>
      </div>
      <div v-if="isGD" key="gd" :style="`height: 39%;`" class="pu-randa-l2r">
        <div class="video-main polluted-video">
          <div class="common-title box-title">
            <div class="title">{{ `视频监控` }}</div>
            <div class="unit" style="color：#5E85A8" @click="getMore1">
              更多 >>
            </div>
          </div>

          <div class="video_coont">
            <div v-if="VertigoVideo" class="video_coont1">
              <LivePlayer
                ref="videoGD"
                v-if="VertigoVideo"
                :video-url="VertigoVideo"
                fluent
                autoplay
                live
                style="height: 100%; width: 100%"
                :stretch="true"
                :hasaudio="false"
              />
            </div>
            <div v-if="!VertigoVideo" class="no-data">暂无视频监控</div>
          </div>
        </div>
      </div>
    </div>
    <div class="construction-plant" v-if="type === 6">
      <div style="height: 25%" class="pu-randa-l2r">
        <div class="common-title box-title">
          <div class="title">{{ `站点信息` }}</div>
          <a-select
            style="width: 1.1rem"
            v-model="noiseType"
            class="select-main"
          >
            <a-icon
              slot="suffixIcon"
              type="caret-down"
              style="color: rgba(0, 234, 255, 1); width: 14px; height: 8px"
            />
            <a-select-option
              :value="item.id"
              v-for="(item, index) in noiseTypeList"
              :key="index"
              :disabled="item.disabled"
            >
              {{ item.name }}
            </a-select-option>
          </a-select>
          <a-select
            style="width: 2rem"
            v-model="curSite"
            class="select-main"
            @change="curSiteChange"
          >
            <a-icon
              slot="suffixIcon"
              type="caret-down"
              style="color: rgba(0, 234, 255, 1); width: 14px; height: 8px"
            />
            <a-select-option
              :value="item.companyId"
              v-for="(item, index) in companyList"
              :key="index"
            >
              {{ item.stationName }}
            </a-select-option>
          </a-select>
        </div>
        <div class="sub-title">
          <img src="@/assets/biaoti.png" alt class="title-img" />
        </div>
        <table border="1" class="table-site">
          <tr>
            <td class="table-name">站点名称</td>
            <td colspan="3" class="table-text">
              {{
                companyList.length ? companyList[curSiteIndex].stationName : ""
              }}
            </td>
          </tr>
          <tr>
            <td class="table-name">所在街道</td>
            <td colspan="3" class="table-text">
              {{
                companyList.length ? companyList[curSiteIndex].streetName : ""
              }}
            </td>
          </tr>
          <tr>
            <td class="table-name">详细地址</td>
            <td colspan="3" class="table-text">
              {{ companyList.length ? companyList[curSiteIndex].address : "" }}
            </td>
          </tr>
          <tr>
            <td class="table-name">运维公司</td>
            <td class="table-text">
              {{
                companyList.length
                  ? companyList[curSiteIndex].principalCompany
                  : ""
              }}
            </td>
            <td class="table-name">运维号码</td>
            <td class="table-text">
              {{
                companyList.length
                  ? companyList[curSiteIndex].principalPhone
                  : ""
              }}
            </td>
          </tr>
        </table>
      </div>
      <div style="height: 36%" class="pu-randa-r2l">
        <div class="video-main polluted-video">
          <div class="common-title box-title" style="position: relative">
            <div class="title">{{ `噪声监测趋势` }}</div>
            <div class="title_select">
              <div
                class="title_select_item"
                :class="
                  lineDataType === 'hour' ? 'title_select_item_select' : ''
                "
                style="margin-right: 0.05rem"
                @click="lineDataType = 'hour'"
              >
                小时数据
              </div>
              <div
                class="title_select_item"
                :class="
                  lineDataType === 'day' ? 'title_select_item_select' : ''
                "
                @click="lineDataType = 'day'"
              >
                天数据
              </div>
            </div>
          </div>
          <div class="sub-title" style="margin: 0">
            <img src="@/assets/biaoti.png" alt class="title-img" />
            <div class="sub_tabs" v-if="subTabs.length">
              <div
                v-for="tab in subTabs"
                :key="tab"
                class="tab"
                @click="subTabName = tab"
                :class="{ active: tab === subTabName }"
              >
                {{ tab }}
              </div>
            </div>
            <noiseLine
              v-loading="LineLoading"
              element-loading-text="拼命加载中"
              element-loading-spinner="el-icon-loading"
              element-loading-background="rgba(0, 11, 58, 0.8)"
              :propData="noiseLineData[subTabName] || []"
              :subTabName="subTabName"
              :standard="standardObj[subTabName]"
              :id="'noiseLinecc'"
              :width="'4rem'"
              :height="'2.25rem'"
              :smooth="true"
            />
          </div>
        </div>
      </div>
      <div style="height: 39%" class="pu-randa-l2r">
        <div class="video-main polluted-video">
          <div class="common-title box-title">
            <div class="title">{{ `昼夜达标率` }}</div>
          </div>
          <div class="sub-title" style="margin: 0">
            <img src="@/assets/biaoti.png" alt class="title-img" />
            <!-- <div class="no-data">暂无告警信息</div> -->
            <noiseBar
              v-loading="PieLoading"
              element-loading-text="拼命加载中"
              element-loading-spinner="el-icon-loading"
              element-loading-background="rgba(0, 11, 58, 0.8)"
              :propData="noiseBarData"
              :id="'noiseBarcc'"
              :width="'4rem'"
              :height="'2.45rem'"
            ></noiseBar>
          </div>
        </div>
      </div>
    </div>
    <div class="construction-plant" v-if="type === 7">
      <div style="height: 25%" class="pu-randa-r2l">
        <div class="common-title box-title">
          <div class="title">{{ `站点信息` }}</div>
          <a-select
            style="width: 2rem"
            v-model="curSite"
            class="select-main"
            @change="curSiteChange"
          >
            <a-icon
              slot="suffixIcon"
              type="caret-down"
              style="color: rgba(0, 234, 255, 1); width: 14px; height: 8px"
            />
            <a-select-option
              :value="item.companyId"
              v-for="(item, index) in companyList"
              :key="index"
            >
              {{ item.enterpriseName }}
            </a-select-option>
          </a-select>
        </div>
        <div class="sub-title">
          <img src="@/assets/biaoti.png" alt class="title-img" />
        </div>
        <table border="1" class="table-site">
          <tr>
            <td class="table-name">站点名称</td>
            <td colspan="3" class="table-text">
              {{
                companyList.length
                  ? companyList[curSiteIndex].enterpriseName
                  : ""
              }}
            </td>
          </tr>
          <tr>
            <td class="table-name">所在街道</td>
            <td colspan="3" class="table-text">
              {{
                companyList.length ? companyList[curSiteIndex].streetName : ""
              }}
            </td>
          </tr>
          <tr>
            <td class="table-name">详细地址</td>
            <td colspan="3" class="table-text">
              {{ companyList.length ? companyList[curSiteIndex].address : "" }}
            </td>
          </tr>
          <tr>
            <td class="table-name">联系人</td>
            <td class="table-text">
              {{ companyList.length ? companyList[curSiteIndex].contact : "" }}
            </td>
            <td class="table-name">联系电话</td>
            <td class="table-text">
              {{
                companyList.length ? companyList[curSiteIndex].contactPhone : ""
              }}
            </td>
          </tr>
        </table>
      </div>
      <div style="height: 36%" class="pu-randa-l2r">
        <div class="video-main polluted-video">
          <div class="common-title box-title" style="position: relative">
            <div class="title">{{ `用电量监测趋势` }}</div>
            <div class="title_select">
              <div
                class="title_select_item"
                :class="
                  lineDataType === 'hour' ? 'title_select_item_select' : ''
                "
                style="margin-right: 0.05rem"
                @click="lineDataType = 'hour'"
              >
                小时数据
              </div>
              <div
                class="title_select_item"
                :class="
                  lineDataType === 'day' ? 'title_select_item_select' : ''
                "
                @click="lineDataType = 'day'"
              >
                天数据
              </div>
            </div>
          </div>
          <div class="sub-title" style="margin: 0">
            <img src="@/assets/biaoti.png" alt class="title-img" />
            <electricityLine
              v-loading="LineLoading"
              element-loading-text="拼命加载中"
              element-loading-spinner="el-icon-loading"
              element-loading-background="rgba(0, 11, 58, 0.8)"
              :propData="electricityLineData"
              :id="'noiseLinec'"
              :width="'4rem'"
              :height="'2.65rem'"
              :smooth="true"
            />
          </div>
        </div>
      </div>
      <div style="height: 39%" class="pu-randa-r2l">
        <div class="video-main polluted-video">
          <div class="common-title box-title">
            <div class="title">{{ `用电达标率` }}</div>
          </div>
          <div class="sub-title" style="margin: 0">
            <img src="@/assets/biaoti.png" alt class="title-img" />
            <!-- <div class="no-data">暂无告警信息</div> -->
            <electricityBar
              v-loading="PieLoading"
              element-loading-text="拼命加载中"
              element-loading-spinner="el-icon-loading"
              element-loading-background="rgba(0, 11, 58, 0.8)"
              :propData="electricityBarData"
              :id="'noiseBarc'"
              :width="'4rem'"
              :height="'2.45rem'"
            ></electricityBar>
          </div>
        </div>
      </div>
    </div>
    <div class="construction-plant" v-if="type === 8">
      <div style="height: 25%" class="pu-randa-l2r">
        <div class="common-title box-title">
          <div class="title">{{ `站点信息` }}</div>
          <a-select style="width: 1rem" v-model="wasteType" class="select-main">
            <a-icon
              slot="suffixIcon"
              type="caret-down"
              style="color: rgba(0, 234, 255, 1); width: 14px; height: 8px"
            />
            <a-select-option
              :value="item.id"
              v-for="(item, index) in wasteTypeList"
              :key="index"
              :disabled="item.disabled"
            >
              {{ item.name }}
            </a-select-option>
          </a-select>
          <a-select
            style="width: 1.8rem"
            v-model="curSite"
            class="select-main"
            @change="curSiteChange"
          >
            <a-icon
              slot="suffixIcon"
              type="caret-down"
              style="color: rgba(0, 234, 255, 1); width: 14px; height: 8px"
            />
            <a-select-option
              :value="item.companyId"
              v-for="(item, index) in companyList"
              :key="index"
            >
              {{ item.deviceName }}
            </a-select-option>
          </a-select>
        </div>
        <div class="sub-title">
          <img src="@/assets/biaoti.png" alt class="title-img" />
        </div>
        <table border="1" class="table-site">
          <tr>
            <td class="table-name">站点名称</td>
            <td colspan="3" class="table-text">
              {{
                companyList.length ? companyList[curSiteIndex].deviceName : ""
              }}
            </td>
          </tr>
          <tr>
            <td class="table-name">所在街道</td>
            <td colspan="3" class="table-text">
              {{
                companyList.length
                  ? companyList[curSiteIndex].streetName || "--"
                  : ""
              }}
            </td>
          </tr>
          <tr>
            <td class="table-name">详细地址</td>
            <td colspan="3" class="table-text">
              {{
                companyList.length
                  ? companyList[curSiteIndex].address || "--"
                  : ""
              }}
            </td>
          </tr>
          <tr>
            <td class="table-name">联系人</td>
            <td class="table-text">
              {{
                companyList.length
                  ? companyList[curSiteIndex].principal || "--"
                  : ""
              }}
            </td>
            <td class="table-name">联系电话</td>
            <td class="table-text">
              {{
                companyList.length
                  ? companyList[curSiteIndex].connectPhone || "--"
                  : ""
              }}
            </td>
          </tr>
        </table>
      </div>
      <div style="height: 36%" class="pu-randa-r2l">
        <div class="video-main polluted-video">
          <div class="common-title box-title" style="position: relative">
            <div class="title">{{ `监测趋势` }}</div>
            <a-select
              style="width: 1rem; text-align: center"
              v-model="wasteLineDataType"
              class="select-main"
            >
              <a-icon
                slot="suffixIcon"
                type="caret-down"
                style="color: rgba(0, 234, 255, 1); width: 14px; height: 8px"
              />
              <a-select-option :value="7"> 近七天 </a-select-option>
              <a-select-option :value="30"> 近一个月 </a-select-option>
            </a-select>
          </div>
          <div class="sub-title" style="margin: 0">
            <img src="@/assets/biaoti.png" alt class="title-img" />
            <div
              class="sub_tabs"
              v-if="wastesubTabs.length"
              style="margin-top: 0.1rem"
            >
              <div
                v-for="tab in wastesubTabs"
                :key="tab"
                class="tab"
                @click="wastesubTabName = tab"
                :class="{ active: tab === wastesubTabName }"
              >
                {{ tab }}
              </div>
            </div>
            <solidWasteLine
              v-loading="LineLoading"
              element-loading-text="拼命加载中"
              element-loading-spinner="el-icon-loading"
              element-loading-background="rgba(0, 11, 58, 0.8)"
              :propData="wasteLineData[wastesubTabNameMap[wastesubTabName]]"
              :unit="wasteUnit"
              :dataType="wasteLineDataType"
              :subTabName="wastesubTabName"
              :id="'SolidWasteLine'"
              :width="'4rem'"
              :height="'2.45rem'"
              :smooth="true"
            />
          </div>
        </div>
      </div>
      <div style="height: 39%" class="pu-randa-l2r">
        <div class="video-main polluted-video">
          <div class="common-title box-title">
            <div class="title">{{ `固废占比率` }}</div>
            <el-date-picker
              v-model="wasteSelDate"
              value-format="yyyy-MM"
              format="yyyy年MM月"
              type="month"
              size="small"
              popper-class="picker_class_waste"
              prefix-icon="null"
              :clearable="true"
              :editable="false"
              :append-to-body="false"
              :picker-options="pickerOptions"
              placeholder="选择日期"
              style="width: 1.2rem"
              class="waste_date_picker"
            >
            </el-date-picker>
            <a-select
              style="width: 0.8rem"
              v-model="wastePieTabName"
              class="select-main"
            >
              <a-icon
                slot="suffixIcon"
                type="caret-down"
                style="color: rgba(0, 234, 255, 1); width: 14px; height: 8px"
              />
              <a-select-option value="stock"> 贮存量 </a-select-option>
              <a-select-option value="in"> 产生量 </a-select-option>
              <a-select-option value="out"> 转运量 </a-select-option>
            </a-select>
          </div>
          <div class="sub-title" style="margin: 0">
            <img src="@/assets/biaoti.png" alt class="title-img" />
            <!-- <div class="no-data">暂无告警信息</div> -->
            <solidWastePie
              v-loading="PieLoading"
              element-loading-text="拼命加载中"
              element-loading-spinner="el-icon-loading"
              element-loading-background="rgba(0, 11, 58, 0.8)"
              :echartData="wastePieDate[wastePieTabName]"
              :id="'wastePie'"
            ></solidWastePie>
          </div>
        </div>
      </div>
    </div>
    <div class="construction-plant" v-if="type === 9">
      <div style="height: 25%" class="pu-randa-r2l">
        <div class="common-title box-title">
          <div class="title">{{ `站点信息` }}</div>
          <a-select
            style="width: 2rem"
            v-model="curSite"
            class="select-main"
            @change="curSiteChange"
          >
            <a-icon
              slot="suffixIcon"
              type="caret-down"
              style="color: rgba(0, 234, 255, 1); width: 14px; height: 8px"
            />
            <a-select-option
              :value="item.companyId"
              v-for="(item, index) in companyList"
              :key="index"
            >
              {{ item.heavilyPollutingEnterpriseName }}
            </a-select-option>
          </a-select>
        </div>
        <div class="sub-title">
          <img src="@/assets/biaoti.png" alt class="title-img" />
        </div>
        <table border="1" class="table-site">
          <tr>
            <td class="table-name">站点名称</td>
            <td colspan="3" class="table-text">
              {{
                companyList.length
                  ? companyList[curSiteIndex].heavilyPollutingEnterpriseName
                  : ""
              }}
            </td>
          </tr>
          <tr>
            <td class="table-name">所在街道</td>
            <td colspan="3" class="table-text">
              {{
                companyList.length ? companyList[curSiteIndex].streetName : ""
              }}
            </td>
          </tr>
          <tr>
            <td class="table-name">详细地址</td>
            <td colspan="3" class="table-text">
              {{
                companyList.length
                  ? companyList[curSiteIndex].heavilyPollutingEnterpriseAddress
                  : ""
              }}
            </td>
          </tr>
          <tr>
            <td class="table-name">联系人</td>
            <td class="table-text">
              {{
                companyList.length ? companyList[curSiteIndex].principal : ""
              }}
            </td>
            <td class="table-name">联系电话</td>
            <td class="table-text">
              {{
                companyList.length
                  ? companyList[curSiteIndex].principalPhoneNumber
                  : ""
              }}
            </td>
          </tr>
        </table>
      </div>
      <div style="height: 36%" class="pu-randa-l2r">
        <div class="video-main polluted-video">
          <div class="common-title box-title" style="position: relative">
            <div class="title">{{ `污水排放监测趋势` }}</div>
            <a-select
              style="width: 1rem"
              v-model="HCLineType"
              class="select-main"
            >
              <a-icon
                slot="suffixIcon"
                type="caret-down"
                style="color: rgba(0, 234, 255, 1); width: 14px; height: 8px"
              />
              <a-select-option value="hour"> 小时数据 </a-select-option>
              <a-select-option value="day"> 天数据 </a-select-option>
            </a-select>
          </div>
          <div class="sub-title" style="margin: 0">
            <img src="@/assets/biaoti.png" alt class="title-img" />
            <div
              class="sub_tabs1"
              v-if="HCsubTabs && HCsubTabs.length"
              style="margin-top: 0.1rem"
            >
              <div
                v-for="tab in HCsubTabs"
                :key="tab"
                class="tab"
                @click="HCsubTabName = tab"
                :class="{
                  active: tab === HCsubTabName,
                  mult: tab === '化学需氧量',
                  else: tab !== '化学需氧量',
                }"
                :title="tab"
              >
                {{ tab }}
              </div>
            </div>
            <heavyCorruptLine
              v-loading="LineLoading"
              element-loading-text="拼命加载中"
              element-loading-spinner="el-icon-loading"
              element-loading-background="rgba(0, 11, 58, 0.8)"
              :propData="HCLineData[HCsubTabName] || []"
              :id="'HCLineC'"
              :subTabName="HCsubTabName"
              :width="'4rem'"
              :height="'2.45rem'"
              :smooth="true"
            />
          </div>
        </div>
      </div>
      <div style="height: 39%" class="pu-randa-r2l">
        <div class="video-main polluted-video">
          <div class="common-title box-title">
            <div class="title">{{ `污水排放占比率` }}</div>
            <a-select
              style="width: 1.2rem"
              v-model="HCPieTabName"
              class="select-main"
            >
              <a-icon
                slot="suffixIcon"
                type="caret-down"
                style="color: rgba(0, 234, 255, 1); width: 14px; height: 8px"
              />
              <a-select-option
                v-for="item in companyList[curSiteIndex]
                  ? companyList[curSiteIndex].pointList
                  : []"
                :value="item.pointId"
                :key="item.pointId"
              >
                {{ item.pointName }}
              </a-select-option>
            </a-select>
          </div>
          <div class="sub-title" style="margin: 0">
            <img src="@/assets/biaoti.png" alt class="title-img" />
            <!-- <div class="no-data">暂无告警信息</div> -->
            <div id="HPBox">
              <template
                v-if="
                  (companyList[curSiteIndex]
                    ? companyList[curSiteIndex].pointList
                    : []
                  ).length
                "
              >
                <heavyCorruptPie
                  v-loading="PieLoading"
                  element-loading-text="拼命加载中"
                  element-loading-spinner="el-icon-loading"
                  element-loading-background="rgba(0, 11, 58, 0.8)"
                  :echartData="HCPdata"
                  style="margin-top: 0.12rem"
                ></heavyCorruptPie>
              </template>
              <template v-else>
                <div style="font-size: 0.2rem; margin: 1.3rem 1.5rem">
                  暂无数据
                </div>
              </template>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="construction-plant" v-if="type === 13">
      <div style="height: 25%" class="pu-randa-r2l">
        <div class="common-title box-title">
          <div class="title">{{ `站点信息` }}</div>
          <a-select
            style="width: 2rem"
            v-model="curSite"
            class="select-main"
            @change="curSiteChange"
          >
            <a-icon
              slot="suffixIcon"
              type="caret-down"
              style="color: rgba(0, 234, 255, 1); width: 14px; height: 8px"
            />
            <a-select-option
              :value="item.companyId"
              v-for="(item, index) in companyList"
              :key="index"
            >
              {{ item.deviceName }}
            </a-select-option>
          </a-select>
        </div>
        <div class="sub-title">
          <img src="@/assets/biaoti.png" alt class="title-img" />
        </div>
        <table border="1" class="table-site">
          <tr>
            <td class="table-name">站点名称</td>
            <td colspan="3" class="table-text">
              {{
                companyList.length
                  ? companyList[curSiteIndex].deviceName || "--"
                  : "--"
              }}
            </td>
          </tr>
          <tr>
            <td class="table-name">所在街道</td>
            <td colspan="3" class="table-text">
              {{
                companyList.length
                  ? companyList[curSiteIndex].streetName || "--"
                  : "--"
              }}
            </td>
          </tr>
          <tr>
            <td class="table-name">详细地址</td>
            <td colspan="3" class="table-text">
              {{
                companyList.length
                  ? companyList[curSiteIndex].address || "--"
                  : "--"
              }}
            </td>
          </tr>
          <tr>
            <td class="table-name">联系人</td>
            <td class="table-text">
              {{ "--" }}
            </td>
            <td class="table-name">联系电话</td>
            <td class="table-text">
              {{ "--" }}
            </td>
          </tr>
        </table>
      </div>
      <div style="height: 36%" class="pu-randa-l2r">
        <div class="video-main polluted-video">
          <div class="common-title box-title" style="position: relative">
            <div class="title">{{ `监测趋势` }}</div>
            <div style="display: flex; justify-content: space-between">
              <div class="lian" @click="showL">抓拍证据链</div>
              <!-- <a-select
                style="width: 1rem"
                v-model="GTLineType"
                class="select-main"
              >
                <a-icon
                  slot="suffixIcon"
                  type="caret-down"
                  style="color: rgba(0, 234, 255, 1); width: 14px; height: 8px"
                />
                <a-select-option value="hour"> 小时数据 </a-select-option>
                <a-select-option value="day"> 天数据 </a-select-option>
              </a-select> -->
            </div>
          </div>
          <div class="sub-title" style="margin: 0">
            <img src="@/assets/biaoti.png" alt class="title-img" />
            <div class="sub_tabs" style="margin-top: 0.1rem">
              <div
                v-for="tab in GTsubTabs"
                :key="tab"
                class="tab"
                @click="GTsubTabName = tab"
                :class="{ active: GTsubTabName == tab }"
              >
                {{ tab }}
              </div>
            </div>
            <blackGasLine
              v-loading="LineLoading"
              element-loading-text="拼命加载中"
              element-loading-spinner="el-icon-loading"
              element-loading-background="rgba(0, 11, 58, 0.8)"
              :propData="GTLineData[GTsubTabName]"
              :id="'GTLine'"
              :subTabName="GTsubTabName"
              :GTLineType="GTLineType"
              :width="'4rem'"
              :height="'2.45rem'"
              :smooth="true"
            />
          </div>
        </div>
      </div>
      <div style="height: 39%" class="pu-randa-r2l">
        <div class="video-main polluted-video">
          <div class="common-title box-title">
            <div class="title">{{ `车辆占比率` }}</div>
            <a-select
              style="width: 1.2rem"
              v-model="GTPieTabName"
              class="select-main"
            >
              <a-icon
                slot="suffixIcon"
                type="caret-down"
                style="color: rgba(0, 234, 255, 1); width: 14px; height: 8px"
              />
              <a-select-option
                v-for="item in GTPieTabList"
                :value="item.id"
                :key="item.id"
              >
                {{ item.name }}
              </a-select-option>
            </a-select>
          </div>
          <div class="sub-title" style="margin: 0">
            <img src="@/assets/biaoti.png" alt class="title-img" />
            <!-- <div class="no-data">暂无告警信息</div> -->
            <div id="HPBox">
              <template>
                <blackGasPie
                  v-loading="PieLoading"
                  element-loading-text="拼命加载中"
                  element-loading-spinner="el-icon-loading"
                  element-loading-background="rgba(0, 11, 58, 0.8)"
                  :echartData="GTPiedata[GTPieTabName]"
                  style="margin-top: 0.12rem"
                ></blackGasPie>
              </template>
              <!-- <template
                v-else
              >
                <div style="font-size:0.20rem;margin:1.3rem 1.5rem;">暂无数据</div>
              </template> -->
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="construction-plant"
      v-if="type === 10 && randaLocation.lat"
      :style="{ 'background-color': 'rgba(4, 36, 62, 0.8)' }"
    >
      <div style="height: 30%" class="pu-randa-l2r">
        <div class="video-main polluted-video">
          <div class="common-title box-title" style="position: relative">
            <div class="title">监测趋势</div>
          </div>
          <div class="sub-title" style="margin: 0">
            <img src="@/assets/biaoti.png" alt class="title-img" />
            <randaLine
              :propData="RDLineData"
              :id="'RDLine'"
              :subTabName="RDSubName"
              :width="'4rem'"
              :height="'2.15rem'"
              :smooth="true"
            />
          </div>
        </div>
      </div>
      <div style="height: 35%" class="pu-randa-r2l">
        <div class="common-title box-title">
          <div class="title">{{ `涉污企业指纹库（1公里）` }}</div>
        </div>
        <div class="sub-title">
          <img src="@/assets/biaoti.png" alt class="title-img" />
        </div>
        <!-- <div @mouseenter="swiperMouseEnter" @mouseleave="swiperMouseLeave" v-if="RDFigList.length&&RDFigList.length>3">
          <swiper
            ref="fingerprintSwiper"
            :options="swiperOptionRound"
            class="randa_left_top"
            style="height: 2.7rem"
            @click.native="toViewMore"
          >
            <swiper-slide
              v-for="(item, index) in RDFigList"
              :key="index"
            >
              <div class="item">
                <div class="more" :data-item="JSON.stringify(item)">更多>></div>
                <div style="width:58%;margin-right:5px;">
                  <div class="title" :title="item.stationName"  :data-item="JSON.stringify(item)">{{item.stationName||'--'}}</div>
                  <div class="time" :data-item="JSON.stringify(item)">所在街道：{{item.streetName||'--'}}</div>
                </div>
                <div class="area" :data-item="JSON.stringify(item)"><span class="shuzi" :data-item="JSON.stringify(item)">{{(item.distance.toFixed(3)||item.distance==0)||'--'}}</span><span style="font-size:18px;" class="danwei" :data-item="JSON.stringify(item)">km</span></div>
              </div>
            </swiper-slide>
          </swiper>
        </div> -->
        <div
          v-if="RDFigList.length"
          class="randa_left_top"
          style="height: 2.7rem; margin-top: 0.2rem"
        >
          <div
            v-for="(item, index) in RDFigList"
            :key="index"
            style="height: 0.8rem"
          >
            <div class="item" @click="toViewMore(item)">
              <div class="more" @click="toViewMore(item)">更多>></div>
              <div style="width: 58%; margin-right: 5px">
                <div class="title" :title="item.stationName">
                  {{ item.stationName || "--" }}
                </div>
                <div class="time">所在街道：{{ item.streetName || "--" }}</div>
              </div>
              <div class="area">
                <span class="shuzi">{{
                  item.distance.toFixed(2) || item.distance == 0 || "--"
                }}</span
                ><span
                  style="font-size: 18px"
                  class="danwei"
                  :data-item="JSON.stringify(item)"
                  >km</span
                >
              </div>
            </div>
          </div>
        </div>
        <div
          v-if="!RDFigList.length"
          style="margin: 0.7rem 0 0; font-size: 0.2rem; text-align: center"
        >
          <img src="../../assets/heavy-corrupt/<EMAIL>" alt="" />
          <br />
          暂无数据
        </div>
      </div>
      <div style="height: 35%" class="pu-randa-l2r">
        <div class="common-title box-title">
          <div class="title">{{ `污染点位告警历史` }}</div>
        </div>
        <div class="sub-title">
          <img src="@/assets/biaoti.png" alt class="title-img" />
        </div>
        <div class="Rd_his_title">
          <div
            v-for="(item, i) in RDHistoryTitle"
            :key="i"
            class="Rd_his_title_item"
          >
            <img :src="item.icon" alt="" />
            <div>
              <div class="Rd_his_title_item_label">{{ item.label }}</div>
              <div class="Rd_his_title_item_count">{{ item.count }}</div>
            </div>
          </div>
        </div>
        <!-- <swiper :options="swiperOptionRound" style="height: 1.65rem" class="Rd_his_list" v-if="RDHistoryList.length&&RDHistoryList.length>3">
          <swiper-slide
            v-for="(item, index) in RDHistoryList"
            :key="index"
          >
            <div class="Rd_his_list_item">
              <img src="../../assets/randa/<EMAIL>" alt="">
              <div class="Rd_his_list_item_time">{{item.alarmTime.slice(0,16)||'--'}}</div>
              <div class="Rd_his_list_item_split">------------</div>
              <div class="Rd_his_list_item_alarm">{{item.name||'--'}}：<span style="color:#fff;font-size:18px;">{{item.alarmValue.toFixed(2)||'--'}}</span>{{item.unit||''}}</div>
            </div>
          </swiper-slide>
        </swiper> -->
        <div
          style="height: 1.65rem"
          class="Rd_his_list"
          v-if="RDHistoryList.length"
        >
          <div
            v-for="(item, index) in RDHistoryList"
            :key="index"
            style="height: 0.55rem"
          >
            <div class="Rd_his_list_item">
              <img src="../../assets/randa/<EMAIL>" alt="" />
              <div class="Rd_his_list_item_time">
                {{ item.alarmTime.slice(0, 16) || "--" }}
              </div>
              <div class="Rd_his_list_item_split">------------</div>
              <div class="Rd_his_list_item_alarm">
                {{ item.name || "--" }}：<span
                  style="color: #fff; font-size: 18px"
                  >{{ item.alarmValue.toFixed(2) || "--" }}</span
                >{{ item.unit || "" }}
              </div>
            </div>
          </div>
        </div>
        <div
          v-if="!RDHistoryList.length"
          style="margin: 0.5rem 0 0; font-size: 0.2rem; text-align: center"
        >
          <img src="../../assets/heavy-corrupt/<EMAIL>" alt="" />
          <br />
          暂无数据
        </div>
      </div>
    </div>
    <div class="randaSel" v-if="type === 10">
      <div
        v-for="item in RDSubNameList"
        :key="item"
        class="randaSel_item"
        :class="RDSubName == item ? 'randaSel_item_active' : ''"
        @click="RDSubName = item"
      >
        {{ item }}
      </div>
    </div>
    <!-- 弹框部分-->
    <a-modal
      :visible="visible"
      class="ant-modal-hpe"
      :footer="null"
      :destroyOnClose="true"
      @cancel="handleCancel"
    >
      <div class="title">
        {{ randaModalObj.stationName }}
        <div
          style="
            font-size: 0.16rem;
            font-family: PingFang SC;
            font-weight: 400;
            color: #ffffff;
          "
        >
          数据更新：{{ randaModalObj.dataTime }}
        </div>
      </div>
      <div>
        <table
          class="table-container"
          border="1px"
          cellpadding="5"
          cellspacing="0"
          align="center"
        >
          <thead>
            <tr style="height: 0.49rem">
              <th>名称</th>
              <th>数值</th>
              <th>单位</th>
              <th>在线状态</th>
            </tr>
          </thead>
          <tbody v-if="randaModalData.length">
            <tr
              style="height: 0.45rem"
              v-for="(item, index) in randaModalData"
              :key="index"
            >
              <td>{{ item.name || "--" }}</td>
              <td>
                <span :style="{ color: item.isAlarm ? '#D34E4E' : '' }">{{
                  item.value || item.value == 0 ? item.value : "--"
                }}</span>
              </td>
              <td>{{ item.unit || "--" }}</td>
              <td>
                <span
                  class="online"
                  :class="item.online ? 'yes_line' : 'no_line'"
                  >{{ item.online ? "在 线" : "离 线" }}</span
                >
              </td>
            </tr>
          </tbody>
          <tbody v-else style="height: 4rem; position: relative">
            <div class="tb_no_data">暂无数据</div>
          </tbody>
        </table>
      </div>
    </a-modal>

    <!-- 违规统计弹框 -->
    <el-dialog
      :visible.sync="dialogTable"
      :append-to-body="true"
      :modal-append-to-body="false"
      :before-close="handleClose"
      top="6vh"
    >
      <div class="titleimg">
        <img src="../../assets/<EMAIL>" alt="" />
      </div>
      <div class="titleTopp">{{ Wtitle }}</div>
      <div class="tablesss">
        <el-table
          :header-cell-style="{ 'text-align': 'center', color: '#01D5F8' }"
          :cell-style="{ 'text-align': 'center' }"
          :row-class-name="tableRowClassName"
          height="496px"
          :data="tableList"
        >
          <el-table-column
            property="motionTitle"
            label="发现时间"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="{ row }">
              <span>{{ row.alarmTime || "--" }}</span>
            </template>
          </el-table-column>

          <el-table-column
            property="motionTitle"
            label="违规现象"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="{ row }">
              <span>{{ row.alarmRemark || "--" }}</span>
            </template>
          </el-table-column>

          <el-table-column
            property="motionTitle"
            label="取证视频"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="{ row }">
              <el-image
                style="width: 102px; height: 58px"
                :src="row.alarmImage"
                @click.native="showVideo(row.alarmVideo)"
              >
              </el-image>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- 分页 -->
      <div class="suggestPagetion">
        <el-pagination
          class="billPagenation"
          @current-change="handleCurrentChange"
          :current-page="pageNum"
          :page-size="pageSize"
          background
          prev-text="上一页"
          next-text="下一页"
          layout="prev, pager, next"
          :total="total"
        >
        </el-pagination>
      </div>
    </el-dialog>

    <!-- 记录视频弹框 -->
    <el-dialog
      :visible.sync="dialogTable1"
      :append-to-body="true"
      :modal-append-to-body="false"
      :before-close="handleClose1"
      top="6vh"
      class="video_dialog"
    >
      <div class="video_cont">
        <!-- <LivePlayer
          class="center_btn"
          :video-url="videoUrl"
          fluent
          autoplay
          :live="false"
          :hasaudio="false"
          :stretch="true"
        /> -->
        <video
          ref="video"
          id="video"
          :src="videoUrl"
          autoplay
          controls
          style="width: 100%; height: 100%"
        ></video>
      </div>
    </el-dialog>

    <!-- 抓拍证据链弹框 -->
    <el-dialog
      :visible.sync="dialogTable2"
      :append-to-body="true"
      :modal-append-to-body="false"
      :before-close="handleClose"
      top="6vh"
    >
      <div class="titleimg">
        <img src="../../assets/<EMAIL>" alt="" />
      </div>
      <div class="titleTopp">黑烟车抓拍证据链</div>
      <div class="tablesss">
        <el-table
          :header-cell-style="{ 'text-align': 'center', color: '#01D5F8' }"
          :cell-style="{ 'text-align': 'center' }"
          :row-class-name="tableRowClassName"
          height="496px"
          :data="tableList1"
        >
          <el-table-column
            property="captureDateTime"
            label="发现时间"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="{ row }">
              <span>{{ row.captureDateTime || "--" }}</span>
            </template>
          </el-table-column>

          <el-table-column
            property="plateNumberColor"
            label="车牌颜色"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="{ row }">
              <span>{{ row.plateNumberColor || "--" }}</span>
            </template>
          </el-table-column>
          <el-table-column
            property="plateNumber"
            label="车牌号"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="{ row }">
              <span>{{ row.plateNumber || "--" }}</span>
            </template>
          </el-table-column>
          <el-table-column
            property="motionTitle"
            label="取证视频"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="{ row }">
              <el-image
                style="width: 102px; height: 58px"
                :src="row.image"
                @click.native="showVideo1(row.videoUrl)"
              >
              </el-image>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- 分页 -->
      <div class="suggestPagetion">
        <el-pagination
          class="billPagenation"
          @current-change="handleCurrentChange1"
          :current-page="pageNum1"
          :page-size="pageSize1"
          background
          prev-text="上一页"
          next-text="下一页"
          layout="prev, pager, next"
          :total="total1"
        >
        </el-pagination>
      </div>
    </el-dialog>
  </section>
</template>

<script lang="ts">
/* eslint-disable @typescript-eslint/ban-ts-ignore */
//@ts-ignore
import LivePlayer from "@liveqing/liveplayer";
/* eslint-disable @typescript-eslint/ban-ts-ignore */
//@ts-ignore
import Bus from "@/utils/bus";
/* eslint-disable @typescript-eslint/ban-ts-ignore */
//@ts-ignore
import _ from "lodash";
import { Component, Vue, Prop, Watch } from "vue-property-decorator";
import { Swiper, SwiperSlide } from "vue-awesome-swiper";
import GaoDeMap from "@/components/GaoDeMap/index.vue";
import {
  Table,
  Radio,
  Select,
  Icon,
  Tooltip,
  Modal,
  message,
} from "ant-design-vue";
import RotateBar from "@/components/Charts/LineAndBarChart.vue";
import tabs from "./tabs.vue";
import PollutionSource from "@/components/Charts/PollutionSource.vue";
import moment from "moment";
import realTimeMonitor from "@/components/Charts/realTimeMonitor.vue";
import noiseLine from "./noiseComponent/noiseLine.vue";
import noiseBar from "./noiseComponent/noiseBar.vue";
import electricityBar from "./electricityComponent/electricityBar.vue";
import electricityLine from "./electricityComponent/electricityLine.vue";
import solidWasteLine from "./solidWasteComponent/solidWasteLine.vue";
import solidWastePie from "./solidWasteComponent/solidWastePie.vue";
import heavyCorruptLine from "./heavyCorruptComponent/heavyCorruptLine.vue";
import heavyCorruptPie from "./heavyCorruptComponent/heavyCorruptPie2.vue";
import randaLine from "./randaComponent/randaLine.vue";
import blackGasLine from "./blackGasComponent/blackGasLine.vue";
import blackGasPie from "./blackGasComponent/blackGasPie.vue";
// 雷达告警历史图标
import hisDay from "@/assets/randa/<EMAIL>";
import hisMonth from "@/assets/randa/<EMAIL>";
import hisWeek from "@/assets/randa/<EMAIL>";
import {
  fetchStationWithRecord,
  getTodayMonitorItemRecord,
  fetchRecentAlarm,
} from "@/api/water";
import { heavilyPollutingEnterpriseList } from "@/api/homeTable";
import { pollutionCameraUrl } from "@/api/pollutionVideo";
import {
  getCompanyList,
  getPollutionCount,
  getPollutionEnterprise,
  getSiteVideoList,
  getCameraUrl,
  getCurSitePM10,
  findByKeyWord,
  getFiveWaterList,
  getSevenWaterList,
  getcompanyAlarmList,
  getTwentyFourHour,
  getSiteCount,
  getHeavilyPollutingEnterpriseList,
  getHeavyCount,
  thisDayCount,
  pageList,
  pageList1,
  listGroup,
} from "@/api/headvily-pollution";
import {
  getNoiseSite,
  getNoiseMapList,
  getDataComplianceRate,
  getHourAnalyze,
  getDaysAnalyze,
} from "@/api/noise";
import {
  getElectricityMapList,
  getDataComplianceRateE,
  getDayHourRecord,
  getDayRecord,
  getElectricitySite,
} from "@/api/electricity";
import {
  getSolidWasteSite,
  getSolidWasteMapList,
  getSolidWasteListRecord,
  getSolidWasteProportionTotal,
  getSolidWastecamera,
} from "@/api/solidWaste";
import {
  getHeavyCorruptMapList,
  getHeavyCorruptSite,
  getHeavyCorruptHour,
  getHeavyCorruptDay,
  getHeavyCorruptPie,
} from "@/api/heavyCorrupt";
import {
  randaListStation,
  randaMonitorTrend,
  randaAlarmCount,
  getStationMonitorItem,
} from "@/api/randa";
import {
  getGasTruckMapList,
  getGasTruckSite,
  getGasHourList,
  getGasDayList,
  getGasPieData,
} from "@/api/blackGas";
import "video.js/dist/video-js.css";
import "videojs-contrib-hls";
import "videojs-flash";
import momentjs from "moment";

import muxjs from "mux.js";

interface MapCenter {
  lng: number;
  lat: number;
}
interface EnterPriseInformation {
  name: string;
  district: string;
  type: string;
  address: string;
  status: string;
  kind: string;
  principal: string;
  principalTel: string;
  licence: string;
  emergencyPlan: string;
}
interface RankList {
  rank: number;
  type: string;
  release: string;
}
interface realTimeData {
  bottomList: string[];
  dataList: string | number[];
  dataList1: string | number[];
  standard: number;
  max: number | String;
  unit: string;
  name: string;
}
@Component({
  name: "HeavilyPollutedEnterprise",
  components: {
    PollutionSource,
    RotateBar,
    GaoDeMap,
    ATable: Table,
    ARadioGroup: Radio.Group,
    ARadioButton: Radio.Button,
    ASelect: Select,
    AIcon: Icon,
    ASelectOption: Select.Option,
    AModal: Modal,
    Swiper,
    SwiperSlide,
    ATooltip: Tooltip,
    "component-tabs": tabs,
    realTimeMonitor,
    noiseLine,
    noiseBar,
    electricityBar,
    electricityLine,
    solidWastePie,
    solidWasteLine,
    heavyCorruptPie,
    heavyCorruptLine,
    randaLine,
    blackGasPie,
    blackGasLine,
    LivePlayer,
  },
})
export default class extends Vue {
  private visible = false;
  private LineLoading = false;
  private PieLoading = false;
  // 黑烟车数据
  private GTsubTabs = ["车流量", "黑烟车", "汽油车", "柴油车"];
  // private GTsubTabs = ['车流量', '黑烟车']
  private GTsubTabName = "车流量";
  private GTLineType = "day";
  private GTPieTabName = "trafficFlow";
  private GTPieTabList = [
    { name: "车流量", id: "trafficFlow" },
    { name: "黑烟车", id: "smokyCarCount" },
    // { name: '汽油车', id: 'dieselFuelCount' },
    // { name: '柴油车', id: 'gasolineCarCount' },
  ];
  private GTLineData: any = {};
  private GTPiedata: any = {};

  nan: string = require("@/assets/nan.png");
  hong: string = require("@/assets/hong.png");
  pageNum: number = 1;
  pageSize: number = 5;
  total: number = 0;
  pageNum1: number = 1;
  pageSize1: number = 5;
  total1: number = 0;
  // 雷达监测
  private RDLineData: any = {
    bottomList: [],
    dataList: [],
    standard: null,
  };
  private RDSubNameList: any = ["消光系数", "PM₁₀", "PM₂.₅", "退偏振比"];
  private RDSubName: any = "消光系数";
  private RDFigList: any = [];
  private RDHistoryTitle: any = [
    { icon: hisDay, count: 0, label: "本日/次" },
    { icon: hisWeek, count: 0, label: "本周/次" },
    { icon: hisMonth, count: 0, label: "本月/次" },
  ];
  private RDHistoryList: any = [];
  // 雷达污染指纹库/历史告警记录 swiperoption
  private swiperOptionRound = {
    direction: "vertical",
    slidesPerView: 3,
    slidesPerGroup: 1,
    loop: true,
    autoplay: {
      delay: 3500,
      disableOnInteraction: false,
    },
  };
  // 雷达弹框数据
  private randaModalData: any = [];
  private randaModalObj: any = {};
  // 重污监测
  private HCsubTabs: any = [];
  private HCsubTabName: any = "";
  private HCLineType: any = "hour";
  private HCLineData: any = {};
  private HCLineStandard: any = {};
  private HCPdata: any = { list: [] };
  private HCPieTabName: any = "";
  projectData: any = {};
  // 固废类型筛选
  private wasteType = "";
  private wasteTypeList: any = [
    { name: "全部", id: "", disabled: false },
    { name: "汽修", id: "汽修", disabled: false },
    { name: "工况", id: "工况", disabled: true },
    { name: "医疗", id: "医疗", disabled: true },
    { name: "餐饮", id: "餐饮", disabled: true },
  ];
  // 固废禁止选择今天之后日期
  private wasteSelDate = moment().format("YYYY-MM");
  private pickerOptions: any = {
    disabledDate(time: any) {
      return time.getTime() > Date.now();
    },
  };
  // 固废图表数据
  private wastesubTabs: any = [];
  private wastesubTabName: any = "";
  private wastesubTabNameMap: any = {};
  private wasteLineData: any = {};
  private wasteUnit: any = "";
  private wastePieDate: any = {};
  private wastePieTabName: any = "stock";
  private wasteLineDataType: number = 7;
  // 噪声类型筛选
  private noiseType = "";
  private noiseTypeList = [
    { name: "全部", id: "", disabled: false },
    { name: "0类", id: "0类", disabled: true },
    { name: "1类", id: "1类", disabled: true },
    { name: "2类", id: "2类", disabled: false },
    { name: "3类", id: "3类", disabled: true },
    { name: "4a类", id: "4a类", disabled: false },
    { name: "建筑工地类", id: "建筑工地类", disabled: false },
    { name: "4b类", id: "4b类", disabled: true },
  ];
  // 噪声图表数据
  private subTabs = [];
  private standardObj: any = {};
  private noiseLineData: any = {};
  private noiseBarData: any = {
    bottomList: [],
    dataList: [
      { name: "昼间", data: [], colors: ["#42A8FF", "#082641"] },
      { name: "夜间", data: [], colors: ["#98ECD7", "#072222"] },
    ],
  };
  // 用电量图表数据
  private electricityLineData: any = {};
  private electricityBarData: any = {
    bottomList: [],
    dataList: [],
  };
  videoUrl: string = "";
  private subTabName: string = "";
  private lineDataType: string = "hour";

  private enterpriseText: any = null;
  private currentSelectedBuildingMarker: any = null;
  private currentSelectedBuildingObj: any = {
    target: {
      w: {
        data: {},
      },
    },
  };
  private realTimeData: any = {
    bottomList: [],
    dataList: [],
    dataList1: [],
    standard: 0,
    max: 0,
    unit: "",
    name: "",
  };
  private curMarkerClickMarker: any = "";
  private curMarkerClickObj: any = {
    target: {
      w: {
        data: {},
      },
    },
  };
  private enterpriseSearch: any = "";
  private enterpriseStreet: any = "";
  private showType: any = 0;
  // private mapStyle = "amap://styles/0739a6632d849ae1b5e6f1c52f7d12c3";
  private mapStyle = "amap://styles/0739a6632d849ae1b5e6f1c52f7d12c3";
  // 地图视图中心点
  private mapViewCenter: MapCenter = {
    lng: 104.11,
    lat: 30.912,
  };
  // 地图缩放大小
  private mapViewZoom = 12.8;
  // 默认选中重污染企业
  private defaultEnterpriseValue = "";
  private swiperOptionAlert = {
    direction: "vertical",
    slidesPerView: 5,
    slidesPerGroup: 1,
    loop: true,
    autoplay: {
      delay: 3500,
      disableOnInteraction: false,
    },
  };
  // 当前展示的重污染企业信息
  private currentEnterpriseInfor: EnterPriseInformation = {
    name: "",
    district: "",
    type: "",
    address: "",
    status: "",
    kind: "",
    principal: "",
    principalTel: "",
    licence: "",
    emergencyPlan: "",
  };
  // 重污企业排放量
  private heavyPollution = [];
  private pollutionType = "s"; //当前排污类型
  // 重污染企业轮播timer
  private switchEnterpriseTimerStore: any;
  private playerOptions: any = {
    //playbackRates: [0.7, 1.0, 1.5, 2.0], //播放速度
    autoplay: false, //如果true,浏览器准备好时开始回放。
    muted: true, // 默认情况下将会消除任何音频。
    loop: false, // 导致视频一结束就重新开始。
    preload: "auto", // 建议浏览器在<video>加载元素后是否应该开始下载视频数据。auto浏览器选择最佳行为,立即开始加载视频（如果浏览器支持）
    language: "zh-CN",
    languages: {
      "zh-CN": {
        Play: "播放",
        Pause: "暂停",
        "Current Time": "当前时间",
        Duration: "时长",
        "Remaining Time": "剩余时间",
        "Stream Type": "媒体流类型",
        LIVE: "直播",
        Loaded: "加载完毕",
        Progress: "进度",
        Fullscreen: "全屏",
        "Non-Fullscreen": "退出全屏",
        Mute: "静音",
        Unmute: "取消静音",
        "Playback Rate": "播放速度",
        Subtitles: "字幕",
        "subtitles off": "关闭字幕",
        Captions: "内嵌字幕",
        "captions off": "关闭内嵌字幕",
        Chapters: "节目段落",
        "Close Modal Dialog": "关闭弹窗",
        Descriptions: "描述",
        "descriptions off": "关闭描述",
        "Audio Track": "音轨",
        "You aborted the media playback": "视频播放被终止",
        "A network error caused the media download to fail part-way.":
          "网络错误导致视频下载中途失败。",
        "The media could not be loaded, either because the server or network failed or because the format is not supported.":
          "视频因格式不支持或者服务器或网络的问题无法加载。",
        "The media playback was aborted due to a corruption problem or because the media used features your browser did not support.":
          "由于视频文件损坏或是该视频使用了你的浏览器不支持的功能，播放终止。",
        "No compatible source was found for this media.":
          "无法找到此视频兼容的源。",
        "The media is encrypted and we do not have the keys to decrypt it.":
          "视频已加密，无法解密。",
        "Play Video": "播放视频",
        Close: "关闭",
        "Modal Window": "弹窗",
        "This is a modal window": "这是一个弹窗",
        "This modal can be closed by pressing the Escape key or activating the close button.":
          "可以按ESC按键或启用关闭按钮来关闭此弹窗。",
        ", opens captions settings dialog": ", 开启标题设置弹窗",
        ", opens subtitles settings dialog": ", 开启字幕设置弹窗",
        ", opens descriptions settings dialog": ", 开启描述设置弹窗",
        ", selected": ", 选择",
        "captions settings": "字幕设定",
        "Audio Player": "音频播放器",
        "Video Player": "视频播放器",
        Replay: "重播",
        "Progress Bar": "进度小节",
        "Volume Level": "音量",
        "subtitles settings": "字幕设定",
        "descriptions settings": "描述设定",
        Text: "文字",
        White: "白",
        Black: "黑",
        Red: "红",
        Green: "绿",
        Blue: "蓝",
        Yellow: "黄",
        Magenta: "紫红",
        Cyan: "青",
        Background: "背景",
        Window: "视窗",
        Transparent: "透明",
        "Semi-Transparent": "半透明",
        Opaque: "不透明",
        "Font Size": "字体尺寸",
        "Text Edge Style": "字体边缘样式",
        None: "无",
        Raised: "浮雕",
        Depressed: "压低",
        Uniform: "均匀",
        Dropshadow: "下阴影",
        "Font Family": "字体库",
        "Proportional Sans-Serif": "比例无细体",
        "Monospace Sans-Serif": "单间隔无细体",
        "Proportional Serif": "比例细体",
        "Monospace Serif": "单间隔细体",
        Casual: "舒适",
        Script: "手写体",
        "Small Caps": "小型大写字体",
        Reset: "重启",
        "restore all settings to the default values": "恢复全部设定至预设值",
        Done: "完成",
        "Caption Settings Dialog": "字幕设定视窗",
        "Beginning of dialog window. Escape will cancel and close the window.":
          "开始对话视窗。离开会取消及关闭视窗",
        "End of dialog window.": "结束对话视窗",
      },
    },
    aspectRatio: "448:196", // 将播放器置于流畅模式，并在计算播放器的动态大小时使用该值。值应该代表一个比例 - 用冒号分隔的两个数字（例如"16:9"或"4:3"）
    fluid: true, // 当true时，Video.js player将拥有流体大小。换句话说，它将按比例缩放以适应其容器。
    //application/x-mpegURL-m3u8 video/mp4-mp4 rtmp/mp4-rtmp flv-application/octet-stream-flv
    sources: [
      // {
      //   type: "application/x-mpegURL",
      //   src:
      //     "http://60.255.36.154:10000/sms/34020000002020000001/hls/51010600001320000005_51010600001320000001/51010600001320000005_51010600001320000001_live.m3u8"
      // }
    ],
    // poster:
    //   "http://60.255.36.154:10000/snap/51010600001320000005/51010600001320000001.jpg?t=1586164395847422586", //你的封面地址
    width: document.documentElement.clientWidth,
    notSupportedMessage: "该站点暂未设置监控，请查看其它站点", //允许覆盖Video.js无法播放媒体源时显示的默认信息。
    // controls: false,
    // controlBar: {
    //   timeDivider: false,
    //   durationDisplay: false,
    //   remainingTimeDisplay: false,
    //   fullscreenToggle: false, //全屏按钮
    // },
  };
  // 摄像头列表
  private videoList: any[] = [];
  private curVideo: number | string = ""; //当前的摄像头id
  private fetchTimer: any = null;
  private type: number = 0;
  private isFirst = false;
  dialogTable: boolean = false;
  dialogTable1: boolean = false;
  dialogTable2: boolean = false;
  Wtitle: string = "";
  tableList: any = [];
  tableList1: any = [];
  private searchcurWords: string = "";
  projectId: string = "";
  VertigoVideo: string = "";
  VertigoVideoLsit: any = [];
  @Watch("wasteSelDate")
  private wasteSelDateChange() {
    this.wasteSelDate = this.wasteSelDate
      ? this.wasteSelDate
      : moment().format("YYYY-MM");
    this.getSolidWasteProportionTotal();
  }
  @Watch("wasteType")
  private wasteTypeChange() {
    this.getSolidWasteMapList();
  }
  @Watch("noiseType")
  private noiseTypeChange() {
    this.paGetNoiseMapList();
  }
  // 雷达数据类型名
  @Watch("RDSubName", { immediate: true })
  private ChangeRDSubName(nVal: any) {
    this.$emit("changeRandaType", nVal);
  }
  // 雷达点击的经纬度
  @Prop({ default: () => ({}) }) private randaLocation!: any;
  @Watch("randaLocation", { deep: true })
  private randaLocationChange(nVal: any) {
    // console.log([nVal.lat,nVal.lng],'------2401----nVal');
    if (!nVal.lat) return;
    this.getRandaFigData(nVal);
    this.getRandaTrend(nVal);
    this.getRandaAlarmCount(nVal);
  }
  // 雷达所选文件名
  @Prop({ default: "" }) private randaItem!: any;
  // 图层索引
  @Prop({ default: -1 }) private index!: number;
  @Watch("index", { immediate: true, deep: true })
  private onIndex(newValue: any, oldValue: any) {
    this.searchcurWords = "";
    this.wastesubTabName = "";
    this.enterpriseSearch = "";
    this.clearChartData();
    if ([0, 5, 6, 7, 8, 9, 10, 13].includes(newValue)) {
      this.type = newValue;
      if (this.type == 0) {
        this.getSiteCount();
        this.fetchcompanyList();
      } else if (this.type == 5) {
        this.getHeavyCount();
        this.getHeavilyPollutingEnterpriseList();
      } else if (this.type === 6) {
        this.getNoiseSite();
        this.paGetNoiseMapList();
      } else if (this.type === 7) {
        this.getElectricitySite();
        this.getElectricityMapList();
      } else if (this.type === 8) {
        this.getSolidWasteSite();
        this.getSolidWasteMapList();
      } else if (this.type === 9) {
        this.getHeavyCorruptSite();
        this.getHeavyCorruptMapList();
      } else if (this.type === 13) {
        this.getExhaustGasTruckSite();
        this.paGetGasMapList();
      }
      this.isFirst = true;
    }
  }
  // 搜索关键词
  @Prop({ default: null }) private searchWords!: String;
  @Watch("searchWords", { immediate: true, deep: true })
  private onsearchWords(newValue: any, oldValue: any) {
    this.searchcurWords = newValue;
    this.clearChartData();
    if (newValue) {
      if (this.type == 6) {
        this.paGetNoiseMapList();
        this.getNoiseSite();
        return;
      } else if (this.type === 7) {
        this.getElectricitySite();
        this.getElectricityMapList();
        return;
      } else if (this.type === 8) {
        this.getSolidWasteSite();
        this.getSolidWasteMapList();
        return;
      } else if (this.type === 9) {
        this.getHeavyCorruptSite();
        this.getHeavyCorruptMapList();
        return;
      } else if (this.type === 13) {
        this.getExhaustGasTruckSite();
        this.paGetGasMapList();
        return;
      }
      this.enterpriseSearch = newValue;
      // this.enterpriseSearchBtn()
      this.fetchcompanyList();
    } else {
      this.clearSearch();
    }
  }
  isGD: boolean = false;
  @Watch("projectData", { immediate: false, deep: true })
  private onisGD(newValue: any, oldValue: any) {
    this.isGD = true;
  }

  @Watch("projectId", { immediate: true, deep: true })
  private onprojectId(newValue: any, oldValue: any) {
    this.getlistGroup(newValue);
  }

  // 所选街道
  @Prop({ default: null }) private currStreet!: String;
  @Watch("currStreet", { immediate: true, deep: true })
  private onCurrStreet(newValue: any, oldValue: any) {
    this.clearChartData();
    if (newValue) {
      this.enterpriseStreet = newValue;
      this.curSiteIndex = 0;
      if (this.type == 6) {
        this.getNoiseSite();
        this.paGetNoiseMapList();
      } else if (this.type === 7) {
        this.getElectricitySite();
        this.getElectricityMapList();
      } else if (this.type === 8) {
        this.getSolidWasteSite();
      } else if (this.type === 9) {
        this.getHeavyCorruptSite();
      } else if (this.type === 13) {
        this.getExhaustGasTruckSite();
        this.paGetGasMapList();
      }
      if (!this.isFirst) {
        if (this.type == 0) {
          this.getSiteCount();
          this.fetchcompanyList();
        } else if (this.type == 5) {
          this.getHeavilyPollutingEnterpriseList();
        }
      } else {
        this.isFirst = false;
      }
    }
  }
  // 在线离线状态数组
  @Prop({ default: 0 }) private online!: any[];
  @Watch("online", { deep: true })
  private onOnline(newValue: any, oldValue: any) {
    this.clearChartData();
    if (newValue) {
      this.curSiteIndex = 0;
      if (this.type == 0) {
        this.fetchcompanyList();
      } else if (this.type == 6) {
        this.paGetNoiseMapList();
      } else if (this.type === 7) {
        this.getElectricityMapList();
      } else if (this.type === 8) {
        this.getSolidWasteMapList();
      } else if (this.type === 9) {
        this.getHeavyCorruptMapList();
      } else if (this.type === 13) {
        this.paGetGasMapList();
      }
    }
  }
  // 电量监测趋势线型图类型 小时(hour)、天(day)
  @Watch("lineDataType", { immediate: true, deep: true })
  private lineDataTypeChange(newValue: any, oldValue: any) {
    if (newValue) {
      if (this.type === 6) {
        this.getRightHourAnalyze();
      } else if (this.type === 7) {
        this.getRightHourAnalyzeE();
      } else if (this.type === 9) {
      }
    }
  }
  // 固废监测趋势线型图类型  七天(7)、一个月(30)
  @Watch("wasteLineDataType", { immediate: true, deep: true })
  private wasteLineDataTypeChange(newValue: any, oldValue: any) {
    if (newValue) {
      if (this.type != 8) return;
      this.getSolidWasteListRecord();
    }
  }
  // 黑烟车监测趋势线型图类型 小时(hour)、天(day)
  @Watch("GTLineType", { immediate: false, deep: true })
  private GTLineTypeChange(newValue: any, oldValue: any) {
    if (newValue) {
      if (this.type != 13) return;
      this.getBGLineData();
    }
  }
  // 排污监测趋势线型图类型 小时(hour)、天(day)
  @Watch("HCLineType", { immediate: true, deep: true })
  private HCLineTypeChange(newValue: any, oldValue: any) {
    if (newValue) {
      if (this.type != 9) return;
      this.getHCLineData();
    }
  }
  // 排污监测饼图数据类型 贮(zhu)存量、产生量、转运量
  @Watch("HCPieTabName", { immediate: true, deep: true })
  private HCPieTabNameChange(newValue: any, oldValue: any) {
    if (newValue) {
      if (this.type === 9) {
        this.getHCPieData();
      }
    }
  }
  mounted() {
    this.type = this.index;
    Bus.$on(
      "curSiteChange",
      (value: any, id: any, projectId: any, alias: any) => {
        this.getVideo(value);
        this.getCurSiteFromMap(value);
        this.Wtitle = alias;
        this.projectId = projectId;
        if (id) {
          this.isGD = true;
          this.getDayCount(projectId);
        } else {
          this.isGD = false;
        }
      }
    );
    Bus.$on("curCompanyChange", (value: any) => {
      this.curCompanyChange(value);
    });
    this.getcompanyAlarmList();
    // this.getHeavilyPollutingEnterpriseList();
    // this.fetchcompanyList();
    this.fetchHeavyPollution();
  }
  /* 今日违规统计 */
  getDayCount(projectId: any) {
    thisDayCount({ projectId }).then((res) => {
      const { data } = res.data;
      if (data) {
        this.projectData = {
          防尘喷雾未开启: data.dustProof,
          天幕未开始: data.canopy,
          裸土未覆盖: data.bareSoil,
          车辆未清洗: data.carNoCleanOut,
          车辆清洗不达标: data.carCleanNoStandardOut,
          车辆违规入场: data.carIn,
          其他违规: data.other,
        };
      } else {
        this.projectData = {};
      }
      this.isGD = true;
    });
  }

  /* 历史违规统计 */
  getList() {
    const data = {
      pageNum: this.pageNum,
      pageSize: this.pageSize,
      projectId: this.projectId,
    };
    pageList(data).then((res) => {
      this.tableList = res.data.data.records;
      this.total = res.data.data.total;
    });
  }

  /* 视频监控 */
  getlistGroup(projectId: any) {
    listGroup({ projectId }).then((res) => {
      res.data.data.forEach((v: any) => {
        v.stationList.forEach((x: any) => {
          if (x.id === projectId && x.cameList.length > 0) {
            this.VertigoVideo = x.cameList[0].liveAddress;
          }
        });
      });
      this.VertigoVideoLsit = res.data.data;
    });
  }

  //固废视频
  getVideo(id: any) {
    getSolidWastecamera({ stationId: id }).then((res) => {
      if (!res.data.data[0]) {
        this.$emit("setVideo", false);
        return;
      }
      pollutionCameraUrl(res.data.data[0].monitorId).then((res1) => {
        this.$emit("setVideo", res1.data.data.flv || res1.data.data.flvHttps);
      });
    });
  }

  /* 黑烟车违规统计 */
  getList1() {
    const data = {
      pageNum: this.pageNum1,
      pageSize: this.pageSize1,
      stationId: this.curSite,
    };
    pageList1(data).then((res) => {
      this.tableList1 = res.data.data.records;
      this.total1 = res.data.data.total;
    });
  }
  getMore() {
    this.getList();
    this.dialogTable = true;
  }

  getMore1() {
    const VertigoVideoLsit: any = this.VertigoVideoLsit;
    const projectId: any = this.projectId;
    this.$router.push({
      name: "MonitorVideo",
      params: {
        VertigoVideoLsit,
        projectId,
      },
    });
  }
  handleClose() {
    this.dialogTable = false;
    this.dialogTable2 = false;
  }
  handleClose1() {
    this.dialogTable1 = false;
  }

  getSrc(src: any) {
    let xhr = new XMLHttpRequest();
    xhr.open("GET", src);
    // 接收的是 video/mp2t 二进制数据，并且arraybuffer类型方便后续直接处理
    xhr.responseType = "arraybuffer";
    xhr.send();
    xhr.onreadystatechange = () => {
      if (xhr.readyState == 4) {
        if (xhr.status == 200) {
          this.transferFormat(xhr.response);
        } else {
          console.log("error");
        }
      }
    };
  }

  transferFormat(data: any) {
    let segment = new Uint8Array(data);
    let combined = false;
    // 接收无音频ts文件，OutputType设置为'video'，带音频ts设置为'combined'
    let outputType = "video";
    let remuxedSegments: any = [];
    let remuxedBytesLength = 0;
    let remuxedInitSegment: any = null;
    // remux选项默认为true，将源数据的音频视频混合为mp4，设为false则不混合
    let transmuxer = new muxjs.mp4.Transmuxer({
      remux: false,
    });

    // 监听data事件，开始转换流
    transmuxer.on("data", function(event: any) {
      console.log(event);
      if (event.type === outputType) {
        remuxedSegments.push(event);
        remuxedBytesLength += event.data.byteLength;
        remuxedInitSegment = event.initSegment;
      }
    });
    // 监听转换完成事件，拼接最后结果并传入MediaSource
    transmuxer.on("done", () => {
      let offset = 0;
      let bytes = new Uint8Array(
        remuxedInitSegment.byteLength + remuxedBytesLength
      );
      bytes.set(remuxedInitSegment, offset);
      offset += remuxedInitSegment.byteLength;

      for (let j = 0, i = offset; j < remuxedSegments.length; j++) {
        bytes.set(remuxedSegments[j].data, i);
        i += remuxedSegments[j].byteLength;
      }
      remuxedSegments = [];
      remuxedBytesLength = 0;
      // 解析出转换后的mp4相关信息，与最终转换结果无关
      let vjsParsed = muxjs.mp4.tools.inspect(bytes);
      console.log("transmuxed", vjsParsed);

      this.prepareSourceBuffer(combined, outputType, bytes);
    });
    // push方法可能会触发'data'事件，因此要在事件注册完成后调用
    transmuxer.push(segment); // 传入源二进制数据，分割为m2ts包，依次调用上图中的流程
    // flush的调用会直接触发'done'事件，因此要事件注册完成后调用
    transmuxer.flush(); // 将所有数据从缓存区清出来
  }

  prepareSourceBuffer(combined: any, outputType: any, bytes: any) {
    let buffer: any;
    let video: any = document.querySelector("#video");
    video.controls = true;
    let mediaSource = new MediaSource();
    video.src = URL.createObjectURL(mediaSource);

    // 转换后mp4的音频格式 视频格式
    let codecsArray = ["avc1.64001f", "mp4a.40.5"];

    mediaSource.addEventListener("sourceopen", () => {
      // MediaSource 实例默认的duration属性为NaN
      mediaSource.duration = 0;
      // 转换为带音频、视频的mp4
      if (combined) {
        buffer = mediaSource.addSourceBuffer(
          'video/mp4;codecs="' + "avc1.64001f,mp4a.40.5" + '"'
        );
      } else if (outputType === "video") {
        // 转换为只含视频的mp4
        buffer = mediaSource.addSourceBuffer(
          'video/mp4;codecs="' + codecsArray[0] + '"'
        );
      } else if (outputType === "audio") {
        // 转换为只含音频的mp4
        buffer = mediaSource.addSourceBuffer(
          'audio/mp4;codecs="' + (codecsArray[1] || codecsArray[0]) + '"'
        );
      }

      buffer.addEventListener("updatestart", this.logevent);
      buffer.addEventListener("updateend", this.logevent);
      buffer.addEventListener("error", this.logevent);
      video.addEventListener("error", this.logevent);
      buffer.appendBuffer(bytes);
      // 自动播放
      video.play();
    });
  }

  logevent(event: any) {
    console.log(event);
  }

  /* 播放大视频 */
  showVideo(data: any) {
    if (data.length > 0) {
      // this.videoUrl = data[0]
      this.dialogTable1 = true;
      this.$nextTick(() => {
        this.getSrc(data[0]);
      });
    } else {
      message.warning("暂无视频");
    }
  }

  /* 播放大视频 */
  showVideo1(data: any) {
    if (data.length > 0) {
      this.videoUrl = data;
      this.dialogTable1 = true;
    } else {
      message.warning("暂无视频");
    }
  }
  // 页面变化
  handleCurrentChange(e: any) {
    this.pageNum = e;
    this.getList();
  }
  // 页面变化
  handleCurrentChange1(e: any) {
    this.pageNum1 = e;
    this.getList1();
  }
  /* 抓拍证据链弹框 */
  showL() {
    this.dialogTable2 = true;
    this.getList1();
  }
  tableRowClassName(data: any) {
    const { row, rowIndex } = data;
    console.log(row);
    if (rowIndex % 2 == 0) {
      return "warning-row";
    }
    return "normal-row";
  }
  private swiperMouseEnter() {
    const el: any = this.$refs.fingerprintSwiper;
    el.$swiper.autoplay.stop();
  }
  private swiperMouseLeave() {
    const el: any = this.$refs.fingerprintSwiper;
    el.$swiper.autoplay.start();
  }
  private toViewMore(item: any) {
    const { type, stationCode, online, dataTime } = item;
    this.randaModalObj = {
      ...item,
      dataTime: dataTime ? moment(dataTime).format("YYYY-MM-DD HH:mm") : "--",
    };
    getStationMonitorItem({ type, stationId: stationCode })
      .then((res) => {
        const { data } = res.data;
        if (!data) return;
        const list = data.map((v: any) => ({ ...v, online }));
        this.randaModalData = list;
      })
      .finally(() => {
        this.visible = true;
      });
  }
  private handleCancel() {
    this.randaModalData = [];
    this.visible = false;
  }
  // private subTabClick(tabName:any){
  //   this.subTabName = tabName
  // }
  private clearChartData() {
    this.companyList = [];
    this.lineDataType = "hour";
    this.noiseLineData = {};
    this.noiseBarData = {
      bottomList: [],
      dataList: [
        { name: "昼间", data: [], colors: ["#42A8FF", "#082641"] },
        { name: "夜间", data: [], colors: ["#98ECD7", "#072222"] },
      ],
    };
    this.electricityBarData = {
      bottomList: [],
      dataList: [],
    };
    this.electricityLineData = {};
  }

  private clearChartDataZS() {
    this.noiseLineData = {};
  }
  // 噪声站点数量
  private getNoiseSite() {
    getNoiseSite({
      streetCode: this.currStreet === "全部街道" ? undefined : this.currStreet,
      keywords: this.searchWords || undefined,
    }).then((res) => {
      this.$emit("noiseSitecount", res.data.data);
    });
  }
  // 用电量站点数量
  private getElectricitySite() {
    getElectricitySite({
      streetCode: this.currStreet === "全部街道" ? undefined : this.currStreet,
      keywords: this.searchWords || undefined,
    }).then((res) => {
      this.$emit("noiseSitecount", res.data.data);
    });
  }
  // 固废站点数量
  private getSolidWasteSite() {
    getSolidWasteSite({
      streetCode: this.currStreet === "全部街道" ? undefined : this.currStreet,
      keywords: this.searchWords || undefined,
    }).then((res) => {
      this.$emit("noiseSitecount", res.data.data);
    });
  }
  // 排污站点数量
  private getHeavyCorruptSite() {
    getHeavyCorruptSite({
      streetCode: this.currStreet === "全部街道" ? undefined : this.currStreet,
      keywords: this.searchWords || undefined,
    }).then((res) => {
      this.$emit("noiseSitecount", res.data.data);
    });
  }
  // 黑烟车站点数据
  private getExhaustGasTruckSite() {
    // getGasTruckSite
    getGasTruckSite({
      streetCode: this.currStreet === "全部街道" ? undefined : this.currStreet,
      keywords: this.searchWords || undefined,
    }).then((res) => {
      this.$emit("noiseSitecount", res.data.data);
    });
  }
  private getHeavyCount() {
    getHeavyCount().then((res) => {
      this.$emit("heaaviycount", res.data.data);
    });
  }
  private getSiteCount() {
    getSiteCount({
      streetCode: this.currStreet === "全部街道" ? undefined : this.currStreet,
      keywords: this.searchWords || undefined,
    }).then((res) => {
      this.$emit("sitecount", res.data.data);
    });
  }
  beforeDestroy() {
    clearInterval(this.switchEnterpriseTimerStore);
    clearInterval(this.fetchTimer);
  }
  // 清空搜索
  private clearSearch() {
    if (this.enterpriseSearch !== "") {
      this.enterpriseSearch = "";
      // 企业
      this.getHeavilyPollutingEnterpriseList();
      // 工地
      this.fetchcompanyList();
    }
  }
  // 搜索
  private enterpriseSearchBtn() {
    if (this.enterpriseSearch !== "") {
      findByKeyWord(this.enterpriseSearch).then((res: any) => {
        // 企业;
        this.heavyPollutionEnterpriseList =
          this.type == 5
            ? res.data.data.heavilyPollutingEnterpriseList.map(
                (enterprise: any) => {
                  return {
                    name: enterprise.heavilyPollutingEnterpriseNickname,
                    value: enterprise.heavilyPollutingEnterpriseId,
                    lng: enterprise.lng,
                    lat: enterprise.lat,
                  };
                }
              )
            : [];
        if (this.type === 5) {
          this.$emit(
            "heavyPollutionEnterpriseList",
            this.heavyPollutionEnterpriseList
          );
        }
        // 工地;
        this.companyList = (this.type == 0 ? res.data.data.companyList : [])
          .filter((item: any) => item.gcLng)
          .map((item: any) => {
            item.companyId = item.projectId;
            return item;
          });
        if (this.type === 0) {
          this.$emit("constrSite", this.companyList);
        }
        if (res.data.data.heavilyPollutingEnterpriseList.length > 0) {
          // 企业
          this.heavilyPollutingEnterpriseChage(
            this.heavyPollutionEnterpriseList[0].value
          );
          this.showType = 0;
        } else if (res.data.data.companyList.length > 0) {
          // 工地;
          this.curSiteChange(this.companyList[0].companyId);
        }
      });
    }
  }
  // 获取单个摄像头URL
  private fetchCameraUrl(): void {
    getCameraUrl(this.curVideo).then((res) => {
      // 清除上一次地址
      this.playerOptions.sources = [
        {
          src: "",
          type: "application/x-mpegURL",
        },
      ];
      if (res.data.data.hlsHttps) {
        this.playerOptions.sources.push({
          type: "application/x-mpegURL",
          src: res.data.data.hlsHttps,
        });
      } else if (res.data.data.flvHttps) {
        this.playerOptions.sources.push({
          type: "flv-application/octet-stream",
          src: res.data.data.flvHttps,
        });
      } else if (res.data.data.rtmp) {
        this.playerOptions.sources.push({
          type: "rtmp/mp4",
          src: res.data.data.rtmp,
        });
      } else {
        this.playerOptions.sources = [
          {
            src: "",
            type: "application/x-mpegURL",
          },
        ];
      }
      if (res.data.data.snapUrl) {
        this.playerOptions.poster = res.data.data.snapUrl;
      } else {
        this.playerOptions.poster = "";
      }
    });
  }
  private videoChange(value: number | string): void {
    this.curVideo = value;
    this.fetchCameraUrl();
  }
  // 播放状态更改
  private playerStateChanged(playerCurrentState: any) {
    if (playerCurrentState.error) {
      this.videoChange(this.curVideo);
    }
  }
  // 地图标记点和下拉选项列表
  private heavyPollutionEnterpriseList: Array<any> = [];
  // 重污染企业store
  private heavyPollutionEnterpriseStrore: Array<any> = [];
  private heavilyPollutionCount: string | number = 0; //企业数量
  // 获取重污染企业列表
  private getHeavilyPollutingEnterpriseList(): void {
    getHeavilyPollutingEnterpriseList({
      streetCode: this.currStreet === "全部街道" ? undefined : this.currStreet,
      keywords: this.searchWords || undefined,
    }).then((res) => {
      const data = res.data.data;
      if (!data.length) {
        this.defaultEnterpriseValue = "";
        this.currentEnterpriseInfor = {
          name: "",
          district: "",
          type: "",
          address: "",
          status: "",
          kind: "",
          principal: "",
          principalTel: "",
          licence: "",
          emergencyPlan: "",
        };
        this.heavyPollutionEnterpriseList = [];
        this.pollutionSourceList = [
          {
            nameList: [],
            dataList: [],
            unit: "",
          },
          {
            nameList: [],
            dataList: [],
            unit: "",
          },
          {
            nameList: [],
            dataList: [],
            unit: "",
          },
        ];
        this.pollutionAllCount = {};
        if (this.type === 5) {
          this.$emit("heavyPollutionEnterpriseList", []);
        }
        return;
      }
      // this.heavilyPollutionCount =
      //     data.length > 99 ? String(data.length) : "0" + data.length;
      // if (this.type === 5) {
      //     this.$emit('heaaviycount', this.heavilyPollutionCount)
      // }
      this.heavyPollutionEnterpriseStrore = data;
      //企业信息
      this.currentEnterpriseInfor = {
        name: data[0].heavilyPollutingEnterpriseName,
        district: data[0].administrativeDivision,
        type: data[0].heavilyPollutingEnterpriseType,
        address: data[0].heavilyPollutingEnterpriseAddress,
        status: data[0].heavilyPollutingEnterpriseStatus,
        kind: data[0].industryCategory,
        principal: data[0].principal,
        principalTel: data[0].principalPhoneNumber,
        licence: data[0].haveSewageDischargePermission ? "拥有" : "无",
        emergencyPlan: data[0].haveEnvironmentalEmergencyPlan,
      };
      //默认选中的监测点
      this.defaultEnterpriseValue = data[0].heavilyPollutingEnterpriseId;
      this.currentSelectedBuildingMarker =
        this.type == 5 ? data[0].heavilyPollutingEnterpriseId : "";
      this.currentSelectedBuildingObj =
        this.type == 5
          ? {
              target: {
                w: {
                  data: {
                    name: data[0].heavilyPollutingEnterpriseNickname,
                    value: data[0].heavilyPollutingEnterpriseId,
                    lng: data[0].lng,
                    lat: data[0].lat,
                  },
                },
              },
            }
          : {
              target: {
                w: {
                  data: {},
                },
              },
            };
      //地图标记点,下拉选项
      this.heavyPollutionEnterpriseList =
        this.type == 5
          ? data.map((enterprise: any) => {
              return {
                name: enterprise.heavilyPollutingEnterpriseNickname,
                value: enterprise.heavilyPollutingEnterpriseId,
                lng: enterprise.lng,
                lat: enterprise.lat,
              };
            })
          : [];
      if (this.heavyPollutionEnterpriseList.length) {
        this.heavilyPollutingEnterpriseChage(
          this.heavyPollutionEnterpriseList[0].value
        );
        this.showType = 0;
      }
      if (this.type === 5) {
        this.$emit(
          "heavyPollutionEnterpriseList",
          this.heavyPollutionEnterpriseList
        );
      }
      this.fetchPollutionCount();
      this.fetchPollutionEnterprise();
      // this.switchEnterpriseSelectTimer();
    });
  }
  private polluteDetails: any = [];
  private selectIndex = 0; //企业切换下标
  // select切换重污染企业项
  private heavilyPollutingEnterpriseChage(value: any): void {
    clearInterval(this.switchEnterpriseTimerStore);
    this.defaultEnterpriseValue = value;
    this.currentSelectedBuildingMarker = this.type == 5 ? value : "";
    const selectIndex = this.heavyPollutionEnterpriseStrore.findIndex(
      (item) => {
        return item.heavilyPollutingEnterpriseId == value;
      }
    );
    this.currentSelectedBuildingObj =
      this.type == 5
        ? {
            target: {
              w: {
                data: {
                  name: this.heavyPollutionEnterpriseStrore[selectIndex]
                    .heavilyPollutingEnterpriseNickname,
                  value: this.heavyPollutionEnterpriseStrore[selectIndex]
                    .heavilyPollutingEnterpriseId,
                  lng: this.heavyPollutionEnterpriseStrore[selectIndex].lng,
                  lat: this.heavyPollutionEnterpriseStrore[selectIndex].lat,
                },
              },
            },
          }
        : {
            target: {
              w: {
                data: {},
              },
            },
          };
    this.$emit("change", this.currentSelectedBuildingObj);
    this.selectIndex = selectIndex;
    const currentSelect = this.heavyPollutionEnterpriseStrore[selectIndex];
    this.currentEnterpriseInfor = {
      name: currentSelect.heavilyPollutingEnterpriseName,
      district: currentSelect.administrativeDivision,
      type: currentSelect.heavilyPollutingEnterpriseType,
      address: currentSelect.heavilyPollutingEnterpriseAddress,
      status: currentSelect.heavilyPollutingEnterpriseStatus,
      kind: currentSelect.industryCategory,
      principal: currentSelect.principal,
      principalTel: currentSelect.principalPhoneNumber,
      licence: currentSelect.haveSewageDischargePermission ? "拥有" : "无",
      emergencyPlan: currentSelect.haveEnvironmentalEmergencyPlan,
    };
    this.fetchPollutionCount();
    this.fetchPollutionEnterprise();
    // this.switchEnterpriseSelectTimer();
    if (value == "510100000004") {
      // 水七厂
      this.enterpriseText = "水五(七)厂";
      this.getSevenWaterList();
    } else if (value == "0028CDSCDSWC01") {
      // 水五厂
      this.enterpriseText = "水五(七)厂";
      this.getFiveWaterList();
    } else {
      this.enterpriseText = "";
    }
  }
  // 水七厂
  private getSevenWaterList() {
    getSevenWaterList().then((res: any) => {
      this.polluteDetails = res.data.data;
    });
  }
  // 水五厂
  private getFiveWaterList() {
    getFiveWaterList().then((res: any) => {
      this.polluteDetails = res.data.data;
    });
  }
  // 重污染企业Select轮播处理
  private switchEnterpriseSelectTimer(): void {
    const total = this.heavyPollutionEnterpriseStrore.length;
    this.switchEnterpriseTimerStore = setInterval(() => {
      this.selectIndex =
        this.selectIndex + 1 >= this.heavyPollutionEnterpriseStrore.length
          ? 0
          : this.selectIndex + 1;
      this.defaultEnterpriseValue = this.heavyPollutionEnterpriseStrore[
        this.selectIndex
      ].heavilyPollutingEnterpriseId;
      this.heavilyPollutingEnterpriseChage(this.defaultEnterpriseValue);
    }, 10 * 1000);
  }
  private siteCount: number | string = 0; //在建工地数量
  private companyList: any[] = [];
  private curSite = ""; //当前选中工地id
  private curSiteIndex = 0; //当前选中工地索引
  private curSitePM10: any = {}; //当前选中工地的PM10
  // 获取重污染工地列表
  private fetchcompanyList(): void {
    let online =
      this.online.indexOf(2) > -1 && this.online.length == 1
        ? 1
        : this.online.indexOf(3) > -1 && this.online.length == 1
        ? 0
        : undefined;
    getCompanyList({
      streetCode: this.currStreet === "全部街道" ? undefined : this.currStreet,
      keywords: this.enterpriseSearch || undefined,
      online: online,
    }).then((res: any) => {
      const data = res.data.data;
      // this.siteCount =
      //     data.length > 99 ? String(data.length) : "0" + data.length;
      // if (this.type ===0) {
      //     this.$emit('sitecount', this.siteCount)
      // }
      this.companyList = (this.type == 0 ? data : [])
        .filter((item: any) => item.gcLng)
        .map((item: any) => {
          item.companyId = item.projectId;
          return item;
        });
      if (this.type === 0) {
        this.$emit("constrSite", this.companyList);
      }
      this.curSite =
        this.type == 0 && this.companyList.length
          ? this.companyList[0].companyId
          : "";
      if (this.curSite) {
        this.getTwentyFourHour();
      } else {
        this.realTimeData = {
          bottomList: [],
          dataList: [],
          dataList1: [],
          standard: 0,
          max: 0,
          unit: "μg/m3",
          name: "",
        };
      }
      // this.curMarkerClickMarker = this.companyList[0].companyId;
      // this.curMarkerClickObj.target.w.data = this.companyList[0];
      // this.fetchCurSitePM10();
    });
  }
  // 获取24小时监测值
  getTwentyFourHour() {
    getTwentyFourHour(this.curSite).then((res) => {
      const data = res.data.data.data || [];
      const data2 = res.data.data.npm10 || [];
      const bottomList = data.map((item: any) => item.hour);
      const dataList = data.map((item: any) => item.value);
      let data1 = dataList.sort((a: any, b: any) => a - b);
      this.realTimeData = {
        bottomList: bottomList || [],
        dataList: dataList || [],
        dataList1: data2,
        standard: res.data.data.standard,
        max:
          (data1.length ? data1[data1.length - 1] : 0) <
          (res.data.standard || 0)
            ? res.data.standard || 0
            : (data1.length ? data1[data1.length - 1] : 0) || 0,
        unit: "μg/m3",
        name: "pm10",
        name1: "npm10",
      };
    });
  }
  //获取当前工地PM10相关信息
  private fetchCurSitePM10(): void {
    getCurSitePM10(this.curSite).then((res) => {
      const data = res.data.data;
      if (data) {
        data.inhalableParticles = Math.round(data.inhalableParticles);
        this.curSitePM10 = data;
      } else {
        this.curSitePM10 = null;
      }
    });
  }
  //下拉选择工地、噪声站点
  private curSiteChange(value: any): void {
    const fn = _.throttle(() => {
      this.curSite = value;
      this.curSiteIndex = this.companyList.findIndex((item: any) => {
        return item.companyId == this.curSite;
      });
      if (this.curSiteIndex > -1) {
        this.curMarkerClickMarker = this.companyList[
          this.curSiteIndex
        ].companyId;
        if (this.type === 9) {
          this.HCPieTabName = this.companyList[this.curSiteIndex].pointList
            .length
            ? this.companyList[this.curSiteIndex].pointList[0].pointId
            : "";
        }
        this.$emit("changeSite", this.curMarkerClickMarker);
        this.curMarkerClickObj.target.w.data = this.companyList[
          this.curSiteIndex
        ];
        if (this.type === 0) {
          this.getTwentyFourHour();
        } else if (this.type === 6) {
          this.getDataComplianRate();
          this.getRightHourAnalyze();
        } else if (this.type === 7) {
          this.getDataComplianRateE();
          this.getRightHourAnalyzeE();
        } else if (this.type === 8) {
          this.getSolidWasteListRecord();
          this.getSolidWasteProportionTotal();
        } else if (this.type === 9) {
          this.getHCLineData();
          this.getHCPieData();
        } else if (this.type === 13) {
          this.getBGLineData();
          this.getBGPieData();
        }
      }
    }, 2500);
    fn();
    // this.fetchCurSitePM10();
  }
  // 工地地图点击
  private getCurSiteFromMap(value: any): void {
    this.curSiteChange(value);
    if ([6, 7, 8, 9, 13].includes(this.index)) return;
    this.showType = 1;
    if (this.enterpriseSearch !== "") {
      // this.enterpriseSearchBtn();
    } else {
      // 企业
      // this.getHeavilyPollutingEnterpriseList();
      // 工地
      // this.fetchcompanyList();
    }
    getSiteVideoList(value).then((res) => {
      const data = res.data.data;
      this.videoList = data;
      if (this.videoList.length) {
        this.curVideo = this.videoList[0].cameraId;
        // this.fetchCameraUrl()
      } else {
        this.curVideo = "";
      }
    });
  }
  //污染源类型切换
  private changePollutionType(index: number): void {
    this.curPollutionIndex = index;
  }
  //污染源总数量
  private pollutionAllCount: any = {};
  private fetchPollutionCount(): void {
    getPollutionCount(this.defaultEnterpriseValue).then((res) => {
      const data = res.data.data;
      data.countOfWastewater = data.countOfWastewater
        ? Math.round(data.countOfWastewater)
        : 0;
      data.countOfGas = data.countOfGas ? Math.round(data.countOfGas) : 0;
      data.countOfWaste = data.countOfWaste ? Math.round(data.countOfWaste) : 0;
      data.updateTime = moment(data.updateTime).format("YYYY-MM-DD HH:mm");
      this.pollutionAllCount = data;
    });
  }
  //污染源类型下标
  private curPollutionIndex = 0;
  //污染监测物
  private pollutionSourceList: {
    nameList: string[];
    dataList: number[] | string[];
    unit: string;
  }[] = [
    {
      nameList: [],
      dataList: [],
      unit: "",
    },
    {
      nameList: [],
      dataList: [],
      unit: "",
    },
    {
      nameList: [],
      dataList: [],
      unit: "",
    },
  ];
  //污染源分布图
  private fetchPollutionEnterprise(): void {
    this.curPollutionIndex = 0;
    getPollutionEnterprise(this.defaultEnterpriseValue).then((res: any) => {
      const data = res.data.data;
      const pollutionArr: any = [];
      const list = [
        data.wastewaterDischargeList,
        data.exhaustGasList,
        data.wasteDischargeList,
      ];
      list.forEach((item: any) => {
        const obj: any = { nameList: [], dataList: [], unit: "", show: false };
        item.forEach((child: any) => {
          obj.nameList.push(child.name);
          obj.dataList.push(Number(child.value).toFixed(2));
          obj.unit = child.unit;
        });
        obj.show = item.length ? true : false;
        pollutionArr.push(obj);
      });
      this.pollutionSourceList = pollutionArr;
    });
  }
  // 重污企业swiper配置项
  private swiperOptionPollution = {
    direction: "vertical",
    slidesPerView: 4,
    slidesPerGroup: 1,
    loop: true,
    autoplay: {
      delay: 5000,
      disableOnInteraction: false,
    },
  };
  //重污企业
  private fetchHeavyPollution() {
    return new Promise<void>((resolve, reject) => {
      heavilyPollutingEnterpriseList(510106, this.pollutionType).then(
        (res: any) => {
          const data = res.data.data;
          if (data && data.length) {
            this.heavyPollution = data;
          }
          resolve();
        }
      );
    });
  }
  //重污企业下拉改变
  private pollutionStationChange(type: any) {
    this.pollutionType = type;
    this.fetchHeavyPollution();
  }
  // 地图重污染企业选中处理
  private curCompanyChange(BuildingId: string): void {
    // clearInterval(this.switchEnterpriseTimerStore);
    this.heavilyPollutingEnterpriseChage(BuildingId);
    this.showType = 0;
    if (this.enterpriseSearch !== "") {
      // this.enterpriseSearchBtn();
    } else {
      // 企业
      // this.getHeavilyPollutingEnterpriseList();
      // 工地
      this.fetchcompanyList();
    }
    // this.switchEnterpriseSelectTimer();
  }
  private companyAlarmList: any = [];
  private getcompanyAlarmList() {
    getcompanyAlarmList().then((res: any) => {
      for (const item of res.data.data) {
        item.alarmTime = item.alarmTime
          ? moment(item.alarmTime).format("MM月DD日 HH:mm")
          : "";
      }
      this.companyAlarmList = res.data.data;
    });
  }
  // 噪声地图列表
  private paGetNoiseMapList() {
    let online = undefined;
    if (!this.online.length) return;
    if (this.online.length == 2) {
      let list: any = [];
      this.online.forEach((status) => {
        getNoiseMapList({
          status,
          streetCode:
            this.currStreet === "全部街道" ? undefined : this.currStreet,
          keywords: this.searchcurWords || undefined,
          functionType: this.noiseType || undefined,
        }).then((res) => {
          const data = res.data.data || [];
          const arr = (this.type == 6 ? data : [])
            .filter((item: any) => item.longitude)
            .map((item: any) => {
              item.companyId = item.stationId;
              return item;
            });
          list = [...list, ...arr];
          this.companyList = JSON.parse(JSON.stringify(list));
          this.curSite = this.companyList.length
            ? this.companyList[0].companyId
            : "";
          this.curSiteChange(this.curSite);
          if (this.type == 6) {
            this.$emit("constrSite", this.companyList);
          }
        });
      });
    } else if (this.online.length == 1) {
      this.online.forEach((status) => {
        getNoiseMapList({
          status,
          streetCode:
            this.currStreet === "全部街道" ? undefined : this.currStreet,
          keywords: this.searchcurWords || undefined,
          functionType: this.noiseType || undefined,
        }).then((res) => {
          const data = res.data.data || [];
          const arr = (this.type == 6 ? data : [])
            .filter((item: any) => item.longitude)
            .map((item: any) => {
              item.companyId = item.stationId;
              return item;
            });
          this.companyList = arr;
          this.curSite = this.companyList.length
            ? this.companyList[0].companyId
            : "";
          this.curSiteChange(this.curSite);
          if (this.type == 6) {
            this.$emit("constrSite", this.companyList);
          }
        });
      });
    } else {
      getNoiseMapList({
        status: online,
        streetCode:
          this.currStreet === "全部街道" ? undefined : this.currStreet,
        keywords: this.searchcurWords || undefined,
        functionType: this.noiseType,
      }).then((res) => {
        const data = res.data.data || [];
        this.companyList = (this.type == 6 ? data : [])
          .filter((item: any) => item.longitude)
          .map((item: any) => {
            item.companyId = item.stationId;
            return item;
          });
        this.curSite = this.companyList.length
          ? this.companyList[0].companyId
          : "";
        this.curSiteChange(this.curSite);
        if (this.type == 6) {
          this.$emit("constrSite", this.companyList);
        }
      });
    }
  }
  // 噪声右侧昼夜达标率
  private getDataComplianRate() {
    this.PieLoading = true;
    getDataComplianceRate({ stationId: this.curSite })
      .then((res) => {
        this.noiseBarData = {
          dataList: [{}, {}],
        };
        if (res.data && res.data.data) {
          this.$nextTick(() => {
            this.noiseBarData.bottomList = res.data.data.map((v: any) =>
              v.date.slice(5, 10)
            );
            this.noiseBarData.dataList[0].name = "昼间";
            this.noiseBarData.dataList[0].colors = ["#42A8FF", "#082641"];
            this.noiseBarData.dataList[0].data = res.data.data.map((v: any) => {
              return {
                rate: v.dayTime ? v.dayTime.complianceRate : 0,
                dB: v.dayTime ? v.dayTime.ld : 0,
                total: v.dayTime ? v.dayTime.total : 0,
                count: v.dayTime ? v.dayTime.upToStandardCount : 0,
              };
            });
            this.noiseBarData.dataList[1].name = "夜间";
            this.noiseBarData.dataList[1].colors = ["#98ECD7", "#072222"];
            this.noiseBarData.dataList[1].data = res.data.data.map((v: any) => {
              return {
                rate: v.nighTime ? v.nighTime.complianceRate : 0,
                dB: v.nighTime ? v.nighTime.ld : 0,
                total: v.nighTime ? v.nighTime.total : 0,
                count: v.nighTime ? v.nighTime.upToStandardCount : 0,
              };
            });
          });
        }
      })
      .finally(() => {
        this.PieLoading = false;
      });
  }
  // 噪声右侧站点监测趋势
  private getRightHourAnalyze() {
    this.clearChartDataZS();
    this.LineLoading = true;
    const fn = this.lineDataType === "hour" ? getHourAnalyze : getDaysAnalyze;
    fn({ stationId: this.curSite })
      .then((res) => {
        const data = res.data.data;
        if (!data) return;
        const nameList = data.itemList;
        this.subTabs = data.itemList;
        this.subTabName = data.itemList[0];
        this.standardObj = {
          Ld: data.daytime,
          Ln: data.nighttime,
        };
        nameList.forEach((ele: any) => {
          this.noiseLineData[ele] = {
            bottomList: [],
            dataList: [],
            max: 0,
            unit: "",
          };
          this.noiseLineData[ele].bottomList =
            this.lineDataType === "hour"
              ? data[ele].x.map((v: any) => {
                  return {
                    fullDate: v,
                    x: v.slice(6, 11),
                  };
                })
              : data[ele].x.map((v: any) => {
                  return {
                    fullDate: v,
                    x: v,
                  };
                });
          const maxY = Math.max(...data[ele].vale);
          this.noiseLineData[ele].dataList = data[ele].vale;
          if (ele == "Ld") {
            this.noiseLineData[ele].max =
              maxY > (this.standardObj.Ld || 0)
                ? maxY
                : +this.standardObj.Ld + 10;
          } else if (ele == "Ln") {
            this.noiseLineData[ele].max =
              maxY > (this.standardObj.Ln || 0)
                ? maxY
                : +this.standardObj.Ln + 10;
          } else {
            this.noiseLineData[ele].max = maxY;
          }
          this.noiseLineData[ele].unit = data.unit;
        });
      })
      .finally(() => {
        this.LineLoading = false;
      });
  }
  // 用电量地图列表
  private getElectricityMapList() {
    let online = undefined;
    this.companyList = [];
    if (!this.online.length) return;
    if (this.online.length == 2) {
      let list: any = [];
      this.online.forEach((status) => {
        getElectricityMapList({
          status,
          streetCode:
            this.currStreet === "全部街道" ? undefined : this.currStreet,
          keywords: this.searchcurWords || undefined,
        }).then((res) => {
          const data = res.data.data || [];
          const arr = (this.type == 7 ? data : [])
            .filter((item: any) => item.longitude)
            .map((item: any) => {
              item.companyId = item.id;
              return item;
            });
          list = [...list, ...arr];
          this.companyList = JSON.parse(JSON.stringify(list));
          this.curSite = this.companyList.length
            ? this.companyList[0].companyId
            : "";
          this.curSiteChange(this.curSite);
          if (!this.companyList.length) {
            this.electricityBarData = {};
            this.electricityLineData = {};
          }
          if (this.type == 7) {
            this.$emit("constrSite", this.companyList);
          }
        });
      });
    } else if (this.online.length == 1) {
      this.online.forEach((status) => {
        getElectricityMapList({
          status,
          streetCode:
            this.currStreet === "全部街道" ? undefined : this.currStreet,
          keywords: this.searchcurWords || undefined,
        }).then((res) => {
          const data = res.data.data || [];
          const arr = (this.type == 7 ? data : [])
            .filter((item: any) => item.longitude)
            .map((item: any) => {
              item.companyId = item.id;
              return item;
            });
          this.companyList = arr;
          this.curSite = this.companyList.length
            ? this.companyList[0].companyId
            : "";
          this.curSiteChange(this.curSite);
          if (!this.companyList.length) {
            this.electricityBarData = {};
            this.electricityLineData = {};
          }
          if (this.type == 7) {
            this.$emit("constrSite", this.companyList);
          }
        });
      });
    } else {
      getElectricityMapList({
        status: online,
        streetCode:
          this.currStreet === "全部街道" ? undefined : this.currStreet,
        keywords: this.searchcurWords || undefined,
      }).then((res) => {
        const data = res.data.data || [];
        this.companyList = (this.type == 7 ? data : [])
          .filter((item: any) => item.longitude)
          .map((item: any) => {
            item.companyId = item.id;
            return item;
          });
        this.curSite = this.companyList.length
          ? this.companyList[0].companyId
          : "";
        this.curSiteChange(this.curSite);
        if (!this.companyList.length) {
          this.electricityBarData = {};
          this.electricityLineData = {};
        }
        if (this.type == 7) {
          this.$emit("constrSite", this.companyList);
        }
      });
    }
  }
  // 用电量右侧昼夜达标率
  private getDataComplianRateE() {
    this.electricityBarData = {
      dataList: [],
      bottomList: [],
    };
    this.PieLoading = true;
    const endTime = momentjs().format("YYYY-MM-DD HH:mm:ss");
    const startTime = momentjs()
      .subtract(6, "day")
      .format("YYYY-MM-DD HH:mm:ss");
    getDataComplianceRateE({ stationId: this.curSite, endTime, startTime })
      .then((res) => {
        if (!res.data || !res.data.data) return;
        this.electricityBarData.bottomList = res.data.data.map((v: any) => {
          return v.monitorTime;
        });
        this.electricityBarData.dataList = res.data.data;
        this.electricityBarData.standard = res.data.data.length
          ? res.data.data[0].threshold
          : 0;
      })
      .finally(() => {
        this.PieLoading = false;
      });
  }
  // 用电量右侧站点监测趋势
  private getRightHourAnalyzeE() {
    this.electricityLineData = {};
    this.LineLoading = true;
    const houRendTime = momentjs().format("YYYY-MM-DD HH:mm:ss");
    const houRstartTime = momentjs()
      .subtract(1, "day")
      .format("YYYY-MM-DD HH:mm:ss");
    const daYendTime = momentjs().format("YYYY-MM-DD HH:mm:ss");
    const daYstartTime = momentjs()
      .subtract(6, "day")
      .format("YYYY-MM-DD HH:mm:ss");
    const obj =
      this.lineDataType === "hour"
        ? {
            stationId: this.curSite,
            endTime: houRendTime,
            startTime: houRstartTime,
          }
        : {
            stationId: this.curSite,
            endTime: daYendTime,
            startTime: daYstartTime,
          };
    const fn = this.lineDataType === "hour" ? getDayHourRecord : getDayRecord;
    fn(obj)
      .then((res) => {
        const data = res.data.data;
        if (!data) return;
        this.$set(
          this.electricityLineData,
          "bottomList",
          data.x.map((v: any) => {
            return {
              fullDate: v,
              x: v.slice(5, 11),
            };
          })
        );
        this.$set(this.electricityLineData, "dataList", data.values);
        this.$set(this.electricityLineData, "unit", data.unit);
        this.$set(this.electricityLineData, "standard", data.threshold || null);
      })
      .finally(() => {
        this.LineLoading = false;
      });
  }
  // 固废地图列表
  private getSolidWasteMapList() {
    let online = undefined;
    this.companyList = [];
    if (!this.online.length) return;
    if (this.online.length == 2) {
      let list: any = [];
      this.online.forEach((status) => {
        getSolidWasteMapList({
          status,
          streetCode:
            this.currStreet === "全部街道" ? undefined : this.currStreet,
          keywords: this.searchcurWords || undefined,
          enterpriseType: this.wasteType || undefined,
        }).then((res) => {
          const data = res.data.data || [];
          const arr = (this.type == 8 ? data : [])
            .filter((item: any) => item.lng)
            .map((item: any) => {
              item.companyId = item.wasteSolidDeviceId;
              return item;
            });
          list = [...list, ...arr];
          this.companyList = JSON.parse(JSON.stringify(list));
          this.curSite = this.companyList.length
            ? this.companyList[0].companyId
            : "";
          this.curSiteChange(this.curSite);
          if (!this.companyList.length) {
            this.wastePieDate = {};
            this.wasteLineData = {};
          }
          if (this.type == 8) {
            this.$emit("constrSite", this.companyList);
          }
        });
      });
    } else if (this.online.length == 1) {
      this.online.forEach((status) => {
        getSolidWasteMapList({
          status,
          streetCode:
            this.currStreet === "全部街道" ? undefined : this.currStreet,
          keywords: this.searchcurWords || undefined,
          enterpriseType: this.wasteType || undefined,
        }).then((res) => {
          const data = res.data.data || [];
          const arr = (this.type == 8 ? data : [])
            .filter((item: any) => item.lng)
            .map((item: any) => {
              item.companyId = item.wasteSolidDeviceId;
              return item;
            });
          this.companyList = arr;
          this.curSite = this.companyList.length
            ? this.companyList[0].companyId
            : "";
          this.curSiteChange(this.curSite);
          if (!this.companyList.length) {
            this.wastePieDate = {};
            this.wasteLineData = {};
          }
          if (this.type == 8) {
            this.$emit("constrSite", this.companyList);
          }
        });
      });
    } else {
      getSolidWasteMapList({
        status: online,
        streetCode:
          this.currStreet === "全部街道" ? undefined : this.currStreet,
        keywords: this.searchcurWords || undefined,
        enterpriseType: this.wasteType || undefined,
      }).then((res) => {
        const data = res.data.data || [];
        this.companyList = (this.type == 8 ? data : [])
          .filter((item: any) => item.lng)
          .map((item: any) => {
            item.companyId = item.wasteSolidDeviceId;
            return item;
          });
        this.curSite = this.companyList.length
          ? this.companyList[0].companyId
          : "";
        this.curSiteChange(this.curSite);
        if (!this.companyList.length) {
          this.wastePieDate = {};
          this.wasteLineData = {};
        }
        if (this.type == 8) {
          this.$emit("constrSite", this.companyList);
        }
      });
    }
  }
  // 固废右侧监测趋势线性图
  private getSolidWasteListRecord() {
    this.wasteLineData = {};
    this.LineLoading = true;
    const houRendTime = momentjs().format("YYYY-MM-DD HH:mm:ss");
    const houRstartTime = momentjs()
      .subtract(6, "day")
      .format("YYYY-MM-DD HH:mm:ss");
    const daYendTime = momentjs().format("YYYY-MM-DD HH:mm:ss");
    const daYstartTime = momentjs()
      .subtract(30, "day")
      .format("YYYY-MM-DD HH:mm:ss");
    const obj =
      this.wasteLineDataType === 7
        ? {
            stationId: this.curSite,
            endTime: houRendTime,
            startTime: houRstartTime,
          }
        : {
            stationId: this.curSite,
            endTime: daYendTime,
            startTime: daYstartTime,
          };
    getSolidWasteListRecord(obj)
      .then((res) => {
        const data = res.data.data;
        if (!data) return;
        this.wasteLineData = data;
        this.wasteUnit = data.unit;
        this.wastesubTabs = data.itemList;
        if (!this.wastesubTabName) {
          this.wastesubTabName = data.itemList[0];
        }
        const arr = ["stock", "in", "out"];
        data.itemList.forEach((item: any, i: any) => {
          this.wastesubTabNameMap[item] = arr[i];
        });
      })
      .finally(() => {
        this.LineLoading = false;
      });
  }
  // 固废右侧饼图
  private getSolidWasteProportionTotal() {
    this.wastePieDate = {};
    this.PieLoading = true;
    getSolidWasteProportionTotal({
      stationId: this.curSite,
      date: this.wasteSelDate + "-01",
    })
      .then((res) => {
        const data = res.data ? res.data.data : null;
        if (!data) return;
        this.wastePieDate = data;
      })
      .finally(() => {
        this.PieLoading = false;
      });
  }
  // 重污地图列表
  private getHeavyCorruptMapList() {
    let online = undefined;
    this.companyList = [];
    if (!this.online.length) return;
    if (this.online.length == 2) {
      let list: any = [];
      this.online.forEach((status) => {
        getHeavyCorruptMapList({
          status,
          streetCode:
            this.currStreet === "全部街道" ? undefined : this.currStreet,
          keywords: this.searchcurWords || undefined,
        }).then((res) => {
          const data = res.data.data || [];
          const arr = (this.type == 9 ? data : [])
            .filter((item: any) => item.longitude)
            .map((item: any) => {
              item.companyId = item.heavilyPollutingEnterpriseId;
              return item;
            });
          list = [...list, ...arr];
          this.companyList = JSON.parse(JSON.stringify(list));
          this.curSite = this.companyList.length
            ? this.companyList[0].companyId
            : "";
          this.HCPieTabName = this.companyList.length
            ? this.companyList[0].pointList[0]
              ? this.companyList[0].pointList[0] &&
                this.companyList[0].pointList[0].pointId
              : ""
            : "";
          this.curSiteChange(this.curSite);
          if (!this.companyList.length) {
            this.HCPdata = { list: [] };
            this.HCLineData = {};
          }
          if (this.type == 9) {
            this.$emit("constrSite", this.companyList);
          }
        });
      });
    } else if (this.online.length == 1) {
      this.online.forEach((status) => {
        getHeavyCorruptMapList({
          status,
          streetCode:
            this.currStreet === "全部街道" ? undefined : this.currStreet,
          keywords: this.searchcurWords || undefined,
        }).then((res) => {
          const data = res.data.data || [];
          const arr = (this.type == 9 ? data : [])
            .filter((item: any) => item.longitude)
            .map((item: any) => {
              item.companyId = item.heavilyPollutingEnterpriseId;
              return item;
            });
          this.companyList = arr;
          this.curSite = this.companyList.length
            ? this.companyList[0].companyId
            : "";
          this.HCPieTabName = this.companyList.length
            ? this.companyList[0].pointList[0]
              ? this.companyList[0].pointList[0] &&
                this.companyList[0].pointList[0].pointId
              : ""
            : "";
          this.curSiteChange(this.curSite);
          if (!this.companyList.length) {
            this.HCPdata = { list: [] };
            this.HCLineData = {};
          }
          if (this.type == 9) {
            this.$emit("constrSite", this.companyList);
          }
        });
      });
    } else {
      getHeavyCorruptMapList({
        status: online,
        streetCode:
          this.currStreet === "全部街道" ? undefined : this.currStreet,
        keywords: this.searchcurWords || undefined,
      }).then((res: any) => {
        const data = res.data.data || [];
        this.companyList = (this.type == 9 ? data : [])
          .filter((item: any) => item.longitude)
          .map((item: any) => {
            item.companyId = item.heavilyPollutingEnterpriseId;
            return item;
          });
        this.curSite = this.companyList.length
          ? this.companyList[0].companyId
          : "";
        this.HCPieTabName = this.companyList.length
          ? this.companyList[0].pointList[0] &&
            this.companyList[0].pointList[0].pointId
          : "";
        this.curSiteChange(this.curSite);
        if (!this.companyList.length) {
          this.HCPdata = { list: [] };
          this.HCLineData = {};
        }
        if (this.type == 9) {
          this.$emit("constrSite", this.companyList);
        }
      });
    }
  }
  // 重污右侧监测趋势线性图
  private getHCLineData() {
    this.HCLineData = {};
    this.LineLoading = true;
    const houRendTime = momentjs().format("YYYY-MM-DD HH:mm:ss");
    const houRstartTime = momentjs()
      .subtract(1, "day")
      .format("YYYY-MM-DD HH:mm:ss");
    const daYendTime = momentjs().format("YYYY-MM-DD HH:mm:ss");
    const daYstartTime = momentjs()
      .subtract(7, "day")
      .format("YYYY-MM-DD HH:mm:ss");
    const obj =
      this.HCLineType === "hour"
        ? {
            stationId: this.curSite,
            endTime: houRendTime,
            startTime: houRstartTime,
          }
        : {
            stationId: this.curSite,
            endTime: daYendTime,
            startTime: daYstartTime,
          };
    let fn =
      this.HCLineType === "hour" ? getHeavyCorruptHour : getHeavyCorruptDay;
    fn(obj)
      .then((res) => {
        const { data } = res.data;
        this.HCsubTabs = data.itemList;
        this.HCsubTabName = data.itemList ? data.itemList[0] : "";
        data.itemList &&
          data.itemList.forEach((v: any) => {
            let arr = data.pointList;
            this.HCLineData[v] = arr.map((o: any) => {
              return o[v]
                ? { ...o[v], name: o.pointName }
                : { name: o.pointName };
            });
          });
      })
      .finally(() => {
        this.LineLoading = false;
      });
  }
  // 重污右侧饼图
  private getHCPieData() {
    this.HCPdata = { list: [] };
    this.PieLoading = true;
    getHeavyCorruptPie({ pointId: this.HCPieTabName })
      .then((res) => {
        const data = res.data ? res.data.data : null;
        if (!data) {
          this.HCPdata = {};
          return;
        }
        this.HCPdata = {
          ...data,
        };
      })
      .finally(() => {
        this.PieLoading = false;
      });
  }
  // 雷达指纹库
  private getRandaFigData(location: any) {
    randaListStation(location).then((res) => {
      // console.log(res.data, '--------3350----getRandaFigData');
      this.RDFigList = res.data.data || [];
    });
  }
  // 雷达监测趋势
  private getRandaTrend(location: any) {
    let type =
      this.RDSubName == "消光系数"
        ? "1"
        : this.RDSubName == "PM₁₀"
        ? "2"
        : this.RDSubName == "PM₂.₅"
        ? "3"
        : "4";
    let groupFileName = this.randaItem;
    randaMonitorTrend({ ...location, type, groupFileName }).then((res) => {
      // console.log(res.data,'------------3356----getRandaTrend');
      this.RDLineData.bottomList = res.data.data
        ? res.data.data.monitorItemList.map((v: any) => v.x)
        : [];
      this.RDLineData.dataList = res.data.data
        ? res.data.data.monitorItemList.map((v: any) => v.y)
        : [];
      this.RDLineData.standard = res.data.data ? res.data.data.threshold : null;
    });
  }
  // 雷达污染点位历史记录
  private getRandaAlarmCount(location: any) {
    let type =
      this.RDSubName == "消光系数"
        ? "1"
        : this.RDSubName == "PM₁₀"
        ? "2"
        : this.RDSubName == "PM₂.₅"
        ? "3"
        : "4";
    let groupFileName = this.randaItem;
    randaAlarmCount({ ...location, type, groupFileName }).then((res) => {
      // console.log(res.data.data,'------4087----getRandaAlarmCount');
      const { data } = res.data;
      if (!data) return;
      this.RDHistoryTitle[0].count = data.day;
      this.RDHistoryTitle[1].count = data.week;
      this.RDHistoryTitle[2].count = data.month;
      this.RDHistoryList = data.alarmList;
    });
  }
  // 获取黑烟车地图列表
  private paGetGasMapList() {
    this.GTPiedata = {};
    this.GTLineData = {};
    let online = undefined;
    if (!this.online.length) return;
    if (this.online.length == 2) {
      let list: any = [];
      this.online.forEach((status) => {
        getGasTruckMapList({
          status,
          streetCode:
            this.currStreet === "全部街道" ? undefined : this.currStreet,
          keywords: this.searchcurWords || undefined,
        }).then((res) => {
          const data = res.data.data || [];
          const arr = (this.type == 13 ? data : [])
            .filter((item: any) => item.lng)
            .map((item: any) => {
              item.companyId = item.deviceNo;
              return item;
            });
          list = [...list, ...arr];
          this.companyList = JSON.parse(JSON.stringify(list));
          this.curSite = this.companyList.length
            ? this.companyList[0].companyId
            : "";
          this.curSiteChange(this.curSite);
          if (this.type == 13) {
            this.$emit("constrSite", this.companyList);
          }
        });
      });
    } else if (this.online.length == 1) {
      this.online.forEach((status) => {
        getGasTruckMapList({
          status,
          streetCode:
            this.currStreet === "全部街道" ? undefined : this.currStreet,
          keywords: this.searchcurWords || undefined,
        }).then((res) => {
          const data = res.data.data || [];
          const arr = (this.type == 13 ? data : [])
            .filter((item: any) => item.lng)
            .map((item: any) => {
              item.companyId = item.deviceNo;
              return item;
            });
          this.companyList = arr;
          this.curSite = this.companyList.length
            ? this.companyList[0].companyId
            : "";
          this.curSiteChange(this.curSite);
          if (this.type == 13) {
            this.$emit("constrSite", this.companyList);
          }
        });
      });
    } else {
      getGasTruckMapList({
        status: online,
        streetCode:
          this.currStreet === "全部街道" ? undefined : this.currStreet,
        keywords: this.searchcurWords || undefined,
      }).then((res) => {
        const data = res.data.data || [];
        this.companyList = (this.type == 13 ? data : [])
          .filter((item: any) => item.lng)
          .map((item: any) => {
            item.companyId = item.deviceNo;
            return item;
          });
        this.curSite = this.companyList.length
          ? this.companyList[0].companyId
          : "";
        this.curSiteChange(this.curSite);
        if (this.type == 13) {
          this.$emit("constrSite", this.companyList);
        }
      });
    }
  }
  // 黑烟车右侧监测趋势线性图
  private getBGLineData() {
    this.GTLineData = {};
    const houRendTime = momentjs().format("YYYY-MM-DD HH:mm:ss");
    const houRstartTime = momentjs()
      .subtract(1, "day")
      .format("YYYY-MM-DD HH:mm:ss");
    const daYendTime = momentjs().format("YYYY-MM-DD HH:mm:ss");
    const daYstartTime = momentjs()
      .subtract(7, "day")
      .format("YYYY-MM-DD HH:mm:ss");
    const obj =
      this.GTLineType === "hour"
        ? {
            stationId: this.curSite,
            endTime: houRendTime,
            startTime: houRstartTime,
          }
        : {
            stationId: this.curSite,
            endTime: daYendTime,
            startTime: daYstartTime,
          };
    let fn = this.GTLineType === "hour" ? getGasHourList : getGasDayList;
    this.LineLoading = true;
    fn(obj)
      .then((res) => {
        const { data } = res.data;
        const bottomList = data.map((v: any) => v.monitorTime);
        const 车流量 = {
          bottomList,
          dataList: data.map((v: any) => v.trafficFlow),
        };
        const 黑烟车 = {
          bottomList,
          dataList: data.map((v: any) => v.smokyCarCount),
        };
        const 柴油车 = {
          bottomList,
          dataList: data.map((v: any) => v.dieselFuelCount),
        };
        const 汽油车 = {
          bottomList,
          dataList: data.map((v: any) => v.gasolineCarCount),
        };
        this.GTLineData = {
          车流量,
          黑烟车,
          柴油车,
          汽油车,
        };
      })
      .finally(() => {
        this.LineLoading = false;
      });
  }
  // 黑烟车右侧饼图
  private getBGPieData() {
    this.GTPiedata = {};
    this.PieLoading = true;
    getGasPieData({ stationId: this.curSite })
      .then((res) => {
        this.GTPiedata = res.data.data;
      })
      .finally(() => {
        this.PieLoading = false;
      });
  }
}
</script>
