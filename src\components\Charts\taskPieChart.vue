<style lang="less" scoped>
    .no-data {
        font-size: 0.2rem;
        display: flex;
        align-items: center;
        justify-content: center;
    }
</style>
<template>
    <div :id="id" v-if="PieChartData.dataList.length" :style="{ height: height, width: width }"></div>
    <div v-else class="no-data" :style="{ height: height, width: width }">
        暂无任务统计
    </div>
</template>

<script lang="ts">
import * as echarts from 'echarts'
import { Component, Prop, Watch } from "vue-property-decorator";
import { mixins } from "vue-class-component";
import ResizeMixin from "./mixins/resize";
interface InData {
    name: string;
    dataList: any[];
    colorList: string[];
}
@Component({
    name: "airPieChart"
})
export default class extends mixins(ResizeMixin) {
    // @Prop({ default: "chart" }) private className!: string;
    @Prop({ default: "charts" }) private id!: string;
    @Prop({ default: "200px" }) private width!: string;
    @Prop({ default: "200px" }) private height!: string;
    @Prop({ required: true }) private PieChartData!: InData;
    @Watch("PieChartData", { immediate: true, deep: true }) public onMsgChanged(
      newValue: InData,
      oldValue: InData
    ) {
      if (this.PieChartData) {
        if (this.chart) {
          this.chart.clear()
          this.chart = null
        }
        this.$nextTick(() => {
          this.initChart();
        });
      }
    }
    // private chart: any;
    private timer: any;
    private chartCurrentIndex = 0;
    private colors: string[] = [];
    private option: any;
    mounted() {
      if (this.PieChartData) {
        if (this.chart) {
          this.chart.clear();
        }
        this.initChart();
      }
    }
    beforeDestroy() {
        if (!this.chart) {
            return;
        }
        this.chart.clear();
        this.chart.dispose();
        this.chart = null;
        clearInterval(this.timer);
    }
    private initChart() {
        if (!this.id) {
          return
        }
        this.chart = echarts.init(document.getElementById(this.id) as any)
        const list = ['未完成','已关闭','已完成']
        this.colors = this.PieChartData.colorList;
        this.option = {
            color: this.colors,
            title: [
                {
                    textStyle: {
                        color: "white",
                        fontSize: 14
                    },
                    left: "39%",
                    top: "44%"
                }
            ],
            tooltip: {
                show: true,
                trigger: "item",
                formatter: function (params:any) {
                    return '本月日常任务'+list[params.dataIndex]+':'+params.value+'件'
                },
                fontSize: 12
            },
            // graphic: {
            //   //图形中间文字
            //   type: "text",
            //   left: "center",
            //   top: "center",
            //   style: {
            //     text: "66",
            //     textAlign: "center",
            //     fill: "#fff",
            //     fontSize: 60
            //   }
            // },
            series: [
                {
                    // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
                    // @ts-ignore
                    // name: this.PieChartData.name,
                    type: "pie",
                    radius: ["60%", "80%"],
                    center: ["50%", "50%"],
                    data: this.PieChartData.dataList,
                    itemStyle: {
                        normal: {
                            label: {
                                show: false //隐藏标示文字
                            },
                            labelLine: {
                                show: false //隐藏标示线
                            }
                        }
                    },
                    label: {
                        show: true
                    }
                }
            ]
        };
        this.chart.setOption(this.option);
        clearInterval(this.timer);
        this.timer = setInterval(() => {
            this.autoPlayTool();
        }, 2000);

        //鼠标移动上去的时候的高亮动画
        this.chart.on("mouseover", (param: any) => {
            clearInterval(this.timer);
            // 取消之前高亮的图形
            (this.chart as any).dispatchAction({
                type: "downplay",
                seriesIndex: 0
                // dataIndex: this.chartCurrentIndex
            });
            this.chartCurrentIndex = param.dataIndex;
            // 高亮当前图形
            (this.chart as any).dispatchAction({
                type: "highlight",
                seriesIndex: 0,
                dataIndex: this.chartCurrentIndex
            });
            // 显示 tooltip
            (this.chart as any).dispatchAction({
                type: "showTip",
                seriesIndex: 0,
                dataIndex: this.chartCurrentIndex
            });
        });
        // 鼠标移出之后，恢复自动高亮
        this.chart.on("mouseout", (param: any) => {
            // 显示 tooltip
            (this.chart as any).dispatchAction({
                type: "showTip",
                seriesIndex: 0,
                dataIndex: param.dataIndex
            });
            clearInterval(this.timer);
            this.timer = setInterval(() => {
                this.autoPlayTool();
            }, 2000);
        });
    }
    /**
     * @param {chartIns} echarts实例
     * @param {chartDate} echarts数据
     * @param {curIndex} 当前要展示的数据index
     */
    private autoPlayTool(): void {
        const dataLen = this.option.series[0].data.length;
        // 取消之前高亮的图形
        (this.chart as any).dispatchAction({
            type: "downplay",
            seriesIndex: 0,
            dataIndex: this.chartCurrentIndex
        });
        this.chartCurrentIndex =
            this.chartCurrentIndex + 1 >= dataLen ? 0 : this.chartCurrentIndex + 1;
        // 高亮当前图形
        (this.chart as any).dispatchAction({
            type: "highlight",
            seriesIndex: 0,
            dataIndex: this.chartCurrentIndex
        });
        // 显示 tooltip
        (this.chart as any).dispatchAction({
            type: "showTip",
            seriesIndex: 0,
            dataIndex: this.chartCurrentIndex
        });
    }
}
</script>
