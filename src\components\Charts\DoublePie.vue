<template>
  <div
    :id="id"
    :class="className"
    :style="{ height: height, width: width }"
  ></div>
</template>

<script lang="ts">
import * as echarts from 'echarts'
import { Component, Prop } from "vue-property-decorator";
import { mixins } from "vue-class-component";
import ResizeMixin from "./mixins/resize";
@Component({
  name: "doublePie"
})
export default class extends mixins(ResizeMixin) {
  @Prop({ default: "chart" }) private className!: string;
  @Prop({ default: "chart" }) private id!: string;
  @Prop({ default: "200px" }) private width!: string;
  @Prop({ default: "200px" }) private height!: string;
  mounted() {
    this.$nextTick(() => {
      this.initChart();
      // this.autoPlayTool(this.chart, ["垃圾清运车", "巡逻车", "洒水车"], 0);
    });
  }
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.clear();
    this.chart.dispose();
    this.chart = null;
  }

  private initChart() {

    if(this.chart === null || this.chart === undefined) {
      this.chart = echarts.init(
        document.getElementById(this.id) as HTMLDivElement
      );
    }
    this.chart.setOption({
      color: ["#2096CD", "#FDA709", "#FD4519"],
      legend: {
        top: 0,
        right: "10%",
        itemWidth: 10,
        itemHeight: 10,
        data: ["垃圾清运车", "巡逻车", "洒水车"],
        itemGap: 25,
        textStyle: {
          color: "#fff"
        }
      },
      title: [
        {
          text: "总车辆",
          subtext: "58",
          top: 86,
          left: 80,
          textAlign: "center",
          itemGap: 5,
          textStyle: {
            fontSize: 15,
            fontWeight: "normal",
            color: "#fff"
          },
          subtextStyle: {
            color: "#fff",
            fontSize: 20
          }
        },
        {
          text: "总出勤次",
          subtext: "245",
          top: 86,
          right: 60,
          textAlign: "center",
          itemGap: 5,
          textStyle: {
            fontSize: 13,
            fontWeight: "normal",
            color: "#fff"
          },
          subtextStyle: {
            color: "#fff",
            fontSize: 20
          }
        }
      ],
      grid: [
        {
          top: 35,
          height: 38,
          left: 48
        }
      ],
      yAxis: [
        {
          type: "category",
          inverse: true,
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            interval: 0,
            inside: true,
            textStyle: {
              color: "#00EAFF",
              fontSize: 12
            }
          },
          data: ["75辆", "60辆", "50辆"]
        },
        {
          type: "category",
          inverse: true,
          offset: -55,
          // axisLine: {
          //   show: false
          // },
          // axisTick: {
          //   show: false
          // },
          axisLabel: {
            interval: 0,
            inside: true,
            textStyle: {
              color: "#00EAFF",
              fontSize: 12
            }
          },
          data: ["123次", "46次", "30次"]
        }
      ],
      xAxis: [
        {
          show: false
        }
      ],
      series: [
        {
          name: "Line 4",
          type: "pie",
          // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
          // @ts-ignore
          clockWise: true,
          hoverAnimation: false,
          radius: ["74%", "80%"],
          center: ["15%", "60%"],
          labelLine: {
            show: false
          },
          label: {
            show: false
          },
          data: [
            {
              value: 75,
              name: "垃圾清运车"
            },
            {
              value: 25,
              itemStyle: {
                color: "transparent"
              }
            }
          ]
        },
        {
          name: "Line 3",
          type: "pie",
          clockWise: true,
          radius: ["59%", "65%"],
          center: ["15%", "60%"],
          hoverAnimation: false,
          labelLine: {
            show: false
          },
          label: {
            show: false
          },
          data: [
            {
              value: 60,
              name: "巡逻车"
            },
            {
              value: 40,
              itemStyle: {
                color: "transparent"
              }
            }
          ]
        },
        {
          name: "Line 2",
          type: "pie",
          clockWise: true,
          hoverAnimation: false,
          radius: ["43%", "50%"],
          center: ["15%", "60%"],
          labelLine: {
            show: false
          },
          label: {
            show: false
          },
          data: [
            {
              value: 50,
              name: "洒水车"
            },
            {
              value: 50,
              itemStyle: {
                color: "transparent"
              }
            }
          ]
        },
        {
          name: "Line 5",
          type: "pie",
          clockWise: true,
          hoverAnimation: false,
          radius: ["74%", "80%"],
          center: ["80%", "60%"],
          labelLine: {
            show: false
          },
          label: {
            show: false
          },
          data: [
            {
              value: 75,
              name: "垃圾清运车"
            },
            {
              value: 25,
              itemStyle: {
                color: "transparent"
              }
            }
          ]
        },
        {
          name: "Line 3",
          type: "pie",
          clockWise: true,
          radius: ["59%", "65%"],
          center: ["80%", "60%"],
          hoverAnimation: false,
          labelLine: {
            show: false
          },
          label: {
            show: false
          },
          data: [
            {
              value: 60,
              name: "巡逻车"
            },
            {
              value: 40,
              itemStyle: {
                color: "transparent"
              }
            }
          ]
        },
        {
          name: "Line 2",
          type: "pie",
          clockWise: true,
          hoverAnimation: false,
          radius: ["43%", "50%"],
          center: ["80%", "60%"],
          labelLine: {
            show: false
          },
          label: {
            show: false
          },
          data: [
            {
              value: 50,
              name: "洒水车"
            },
            {
              value: 50,
              itemStyle: {
                color: "transparent"
              }
            }
          ]
        }
      ]
    } as EChartOption);
  }

  /**
   * @param {chartIns} echarts实例
   * @param {chartDate} echarts数据
   * @param {curIndex} 当前要展示的数据index
   */
  private autoPlayTool(
    chartIns: any,
    chartDate: any,
    curIndex: any,
    seriesIndex = 0,
    speed = 1000
  ) {
    // clearInterval(chartsTimer)
    let chartsTimer = 0;
    const setpfun = () => {
      chartsTimer += 15;
      if (chartsTimer > speed) {
        chartsTimer = 0;
        chartIns.dispatchAction({
          type: "downplay",
          seriesIndex: seriesIndex,
          dataIndex: curIndex - 1 < 0 ? chartDate.length - 1 : curIndex - 1
        });
        chartIns.dispatchAction({
          type: "highlight",
          seriesIndex: seriesIndex,
          dataIndex: curIndex
        });
        chartIns.dispatchAction({
          type: "showTip",
          seriesIndex: seriesIndex,
          dataIndex: curIndex
        });
        curIndex === chartDate.length - 1 ? (curIndex = 0) : curIndex++;
      }
      window.requestAnimationFrame(setpfun);
    };
    window.requestAnimationFrame(setpfun);
    // chartsTimer = setInterval(setpfun, speed)
    chartIns.on("mouseover", function() {
      // clearInterval(chartsTimer)
      window.cancelAnimationFrame(setpfun as any);
    });
    chartIns.on("mouseout", function() {
      // clearInterval(chartsTimer)
      // chartsTimer = setInterval(setpfun, speed)
      // window.requestAnimationFrame(setpfun)
    });
  }
  private graceRequestAnimationFrame() {
    if (!Date.now) {
      Date.now = function() {
        return new Date().getTime();
      };
    }

    (function() {
      "use strict";

      const vendors = ["webkit", "moz"];
      for (
        let i = 0;
        i < vendors.length && !window.requestAnimationFrame;
        ++i
      ) {
        const vp = vendors[i];
        window.requestAnimationFrame = window[vp + "RequestAnimationFrame"];
        window.cancelAnimationFrame =
          window[vp + "CancelAnimationFrame"] ||
          window[vp + "CancelRequestAnimationFrame"];
      }
      if (
        /iP(ad|hone|od).*OS 6/.test(window.navigator.userAgent) || // iOS6 is buggy
        !window.requestAnimationFrame ||
        !window.cancelAnimationFrame
      ) {
        let lastTime = 0;
        (window as any).requestAnimationFrame = function(callback: Function) {
          const now = Date.now();
          const nextTime = Math.max(lastTime + 16, now);
          return setTimeout(function() {
            callback((lastTime = nextTime));
          }, nextTime - now);
        };
        window.cancelAnimationFrame = clearTimeout;
      }
    })();
  }
}
</script>
