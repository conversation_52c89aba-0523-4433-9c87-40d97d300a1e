const TokenKey = 'fast-token'
const TokenPrefix = 'Bearer '
const isLogin = () => {
  return !!sessionStorage.getItem(TokenKey)
}
const getToken = () => {
  return sessionStorage.getItem(TokenKey)
}
const setToken = (token: string) => {
  sessionStorage.setItem(TokenKey, token)
}
const clearToken = () => {
  // localStorage.removeItem(TokenKey)
  sessionStorage.clear()
}

/**
 * 加密存储数据
 * @param name 名称
 * @param string 要加密的数据
 * @returns 是否成功
 */
const encryptSave = (name: string, string: string) => {
  try {
    localStorage.setItem(name, window.btoa(string))
    return true
  } catch (error) {
    console.log('存储加密数据失败', error)
    return false
  }
}

/**
 * 获取加密数据
 * @param name 数据名称
 * @returns 解密后的加密数据
 */
const getEncryptSave = (name: string) => {
  try {
    const res = localStorage.getItem(name) || ''
    return window.atob(res)
  } catch (error) {
    console.log('获取加密数据失败', error)
    return ''
  }
}

/**
 * 移除加密存储
 * @param name 数据名称
 */
const removeEncryptSave = (name: string) => {
  localStorage.removeItem(name)
}

export {
  TokenPrefix,
  isLogin,
  getToken,
  setToken,
  clearToken,
  encryptSave,
  getEncryptSave,
  removeEncryptSave
}
