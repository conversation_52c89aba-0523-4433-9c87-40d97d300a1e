<style lang="less" scoped>
.flowchart {
  padding: 0 50px;
  padding-left: 0;
  position: relative;
  .button-area {
    position: absolute;
    top: 50px;
    right: 100px;
  }
  .tree_menu {
    position: absolute;
    display: block;
    z-index: 20000;
    background-color: #fff;
    padding: 5px 0;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

    ul {
      margin: 0;
      padding: 0;
    }
    ul li {
      list-style: none;
      margin: 0;
      padding: 0 15px;
      font-size: 14px;
      line-height: 30px;
      cursor: pointer;
    }
    ul li:hover {
      background-color: #f5f5f5;
    }
  }
}
.button-area {
  display: flex;
  flex-direction: column;
  span {
    margin-bottom: 10px;
    cursor: pointer;
    z-index: 1000;
  }
}
</style>
<template>
  <div
    :id="flowChartId"
    class="flowchart"
  >
    <diagram
      ref="diag"
      :model-data="modelData"
      :type="type"
      style="border: solid 1px #C0C4CC;width:100%;height:400px;padding: 0;border-radius: 5px"
      :style="{border: (dialog || taskId) ? 'none' : 'solid 1px #C0C4CC'}"
      @model-changed="modelChanged"
      @changed-selection="changedSelection"
      @click="handleClick"
      @right="rightClick"
    />
    <div
      v-if="tmDisplay && !dialog"
      id="perTreeMenu"
      class="tree_menu"
      :style="{ top: top + 'px', left: left + 'px' }"
    >
      <ul>
        <li
          v-if="(currentNode && Number(currentNode.data.permission) !== 1 && !taskId) ||
            (taskId && currentNode.data.userId === userId &&
            Number(currentNode.data.completeStatus) === 0 &&
            Number(currentNode.data.permission) !== 1)"
          @click="handleAdd(1)"
        ><i
          class="el-icon-edit"
          style="color: #3A79CE"
        /> 新增执行</li>
        <li
          v-if="(currentNode && Number(currentNode.data.permission) !== 1 &&
            !taskId) || (taskId && currentNode.data.userId === userId &&
            Number(currentNode.data.completeStatus) === 0 &&
            Number(currentNode.data.permission) !== 1)"
          @click="handleAdd(2)"
        ><i
          class="el-icon-plus"
          style="color: #ED7500"
        /> 新增抄送</li>
        <li
          v-if="taskId && currentNode.data.userId === userId &&
            Number(currentNode.data.completeStatus) === 0 &&
            Number(currentNode.data.permission) !== 1"
          @click="handleNodeComplete"
        ><i
          class="el-icon-check"
          style="color:#119F4E;"
        /> 完成任务</li>
        <li
          v-if="taskId && currentNode.data.userId === userId &&
            Number(currentNode.data.completeStatus) === 0 &&
            Number(currentNode.data.permission) !== 1"
          @click="handleNodeClose"
        ><i class="el-icon-delete" /> {{ isParent ? '任务退单' : '关闭任务' }}</li>
        <li
          v-if="currentNode && (currentNode.data.key!==modelData.nodeDataArray[0].key) && !taskId"
          @click="deleteNode"
        ><i class="el-icon-delete" /> 删除</li>
      </ul>
    </div>
    <div
      v-if="isNodeClosed"
      id="perTreeMenu"
      class="tree_menu"
      :style="{ top: top + 'px', left: left + 'px' }"
    >
      <ul>
        <li @click="handleParentNodeClose"><i class="el-icon-delete" /> 任务退单</li>
      </ul>
    </div>
    <!--    <div v-if="isSelected" class="button-area">-->
    <!--      <span @click="handleAdd">&#45;&#45; 新增 &#45;&#45;</span>-->
    <!--      <span @click="handleEdit">&#45;&#45; 查看 &#45;&#45;</span>-->
    <!--      <span v-if="currentNode && Number(currentNode.data.taskNodeType)!==0"
    @click="deleteNode">&#45;&#45; 删除 &#45;&#45;</span>-->
    <!--    </div>-->
    <!--        <div class="drawer"></div>-->
    <!--        <button @click="addNode">Add Child</button>-->
    <!--        &lt;!&ndash;    <button @click="modifyStuff">
      Modify view model data without undo</button>&ndash;&gt;-->
    <!--        <button @click="deleteNode">delete Child</button>-->
    <!--        <br>Current Node:-->
    <!--        <input v-model.lazy="currentNodeText" :disabled="currentNode === null" />-->
    <!--        <br>The saved GoJS Model:-->
    <!--        <textarea v-model="savedModelText" style="width:100%;height:250px" />-->
  </div>
</template>

<script>
import go from 'gojs'
import diagram from './diagram'

export default {
  name: 'Index1',
  components: {
    diagram
  },
  props: {
    flowChartId: {
      type: String,
      require: true,
      default: 'init_flow_chart_id'
    },
    modelData: {
      type: Object,
      require: true,
      default: () => {}
    },
    type: {
      type: Number,
      default: -1
    },
    dialog: {
      type: Boolean,
      default: false
    },
    taskId: {
      type: String,
      default: ''
    },
    userId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      currentNode: null,
      currkey: null,
      savedModelText: '',
      counter: 1, // used by addNode
      counter2: 4, // used by modifyStuff
      isSelected: false,
      tmDisplay: false,
      top: '',
      left: '',
      isClosed: false,
      isParent: undefined,
      isNodeClosed: false
    }
  },
  computed: {
    currentNodeText: {
      get() {
        const node = this.currentNode
        if (node instanceof go.Node) {
          return node.data.text
        }
        return ''
      },
      set(val) {
        const node = this.currentNode
        if (node instanceof go.Node) {
          const model = this.model()
          model.startTransaction()
          model.setDataProperty(node.data, 'text', val)
          model.commitTransaction('edited text')
        }
      }
    }
  },
  watch: {
    modelData(val) {
      this.tmDisplay = false
      this.isNodeClosed = false
    }
  },
  mounted() {},
  methods: {
    // get access to the GoJS Model of the GoJS Diagram
    model() {
      return this.$refs.diag.model()
    },

    // tell the GoJS Diagram to update based on the arbitrarily modified model data
    updateDiagramFromData() {
      this.$refs.diag.updateDiagramFromData()
    },

    // this event listener is declared on the <diagram>
    modelChanged(e) {
      if (e.isTransactionFinished) {
        // show the model data in the page's TextArea
        this.savedModelText = e.model.toJson()
      }
    },
    handleAdd(params) {
      this.tmDisplay = false
      this.isNodeClosed = false
      this.$emit('params', this.currentNode)
      this.$emit('add', params)
    },
    handleNodeComplete() {
      this.tmDisplay = false
      this.isNodeClosed = false
      this.$emit('complete', this.currentNode.data)
    },
    handleNodeClose() {
      this.tmDisplay = false
      this.isNodeClosed = false
      this.$emit('close', this.currentNode)
    },
    handleParentNodeClose() {
      this.tmDisplay = false
      this.isNodeClosed = false
      this.$emit('close', this.currentNode)
    },
    // handleEdit() {
    //   this.$emit('edit', this.currentNode)
    // },
    changedSelection(e) {
      const node = e.diagram.selection.first()
      if (node instanceof go.Node) {
        this.currentNode = node
        this.currkey = node.data.key
        this.currentNodeText = node.data.text
      } else {
        this.currentNode = null
        this.currentNodeText = ''
        this.currkey = null
        this.tmDisplay = false
        this.isNodeClosed = false
      }
    },
    handleClick(e) {
      if (this.currentNode) {
        this.tmDisplay = false
        this.isNodeClosed = false
        this.$emit('detail', this.currentNode)
      }
    },
    rightClick(args) {
      this.isNodeClosed = false
      // 确定操作弹窗位置
      this.top = args.docloc.y
      this.left = args.docloc.x
      this.tmDisplay = true
      const { data } = this.currentNode
      this.$emit('detail', this.currentNode)

      this.isParent = data.parentId
      // 找出当前右击点击节点的父节点,当前状态为进行中
      const parentNode = this.modelData.nodeDataArray.find(
        (item) => data.parentId &&
          Number(data.completeStatus) === 0 &&
          data.parentId === item.key
      )

      // 必须存在父级节点 且父级节点未关闭的情况下才能进行操作
      if (parentNode && parentNode.completeStatus === 0 && parentNode.userId === this.userId) {
        this.isNodeClosed = true
      }

      if (Number(data.permission) === 2 && Number(data.completeStatus) === 0) {
        const currLinkObj = this.modelData.linkDataArray.filter(
          (item) => item.to === data.key
        )[0]
        const currNodeObj = this.modelData.nodeDataArray.filter(
          (item) => item.key === currLinkObj.from
        )[0]
        this.isClosed = currNodeObj.userId === this.userId
      } else {
        this.isClosed = false
      }
    },
    changeSelection(e) {
      this.updateDiagramFromData()
    },
    // Here we modify the GoJS Diagram's Model using its methods,
    // which can be much more efficient than modifying some memory and asking
    // the GoJS Diagram to find differences and update accordingly.
    // Undo and Redo will work as expected.
    addNode(data) {
      const model = this.model()
      model.startTransaction()
      // var data = { title: '新节点', avator: '', planNodeType: 1 }
      // model.setDataProperty(model.findNodeDataForKey(4), 'color', 'purple')
      model.addNodeData(data)
      model.addLinkData({
        from: this.currkey,
        to: model.getKeyForNodeData(data)
      })

      model.commitTransaction('added Node and Link')
      this.$emit('data', this.modelData)
      // also manipulate the Diagram by changing its Diagram.selection collection
      // var diagram = this.$refs.diag.diagram
      // diagram.select(diagram.findNodeForData(data))
    },
    nodeTreeAdd(key, data) {
      if (this.nodeTree[key]) {
        this.nodeTree[key][data.key] = data
      }
    },
    // 删除节点
    deleteNode() {
      // var diagram = this.$refs.diag.diagram
      // diagram.commandHandler.deleteSelection()
      this.tmDisplay = false
      this.$emit('delete', this.currentNode)
    },

    // Here we modify VUE's view model directly, and
    // then ask the GoJS Diagram to update everything from the data.
    // This is less efficient than calling the appropriate GoJS Model methods.
    // NOTE: Undo will not be able to restore all of the state properly!!
    modifyStuff() {
      const data = this.diagramData
      data.nodeDataArray[0].color = 'red'
      // Note here that because we do not have the GoJS Model,
      // we cannot find out what values would be unique keys, for reference by the link data.
      data.nodeDataArray.push({
        key: ++this.counter2,
        text: this.counter2.toString(),
        color: 'orange'
      })
      data.linkDataArray.push({ from: 2, to: this.counter2 })
      this.updateDiagramFromData()
    }
  }
}
</script>

<style scoped>
</style>
