<template>
  <div class="taskFeedbackBox">
    <!-- 任务标题大盒子 -->
    <div class="left-line">
      <img src="@/assets/department/<EMAIL>" alt="">
      <div class="line"></div>
    </div>
    <div class="right">
      <div class="title">
        <div class="title-left">{{person}}</div>
        <div class="title-right">{{time}}</div>
      </div>
      <div class="content" :style="{paddingBottom:hiddenbtom?'0px':''}">
        <slot></slot>
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    props:{
      person: {
        type: String,
        default: ''
      },
      time: {
        type: String,
        default: ''
      },
      hiddenbtom: {
        type: Boolean,
        default: false
      }
    }
  }
</script>

<style lang="less" scoped>
.taskFeedbackBox{
  display: flex;
  .left-line{
    width: 32px;
    display: flex;
    flex-direction: column;
    align-items: center;
    // margin-right: 15px;
    .line{
      height: calc(100% - 32px);
      width: 2px;
      background-image: linear-gradient(rgba(12,44,89,0.5),#22C3FF,rgba(12,44,89,0.5) );
    }
    img{
      width: 32px;
      height: 32px;
    }
  }
  .right{
    flex: 1;
    // padding-right: 40px;
    .title{
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 5px 0;
      .title-left{
        font-size: 16px;
        color: #d6f4f4;
      }
      .title-right{
        font-size: 14px;
        color: #93B2C7;
      }
    }
    .content{
      padding:15px 10px;
      padding-bottom: 40px;
    }
  }
}
</style>