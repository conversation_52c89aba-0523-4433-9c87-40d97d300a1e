<template>
  <card title="人员信息">
    <template v-slot:right>
      <peopleButton
        v-for="(item, index) in buttonData"
        :key="index"
        :text="item"
        :className="className(index)"
        @btnClick="btnClick(index)"
      ></peopleButton>
    </template>
    <div class="move-box">
      <div
        class="move-block"
        :class="currIndex == 1 ? 'move1' : currIndex == 2 ? 'move2' : ''"
      >
        <div class="move-item">
          <!-- 救援队伍卡片 -->
          <!-- <div class="scroll-box" v-bar> -->
          <div class="scroll-box">
            <template v-if="excutorArr.length">
              <personInfoCar
                v-for="(obj, ind) in excutorArr"
                :key="ind + 'fbi'"
                :thind="ind"
                :cardata="obj"
                @getclick="getclick"
              >
              </personInfoCar>
            </template>
            <template v-else>
              <personInfoCar
                v-for="o in 2"
                :key="o"
                :thind="0"
                :moreFlg="false"
                :cardata="noneObj"
              >
              </personInfoCar>
            </template>
          </div>
        </div>
        <!-- 专家名单以及物资信息 -->
        <div class="move-item">
          <expertList :listData="experData"></expertList>
        </div>

        <div class="move-item">
          <materialsList :listData="storageData"></materialsList>
        </div>
      </div>
    </div>
  </card>
</template>

<script name="peopleInfo">
import card from "@/components/card/index";
import topTiltle from "@/components/peopleInfo/topTiltle.vue";
// import peopleList from '@/components/peopleInfo/peopleList.vue'
import peopleButton from "@/components/peopleInfo/peopleButton.vue";
import personInfoCar from "@/components/personInfoCar/index.vue";
import expertList from "@/components/peopleInfo/expertList.vue";
import materialsList from "@/components/peopleInfo/materialsList.vue";
import { storagePage, expertPage } from "@/api/emergencyDetails/emergencyEvent";

export default {
  // emits: ['getclick'],
  name: "peopleInfo",
  props: {
    peopledata: {
      type: Object | Array,
      default: () => ({}),
    },
  },
  components: {
    topTiltle,
    peopleButton,
    personInfoCar,
    expertList,
    materialsList,
    card,
  },
  data() {
    return {
      experData: [],
      storageData: [],
      cindex: 0,
      currIndex: 0,
      excutorArr: [],
      buttonData: ["救援队伍", "专家名单", "物资信息"],
      // flagArr: [false, false, false],
      noneObj: {
        departmentExecutor: [],
        departmentId: "",
        departmentName: "救援队伍",
      },
    };
  },
  computed: {},
  watch: {
    peopledata: {
      handler(nval, oval) {
        // console.log(oval, nval, '人员的数据改变-----------------83')
        if (nval && nval.data && nval.data.length) {
          // nval.data.forEach((e)=>{
          //   e.flagArr = false
          // })
          this.excutorArr = nval.data;
        } else {
          this.excutorArr = [];
        }
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {
    this.expertPage();
    this.storagePage();
  },
  methods: {
    getclick(val) {
      console.log(val);
      // this.flagArr[val] = !this.flagArr[val]
    },
    btnClick(args) {
      this.currIndex = args;
    },
    className(index) {
      return this.currIndex === index ? "choose-btn" : "";
    },
    /**
     * 专家列表
     */
    expertPage() {
      expertPage().then(({ data }) => {
        // console.log('专家列表', data.data)
        if (data.data && data.data.length) {
          this.experData = data.data;
        } else {
          this.experData = [];
        }
      });
    },
    /**
     * 物资列表
     */
    storagePage() {
      storagePage().then(({ data }) => {
        // console.log('物资列表', data.data)
        if (data.data && data.data.length) {
          this.storageData = data.data;
        } else {
          this.storageData = [];
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
.move-box {
  overflow: hidden;
  .move-block {
    width: 1344px;
    display: flex;
    transform: translateX(0px);
    transition-duration: 0.5s;
    .move-item {
      width: 448px;
    }
  }
  .move1 {
    transform: translateX(-448px);
  }
  .move2 {
    transform: translateX(-896px);
  }
}
.scroll-box {
  overflow-y: scroll;
  height: 305px;
  &::-webkit-scrollbar {
    width: 0px;
  }
  &::-webkit-scrollbar-thumb {
    border-radius: 10px;
    width: 1px;
    background: rgba(21, 62, 105, 0.5);
  }
}
</style>
