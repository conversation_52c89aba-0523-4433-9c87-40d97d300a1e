p {
  margin-bottom: 0;
}

.monitor {
  color: #ffffff;
  padding: 0.12rem;
  align-items: center;
  >div{
    cursor: pointer;
    display: flex;
  }
  .spot {
    width: 0.2rem;
    height: 0.2rem;
    background-image: url("../../assets/rwcztjc-jb.png");
    background-size: 100% 100%;
    margin-right: 0.12rem;
  }
  .line {
    width: 0.02rem;
    height: 0.24rem;
    background-image: url("../../assets/<EMAIL>");
    background-size: 100% 100%;
    margin: 0 0.2rem;
  }
}
.monitor:nth-child(odd) {
  background: #052f61;
}
.monitor:nth-child(even) {
  background: transparent;
}
.task {
  box-sizing: border-box;
  .task-top {
    padding: 0.15rem 0.25rem;
    box-sizing: border-box;
    background-image: url("../../assets/task2.png");
    background-size: 100% 100%;
    > div:nth-child(2) {
      display: flex;
      > :nth-of-type(1) {
        img {
          object-fit: cover;
          width: 100%;
          height: 100%;
        }
      }
      > :nth-of-type(2) {
        display: flex;
        flex-direction: column;
        padding-left: 0.1rem;
        justify-content: space-between;
        flex: 1;
        p {
          margin-bottom: 0;
        }
      }
      .task-top-name {
        color: rgba(0, 252, 249, 1);
        font-size: 0.16rem;
        // margin-left: 0.15rem;
      }
      .task-top-text {
        font-size: 0.16rem;
        color: #ffffff;
      }
      .task-top-text-big {
        display: inline-block;
        font-size: 0.16rem;
        background: rgba(18, 75, 156, 1);
        border-radius: 0.04rem;
        padding: 0.04rem;
      }
    }
  }
  .task-bottom {
    box-sizing: border-box;
    background-image: url("../../assets/task1.png");
    background-size: 100% 180%;
    padding: 0.15rem 0.25rem;
    .task-bottom-top {
      border-bottom: 1px solid rgba(255, 255, 255, 0.35);
      padding-bottom: 0.05rem;
      > :nth-of-type(1) {
        width: 0.4rem;
        height: 0.4rem;
        border-radius: 50%;
        margin-right: 0.1rem;
        img {
          width: 100%;
          height: 100%;
          border-radius: 50%;
        }
      }
      display: flex;
      align-items: center;
      .task-bottom-top-name {
        color: rgba(0, 234, 255, 1);
        font-size: 0.16rem;
      }
      .task-bottom-top-time {
        color: rgba(191, 208, 210, 1);
        font-size: 0.13rem;
      }
    }
    .task-bottom-bottom {
      font-size: 0.16rem;
      color: #ffffff;
      padding-top: 0.05rem;
    }
  }
  .lines {
    width: 2.66rem;
    height: 0.02rem;
    background: url(../../assets/<EMAIL>) no-repeat;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    margin: 0.15rem auto;
  }
  // .task-charts {
  //   padding: 0.15rem;
  //   box-sizing: border-box;
  //   background-image: url("../../assets/task3.png");
  //   background-size: 100% 100%;
  //   display: flex;
  //   justify-content: space-between;
  // }
  .playback-form {
    color: #fff;
    transition: all 1s linear;
    opacity: 0;
    pointer-events: none;
    > div:nth-child(odd) {
      margin-bottom: 0.03rem;
    }
    > div:nth-child(even) {
      margin-bottom: 0.15rem;
    }
    .date-picker {
      margin-right: 0.2rem;
    }
    .time-picker {
      width: 1.28rem;
    }
  }
  .show-playback {
    opacity: 1;
    pointer-events: auto;
  }
}
.type-center {
  position: relative;
  width: 9.5rem;
  .type-center-top {
    margin-top: 0.57rem;
    display: flex;
    justify-content: space-between;
    .car-normal {
      p {
        margin-bottom: 0;
      }
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      width: 1.6rem;
      height: 1rem;
      background: url(../../assets/<EMAIL>);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      > :nth-of-type(1) {
        font-size: 0.19rem;
        color: rgba(255, 255, 255, 1);
      }
      > :nth-of-type(2) {
        width: 0.34rem;
        height: 0.3rem;
        margin-top: 0.11rem;
        img {
          width: 100%;
          height: 100%;
        }
      }
    }
    .car-active {
      p {
        margin-bottom: 0;
      }
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      width: 1.6rem;
      height: 1rem;
      background: url(../../assets/<EMAIL>);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      > :nth-of-type(1) {
        font-size: 0.19rem;
        color: rgba(255, 255, 255, 1);
      }
      > :nth-of-type(2) {
        width: 0.34rem;
        height: 0.3rem;
        margin-top: 0.11rem;
        img {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
  .center-map {
    position: absolute;
    width: 10.5rem;
    left: -0.5rem;
    height: calc(1080px - 2.6rem);
    margin-top: 0.15rem;
    > div {
      width: 100%;
      height: 100%;
    }
  }
}
.main-content {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  > div {
    width: 100%;
    height: 0.31rem;
  }
  .legend {
    display: flex;
    justify-content: flex-end;
    .on {
      display: flex;
      align-items: center;
      .block {
        width: 0.08rem;
        height: 0.08rem;
        background: #56ccff;
        margin: 0 0.1rem 0 0.3rem;
      }
    }
    .off {
      display: flex;
      align-items: center;
      .block {
        width: 0.08rem;
        height: 0.08rem;
        background: #719eef;
        margin: 0 0.1rem 0 0.3rem;
      }
    }
    .error {
      display: flex;
      align-items: center;
      .block {
        width: 0.08rem;
        height: 0.08rem;
        background: #aaa;
        margin: 0 0.1rem 0 0.3rem;
      }
    }
  }
  .line-one {
    display: flex;
    justify-content: space-between;
    font-size: 0.14rem;
    font-family: PingFang SC;
    font-weight: 500;
    color: rgba(255, 255, 255, 1);
    > :nth-of-type(2) {
      color: rgba(0, 222, 255, 1);
    }
  }
  .line-two {
    height: 0.06rem;
    // background-color: #f27678;
    background-color: rgb(113, 158, 239);
    // border-radius: 0.12rem;
    display: flex;
    align-items: center;
    > div {
      height: 0.06rem;
      // border-radius: 0.12rem;
      // border-top-right-radius: 0;
      // border-bottom-right-radius: 0;
    }
  }
}
.common-content-title {
  display: flex;
  align-items: center;
  justify-content: center;
  // width: 76px;
  height: 0.3rem;
  font-size: 0.16rem;
  background: rgba(18, 75, 156, 1);
  text-align: center;
  margin-bottom: 0.3rem;
  // > span {
  //   display: inline-block;
  // }
  // span.circle {
  //   margin-right: 0.1rem;
  //   width: 0.06rem;
  //   height: 0.06rem;
  //   background: rgba(0, 234, 255, 1);
  //   border-radius: 50%;
  // }
}
.title-top {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;

  // > div {
  //   display: flex;
  //   font-size: 0.14rem;
  //   font-weight: 400;

  //   > div {
  //     width: 0.8rem;
  //     height: 0.28rem;
  //     line-height: 0.3rem;
  //     color: #0e8bff;
  //     // background: rgba(14, 139, 255, 0.32);
  //     // border: 1px solid rgba(14, 139, 255, 1);
  //     text-align: center;
  //     // clip-path: polygon(100% 0, 100% 100%, 12% 100%, 0% 70%, 0 0);
  //     background: url(../../assets/<EMAIL>);
  //     background-size: 100% 100%;
  //     background-repeat: no-repeat;
  //     margin-left: 0.1rem;
  //     cursor: pointer;
  //   }
  //   div.type-active {
  //     background: url(../../assets/<EMAIL>);
  //     background-size: 100% 100%;
  //     background-repeat: no-repeat;
  //     color: rgba(0, 234, 255, 1);
  //   }
  // }
}
.table-data {
  width: 100%;
  .table-data-thead {
    width: 100%;
    .tr {
      display: flex;
      justify-content: space-between;
      .th {
        background: transparent !important;
        color: #ffffff;
        padding: 0;
        text-align: center;
        border: none;
        line-height: 0.34rem;
        font-size: 0.14rem;
        height: 0.34rem;
      }
      > :nth-of-type(1) {
        width: 20%;
      }
      > :nth-of-type(2) {
        width: 25%;
      }
      > :nth-of-type(3) {
        width: 20%;
      }
      > :nth-of-type(4) {
        width: 25%;
      }
    }
  }
  .table-data-tbody {
    width: 4.6rem;
    height: calc(2rem - 0.35rem);
    .tr {
      display: flex;
      justify-content: space-between;
      .td {
        color: #ffffff;
        padding: 0;
        font-size: 0.14rem;
        text-align: center;
        height: 0.34rem;
        line-height: 0.34rem;
        border: none;
      }
      > :nth-of-type(1) {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 20%;
      }
      > :nth-of-type(2) {
        width: 25%;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
      > :nth-of-type(3) {
        width: 20%;
      }
      > :nth-of-type(4) {
        width: 25%;
      }
    }
  }
  .table-data-tbody .tr:nth-child(odd) {
    background: rgba(5, 47, 97, 1);
  }
  .table-data-tbody .tr:nth-child(even) {
    background: transparent;
  }
  .tdBefore {
    //前三
    width: 0.26rem;
    display: block;
    text-align: center;
    height: 0.2rem;
    line-height: 0.2rem;
    background: rgba(255, 133, 9, 1);
    border-radius: 0.06rem;
    margin: 0 auto;
  }
  .tdAfter {
    //前三外
    width: 0.26rem;
    display: block;
    text-align: center;
    height: 0.2rem;
    line-height: 0.2rem;
    background: rgba(18, 136, 226, 1);
    border-radius: 0.06rem;
    margin: 0 auto;
  }
}
.common-main {
  padding: 0;
}
.left-region {
  width: 100%;
  height: 100%;
  .work-car {
    pointer-events: auto;
    position: absolute;
    bottom: 0.83rem;
    left: 0.83rem;
    width: 2.02rem;
    height: 1.1rem;
    background-image: url("../../assets/heavily/<EMAIL>");
    background-size: 100% 100%;
    padding: 0.24rem 0.36rem 0.22rem 0.34rem;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .work-type {
      display: flex;
      align-items: center;
      cursor: pointer;
      justify-content: space-between;
      span {
        color: #97B6E4;
        font-size: 0.14rem;
        &:nth-child(3) {
          width: 0.2rem;
          height: 0.2rem;
          border: 1px solid #2AFFF8;
          border-radius: 50%;
          position: relative;
         .active {
            content: '';
            width: .1rem;
            height: .1rem;
            background: #2AFFF8;
            border-radius: 50%;
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%,-50%);
          }
        }
      }
    }
  }
  .left-region-bottom {
    position: absolute;
    bottom: 0;
    left: 0;
    padding: 0 0.2rem 0 0.5rem;
    display: flex;
    justify-content: space-between;
    height: 3.5rem;
    padding-top: 0.5rem;
    width: 1920px;
    background-image: url("../../assets/bottomBg.png");
    background-size: 100% 100%;
    // box-shadow: 0 0 10px 10px #888888;

    > div {
      position: relative;
      padding: 0.16rem;
      margin-bottom: 0.1rem;
      width: 4.46rem;
    }
    .left-part-one1 {
      width: 5rem;
    }
    .left-part-two2 {
      width: 4rem;
      .common-content-two {
        display: flex;
        justify-content: space-between;
      }
    }
  }
  .left-region-top {
    height: 100%;
    width: 100%;
    position: relative;
    // margin-bottom: 0.35rem;
    // margin-top: 0.4rem;
    .conner-left-top {
      position: absolute;
      top: -2px;
      left: -2px;
      width: 0.1rem;
      height: 0.1rem;
      border-top: 2px solid #00eaff;
      border-left: 2px solid #00eaff;
    }
    .conner-right-top {
      position: absolute;
      top: -2px;
      right: -2px;
      width: 0.1rem;
      height: 0.1rem;
      border-top: 2px solid #00eaff;
      border-right: 2px solid #00eaff;
    }
    .conner-left-bot {
      position: absolute;
      left: -2px;
      bottom: -2px;
      width: 0.1rem;
      height: 0.1rem;
      border-bottom: 2px solid #00eaff;
      border-left: 2px solid #00eaff;
    }
    .conner-right-bot {
      position: absolute;
      right: -2px;
      bottom: -2px;
      width: 0.1rem;
      height: 0.1rem;
      border-right: 2px solid #00eaff;
      border-bottom: 2px solid #00eaff;
    }
    .center-map {
      width: 1920px;
      height: 1080px;
      position: relative;
      .type-car {
        margin-top: 0.1rem;
        position: absolute;
        left: 0;
        top: 0;
        box-sizing: border-box;
        padding: 0.2rem 0.15rem 0.1rem 0.5rem;
        width: 2.5rem;
        .count {
          font-size: 0.24rem;
          font-weight: bold;
        }
        .line {
          font-size: 0.24rem;
        }
        > div {
          display: flex;
          align-items: center;
          justify-content: left;
          margin-bottom: 0.1rem;
          width: 2rem;
          > div {
            height: 0.5rem;
          }
          > div:nth-child(1) {
            width: 0.65rem;
            height: 0.65rem;
            display: flex;
            align-items: center;
            justify-content: center;
            background-image: url("../../assets/carbox.png");
            background-size: 100% 100%;
          }
          div.car-active {
            background-image: url("../../assets/car-selected.png");
          }
          .circle {
            align-self: center;
            // border-radius: 50%;
            margin-right: 0.24rem;
            img {
              width: 0.3rem;
              // height: 0.2rem;
            }
          }
          // .circle1 {
          //   background: rgba(4, 186, 25, 1);
          // }
          // .circle2 {
          //   background: rgba(230, 0, 18, 1);
          // }
          // .circle3 {
          //   background: rgba(255, 172, 40, 1);
          // }
          // .circle4 {
          //   border: 0.02rem solid rgba(196, 40, 229, 1);
          //   box-sizing: border-box;
          // }
        }
        .legend {
          font-size: 0.15rem;
          color: #19f1f9;
        }
      }
    }
    .img-top-right {
      position: absolute;
      background: url(../../assets/<EMAIL>);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      width: 3rem;
      height: 0.08rem;
      top: -0.2rem;
      right: 0;
    }
    .img-top-left {
      position: absolute;
      background: url(../../assets/<EMAIL>);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      width: 3rem;
      height: 0.08rem;
      top: -0.2rem;
      left: 0;
    }
    .img-bottom-right {
      position: absolute;
      background: url(../../assets/<EMAIL>);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      width: 1.73rem;
      height: 0.08rem;
      bottom: -0.2rem;
      right: 0;
    }
    .img-bottom-left {
      position: absolute;
      background: url(../../assets/<EMAIL>);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      width: 1.73rem;
      height: 0.08rem;
      bottom: -0.2rem;
      left: 0;
    }
    .img-left-top {
      position: absolute;
      background: url(../../assets/<EMAIL>);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      width: 0.08rem;
      height: 0.83rem;
      top: 0;
      left: -0.2rem;
    }
    .img-left-bottom {
      position: absolute;
      background: url(../../assets/<EMAIL>);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      width: 0.08rem;
      height: 0.83rem;
      bottom: 0;
      left: -0.2rem;
    }
    .img-right-top {
      position: absolute;
      background: url(../../assets/<EMAIL>);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      width: 0.08rem;
      height: 0.83rem;
      top: 0;
      right: -0.2rem;
    }
    .img-right-bottom {
      position: absolute;
      background: url(../../assets/<EMAIL>);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      width: 0.08rem;
      height: 0.83rem;
      bottom: 0;
      right: -0.2rem;
    }
  }
}
.display-state {
  display: none !important;
  transform: scale(0);
  transition: 1s;
}
.display-state-map {
  height: 100%;
  width: 1843px;
}
.conner-left-top {
  position: absolute;
  top: -2px;
  left: -2px;
  width: 0.1rem;
  height: 0.1rem;
  border-top: 2px solid #00eaff;
  border-left: 2px solid #00eaff;
}
.conner-right-top {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 0.1rem;
  height: 0.1rem;
  border-top: 2px solid #00eaff;
  border-right: 2px solid #00eaff;
}
.conner-left-bot {
  position: absolute;
  left: -2px;
  bottom: -2px;
  width: 0.1rem;
  height: 0.1rem;
  border-bottom: 2px solid #00eaff;
  border-left: 2px solid #00eaff;
}
.conner-right-bot {
  position: absolute;
  right: -2px;
  bottom: -2px;
  width: 0.1rem;
  height: 0.1rem;
  border-right: 2px solid #00eaff;
  border-bottom: 2px solid #00eaff;
}
.sub-title {
  margin-bottom: 0.1rem !important;
}
.map-box {
  overflow: hidden;
  width: 100%;
  height: 100%;
}
.quanpin {
  position: absolute;
  top: -0.88rem;
  right: 0.5rem;
  transition: 1.5s;
  z-index: 99;
}
.fade-enter-active,
.fade-leave-active {
  transition: all 0.5s;
  position: absolute;
}
.fade-enter, .fade-leave-to /* .fade-leave-active below version 2.1.8 */ {
  left: -5rem;
}
.common-title .title {
  text-shadow: 0 0 5px blue, 0 0 5px blue;
}
.mile,
.mile2 {
  background: url("../../assets/lcs-k1.png");
  background-size: 100% 100%;
  width: 0.25rem;
  height: 0.4rem;
  line-height: 0.4rem;
  font-size: 0.22rem;
  text-align: center;
  margin-right: 0.01rem;
}
.mile2 {
  background: url("../../assets/lcs-k2.png");
  background-size: 100% 100%;
}
.mileage {
  display: flex;
  align-items: center;
}
.mile-cycle {
  cursor: pointer;
  width: 0.25rem;
  height: 0.25rem;
  line-height: 0.25rem;
  text-align: center;
  border-radius: 5px;
}
.cycle-of-day{
  margin: 0 0.1rem 0 0.15rem;
}
.mile-cycle-selected {
  background: #03112D;
  border: 1px solid rgba(0, 132, 255, 1);
}
.playback {
  width: 1.63rem;
  height: 0.35rem;
  background: linear-gradient(
    0deg,
    rgba(77, 161, 248, 1) 0%,
    rgba(54, 116, 217, 1) 100%
  );
  cursor: pointer;
  border-radius: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.playback-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  img {
    margin: 0.08rem 0;
  }
}
.playback-form {
  padding: 0.12rem 0.3rem 0.2rem 0.3rem !important;
}
.ant-form {
  color: #fff;
}
.form-item {
  margin-bottom: 0.1rem;
}
.ant-calendar-picker {
  width: 152px !important;
  min-width: 152px !important;
}
.ant-form-item {
  margin: 0 5px !important;
}
.working-condition{
  display: flex;
  justify-content: space-between;
  flex-direction: column;
}
.enterprise-search {
  position: absolute;
  top: 0.6rem;
  pointer-events: auto;
  right: 50%;
  display: flex;
  align-items: center;
  width: 7.5rem;
  height: 0.6rem;
  margin-right: -3.5rem;
  > :nth-of-type(1) {
    display: flex;
    align-items: center;
    width: 6.5rem;
    height: 0.6rem;
    background: rgba(5, 7, 95, 0.7);
    border: 1px solid #034fa8;
    input {
      width: 100%;
      margin-left: 0.25rem;
      // margin-right: 0.25rem;
      background: transparent;
      border: none;
      outline: none;
      font-size: 0.22rem;
    }
    input::-webkit-input-placeholder {
      color: #ffffff;
    }
    input::-moz-placeholder {
      /* Mozilla Firefox 19+ */
      color: #ffffff;
    }
    input:-moz-placeholder {
      /* Mozilla Firefox 4 to 18 */
      color: #ffffff;
    }
    input:-ms-input-placeholder {
      /* Internet Explorer 10-11 */
      color: #ffffff;
    }
  }
  > :nth-of-type(2) {
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(5, 7, 95, 0.7);
    border: 1px solid #034fa8;
    height: 0.6rem;
    width: 1rem;
    img {
      cursor: pointer;
    }
  }
}
.enterprise-search {
  .fetch-input {
    position: absolute;
    width: 6.5rem;
    cursor: pointer;
    top: 0.6rem;
    font-size: 0.22rem;
    > div {
      color: #8EB3F6;
      background: #012474;
      padding: 0.1rem 0.25rem;
      &:hover {
        color: #ffffff;
        background-color: #0061C6;
      }
    }
  }
}
.query-button{
  width: 100%;
  margin-top: 0.05rem;
  display: flex;
  justify-content: center;
  >button{
    width: 1.62rem;
    // background:linear-gradient(0deg,rgba(26,154,255,1),rgba(48,120,253,1));
    // outline: none;
    // border: none;
    // color: #fff
  }
}
.multiple-speed,.play-ctr{
  width: 100%;
  display: flex;
  justify-content: space-around;
  margin-top: 0.1rem;
  >button{
    width: 0.8rem;
  }
}
.play-ctr{
  padding: 0 0.65rem;
  box-sizing: border-box;
  >button{
    width: 1rem;
  }
}
.button{
  width: 0.79rem;
  height: 0.3rem;
  line-height: 0.3rem;
  text-align: center;
  padding: 0 0.15rem;
  box-sizing: border-box;
  border: none;
  cursor: pointer;
  border-radius: 0.05rem;
}
.button-selected{
  border: 1px solid #0084FF
}
.change-camera{
  position: absolute;
  top: 0.0rem;
  right: 0.74rem;
  width: 0.8rem;
  height: 0.24rem;
  background:rgba(13,29,74,0.7);
  border:1px solid rgba(32, 103, 224, 1);
  border-radius:0.04rem;
  display: flex;
  align-items: center;
  color: #ccc;
  justify-content: space-around;
  cursor: pointer;
}
.change-camera-selected{
  text-shadow: 0 0 5px blue, 0 0 5px blue;
  color: #fff
}



