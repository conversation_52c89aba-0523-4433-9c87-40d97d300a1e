{"name": "vue-typescript-template", "version": "0.0.1", "private": true, "scripts": {"dev": "vue-cli-service serve --mode development", "build:stage": "vue-cli-service build --mode stage", "build:http": "node --max-old-space-size=4096 node_modules/@vue/cli-service/bin/vue-cli-service.js build --mode production", "build:https": "node --max-old-space-size=4096 node_modules/@vue/cli-service/bin/vue-cli-service.js build --mode prodhttps", "svg": "vsvg -s ./src/icons/svg -t ./src/icons/components --ext ts --es6", "test:unit": "vue-cli-service test:unit", "lint": "vue-cli-service lint"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@antv/g2": "^4.1.47", "@antv/l7": "^2.8.1", "@antv/l7-draw": "^2.4.23", "@antv/l7-maps": "^2.8.1", "@babel/compat-data": "^7.11.0", "@liveqing/liveplayer": "^2.3.4", "@types/axios": "^0.14.0", "@types/echarts": "^4.4.3", "@types/js-cookie": "^2.2.5", "@types/moment": "^2.13.0", "@types/node": "^13.9.2", "@types/nprogress": "^0.2.0", "@types/qs": "^6.9.7", "@types/swiper": "^5.2.1", "@wanglin1994/video-timeline": "^0.1.10", "amap-wind": "^1.2.0", "animate.css": "^4.1.1", "animejs": "^3.2.1", "ant-design-vue": "^1.5.4", "axios": "^0.19.2", "babel": "^6.23.0", "core-js": "^3.6.4", "crypto-js": "^4.1.1", "dayjs": "^1.11.6", "echarts": "^5.6.0", "echarts-gl": "^2.0.9", "element-ui": "^2.15.9", "ezuikit-js": "^0.1.7", "flv.js": "^1.5.0", "gojs": "^2.0.17", "html2canvas": "^1.4.1", "jquery": "^1.12.4", "js-cookie": "^2.2.1", "lodash": "^4.17.21", "lottie-web": "^5.7.8", "moment": "^2.26.0", "mux.js": "^6.3.0", "muxjs": "^2.4.18", "normalize.css": "^8.0.1", "nprogress": "^0.2.0", "swiper": "^5.3.6", "videojs-contrib-hls.js": "^3.2.0", "videojs-flash": "^2.2.1", "vue": "^2.6.11", "vue-aliplayer": "^1.0.0", "vue-awesome-swiper": "^4.1.0", "vue-class-component": "^7.2.2", "vue-count-to": "^1.0.13", "vue-photo-preview": "^1.1.3", "vue-property-decorator": "^8.3.0", "vue-router": "^3.1.5", "vue-seamless-scroll": "^1.1.23", "vue-svgicon": "^3.2.6", "vue-video-player": "^5.0.2", "vue2-org-tree": "^1.3.6", "vuex": "^3.1.2", "web-worker-helper": "0.0.3"}, "devDependencies": {"@types/jest": "^24.0.19", "@typescript-eslint/eslint-plugin": "^2.18.0", "@typescript-eslint/parser": "^2.18.0", "@vue/cli-plugin-babel": "~4.2.0", "@vue/cli-plugin-eslint": "~4.2.0", "@vue/cli-plugin-router": "~4.2.0", "@vue/cli-plugin-typescript": "~4.2.0", "@vue/cli-plugin-unit-jest": "~4.2.0", "@vue/cli-plugin-vuex": "~4.2.0", "@vue/cli-service": "~4.2.0", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-standard": "^5.1.2", "@vue/eslint-config-typescript": "^5.0.2", "@vue/test-utils": "1.0.0-beta.31", "compression-webpack-plugin": "^5.0.1", "eslint": "^6.7.2", "eslint-plugin-prettier": "^3.1.1", "eslint-plugin-vue": "^6.1.2", "less": "^3.0.4", "less-loader": "^5.0.0", "prettier": "^3.5.3", "script-ext-html-webpack-plugin": "^2.1.4", "typescript": "~3.7.5", "vue-template-compiler": "^2.6.11"}}