/* eslint-disable @typescript-eslint/no-explicit-any */
<template>
  <div
    :id="id"
    :style="{ height: height, width: width }"
    v-if="propData.show"
  ></div>
  <div v-else>
    <img
      style="width: 2.3rem;position: relative;left: 0.1rem;top: 0.2rem;"
      src="@/assets/pollution-not.png"
      alt=""
    />
  </div>
</template>

<script lang="ts">
import * as echarts from 'echarts'
import { Component, Prop, Watch } from "vue-property-decorator";
import { mixins } from "vue-class-component";
import ResizeMixin from "./mixins/resize";
interface InData {
  nameList: string[];
  dataList: number[];
  unit: string;
  show: false;
}
@Component({
  name: "PollutionSource"
})
export default class extends mixins(ResizeMixin) {
  @Prop({ default: "chart" }) private id!: string;
  @Prop({ default: "200px" }) private width!: string;
  @Prop({ default: "200px" }) private height!: string;
  @Prop({ required: true }) private propData!: InData;
  @Watch("propData", { immediate: true, deep: true }) public onMsgChanged(
    newValue: any,
    oldValue: any
  ) {
    /* eslint-disable @typescript-eslint/ban-ts-ignore */
    //@ts-ignore
    // this.chart.clear();
    if (this.propData.show) {
      this.$nextTick(() => {
        this.initChart();
      });
    }
  }
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
  }

  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.clear();
    this.chart.dispose();
    this.chart = null;
  }

  private initChart() {
    this.chart = echarts.init(
            document.getElementById(this.id) as HTMLDivElement
    );
    this.chart.setOption({
      color: ["#fff"],
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "shadow"
        },
        formatter: function(objs: any) {
          const obj = objs[0];
          return `${obj.name}<br/><span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:${obj.color.colorStops[0].color}"></span>数值 : ${obj.value}`;
        }
      },
      grid: {
        top: "17%",
        right: "0%",
        left: "12%",
        bottom: "2%",
        containLabel: true
      },
      xAxis: {
        data: this.propData.nameList,
        type: "category",
        interval: 20,
        min: 0,
        splitLine: {
          show: false
        },
        axisLine: {
          lineStyle: {
            color: "#fff"
          }
        },
        axisLabel: {
          interval: 0,
          rotate: 25,
          textStyle: {
            color: "#fff",
            fontSize: 10
          }
        },
        axisTick: {
          show: false
        }
      },
      yAxis: {
        type: "value",
        name: this.propData.unit,
        boundaryGap: true,
        splitArea: {
          areaStyle: {
            opacity: 0
          }
        },
        axisLine: {
          lineStyle: {
            color: "#fff"
          }
        },
        axisTick: {
          show: false
        },
        splitLine: {
          show: false
        }
      },
      series: [
        {
          barMaxWidth: 10,
          type: "bar",
          data: this.propData.dataList,
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: "rgba(1, 170, 255, 1)"
              },
              {
                offset: 1,
                color: "rgba(0, 234, 255, 1)"
              }
            ]),
            barBorderRadius: [20, 20, 0, 0]
          }
        }
      ]
    } as any);
  }
}
</script>
