<template>
  <div
    id="dv-full-screen-container"
    :ref="ref"
  >
    <template v-if="ready">
      <slot />
    </template>
  </div>
</template>

<script>
import autoResize from './autoResize'

export default {
  name: 'DvFullScreenContainer',
  mixins: [autoResize],
  data() {
    return {
      ref: 'full-screen-container',
      allWidth: 0,
      allHeight: 0,
      scale: 0,
      datavRoot: '',
      ready: false
    }
  },
  methods: {
    afterAutoResizeMixinInit() {
      const { initConfig, setAppScale } = this

      initConfig()

      setAppScale()

      this.ready = true
    },
    initConfig() {
      const { dom } = this
      // const { width, height } = window.screen
      const [width, height] = [1920, 1080]

      this.allWidth = width

      this.allHeight = height

      dom.style.width = `${width}px`
      dom.style.height = `${height}px`
    },
    setAppScale() {
      const { allWidth, allHeight, dom } = this

      const currentWidth = document.body.clientWidth
      const currentHeight = document.body.clientHeight
      if (currentWidth / currentHeight - 0.405 > 1920 / 1080) {
        dom.style.transform = `scale(${currentHeight / allHeight})`
      } else {
        dom.style.transform = `scale(${currentWidth / allWidth})`
      }
    },
    onResize() {
      const { setAppScale } = this

      setAppScale()
    }
  }
}
</script>

<style>
#dv-full-screen-container {
  position: fixed;
  top: 0px;
  left: 0px;
  overflow: hidden;
  transform-origin: left top;
  z-index: 999;
}
</style>
