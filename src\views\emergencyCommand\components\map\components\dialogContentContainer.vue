<template>
  <div class="dialogContentContainer">
    <!-- 时间数据 S -->
    <div class="header-date flex justify-end" v-if="date">
      <span>最新监测：</span><span>{{ date }}</span>
    </div>
    <!-- 时间数据 E -->

    <!-- tabbar S -->
    <div class="tabbar-list flex">
      <div
        v-for="(item, i) in tabbar.filter((item) => item.name)"
        :key="i"
        class="bar-item"
        :class="{ cur: i === curTabbar }"
        @click="tabbarClick(i, item)"
      >
        {{ item.name }}
      </div>
    </div>
    <!-- tabbar E -->

    <!-- 内容填充区域 S -->
    <div class="content-box">
      <slot></slot>
    </div>
    <!-- 内容填充区域 E -->
  </div>
</template>

<script>
export default {
  name: 'dialogContentContainer',
  props: {
    date: {
      type: String,
      default: '',
    },
    tabbar: {
      type: Array,
      default: ()=>[
        {
          name: '测试按钮',
          data: {
            name: '绑定数据',
          },
        },
      ],
    },
  },
  data() {
    return {
      curTabbar: 0,
    }
  },
  methods: {
    /**
     * tanbar点击事件
     */
    tabbarClick(index, info) {
      // console.log('tabbar点击事件', index)
      if (this.curTabbar === index) return
      this.curTabbar = index
      this.$emit('tabbarClick', info)
    },
  },
}
</script>

<style lang="less" scoped>
.dialogContentContainer {
  width: 100%;

  .header-date {
    color: #778da0;
    font-size: 14px;
    margin-bottom: 13px;
  }

  .bar-item {
    display: flex;
    justify-content: center;
    flex: 1;
    text-align: center;
    height: 32px;
    line-height: 32px;
    font-size: 16px;
    color: #4c79a1;
    background-color: rgba(45, 154, 255, 0.12);

    &.cur {
      color: #d3e6f5;
      border-top: 1px solid #10c9ff;
      background-color: rgba(45, 154, 255, 0.25);
    }
  }

  .content-box {
    padding: 20px 5px;
    width: 100%;
  }
}
</style>
