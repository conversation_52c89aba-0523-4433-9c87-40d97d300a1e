<template>
  <div class="dialogVideoBox-container flex flex-col items-center">
    <div class="video-box base-bg-img">
      <VideoItem
        v-if="videoData.videoUrl || videoData.ezopen"
        :videoUrl="videoData.videoUrl"
        :ezopen="videoData.ezopen"
        :accessToken="videoData.accessToken"
        :online="videoData.online"
      ></VideoItem>

      <a-empty description="暂无视频数据" v-else />
    </div>
    <div class="body flex flex-wrap">
      <div class="item" v-for="(item, i) in infoList" :key="i">
        <infoItem
          :name="`${item.name || '—'}`"
          :value="item.value || '—'"
          :warnValue="item.warnValue"
        ></infoItem>
      </div>
    </div>
  </div>
</template>
<script>
import VideoItem from '@/components/videoItem.vue'
import infoItem from './infoItem.vue'
export default {
  name: 'dialogVideoBox',
  components: {
    VideoItem,
    infoItem,
  },
  props: {
    videoBoxData: {
      type: Object,
      default: () => ({
        list: [
          {
            name: '',
            value: '',
            warnValue: '',
          },
        ],
        video: {
          videoUrl: '',
          ezopen: '',
          online: false,
        },
      }),
    },
  },
  computed: {
    infoList() {
      const { videoBoxData } = this
      const { list } = videoBoxData
      if (Array.isArray(list)) return list
      return []
    },
    videoData() {
      const { videoBoxData } = this
      const { video } = videoBoxData
      console.log(video.videoUrl, '--------video---------67')
      if (video) return video
      return {}
    },
  },
}
</script>

<style lang="less" scoped>
.dialogVideoBox-container {
  width: 100%;

  .video-box {
    background-image: url('../../../../../assets/images/<EMAIL>');
    background-repeat: no-repeat;
    background-size: 100% 99%;
    // width: 353px;
    // height: 200px;
    width: 100%;
    padding: 10px;
  }

  .body {
    width: 100%;
    margin-top: 20px;

    .item {
      flex: 1;
      min-width: 50%;
      margin-bottom: 8px;
    }
  }
}
.flex{
  display: flex;
}
.flex-col {
  flex-direction: column;
}
.items-center {
  align-items: center;
}
.flex-grow {
  flex-grow: 1;
}
.flex-wrap{
  flex-wrap: wrap;
}
</style>
