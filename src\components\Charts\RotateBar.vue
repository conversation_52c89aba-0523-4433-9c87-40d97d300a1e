<template>
  <div :id="id" :style="{ height: height, width: width }" />
</template>

<script lang="ts">
import * as echarts from 'echarts'
import { Component, Prop, Watch } from "vue-property-decorator";
import { mixins } from "vue-class-component";
import ResizeMixin from "./mixins/resize";
interface AirData {
  bottomList: string[];
  dataList: string | string[];
}
@Component({
  name: "RotateBar"
})
export default class extends mixins(ResizeMixin) {
  // @Prop({ default: "chart" }) private className!: string;
  @Prop({ default: "chart" }) private id!: string;
  @Prop({ default: "200px" }) private width!: string;
  @Prop({ default: "200px" }) private height!: string;
  @Prop({ required: true }) private airData!: AirData;

  @Watch("airData", { immediate: true, deep: true })
  private onAirDataChange(newValue: AirData, oldValue: AirData) {
    if (newValue) {
      this.$nextTick(() => {
        this.initChart();
      });
    }
  }

  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
  }

  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.clear();
    this.chart.dispose();
    this.chart = null;
  }

  private initChart() {

    if(this.chart === null || this.chart === undefined) {
      this.chart = echarts.init(
        document.getElementById(this.id) as HTMLDivElement
      );
    }
    this.chart.setOption({
      tooltip: {},
      calculable: true,
      grid: {
        height: 150,
        bottom: 35,
        left: 75,
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
            label: {
              show: true,
              formatter: function(params: any) {
                return params.value.replace("\n", "");
              }
            }
          }
        }
      },
      xAxis: {
        type: "value",
        name: "Kg",
        nameTextStyle: {
          color: "white"
        },
        axisLabel: {
          rotate: -15,
          textStyle: {
            fontSize: 12,
            color: "white"
          }
        },
        axisLine: {
          show: true
        },
        axisTick: {
          show: false
        },
        splitLine: {
          lineStyle: {
            type: "dashed"
          }
        }
      },
      yAxis: {
        // name: "重污染企业",
        nameTextStyle: {
          color: "white"
        },
        type: "category",
        data: this.airData.bottomList,
        axisLabel: {
          interval: 0,
          rotate: 50,
          textStyle: {
            fontSize: 9,
            color: "white"
          }
        },
        axisLine: {
          show: true
        },
        axisTick: {
          show: false
        }
      },
      series: [
        {
          type: "bar",
          // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
          // @ts-ignore
          barWidth: "30%",
          data: this.airData.dataList,
          itemStyle: {
            normal: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: "#0263FF"
                },
                {
                  offset: 1,
                  color: "#00A2FF"
                }
              ])
            }
          }
        }
      ]
    } as EChartOption<EChartOption.SeriesLine>);
  }
}
</script>
