<template>
  <div class="dialogElectronicGun-container" @wheel.stop>
    <div class="item" v-for="(item, i) in dialogElectronicGunData" :key="i">
      <dialogElectronicGunItem :data="item"></dialogElectronicGunItem>
    </div>

    <a-empty description="暂无电枪数据" v-if="!dialogElectronicGunData.length" />
  </div>
</template>

<script>
  import dialogElectronicGunItem from './dialogElectronicGunItem.vue'
  export default {
    name: 'dialogElectronicGun',
    components:{
      dialogElectronicGunItem
    },
    props:{
      dialogElectronicGunData: {
        type: Array,
        default: () => [
          {
            name: '测试名称',
            content: ['加油油品：1', '油品编号：2'],
            percent: 0.75,
            percentName: '' || '气液比',
          },
        ],
      },
    }
  }
</script>

<style lang="less" scoped>
  .dialogElectronicGun-container {
    width: 100%;
    max-height: 300px;
    overflow: auto;

    .item {
      width: 100%;
      margin-top: 25px;
    }
    &::-webkit-scrollbar-track {
      border-radius: 10px;
      background: rgba(57, 177, 255, 0);
    }
    &::-webkit-scrollbar-thumb {
      border-radius: 10px;
      width: 1px;
      background: rgba(21, 62, 105, 0.5);
    }
  }
</style>
