<style lang="less" scoped>
.main-warp {
  .content {
    height: calc(1080px - 0.94rem);
    background-image: url('../assets/bg.png');
    background-repeat: no-repeat;
    background-size: cover;
    background-attachment: fixed;
    background-position: 0px 0px;
  }
}
</style>

<template>
  <section class="main-warp">
    <Header></Header>
    <!-- <transition name="shift"> -->
    <!-- <keep-alive include="otherPages"> -->
    <router-view class="content" />
    <!-- </keep-alive> -->
    <!-- </transition> -->
  </section>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import Header from './modules/Header.vue'

@Component({
  name: 'Layout',
  components: {
    Header: Header,
  },
})
export default class extends Vue {}
</script>
