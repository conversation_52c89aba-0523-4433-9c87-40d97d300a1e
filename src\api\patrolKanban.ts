import request from "@/utils/request";
import { AxiosPromise } from "axios";
// 巡岗看板 异常上报统计
export function patrolCount(): AxiosPromise<any> {
  return request({
    url: "/task/big-data/patrolCount",
    method: "get",
  });
}
// 获取巡岗任务列表
export function patrolList(params: any): AxiosPromise<any> {
  return request({
    url: "/task/big-data/patrolList",
    method: "get",
    params
  });
}
// 获取巡岗任务列表
export function patrolDetail(params: any): AxiosPromise<any> {
  return request({
    url: "/task/big-data/patrolDetail",
    method: "get",
    params
  });
}