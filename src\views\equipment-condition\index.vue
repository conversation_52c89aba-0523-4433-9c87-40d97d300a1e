<style lang="less" scoped>
p {
  margin-bottom: 0 !important;
}
.equipment-condition {
  width: 100%;
  height: 100%;
  // background: url("../../assets/emergencyCommand.png") no-repeat;
  background-size: 100%;
  .equipment-condition-content {
    position: relative;
    .center-map {
      width: 100%;
      height: calc(1080px - 0.94rem);
    }
    .title {
      font-size: 0.2rem;
      font-family: Source Han Sans CN;
      font-weight: 500;
      text-shadow: 0 0 5px blue, 0 0 5px blue;
    }
    .equipment-condition-right {
      padding-right: 0.5rem;
      padding-bottom: 0.25rem;
      padding-top: 0.25rem;
      overflow: hidden;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      position: absolute;
      right: 0;
      top: 0;
      width: 4.5rem;
      background-image: url(../../assets/air_bg.png) !important;
      background-size: 100% 100% !important;
      .sub-title {
        margin-bottom: 0.1rem;
      }
      .comment-content-des {
        height: 2rem;
      }
      .content-one {
        height: 1.5rem;
        .table-site {
          width: 100%;
          border: 1px solid rgba(54, 218, 234, 0.42);
          .table-name {
            font-size: 0.12rem;
            width: 0.76rem;
            background: rgba(15, 36, 94, 1);
            text-align: center;
          }
          .table-text {
            padding-left: 0.06rem;
          }
          td {
            font-size: 0.12rem;
            height: 0.34rem;
            color: #ffffff;
            font-weight: 500;
          }
        }
      }
      .content-two {
        height: 1.7rem;
        display: flex;
        justify-content: space-between;
      }
      .content-three {
        height: 2.5rem;
        .table-data {
          width: 100%;
          .table-data-thead {
            .tr {
              display: flex;
              justify-content: space-between;
              .th {
                background: transparent !important;
                color: #ffffff;
                padding: 0;
                text-align: center;
                border: none;
                line-height: 0.43rem;
                font-size: 0.14rem;
                height: 0.43rem;
              }
              > :nth-of-type(1) {
                width: 20%;
              }
              > :nth-of-type(2) {
                width: 15%;
              }
              > :nth-of-type(3) {
                width: 35%;
              }
              > :nth-of-type(4) {
                width: 25%;
              }
            }
          }
          .table-data-tbody {
            height: calc(2.5rem - 0.35rem);
            .tr {
              display: flex;
              justify-content: space-between;
              .td {
                color: #ffffff;
                padding: 0;
                font-size: 0.14rem;
                text-align: center;
                height: 0.43rem;
                line-height: 0.43rem;
                border: none;
                text-overflow: ellipsis;
                overflow: hidden;
                white-space: nowrap;
              }
              > :nth-of-type(1) {
                width: 20%;
                text-align: left;
                box-sizing: border-box;
                padding-left: 0.1rem;
              }
              > :nth-of-type(2) {
                width: 15%;
                text-overflow: ellipsis;
                overflow: hidden;
                white-space: nowrap;
              }
              > :nth-of-type(3) {
                width: 35%;
              }
              > :nth-of-type(4) {
                width: 25%;
              }
            }
          }
          .table-data-tbody .tr:nth-child(odd) {
            background: rgba(5, 47, 97, 1);
          }
          .table-data-tbody .tr:nth-child(even) {
            background: transparent;
          }
          .tdBefore {
            //前三
            width: 0.26rem;
            display: block;
            text-align: center;
            height: 0.2rem;
            line-height: 0.2rem;
            background: rgba(255, 133, 9, 1);
            border-radius: 0.06rem;
            margin: 0 auto;
          }
          .tdAfter {
            //前三外
            width: 0.26rem;
            display: block;
            text-align: center;
            height: 0.2rem;
            line-height: 0.2rem;
            background: rgba(18, 136, 226, 1);
            border-radius: 0.06rem;
            margin: 0 auto;
          }
        }
      }
      .title-top {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-right: 0.2rem;
      }
    }
    .type-icon {
      position: absolute;
      bottom: 0.2rem;
      right: 5rem;
      > div {
        box-sizing: border-box;
        padding: 0.1rem;
        display: flex;
        align-items: center;
        width: 1.72rem;
        height: 0.54rem;
        background-image: url(../../assets/<EMAIL>);
        background-size: 100% 100%;
        > span {
          display: inline-block;
          width: 0.6rem;
          margin-left: 0.1rem;
        }
        .type-icon-img {
          width: 0.44rem;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }
  }
}
</style>
<style lang="less">
.equipment-condition {
  .title-video-select {
    display: flex;
    justify-content: space-between;
    margin-right: 0.1rem;
    .ant-select-selection {
      width: 1.4rem;
      height: 0.3rem;
      border: none;
      border-radius: unset;
      background: url(../../assets/<EMAIL>);
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
    .ant-select-selection-selected-value {
      color: rgba(0, 234, 255, 1);
    }
    .ant-select-focused .ant-select-selection,
    .ant-select-selection:focus,
    .ant-select-selection:active {
      border-color: #40a9ff;
      border-right-width: 0 !important;
      outline: 0;
      box-shadow: none;
    }
  }
}
</style>

<template>
  <section>
    <section class="equipment-condition">
      <section class="equipment-condition-content">
        <section class="center-map">
          <!-- <keep-alive> -->
            <!-- :emergencyList="offLineList" -->
            <gao-de-map
              :mapStyle="mapStyle"
              :enterType="9"
              :mapZoom="13"
              :equipmentAirMarkerList="airMapMarker"
              :equipmentWaterMarkerList="waterMarkerSite"
              :equipmentVideoMarkerList="waterMarkerVideo"
            ></gao-de-map>
          <!-- </keep-alive> -->
        </section>
        <section class="equipment-condition-right">
          <div class="comment-content">
            <div class="title title-top">
              <span>{{ `设备信息` }}</span>
              <div>
                <a-select
                  v-model="defaultValue"
                  class="title-video-select"
                  @change="selectChange"
                >
                  <a-icon
                    slot="suffixIcon"
                    type="caret-down"
                    style="color:rgba(0, 234, 255, 1);width:14px;height:8px;"
                  />
                  <a-select-option :value="'连心桥站'"
                    >连心桥站</a-select-option
                  >
                </a-select>
              </div>
            </div>
            <div class="sub-title">
              <img src="@/assets/biaoti.png" alt class="title-img" />
            </div>
            <div class="comment-content-des content-one">
              <table border="1" class="table-site">
                <tr>
                  <td class="table-name">设备站点</td>
                  <td colspan="3" class="table-text">
                    连心桥水质监测站点
                  </td>
                </tr>
                <tr>
                  <td class="table-name">设备类型</td>
                  <td class="table-text">空气站</td>
                  <td class="table-name">设备状态</td>
                  <td class="table-text">
                    <img
                      src="@/assets/state.png"
                      alt=""
                      style="width:0.15rem;height:0.14rem"
                    />
                    在线
                  </td>
                </tr>
                <tr>
                  <td class="table-name">设备位置</td>
                  <td colspan="3" class="table-text">
                    成都市营门口立交桥互利西一巷8号
                  </td>
                </tr>
                <tr>
                  <td class="table-name">设备参数</td>
                  <td colspan="3" class="table-text"></td>
                </tr>
                <tr>
                  <td class="table-name">更新频率</td>
                  <td colspan="3" class="table-text"></td>
                </tr>
              </table>
            </div>
          </div>
          <div class="comment-content">
            <div class="title">{{ `设备状态` }}</div>
            <div class="sub-title">
              <img src="@/assets/biaoti.png" alt class="title-img" />
            </div>
            <div class="comment-content-des content-two">
              <div v-for="(item, index) in stateList" :key="index">
                <SitePie
                  :id="`siteTypeS${index}`"
                  :width="'1.3rem'"
                  :height="'1.5rem'"
                  :propData="item"
                />
              </div>
            </div>
          </div>
          <div class="comment-content">
            <div class="title">{{ `离线设备列表` }}</div>
            <div class="sub-title">
              <img src="@/assets/biaoti.png" alt class="title-img" />
            </div>
            <div class="comment-content-des content-three">
              <div class="table-data">
                <div class="table-data-thead">
                  <div class="tr">
                    <div class="th">名称</div>
                    <div class="th">类型</div>
                    <div class="th">位置</div>
                    <div class="th">时间</div>
                  </div>
                </div>
                <swiper
                  :options="swiperOptionAirs"
                  class="table-data-tbody"
                  v-if="offLineList.length >= 5"
                >
                  <swiper-slide
                    class="tr"
                    v-for="(item, index) in offLineList"
                    :key="index"
                  >
                    <div class="td">
                      <div :title="item.name">{{ item.name }}</div>
                    </div>
                    <div class="td" :title="item.type">{{ item.type }}</div>
                    <div class="td" :title="item.address">
                      {{ item.address }}
                    </div>
                    <div class="td" :title="item.time">{{ item.time }}</div>
                  </swiper-slide>
                </swiper>
                <div class="table-data-tbody" v-else>
                  <div
                    class="tr"
                    v-for="(item, index) in offLineList"
                    :key="index"
                  >
                    <div class="td">
                      <div :title="item.name">{{ item.name }}</div>
                    </div>
                    <div class="td" :title="item.type">{{ item.type }}</div>
                    <div class="td" :title="item.address">
                      {{ item.address }}
                    </div>
                    <div class="td" :title="item.time">{{ item.time }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
        <section class="type-icon">
          <div>
            <a-radio style="margin-left: 0.05rem;" />
            <span>水</span>
            <div class="type-icon-img">
              <img
                src="@/assets/<EMAIL>"
                alt=""
                style="width:0.44rem;height:0.27rem;"
              />
            </div>
          </div>
          <div>
            <a-radio style="margin-left: 0.05rem;" />
            <span>空气</span>
            <div class="type-icon-img">
              <img
                src="@/assets/<EMAIL>"
                alt=""
                style="width:0.32rem;height:0.32rem;"
              />
            </div>
          </div>
          <div>
            <a-radio style="margin-left: 0.05rem;" />
            <span>摄像头</span>
            <div class="type-icon-img">
              <img
                src="@/assets/<EMAIL>"
                alt=""
                style="width:0.24rem;height:0.22rem;"
              />
            </div>
          </div>
        </section>
      </section>
    </section>
  </section>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import GaoDeMap from "@/components/GaoDeMap/index.vue";
import SitePie from "@/components/Charts/SitePie.vue";
import { Swiper, SwiperSlide } from "vue-awesome-swiper";
import { Table, Icon, Empty, Avatar, Select, Radio } from "ant-design-vue";
import { allAirStation } from "@/api/air";
import { getAllStationRealTimeRecordList } from "@/api/water";
@Component({
  name: "EquipmentCondition",
  components: {
    Swiper,
    SwiperSlide,
    GaoDeMap,
    ATable: Table,
    AIcon: Icon,
    AEmpty: Empty,
    AAvatar: Avatar,
    ASelect: Select,
    ASelectOption: Select.Option,
    SitePie,
    ARadio: Radio
  }
})
export default class extends Vue {
  private mapStyle = "amap://styles/0739a6632d849ae1b5e6f1c52f7d12c3";
  private defaultValue: any = "连心桥站";
  private equipmentList: any = [
    {
      lng: 104.05494689941406,
      lat: 30.733130500446673,
      type: 0
    },
    {
      lng: 104.04636383056639,
      lat: 30.677929987145504,
      type: 0
    },
    {
      lng: 104.0185546875,
      lat: 30.693135418679468,
      type: 0
    },
    {
      lng: 104.01924133300781,
      lat: 30.727228210785487,
      type: 1
    },
    {
      lng: 104.08687591552734,
      lat: 30.733720709532616,
      type: 1
    }
  ];
  private stateList: any[] = [
    {
      name: "空气站 47个",
      number: 24,
      total: 47,
      startColor: "rgba(64, 119, 200, 1)",
      endColor: "rgba(33, 185, 241, 1)"
    },
    {
      name: "水站站 10个",
      number: 4,
      total: 10,
      startColor: "rgba(64, 119, 200, 1)",
      endColor: "rgba(33, 185, 241, 1)"
    },
    {
      name: "摄像头 8个",
      number: 4,
      total: 8,
      startColor: "rgba(64, 119, 200, 1)",
      endColor: "rgba(33, 185, 241, 1)"
    }
  ];
  private swiperOptionAirs = {
    direction: "vertical",
    slidesPerView: 5,
    slidesPerGroup: 1,
    loop: true,
    autoplay: {
      delay: 3500,
      disableOnInteraction: false
    }
  };
  private offLineList: any[] = [
    {
      name: "连心桥站",
      type: "水站站",
      address: "沙河源街道新桥社区东北方向",
      time: "5月8日 13:20"
    },
    {
      name: "连心桥站",
      type: "水站站",
      address: "沙河源街道新桥社区东北方向",
      time: "5月8日 13:20"
    },
    {
      name: "连心桥站",
      type: "水站站",
      address: "沙河源街道新桥社区东北方向",
      time: "5月8日 13:20"
    },
    {
      name: "连心桥站",
      type: "水站站",
      address: "沙河源街道新桥社区东北方向",
      time: "5月8日 13:20"
    },
    {
      name: "连心桥站",
      type: "水站站",
      address: "沙河源街道新桥社区东北方向",
      time: "5月8日 13:20"
    },
    {
      name: "连心桥站",
      type: "水站站",
      address: "沙河源街道新桥社区东北方向",
      time: "5月8日 13:20"
    }
  ];
  // 空气站点
  private airMapMarker: any[] = [];
  // 水站点
  private waterMarkerSite: any[] = [];
  // 摄像头站点
  private waterMarkerVideo: any[] = [];
  mounted() {
    this.getAirStation();
    this.getAllStationRealTimeRecordList();
  }
  private selectChange(val: any) {
    console.log(val);
  }
  // 获取地图站点和热力图
  private getAirStation() {
    allAirStation({
      stationTypeId: "1,2,3",
      pollutionCode: "aqi"
    }).then((res: any) => {
      this.airMapMarker = res.data.data;
    });
  }

  // 获取金牛区所有站点和对应列表
  private getAllStationRealTimeRecordList() {
    getAllStationRealTimeRecordList().then((res: any) => {
      if (res.data.data) {
        // 摄像头
        this.waterMarkerVideo = res.data.data.map((list: any) => {
          const retMap = list.station;
          list.realtimeMonitorRecords.forEach((item: any) => {
            switch (item.itemCode) {
              case "003":
                retMap["cod"] = item.value;
                retMap["codAlarm"] = item.alarmStatus === 1;
                break;
              case "002":
                retMap["nh3n"] = item.value;
                retMap["nh3nAlarm"] = item.alarmStatus === 1;
                break;
              case "004":
                retMap["totalPhosphorus"] = item.value;
                retMap["totalPhosphorusAlarm"] = item.alarmStatus === 1;
                break;
              case "007":
                retMap["ph"] = item.value;
                retMap["phAlarm"] = item.alarmStatus === 1;
                break;
            }
          });
          return retMap;
        });
        // 站点
        this.waterMarkerSite = res.data.data.map((list: any) => {
          const retMap = list.station;
          list.realtimeMonitorRecords.forEach((item: any) => {
            switch (item.itemCode) {
              case "003":
                retMap["cod"] = item.value;
                retMap["codAlarm"] = item.alarmStatus === 1;
                break;
              case "002":
                retMap["nh3n"] = item.value;
                retMap["nh3nAlarm"] = item.alarmStatus === 1;
                break;
              case "004":
                retMap["totalPhosphorus"] = item.value;
                retMap["totalPhosphorusAlarm"] = item.alarmStatus === 1;
                break;
              case "007":
                retMap["ph"] = item.value;
                retMap["phAlarm"] = item.alarmStatus === 1;
                break;
            }
          });
          return retMap;
        });
      }
    });
  }
}
</script>
