<template>
  <div class="emergencyEventItem" @click="iteclick">
    <div class="imgbox">
      <img :src="cflag2 ? icon.i2 : icon.i1" alt="" />
    </div>
    <div class="right" :class="cflag2 ? 'right-chose' : ''">
      <div class="right-number">{{ number }}</div>
      <div class="right-text">{{ typeText }}</div>
    </div>
  </div>
</template>
<script>
import sgzh1 from '@/assets/images/emergencyEvent/<EMAIL>'
import sgzh2 from '@/assets/images/emergencyEvent/<EMAIL>'
import zrzh1 from '@/assets/images/emergencyEvent/<EMAIL>'
import zrzh2 from '@/assets/images/emergencyEvent/<EMAIL>'
import ggaq1 from '@/assets/images/emergencyEvent/<EMAIL>'
import ggaq2 from '@/assets/images/emergencyEvent/<EMAIL>'
import sqaq1 from '@/assets/images/emergencyEvent/<EMAIL>'
import sqaq2 from '@/assets/images/emergencyEvent/<EMAIL>'
export default {
  name: '',
  props: {
    typeText:{
      type: String,
      default:  ''
    },
    type: {
      type: String,
      default: 'sgzh'
    },
    number: {
      default: '0'
    },
    cflag: {
      type: Boolean,
      default:false
    },
  },
  data() {
    return {
      iconObj: {
        sgzh: {
          i1: sgzh1,
          i2: sgzh2,
        },
        zrzh: {
          i1: zrzh1,
          i2: zrzh2,
        },
        ggaq: {
          i1: ggaq1,
          i2: ggaq2,
        },
        sqaq: {
          i1: sqaq1,
          i2: sqaq2,
        },
      },
      cflag2: false,
    }
  },
  watch:{
    cflag:{
      handler(count, prevCount){
        this.cflag2 = count
      },
      immediate:true
    },
  },
  methods:{
    iteclick(){
      this.$emit('iteclick')
    }
  },
  computed:{
    icon(){
      return this.iconObj[this.type]
    }
  }
}
</script>

<style lang="less" scoped>
.emergencyEventItem {
  display: flex;
  margin: 20px 0;
  cursor: pointer;
  .imgbox {
    width: 64px;
    height: 64px;
    border: 1px solid #0a1121;
    margin-right: 10px;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .right {
    flex: 1;
    .right-number {
      font-size: 23px;
      color: #a9c8db;
    }
    .right-text {
      font-size: 15px;
      color: #7a8d9f;
    }
  }
  .right-chose {
    .right-number {
      color: #10c9ff;
    }
    .right-text {
      color: #1389b6;
    }
  }
}
</style>
