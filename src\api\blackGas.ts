import request from "@/utils/request";
import { AxiosPromise } from "axios";

/**
 * @method functionName
 * @param {type} data 说明
 * @description 获取黑烟车站点统计
 */
export function getGasTruckSite(params:any): AxiosPromise<any> {
  return request({
    url: `/water/traffic-flow-station/stationStatusStatistics`,
    method: "get",
    params
  });
}
/**
 * @method functionName
 * @param {type} data 说明
 * @description 获取黑烟车站点列表
 */
export function getGasTruckMapList(params:any): AxiosPromise<any> {
  return request({
    url: `/water/traffic-flow-station/listStation`,
    method: "get",
    params
  });
}
/**
 * @method functionName
 * @param {type} data 说明
 * @description 获取黑烟车站点列表小时数据
 */
export function getGasHourList(params:any): AxiosPromise<any> {
  return request({
    url: `/water/trafficFlow/record/hour/listRecord`,
    method: "get",
    params
  });
}
/**
 * @method functionName
 * @param {type} data 说明
 * @description 获取黑烟车站点列表天数据
 */
export function getGasDayList(params:any): AxiosPromise<any> {
  return request({
    url: `/water/trafficFlow/record/day/listRecord`,
    method: "get",
    params
  });
}
/**
 * @method functionName
 * @param {type} data 说明
 * @description 获取黑烟车饼图数据
 */
export function getGasPieData(params:any): AxiosPromise<any> {
  return request({
    url: `/water/trafficFlow/record/carTypeCount`,
    method: "get",
    params
  });
}