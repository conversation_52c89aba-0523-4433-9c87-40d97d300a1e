<template>
  <div class="dialog-box">
    <div class="header">
      <div class="title">巡查详情</div>
      <div class="close" @click="close">×</div>
    </div>

    <div class="body" id="myDialogBoxBody">
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'dialogCard',
  methods: {
    close() {
      console.log('关闭事件触发')
      this.$emit('close')
    },
  },
}
</script>

<style lang="less" scoped>
.dialog-box {
  width: 100%;
  height: 100%;
  background: url('../../assets/images/<EMAIL>') no-repeat center center;
  background-size: 108% 105%;
  padding: 25px;
  position: relative;
  z-index: 999;

  .header {
    display: flex;
    justify-content: space-between;

    .title {
      font-weight: 700;
      color: #ffffff;
      background: linear-gradient(0deg, #56d1ed 0%, #caf6ff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      font-size: 16px;
    }
  }

  .close {
    color: #b2e4f9;
    font-size: 30px;
    line-height: 1.5;
    width: 50px;
    text-align: right;
    cursor: pointer;
  }
}
</style>
