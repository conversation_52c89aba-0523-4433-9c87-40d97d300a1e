<template>
  <div class="marker-control-box">
    <section class="air-station-main">
      <div
        v-for="item in areaList"
        :key="item.code"
        class="list-air"
        @click="checkChange(item)"
      >
        <img
          v-if="!item.selected"
          src="@/assets/heavily/<EMAIL>"
          alt=""
          class="gou"
        />
        <img v-else src="@/assets/gou_active.png" alt="" class="gou" />
        <div style="display: inline-block">{{ item.name }}</div>
      </div>
    </section>
  </div>
</template>

<script>
import { Checkbox } from "ant-design-vue";
import { districtList } from "@/api/analysisAir";
export default {
  components: {
    ACheckbox: Checkbox,
  },
  props: {
    typeNum: {
      type: Number,
      default: 0,
    },
    close: {
      type: Number,
      default: 1,
    },
  },
  watch: {},
  created() {
    this.handleGetDistrictList();
  },
  data() {
    return {
      allSelectArea: [],
      activeIndex: 0,
      areaList: [],
    };
  },
  methods: {
    checkChange(raw) {
      raw.selected = !raw.selected;
      if (!this.areaList.some((item) => item.selected)) {
        return this.$message.warning("所选区域不能为空");
      }
      this.allSelectArea = this.areaList
        .filter((item) => item.selected)
        .map((it) => it.code);
      this.$emit("handleGetSelectArea", this.allSelectArea);
    },
    handleGetDistrictList() {
      districtList().then((res) => {
        console.log("行政区域列表", res);
        this.areaList = (res?.data?.data ?? []).map((item) => {
          return {
            ...item,
            selected: true,
          };
        });
        const arr = this.areaList.map((item) => item.code);
        this.$emit("handleGetSelectArea", arr);
      });
    },
  },
};
</script>

<style lang="less" scoped>
.flex {
  display: flex;
}
.items-center {
  justify-items: center;
  align-items: center;
  vertical-align: middle;
}
.marker-control-box {
  position: fixed;
  right: 2.55rem;
  bottom: 1px;
  padding: 11px 32px;
  z-index: 2999;
}

.air-station-main {
  transition: 1.5s;
  width: 0.93rem;
  height: 1.3rem;
  // background: rgba(23, 56, 92, 0.5);
  position: absolute;
  bottom: 0.8rem;
  right: 4.5rem;
  z-index: 100;
  .gou {
    width: 0.16rem;
    margin-right: 0.1rem;
    height: 0.16rem;
  }
  .list-air {
    display: flex;
    width: fit-content;
    cursor: pointer;
  }
  > div {
    display: inline-block;
    align-items: center;
    margin-top: 0.08rem;
    > input {
      margin-right: 0.1rem;
      width: 0.16rem;
      height: 0.16rem;
    }
    > :nth-of-type(2) {
      width: 0.5rem;
      padding-left: 8px;
    }
  }
  padding: 0.05rem 0 0 0.25rem;
}
</style>
