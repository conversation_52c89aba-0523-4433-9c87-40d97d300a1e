<style lang="less" scoped>
@font-face {
  font-family: "DS-DIGII";
  src: url("../../assets/font/DS-DIGII.ttf");
  font-display: swap;
}
.details-main-warp {
  padding: 0.2rem 0.2rem 0.2rem;
  position: relative;
  .details-main {
    height: 100%;
    background-image: url("../../assets/kqbk.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    padding: 0.65rem 0.5rem 0.35rem;
    display: flex;
    justify-content: space-around;
    flex-direction: column;
    .details-region {
      padding: 0 0.7rem;
      margin-bottom: 0.15rem;
      .region-box {
        padding: 0.05rem 0;
        box-sizing: border-box;
        background: rgba(6, 30, 101, 1);
        border: 1px solid rgba(2, 174, 210, 1);
        display: flex;
        justify-content: space-between;
        align-items: center;
        .street {
          width: 3rem;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .site-type {
          width: 3rem;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .site-name {
          width: 3rem;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .select-time {
          width: 3.8rem;
          font-family: fontnameRegular;
          font-size: 0.36rem;
          margin-left: 0.35rem;
        }
        .line {
          width: 0.02rem;
          height: 0.7rem;
          background-image: url("../../assets/xgx.png");
          background-repeat: no-repeat;
          background-size: 100% 100%;
        }
        .time {
          width: 4rem;
          font-size: 0.32rem;
          font-family: "DS-DIGII";
          font-weight: 400;
          text-align: center;
          // display: none;
        }
        .name,
        .type {
          width: 3rem;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .type {
          > div {
            width: 1.6rem;
            height: 0.44rem;
            background: #0084ff;
            border-radius: 0.44rem;
            line-height: 0.44rem;
            font-size: 0.24rem;
            display: flex;
            justify-content: center;
          }
        }
        .name {
          // padding-left: 0.3rem;
          // width: 3.5rem;
        }
        .address {
          width: 6rem;
          font-size: 0.24rem;
          display: flex;
          align-items: center;
          padding-left: 0.5rem;
          > div:nth-child(2) {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
          .icon {
            width: 0.3rem;
            height: 0.3rem;
            margin-right: 0.28rem;
            background-image: url("../../assets/<EMAIL>");
            background-size: 100% 100%;
          }
        }
      }
    }
    .details-top {
      // height: 2.8rem;
      margin-bottom: 0.2rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .left-arrow {
        cursor: pointer;
        width: 0.4rem;
        height: 0.4rem;
        background: rgba(7, 56, 141, 0.6);
        border: 1px solid rgba(54, 218, 234, 0.42);
      }
      .middle-wrap {
        // flex: 1;
        height: 100%;
        width: 17rem;
        // width: 100%;
        padding: 0 0.3rem;
        .middle-carousel {
          display: flex;
          // justify-content: space-between;
          align-items: center;
          height: calc(100% - 0.5rem);
          > div {
            width: 1.8rem;
            height: 2.35rem;
          }
        }
      }
      .right-arrow {
        cursor: pointer;
        width: 0.4rem;
        height: 0.4rem;
        background: rgba(7, 56, 141, 0.6);
        border: 1px solid rgba(54, 218, 234, 0.42);
      }
    }
    .details-bottom {
      height: 3.5rem;
      padding: 0 0.7rem;
      .details-bottom-chart {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .bottom-chart {
          height: 2.5rem;
        }
      }
      .bottom-btn {
        height: 0.8rem;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
    .middle-carousel {
      .middle-carousel-title {
        height: 0.3rem;
        text-align: center;
        font-size: 0.18rem;
        font-family: PingFang SC;
        font-weight: bold;
        color: rgba(255, 255, 255, 1);
      }
      .middle-carousel-content {
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        height: calc(100% - 0.3rem);
        background: rgba(1, 41, 104, 1);
        padding: 0.1rem 0.2rem;
        > div {
          font-size: 16px;
          font-family: Source Han Sans CN;
          font-weight: 400;
          color: rgba(255, 255, 255, 1);
        }
      }
    }
    .common-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 0.2rem;
      font-family: Source Han Sans CN;
      font-weight: 500;
      color: #fff;
      text-shadow: 0 0 0.1rem #2e73d6, 0 0 0.1rem #2e73d6;
      margin-bottom: 0.1rem;
      > div {
        display: flex;
        align-items: center;
        > span:nth-child(1) {
          width: 0.05rem;
          display: inline-block;
          height: 0.25rem;
          background: #fff;
          margin-right: 0.2rem;
        }
      }
    }
    .svg-arrow {
      width: 0.35rem;
      height: 0.35rem;
    }
  }
  label {
    font-size: 0.16rem;
  }
  .ant-radio-group-solid
    .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
    color: #fff;
    background: #0084ff;
    border-color: #0084ff;
  }
  .ant-radio-button-wrapper-checked {
    z-index: 1;
    color: #0084ff;
    background: #fff;
    border-color: #0084ff;
    -webkit-box-shadow: -1px 0 0 0 #0084ff;
    box-shadow: -1px 0 0 0 #0084ff;
  }
  .ant-radio-button-wrapper:hover {
    position: relative;
    color: #0084ff;
  }
  .ant-radio-button-wrapper {
    position: relative;
    display: inline-block;
    height: 32px;
    margin: 0;
    padding: 0 30px;
    color: rgb(255, 255, 255);
    line-height: 30px;
    background: rgba(10, 45, 107, 1);
    margin-right: 0.1rem;
    border: 1px solid rgba(255, 255, 255, 0);
    border-top-width: 1.02px;
    border-left: 0;
    cursor: pointer;
    -webkit-transition: color 0.3s, background 0.3s, border-color 0.3s;
    transition: color 0.3s, background 0.3s, border-color 0.3s;
  }
  .ant-radio-button-wrapper:not(:first-child)::before {
    position: absolute;
    top: 0;
    left: -1px;
    display: block;
    width: 0px;
    height: 100%;
    background-color: #d9d9d9;
    content: "";
  }
}
</style>

<style lang="less">
.title-select2 {
  align-items: flex-end;
  .ant-select-selection {
    width: 2.5rem;
    height: 0.3rem;
    font-size: 0.24rem;
    // background: rgba(14, 139, 255, 0.32);
    border: none;
    border-radius: unset;
    // clip-path: polygon(100% 0, 100% 100%, 12% 100%, 0% 70%, 0 0);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-color: transparent;
  }
  .ant-select-selection__placeholder,
  .ant-select-search__field__placeholder {
    height: 0.3rem;
    text-align: center;
    line-height: 0.3rem;
  }
  .ant-select-selection-selected-value {
    color: #1ffffc;
  }
  .ant-select-focused .ant-select-selection,
  .ant-select-selection:focus,
  .ant-select-selection:active {
    border-color: transparent !important;
    border-right-width: 0 !important;
    outline: 0 !important;
    box-shadow: none !important;
    border: none !important;
  }
  .ant-select-selection__rendered {
    display: flex;
    justify-content: center;
  }
}
.middle-carousel {
  .swiper-slide {
    height: 2.35rem !important;
  }
}
</style>

<template>
  <section class="details-main-warp">
    <div class="details-main">
      <!-- 头部区域选择 -->
      <div class="details-region">
        <div class="region-box" v-if="$route.query.type === 1">
          <!-- 街道筛选 -->
          <div class="street">
            <a-select
              v-model="streetName"
              class="title-select2"
              @change="streetNameChange"
              placeholder="请选择街道"
            >
              <a-icon
                slot="suffixIcon"
                type="caret-down"
                style="color:#1FFFFC;font-size:0.2rem"
              />
              <a-select-option
                :value="String(index)"
                v-for="(item, index) in siteList"
                :key="index"
                >{{ item.street }}</a-select-option
              >
            </a-select>
          </div>
          <div class="line"></div>
          <!-- 站点类型筛选 -->
          <div class="site-type">
            <a-select
              v-model="siteTypeName"
              class="title-select2"
              @change="siteTypeNameChange"
              placeholder="请选择站点类型"
            >
              <a-icon
                slot="suffixIcon"
                type="caret-down"
                style="color:#1FFFFC;font-size:0.2rem"
              />
              <a-select-option :value="'sk'">市控站</a-select-option>
              <a-select-option :value="'qk'">区控站</a-select-option>
              <a-select-option :value="'wk'">微控站</a-select-option>
            </a-select>
          </div>
          <div class="line"></div>
          <!-- 站点名称筛选 -->
          <div class="site-name">
            <a-select
              v-model="sitetName"
              class="title-select2"
              @change="sitetNameChange"
              placeholder="请选择站点"
            >
              <a-icon
                slot="suffixIcon"
                type="caret-down"
                style="color:#1FFFFC;font-size:0.2rem"
              />
              <a-select-option
                :value="String(index)"
                v-for="(item, index) in siteList[Number(streetName)].data[
                  siteTypeName
                ]"
                :key="index"
                >{{ item.positionName }}</a-select-option
              >
            </a-select>
          </div>
          <div class="line"></div>
          <div class="address">
            <div class="icon"></div>
            <div>{{ stationdDetails.address }}</div>
          </div>
        </div>
        <div class="region-box" v-if="$route.query.type === 2">
          <div class="time">{{ nowTime }}</div>
          <div class="line"></div>
          <div class="name">
            <a-select
              :defaultValue="`${stationCode}`"
              v-model="stationCode"
              class="title-select2"
              @change="stationChange"
            >
              <a-icon
                slot="suffixIcon"
                type="caret-down"
                style="color:#1FFFFC;font-size:0.2rem"
              />
              <a-select-option
                :value="
                  $route.query.type === 1
                    ? `${item.stationCode}`
                    : `${item.stationId}`
                "
                v-for="(item, index) in stationList"
                :key="index"
              >
                {{
                  $route.query.type === 1 ? item.positionName : item.stationName
                }}
              </a-select-option>
            </a-select>
          </div>
          <div class="line"></div>
          <div class="type">
            <div v-if="stationdDetails.stationTypeId == 1">区控站</div>
            <div v-if="stationdDetails.stationTypeId == 2">微控站</div>
            <div v-if="stationdDetails.stationTypeId == 3">市控站</div>
            <div v-if="$route.query.type === 2">监测站</div>
          </div>
          <div class="line"></div>
          <div class="address">
            <div class="icon"></div>
            <div>{{ stationdDetails.address }}</div>
          </div>
        </div>
      </div>
      <!-- 上半边部分 -->
      <div class="details-top">
        <!-- 左侧箭头 -->
        <div class="left-arrow">
          <svgicon name="left" color="#00D9D5" class="svg-arrow"></svgicon>
        </div>
        <!-- 中间部分 -->
        <div class="middle-wrap">
          <!-- 通用标题 -->
          <div class="common-title">
            <div>
              <span></span>
              <span>
                {{
                  `${
                    $route.query.type === 1
                      ? "24小时空气质量监测"
                      : "24小时水质质量监测"
                  }`
                }}
              </span>
            </div>
            <div
              style="text-shadow: none;font-size: 0.18rem;"
              v-if="$route.query.type === 1"
            >
              （单位：μg/m³）
            </div>
          </div>
          <!-- 内容滚动部分 -->
          <swiper
            class="middle-carousel"
            :options="swiperOption"
            v-if="trendDataList.length > 8"
          >
            <swiper-slide v-for="(item, i) in trendDataList" :key="i">
              <!-- 空气 -->
              <div
                v-if="$route.query.type === 1"
                class="middle-carousel-title"
                :style="{
                  backgroundColor: item.bgColor ? item.bgColor : '#0072ff'
                }"
              >
                {{ $route.query.type === 1 ? `${i}:00` : `${item.hour}:00` }}
              </div>
              <!-- 水 -->
              <!-- :style="{ backgroundColor: `${colors[i % 6]}` }" -->
              <div
                v-if="$route.query.type === 2"
                class="middle-carousel-title"
                :style="{ backgroundColor: '#0072ff' }"
              >
                {{ $route.query.type === 1 ? `${i}:00` : `${item.hour}:00` }}
              </div>
              <!-- 空气质量 -->
              <div
                v-if="$route.query.type === 1"
                class="middle-carousel-content"
              >
                <div style="margin-left: 0.15rem;">
                  NO2:
                  <span
                    :style="{ 'font-size': '0.2rem', color: item.fontColor }"
                    >{{ item.nitrogenDioxide }}</span
                  >
                </div>
                <div style="margin-left: 0.15rem;">
                  PM2.5:
                  <span
                    :style="{ 'font-size': '0.2rem', color: item.fontColor }"
                    >{{ item.fineParticulateMatter }}</span
                  >
                </div>
                <div style="margin-left: 0.15rem;">
                  PM10:
                  <span
                    :style="{ 'font-size': '0.2rem', color: item.fontColor }"
                    >{{ item.inhalableParticles }}</span
                  >
                </div>
                <div style="margin-left: 0.15rem;">
                  O3:
                  <span
                    :style="{ 'font-size': '0.2rem', color: item.fontColor }"
                    >{{ item.ozone }}</span
                  >
                </div>
              </div>
              <!-- 水质污染 -->
              <div
                v-else
                class="middle-carousel-content"
                style="padding:0.1rem 0.15rem;"
              >
                <div
                  v-for="(detail, index) in item.monitorItemDetails"
                  :key="index"
                >
                  <!-- {{ airTypeName[detail.pollutantName] }}: {{ detail.monValue }} -->
                  {{ detail.itemName }}: {{ detail.monitorValue }}({{
                    detail.itemUnit ? detail.itemUnit : " - "
                  }})
                </div>
              </div>
            </swiper-slide>
            <!-- <div class="swiper-pagination" slot="pagination"></div> -->
          </swiper>
          <!-- 内容滚动部分 -->
          <div class="middle-carousel" v-if="trendDataList.length <= 8">
            <div
              v-for="(item, i) in trendDataList"
              :key="i"
              style="margin-right: 0.15rem;"
            >
              <!-- 空气 -->
              <div
                v-if="$route.query.type === 1"
                class="middle-carousel-title"
                :style="{
                  backgroundColor: item.bgColor ? item.bgColor : '#0072ff'
                }"
              >
                {{ $route.query.type === 1 ? `${i}:00` : `${item.hour}:00` }}
              </div>
              <!-- 水 -->
              <!-- :style="{ backgroundColor: `${colors[i % 6]}` }" -->
              <div
                v-if="$route.query.type === 2"
                class="middle-carousel-title"
                :style="{ backgroundColor: '#0072ff' }"
              >
                {{ $route.query.type === 1 ? `${i}:00` : `${item.hour}:00` }}
              </div>
              <!-- 空气质量 -->
              <div
                v-if="$route.query.type === 1"
                class="middle-carousel-content"
              >
                <div style="margin-left: 0.15rem;">
                  NO2:
                  <span
                    :style="{ 'font-size': '0.2rem', color: item.fontColor }"
                    >{{ item.nitrogenDioxide }}</span
                  >
                </div>
                <div style="margin-left: 0.15rem;">
                  PM2.5:
                  <span
                    :style="{ 'font-size': '0.2rem', color: item.fontColor }"
                    >{{ item.fineParticulateMatter }}</span
                  >
                </div>
                <div style="margin-left: 0.15rem;">
                  PM10:
                  <span
                    :style="{ 'font-size': '0.2rem', color: item.fontColor }"
                    >{{ item.inhalableParticles }}</span
                  >
                </div>
                <div style="margin-left: 0.15rem;">
                  O3:
                  <span
                    :style="{ 'font-size': '0.2rem', color: item.fontColor }"
                    >{{ item.ozone }}</span
                  >
                </div>
              </div>
              <!-- 水质污染 -->
              <div
                v-else
                class="middle-carousel-content"
                style="padding:0.1rem 0.15rem;"
              >
                <div
                  v-for="(detail, index) in item.monitorItemDetails"
                  :key="index"
                >
                  <!-- {{ airTypeName[detail.pollutantName] }}: {{ detail.monValue }} -->
                  {{ detail.itemName }}: {{ detail.monitorValue }}({{
                    detail.itemUnit ? detail.itemUnit : " - "
                  }})
                </div>
              </div>
            </div>
            <!-- <div class="swiper-pagination" slot="pagination"></div> -->
          </div>
        </div>
        <!-- 右侧箭头 -->
        <div class="right-arrow">
          <svgicon name="right" color="#00D9D5" class="svg-arrow"></svgicon>
        </div>
      </div>
      <!-- 下半边部分 -->
      <div class="details-bottom">
        <div class="details-bottom-chart">
          <div>
            <!-- 通用标题 -->
            <div class="common-title">
              <div>
                <span></span>
                <span>
                  {{
                    `${
                      $route.query.type === 1
                        ? "24小时空气质量趋势"
                        : "24小时水质质量趋势"
                    }`
                  }}
                </span>
              </div>
            </div>
            <div class="bottom-chart">
              <MultiLineChart
                v-if="$route.query.type === 1"
                :id="'multi-line-trend'"
                :width="'8.2rem'"
                :height="'2.5rem'"
                :type="1"
                :airDataProp="airDataProp"
                :airContrast="airContrast"
              ></MultiLineChart>
              <LineChartDashed
                v-else
                :id="'waterMonitor'"
                :width="'8.2rem'"
                :height="'2.5rem'"
                :propData="waterMonitorData"
                :smooth="false"
              />
            </div>
          </div>
          <div>
            <!-- 通用标题 -->
            <div class="common-title">
              <div>
                <span></span>
                <span>
                  {{
                    `${
                      $route.query.type === 1 ? "空气质量对比" : "水质质量对比"
                    }`
                  }}
                </span>
              </div>
            </div>
            <div class="bottom-chart">
              <MultiLineChart
                v-if="$route.query.type === 1"
                :id="'multi-line-contrast'"
                :width="'8.2rem'"
                :height="'2.5rem'"
                :type="2"
                :airDataProp="airDataProp"
                :airContrast="airContrast"
              ></MultiLineChart>
              <LineAndBarChart
                v-else
                :id="'detail-water-change'"
                :width="'8.2rem'"
                :height="'2.5rem'"
                :LineAndBarData="waterChangeChartData"
              />
            </div>
          </div>
        </div>
        <div class="bottom-btn">
          <a-radio-group
            v-model="elementType"
            buttonStyle="solid"
            size="large"
            @change="elementTypeChange"
          >
            <a-radio-button
              v-for="btn in ButtonGroup"
              :key="btn.value"
              :value="btn.value"
              >{{ btn.name }}
            </a-radio-button>
          </a-radio-group>
        </div>
      </div>
    </div>
  </section>
</template>

<script lang="ts">
/* eslint-disable @typescript-eslint/ban-ts-ignore */
import { Component, Vue, Watch } from "vue-property-decorator";
import MultiLineChart from "@/components/Charts/MultiLineChart.vue";
import LineChartDashed from "@/components/Charts/LineChartDashed.vue";
import LineAndBarChart from "@/components/Charts/LineAndBarChart.vue";
import { Radio, Select, Icon } from "ant-design-vue";
import { Swiper, SwiperSlide } from "vue-awesome-swiper";
import { getNowTime } from "@/utils/index";
import {
  getAllAqiInfo,
  aqidetails,
  airStationdDetails,
  aqiTrend,
  yearOnYear,
  aqcistation,
  airStationOptionList
} from "@/api/air";
import {
  getHourMonitorRecordList,
  getStationList,
  getMonitorItemDetailRecord
} from "@/api/water";
import { getAirTypes } from "@/api/homeTable";
interface DataList {
  name: string;
  value: number;
}

interface ButtonProperty {
  name: string | number;
  value: string | number;
}

interface AirDataProp {
  airTrendYesterday: string | number[];
  airToday1: string | number[];
  airToday2: string | number[];
  airAverage: string | number[];
  unit: string;
}

interface AirContrast {
  monthList: string | number[];
  thisYear: string | number[];
  lastYear: string | number[];
  thisYearName?: string;
  lastYearName?: string;
}

interface LineChartData {
  bottomList: string[];
  dataList: string[];
  unit?: string;
}

interface WaterChageData {
  bottomList: string[];
  currentYearName: string;
  currentYearDataList: string[];
  beforeOneYearName: string;
  beforeOneYearDataList: string[];
  unit?: string;
}

enum EnterType {
  AIR = 1,
  WATER = 2,
  VEHICLE = 3,
  HOME = 4
}

@Component({
  name: "Details",
  components: {
    MultiLineChart,
    LineChartDashed,
    LineAndBarChart,
    ARadioGroup: Radio.Group,
    ARadioButton: Radio.Button,
    AIcon: Icon,
    ASelect: Select,
    ASelectOption: Select.Option,
    Swiper,
    SwiperSlide
  }
})
export default class extends Vue {
  @Watch("stationCode")
  public async onStationCode(newValue: string, oldValue: string) {
    // @ts-ignore
    if (this.$route.query.type === EnterType.AIR) {
      this.getAqidetails();
      this.getAqiTrend();
      this.getYearOnYear();
      this.getAirStationdDetails();
      this.getAirTypes();
    } else {
      await this.getWaterStationList();
      await this.getWaterHourMonitorRecordList();
    }
  }
  @Watch("elementType", { immediate: true, deep: true })
  public onSirTypeChange(newValue: string, oldValue: string) {
    // @ts-ignore
    if (this.$route.query.type === EnterType.AIR) {
      this.getAqiTrend();
      // this.getYearOnYear();
    }
  }
  private nowTime = "";
  private timer: any;
  readonly colors: Array<string> = [
    "#1D9DFF",
    "#FF9F7F",
    "#FB7293",
    "#E7BCF3",
    "#8378EA",
    "#32C5E9",
    "#9FE6B8",
    "#FFDB5C"
  ];
  private airTypeName = {
    NO2: "μg/m³",
    O3: "μg/m³",
    PM10: "μg/m³",
    "PM2.5": "μg/m³",
    NOx: "μg/m³",
    NO: "μg/m³"
  };
  private airTypeNamePub = {
    NO2: "NO₂",
    O3: "O₃",
    PM10: "PM₁₀",
    "PM2.5": "PM₂.₅",
    NOx: "NOx",
    NO: "NO"
  };
  private dataList!: Array<any>;

  private displayDataList: Array<DataList> = [
    {
      name: "二氧化硫",
      value: 0.13
    },
    {
      name: "二氧化氮",
      value: 0.4
    },
    {
      name: "PM2.5",
      value: 0.139
    },
    {
      name: "PM10",
      value: 0.246
    },
    {
      name: "一氧化硫",
      value: 0.145
    },
    {
      name: "臭氧",
      value: 0.317
    }
  ];

  private ButtonGroup: Array<ButtonProperty> = [
    // {
    //   name: "AQI",
    //   value: "aqi"
    // },
    {
      name: "NO₂",
      value: "101"
    },
    {
      name: "PM₂.₅",
      value: "105"
    },
    {
      name: "PM₁₀",
      value: "104"
    },
    {
      name: "O₃",
      value: "102"
    },
    {
      name: "SO₂",
      value: "100"
    },
    {
      name: "CO",
      value: "103"
    }
  ];

  private swiperOption: any = {
    slidesPerView: 8,
    spaceBetween: 15,
    slidesPerGroup: 1,
    loop: true,
    loopFillGroupWithBlank: true,
    pagination: {
      el: ".swiper-pagination",
      clickable: true
    },
    autoplay: {
      delay: 30000,
      disableOnInteraction: false
    },
    navigation: {
      nextEl: ".left-arrow",
      prevEl: ".right-arrow"
    }
  };

  readonly AirColors: Array<any> = [
    {
      color: "rgb(0,255,0)",
      min: 0,
      max: 50
    },
    {
      color: "rgb(255,255,0)",
      min: 51,
      max: 100
    },
    {
      color: "rgb(255,126,0)",
      min: 101,
      max: 150
    },
    {
      color: "rgb(255,0,0)",
      min: 151,
      max: 200
    },
    {
      color: "rgb(153,0,76)",
      min: 201,
      max: 300
    },
    {
      color: "rgb(126,0,35)",
      min: 300,
      max: 999
    }
  ];
  readonly NO2Colors: number = 700;
  readonly O3Colors: number = 300;
  readonly PM10Colors: number = 250;
  readonly PM25Colors: number = 115;
  private stationList = [];
  private stationCode = "";
  private elementType = "";
  private trendDataList = [];
  private airDataProp: AirDataProp = {
    airTrendYesterday: [],
    airToday1: [],
    airToday2: [],
    airAverage: [],
    unit: ""
  };
  private airContrast: AirContrast = {
    thisYear: [], // 今年
    lastYear: [], // 上年
    monthList: [],
    thisYearName: "",
    lastYearName: ""
  };
  // 下方按钮组自动轮播count
  private switchButtonGroupCount = 0;
  // 下方按钮组自动轮播timer
  private switchButtonGroupTimer: any;
  private selectTimer: any;
  private airTimer: any;
  // 水质变化Chart
  private waterChangeChartData: WaterChageData = {
    bottomList: [
      "1月",
      "2月",
      "3月",
      "4月",
      "5月",
      "6月",
      "7月",
      "8月",
      "9月",
      "10月",
      "11月",
      "12月"
    ],
    currentYearName: "",
    currentYearDataList: [],
    beforeOneYearName: "",
    beforeOneYearDataList: [],
    unit: ""
  };
  //水质监测详情数据
  private waterMonitorData: LineChartData = {
    bottomList: [],
    dataList: [],
    unit: ""
  };
  private stationdDetails: any = {};
  private siteList: any[] = [];
  private streetName = "0";
  private siteTypeName: any = "qk"; //  = "sk"
  private sitetName: any = "请选择站点"; // = "0"
  created() {
    // @ts-ignore
    this.stationCode = this.$route.query.stationCode;
    // @ts-ignore
    if (this.$route.query.type === EnterType.AIR) {
      this.getAirStationOptionList();
      this.getAllAirAqiInfo();
    }
  }
  mounted() {
    this.nowTime;
    this.timer = setInterval(() => {
      this.nowTime = getNowTime();
    }, 1000);
    // @ts-ignore
    if (this.$route.query.type === EnterType.AIR) {
      // 空气质量
      this.getAqidetails();
      // this.getAqiTrend();
      this.getYearOnYear();
      this.autoTime1();
    } else {
      // 水质监测
      this.getWaterStationList();
      this.getWaterHourMonitorRecordList();
    }
    // @ts-ignore
    console.log("routeQuery----->", this.$route.query);
  }
  beforeDestroy() {
    clearInterval(this.timer);
    clearInterval(this.switchButtonGroupTimer);
    clearInterval(this.switchButtonGroupTimer);
    clearTimeout(this.airTimer);
  }
  // 下拉框定时器
  private autoTime() {
    const autoTimer = () => {
      for (let index = 0; index < this.stationList.length; index++) {
        this.selectTimer = setTimeout(() => {
          this.stationCode = (this.stationList[index] as any).stationCode;
          if (index === this.stationList.length - 1) {
            clearTimeout(this.selectTimer);
            setTimeout(autoTimer, 90000);
          }
        }, index * 90000);
      }
    };
    autoTimer();
  }
  private i: any = 0;
  // 污染物类型定时器
  private autoTime1() {
    // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
    // @ts-ignore
    this.elementType = this.ButtonGroup[this.i].value;
    const autoTimer = () => {
      this.airTimer = setInterval(() => {
        // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
        // @ts-ignore
        this.elementType = this.ButtonGroup[this.i].value;
        if (this.i === this.ButtonGroup.length - 1) {
          setTimeout(() => {
            clearInterval(this.airTimer);
            this.i = 0;
            // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
            // @ts-ignore
            this.elementType = this.ButtonGroup[this.i].value;
            autoTimer();
          }, 4900);
        }
        if (this.i < this.ButtonGroup.length - 1) {
          this.i = this.i + 1;
        }
      }, 5000);
    };
    autoTimer();
  }

  // 街道类型站点层级列表选择
  private getAirStationOptionList() {
    airStationOptionList().then((res: any) => {
      this.siteList = res.data.data;
      res.data.data.forEach((item1: any, index1: number) => {
        for (const key in item1.data) {
          item1.data[key].forEach((item2: any, index2: number) => {
            if (item2.stationCode === this.stationCode) {
              console.log(res.data.data[index1]);
              console.log(key);
              this.streetName = String(index1);
              this.siteTypeName = String(key);
              this.streetNameChange(String(index1));
              this.siteTypeNameChange(String(key));
            }
          });
        }
      });
    });
  }

  // 街道名称
  private streetNameChange(val: any) {
    this.streetName = val;
    this.siteTypeName = "qk";
    this.sitetName =
      this.siteList[Number(val)].data[this.siteTypeName].length != 0
        ? "0"
        : "请选择站点";
    if (this.siteList[Number(val)].data[this.siteTypeName].length != 0) {
      this.stationCode = this.siteList[Number(val)].data[
        this.siteTypeName
      ][0].stationCode;
    }
  }

  // 站点类型
  private siteTypeNameChange(val: any) {
    // console.log(this.siteList[Number(this.streetName)].data[e]);
    this.siteTypeName = val;
    this.sitetName =
      this.siteList[Number(this.streetName)].data[val].length != 0
        ? "0"
        : "请选择站点";
    if (this.siteList[Number(this.streetName)].data[val].length != 0) {
      this.stationCode = this.siteList[Number(this.streetName)].data[
        val
      ][0].stationCode;
    }
  }

  // 站点名称
  private sitetNameChange(val: any) {
    this.sitetName = val;
    this.stationCode = this.siteList[Number(this.streetName)].data[
      this.siteTypeName
    ][Number(val)].stationCode;
  }

  // 根据站点code获取污染物列表
  private getAirTypes() {
    getAirTypes({
      stationCode: this.stationCode
    }).then((res: any) => {
      const ButtonGroup: Array<ButtonProperty> = [];
      for (const key in res.data.data) {
        ButtonGroup.push({
          name: key,
          value: res.data.data[key]
        });
      }
      this.ButtonGroup = ButtonGroup;
    });
  }

  // 通过区代码获取全部站点aqi信息
  private getAllAirAqiInfo() {
    getAllAqiInfo({ areaCode: "510106" }).then((res: any) => {
      this.stationList = res.data.data;
      // this.autoTime();
      this.getAqiTrend();
      this.getAirStationdDetails();
    });
  }
  // 站点下拉框
  private stationChange(val: any) {
    this.stationCode = val;
    // @ts-ignore
    if (this.$route.query.type === EnterType.AIR) {
      // 处理逻辑
    } else {
      this.switchButtonGroupCount = 0;
      this.stationList.find((item: any, index: number) => {
        if (item.stationId == val) {
          this.stationdDetails.address = item.stationAddress;
        }
      });
    }
  }
  // 详情页顶部站点信息
  private getAirStationdDetails() {
    airStationdDetails(this.stationCode).then((res: any) => {
      this.stationdDetails = res.data.data;
    });
  }
  // 24小时空气质量检测
  private getAqidetails() {
    aqidetails({
      stationCode: this.stationCode
    }).then((res: any) => {
      for (const item in res.data.data) {
        if (res.data.data[item].length == 0) {
          delete res.data.data[item];
        }
      }
      for (const item in res.data.data) {
        for (const item1 in res.data.data[item]) {
          // NO2
          if (item1 == "nitrogenDioxide") {
            if (res.data.data[item].monValue > this.NO2Colors) {
              res.data.data[item].fontColor = "rgb(255,0,0)";
              res.data.data[item].bgColor = "rgb(255,0,0)";
            } else {
              res.data.data[item].fontColor = "#fff";
            }
          }
          // O3
          if (item1 == "ozone") {
            if (res.data.data[item].monValue > this.O3Colors) {
              res.data.data[item].fontColor = "rgb(255,0,0)";
              res.data.data[item].bgColor = "rgb(255,0,0)";
            } else {
              res.data.data[item].fontColor = "#fff";
            }
          }
          // PM10
          if (item1 == "inhalableParticles") {
            if (res.data.data[item].monValue > this.PM10Colors) {
              res.data.data[item].fontColor = "rgb(255,0,0)";
              res.data.data[item].bgColor = "rgb(255,0,0)";
            } else {
              res.data.data[item].fontColor = "#fff";
            }
          }
          // PM2.5
          if (item1 == "fineParticulateMatter") {
            if (res.data.data[item].monValue > this.PM25Colors) {
              res.data.data[item].fontColor = "rgb(255,0,0)";
              res.data.data[item].bgColor = "rgb(255,0,0)";
            } else {
              res.data.data[item].fontColor = "#fff";
            }
          }
        }
      }
      const dataList: any = [];
      for (const key in res.data.data) {
        dataList.push(res.data.data[key]);
      }
      this.trendDataList = dataList;
    });
  }
  // 24小时空气质趋势
  private getAqiTrend() {
    if (this.elementType != "" && this.stationCode != "") {
      aqiTrend({
        pollutantCode: this.elementType,
        stationCode: this.stationCode
      }).then((res: any) => {
        if (res.data.data) {
          const airTrendYesterday = [];
          const airToday1: any[] = [];
          const airToday2: any[] = [];
          const airAverage: any[] = [];
          if (
            res.data.data.yesterday &&
            res.data.data.todayList &&
            res.data.data.todayAverage
          ) {
            for (const item of res.data.data.yesterday) {
              airTrendYesterday.push(item.monValue);
            }
            res.data.data.todayList.forEach((item: any, index: number) => {
              if (index <= new Date().getHours()) {
                airToday1.push(item.monValue);
              } else {
                airToday1.push("-");
              }
            });
            res.data.data.todayList.forEach((item: any, index: number) => {
              if (index <= new Date().getHours()) {
                airToday2.push("-");
              } else {
                airToday2.push(item.monValue);
              }
            });
            airToday2[airToday2.lastIndexOf("-")] =
              airToday1[airToday2.lastIndexOf("-")];
            for (let index = 0; index <= 24; index++) {
              airAverage.push(res.data.data.todayAverage);
            }
          }
          if (this.elementType == "103") {
            this.airDataProp.unit = "mg/m³";
          } else {
            this.airDataProp.unit = "μg/m³";
          }
          this.airDataProp.airTrendYesterday = airTrendYesterday;
          this.airDataProp.airToday1 = airToday1;
          this.airDataProp.airToday2 = airToday2;
          this.airDataProp.airAverage = airAverage;
        }
      });
    }
  }
  // 切换按钮组标签
  private elementTypeChange(e: any) {
    this.elementType = e.target.value;
    // @ts-ignore
    if (this.$route.query.type === EnterType.AIR) {
      this.ButtonGroup.find((item: any, index: number) => {
        if (item.value == e.target.value) {
          this.i = index;
          return;
        }
      });
      // 处理逻辑
    } else {
      this.ButtonGroup.forEach((item: any, index: number) => {
        if (item.value === e.target.value) {
          if (!e.target.mechanical) {
            this.switchButtonGroupCount = index;
            this.waterMonitorData.unit = item.unit;
            this.waterChangeChartData.unit = item.unit;
          }
        }
      });
      this.getMonitorItemDetailRecord(this.stationCode, this.elementType);
    }
  }
  // 空气质量对比
  private getYearOnYear() {
    aqcistation(this.stationCode).then((res: any) => {
      const airContrast: AirContrast = {
        thisYear: [], // 今年
        lastYear: [], // 上年
        monthList: [],
        thisYearName: "今年",
        lastYearName: "去年"
      };
      for (const item of res.data.data.thisYear) {
        (airContrast.thisYear as any).push(item.aqci);
      }
      for (const item of res.data.data.lastYear) {
        (airContrast.lastYear as any).push(item.aqci);
      }
      const leng = 12 - airContrast.lastYear.length;
      for (let index = 0; index < leng; index++) {
        (airContrast.lastYear as any).unshift("-");
      }
      for (let index = 1; index <= 12; index++) {
        (airContrast.monthList as any).push(index + "月");
      }
      this.airContrast = airContrast;
    });
  }
  // 获取水质监测站点列表
  private getWaterStationList(): void {
    getStationList('').then((res:any) => {
      if (res.data.data && res.data.data.length > 0) {
        this.stationList = res.data.data;
        this.stationList.find((item: any) => {
          if (item.stationId == this.stationCode) {
            this.stationdDetails.address = item.stationAddress;
          }
        });
        this.generateCurrentBtn();
      }
    });
  }
  // 获取水质监测24小时监测数据
  private getWaterHourMonitorRecordList(): void {
    getHourMonitorRecordList(this.stationCode).then(res => {
      if (res.data.data && res.data.data.length > 0) {
        this.trendDataList = res.data.data;
      } else {
        this.trendDataList = [];
      }
    });
  }
  // 生成下方只针对此时站点的项目按钮
  private generateCurrentBtn(): void {
    for (const station of this.stationList as Array<any>) {
      if (station.stationId === this.stationCode + "") {
        this.ButtonGroup = station.monitorItems.map((item: any) => {
          return {
            name: item.name,
            value: item.itemCode,
            unit: item.concentrationUnit
          };
        });
        this.waterMonitorData.unit = (this.ButtonGroup[0] as any).unit + "";
        this.waterChangeChartData.unit = (this.ButtonGroup[0] as any).unit + "";
        this.getMonitorItemDetailRecord(
          this.stationCode,
          this.ButtonGroup[0].value + ""
        );
        this.switchButtonGroup(this.ButtonGroup, true);
      }
    }
  }
  // 自动切换下方按钮组
  private switchButtonGroup(buttonList: Array<any>, immediate?: boolean): void {
    // eslint-disable-next-line
    const _this = this;
    const total = buttonList.length;
    // 如果有中间操作，重置timer
    if (this.switchButtonGroupTimer) {
      clearInterval(this.switchButtonGroupTimer);
    }
    if (immediate && buttonList.length && buttonList.length > 0) {
      // 立即执行一次
      this.elementType = buttonList[0].value + "";
      this.getMonitorItemDetailRecord(this.stationCode, this.elementType);
      this.switchButtonGroupCount++;
    }
    this.switchButtonGroupTimer = setInterval(() => {
      _this.elementType = buttonList[_this.switchButtonGroupCount].value + "";
      _this.switchButtonGroupCount < total - 1
        ? _this.switchButtonGroupCount++
        : (_this.switchButtonGroupCount = 0);
      _this.elementTypeChange({
        target: {
          value: _this.elementType,
          mechanical: true
        }
      });
    }, 30 * 1000);
  }
  // 获取监测项详情记录
  private getMonitorItemDetailRecord(stationId: string, itemCode: string) {
    getMonitorItemDetailRecord(stationId, itemCode).then(res => {
      if (res.data.data) {
        const { hourMonitorItems, lastYear, thisYear } = res.data.data;
        // 24小时水质质量趋势
        if (hourMonitorItems.length > 0) {
          const sortedHourMonitorItems = hourMonitorItems.sort(
            (a: any, b: any) => {
              return a.hour - b.hour;
            }
          );
          this.waterMonitorData.bottomList = [];
          this.waterMonitorData.dataList = [];
          sortedHourMonitorItems.forEach((record: any) => {
            this.waterMonitorData.bottomList.push(record.hour + "时");
            this.waterMonitorData.dataList.push(record.monitorValue || "-");
          });
        }

        // 水质质量对比 lastYear
        if (lastYear.length > 0) {
          const sortedLastYear = lastYear.sort((a: any, b: any) => {
            return a.month - b.month;
          });
          this.waterChangeChartData.beforeOneYearName =
            sortedLastYear[0].year + "";
          this.waterChangeChartData.beforeOneYearDataList = [];
          this.waterChangeChartData.bottomList = [];
          sortedLastYear.forEach((yearLucy: any) => {
            this.waterChangeChartData.bottomList.push(`${yearLucy.month}月`);
            this.waterChangeChartData.beforeOneYearDataList.push(
              yearLucy.value || "-"
            );
          });
        }

        // 水质质量对比 thisYear
        if (thisYear.length > 0) {
          const sortedThisYear = thisYear.sort((a: any, b: any) => {
            return a.month - b.month;
          });
          this.waterChangeChartData.currentYearName =
            sortedThisYear[0].year + "";
          this.waterChangeChartData.currentYearDataList = [];
          sortedThisYear.forEach((yearLucky: any) => {
            this.waterChangeChartData.currentYearDataList.push(
              yearLucky.value || "-"
            );
          });
        }
      }
    });
  }
}
</script>
