<template>
  <div class="marker-control-box" :style="{ right: '4.3rem' }">
    <!-- 按钮 S -->
    <div
      class="flex items-center marker-control-btn"
      @click="showAreaList = !showAreaList"
    >
      <div class="marker-control-btn-icon"></div>
      <span>地图区域</span>
    </div>
    <!-- 按钮 E -->

    <!-- 图标选择弹窗 S -->

    <transition
      :enter-active-class="enterClass"
      :leave-active-class="leaveClass"
    >
      <div v-show="showAreaList" class="icon-selecte-box">
        <!-- <a-checkbox
          v-model="allSelectArea"
          class="check-item"
          @click="selectAllChange"
        >
          <div class="flex items-center">
            <span class="name flex flex-1">全选</span>
          </div>
        </a-checkbox> -->
        <a-checkbox
          v-for="item in areaList"
          :key="item.code"
          v-model="item.selected"
          class="check-item"
          @change="checkChange(item.code)"
        >
          <div class="flex items-center">
            <span class="name flex flex-1" style="margin-left: 20px">{{
              item.name
            }}</span>
          </div>
        </a-checkbox>
      </div>
    </transition>
    <!-- 图标选择弹窗 E -->
  </div>
</template>

<script>
import { Checkbox } from "ant-design-vue";
import { districtList } from "@/api/analysisAir";
export default {
  components: {
    ACheckbox: Checkbox,
  },
  props: {
    typeNum: {
      type: Number,
      default: 0,
    },
  },
  watch: {},
  created() {
    this.handleGetDistrictList();
  },
  data() {
    return {
      showAreaList: false,
      allSelectArea: [],
      leaveClass: "animate__animated animate__fadeOutRight",
      enterClass: "animate__animated animate__fadeInRight",
      activeIndex: 0,
      areaList: [],
    };
  },
  methods: {
    selectAllChange() {},
    checkChange(code) {
      console.log(code);
      const selectNum = this.areaList.filter((item) => item.selected);
      if (selectNum <= 1) {
        this.areaList.forEach((item) => {
          if (code === item.code) {
            item.selected = !item.selected;
          }
        });
        this.showAreaList = !this.showAreaList;
        return this.$message.warning("所选区域不能为空");
      }
      this.areaList.forEach((item) => {
        if (code === item.code) {
          item.selected = item.selected;
        }
      });
      this.allSelectArea = this.areaList
        .filter((item) => item.selected)
        .map((it) => it.code);
      this.$emit("handleGetSelectArea", this.allSelectArea);
      this.showAreaList = !this.showAreaList;
      console.log("this.allSelectArea", this.allSelectArea);
    },
    handleGetDistrictList() {
      districtList().then((res) => {
        console.log("行政区域列表", res);
        this.areaList = res.data.data.map((item) => {
          return {
            ...item,
            selected: item.name === "金牛区" ? true : false,
          };
        });
        const arr = this.areaList
          .filter((item) => item.name === "金牛区")
          .map((item) => item.code);
        console.log(arr, 66666666);
        this.$emit("handleGetSelectArea", arr);
      });
    },
  },
};
</script>

<style lang="less" scoped>
.flex {
  display: flex;
}
.items-center {
  justify-items: center;
  align-items: center;
  vertical-align: middle;
}
.marker-control-box {
  position: fixed;
  width: 1.5rem;
  height: 0.4rem;
  top: 1.45rem;
  background: url(~@/assets/images/<EMAIL>) no-repeat center center;
  background-size: 100% 100%;
  padding: 11px 32px;
  z-index: 2999;
  // transition-property: right;
  // transition-duration: .5s;

  .marker-control-btn {
    width: 100%;
    height: 100%;
    cursor: pointer;

    span {
      font-size: 14px;
      font-weight: bold;
      color: #ffffff;
    }
  }

  .marker-control-btn-icon {
    width: 17px;
    height: 15px;
    background: url(~@/assets/images/<EMAIL>) no-repeat center
      center;
    background-size: 100% 100%;
    margin-right: 9px;
    margin-top: 2px;
  }

  .icon-selecte-box {
    position: absolute;
    padding: 5px 15px 5px 20px;
    margin-top: 20px;
    left: -9px;
    width: 180px;
    background-color: rgba(0, 4, 18, 0.56);
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    border-top: 2px solid #3883df;
    border-bottom: 2px solid #3883df;
  }
}

.check-item {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  // margin-left: 0;
  margin: 10px 0;

  img {
    width: 16px;
    height: 14px;
    margin: 2px 10px 0 10px;
  }

  .name {
    display: inline-block;
    color: #caf6ff;
    font-size: 14px;
  }
}
</style>
