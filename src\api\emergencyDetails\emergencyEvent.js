import request from "@/utils/request";
// import axios from 'axios';
const URL = {
  emergencyStat: '/task/emergencyStat',
  taskRecord: '/task/taskPageByWSBG',
  taskDetail: '/task/taskDetailByWSBG?taskId=',
  mapMarkerData: '/water/monitor/within/range/punctuation?',
  storagePage: '/waterSource/storage/listByWSBD',
  expertPage: '/waterSource/expert/listByWSBG',
  // 加油站详情
  getGasStationInfo: '/water/monitor/gasDetailByWSB?projectId=',
  // 工地详情
  getBuildingInfo: '/air/company/buildingDetail?projectId=',
  // 餐饮详情
  getRestaurantInfo: '/water/monitor/restaurantDetailByWSB?projectId=',
  // 汽修详情
  getGarageInfo: '/water/monitor/garageDetailByWSB?projectId=',
  // 印刷详情
  getPrintFactoryInfo: '/water/monitor/printFactoryDetailByWSBG?projectId=',
  // 医疗详情
  getHospitalInfo: '/water/sewage/enterprise/hospitalDetail?projectId=',
  // 摄像头、音视频、物资仓库详情
  getDeviceAndStorageInfo: '/water/monitor/deviceAndStorage',
  // 获取水厂信息
  getWaterWorkInfo: '/water/monitor/waterworksDetail?project=',
  // 获取全景摄像头列表
  panoramicCameraList: '/water/panoramic/camera/list',
  // 获取直播流地址
  panoramicCameraLive: '/water/panoramic/camera/live/'
}


const emergencyStat = async () => request({ url: URL.emergencyStat, method: "get" })
const storagePage = async () => request({ url: URL.storagePage, method: "get", })
const expertPage = async () => request({ url: URL.expertPage, method: "get", })
const taskRecord = async (data) =>
  request({ url: URL.taskRecord,method: "post", data })
const getTaskDetail = async (taskId) =>
  request({ url: `${URL.taskDetail}${taskId}`,method: "get" })
const getMapMarkerData = async ({ lat, lng, distance }) =>
  request({
    method: "get",
    url: `${URL.mapMarkerData}lat=${lat}&lng=${lng}&distance=${distance}`,
  })

/**
 * 获取地图弹窗详情
 * @param {Object} data {
 * type: 加油站 工地 餐饮 汽修 印刷 医院 摄像头 音柱 仓库
 * id: 对应的id
 * }
 */
const getMapDialogInfo = async ({ type, id }) => {
  let url = ''

  switch (type) {
    case '加油站':
      url = `${URL.getGasStationInfo}${id}`
      break
    case '工地':
      url = `${URL.getBuildingInfo}${id}`
      break
    case '餐饮':
      url = `${URL.getRestaurantInfo}${id}`
      break
    case '汽修':
      url = `${URL.getGarageInfo}${id}`
      break
    case '印刷':
      url = `${URL.getPrintFactoryInfo}${id}`
      break
    case '医院':
      url = `${URL.getHospitalInfo}${id}`
      break
    case '摄像头':
      url = `${URL.getDeviceAndStorageInfo}?id=${id}&type=1`
      break
    case '音柱':
      url = `${URL.getDeviceAndStorageInfo}?id=${id}&type=2`
      break
    case '仓库':
      url = `${URL.getDeviceAndStorageInfo}?id=${id}&type=3`
      break
    case '声光报警器':
      url = `${URL.getDeviceAndStorageInfo}?id=${id}&type=4`
      break
    case '自来水厂':
      url = `${URL.getWaterWorkInfo}${id}`
      break

    default:
      break
  }

  if (!url) return Promise.reject(false)

  return request({
    method: "get",
    url,
  })
}

const getPanoramicCameraList = async() => request({method: "get",url: URL.panoramicCameraList})

const getPanoramicCameraLive = async(channelId) => request({method: "get",url: `${URL.panoramicCameraLive}${channelId}`})

export {
  emergencyStat,
  taskRecord,
  getTaskDetail,
  getMapMarkerData,
  storagePage,
  expertPage,
  getMapDialogInfo,
  getPanoramicCameraList,
  getPanoramicCameraLive
}

