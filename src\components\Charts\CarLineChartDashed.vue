<template>
  <div :id="id" :style="{ height: height, width: width }" />
</template>

<script lang="ts">
import * as echarts from 'echarts'
import { Component, Prop, Watch } from "vue-property-decorator";
import { mixins } from "vue-class-component";
import ResizeMixin from "./mixins/resize";
interface AirData {
  bottomList: string[];
  dataList: string[];
  unit: string;
  name?: string;
  colorType?: string;
  warnValue?: number;
  miniValue?: number;
  maxValue?: number;
}
@Component({
  name: "CarLineChartDashed"
})
export default class extends mixins(ResizeMixin) {
  // @Prop({ default: "chart" }) private className!: string;
  @Prop({ default: "chart" }) private id!: string;
  @Prop({ default: "200px" }) private width!: string;
  @Prop({ default: "200px" }) private height!: string;
  @Prop({ required: true }) private propData!: AirData;
  @Prop({ required: false, default: "rgba(14,156,255,1)" })
  private bgColor!: string;
  @Prop({ required: false, default: false }) private smooth!: boolean;
  @Prop({ required: false, default: true }) private bgColorState!: boolean;
  @Watch("propData", { immediate: true, deep: true }) public onMsgChanged(
    newValue: AirData,
    oldValue: AirData
  ) {
    if (this.propData) {
      this.$nextTick(() => {
        this.initChart();
      });
    }
  }
  mounted() {
    if (this.propData) {
      this.initChart();
    }
  }
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.clear();
    this.chart.dispose();
    this.chart = null;
  }

  private initChart() {

    if(this.chart === null || this.chart === undefined) {
      this.chart = echarts.init(
        document.getElementById(this.id) as HTMLDivElement
      );
    }
    let option = {
      backgroundColor: "transparent",
      grid: {
        height: "80%",
        bottom: 20,
        // right: 0,
        top: 35,
        left: "left",
        containLabel: true
      },
      xAxis: {
        type: "category",
        nameTextStyle: {
          color: "#fff"
        },
        data: this.propData.bottomList,
        axisLabel: {
          interval: 0,
          rotate: 50,
          textStyle: {
            fontSize: 12,
            color: "white"
          }
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        }
      },
      yAxis: {
        type: "value",
        name: this.propData.unit,
        nameTextStyle: {
          color: "#fff"
          // align: "center"
        },
        axisLabel: {
          textStyle: {
            fontSize: 12,
            color: "white"
          }
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        splitLine: {
          lineStyle: {
            // type: "dashed"
            color: "RGBA(2, 39, 75, 1)"
          }
        }
      },
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "cross",
          label: {
            backgroundColor: "#6a7985"
          }
        }
      },
      series: [
        {
          // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
          // @ts-ignore
          name: "数值",
          data: this.propData.dataList,
          type: "bar",
          barWidth: "25%",
          itemStyle: {
            normal: {
              barBorderRadius: [10, 10, 0, 0],
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: "rgba(0, 157, 255, 1)"
                },
                {
                  offset: 1,
                  color: "rgba(2, 105, 255, 1)"
                }
              ]),
              label: {
                show: true, //开启显示
                position: "top", //在上方显示
                textStyle: {
                  //数值样式
                  color: "#fff",
                  fontSize: 16
                }
              }
            }
          }
        }
      ]
    }
    if (!this.propData.dataList.length) {
      // @ts-ignore
      option.yAxis.min = 0
      // @ts-ignore
      option.yAxis.max = 100
    }
    this.chart.setOption( option as EChartOption<EChartOption>);
  }
}
</script>
