<template>
  <div class="box bilateral-bg-shadow">
    <!-- 页面标题 S -->
    <div v-show="planName" class="flex items-center page-title-box">
      <div class="icon"></div>
      <div class="inner">
        <div ref="title" class="title">{{ planName }}</div>
      </div>
    </div>
    <!-- 页面标题 E -->

    <!-- 左侧区域 S -->
    <div class="left">
      <div class="card">
        <ResponseStatus
          :start="startTime"
          :level="eventLevel"
          :end="endTime"
        ></ResponseStatus>
      </div>
      <div class="card">
        <EmergencyDetails @detailChange="taskDetailChange"></EmergencyDetails>
      </div>
      <div class="card">
        <EventDetails :detail="eventDetailsCardData"></EventDetails>
      </div>
    </div>
    <!-- 左侧区域 E -->

    <!-- 右侧区域 S -->
    <div class="right">
      <div class="card">
        <PeopleInfo :peopledata="peopleData"></PeopleInfo>
      </div>
      <div class="card">
        <TaskFeedback :taskbackdata="taskBackData"></TaskFeedback>
      </div>
    </div>
    <!-- 右侧区域 S -->

    <!-- 底部区域 S -->
    <div class="bottom">
      <FiveLine :detailData="taskTimeData" @pointclicks="pointClick"></FiveLine>
    </div>
    <!-- 底部区域 E -->

    <!-- 地图区域 S -->
    <EmergencyCommandMap
      :lngLat="lngLat"
      :level="eventLevel"
    ></EmergencyCommandMap>
    <!-- 地图区域 E -->

    <!-- 顶部蒙层S -->
    <div class="top_model"></div>
    <!-- 顶部蒙层E -->
    <!-- 底部蒙层S -->
    <div class="botom_model"></div>
    <!-- 底部蒙层E -->
  </div>
</template>

<script>
import anime from 'animejs/lib/anime.es.js'

import ResponseStatus from './components/responseStatus/index.vue'
import EmergencyDetails from './components/emergencyDetails/index.vue'
import EventDetails from './components/eventDetails/index.vue'
import PeopleInfo from './components/peopleInfo/index.vue'
import TaskFeedback from './components/taskFeedback/index.vue'
import EmergencyCommandMap from './components/map/index.vue'
import FiveLine from './components/fiveLine/index.vue'

import { eventType2NameMap, eventLevel2Text } from './common/data'
import dayjs from 'dayjs'
export default {
  name: 'emergencyCommand',
  components: {
    ResponseStatus,
    EmergencyDetails,
    EventDetails,
    PeopleInfo,
    TaskFeedback,
    EmergencyCommandMap,
    FiveLine
  },
  data() {
    return {
      // 事件详情数据
      eventDetail: {},
      // 经纬度数据
      lngLat: {},
      // 左侧--事件详情-数据
      eventDetailsCardData: {
        title: '',
        address: '',
        eventType: '',
        eventLevel: '',
        eventTime: '',
        content: '',
      },
      // 线谱数据
      taskTimeData: {
        data: [],
        time: [],
      },
      // 反馈数据
      taskBackData: {
        excutor: [],
        data: [],
      },
      // 人员信息
      peopleData: {
        data: [],
      },
      // 事件等级
      eventLevel: 0,
      // 事件开始时间
      startTime: new Date(),
      // 当前结束时间
      endTime: '',
      // 预案名称
      planName: '',
    }
  },
  beforeMount(){
    
  },
  methods: {
    /**
     * 事件详情变化 ==》 后续页面状态更新的入口方法
     */
    taskDetailChange(detail) {
      console.log('事件详情变化', detail)
      Object.assign(this.eventDetail, detail)
      this.changeCommonData(detail)
      this.changeEventDetailsCardData(detail)
      this.getLineData(detail)
    },
    /**
     * 线谱上的点或者部门名点击
     * @param {*} params
     */
    pointClick(par) {
      // console.log(par, '点击反馈改变')
      if (par.taskLogId) {
        //按小时线谱点 点击
        console.log('按小时线谱点 点击')
        this.$set(this.taskBackData, 'data', this.taskBackData.excutor.map((e) => {
          if (e.taskLogId == par.taskLogId) {
            return {
              ...e,
              highlight: true,
            }
          } else {
            return {
              ...e,
              highlight: false,
            }
          }
        }))
        console.log(this.taskBackData.data, 'taskBackData.data')
      } else if (par.departmentName) {
        //按天线谱点 点击
        console.log('按天线谱点 点击')
        this.$set(this.taskBackData, 'data', par.time
          ? this.taskBackData.excutor.map((e) => {
              const date = dayjs(e.completeTime).format('YYYY-MM-DD')
              if (e.departmentName == par.departmentName && date == par.time) {
                return {
                  ...e,
                  highlight: true,
                }
              } else {
                return {
                  ...e,
                  highlight: false,
                }
              }
            })
          : this.taskBackData.excutor)
      } else if (par.name !== undefined) {
        // 部门名称点击
        console.log('部门名称点击')
        this.$set(this.taskBackData, 'data', par.name
          ? this.taskBackData.excutor.map((e) => {
              if (e.departmentName == par.name) {
                return {
                  ...e,
                  highlight: true,
                }
              } else {
                return {
                  ...e,
                  highlight: false,
                }
              }
            })
          : this.taskBackData.excutor)
      }
    },
    /**
     * 线谱及反馈数据获取
     */
    getLineData(detail) {
      const { excutor, taskTimeShaft } = detail
      this.$set(this.taskTimeData, 'data', taskTimeShaft)
      this.taskBackData = {
        ...this.taskBackData,
        data: excutor,
        excutor
      }
      this.$set(this.taskTimeData, 'time', [detail.minTime, detail.maxTime])
      // if (excutor && excutor.length) {
      //   taskTimeData.time = [detail.minTime, detail.maxTime]
      // } else {
      //   taskTimeData.data = []
      //   taskTimeData.time = []
      // }
    },
    /**
     * 改变底部卡片--事件详情的数据
     */
    changeEventDetailsCardData(detail) {
      try {
        const { task, departmentExecutors } = detail
        console.log('task--------225', task)
        this.eventDetailsCardData = {
          ...this.eventDetailsCardData,
          title: task.title,
          address: task.address,
          eventType: eventType2NameMap[task.emergencyEventType],
          eventLevel: eventLevel2Text[task.eventLevel],
          eventTime: task.startTime,
          content: task.content
        }
        console.log(
          'eventDetailsCardData--------------------235',
          this.eventDetailsCardData
        )
        // 人员信息数据
        this.$set(this.peopleData, 'data', departmentExecutors)
        // console.log(peopleDat,'人员信息数据');
      } catch (error) {
        console.log('事件详情数据存在错误', error)
      }
    },
    /**
     * 改变公共数据
     */
    changeCommonData(detail) {
      console.log(detail,'--------248');
      try {
        const { task, maxTime } = detail
        this.eventLevel = task.eventLevel
        this.startTime = task.startTime
        this.endTime = task.completeStatus === 0 ? '' : maxTime
        this.planName = task.planName

        this.lngLat = {lng: task.lng, lat:task.lat}
        // 处理动画
        this.titleAnimate()
      } catch (error) {
        console.log('改变公共数据存在错误', error)
      }
    },
    /**
     * 检查是否进行标题左滑动画
     */
    titleAnimate() {
      const titleDom = this.$refs.title

      anime.remove(titleDom)
      titleDom.style = ''

      this.$nextTick(() => {
        const width = titleDom.offsetWidth
        const scrollWidth = titleDom.scrollWidth

        if (scrollWidth > width) {
          titleDom.innerHTML += `  ${titleDom.innerHTML}`
          anime({
            targets: titleDom,
            translateX: -scrollWidth,
            loop: true,
            duration: this.planName.length * 500,
            easing: 'linear',
          })
        }
      })
    },
  },
}
</script>

<style lang="less" scoped>
.box {
  position: relative;
  width: 100%;
  height: 100%;
  .top_model {
    position: absolute;
    background-image: linear-gradient(
      to bottom,
      rgba(0, 6, 23, 0.9),
      rgba(0, 6, 23, 0.8) 70%,
      rgba(0, 6, 23, 0.05)
    );
    height: 200px;
    width: 100%;
    z-index: 2;
    top: -110px;
  }
  .botom_model {
    position: absolute;
    background-image: linear-gradient(
      to top,
      rgba(0, 6, 23, 0.9),
      rgba(0, 6, 23, 0.8) 70%,
      rgba(0, 6, 23, 0.05)
    );
    height: 300px;
    width: 100%;
    z-index: 7;
    bottom: 0px;
    pointer-events: none;
  }
}

.left,
.right {
  position: absolute;
  top: 0;
  z-index: 9;
  height: 100%;
  overflow: auto;
  &::-webkit-scrollbar-track {
    border-radius: 10px;
    background: rgba(57, 177, 255, 0);
  }
  &::-webkit-scrollbar-thumb {
    border-radius: 10px;
    width: 1px;
    background: rgba(21, 62, 105, 0);
  }
}
.left {
  left: 0;
  padding: 20px 0 0 30px;
  background-image: linear-gradient(
    to right,
    rgba(0, 6, 23, 0.9),
    rgba(0, 6, 23, 0.8) 70%,
    rgba(0, 6, 23, 0.05)
  );
}
.right {
  right: 0;
  padding: 20px 30px 0 0;
  background-image: linear-gradient(
    to left,
    rgba(0, 6, 23, 0.9),
    rgba(0, 6, 23, 0.8) 70%,
    rgba(0, 6, 23, 0.05)
  );
}
.bottom {
  width: calc(100% - 52px);
  position: absolute;
  bottom: 0;
  z-index: 9;
  left: -12px;
}

.card {
  width: 448px;
  margin-bottom: 30px;
  &:last-child {
    margin-bottom: 0;
  }
}
.flex{
  display: flex;
}
.items-center {
  align-items: center;
}
.page-title-box {
  position: fixed;
  z-index: 9;
  top: 125px;
  width: 714px;
  left: 50%;
  margin-left: -357px;
  height: 66px;
  background: url('../../assets/images/<EMAIL>') no-repeat center center;
  background-size: 100% 100%;

  .icon {
    width: 127px;
    height: 29px;
    background: url('../../assets/images/<EMAIL>') no-repeat center
      center;
    background-size: 100% 100%;
    margin-right: 30px;
  }

  .inner {
    flex: 1;
    max-width: 100%;
    overflow: hidden;
  }
  .title {
    font-size: 22px;
    color: #fcebeb;
    white-space: nowrap;
    position: relative;
  }
}
</style>
