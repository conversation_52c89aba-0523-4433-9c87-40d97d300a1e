<template>
  <div :id="id" :style="{ height: height, width: width }" />
</template>

<script lang="ts">
import * as echarts from 'echarts'
import { Component, Prop, Watch } from "vue-property-decorator";
import { mixins } from "vue-class-component";
import ResizeMixin from "./mixins/resize";
interface AirData {
  bottomList: string[];
  dataList: string[];
  unit: string;
  name?: string;
  colorType?: string;
}
@Component({
  name: "LineChartCylinder"
})
export default class extends mixins(ResizeMixin) {
  @Prop({ default: "chart" }) private id!: string;
  @Prop({ default: "200px" }) private width!: string;
  @Prop({ default: "200px" }) private height!: string;
  @Prop({ default: null }) private propData!: AirData;
  @Prop({ default: null }) private lineColor!: string;
  // private chart: any = null;
  private option: any = {};
  // @Watch("propData", { immediate: true, deep: true }) public onMsgChanged(
  //   newValue: AirData,
  //   oldValue: AirData
  // ) {
  //   /* eslint-disable @typescript-eslint/ban-ts-ignore */
  //   //@ts-ignore
  //   this.chart.clear();
  //   this.getOption();
  //   /* eslint-disable @typescript-eslint/ban-ts-ignore */
  //   //@ts-ignore
  //   this.chart.setOption(this.option as EChartOption<EChartOption>);
  // }
  mounted() {
    this.initChart();
  }
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.clear();
    this.chart.dispose();
    this.chart = null;
  }
  private initChart() {

    if(this.chart === null || this.chart === undefined) {
      this.chart = echarts.init(
        document.getElementById(this.id) as HTMLDivElement
      );
    }
    this.getOption();
    this.chart.setOption(this.option as EChartOption<EChartOption>);
  }
  private getOption(): void {
    this.option = {
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "shadow"
        }
      },
      grid: {
        top: "15%",
        right: "0",
        left: "10%",
        bottom: "15%"
      },
      xAxis: [
        {
          type: "category",
          data: ["PM₁₀", "PM₂.₅", "SO₂", "NO₂", "O₃", "CO"],
          axisLine: {
            lineStyle: {
              color: "rgba(255,255,255,0.12)"
            }
          },
          axisLabel: {
            margin: 10,
            color: "#0E8BFF",
            textStyle: {
              fontSize: 16
            }
          }
        }
      ],
      yAxis: [
        {
          axisLabel: {
            formatter: "{value}",
            color: "#CCCCCC"
          },
          nameTextStyle: {
            color: "#CCCCCC",
            align: "right"
          },
          nameLocation: "end",
          name: "ug/m³",
          axisLine: {
            show: false
          },
          splitLine: {
            lineStyle: {
              color: "rgba(255,255,255,0.12)"
            }
          }
        }
      ],
      series: [
        {
          type: "bar",
          data: [300, 450, 770, 203, 255, 188],
          barWidth: "12px",
          itemStyle: {
            normal: {
              color: new echarts.graphic.LinearGradient(
                0,
                0,
                0,
                1,
                [
                  {
                    offset: 0,
                    color: "rgba(0,244,255,1)" // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: "rgba(0,77,167,1)" // 100% 处的颜色
                  }
                ],
                false
              ),
              barBorderRadius: [30, 30, 30, 30],
              shadowColor: "rgba(0,160,221,1)",
              shadowBlur: 4
            }
          }
        }
      ]
    };
  }
}
</script>
