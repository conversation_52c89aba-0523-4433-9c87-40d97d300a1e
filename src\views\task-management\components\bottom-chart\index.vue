<template>
  <div style="margin-bottom:60px">
    <real-time-monitor
       v-if="pollutNameList.length"
      :id="'realTimeMonitor' + new Date().getTime()"
      :width="'100%'"
      :height="'1.75rem'"
      :propData="realTimeData"
      :smooth="true"
    />
    <div v-if="pollutNameList.length" class="button-list">
      <span
        v-for="(item, index) in pollutNameList"
        :key="index"
        :class="{ active: activeIndex == index ? 'active' : '' }"
        @click="handleClick(index)"
        >{{ item }}</span
      >
    </div>
  </div>
</template>

<script>
// import LineChartDashed from './chart.vue'
//  :style="{ 'flex-grow': item.length > 4 ? 2 : 1 }"
import realTimeMonitor from './realTimeMonitors'
import { Radio } from 'ant-design-vue'
export default {
  name: '',
  props: {
    pollutNameList: {
      type: Array,
      default: () => { return [] }
    },
    pollutList: {
      type: Array,
      default: () => { return []}
    }
  },
  data() {
    return {
        realTimeData:[],
        airType: null,
        airTypes:[],
        airData: {
    "bottomList": [
        "20时",
        "21时",
        "22时",
        "23时",
        "0时",
        "1时",
        "2时",
        "3时",
        "4时",
        "5时",
        "6时",
        "7时",
        "8时",
        "9时",
        "10时",
        "11时",
        "12时",
        "13时",
        "14时",
        "15时",
        "16时",
        "17时",
        "18时"
    ],
    "dataList": [
        16,
        22,
        24,
        38,
        34,
        20,
        20,
        21,
        18,
        14,
        12,
        19,
        27,
        24,
        15,
        21,
        19,
        15,
        12,
        12,
        12,
        10,
        11
    ],
    "unit": "μg/m³",
    "colorType": "NO2"
}
    }
  },
  created() {
    
  },
  mounted(){
    if(this.pollutList.length){
      this.realTimeData = this.pollutList[0]
      this.activeIndex = 0
    }
  },
  methods: {
    airTypeChange() {},
    handleClick(index) {
      this.activeIndex = index
      this.realTimeData = this.pollutList[index]
    },
  },
  computed: {},
  watch: {
    pollutList:{
      handler(newVal, oldVal){
        this.realTimeData = this.pollutList[0]
        this.activeIndex = 0
      },
      deep: true,
      immediate: true
    }
  },
  components: {
    realTimeMonitor,
    // LineChartDashed,
    ARadioGroup: Radio.Group,
  }
}
</script>

<style lang="less" scoped>
.common-content > div {
  font-size: 0.16rem;
  text-align: center;
}
.type-radio {
  .water-monitor-tab {
    height: 0.24rem;
    font-size: 0.12rem;
    color: rgba(255, 255, 255, 1);
    background: rgba(12, 39, 92, 1);
    border-radius: 0;
    padding: 0 0.1rem;
    border: none;
  }
  .ant-radio-group-solid
    .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
    background: #0084ff;
    border: none;
  }
  .ant-radio-button-wrapper:not(:first-child)::before {
    background: transparent;
  }
  .ant-radio-button-wrapper-checked::before {
    background: transparent !important;
  }
  .ant-radio-button-wrapper-checked {
    z-index: 1;
    border-color: #0084ff !important;
    -webkit-box-shadow: -1px 0 0 0 #0084ff;
    box-shadow: -1px 0 0 0 #0084ff;
  }
  .ant-radio-group-small .ant-radio-button-wrapper {
    // width: 14.28%;
    width: 16.66%;
    text-align: center;
  }
  .ant-radio-group {
    display: flex;
  }
  .ant-radio-group-small .ant-radio-button-wrapper {
    padding: 0;
  }
}
.common-title {
  position: relative;
}
.button-list {
  width: 100%;
  height: 0.24rem;
  background-color: #0f245e;
  margin: 0.28rem 0;
  /*padding: 0 0.1rem;*/
  display: flex;
  align-items: center;
  box-sizing: border-box;
  color: white;
  line-height: 0.24rem;
  font-size: 0.12rem;
  span {
    flex: 1;
    padding: 0 0.05rem;
    cursor: pointer;
    display: inline-block;
    height: 100%;
    text-align: center;
  }
  .active {
    background-color: #0084ff;
  }
}
</style>
