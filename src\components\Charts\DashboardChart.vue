<template>
  <div
    :id="id"
    :class="className"
    :style="{ height: height, width: width }"
  ></div>
</template>

<script lang="ts">
import * as echarts from 'echarts'
import { Component, Prop } from "vue-property-decorator";
import { mixins } from "vue-class-component";
import ResizeMixin from "./mixins/resize";
interface TaskData {
  type: number;
  showNumber: number;
  name: string;
  number: number;
}
@Component({
  name: "DashboardChart"
})
export default class extends mixins(ResizeMixin) {
  @Prop({ default: "chart" }) private className!: string;
  @Prop({ default: "chart" }) private id!: string;
  @Prop({ default: "200px" }) private width!: string;
  @Prop({ default: "200px" }) private height!: string;
  @Prop({ required: true }) private propData!: TaskData;

  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
  }

  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.clear();
    this.chart.dispose();
    this.chart = null;
  }

  private initChart() {

    if(this.chart === null || this.chart === undefined) {
      this.chart = echarts.init(
        document.getElementById(this.id) as HTMLDivElement
      );
    }
    this.chart.setOption({
      backgroundColor: "rgba(0,0,0,0)",
      series: [
        {
          name: "刻度",
          type: "gauge",
          radius: "85%",
          min: 0, //最小刻度
          max: 16, //最大刻度
          splitNumber: 8, //刻度数量
          startAngle: 225,
          endAngle: -45,
          axisLine: {
            show: true,
            lineStyle: {
              width: 1,
              color: [[1, "rgba(0,0,0,0)"]]
            }
          }, //仪表盘轴线
          axisLabel: {
            show: false,
            color: "#4d5bd1",
            distance: 25
          }, //刻度标签。
          axisTick: {
            show: false,
            splitNumber: 7,
            lineStyle: {
              // color: '#fff',
              width: 1
            },
            length: -8
          }, //刻度样式
          splitLine: {
            show: false,
            length: -20,
            lineStyle: {
              color: "#fff"
            }
          }, //分隔线样式
          detail: {
            show: false
          },
          pointer: {
            show: false
          }
        },
        {
          type: "gauge",
          radius: "100%", // 控制半径大小
          center: ["50%", "50%"],
          splitNumber: 0, //刻度数量
          startAngle: 210,
          endAngle: -35,
          axisLine: {
            show: true,
            lineStyle: {
              width: 20,
              color: [
                [
                  this.propData.showNumber,
                  new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                    {
                      offset: 0,
                      color: this.propData.type === 1 ? "#2783CD" : "#FE8B1A"
                    },
                    {
                      offset: 1,
                      color: this.propData.type === 1 ? "#0EFCFF" : "#FED501"
                    }
                  ])
                ],
                [1, "#14407E"]
              ]
            }
          },
          itemStyle: {
            normal: {
              borderWidth: 5
            }
          },
          //分隔线样式。
          splitLine: {
            show: false
          },
          axisLabel: {
            show: false
          },
          axisTick: {
            show: false
          },
          pointer: {
            show: false
          },
          title: {
            // 汉字描述
            show: true,
            offsetCenter: [0, "50%"], // x, y，单位px
            // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
            // @ts-ignore
            textStyle: {
              color: "#fff",
              fontSize: 12
            }
          },
          detail: {
            // 详情数值
            show: true,
            offsetCenter: [0, "-5%"],
            color: "#fff",
            formatter: (params: any) => {
              return `${params}${this.propData.type === 1 ? "次" : "KM"}`;
            },
            textStyle: {
              fontSize: 24
            }
          },
          data: [
            {
              name: this.propData.name,
              value: this.propData.number
            }
          ]
        }
      ],
      animationEasing: "elasticOut",
      animationEasingUpdate: "elasticOut",
      animationDelay(idx: number) {
        return idx * 20;
      },
      animationDelayUpdate(idx: number) {
        return idx * 20;
      }
    } as EChartOption);
  }
}
</script>
