<template>
  <div class="people-area">
    <peopleRow :list="title" rowTitle="row-title" :height="40"></peopleRow>

    <!-- <swiper
      :mousewheel="true"
      :autoplay="swiper_options.autoplay"
      :loop="swiper_options.loop"
      :speed="swiper_options.speed"
      :direction="swiper_options.direction"
      :slidesPerView="swiper_options.slidesPerView"
      :spaceBetween="swiper_options.spaceBetween"
      style="height: 252px"
    >
      <swiper-slide
        v-for="(item, index) in data1"
        :key="index"
        style="height: 36px"
      >
        <peopleRow :list="item" :index="index" />
      </swiper-slide>
    </swiper> -->
    <div class="experBox">
      <peopleRow 
        v-for="(item, index) in data1"
        :key="index"
        :list="item"
        :index="index"
      />
    </div>
  </div>
</template>
<script>

import peopleRow from './peopleRow.vue'
export default {
  name: 'expertList',
  props: {
    listData: {
      type: Array,
      default: () => [],
    },
  },
  components: {
    peopleRow,
  },
  data() {
    return {
      title: {
        // departmentName: '专家类别',
        name: '姓名',
        tel: '电话',
        state: '专家类别',
      },
      swiper_options: {
        autoplay: {
          delay: 3000,
          disableOnInteraction: false,
        },
        loop: false,
        slidesPerView: 7,
        spaceBetween: 0,
        direction: 'vertical',
      },
      data1: [],
    }
  },
  watch: {
    listData: {
      handler(nval, oval) {
        // console.log(nval, '专家列表')
        if (nval) {
          this.data1 = nval.map((e) => {
            return {
              // departmentName: e.type||'--',
              name: e.name,
              tel: e.phone,
              state: e.type || '--',
            }
          })
        } else {
          this.data1 = []
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {},
}
</script>
<style lang="less" scoped>
.experBox{
  height: 252px;
  overflow-y: auto;
  &::-webkit-scrollbar-track {
    border-radius: 10px;
    background: rgba(57, 177, 255, 0);
  }
  &::-webkit-scrollbar-thumb {
    border-radius: 10px;
    width: 1px;
    background: rgba(21, 62, 105, 0.5);
  }
}
</style>