<template>
  <div class="flex items-center dialog-info-item" v-show="name.length">
    <div class="name">{{ name || '—' }}：</div>
    <div class="value">
      <span v-if="warnValue" class="warn">{{ warnValue }}</span>
      <slot v-else>{{ value || '—' }}</slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'infoItem',
  props: {
    name: {
      type: String,
      default: '',
    },
    value: {
      type: String,
      default: '',
    },
    warnValue: {
      type: String | Number,
      default: '',
    },
  },
}
</script>

<style lang="less" scoped>
.dialog-info-item {
  font-size: 14px;
  color: #dcf0ff;
  white-space: normal;
  align-items: baseline;

  .name {
    color: #56a7f1;
    white-space: nowrap;
  }

  .warn {
    font-size: 14px;
    color: #ef4a40;
  }
}
</style>
