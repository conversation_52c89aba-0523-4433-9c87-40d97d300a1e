<style lang="less">
  .flowchart {
    canvas {
      outline: none!important;
    }
  }
</style>

<style lang="less" scoped>

</style>

<template>
  <section>
    <div
      :id="flowChartId"
      :style="{width: flowChartWidth + 'px', height: flowChartHeight + 'px', backgroundColor: '#fff'}"
      class="flowchart"
    />
  </section>
</template>

<script>
const go = require('gojs')

const MAKE = go.GraphObject.make
export default {
  name: 'FlowChart',
  filters: {},
  components: {},
  mixins: [],
  props: {
    flowChartId: {
      type: String,
      require: true,
      default: 'init_flow_chart_id'
    },
    flowChartData: {
      type: Object,
      require: true,
      default: () => {}
    },
    flowChartWidth: {
      type: Number,
      require: true,
      default: 800
    },
    flowChartHeight: {
      type: Number,
      require: true,
      default: 200
    }
  },
  data() {
    return {
      myDiagram: null,
      modelDataCopy: null
    }
  },
  computed: {
    modelData: {
      get() {
        return this.flowChartData
      },
      set(value) {
        this.modelDataCopy = value
      }
    }
  },
  watch: {
    modelData: {
      handler(oldValue, newValue) {
        if (newValue) {
          this.load()
        }
      },
      deep: false,
      immediate: true
    }
  },
  mounted() {
    const that = this
    this.myDiagram = MAKE(go.Diagram, this.flowChartId, // must be the ID or reference to div
      {
        initialContentAlignment: go.Spot.Center, // 设置整个图表在容器中的位置 https://gojs.net/latest/api/symbols/Spot.html
        allowZoom: true,
        'toolManager.mouseWheelBehavior': go.ToolManager.WheelZoom, // 启动滚轮缩放
        maxSelectionCount: 1, // users can select only one part at a time
        validCycle: go.Diagram.CycleDestinationTree, // make sure users can only create trees
        isEnabled: true, // 是否可拖拽，默认为是
        allowLink: false,
        allowMove: false,
        allowRelink: false,
        layout:
        MAKE(go.TreeLayout,
          {
            treeStyle: go.TreeLayout.StyleLastParents,
            arrangement: go.TreeLayout.ArrangementVertical,
            // properties for most of the tree:
            angle: 90,
            layerSpacing: 50,
            nodeSpacing: 50,
            // properties for the "last parents":
            alternateAngle: 90,
            alternateLayerSpacing: 50,
            alternateAlignment: go.TreeLayout.AlignmentBus,
            alternateNodeSpacing: 50
          }),
        'undoManager.isEnabled': false // enable undo & redo
      })

    // add myDiagram event
    this.myDiagram.addDiagramListener('ObjectSingleClicked', (e) => {
      that.$emit('node-click', { node: e.subject.part.data })
    })
    this.myDiagram.addDiagramListener('TreeCollapsed', (e) => {
      that.$emit('tree-collapsed', { node: e.As._dataArray[0].data })
    })
    this.myDiagram.addDiagramListener('TreeExpanded', (e) => {
      that.$emit('tree-expanded', { node: e.As._dataArray[0].data })
    })

    // var levelColors = ['#AC193D', '#2672EC', '#8C0095', '#5133AB',
    //   '#008299', '#D24726', '#008A00', '#094AB2']

    // override TreeLayout.commitNodes to also modify the background brush based on the tree depth level
    this.myDiagram.layout.commitNodes = function() {
      go.TreeLayout.prototype.commitNodes.call(that.myDiagram.layout) // do the standard behavior
      // then go through all of the vertexes and set their corresponding node's Shape.fill
      // to a brush dependent on the TreeVertex.level value
      // that.myDiagram.layout.network.vertexes.each(function(v) {
      //   if (v.node) {
      //     var level = v.level % (levelColors.length)
      //     var color = '#e0e6f1' // levelColors[level]
      //     var shape = v.node.findObject('SHAPE')
      //     if (shape) shape.fill = MAKE(go.Brush, 'Linear', { 0: color, 1: go.Brush.lightenBy(color, 0.05), start: go.Spot.Left, end: go.Spot.Right })
      //   }
      // })
    }
    // define the Node template
    this.myDiagram.nodeTemplate =
        MAKE(go.Node, 'Auto',
          // define the node's outer shape
          MAKE(go.Shape, 'RoundedRectangle',
            {
              name: 'SHAPE',
              stroke: null,
              // set the port properties:
              portId: '',
              fromLinkable: true,
              toLinkable: true,
              cursor: 'pointer'
            }, new go.Binding('fill', 'data', (data) => {
              if (data.taskParentId) {
                return '#85abe8'
              } if (data.type === '2') {
                return '#9cd8e2'
              } if (data.type === '1') {
                return '#aedc96'
              }
              return '#ef6d6d'
            }).ofObject()),
          MAKE(go.Panel, 'Horizontal',
            MAKE(go.Panel, 'Table',
              {
                maxSize: new go.Size(150, 999),
                margin: new go.Margin(0, 0, 0, 3),
                defaultAlignment: go.Spot.Left
              },
              MAKE(go.RowColumnDefinition, { column: 10, width: 2 }),
              MAKE(go.TextBlock, this.labelTextStyle(),
                {
                  row: 0,
                  column: 1,
                  editable: false,
                  isMultiline: true,
                  minSize: new go.Size(10, 12),
                  margin: new go.Margin(0, 0, 0, 3)
                },
                new go.Binding('text', 'data', (data) => {
                  if (data.taskParentId) {
                    return '指\r\n派\r\n人'
                  } if (data.type === '2') {
                    return '抄\r\n送\r\n人'
                  } if (data.type === '1') {
                    return '执\r\n行\r\n人'
                  }
                  return '发\r\n起\r\n人'
                }).ofObject(),
                new go.Binding('stroke', 'data', (data) => {
                  if (data.taskParentId) {
                    return '#fff' // '#9245e4'
                  } if (data.type === '2') {
                    return '#fff' // '#13ce66'
                  } if (data.type === '1') {
                    return '#fff' // '#3378f4'
                  }
                  return 'black'
                }).ofObject())),
            MAKE(go.Panel, 'Table',
              { isClipping: true, scale: 2, margin: new go.Margin(6, 10, 6, 3) },
              MAKE(go.Shape, 'Circle', { width: 24, strokeWidth: 0 }),
              MAKE(go.Picture, {
                name: 'Picture',
                desiredSize: new go.Size(24, 24)
              },
              new go.Binding('source', 'avatar', (avatar) => that.transforPicture(avatar)))),
            // define the panel where the text will appear
            MAKE(go.Panel, 'Table',
              {
                maxSize: new go.Size(200, 999),
                margin: new go.Margin(6, 10, 0, 3),
                defaultAlignment: go.Spot.Left
              },
              MAKE(go.RowColumnDefinition, { column: 2, width: 8 }),
              MAKE(go.TextBlock, this.textStyle(),
                {
                  row: 0,
                  column: 0,
                  columnSpan: 8,
                  font: '10pt Segoe UI,sans-serif',
                  editable: false,
                  isMultiline: false,
                  minSize: new go.Size(10, 14),
                  margin: new go.Margin(0, 0, 0, 3)
                },
                new go.Binding('text', 'name')),
              MAKE(go.TextBlock, this.textStyle(),
                {
                  row: 1,
                  column: 0,
                  columnSpan: 8,
                  font: '12pt Segoe UI,sans-serif',
                  editable: false,
                  isMultiline: false,
                  minSize: new go.Size(10, 16),
                  margin: new go.Margin(0, 0, 0, 3)
                },
                new go.Binding('text', 'title')),), // end Table Panel
            MAKE('TreeExpanderButton'))) // end Node

    // the context menu allows users to make a position vacant,
    // remove a role and reassign the subtree, or remove a department

    // define the Link template
    this.myDiagram.linkTemplate =
        MAKE(go.Link, go.Link.Orthogonal,
          { corner: 5, relinkableFrom: true, relinkableTo: true },
          MAKE(go.Shape, { strokeWidth: 3 }, new go.Binding('stroke', 'toNode', (n) =>
            // if (n.data.taskParentId) {
            //   return '#9245e4'
            // } else if (n.data.type === '2') {
            //   return '#13ce66'
            // } else if (n.data.type === '1') {
            //   return '#3378f4'
            // }
            '#c0dac0').ofObject())
          // MAKE(go.Shape, { toArrow: 'Standard', fill: '#3378f4', strokeWidth: 3 }, new go.Binding('stroke', 'toNode', function(n) {
          // if (n.data.taskParentId) {
          //   return '#9245e4'
          // } else if (n.data.type === '2') {
          //   return '#13ce66'
          // } else if (n.data.type === '1') {
          //   return '#3378f4'
          // }
          // return '#eee'
          // }).ofObject())
          // MAKE(go.Panel, 'Auto',
          //   MAKE(go.Shape, 'RoundedRectangle', new go.Binding('fill', 'toNode', function(n) {
          //     if (n.data.parent) return 'white'
          //     return 'transparent'
          //   }).ofObject(), new go.Binding('stroke', 'toNode', function(n) {
          //     if (n.data.parent) return 'white'
          //     return 'transparent'
          //   }).ofObject()),
          //   MAKE(go.TextBlock, { margin: 3 },
          //     new go.Binding('text', 'toNode', function(n) {
          //       if (n.data.taskParentId) {
          //         return '指派人'
          //       } else if (n.data.type === '2') {
          //         return '抄送人'
          //       } else if (n.data.type === '1') {
          //         return '执行人'
          //       } else {
          //         return '发起人'
          //       }
          //     }).ofObject()),MAKE('TreeExpanderButton'))
        ) // the link shape

    // read in the JSON-format data from the "mySavedModel" element
    this.load()
  },
  methods: {
    // Show the diagram's model in JSON format
    save() {
      this.modelData = this.myDiagram.model.toJson()
      this.myDiagram.isModified = false
    },
    load() {
      this.myDiagram.model = go.Model.fromJson(this.modelData)
      // make sure new data keys are unique positive integers
      let lastkey = 1
      this.myDiagram.model.makeUniqueKeyFunction = function(model, data) {
        let k = data.key || lastkey
        while (model.findNodeDataForKey(k)) k++
        data.key = lastkey = k
        return k
      }
    },
    // this is used to determine feedback during drags
    mayWorkFor(node1, node2) {
      if (!(node1 instanceof go.Node)) return false // must be a Node
      if (node1 === node2) return false // cannot work for yourself
      if (node2.isInTreeOf(node1)) return false // cannot work for someone who works for you
      return true
    },
    // This function provides a common style for most of the TextBlocks.
    // Some of these values may be overridden in a particular TextBlock.
    textStyle() {
      return { font: '10pt  Segoe UI,sans-serif', stroke: 'black' }
    },
    labelTextStyle() {
      return { font: '10pt  Segoe UI,sans-serif', stroke: 'white' }
    },
    // This converter is used by the Picture.
    transforPicture(avatar) {
      if (avatar) {
        return avatar
      }
      return 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'
    }
  }
}
</script>
