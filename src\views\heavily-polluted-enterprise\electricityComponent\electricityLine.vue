<style lang="less" scoped>
.no-data {
  font-size: 0.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
<template>
  <div
    v-if="propData.dataList&&propData.dataList.length > 0"
    :id="id"
    :style="{ height: height, width: width }"
  />
  <div v-else class="no-data" :style="{ height: height, width: width }">
    暂无数据
  </div>
</template>

<script lang="ts">
import * as echarts from 'echarts'
import { Component, Prop, Watch } from 'vue-property-decorator'
import { mixins } from 'vue-class-component'
import ResizeMixin from '@/components/Charts/mixins/resize'
import moment from 'moment'
interface AirData {
  bottomList: string[]
  dataList: string[]
  standard: number | string
  max: number | string
  unit: string
  name: string
}
@Component({
  name: 'LineChartDashed'
})
export default class extends mixins(ResizeMixin) {
  // @Prop({ default: "chart" }) private className!: string;
  @Prop({ default: 'chart' }) private id!: string
  @Prop({ default: '200px' }) private width!: string
  @Prop({ default: '200px' }) private height!: string
  @Prop({ required: true }) private propData!: AirData
  @Prop({ required: false, default: 'rgba(14,156,255,1)' })
  private bgColor!: string
  @Prop({ required: false, default: false }) private smooth!: boolean
  @Prop({ required: false, default: true }) private bgColorState!: boolean
  @Prop({ required: false, default: '' }) private xText!: string
  @Watch('propData', { immediate: true, deep: true }) public onMsgChanged(
    newValue: AirData,
    oldValue: AirData
  ) {
    if (this.propData) {
      if (this.chart) {
        this.chart.clear()
        this.chart.dispose()
        this.chart = null
      }
      this.$nextTick(() => {
        this.initChart()
      })
    }
  }
  mounted() {
    if (this.propData) {
      this.initChart()
    }
  }
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.clear()
    this.chart.dispose()
    this.chart = null
  }

  private initChart() {
    if (this.chart === null || this.chart === undefined) {
      const chartDom = document.getElementById(this.id) as HTMLDivElement
      if (!chartDom) return
      this.chart = echarts.init(chartDom)
    }
    // SO2:1 NO2:1 O3:1 CO:1 PM10:1 PM:2.5
    this.chart.setOption({
      backgroundColor: 'transparent',
      color:['#1DCCFF','#15FEFE'],
      // legend: {
      //   right: 30,
      //   textStyle: {
      //     color: '#7BB7ED',
      //     fontSize: 12
      //   },
      //   itemHeight: 12
      // },
      grid: {
        height: '80%',
        left: '0%',
        right:'0%',
        top: '20%',
        bottom: '5%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        name: '',
        nameTextStyle: {
          color: '#fff',
          lineHeight: -30,
          align: 'center',
          verticalAlign: 'bottom'
        },
        data: this.propData.bottomList.map((v:any)=>v.x),
        axisLabel: {
          textStyle: {
            fontSize: 12,
            color: 'white'
          }
        },
        axisLine: {
          show: false,
          lineStyle:{
            type: 'dotted'
          }
        },
        axisTick: {
          show: false
        }
      },
      yAxis: {
        type: 'value',
        name: '    单位：' + this.propData.unit,
        min: 0,
        // max: this.propData.max,
        nameTextStyle: {
          color: '#fff',
          shadowOffsetX: 50
          // align: "center"
        },
        axisLabel: {
          textStyle: {
            fontSize: 12,
            color: 'white'
          }
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        splitLine: {
          lineStyle: {
            // type: "dashed"
            color: 'RGBA(2, 39, 75, 1)'
          }
        }
      },
      tooltip: {
        trigger: 'axis',
        confine: true,
        axisPointer: {
          type: 'cross',
          label: {
            show:false,
            backgroundColor: '#6a7985'
          }
        },
        formatter:(params)=>{
          const time:string = this.propData.bottomList[params[0].dataIndex]
          const year = new Date().getFullYear()
          const curDay = moment().format('YYYY-MM-DD')
          const dayData = this.propData.dataList[params[0].dataIndex]
          const showTime = time['x'].includes('-')?time['fullDate']:year+'-'+time['fullDate']
          let str = `
            <div style="border-radius: 0.06rem;padding: 0.1rem 0.13rem; background-color: rgba(11, 11, 11, .4);color: #fff;">
              <div style="font-size: 0.14rem;"><i class="el-icon-time"></i>  监测时间:  ${showTime}</div>
              <div style="font-size: 0.14rem;margin-top: 0.12rem;">
                <i class="el-icon-stopwatch"></i>  ${time['x'].includes('-')?'当天':'小时'}电量： <span>${dayData} kwh</span>
              </div>
            </div>
          `
          return str
        }
      },
      series: [
        // {
        //   name: '告警阈值',
        //   type: 'line',
        //   markLine: {
        //     symbol: 'none',
        //     label: {
        //       show: true,
        //       // formatter:()=>{
        //       //   return '告警阈值'
        //       // },
        //       // distance: [-13,0]
        //     },
        //     data: [
        //       {
        //         silent: false, //鼠标悬停事件  true没有，false有
        //         lineStyle: {
        //           //警戒线的样式  ，虚实  颜色
        //           type: 'dotted',
        //           color: 'red'
        //         },
        //         yAxis: this.propData.standard
        //       }
        //     ]
        //   }
        // },
        {
          // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
          // @ts-ignore
          name: '',
          data: this.propData.dataList || [],
          type: 'line',
          smooth: this.smooth,
          lineStyle: {
            color: '#15B4FE' //改变折线颜色
          },
          symbol: 'circle',
          symbolSize: 5,
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: this.bgColorState
                    ? 'RGBA(21,180,254, 0.7)'
                    : 'transparent' // 0% 处的颜色
                },
                {
                  offset: 1,
                  color: 'RGBA(25,252,255, 0)' // 100% 处的颜色
                }
              ],
              global: false
            },
            shadowColor: 'rgba(0,85,250,0)',
            shadowBlur: 20
          },
          itemStyle: {
            color: (params:any) => {
              // return params.value < this.propData.standard ? '#1DCCFF'  : 'red'
              return (params.value <= this.propData.standard||this.propData.standard==null) ? '#1DCCFF'  : 'red'
            }, //改变折线点的颜色 '#1DCCFF'
            borderColor: '#fff'
          }
        },
      ]
    } as EChartOption<EChartOption>)
  }
}
</script>



