<template>
  <div id="depmap" class="container" ref="container"></div>
</template>

<script lang="ts">
//@ts-ignore
// import AMap from 'AMap'
//@ts-ignore
import jinniuStreet from '@/assets/map-geojson/jinniu_street_other'
import { Vue, Component, Prop, Watch } from 'vue-property-decorator'
import { Icon } from 'ant-design-vue'
import { webglcontextlostHandle } from '@/utils/index'
import { airPraCheck, waterPraCheck } from '@/api/practiceCheck'
import moment from 'moment'
import electricityBg from '@/assets/noise/<EMAIL>' // 用电弹窗背景
import noiseClose from '@/assets/noise/<EMAIL>' // 噪声弹窗关闭
import heavyCorruptKong from '@/assets/heavy-corrupt/<EMAIL>' // 重污地图弹窗空图片
import html2canvas from 'html2canvas'
// import cg0 from '@/assets/recheck/cg_0.png'
import cg0 from '@/assets/recheck/sz_0.png'
import cg1 from '@/assets/recheck/cg_1.png'
import cg2 from '@/assets/recheck/cg_2.png'
import cg3 from '@/assets/recheck/cg_3.png'
import cg4 from '@/assets/recheck/cg_4.png'
import cg5 from '@/assets/recheck/cg_5.png'
import cg6 from '@/assets/recheck/cg_6.png'
import randaImg from '@/assets/recheck/dingwei.png'
import waterStationJson from './waterJSON.js'
import districtRiver from '@/assets/jinniu-river4.png'
import AMapLoader from '@amap/amap-jsapi-loader'

import { listStationPollutantMonitorAnalysis } from "@/api/homeTable";

// 地图实例
let maps: any = null
let AMap: any

@Component({
  name: 'DepartmentBgMap',
  components: {
    AIcon: Icon,
  },
})
export default class extends Vue {
  // private maps: any = "";
  @Prop({
    required: false,
    type: Number,
    default: 12.5,
  })
  private mapZoom!: number
  @Prop({ required: false }) mapStyle!: string
  @Prop({ required: false, default: '2D' }) viewMode!: string
  @Prop({ required: false, default: true }) heat!: string
  @Prop({ required: false, default: 300 }) heathMapMax!: string
  @Prop({ required: false, default: null }) curHour!: any
  @Prop({ required: false, default: 'AQI' }) curtypeName!: any
  @Prop({ required: false, default: 'air' }) pagetype!: any
  @Watch('pagetype', { immediate: true })
  private pageTypeChange() {
    this.fitstData = true
    // console.log(0,this.curHour);
    if(!maps)return
    if (this.pagetype == 'air') {
      maps.remove(this.maxPon)
      maps.remove(this.siteMarkerList)
      maps.remove(this.riverLayer)
      maps.remove(this.polylinesList)
      this.creatGeojson()
      this.getAirCheck(0)
    } else {
      this.clearHeatMapLayer()
      maps.remove(this.maxPon)
      maps.remove(this.siteMarkerList)
      maps.remove(this.polygonList)
      this.dynamicRivderLayer()
      this.getAirCheck(0)
    }
  }
  @Prop({ required: false, default: () => ({}) }) maxPoint!: any
  @Watch('maxPoint', { deep: true })
  private maxPointChange(point: any) {
    if (this.maxPon) {
      maps.remove(this.maxPon)
    }
    if (!point||!point.latitude) return
    this.drawRanda(point)
  }
  @Watch('curHour', { deep: true })
  curHourChange(val: any) {
    this.getAirCheck(0)
  }
  @Watch('heatmapAllData', { deep: true })
  heatmapAllDataChange(list: any) {}
  @Prop({ required: false, default: 'aqi' }) curtype!: any
  @Watch('curtype')
  curtypeChange(val: any) {
    this.getAirCheck(0)
  }
  @Prop({ required: false, default: '1' }) timeType!: any
  @Watch('timeType', { deep: true })
  timeTypeChange(val: any) {
    this.getAirCheck(0)
  }
  @Prop({ required: false, default: moment().format('YYYY-MM-DD') })
  selDate!: any
  @Watch('selDate')
  selDateChange(val: any) {
    this.getAirCheck(0)
  }
  // @Prop({ required: false, default: ()=>[] }) heatmapData!: any;
  @Watch('heatmapData', { immediate: false, deep: true })
  heatmapDataChange(list: any) {
    if (list.length && maps) {
      if (this.pagetype == 'air') {
        this.drawHeatMap()
      } else {
        this.drawWaterLine()
      }
    } else {

    }
  }
  @Watch('mapZoom', { immediate: true, deep: false })
  private onmapZoomChange(newValue: number, oldValue: number) {
    if (newValue && maps) {
      maps.setZoom(newValue)
    }
  }

  private heatMapLayer: any = null
  private heatmapData: any = []
  private heatmapAllData: any = []
  private HourTimer: any = null
  private infoWindow: any = null
  private firstPhoto: any = true
  private siteMarkerList: any = []
  private curStationCode: any = ''
  private currItem: any = {}
  private curLevel: any = 1
  private maxPon: any = null
  private polygonList: any = []
  private riverLayer: any = null
  private fitstData: any = true
  private polylinesList: any = []
  created() {
    console.log('--created---data---145');
    (window as any).closeInfoWindowRck = this.closeInfoWindowRck // 解决字符串模板@click无效的问题
    AMapLoader['reset']()
  }
  mounted() {
    // // load 加载
    AMapLoader.load({
      "key": "777fec7ef3cc29281d60ae900fa33925",              // 申请好的Web端开发者Key，首次调用 load 时必填
      "version": "1.4.15",   // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
      "plugins": ['AMap.DistrictSearch','AMap.Heatmap','AMap.ControlBar','AMap.Object3DLayer','Map3D','AMap.Geocoder','AMap.CircleMarker','AMap.MouseTool'],           // 需要使用的的插件列表，如比例尺'AMap.Scale'等
      "AMapUI": {             // 是否加载 AMapUI，缺省不加载
        "version": '1.0',   // AMapUI 版本
        // "plugins":['overlay/SimpleMarker'],       // 需要加载的 AMapUI ui插件
      },
      "Loca":{                // 是否加载 Loca， 缺省不加载
        "version": '1.3.2'  // Loca 版本
      },
    }).then((amaps)=>{
      console.log('data---initMap--------162');
      setTimeout((_) => {
        AMap = amaps
        ;(window as any).AMap = amaps
        this.map()
        maps.on('complete', () => {
          // this.createOverlay();
          this.creatGeojson()
          const waterCenterPosition = new AMap.LngLat(104.066, 30.725)
          maps.setCenter(waterCenterPosition)
          this.getAirCheck(0)
        })
      }, 0)
    }).catch(e => {
      console.log(e);
    })
    // eslint-disable-next-line @typescript-eslint/no-this-alias

  }
  beforeDestroy() {
    console.log('--beforeDestroy---data---182');
    this.closeInfoWindowRck()
    if(!maps)return
    maps.clearMap()

    // 销毁地图
    maps.destroy()
    maps = null
    // 销毁前清空方法 防止内存泄漏
    ;(window as any).closeInfoWindowRck = null
    ;(window as any).AMap = null
    AMap = null
  }
  private getPhoto(flg: any = false) {
    const HTMLElement = document.getElementById('depmap') as HTMLElement
    html2canvas(HTMLElement, {
      useCORS: true, // 如果截图的内容里有图片,可能会有跨域的情况,加上这个参数,解决文件跨域问题
    }).then((canvas) => {
      let dataUrl = canvas.toDataURL('image/png')
      this.$emit('getPhoto', dataUrl, this.curHour, flg)
    })
  }
  private getAirCheck(num: any) {
    let findDate: any = ''
    if (this.timeType == '1') {
      if(this.pagetype == 'water'){
        // if(this.fitstData){
        const findNun = this.curHour + num
        const lessNum = findNun%4
        findDate =
          this.selDate +
          ' ' +
          ((findNun-lessNum) > 9
            ? (findNun-lessNum)
            : '0' + (findNun-lessNum)) +
          ':00:00'
      }else{
        findDate =
          this.selDate +
          ' ' +
          (this.curHour + num > 9
            ? this.curHour + num
            : '0' + (this.curHour + num)) +
          ':00:00'
      }
    } else if (this.timeType == '2') {
      if (moment(this.selDate).daysInMonth() == this.curHour) return
      let curCHour = this.curHour
      if (curCHour + 1 == +moment().format('DD')) {
      } else {
        curCHour += 1
      }
      findDate =
        moment(this.selDate).format('YYYY-MM') +
        '-' +
        (curCHour + num > 9 ? curCHour + num : '0' + (curCHour + num)) +
        ' ' +
        '00:00:00'
    }
    this.$emit("handleChangeAnalysis", {findDate,
      pollutantCode: this.curtype,
      timeType: this.timeType,})
    let DataFn = this.pagetype == 'air' ? listStationPollutantMonitorAnalysis : waterPraCheck
    let pollutantCode = this.curtype
    DataFn({
      findDate,
      pollutantCode,
      timeType: this.timeType,
    }).then((res) => {
      const { data } = res.data
      if (!data) {
        this.clearAll()
        if (!this.fitstData) return
        this.fitstData = false
        if(this.pagetype == 'air'){
          this.$emit('noDataFn', findDate, -1)
        }else{
          // this.$emit('noDataFn', findDate, -4)
        }
        return
      }
      this.heatmapData = data.dataList || []
      this.curStationCode = data.dataList[0].stationCode
      this.createNoiseMarker()
      if(this.pagetype == 'air'){
        this.$emit('handleGetCharacteristicRadarChart', data.characteristicRadarChart || {})
      }
      this.$emit(
        'getRightData',
        data.maxList||[],
        data.maxList?data.dataList[0]:null,
        data.analyse||null,
        data.dataList
      )
    })
  }
  private clearAll() {
    this.heatmapData = []
    if (this.maxPon) {
      maps.remove(this.maxPon)
    }
    if (this.siteMarkerList.length !== 0) {
      maps.remove(this.siteMarkerList)
      this.siteMarkerList = []
    }
    this.closeInfoWindowRck()
    maps.remove(this.polylinesList)
    this.clearHeatMapLayer()
    this.$emit('getRightData', [], null, null, [])
    setTimeout(() => {
      this.getPhoto()
    }, 300);
  }
  // 高德地图
  private map(): void {
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const _this = this
    // 初始化地图
    const map = new AMap.Map(this.$refs.container, {
      center: [104.04, 30.71],
      position: [104.04, 30.71],
      zoom: this.mapZoom,
      viewMode: this.viewMode,
      pitch: 0,
      zoomEnable: true,
      dragEnable: true,
      zooms: [11.5, 18],
    })

    // 处理webgl上下文丢失事件
    webglcontextlostHandle.call(this)

    // 设置地图样式
    map.setMapStyle('amap://styles/0739a6632d849ae1b5e6f1c52f7d12c3')

    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const that = this
    // map.on('click', (ev:any)=> {
    //   // console.log(ev,'---------ev-----136')
    //   // 触发事件的地理坐标，AMap.LngLat 类型
    //   let lnglat = ev.lnglat;
    //   console.log([lnglat.lng, lnglat.lat]);
    // });
    maps = map
  }
  // 添加雷达图
  private drawRanda(point: any) {
    const _this = this
    if (this.maxPon) {
      maps.remove(this.maxPon)
    }
    const centerPoint = new AMap.LngLat(point.longitude, point.latitude)
    const pointEN = centerPoint.offset(1000, 1000) //向东1000m，向北1000m的位置的经纬度
    const pointWS = centerPoint.offset(-1000, -1000) //向西1000m，向南1000m的位置的经纬度
    const imageLayer = new AMap.ImageLayer({
      url: randaImg,
      bounds: new AMap.Bounds(pointWS, pointEN),
      zooms: [11, 16],
      zIndex: 99,
    })
    maps.add(imageLayer)
    this.maxPon = imageLayer
  }
  //  marker以及弹窗
  private createNoiseMarker() {
    if (this.siteMarkerList.length !== 0) {
      maps.remove(this.siteMarkerList)
      this.siteMarkerList = []
    }
    if (!this.heat) return
    // 水质等级 I （蓝色）
    const rank0: any = new AMap.Icon({
      size: new AMap.Size(28, 28),
      image: cg0,
      imageSize: this.pagetype=='air'?new AMap.Size(26.6, 18.9):new AMap.Size(49.2, 35),
      imageOffset: this.pagetype=='air'?new AMap.Pixel(2.5, 6):new AMap.Pixel(-7.1, 0),
    })
    // 空气等级1  水质等级 II
    const rank1: any = new AMap.Icon({
      size: new AMap.Size(28, 28),
      image: cg1,
      imageSize: this.pagetype=='air'?new AMap.Size(26.6, 18.9):new AMap.Size(49.2, 35),
      imageOffset: this.pagetype=='air'?new AMap.Pixel(2.5, 6):new AMap.Pixel(-7.1, 0),
    })
    // 空气等级2  水质等级 III
    const rank2: any = new AMap.Icon({
      size: new AMap.Size(28, 28),
      image: cg2,
      imageSize: this.pagetype=='air'?new AMap.Size(26.6, 18.9):new AMap.Size(49.2, 35),
      imageOffset: this.pagetype=='air'?new AMap.Pixel(2.5, 6):new AMap.Pixel(-7.1, 0),
    })
    // 空气等级3  水质等级 IV
    const rank3 = new AMap.Icon({
      size: new AMap.Size(28, 28),
      image: cg3,
      imageSize: this.pagetype=='air'?new AMap.Size(26.6, 18.9):new AMap.Size(49.2, 35),
      imageOffset: this.pagetype=='air'?new AMap.Pixel(2.5, 6):new AMap.Pixel(-7.1, 0),
    })
    // 空气等级4  水质等级 V
    const rank4 = new AMap.Icon({
      size: new AMap.Size(28, 28),
      image: cg4,
      imageSize: this.pagetype=='air'?new AMap.Size(26.6, 18.9):new AMap.Size(49.2, 35),
      imageOffset: this.pagetype=='air'?new AMap.Pixel(2.5, 6):new AMap.Pixel(-7.1, 0),
    })
    // 空气等级5
    const rank5 = new AMap.Icon({
      size: new AMap.Size(28, 28),
      image: cg5,
      imageSize: this.pagetype=='air'?new AMap.Size(26.6, 18.9):new AMap.Size(49.2, 35),
      imageOffset: this.pagetype=='air'?new AMap.Pixel(2.5, 6):new AMap.Pixel(-7.1, 0),
    })
    // 空气等级6
    const rank6 = new AMap.Icon({
      size: new AMap.Size(28, 28),
      image: cg6,
      imageSize: this.pagetype=='air'?new AMap.Size(26.6, 18.9):new AMap.Size(49.2, 35),
      imageOffset: this.pagetype=='air'?new AMap.Pixel(2.5, 6):new AMap.Pixel(-7.1, 0),
    })
    let curLevel = 0
    this.heatmapData.forEach((item: any, index: any) => {
      let siteMarkerItem: any
      let curIcon: any
      if (this.pagetype == 'air') {
        // AQI
        if (this.heathMapMax == '300') {
          if (item.concentration >= 0 && item.concentration <= 50) {
            curIcon = rank1
            curLevel = 1
          } else if (item.concentration <= 100) {
            curIcon = rank2
            curLevel = 2
          } else if (item.concentration <= 150) {
            curIcon = rank3
            curLevel = 3
          } else if (item.concentration <= 200) {
            curIcon = rank4
            curLevel = 4
          } else if (item.concentration <= 300) {
            curIcon = rank5
            curLevel = 5
          } else {
            curIcon = rank6
            curLevel = 6
          }
        }
        // PM2.5
        if (this.heathMapMax == '250') {
          if (item.concentration >= 0 && item.concentration <= 35) {
            curIcon = rank1
            curLevel = 1
          } else if (item.concentration <= 75) {
            curIcon = rank2
            curLevel = 2
          } else if (item.concentration <= 115) {
            curIcon = rank3
            curLevel = 3
          } else if (item.concentration <= 150) {
            curIcon = rank4
            curLevel = 4
          } else if (item.concentration <= 250) {
            curIcon = rank5
            curLevel = 5
          } else {
            curIcon = rank6
            curLevel = 6
          }
        }
        // PM10
        if (this.heathMapMax == '420') {
          if (item.concentration >= 0 && item.concentration <= 50) {
            curIcon = rank1
            curLevel = 1
          } else if (item.concentration <= 150) {
            curIcon = rank2
            curLevel = 2
          } else if (item.concentration <= 250) {
            curIcon = rank3
            curLevel = 3
          } else if (item.concentration <= 350) {
            curIcon = rank4
            curLevel = 4
          } else if (item.concentration <= 420) {
            curIcon = rank5
            curLevel = 5
          } else {
            curIcon = rank6
            curLevel = 6
          }
        }
        // NO2
        if (this.heathMapMax == '2340') {
          if (item.concentration >= 0 && item.concentration <= 100) {
            curIcon = rank1
            curLevel = 1
          } else if (item.concentration <= 200) {
            curIcon = rank2
            curLevel = 2
          } else if (item.concentration <= 700) {
            curIcon = rank3
            curLevel = 3
          } else if (item.concentration <= 1200) {
            curIcon = rank4
            curLevel = 4
          } else if (item.concentration <= 2340) {
            curIcon = rank5
            curLevel = 5
          } else {
            curIcon = rank6
            curLevel = 6
          }
        }
        // O3
        if (this.heathMapMax == '800' && this.curtype == '102') {
          if (item.concentration >= 0 && item.concentration <= 100) {
            curIcon = rank1
            curLevel = 1
          } else if (item.concentration <= 160) {
            curIcon = rank2
            curLevel = 2
          } else if (item.concentration <= 215) {
            curIcon = rank3
            curLevel = 3
          } else if (item.concentration <= 265) {
            curIcon = rank4
            curLevel = 4
          } else if (item.concentration <= 800) {
            curIcon = rank5
            curLevel = 5
          } else {
            curIcon = rank6
            curLevel = 6
          }
        }
        // SO2
        if (this.heathMapMax == '800' && this.curtype == '100') {
          if (item.concentration >= 0 && item.concentration <= 150) {
            curIcon = rank1
            curLevel = 1
          } else if (item.concentration <= 500) {
            curIcon = rank2
            curLevel = 2
          } else if (item.concentration <= 650) {
            curIcon = rank3
            curLevel = 3
          } else if (item.concentration <= 800) {
            curIcon = rank4
            curLevel = 4
          } else {
            curIcon = rank5
            curLevel = 5
          }
        }
        // CO
        if (this.heathMapMax == '90') {
          if (item.concentration >= 0 && item.concentration <= 5) {
            curIcon = rank1
            curLevel = 1
          } else if (item.concentration <= 10) {
            curIcon = rank2
            curLevel = 2
          } else if (item.concentration <= 35) {
            curIcon = rank3
            curLevel = 3
          } else if (item.concentration <= 60) {
            curIcon = rank4
            curLevel = 4
          } else if (item.concentration <= 90) {
            curIcon = rank5
            curLevel = 5
          } else {
            curIcon = rank6
            curLevel = 6
          }
        }
      } else {
        if (item.waterLevel == 'Ⅰ' || item.waterLevel == 'Ⅱ' || item.waterLevel == 'Ⅲ') {
          curIcon = rank1
          curLevel = 1
        } else {
          curIcon = rank4
          curLevel = 4
        }
      }
      if (item.stationCode == this.curStationCode) {
        // 选中
        siteMarkerItem = new AMap.Marker({
          map: maps,
          icon: curIcon,
          position: [item.longitude, item.latitude],
          offset: new AMap.Pixel(-15.5, -15.5),
          data: item,
          zIndex: 999,
        })
        // siteMarkerItem.setLabel({
        //   offset: new AMap.Pixel(6, 10), //设置文本标注偏移量
        //   content: `<div class='${'dep-site-info-sel'+curLevel} dep-site-info'></div>` //设置文本标注内容
        // })
        this.currItem = siteMarkerItem
        // this.openRandaSite(item)
      } else {
        siteMarkerItem = new AMap.Marker({
          map: maps,
          icon: curIcon,
          position: [item.longitude, item.latitude],
          offset: new AMap.Pixel(-15.5, -15.5),
          data: item,
          zIndex: 999,
        })
      }
      AMap.event.addListener(siteMarkerItem, 'click', this.siteNoiseClick)
      this.siteMarkerList.push(siteMarkerItem)
    })
  }
  private siteNoiseClick(marker: any) {
    const data = marker.target.w.data
    let curLevel = 0
    this.siteMarkerList.forEach((items: any) => {
      const item = items.w.data
      if (this.pagetype == 'air') {
        // AQI
        if (this.heathMapMax == '300') {
          if (item.concentration >= 0 && item.concentration <= 50) {
            curLevel = 1
          } else if (item.concentration <= 100) {
            curLevel = 2
          } else if (item.concentration <= 150) {
            curLevel = 3
          } else if (item.concentration <= 200) {
            curLevel = 4
          } else if (item.concentration <= 300) {
            curLevel = 5
          } else {
            curLevel = 6
          }
        }
        // PM2.5
        if (this.heathMapMax == '250') {
          if (item.concentration >= 0 && item.concentration <= 35) {
            curLevel = 1
          } else if (item.concentration <= 75) {
            curLevel = 2
          } else if (item.concentration <= 115) {
            curLevel = 3
          } else if (item.concentration <= 150) {
            curLevel = 4
          } else if (item.concentration <= 250) {
            curLevel = 5
          } else {
            curLevel = 6
          }
        }
        // PM10
        if (this.heathMapMax == '420') {
          if (item.concentration >= 0 && item.concentration <= 50) {
            curLevel = 1
          } else if (item.concentration <= 150) {
            curLevel = 2
          } else if (item.concentration <= 250) {
            curLevel = 3
          } else if (item.concentration <= 350) {
            curLevel = 4
          } else if (item.concentration <= 420) {
            curLevel = 5
          } else {
            curLevel = 6
          }
        }
        // NO2
        if (this.heathMapMax == '2340') {
          if (item.concentration >= 0 && item.concentration <= 100) {
            curLevel = 1
          } else if (item.concentration <= 200) {
            curLevel = 2
          } else if (item.concentration <= 700) {
            curLevel = 3
          } else if (item.concentration <= 1200) {
            curLevel = 4
          } else if (item.concentration <= 2340) {
            curLevel = 5
          } else {
            curLevel = 6
          }
        }
        // O3
        if (this.heathMapMax == '800' && this.curtype == '102') {
          if (item.concentration >= 0 && item.concentration <= 100) {
            curLevel = 1
          } else if (item.concentration <= 160) {
            curLevel = 2
          } else if (item.concentration <= 215) {
            curLevel = 3
          } else if (item.concentration <= 265) {
            curLevel = 4
          } else if (item.concentration <= 800) {
            curLevel = 5
          } else {
            curLevel = 6
          }
        }
        // SO2
        if (this.heathMapMax == '800' && this.curtype == '100') {
          if (item.concentration >= 0 && item.concentration <= 150) {
            curLevel = 1
          } else if (item.concentration <= 500) {
            curLevel = 2
          } else if (item.concentration <= 650) {
            curLevel = 3
          } else if (item.concentration <= 800) {
            curLevel = 4
          } else {
            curLevel = 5
          }
        }
        // CO
        if (this.heathMapMax == '90') {
          if (item.concentration >= 0 && item.concentration <= 5) {
            curLevel = 1
          } else if (item.concentration <= 10) {
            curLevel = 2
          } else if (item.concentration <= 35) {
            curLevel = 3
          } else if (item.concentration <= 60) {
            curLevel = 4
          } else if (item.concentration <= 90) {
            curLevel = 5
          } else {
            curLevel = 6
          }
        }
      } else {
        curLevel =
          item.waterLevel == 'Ⅰ' || item.waterLevel == 'Ⅱ' || item.waterLevel == 'Ⅲ'
            ? 1
            : item.waterLevel == 'Ⅳ' || item.waterLevel == 'Ⅴ'
            ? 4
            : 4
      }
      if (item.stationCode === data.stationCode) {
        this.currItem = items
        items.setLabel({
          offset: this.pagetype=='air'?new AMap.Pixel(6, 10):new AMap.Pixel(8, 12), //设置文本标注偏移量
          content: `<div class='${
            'dep-site-info-sel' + curLevel
          } dep-site-info'></div>`, //设置文本标注内容
        })
      } else {
        items.setLabel({
          offset: this.pagetype=='air'?new AMap.Pixel(6, 10):new AMap.Pixel(8, 12), //设置文本标注偏移量
          content: '<div></div>', //设置文本标注内容
        })
      }
    })
    this.openRandaSite(data)
  }
  // 地图热力图图层
  private drawHeatMap() {
    maps.setCenter([104.066, 30.725])
    maps.setZoom(12.5)
    this.closeInfoWindowRck()
    // const waterCenterPosition = new AMap.LngLat(104.04,30.733);
    const that = this
    const heathMapMax = this.heathMapMax
    let heatMapData: any = this.heatmapData.map((v: any) => {
      return {
        lng: v.longitude,
        lat: v.latitude,
        count: v.concentration,
      }
    })
    if (heatMapData.every((o: any) => o.count == undefined)) {
      heatMapData = []
    }
    maps.plugin(['AMap.Heatmap'], function () {
      if (that.heatMapLayer) {
        that.heatMapLayer.setMap(null)
      }
      //初始化heatmap对象
      that.heatMapLayer = new AMap.Heatmap(maps, {
        radius: 35, //给定半径
        opacity: [0, 0.8],
        gradient: {
          0: 'rgba(0,228,0,0.1)',
          0.1: 'rgba(0,228,0,0.4)',
          0.2: 'rgba(255,255,0,0.1)',
          0.3: 'rgba(255,255,0,0.4)',
          0.4: 'rgba(255,126,0,0.1)',
          0.5: 'rgba(255,126,0,0.4)',
          0.6: 'rgba(255,0,0,0.1)',
          0.7: 'rgba(255,0,0,0.4)',
          0.8: 'rgba(153,0,76,0.1)',
          0.9: 'rgba(153,0,76,0.4)',
          1: 'rgba(126,0,35,0.4)',
        },
      })
      //设置数据集
      that.heatMapLayer.setDataSet({
        data: heatMapData,
        max: heathMapMax,
      })
    })
    that.$nextTick(() => {
      that.getPhoto()
      this.$emit('topBtnOpen')
    })
  }
  private clearHeatMapLayer() {
    this.heatMapLayer?.setMap(null)
  }
  // 地图添加站点水路图
  private drawWaterLine() {
    maps.remove(this.polylinesList)
    maps.setCenter([104.066, 30.725])
    maps.setZoom(12.5)
    this.closeInfoWindowRck()
    this.heatmapData.forEach((item: any) => {
      let strokeColor =
        item.waterLevel == 'Ⅰ' || item.waterLevel == 'Ⅱ' ||item.waterLevel == 'Ⅲ'
          ?  ['rgb(0, 228, 0)', 'rgb(36, 230, 36)', 'rgb(69, 230, 69)', 'rgb(92, 228, 92)', 'rgb(123, 226, 123)']
          // : item.waterLevel == 'Ⅳ'
          // ? ['rgb(255, 255, 0)', 'rgb(252, 252, 33)', 'rgb(250, 250, 67)', 'rgb(250, 250, 104)', 'rgb(248, 248, 135)']
          // : item.waterLevel == 'Ⅴ'
          // ? ['rgb(255, 126, 0)', 'rgb(255, 144, 32)', 'rgb(253, 154, 54)', 'rgb(253, 172, 90)', 'rgb(250, 189, 128)']
          : ['rgb(255, 0, 0)', 'rgb(253, 28, 28)', 'rgb(253, 69, 69)', 'rgb(252, 98, 98)', 'rgb(252, 134, 134)']
      for (let i = 0; i < 5; i++) {
        if (waterStationJson[item.stationName+i]) {
          let polyline= new AMap.Polyline({
            path: waterStationJson[item.stationName+i].path,
            isOutline: true,
            outlineColor: 'rgba(0,0,0,0)',
            borderWeight: 0,
            strokeColor: strokeColor[i],
            strokeOpacity: 1,
            strokeWeight: 6,
            strokeStyle: 'solid',
            lineJoin: 'round',
            lineCap: 'round',
            extData: item.stationName+i
            // zIndex: 50,
          })
          polyline.setMap(maps)
          this.polylinesList.push(polyline)
        }
      }
    })
    setTimeout(() => {
      this.getPhoto()
    }, 500)
  }

  // 添加行政区外的覆盖物
  private createOverlay() {
    const map = maps
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const that = this
    // 添加金牛区地理信息数据 3
    new AMap.DistrictSearch({
      extensions: 'all',
      subdistrict: 0,
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    }).search('金牛区', function (status: any, result: any) {
      // 外多边形坐标数组和内多边形坐标数组
      const outer = [
        new AMap.LngLat(-360, 90, true),
        new AMap.LngLat(-360, -90, true),
        new AMap.LngLat(360, -90, true),
        new AMap.LngLat(360, 90, true),
      ]
      const holes = result.districtList[0].boundaries

      const pathArray: any = [outer]
      // eslint-disable-next-line prefer-spread
      pathArray.push.apply(pathArray, holes)
      const polygon = new AMap.Polygon({
        pathL: pathArray,
        //线条颜色，使用16进制颜色代码赋值。默认值为#006600
        strokeColor: 'rgb(255,255,255)',
        strokeWeight: 0,
        //轮廓线透明度，取值范围[0,1]，0表示完全透明，1表示不透明。默认为0.9
        strokeOpacity: 0,
        //多边形填充颜色，使用16进制颜色代码赋值，如：#FFAA00
        fillColor: 'rgba(3,4,130)',
        // fillColor: "rgba(4,20,50)",
        // fillColor: "#0A1C5F",
        //多边形填充透明度，取值范围[0,1]，0表示完全透明，1表示不透明。默认为0.9
        fillOpacity: 0,
        //轮廓线样式，实线:solid，虚线:dashed
        strokeStyle: 'solid',
        strokeDasharray: [10, 2, 10],
      })
      polygon.setPath(pathArray)
      map.add(polygon)
    })
  }
  // 添加河流水系图层
  private dynamicRivderLayer() {
    maps.setCenter([104.066, 30.725])
    maps.setZoom(12.5)
    const bounds = new AMap.Bounds(
      [103.9363, 30.662957],
      [104.160597, 30.807361]
    )
    const imageLayer = new AMap.ImageLayer({
      url: districtRiver, // districtRiver
      bounds: bounds,
      zooms: [3, 18],
      opacity: 1,
    })
    imageLayer.setMap(maps)
    this.riverLayer = imageLayer
    // setTimeout(() => {
    //   this.getPhoto()
    // }, 500)
  }

  // 添加街道划分区域地图数据
  private creatGeojson() {
    const map = maps
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const that = this
    // 添加金牛区地理信息数据 1
    const geojson = new AMap.GeoJSON({
      geoJSON: jinniuStreet,
      getPolygon: function (geojson: any, lnglats: any) {
        AMap.convertFrom(
          geojson.geometry.coordinates[0],
          'gps',
          function (status: any, result: any) {
            // const area: any = AMap.GeometryUtil.ringArea(lnglats[0]);
            // if (geojson.properties.name !== "金牛区") {
            //   const text = new AMap.Text({
            //     // text: geojson.properties.name,
            //     text:  geojson.properties.name,
            //     anchor: "center", // 设置文本标记锚点
            //     draggable: false,
            //     cursor: "pointer",
            //     angle: 0,
            //     style: {
            //       padding: ".75rem 1.25rem",
            //       "margin-bottom": "1rem",
            //       "border-radius": ".25rem",
            //       "background-color": "rgba(1, 43, 72, 0)",
            //       "border-width": 0,
            //       "text-align": "center",
            //       "font-size": "14px",
            //       color: "rgba(0,228,0, 1)"
            //     },
            //     position: [
            //       geojson.properties.center.lng,
            //       geojson.properties.center.lat
            //     ]
            //   });
            //   text.setMap(map);
            // }

            if (result.info === 'ok' && geojson.properties.name !== '金牛区') {
              const polygon = new AMap.Polygon({
                path: result.locations,
                strokeColor: '#2266AB',
                strokeWeight: 3,
                strokeOpacity: 1,
                // strokeStyle: 'dashed',
                // strokeDasharray: [10,10],
                // fillOpacity: 0.5, // 多边形填充透明度
                fillColor: 'rgba(3,70,109,0.3)',
                zIndex: 1,
              })
              // polygon.on('click', (en: any) => {
              //   const lnglat = en.lnglat
              //   that.openRandaSite({ lng: lnglat.lng, lat: lnglat.lat })
              // })
              map.add(polygon)
              that.polygonList.push(polygon)
              if (geojson.properties.name == '金泉') {
                setTimeout(() => {
                  that.getPhoto(true)
                  that.firstPhoto = false
                }, 20)
              }
            }
          }
        )
      },
    })
    // 添加金牛区地理信息数据 2
    geojson.setMap(map)
  }
  private closeInfoWindowRck() {
    if (maps) {
      maps.clearInfoWindow()
    }
  }
  private openRandaSite(marker: any) {
    // findPointData({groupFileName:this.groupFileName,lng:marker.lng,lat:marker.lat}).then((res:any)=>{
    // const {data} = res.data
    let morUnit = ['PM₁₀', 'PM₂.₅', 'SO₂', 'NO₂', 'O₃'].includes(
      this.curtypeName
    )
      ? 'μg/m³'
      :['总磷', '氨氮', '溶解氧', '高锰酸盐指数'].includes(
      this.curtypeName
    )
      ? 'mg/L'
      : this.curtypeName == 'CO'
      ? 'mg/m³'
      : ''
    let infoWindow
    let str = ''
    let valueColor = ''
    let valueStatus = ''
    if (this.pagetype == 'air') {
      // AQI
      if (this.heathMapMax == '300') {
        if (marker.concentration >= 0 && marker.concentration <= 50) {
          valueColor = 'rgb(0,228,0)'
          valueStatus = '优'
        } else if (marker.concentration <= 100) {
          valueColor = 'rgb(255,255,0)'
          valueStatus = '良'
        } else if (marker.concentration <= 150) {
          valueColor = 'rgb(255,126,0)'
          valueStatus = '轻度污染'
        } else if (marker.concentration <= 200) {
          valueColor = 'rgb(255,0,0)'
          valueStatus = '中度污染'
        } else if (marker.concentration <= 300) {
          valueColor = 'rgb(153,0,76)'
          valueStatus = '重度污染'
        } else {
          valueColor = 'rgb(126,0,35)'
          valueStatus = '严重污染'
        }
      }
      // PM2.5
      if (this.heathMapMax == '250') {
        if (marker.concentration >= 0 && marker.concentration <= 35) {
          valueColor = 'rgb(0,228,0)'
          valueStatus = '优'
        } else if (marker.concentration <= 75) {
          valueColor = 'rgb(255,255,0)'
          valueStatus = '良'
        } else if (marker.concentration <= 115) {
          valueColor = 'rgb(255,126,0)'
          valueStatus = '轻度污染'
        } else if (marker.concentration <= 150) {
          valueColor = 'rgb(255,0,0)'
          valueStatus = '中度污染'
        } else if (marker.concentration <= 250) {
          valueColor = 'rgb(153,0,76)'
          valueStatus = '重度污染'
        } else {
          valueColor = 'rgb(126,0,35)'
          valueStatus = '严重污染'
        }
      }
      // PM10
      if (this.heathMapMax == '420') {
        if (marker.concentration >= 0 && marker.concentration <= 50) {
          valueColor = 'rgb(0,228,0)'
          valueStatus = '优'
        } else if (marker.concentration <= 150) {
          valueColor = 'rgb(255,255,0)'
          valueStatus = '良'
        } else if (marker.concentration <= 250) {
          valueColor = 'rgb(255,126,0)'
          valueStatus = '轻度污染'
        } else if (marker.concentration <= 350) {
          valueColor = 'rgb(255,0,0)'
          valueStatus = '中度污染'
        } else if (marker.concentration <= 420) {
          valueColor = 'rgb(153,0,76)'
          valueStatus = '重度污染'
        } else {
          valueColor = 'rgb(126,0,35)'
          valueStatus = '严重污染'
        }
      }
      // NO2
      if (this.heathMapMax == '2340') {
        if (marker.concentration >= 0 && marker.concentration <= 100) {
          valueColor = 'rgb(0,228,0)'
          valueStatus = '优'
        } else if (marker.concentration <= 200) {
          valueColor = 'rgb(255,255,0)'
          valueStatus = '良'
        } else if (marker.concentration <= 700) {
          valueColor = 'rgb(255,126,0)'
          valueStatus = '轻度污染'
        } else if (marker.concentration <= 1200) {
          valueColor = 'rgb(255,0,0)'
          valueStatus = '中度污染'
        } else if (marker.concentration <= 2340) {
          valueColor = 'rgb(153,0,76)'
          valueStatus = '重度污染'
        } else {
          valueColor = 'rgb(126,0,35)'
          valueStatus = '严重污染'
        }
      }
      // O3
      if (this.heathMapMax == '800' && this.curtype == '102') {
        if (marker.concentration >= 0 && marker.concentration <= 100) {
          valueColor = 'rgb(0,228,0)'
          valueStatus = '优'
        } else if (marker.concentration <= 160) {
          valueColor = 'rgb(255,255,0)'
          valueStatus = '良'
        } else if (marker.concentration <= 215) {
          valueColor = 'rgb(255,126,0)'
          valueStatus = '轻度污染'
        } else if (marker.concentration <= 265) {
          valueColor = 'rgb(255,0,0)'
          valueStatus = '中度污染'
        } else if (marker.concentration <= 800) {
          valueColor = 'rgb(153,0,76)'
          valueStatus = '重度污染'
        } else {
          valueColor = 'rgb(126,0,35)'
          valueStatus = '严重污染'
        }
      }
      // SO2
      if (this.heathMapMax == '800' && this.curtype == '100') {
        if (marker.concentration >= 0 && marker.concentration <= 150) {
          valueColor = 'rgb(0,228,0)'
          valueStatus = '优'
        } else if (marker.concentration <= 500) {
          valueColor = 'rgb(255,255,0)'
          valueStatus = '良'
        } else if (marker.concentration <= 650) {
          valueColor = 'rgb(255,126,0)'
          valueStatus = '轻度污染'
        } else if (marker.concentration <= 800) {
          valueColor = 'rgb(255,0,0)'
          valueStatus = '中度污染'
        } else {
          valueColor = 'rgb(126,0,35)'
          valueStatus = '严重污染'
        }
      }
      // CO
      if (this.heathMapMax == '90') {
        if (marker.concentration >= 0 && marker.concentration <= 5) {
          valueColor = 'rgb(0,228,0)'
          valueStatus = '优'
        } else if (marker.concentration <= 10) {
          valueColor = 'rgb(255,255,0)'
          valueStatus = '良'
        } else if (marker.concentration <= 35) {
          valueColor = 'rgb(255,126,0)'
          valueStatus = '轻度污染'
        } else if (marker.concentration <= 60) {
          valueColor = 'rgb(255,0,0)'
          valueStatus = '中度污染'
        } else if (marker.concentration <= 90) {
          valueColor = 'rgb(153,0,76)'
          valueStatus = '重度污染'
        } else {
          valueColor = 'rgb(126,0,35)'
          valueStatus = '严重污染'
        }
      }
    } else {
      valueStatus = (marker.waterLevel || 'Ⅲ') + '类'
      valueColor =
        marker.waterLevel == 'Ⅰ' || marker.waterLevel == 'Ⅱ' || marker.waterLevel == 'Ⅲ'
          ?  'rgb(0, 228, 0)'
          : 'rgb(255, 0, 0)'
    }
    if (marker) {
      // let morValue = this.randaType=='消光系数'?data.xiaoguang:this.randaType=='PM10'?data.pm10:data.pm25
      str = `
            <div style="width:3.44rem; height:2.9rem; background: url('${electricityBg}') no-repeat; background-size: 100% 100%; padding:0.15rem 0.25rem;transform: translateX(0.06rem);">
              <span style="position: absolute;top:0.4rem;left:0.45rem;font-size: 0.18rem;font-family: YouSheBiaoTiHei;font-weight: 400;color: #FFFFFF;background: linear-gradient(0deg, #56D1ED 0%, #CAF6FF 100%);-webkit-background-clip: text;-webkit-text-fill-color: transparent;">站点信息</span>
              <img src="${noiseClose}" style="position: absolute;width:0.22rem; height:0.22rem;top:0.58rem;right:0.25rem;" onclick="closeInfoWindowRck()"/>
              <div style="margin-top:0.6rem;display:flex;padding-left:0.2rem;">
                <span style="font-size: 0.14rem;font-family: PingFang SC;font-weight: 400;color: #7798B9;">监测时间：
                ${marker.monitorTime?moment(marker.monitorTime).format('YYYY-MM-DD HH:mm'):'--'}
                </span>
              </div>
              <div style="margin-top:0.15rem;margin-left:0.2rem; color:#56A7F1;">
                <div style="display:flex;margin-top:0.05rem;" title="${
                  marker.positionName || marker.stationName
                }">
                  <span style="flex:1;display:-webkit-box;-webkit-line-clamp:2;overflow:hidden;-webkit-box-orient:vertical;text-overflow: ellipsis;">站点名称：<span style="color:#DCF0FF;">${
                    marker.positionName || marker.stationName
                  }</span></span>
                </div>
                <div style="margin-top:0.02rem;"> ${this.curtypeName}：
                  <span style="font-family: YouSheBiaoTiHei;color:${valueColor};font-size:0.18rem;">${
        marker.concentration
      }</span>
                  <span style="color:#DCF0FF;">${morUnit}</span>
                  <span style="color:#fff;font-size: 12px;font-family: Source Han Sans CN;font-weight: 400; background-color:#0E5EAF;border-radius:3px;padding:1px 3px;">${valueStatus}</span>
                </div>
                <div style="display:flex;margin-top:0.05rem;">
                  <span style="flex:1;">${
                    this.pagetype == 'air' ? '所在街道' : '所属河流'
                  }：<span style="color:#DCF0FF;">${
        marker.streetName || marker.riverName
      }</span></span>
                </div>
                <div style="margin-top:0.05rem;display:flex;" title="${
                  marker.address
                }"> <span style="width: 70px;">详细地址：</span><span style="color:#DCF0FF;flex:1;display:-webkit-box;-webkit-line-clamp:2;overflow:hidden;-webkit-box-orient:vertical;text-overflow: ellipsis;">${
        marker.address
      }</span></div>
              </div>
            </div>
          `
    } else {
      str = `
          <div style="width:3.44rem; height:2.9rem; background: url('${electricityBg}') no-repeat; background-size: 100% 100%; padding:0.15rem 0.25rem;transform: translateX(0.06rem);">
            <span style="position: absolute;top:0.44rem;left:0.45rem;font-size: 0.18rem;font-family: YouSheBiaoTiHei;font-weight: 400;color: #FFFFFF;background: linear-gradient(0deg, #56D1ED 0%, #CAF6FF 100%);-webkit-background-clip: text;-webkit-text-fill-color: transparent;">污染信息</span>
            <img src="${noiseClose}" style="position: absolute;width:0.22rem; height:0.22rem;top:0.64rem;right:0.28rem;" onclick="closeInfoWindowRck()"/>
            <div style="text-align: center;width:100%;margin-top:1.1rem;">
              <div>
                <img src="${heavyCorruptKong}" style="width:0.77rem;height:0.74rem;" />
              </div>
              <div style="font-size: 0.15rem;font-family: PingFang SC;font-weight: 300;color: #7694B3;line-height: 0.24rem;">当前点位暂无数据</div>
            </div>
          </div>
          `
    }
    infoWindow = new AMap.InfoWindow({
      // anchor: "bottom-center",
      isCustom: true,
      content: str, //使用默认信息窗体框样式，显示信息内容
      offset: this.pagetype=='air'?new AMap.Pixel(-6, 18):new AMap.Pixel(-6, 13),
    })
    this.infoWindow = infoWindow
    // maps.setCenter([marker.longitude, marker.latitude])
    infoWindow.open(maps, [marker.longitude, marker.latitude])
    // })
  }
}
</script>

<style lang="less">
@import url(./map.less);
.dep-site-info {
  position: relative;
  width: 10px;
  height: 10px;
  left: 0;
  top: -3px;
  border-radius: 50%;
  // transform: rotateX(70deg);
  /*-moz-animation-name: ripple;*/
  /*-webkit-animation-name: ripple;*/
  animation-name: depripple;
  animation-delay: 0s;
  animation-duration: 1s;
  -moz-animation-iteration-count: infinite;
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
  pointer-events: none;
}
.dep-site-info-sel0 {
  background-color: rgba(22, 145, 249, 0.7);
}
.dep-site-info-sel1 {
  background-color: rgba(0, 228, 0, 0.7);
}
.dep-site-info-sel2 {
  background-color: rgba(255, 255, 0, 0.7);
}
.dep-site-info-sel3 {
  background-color: rgba(255, 126, 0, 0.7);
}
.dep-site-info-sel4 {
  background-color: rgba(255, 0, 0, 0.7);
}
.dep-site-info-sel5 {
  background-color: rgba(153, 0, 76, 0.7);
}
.dep-site-info-sel6 {
  background-color: rgba(126, 0, 35, 0.7);
}
@keyframes depripple {
  from {
    opacity: 1;
  }
  to {
    width: 60px;
    height: 60px;
    top: -30px;
    left: -25px;
    border-radius: 50%;
    opacity: 0;
  }
}
.amap-marker-label{
  pointer-events: none !important;
}
.amap-icon{
  overflow: hidden !important;
  img{
    pointer-events: none !important;
  }
}
</style>
<style lang="less" scoped>
.container {
  width: 100%;
  height: 100%;
  color: rgb(252, 134, 134);
}
</style>
