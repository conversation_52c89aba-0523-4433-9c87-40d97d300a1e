<template>
  <div class="app_rank">
    <div id="waterMonitor"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
export default {
  name: 'carRank',
  props: {
    echartData: {
      type: Array,
      default: () => [0, 0, 0, 0, 0],
    },
    chartX: {
      type: Array,
      default: () => [],
    },
    dataType: {
      type: String,
      default: () => 'nh3n',
    },
    chartLegendName: {
      type: String,
      default: '氨氮',
    },
  },
  components: {},
  data() {
    return {
      echart: '',
      // echartData:[ 0,2,4,1,3],
      // echartBgData:[4,4,4,4,4],
      // chartX:[
      //   '氨氮',
      //   '总磷',
      //   'PH',
      //   '溶解氧',
      //   '高锰酸盐指数'
      // ],
    }
  },
  watch: {
    echartData: {
      handler(nval, oval) {
        if (nval) {
          // console.log(nval, '------------newVal')
          if (this.echart) {
            this.echart.dispose()
          }
          this.$nextTick(() => {
            this.initEchart()
          })
        }
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {
    // console.log('initchart----------64')
    this.initEchart()
  },
  methods: {
    initEchart() {
      console.log(this.chartX.map(v=>v.slice(5,16)),'-------67');
      const maxX = Math.max(...this.echartData)
      if (document.getElementById('waterMonitor')) {
        const chartMonth = echarts.init(document.getElementById('waterMonitor'))
        this.echart = chartMonth
        chartMonth.clear()
        chartMonth.showLoading()
        const _that = this
        let option = {
          color: ['#38E3FF'],
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              // 坐标轴指示器，坐标轴触发有效
              type: 'line', // 默认为直线，可选为：'line' | 'shadow'
            },
            formatter: function (params) {
              if (!params[0].value) {
                return ''
              }
              // console.log(params,'909090');
              return (
                '监测时间：' +
                params[0].name +
                '<br/> ' +
                '检测值：' +
                params[0].value +
                `${params[0].seriesName == 'ph' ? '' : 'mg/L'}`
              )
            },
          },
          legend: {
            orient: 'horizontal', //水平展示，不写默认水平展示
            right: 10,
            top: 0,
            icon: 'roundRect',
            itemHeight: 2,
            itemWidth: 15,
            itemGap: 15,
            itemStyle: {},
            formatter: () => {
              return _that.chartLegendName
            },
            textStyle: {
              // fontSize: 18,//字体大小
              color: '#ffffff', //字体颜色
            },
          },
          grid: {
            containLabel: true,
            left: '1%',
            top: '15%',
            right: '0%',
            bottom: '7%',
          },
          xAxis: {
            data: this.chartX.map(v=>v.slice(5,16)),
            axisTick: {
              show: false,
            },
            axisLabel: {
              color: '#80B6C8',
              interval: 'auto',
              // rotate: -35,
              fontSize: 12,
            },
            axisLine: {
              lineStyle: {
                color: 'rgba(56,89,130,0.7)',
                type: [5, 5],
              },
            },
          },
          yAxis: {
            name: '单位:' + (this.dataType == 'ph' ? '无' : '(mg/L)'),
            max: maxX>10?maxX:10,
            min: 0,
            nameTextStyle: {
              color: '#80B6C8',
              padding: [0, 0, 0, 10],
            },
            splitLine: {
              lineStyle: {
                color: 'rgba(56,89,130,0.7)',
                type: [5, 5],
              },
            },
            axisLine: {
              lineStyle: {
                color: '#1B5BBA',
              },
            },
            axisLabel: {
              color: '#80B6C8',
              interval: 'auto',
            },
          },
          series: [
            {
              name: this.dataType,
              // symbolSize:15,
              type: 'line',
              stack: 'Total',
              smooth: true,
              lineStyle: {
                width: 3,
                color: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: '#38E3FF', // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: '#3B7BB9', // 100% 处的颜色
                    },
                  ],
                  global: false, // 缺省为 false
                },
              },
              symbol: 'circle',
              symbolSize: 7,
              // showSymbol: false,
              areaStyle: {
                opacity: 0.8,
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: 'rgba(59,123,185,0.65)',
                  },
                  {
                    offset: 0.5,
                    color: 'rgba(59,123,185,0.2)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(59,123,185,0.01)',
                  },
                ]),
              },
              itemStyle: {
                normal: {
                  color: '#030B1B',
                  borderWidth: 2,
                  borderColor: '#00B3F6 ',
                  shadowBlur: 7,
                  shadowColor: 'rgba(136,213,241,0.7)',
                },
              },
              emphasis: {
                focus: 'series',
              },
              data: this.echartData || [],
            },
          ],
        }
        chartMonth.setOption(option)
        chartMonth.hideLoading()
      }
    },
  },
}
</script>
<style scoped>
#waterMonitor {
  width: 100%;
  height: 190px;
}
</style>