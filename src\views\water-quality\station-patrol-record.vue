<style lang="less" scoped>
@container-shadow: 0.5rem 0.5rem 2rem 0 rgba(black, 0.2);

@gutter: 30px;
@border-width: 4px;
@dot-diameter: 8px;
.patrol-record {
  padding: 0.2rem 0.2rem 0.2rem;
  position: relative;
  .patrol-record-content {
    box-sizing: border-box;
    padding: 0.5rem 1rem;
    // height: calc(100% - 0.8rem);
    height: 100%;
    background-image: url(../../assets/water-video.png) !important;
    background-size: 100% 100% !important;
    .patrol-record-list-flex {
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .patrol-record-list {
      margin-top: 0.1rem;
      height: calc(100% - 0.5rem);
      overflow-y: scroll;
      ul,
      li {
        /*做时间轴的线*/
        margin: 0;
        padding: 0;
      }

      .layui-timeline {
        padding-left: 2rem;
        padding-top: 0.1rem;
      }

      .layui-timeline-item {
        position: relative;
        padding-bottom: 0.2rem;
        margin-top: -0.12rem;
        padding-right: 0.5rem;
      }

      li {
        list-style: none;
      }

      .layui-timeline-item:first-child::before {
        display: block;
      }

      .layui-timeline-item:last-child::before {
        content: "";
        position: absolute;
        left: 0.05rem;
        top: 0;
        z-index: 0;
        width: 0;
      }

      .layui-timeline-item::before {
        content: "";
        position: absolute;
        top: 0;
        z-index: 0;
        width: 0.02rem;
        height: calc(100% - 0.5rem);
      }

      .layui-timeline-item::before,
      hr {
        background-color: #00e9fe;
        margin-top: 0.55rem;
      }

      .layui-timeline-axis {
        position: absolute;
        left: -0.05rem;
        top: 0;
        z-index: 10;
        width: 0.2rem;
        height: 0.2rem;
        line-height: 0.2rem;
        background-color: #fff;
        color: #fff;
        border-radius: 50%;
        text-align: center;
        cursor: pointer;
      }

      .layui-timeline-content {
        padding-left: 0.25rem;
        /*height: 1.81rem;*/
        font-size: 0.14rem;
        color: #fff;
        top: -0.1rem;
        position: relative;
        background-image: url(../../assets/card-bg.png);
        background-size: 100% 100%;
        margin-left: 0.2rem;
        > div {
          display: flex;
          box-sizing: border-box;
          padding: 0.22rem;
          padding-left: 0.5rem;
          > :nth-of-type(1) {
            display: flex;
            div {
              font-size: 0.18rem;
              width: 0.4rem;
              box-sizing: border-box;
              padding: 0 0.1rem;
              display: flex;
              align-items: center;
              height: 1.4rem;
              background: rgba(25, 84, 167, 1);
            }
            img {
              width: 2.04rem;
              border-top-right-radius: 0.08rem;
              border-bottom-right-radius: 0.08rem;
              height: 1.4rem;
            }
          }
          > :nth-of-type(2) {
            padding: 0.05rem 0;
            padding-left: 0.3rem;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            font-size: 0.22rem;
            :nth-of-type(3) {
              /*width: 1.5rem;*/
              /*height: 0.4rem;*/
              /*text-align: center;*/
              /*line-height: 0.4rem;*/
              /*background: rgba(28, 112, 206, 1);*/
              /*border-radius: 0.05rem;*/
            }
          }
        }
      }

      .circle {
        width: 0.23rem;
        height: 0.23rem;
        position: absolute;
        left: -0.115rem;
        top: 0.25rem;
      }

      .etime-first {
        top: 45%;
        left: -1rem;
        position: absolute;
      }

      .time-first {
        position: absolute;
        left: -1.7rem;
        font-size: 0.2rem;
        top: 0.23rem;
      }
    }
    .patrol-record-select {
      display: flex;
      justify-content: flex-end;
    }
    .eventTypeName {
      position: absolute;
      font-size: 0.24rem;
      top: 0.31rem;
      right: 0.47rem;
      color: #4BFBFF;
    }
    .eventTypeName-err {
      color: #FA6076;
    }
  }
}
.no-record {
  text-align: center;
  font-size: 0.24rem;
  align-items: center;
}
</style>

<style lang="less">
// ::-webkit-scrollbar {
//   width: 0 !important;
// }
.patrol-record-select {
  .title-patrol-record {
    display: flex;
    justify-content: space-between;
    margin-right: 0.1rem;
    .ant-select-selection {
      width: 1.5rem;
      height: 0.3rem;
      // background: rgba(14, 139, 255, 0.32);
      border: none;
      border-radius: unset;
      // clip-path: polygon(100% 0, 100% 100%, 12% 100%, 0% 70%, 0 0);
      background: url(../../assets/<EMAIL>);
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
    .ant-select-selection-selected-value {
      color: rgba(0, 234, 255, 1);
    }
    .ant-select-focused .ant-select-selection,
    .ant-select-selection:focus,
    .ant-select-selection:active {
      border-color: #40a9ff;
      border-right-width: 0 !important;
      outline: 0;
      box-shadow: none;
    }
  }
}
</style>

<template>
  <section class="patrol-record">
    <section class="patrol-record-content">
      <section class="patrol-record-select">
        <a-select
          v-model="defaultValue1"
          class="title-patrol-record"
          @change="selectChange"
        >
          <a-icon
            slot="suffixIcon"
            type="caret-down"
            style="color:rgba(0, 234, 255, 1);width:14px;height:8px;"
          />
          <a-select-option
                  :value="item.riverId"
                  v-for="(item, index) in riverList"
                  :key="index"
          >
            {{ item.riverName }}</a-select-option
          >
        </a-select>
        <a-select
          v-model="defaultValue"
          class="title-patrol-record"
          @change="getWaterPatrolRecordListByStation"
        >
          <a-icon
            slot="suffixIcon"
            type="caret-down"
            style="color:rgba(0, 234, 255, 1);width:14px;height:8px;"
          />
          <a-select-option
                  :value="item.stationId"
                  v-for="(item, index) in stationList"
                  :key="index"
          >
            {{ item.stationName }}</a-select-option>
        </a-select>
      </section>
      <section class="patrol-record-list" :class="{'patrol-record-list-flex': recordList.length == 0 ? 'patrol-record-list-flex' : ''}">
        <ul v-if="recordList.length" class="layui-timeline">
          <li class="layui-timeline-item">
            <div class="relative">
              <span class="time-first" style="left: -0.9rem;">{{new Date().getFullYear()}}年</span>
            </div>
            <img src="@/assets/<EMAIL>" alt="" class="circle" />
            <div style="height: 0.8rem;"></div>
          </li>
          <li
            class="layui-timeline-item"
            v-for="(item, index) in recordList"
            :key="index"
          >
            <div class="relative">
              <span class="time-first">{{ item.time }}</span>
              <!-- <span class="etime-first">4月09日 11：37</span> -->
            </div>
            <img src="@/assets/<EMAIL>" alt="" class="circle" />
            <div class="layui-timeline-content">
              <div>
                <div>
                  <div>巡站图片</div>
                  <img v-show="indexs == 0" class="img" v-for="(item,indexs) in item.annexUrlList" :key="indexs" :src="item" :preview="'item'+index">
<!--                  <img :src="(item.annexUrlList && item.annexUrlList[0]) || defaultImage" mode="" />-->
                </div>
                <div>
                  <div>巡岗人员：{{ item.userName }}</div>
                  <div style="margin: 15px 0">巡查反馈：{{ item.content }}</div>
                  <div>巡岗地址：{{ item.address }}</div>
                </div>
                <div class="eventTypeName" :class="{'eventTypeName-err': item.eventType == 1 ? 'eventTypeName-err' : ''}">{{ item.eventTypeName }}</div>
              </div>
            </div>
          </li>
        </ul>
        <div v-else class="no-record">暂无数据</div>
      </section>
    </section>
  </section>
</template>

<script lang="ts">
/* eslint-disable @typescript-eslint/ban-ts-ignore */
import { Component, Vue, Watch } from "vue-property-decorator";
import { Table, Radio, Select, Icon } from "ant-design-vue";
import { riverList, getWaterPatrolRecordListByStation } from '@/api/water'
@Component({
  name: "StationPatrolRecord",
  components: {
    ATable: Table,
    ARadioGroup: Radio.Group,
    ARadioButton: Radio.Button,
    ASelect: Select,
    AIcon: Icon,
    ASelectOption: Select.Option
  }
})
export default class extends Vue {
  private defaultValue: any = "全部";
  private cardList = [
    {
      img:
        "https://oss-chengdu.vankeytech.com:9000/vankeytech-ep/punch/b8b492d56b5f421c9b54a36d6743b56b.png",
      name: "蔡俊行",
      content: "河道有少量漂浮物，已打捞完毕。",
      time: "4月28日 15:14"
    },
    {
      img:
        "https://oss-chengdu.vankeytech.com:9000/vankeytech-ep/punch/80ad04810dde4b95952402bce1fddb88.png",
      name: "柏荣",
      content: "河道污染严重，前去进行处理。",
      time: "4月30日 10:01"
    }
  ];
  private defaultImage = 'https://oss-chengdu.vankeytech.com:9000/vankeytech-ep/punch/b8b492d56b5f421c9b54a36d6743b56b.png'
  private defaultValue1:any = '全部'
  private riverList:any = []
  private stationList:any = []
  private recordList:any = []
  mounted() {
    this.getRiverList()
  }
  private getRiverList() {
    riverList().then(res => {
      this.riverList = res.data.data || []
      this.riverList.unshift({
        riverId: '全部',
        riverName: '全部',
        stationList: [ {
          stationId: '全部',
          stationName: '全部'
        }]
      })
      if (this.riverList.length) {
        this.defaultValue1 = this.riverList[0].riverId
        this.stationList = this.riverList[0].stationList || []
        console.log(this.stationList, 'stationList')
        if (this.stationList.length) {
          this.defaultValue = this.stationList[0].stationId
          this.getWaterPatrolRecordListByStation()
        } else {
          this.defaultValue = ''
        }
      }
    })
  }
  getWaterPatrolRecordListByStation() {
    const params = {
      pageNum: 1,
      pageSize: 20,
      stationId: this.defaultValue == '全部' ? undefined : this.defaultValue
    }
    getWaterPatrolRecordListByStation(params).then((res:any) => {
      const data = res.data.data
      console.log(res, 'data')
      var reg =/(\d{4})\-(\d{2})\-(\d{2})/;
      this.recordList = (data.records || []).map((item:any) => {
        item.time = item.createTime.replace(reg,"$1年$2月$3日").substr(5,12);
        item.annexUrlList = (item.taskAnnexList || []).map((items:any) => items.annexUrl)
        return item
      })
      console.log(this.recordList, 'recordList')
      /* eslint-disable @typescript-eslint/ban-ts-ignore */
      //@ts-ignore
      this.$previewRefresh()
    })
  }
  private selectChange(val: any) {
    const curr = this.riverList.findIndex((item:any) => item.riverId == val)
    console.log(curr, 123)
    this.stationList = this.riverList[curr].stationList || []
    if (this.stationList.length) {
      this.defaultValue = this.stationList[0].stationId
      this.getWaterPatrolRecordListByStation()
    } else {
      this.defaultValue = ''
    }
  }
}
</script>
