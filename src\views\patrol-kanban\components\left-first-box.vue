<template>
  <div class="left-fist-container">
    <div class="item-box">
      <img src="@/assets/patrolKanban/<EMAIL>" alt="" />
      <span class="unit">累计巡查/次</span>
      <span class="number">{{LeftFirstBoxData.total || 0}}</span>
    </div>
    <div class="item-box">
      <img src="@/assets/patrolKanban/<EMAIL>" alt="" />
      <span class="unit">本年巡查/次</span>
      <span class="number">{{LeftFirstBoxData.thisYear || 0}}</span>
    </div>
    <div class="item-box">
      <img src="@/assets/patrolKanban/<EMAIL>" alt="" />
      <span class="unit">本月巡查/次</span>
      <span class="number">{{LeftFirstBoxData.thisMonth}}</span>
    </div>
  </div>
</template>

<script>
export default {
  name: "",
  data() {
    return {};
  },
  props: {
    LeftFirstBoxData: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  created() {},
  methods: {},
  computed: {},
  watch: {},
  components: {},
};
</script>

<style lang="less" scoped>
.left-fist-container {
  display: flex;
  justify-content: space-between;
  margin-top: 30px;
  .item-box {
    display: flex;
    flex-direction: column;
    align-items: center;
    .unit {
      font-size: 16px;
      font-family: PingFang SC;
      font-weight: 400;
      color: #92a9c1;
      margin: 20px auto 15px;
    }
    .number {
      font-size: 28px;
      font-family: DIN;
      font-weight: 500;
      color: #c8dff0;
    }
  }
}
</style>
