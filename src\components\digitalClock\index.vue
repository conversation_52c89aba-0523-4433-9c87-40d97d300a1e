<template>
  <div class="digital-clock-container">
    <numberItem v-for="(i, index) in hour" :key="'hour' + index" :value="i" />
    <span class="separator">:</span>
    <numberItem
      v-for="(i, index) in minute"
      :key="'minute' + index"
      :value="i"
    />
    <span class="separator">:</span>
    <numberItem
      v-for="(i, index) in second"
      :key="'second' + index"
      :value="i"
    />
  </div>
</template>

<script>
import numberItem from "./numberItem.vue";
export default {
  name: "digitalClock",
  props: {
    start: {
      type: [String, Date],
      default: new Date(),
    },
    end: {
      type: [String, Date],
      default: "",
    },
  },
  components: {
    numberItem,
  },
  data() {
    return {
      hour: [0, 0],
      minute: [0, 0],
      second: [0, 0],
      timer: 0,
    };
  },
  watch: {
    start() {
      if (this.end) {
        this.totalTimeChange();
        return;
      }
      this.timer = setInterval(() => {
        // timechange()
        this.totalTimeChange();
      }, 1000);
    },
  },
  mounted() {
    this.timechange();
  },
  activated() {
    if (this.end) {
      this.totalTimeChange();
      return;
    }
    this.timer = setInterval(() => {
      // timechange()
      this.totalTimeChange();
    }, 1000);
  },
  deactivated() {
    clearInterval(this.timer);
  },
  beforeDestroy() {
    clearInterval(this.timer);
  },
  methods: {
    PrefixInteger(num, length) {
      return (Array(length).join("0") + num).slice(-length);
    },
    /**
     * 自带的时钟数据变化
     */
    timechange() {
      const date = new Date();
      const h = this.PrefixInteger(date.getHours(), 2);
      const m = this.PrefixInteger(date.getMinutes(), 2);
      const s = this.PrefixInteger(date.getSeconds(), 2);
      this.hour = [h[0], h[1]];
      this.minute = [m[0], m[1]];
      this.second = [s[0], s[1]];
    },

    /**
     * 计算当前时间距离开始时间的差值
     */
    totalTimeChange() {
      let startTime = new Date();
      let endTime = "";
      const { start, end } = this;
      if (!isNaN(Date.parse(start))) startTime = new Date(start);
      if (!isNaN(Date.parse(end))) endTime = new Date(end);
      let now = endTime || new Date();
      const ms = now - startTime;
      const hours = Math.floor(ms / (1000 * 60 * 60));
      const minutes = parseInt((ms % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((ms % (1000 * 60)) / 1000);
      this.setTime(this.hour, hours);
      this.setTime(this.minute, minutes);
      this.setTime(this.second, seconds);
    },

    setTime(container, time) {
      let val = `${time}`;
      let len = `${time}`.length;
      if (len < 2) {
        val = this.PrefixInteger(time, 2);
        len = 2;
      }
      if (container.length > len) {
        container.length = len;
      }
      for (let i = 0; i < len; i++) {
        this.$set(container, i, val[i]);
      }
    },
  },
};
</script>

<style lang="less">
.digital-clock-container {
  width: 100%;
  max-width: 100%;
  overflow: auto;
  display: flex;
  align-items: center;

  .separator {
    color: #46d5f6;
    font-size: 28px;
    font-weight: 700;
    display: inline-block;
  }
}
</style>
