/* Recommended css code for vue-svgicon */
.svg-icon {
  display: inline-block;
  width: 1.6rem;
  height: 1.6rem;
  color: inherit;
  fill: none;
  stroke: currentColor;
  vertical-align: -0.15em;
}

.svg-fill {
  fill: currentColor;
  stroke: none;
}

.svg-up {
  transform: rotate(0deg);
}

.svg-right {
  transform: rotate(90deg);
}

.svg-down {
  transform: rotate(180deg);
}

.svg-left {
  transform: rotate(-90deg);
}
