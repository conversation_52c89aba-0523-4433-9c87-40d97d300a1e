import request from "@/utils/request";
import { AxiosPromise } from "axios";

/**
 * @method functionName
 * @param {type} data 说明
 * @description 获取空气预警最近20条记录 全部站点
 */
export function getAllRecent(): AxiosPromise<any> {
  return request({
    url: "/air/air-alarm/recent",
    method: "get",
  });
}

/**
 * @method functionName
 * @param {type} data 说明
 * @description 获取站点和人力图
 */
export function allAirStation(data: any): AxiosPromise<any> {
  return request({
    url: "/air/air_station/air_home/data_show",
    method: "get",
    params: data
  });
}

/**
 * @method functionName
 * @param {type} data 说明
 * @description 获取某个站点最近的20条预警信息
 */
export function getOneRecent(data: any): AxiosPromise<any> {
  return request({
    url: `/air/air-alarm/recent/${data}`,
    method: "get",
  });
}

/**
 * @method functionName
 * @param {type} data 说明
 * @description 通过区代码获取全部站点aqi信息
 */
export function getAllAqiInfo(data: any): AxiosPromise<any> {
  return request({
    url: "/air/air_station/station_list",
    method: "get",
    params: {
      stationTypeId: "1,2,3"
    }
  });
}

/**
 * @method functionName
 * @param {type} data 说明
 * @description 根据站点类型获取站点列表
 */
export function airStation(data: any): AxiosPromise<any> {
  return request({
    url: "/air/air_station/station_list",
    method: "get",
    params: data,
  });
}
/**
 * @method functionName
 * @param {type} data 说明
 * @description 24小时空气质量检测
 */
export function aqidetails(data: any): AxiosPromise<any> {
  return request({
    url: "/air/air_station/aqi/details",
    method: "get",
    params: data,
  });
}

/**
 * @method functionName
 * @param {type} data 说明
 * @description 24小时空气质量检测
 */
export function airStationdDetails(stationCode: any): AxiosPromise<any> {
  return request({
    url: `/air/air_station/station_details/${stationCode}`,
    method: "get"
  });
}


/**
 * @method functionName
 * @param {type} data 说明
 * @description 24小时空气质趋势
 */
export function aqiTrend(data: any): AxiosPromise<any> {
  return request({
    url: "/air/air_station/aqi_trend",
    method: "get",
    params: data,
  });
}

/**
 * @method functionName
 * @param {type} data 说明
 * @description 质量指数排行
 */
export function aqiRanking(data: any): AxiosPromise<any> {
  return request({
    url: "/air/air_station/aqi_ranking",
    method: "get",
    params: data,
  });
}

/**
 * @method functionName
 * @param {type} data 说明
 * @description 未来空气质量预测
 */
export function qualityForecast(): AxiosPromise<any> {
  return request({
    url: "/air/air_station/future_air/quality_forecast",
    method: "get",
  });
}

/**
 * @method functionName
 * @param {type} data 说明
 * @description 空气质量考核
 */
export function airCheck(): AxiosPromise<any> {
  return request({
    url: "/air/air_station/aqi_quality/check",
    method: "get",
  });
}

/**
 * @method functionName
 * @param {type} data 说明
 * @description 空气质量趋势
 */
export function aqiQualityTrend(data: any): AxiosPromise<any> {
  return request({
    url: "/air/air_station/aqi_quality/trend",
    method: "get",
    params: data,
  });
}

/**
 * @method functionName
 * @param {type} data 说明
 * @description 首页污染物数据展示
 */
export function airIndexAverage(): AxiosPromise<any> {
  return request({
    url: "/air/air_station/pollution_codes/average",
    method: "get",
  });
}

/**
 * @method functionName
 * @param {type} data 说明
 * @description 首页污染分布热力图
 */
export function pollutionDistributed(data: any): AxiosPromise<any> {
  return request({
    url: "/air/air_station/pollution_distributed",
    method: "get",
    params: data,
  });
}

/**
 * @method functionName
 * @param {type} data 说明
 * @description 空气质量对比
 */
export function yearOnYear(): AxiosPromise<any> {
  return request({
    url: "/air/air_station/station_aqi_quality/year_on_year",
    method: "get",
  });
}

/**
 * @method functionName
 * @param {type} data 说明
 * @description 空气 空气质量综合指数
 */
export function aqci(): AxiosPromise<any> {
  return request({
    url: "/air/air-county-month-aqci/aqci",
    method: "get",
  });
}

/**
 * @method functionName
 * @param {type} data 说明
 * @description 同比
 */
export function aqcistation(stationId: any): AxiosPromise<any> {
  return request({
    // url: `air/aqci/${stationId}`,
    url: `air/air-station-month-aqci/aqci/${stationId}`,
    method: "get",
  });
}
/**
 * @method functionName
 * @param {type} data 环比
 * @description
 */
export function stationRingAir(stationId: any): AxiosPromise<any> {
  return request({
    url: `/air/air-station-month-aqci/ring/aqci/${stationId}`,
    method: "get"
  });
}

/**
 * @method functionName
 * @param {type} data 说明
 * @description 空气 空气质量综合指数
 */
export function airStationDetails(stationCode: any): AxiosPromise<any> {
  return request({
    url: `/air/air_station/station_pollution_details`,
    // url: `air/aqci`,
    method: "get",
    params: {
      stationCode
    }
  });
}

/**
 * @method functionName
 * @param {type} data 说明
 * @description 空气详情页面街道类型站点层级列表选择
 */
export function airStationOptionList(): AxiosPromise<any> {
  return request({
    url: `/air/air_station/option_list`,
    method: "get"
  });
}
/**
 * @method functionName
 * @param {type} data 说明
 * @description 区 12个月环比
 */
export function ringAqci(): AxiosPromise<any> {
  return request({
    url: "/air/air-county-month-aqci/ring/aqci",
    method: "get"
  });
}

/**
 * @method functionName
 * @param {type} data 说明
 * @description
 */
export function functionName(data: any): AxiosPromise<any> {
  return request({
    url: "url",
    method: "post",
    data: data,
  });
}
/**
 * @method getmonthAqiCalendar
 * @description 获取本月污染日历
 */
export function getmonthAqiCalendar(): AxiosPromise<any> {
  return request({
    url: '/air/air-county-day-aqi/monthAqiCalendar',
    method: 'get'
  })
}
/**
 * @method getAreaData
 * @description 获取街道区域数据
 */
export function getAreaData(pollutantCode: any): AxiosPromise<any> {
  return request({
    url: '/air/street/hour/aqi/streetStation',
    method: 'get',
    params: {
      pollutantCode: pollutantCode
    }
  })
}
/**
 * @method getPredict
 * @param {type} data 说明
 * @description 获取7天空气质量预报信息
 */
export function getPredict(): AxiosPromise<any> {
  return request({
    url: "/air/air_station/future_air/aqiPredict",
    method: "get"
  });
}

/**
 * @method getRadar
 * @description 获取雷达列表
 */
export function getRadar(): AxiosPromise<any> {
  return request({
    url: "/air/web/lidar-station/list",
    method: "get"
  });
}
/**
 * @method getCamera
 * @description 获取站点监控列表
 */
export function getCamera(): AxiosPromise<any> {
  return request({
    url: "/air/camera/listGroup",
    method: "get"
  });
}
/**
 * @method ptzStop
 * @description 云台控制停止
 */
export function ptzStop(data: any): AxiosPromise<any> {
  return request({
    url: "/air/web/lidar-camera/ptzStop",
    method: "get",
    params: data
  });
}

/**
 * @method ptz
 * @description 云台控制
 */
export function ptzC(data: any): AxiosPromise<any> {
  return request({
    url: "/air/web/lidar-camera/ptz",
    method: "get",
    params: data
  });
}