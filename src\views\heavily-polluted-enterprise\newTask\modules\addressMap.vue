<template>
  <div class="map-container">
    <div
      id="map"
      :style="{height:height }"
    />
    <el-input
      v-if="detailShow"
      v-model="keywords"
      placeholder="请输入内容"
      class="input-with-select"
    >
      <el-button
        slot="append"
        icon="el-icon-search"
        @click="search()"
      />
    </el-input>
  </div>
</template>
<script>
// eslint-disable-next-line import/no-unresolved
// import AMap from 'AMap'
import AMapLoader from "@amap/amap-jsapi-loader";
// eslint-disable-next-line import/no-unresolved
import icons from '@/assets/images/mark_bs.png'
AMap
export default {
  props: {
    height: {
      type: String,
      default: '400px'
    },
    address: {
      type: String,
      default: ''
    },
    detailShow: {
      type: Boolean,
      default: false
    },
    latlng: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      map: null,
      keywords: '',
      overlays: [],
      mouseTool: null,
      geocoder: null
    }
  },
  mounted() {
    AMapLoader.load({
      key: "777fec7ef3cc29281d60ae900fa33925", // 申请好的Web端开发者Key，首次调用 load 时必填
      version: "1.4.15", // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
      plugins: [
        "AMap.DistrictSearch",
        "AMap.Heatmap",
        "AMap.ControlBar",
        "AMap.Object3DLayer",
        "Map3D",
        "AMap.Geocoder",
        "AMap.CircleMarker",
        "AMap.MouseTool",
        "AMap.RangingTool", // 这个是测距插件
      ], // 需要使用的的插件列表，如比例尺'AMap.Scale'等
      AMapUI: {
        // 是否加载 AMapUI，缺省不加载
        version: "1.0", // AMapUI 版本
        // "plugins":['overlay/SimpleMarker'],       // 需要加载的 AMapUI ui插件
      },
      Loca: {
        // 是否加载 Loca， 缺省不加载
        version: "1.3.2", // Loca 版本
      },
    })
      .then((amaps) => {
        console.log(this.activeIndex, "polluted---initMap--------421");
        AMap = amaps;
        this.initMap()
      })
      .catch((e) => {
        console.log(e);
      });

  },
  methods: {
    /**
     * @method initMap
     * @description 初始化地图
     */
    initMap() {
      this.map = new AMap.Map('map', {
        resizeEnable: true,
        zoom: 14,
        // mapStyle: 'amap://styles/light', // 设置地图的显示样式
        center: [104.071216, 30.668516]
      })
      if (!this.latlng.lat) {
        this.mouseTool = new AMap.MouseTool(this.map)
        this.geocoder = new AMap.Geocoder({
          city: '全国'
        })
        this.drawMaker()
      }
      if (this.latlng.lat) {
        this.addMaker(this.latlng.lng, this.latlng.lat)
      } else if (this.address) {
        this.search(this.address)
      }
    },
    /**
     * @method search
     * @description 搜索
     */
    search(address) {
      const geocoder = new AMap.Geocoder({
        city: '全国' // 城市设为北京，默认：“全国”
      })
      if (this.marker) {
        this.map.remove(this.marker)
      }
      const that = this
      AMap.plugin('AMap.PlaceSearch', () => {
        const autoOptions = {
          city: '全国'
        }
        const placeSearch = new AMap.PlaceSearch(autoOptions)
        placeSearch.search(address || that.keywords, (status, result) => {
          // 搜索成功时，result即是对应的匹配数据
          if (status === 'complete' && result.poiList.pois.length) {
            const lnglat = result.poiList.pois[0].location
            that.$emit('address', result.poiList.pois[0].address || result.poiList.pois[0].name)
            that.addMaker(lnglat.lng, lnglat.lat)
          } else {
            geocoder.getLocation('成都万开科技', (status1, result1) => {
              if (status1 === 'complete' && result1.geocodes.length) {
                const lnglat = result1.geocodes[0].location

                that.$emit('address', result1.geocodes[0].formattedAddress)
                that.addMaker(lnglat.lng, lnglat.lat)
              } else {
                that.$message.warning('根据地址查询位置失败')
              }
            })
          }
        })
      })
    },
    /**
     * @method addMaker
     * @description 添加marker
     */
    addMaker(lng, lat) {
      const icon = new AMap.Icon({
        // 图标尺寸
        size: new AMap.Size(19, 33),
        // 图标的取图地址
        image: icons,
        // 图标所用图片大小
        imageSize: new AMap.Size(19, 33)
        // 图标取图偏移量
        // imageOffset: new AMap.Pixel(-9, -3)
      })
      this.marker = new AMap.Marker({
        icon,
        position: [Number(lng), Number(lat)]
      })
      if (this.overlays.length) {
        this.map.remove(this.overlays[0].obj)
        this.overlays.shift()
      }
      this.overlays.push({ obj: this.marker, position: { lng, lat }})
      this.submitMap()
      this.marker.setMap(this.map)
      this.map.setCenter(new AMap.LngLat(lng, lat))
    },
    /**
     * @method drawMaker
     * @description 地图鼠标点击绘制图标
     */
    drawMaker() {
      // 监听draw事件可获取画好的覆盖物
      if (this.marker) {
        this.overlays.push({ obj: this.marker, position: { lng: this.lng, lat: this.lat }})
      }
      // this.overlays = [];
      const that = this
      this.mouseTool.on('draw', (e) => {
        const position = e.obj.getPosition()
        if (that.overlays.length) {
          that.map.remove(that.overlays[0].obj)
          that.overlays.shift()
        }
        that.overlays.push({ obj: e.obj, position })
        that.submitMap()
        that.geocoder.getAddress(position, (status, result) => {
          if (status === 'complete' && result.info === 'OK') {
            that.$emit('address', result.regeocode.formattedAddress)
          }
        })
      })
      this.mouseTool.marker({})
    },
    submitMap() {
      this.$emit('submit', this.overlays)
    }
  }
}
</script>
<style lang="less" scoped>
.map-container {
  width: 100%;
  height: 100%;
  position: relative;
  #map {
    width: 100%;
  }
  .input-with-select {
    width: 60%;
    position: absolute;
    top: 50px;
    left: 20%;
  }
}
</style>
