<template>
  <div class="drum_cont">
    <div class="guangmu"></div>
    <div class="ball ball1">
      <div class="ball_title">1类</div>
      <div class="ball_num">{{ stationTypeCount["1类"] }}</div>
    </div>
    <div class="ball ball2">
      <div class="ball_title">2类</div>
      <div class="ball_num">{{ stationTypeCount["2类"] }}</div>
    </div>
    <div class="ball ball3">
      <div class="ball_title">3类</div>
      <div class="ball_num">{{ stationTypeCount["3类"] }}</div>
    </div>
    <div class="ball ball4">
      <div class="ball_title">4a类</div>
      <div class="ball_num">{{ stationTypeCount["4a类"] }}</div>
    </div>
    <div class="ball ball5">
      <div class="ball_title">4b类</div>
      <div class="ball_num">{{ stationTypeCount["4b类"] }}</div>
    </div>
  </div>
</template>
<script>
export default {
  name: "cardBox",
  props: {
    stationTypeCount: {
      type: Object,
      default: () => {},
    },
  },
};
</script>

<style lang="less">
.drum_cont {
  width: 100%;
  height: 280px;
  background-image: url("../../../assets/sheng/<EMAIL>");
  background-size: 100% 138px;
  background-position: center bottom;
  background-repeat: no-repeat;
  position: relative;
  .guangmu {
    position: absolute;
    height: 160px;
    left: 43px;
    bottom: 49px;
    width: 310px;
    background-image: url("../../../assets/sheng/<EMAIL>");
    background-size: 100%;
    background-position: center;
    background-repeat: no-repeat;
    animation: identifier 1s linear infinite alternate;
  }

  @keyframes identifier {
    0% {
      opacity: 1;
    }

    100% {
      opacity: 0.2;
    }
  }
  .ball {
    position: relative;
  }

  .ball1 {
    width: 77px;
    height: 77px;
    background-image: url("../../../assets/sheng/<EMAIL>");
    background-size: 100%;
    background-position: center;
    background-repeat: no-repeat;
    position: absolute;
    left: 42%;
    animation: move 3s linear infinite alternate;
    // top: 10%;
  }
  .ball2 {
    width: 65px;
    height: 65px;
    background-image: url("../../../assets/sheng/<EMAIL>");
    background-size: 100%;
    background-position: center;
    background-repeat: no-repeat;
    position: absolute;
    left: 22%;
    top: 10%;
    animation: move 3s linear 0.5s infinite alternate;

    .ball_num {
      color: #2eff8b !important;
    }
  }
  .ball3 {
    width: 64px;
    height: 64px;
    background-image: url("../../../assets/sheng/<EMAIL>");
    background-size: 100%;
    background-position: center;
    background-repeat: no-repeat;
    position: absolute;
    left: 29%;
    top: 37%;
    animation: move 2s linear 1s infinite alternate;
    .ball_num {
      color: #dbe131 !important;
    }
  }
  .ball4 {
    width: 71px;
    height: 71px;
    background-image: url("../../../assets/sheng/<EMAIL>");
    background-size: 100%;
    background-position: center;
    background-repeat: no-repeat;
    position: absolute;
    left: 50%;
    top: 44%;
    animation: move 2.5s linear 1.5s infinite alternate;
    .ball_num {
      color: #f19824 !important;
    }
  }
  .ball5 {
    width: 65px;
    height: 65px;
    background-image: url("../../../assets/sheng/<EMAIL>");
    background-size: 100%;
    background-position: center;
    background-repeat: no-repeat;
    position: absolute;
    left: 61%;
    top: 17%;
    animation: move 2.5s linear infinite alternate;
    .ball_num {
      color: #f67573 !important;
    }
  }
  .ball_title {
    font-size: 18px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #ffffff;
    text-align: center;
    width: 100%;
    position: absolute;
    top: 18%;
    transform: scale(0.8);
  }
  .ball_num {
    font-size: 20px;
    font-family: DIN;
    font-weight: 500;
    color: #2bcef6;
    width: 100%;
    text-align: center;
    position: absolute;
    top: 45%;
  }

  @keyframes move {
    0% {
      transform: translateY(0px);
    }

    100% {
      transform: translateY(24px);
    }
  }
}
</style>
