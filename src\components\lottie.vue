<template>
    <div :ref="id" :style="style" />
</template>

<script>
import lottie from 'lottie-web'

export default {
props: {
    options: {
        type: Object,
        required: true
    },
    height: {
        type: Number,
        required: true
    },
    width: {
        type: Number,
        required: true
    },
    id: {
        type: String,
        required: true,
        default: 'lavContainer'
    }
},
watch: {
    options(val) {
        if (val) {
            this.initView()
        }
    }
},
data() {
    return {
        style: {
            width: this.width ? `${this.width}px` : '100%',
            height: this.height ? `${this.height}px` : '100%',
            overflow: 'hidden',
            margin: '0 auto'
        }
    }
},

mounted() {
    this.initView()
},
methods: {
    initView() {
        this.anim = lottie.loadAnimation({
                container: this.$refs[this.id],
                renderer: 'svg',
                loop: this.options.loop !== false,
                autoplay: this.options.autoplay !== false,
                animationData: this.options.animationData,
                rendererSettings: this.options.rendererSettings
                // assetsPath: '/src/assets/'
            }
        )
        this.$emit('animCreated', this.anim)
    }
}
}
</script>
