<template>
  <div class="left-second-container">
    <div ref="chartRef" v-if="true" class="chart-box"></div>
    <div v-else class="no-data">
      暂无数据
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import ResizeMixin from "@/components/Charts/mixins/resize";

export default {
  name: "LineChartDashed",
  mixins: [ResizeMixin],
  props: {
    leftSecondData: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      chart: null,
    };
  },
  watch: {
    leftSecondData: {
      handler(val) {
        if (val) {
          this.initChart();
        }
      },
      deep: true,
    },
  },
  methods: {
    initChart() {
      if (this.chart === null || this.chart === undefined) {
        const chartDom = this.$refs.chartRef;
        if (!chartDom) return;
        this.chart = echarts.init(chartDom);
      }
      //   构造数据

      const bottomList = this.leftSecondData.issues.x;
      const data1 = this.leftSecondData.issues.y;
      const data2 = this.leftSecondData.rectification.y;

      const option = {
        backgroundColor: "transparent",
        color: ["#1DCCFF", "#15FEFE"],
        grid: {
          height: "80%",
          left: "0%",
          right: "0%",
          top: "20%",
          bottom: "5%",
          containLabel: true,
        },
        legend: {
          icon: "rect",
          data: ["发现率", "整改率"],
          textStyle: {
            color: "white",
          },
          right: "5%",
          top: "5%",
        },
        xAxis: {
          type: "category",
          name: "",
          nameTextStyle: {
            color: "#fff",
            lineHeight: -30,
            align: "center",
            verticalAlign: "bottom",
          },
          data: bottomList,
          axisLabel: {
            textStyle: {
              fontSize: 12,
              color: "white",
            },
          },
          axisLine: {
            show: false,
            lineStyle: {
              type: "dotted",
            },
          },
          axisTick: {
            show: false,
          },
        },
        yAxis: {
          type: "value",
          name: "%",
          min: 0,
          max: 100,
          nameTextStyle: {
            color: "#fff",
            shadowOffsetX: 50,
          },
          axisLabel: {
            textStyle: {
              fontSize: 12,
              color: "white",
            },
          },
          axisLine: {
            show: true,
          },
          axisTick: {
            show: false,
          },
          splitNumber: 5,
          splitLine: {
            lineStyle: {
              color: "RGBA(2, 39, 75, 1)",
            },
          },
        },
        tooltip: {
          trigger: "axis",
          confine: true,
          axisPointer: {
            type: "cross",
            label: {
              show: false,
              backgroundColor: "#6a7985",
            },
          },
        },
        series: [
          {
            name: "发现率",
            data: data1,
            type: "line",
            smooth: true,
            lineStyle: {
              color: "#15B4FE",
            },
            symbol: "none",
            areaStyle: {
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: "rgba(39, 177, 255, 0.5)",
                  },
                  {
                    offset: 1,
                    color: "RGBA(25,252,255, 0)",
                  },
                ],
                global: false,
              },
              shadowColor: "rgba(0,85,250,0)",
              shadowBlur: 20,
            },
          },
          {
            name: "整改率",
            data: data2,
            type: "line",
            smooth: true,
            lineStyle: {
              color: "#2CFDF7",
            },
            symbol: "none",
            areaStyle: {
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: "rgba(44, 253, 247, 0.5)",
                  },
                  {
                    offset: 1,
                    color: "RGBA(25,252,255, 0)",
                  },
                ],
                global: false,
              },
              shadowColor: "rgba(0,85,250,0)",
              shadowBlur: 20,
            },
          },
        ],
      };

      this.chart.setOption(option);
    },
  },
  mounted() {
    this.initChart();
  },
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.clear();
    this.chart.dispose();
    this.chart = null;
  },
};
</script>
<style lang="less" scoped>
.left-second-container {
  width: 100%;
  height: 200px;
  .chart-box {
    width: 100%;
    height: 100%;
  }
}
.no-data {
  width: 100%;
  height: 100%;
  font-size: 0.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
