import request from "@/utils/request";
import { AxiosPromise } from "axios";

/**
 * @method functionName
 * @param {type} data 说明
 * @description
 */
export function getEnforcement(): AxiosPromise<any> {
  return request({
    url: "/task/joint_law_enforcement_task/bigData/get/count",
    method: "get"
  });
}
/**
 * @method functionName
 * @param {type} data 说明
 * @description
 */
export function getEnforcementTaskId(taskId: any): AxiosPromise<any> {
  return request({
    url: `/task/joint_law_enforcement_task/bigData/task/${taskId}`,
    method: "get"
  });
}
/**
 * @method functionName
 * @param {type} data 说明
 * @description
 */
export function getAqiQuality(stationCode: any, pollutantCode: any): AxiosPromise<any> {
  return request({
    url: `/air/air_station/station/aqi_quality/trend?stationCode=${stationCode}&pollutantCode=${pollutantCode}`,
    method: "get"
  });
}
/**
 * @method functionName
 * @param {type} data 说明
 * @description
 */
export function getTwentyFourHour(companyId: any): AxiosPromise<any> {
  return request({
    url: `company/twentyFourHour/${companyId}`,
    method: "get"
  });
}
/**
 * @method functionName
 * @param {type} data 说明
 * @description
 */
export function functionName(data: any): AxiosPromise<any> {
  return request({
    url: "url",
    method: "get",
    params: data
  });
}

/**
 * @method functionName
 * @param {type} data 说明
 * @description
 */
export function getTaskStatistics(departmentType: any): AxiosPromise<any> {
  return request({
    url: `/task/joint_law_enforcement_task/getTaskStatistics?departmentType=${departmentType}`,
    method: "get"
  });
}

/**
 * @method functionName
 * @param {type} data 说明
 * @description
 */
export function getStreetOfficeList(): AxiosPromise<any> {
  return request({
    url: `/system/department/getStreetOfficeList`,
    method: "get"
  });
}

/**
 * @method functionName
 * @param {type} data 街道部门联动 大数据获取任务信息
 * @description
 */
export function getTaskCountList(departmentId?: any): AxiosPromise<any> {
  return request({
    url: `/task/ep_task/big/data/count`,
    method: "get",
    params: {
      departmentId
    }
  });
}

/**
 * @method functionName
 * @param {type} data 根据 执法事件id 和任务id查询 任务详情
 * @description
 */
export function getTaskInfo(taskId: any, enforcementTaskId: any): AxiosPromise<any> {
  return request({
    url: `/task/ep_task/big/data/getTaskInfo`,
    method: "post",
    data: {
      taskId,
      enforcementTaskId
    }
  });
}
/**
 * @description获取任务月度统计
 * @method getTotalMonth
 */
export function getTotalMonth(): AxiosPromise<any>  {
  return request({
    url: '/task/monthlyStatistics',
    method: 'get'
  })
}
