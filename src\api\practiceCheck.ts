import request from "@/utils/request";
import { AxiosPromise } from "axios";

/**
 * @method airPraCheck
 * @param {type} data 说明
 * @description 空气演练复盘
 */
export function airPraCheck(params:any): AxiosPromise<any> {
  return request({
    url: `/air/air-station-hour-aqi/listStationPollutantMonitor`,
    method: "get",
    params
  });
}
/**
 * @method waterPraCheck
 * @param {type} data 说明
 * @description 水质演练复盘
 */
export function waterPraCheck(params:any): AxiosPromise<any> {
  return request({
    url: `/water/record/listStationPollutantMonitor`,
    method: "get",
    params
  });
}

// 气象环境获取风场数据
export function getWindField() {
  return request({
    url: `air/windChart/last`,
    method: "get",
  })
}

// 查询某个时间段的风场数据
export function getWindFieldByTime(time: any) {
  return request({
    url: `/air/windChart/find`,
    method: "get",
    params: { findTime: time }
  })
}

/**
 * @method deduction
 * @param {type} data 说明
 * @description 新演练复盘图片
 */
export function deduction(params:any): AxiosPromise<any> {
  return request({
    url: `/air/air-deduction-spread/list`,
    method: "get",
    params
  });
}

/**
 * @method deduction
 * @param {type} data 说明
 * @description 获取自然灾害预警
*/
export function meteorology_early_warning(params:any): AxiosPromise<any> {
  return request({
    url: `/air/meteorology_early_warning/getByDate`,
    method: "get",
    params
  });
}
/**
 * @method deduction
 * @param {type} data 说明
 * @description 演练复盘获取 空气污染气象条件列表
*/
export function air_pollution_diffusion(params:any): AxiosPromise<any> {
  return request({
    url: `/air/air_pollution_diffusion/listByDate`,
    method: "get",
    params
  });
}
/**
 * @method deduction
 * @param {type} data 说明
 * @description 演练复盘获取 空气污染气象条件列表
*/
export function rangeByPollutionSource(params:any): AxiosPromise<any> {
  return request({
    url: `/system/bigData/pollutionSourceSurvey/rangeByPollutionSource`,
    method: "get",
    params
  });
}
// 点击获取气象条件
export function rangeAqi(params:any): AxiosPromise<any> {
  return request({
    url: `/air/bigData/rangeAqi`,
    method: "get",
    params
  });
}
