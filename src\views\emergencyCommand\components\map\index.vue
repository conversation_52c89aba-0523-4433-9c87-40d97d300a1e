<template>
  <div>
    <div class="map-container">
      <BaseMap id="emergencyCommandMap" ref="map"></BaseMap>
    </div>
    <MarkerControlBox @change="markerChange"></MarkerControlBox>
    <MarkerItem
      v-show="mapShow"
      v-for="(item, key) in markerList"
      :key="key"
      :icon="key"
      :ref="key"
    ></MarkerItem>

    <LevelMarker
      v-show="mapShow"
      v-for="i in 4"
      :key="'levelMarker' + i"
      :level="i"
      :ref="'levelMarker' + i"
    ></LevelMarker>

    <!-- 地图弹窗所使用的元素 S -->
    <MyDialog ref="dialog" :data="curMarkerData" @close="removePopup"></MyDialog>
    <!-- 地图弹窗所使用的元素 E -->

    <!-- 地图弹窗的主体内容 S -->
    <!-- <dialogCard
      v-if="showContent"
    ></dialogCard> -->
    <!-- 地图弹窗的主体内容 E -->
  </div>
</template>

<script>
import MarkerControlBox from './markerControlBox.vue'
import MarkerItem from './markerItem.vue'
import LevelMarker from './levelMarker.vue'
import BaseMap from '@/components/baseMap/index.vue'
import MyDialog from './dialog.vue'
import { Marker, MarkerLayer, Popup } from '@antv/l7'
import { getMapMarkerData } from '@/api/emergencyDetails/emergencyEvent'
export default {
  name: 'emergencyCommandMap',
  props: {
    lngLat: {
      type: Object,
      default: () => ({}),
    },
    level: {
      type: Number,
      default: 1
    },
  },
  components: {
    MarkerControlBox,
    MarkerItem,
    LevelMarker,
    BaseMap,
    MyDialog
  },
  data() {
    return {
      mapShow: false,
      // 场景
      scene: {},
      // 地图相关设置 s
      // 类型对照表
      markerMap: {
        1: '医疗机构',
        // 2: '重污企业',
        3: '汽修行业',
        4: '印刷企业',
        5: '餐饮油烟',
        6: '加油站',
        7: '建筑工地',
        8: '物资仓库',
        9: '音视频',
        10: '摄像头',
        11: '自来水厂',
        12: '声光报警器',
      },
      // 存储marker数据的容器
      markerList: {
        摄像头: [],
        声光报警器: [],
        音视频: [],
        物资仓库: [],
        // 重污企业: [],
        自来水厂: [],
        建筑工地: [],
        汽修行业: [],
        餐饮油烟: [],
        加油站: [],
        印刷企业: [],
        医疗机构: [],
      },
      showMarkerNameList: [],
      // 地图3D图层
      object3Dlayer: {},
      // 雷达波范围 - 事件物资调动范围 - 单位：米 此范围内的设备需要显示
      radarWaveRadius: 2000,
      radar: null,
      // 范围编辑器控制timer
      circleEditorTimer: 0,
      circleEditorShow: false,
      // marker容器
      markerLayer: {},
      centerMarker: {},
      // 范围编辑器节流控制器
      radarWaveRadiusChangeTimer: 0,
      // 范围控制器的圆
      circle: undefined,
      circleEditor: undefined,
      // 定位marker的dom元素
      pointDomList: {},
      // 控制弹窗内容的显示与隐藏
      showContent: false,
      // 存储当前点击marker的数据
      curMarkerData: null,
      curPopup: null,
      // 地图相关设置 E
    }
  },
  watch: {
    // 监听显示的marker数量变化
    showMarkerList: {
      handler(newV) {
        try {
          if (this.markerLayer.update) {
            this.createMarker(this.scene)
          }
        } catch (error) {
          console.log('更新marker失败', error)
        }
      },
      deep: true,
    },
    lngLat: {
      handler(lngLat) {
        console.log('新的经纬度', lngLat)
        // 获取事件点位数据 ==》 成功后 渲染雷达波 根据数据渲染点位
        // 设置地图中心点
        // 设置事件等级图标

        if (!this.scene.map) return
        console.log('获取数据', this.scene.map)
        this.reSetData()
      },
      deep: true,
    },
    // 范围变化时重新请求数据
    radarWaveRadius: {
      handler(newRadarWaveRadius) {
        this.reSetData()
      },
    },
  },
  beforeMount(){},
  async mounted() {
    console.log('挂在并初始化地图')
    this.scene = this.$refs.map.scene
    this.pointDomList['dialog'] = this.$refs['dialog'].$el
    // console.log(this.$refs['dialog'],'---------163---munted');
    this.initAntV()
  },
  destroyed() {
    this.scene.destroy()
  },
  deactivated() {
    this.showContent = false
    this.scene.removeAllMakers()
  },
  methods: {
    /**
     * 重新获取数据
     */
    reSetData() {
      const { lngLat } = this
      getMapMarkerData({
        lng: lngLat.lng,
        lat: lngLat.lat,
        distance: this.radarWaveRadius,
      })
        .then((res) => {
          console.log('获取点位成功', res)
          const { data } = res
          if (data.code === 200) {
            for (const key in this.markerList) {
              if (Object.prototype.hasOwnProperty.call(this.markerList, key)) {
                const len = this.markerList[key].length
                this.markerList[key].splice(0, len)
              }
            }

            data.data.forEach((ele) => {
              const arr = this.markerList[this.markerMap[ele.type]]
              arr && arr.push(ele)
            })
          }
        })
        .catch((err) => {
          console.log('获取点位失败', err)
        })
        .finally(() => {
          console.log('获取点位请求完成')
        })

      // 重置图标范围
      try {
        this.resetRange()
      } catch (error) {
        console.log('重置范围失败：', error)
      }
    },
    /**
     * 重置雷达波及范围控制器
     */
    resetRange() {
      const { lngLat } = this
      if (this.scene.map) {
        this.scene.map.clearMap()
        this.scene.setCenter([lngLat.lng, lngLat.lat])
      }

      // 重置雷达波位置
      this.radar.position([lngLat.lng, lngLat.lat])

      // 重新渲染marker
      this.createMarker(this.scene)

      // 重新渲染控制器
      this.initCircle(this.scene)
      // resetCircleCenter()
    },
    /**
     * 初始化地图场景
     */
    initAntV() {
      // scene = initScene()
      this.scene.on('loaded', () => {
        this.mapShow = true
        // initWaveAnimate(scene)
        this.create3DLayer(this.scene)
        this.initMarkLayer(this.scene)
        this.createMarker(this.scene)
        this.initCircle(this.scene)

        // 场景初始化完成后进行数据获取
        this.reSetData()

        this.scene.on('click', (ev) => {
          console.log('鼠标点击地图事件', ev)
          // console.log(document.querySelector('#video-tem-ali').);
        }) // 鼠标左键点击事件
      })
    },
    /**
     * 初始化marker容器
     */
    initMarkLayer(scene) {
      this.markerLayer = new MarkerLayer({
        cluster: false,
      })
      scene.addMarkerLayer(this.markerLayer)
    },
    /**
     * 创建marker事件
     * @param scene  场景对象
     */
    createMarker(scene) {
      this.$nextTick(() => {
        scene.removeAllMakers()
        // 创建中心marker
        try {
          this.createCenterMarker(scene)
        } catch (error) {
          console.log('创建中心marker失败', error)
        }
        this.showMarkerList.forEach((item) => {
          const dom = this.$refs[item.name][0].$el.cloneNode(true)
          // 克隆弹窗dom元素
          const dialogDom = this.$refs['dialog'].$el
          const marker = new Marker({
            element: dom,
          }).setLnglat({
            lng: item.lng,
            lat: item.lat,
          })
          marker.on('click', (e) => {
            scene.setCenter([item.lng, item.lat])
            scene.panBy(0, 400)
            const targetEle = e.target.target
            const markerContainer = targetEle && targetEle.parentNode
            // console.log(targetEle)
            if (targetEle.classList.contains('close')) {
              console.log('---------------close--------------347')
              this.curMarkerData = null
              this.removePopup()
            }
            if (!markerContainer.classList.contains('l7-marker')) {
              return
            }
            console.log('marker详情', item)
            this.curMarkerData = item
            // 删除多余的弹窗 ==》 防止渲染错误
            this.removePopup()
            // 增加层级属性保证弹窗能覆盖其他元素
            markerContainer.classList.add('zindex-top')
            // 追加元素
            // markerContainer.appendChild(dialogDom)
            const popup = new Popup({
              // 初始锚点经纬度
              lngLat: {
                lng: item.lng,
                lat: item.lat,
              },
              // Popup 内容
              html: dialogDom,
              anchor: 'center',
              offsets:[-30,50]
            });
            dialogDom.style.display = 'block'
            this.curPopup = popup
            scene.addPopup(popup);
            this.showContent = true
          })
          scene.addMarker(marker)
        })
      })
    },
    /**
     * 移除地图弹窗
     */
    removePopup() {
      if(this.curPopup){
        this.curPopup.close()
      }
      const dialogContainer = document.querySelector('.dialog-container')
      if (dialogContainer) {
        // console.log(dialogContainer.parentElement);
        const parent = dialogContainer.parentElement
        if (parent) {
          parent.classList.remove('zindex-top')
        }
        dialogContainer.remove()
        this.showContent = false
      }
    },
    /**
     * 创建中心marker
     * @param params
     */
    createCenterMarker(scene) {
      console.log('创建中心marker')
      const { lngLat } = this
      console.log('传递过来的中心点', lngLat)
      // console.log('levelMarker' + this.level,this.$refs, this.$refs['levelMarker' + this.level]);
      this.centerMarker = new Marker({
        element: this.$refs['levelMarker' + this.level][0].$el,
      }).setLnglat({
        lng: Number(lngLat.lng),
        lat: Number(lngLat.lat),
      }) // 添加进Marker必须设置经纬度才能添加
      this.centerMarker.on('click', () => {
        console.log('marker点击事件')
      })
      console.log(this.centerMarker,'--------351');
      scene.addMarker(this.centerMarker)
    },
    /**
     * 创建3d图层
     * @param scene  场景对象
     */
    create3DLayer(scene) {
      this.object3Dlayer = new AMap.Object3DLayer({
        zIndex: 110,
        opacity: 1,
      })
      const { map } = scene
      map.add(this.object3Dlayer)
      this.initWaveAnimate(scene)
    },
    /**
     * 雷达波2
     * @param scene  场景对象
     */
    initWaveAnimate(scene) {
      const { map } = scene
      const _this = this
      const buildRadar = function () {
        _this.radar = new AMap.Object3D.Mesh()
        _this.radar.transparent = true
        _this.radar.backOrFront = 'front'
        const geometry = _this.radar.geometry
        let radius = 10 // 米
        radius = radius / map.getResolution(map.getCenter(), 20)
        const unit = 1
        // 不加1会有一个缺口
        const range = 360 + 1
        const count = range / unit
        for (let i = 0; i < count; i += 1) {
          const angle1 = (i * unit * Math.PI) / 180
          const angle2 = ((i + 1) * unit * Math.PI) / 180
          const p1x = Math.cos(angle1) * radius
          const p1y = Math.sin(angle1) * radius
          const p2x = Math.cos(angle2) * radius
          const p2y = Math.sin(angle2) * radius
          // 构建顶点
          geometry.vertices.push(0, 0, 0)
          geometry.vertices.push(p1x, p1y, 0)
          geometry.vertices.push(p2x, p2y, 0)
          // 根据顶点确定面
          geometry.faces.push(i + 2, i + 1, i + 4)
          geometry.faces.push(i + 362, i + 361, i + 364)
          geometry.faces.push(i + 722, i + 721, i + 724)
          // 为面填充纹理颜色
          geometry.vertexColors.push(0.01, 1, 0.73, 0)
          geometry.vertexColors.push(0.01, 1, 0.73, 0.06)
          geometry.vertexColors.push(0.01, 1, 0.73, 0.09)
        }
        _this.radar.position(map.getCenter())
        _this.object3Dlayer.add(_this.radar)
      }
      function getOpacity(scale) {
        return 1 - Math.pow(scale, 0.3)
      }
      let scaleValue = 1
      function scan() {
        _this.radar.setScale(scaleValue, scaleValue, scaleValue)
        scaleValue += _this.radarWaveRadius / 1000
        if (scaleValue >= _this.radarWaveRadius / 10) {
          scaleValue = 1
          // radar.setScale(0.1, 0.1, 0.1)
        }
        AMap.Util.requestAnimFrame(scan)
      }
      buildRadar()
      scan()
    },
    /**
     * 关闭范围编辑器
     * @param circle 范围
     * @param circleEditor 编辑器
     */
    closeCircleEditor(circle, circleEditor) {
      if (this.circleEditorTimer) clearTimeout(this.circleEditorTimer)
      this.circleEditorTimer = setTimeout(() => {
        circle.setOptions({ strokeOpacity: 1 })
        // circleEditor.close()
        this.circleEditorShow = false
      }, 1000)
    },
    /**
     * 初始化事件范围控制器
     * @param scene  场景对象
     */
    initCircle(scene) {
      const { map } = scene
      const _this = this
      const { lngLat } = this
      this.circle = new AMap.Circle({
        center: new AMap.LngLat(Number(lngLat.lng), Number(lngLat.lat)),
        radius: this.radarWaveRadius, //半径
        strokeColor: '#fff',
        strokeOpacity: 1,
        strokeWeight: 1,
        strokeStyle: 'dashed',
        strokeDasharray: [10, 10],
        fillColor: '#1791fc',
        fillOpacity: 0,
        zIndex: 1000,
      })
      this.circle.setMap(map)
      this.circleEditor = new AMap.CircleEditor(map, this.circle)
      this.circleEditor.on('move', function (event) {
        // console.log('触发事件：move')
        if (_this.radarWaveRadiusChangeTimer)
          clearTimeout(_this.radarWaveRadiusChangeTimer)
        _this.radarWaveRadiusChangeTimer = setTimeout(
          _this.resetCircleCenter,
          500
        )
      })
      this.circleEditor.on('adjust', function (event) {
        console.log('触发事件：adjust')
        if (_this.radarWaveRadiusChangeTimer)
          clearTimeout(_this.radarWaveRadiusChangeTimer)
        _this.radarWaveRadiusChangeTimer = setTimeout(() => {
          _this.radarWaveRadius = event.radius
          _this.resetRange()
        }, 1000)
        // closeCircleEditor(circle, circleEditor)
      })
      this.circleEditor.open()
    },
    /**
     * 重置控制器中心
     */
    resetCircleCenter() {
      const { lngLat } = this
      this.circle.setOptions({
        center: new AMap.LngLat(Number(lngLat.lng), Number(lngLat.lat)),
      })
      this.circleEditor.close()
      this.circleEditor.open()
    },

    /**
     * marker选择器变化
     */
    markerChange(marker) {
      this.showMarkerNameList = marker
    },
  },
  computed: {
    // 要显示的marker数据
    showMarkerList() {
      const res = []
      this.showMarkerNameList.forEach((item) => {
        this.markerList[item] &&
          this.markerList[item].forEach((ele) => {
            ele.name = item
            res.push(ele)
          })
      })
      return res
    },
  },
}
</script>

<style lang="less" scoped>
.map-container {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 1;

  /deep/ .l7-marker-container {
    pointer-events: none;
  }

  /deep/ .amap-marker-label {
    border: 0;
    background-color: transparent;
    color: #fff;
    font-size: 16px;
  }

  /deep/ .l7-popup {
    z-index: 999;
    .l7-popup-content {
      padding: 0;
      background-color: transparent;
      z-index: 999;

      .l7-popup-close-button {
        display: none;
      }
    }

    .l7-popup-tip {
      display: none;
    }
  }
}
#map {
  width: 100%;
  height: 100%;
  position: relative;
}
</style>
