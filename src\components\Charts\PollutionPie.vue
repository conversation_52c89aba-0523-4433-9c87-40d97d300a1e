<template>
  <div :id="id" :style="{ height: height, width: width }"></div>
</template>

<script lang="ts">
import * as echarts from 'echarts'
import { Component, Prop } from "vue-property-decorator";
import { mixins } from "vue-class-component";
import ResizeMixin from "./mixins/resize";
interface InData {
  name: string;
  number: number;
  total: number;
  startColor: string;
  endColor: string;
}
@Component({
  name: "PollutionPie"
})
export default class extends mixins(ResizeMixin) {
  @Prop({ default: "chart" }) private id!: string;
  @Prop({ default: "200px" }) private width!: string;
  @Prop({ default: "200px" }) private height!: string;
  @Prop({ required: true }) private propData!: InData;
  mounted() {
    setTimeout(() => {
      this.initChart();
    }, 2000);
  }

  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.clear();
    this.chart.dispose();
    this.chart = null;
  }

  private initChart() {

    if(this.chart === null || this.chart === undefined) {
      this.chart = echarts.init(
        document.getElementById(this.id) as HTMLDivElement
      );
    }
    this.chart.setOption({
      title: {
        text: this.propData.name,
        left: "center",
        bottom: 0,
        textStyle: {
          color: "white",
          fontSize: 14
        }
      },
      series: [
        {
          radius: ["60%", "64%"],
          center: ["50%", "41%"],
          hoverAnimation: false,
          // clockwise: false,
          type: "pie",
          itemStyle: {
            borderColor:
              this.propData.total == 0 && this.propData.number == 0
                ? "#B3B3B3"
                : new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: this.propData.startColor
                    },
                    {
                      offset: 1,
                      color: this.propData.endColor
                    }
                  ]),
            borderWidth:
              this.propData.total == 0 && this.propData.number == 0 ? 0 : 5
          },
          label: {
            show: true,
            textStyle: {
              fontSize: 20,
              color: "#ffffff"
            },
            position: "center",
            formatter: "{c}天"
          },
          data: [
            {
              value: this.propData.number
            },
            {
              name: "123",
              value: this.propData.total,
              itemStyle: {
                color: "#B3B3B3",
                borderWidth: 0
              },
              label: {
                show: false
              }
            }
          ]
        }
      ]
    } as EChartOption);
  }
}
</script>
