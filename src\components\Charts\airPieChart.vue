<template>
  <div class="container">
    <div :id="id" :style="{ height: height, width: width }" />
    <div v-if="toolTipName !== ''" class="tool-tip-box">
      {{ toolTipName }}：{{ toolTipVal }}天
    </div>
  </div>
</template>

<script lang="ts">
import * as echarts from 'echarts'
import { Component, Prop, Watch } from "vue-property-decorator";
import { mixins } from "vue-class-component";
import ResizeMixin from "./mixins/resize";
interface InData {
  name: string;
  dataList: any[];
  colorList: string[];
}
// 存储图表实例的变量
let chart: any = null;

@Component({
  name: "airPieChart",
})
export default class extends mixins(ResizeMixin) {
  // @Prop({ default: "chart" }) private className!: string;
  @Prop({ default: "charts" }) private id!: string;
  @Prop({ default: "200px" }) private width!: string;
  @Prop({ default: "200px" }) private height!: string;
  @Prop({ required: true }) private PieChartData!: InData;
  // private chart: any;
  private timer: any;
  private chartCurrentIndex = 0;
  private colors: string[] = [];
  private option: any;

  private toolTipName = "";
  private toolTipVal: string | number = "";
  mounted() {
    this.initChart();
  }
  beforeDestroy() {
    clearInterval(this.timer);
  }
  @Watch("PieChartData", { immediate: false, deep: true })
  public onMsgChanged(newValue: InData, oldValue: InData) {
    this.initChart();
  }
  private initChart() {
    this.colors = this.PieChartData.colorList;
    this.option = {
      color: this.colors,
      title: [
        {
          textStyle: {
            color: "white",
            fontSize: 14,
          },
          left: "39%",
          top: "44%",
        },
      ],
      /* tooltip: {
        show: true,
        trigger: "item",
        formatter: "{b} : {c} 天",
        backgroundColor: "#1D67FF",
        fontSize: 12
      }, */
      // graphic: {
      //   //图形中间文字
      //   type: "text",
      //   left: "center",
      //   top: "center",
      //   style: {
      //     text: "66",
      //     textAlign: "center",
      //     fill: "#fff",
      //     fontSize: 60
      //   }
      // },
      series: [
        {
          // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
          // @ts-ignore
          // name: this.PieChartData.name,
          type: "pie",
          radius: ["60%", "80%"],
          center: ["50%", "50%"],
          data: this.PieChartData.dataList,
          itemStyle: {
            normal: {
              label: {
                show: false, //隐藏标示文字
              },
              labelLine: {
                show: false, //隐藏标示线
              },
            },
          },
          label: {
            show: true,
          },
        },
      ],
    };
    if (chart === null || chart === undefined) {
      chart = echarts.init(document.getElementById(this.id) as HTMLDivElement);
      this.$once("hook:beforeDestroy", () => {
        if (!chart) {
          return;
        }
        chart.on("mouseover", () => {});
        chart.on("mouseout", () => {});
        chart.clear();
        chart.dispose();
        chart = null;
      });
    }
    chart.setOption(this.option as EChartOption<EChartOption>);
    clearInterval(this.timer);
    this.timer = setInterval(() => {
      this.autoPlayTool();
    }, 2000);
    this.$once("hook:beforeDestroy", () => {
      clearInterval(this.timer);
      this.timer = null;
    });

    //鼠标移动上去的时候的高亮动画
    chart.on("mouseover", (param: any) => {
      clearInterval(this.timer);
      // 取消之前高亮的图形
      (chart as any).dispatchAction({
        type: "downplay",
        seriesIndex: 0,
        // dataIndex: this.chartCurrentIndex
      });
      this.chartCurrentIndex = param.dataIndex;
      // 高亮当前图形
      (chart as any).dispatchAction({
        type: "highlight",
        seriesIndex: 0,
        dataIndex: this.chartCurrentIndex,
      });
      // 显示 tooltip
      const curData = this.PieChartData.dataList[param.dataIndex];

      this.toolTipName = curData.name;
      this.toolTipVal = curData.value;
    });
    // 鼠标移出之后，恢复自动高亮
    chart.on("mouseout", (param: any) => {
      // 显示 tooltip
      const curData = this.PieChartData.dataList[param.dataIndex];

      this.toolTipName = curData.name;
      this.toolTipVal = curData.value;

      clearInterval(this.timer);
      this.timer = setInterval(() => {
        this.autoPlayTool();
      }, 2000);

      this.$once("hook:beforeDestroy", () => {
        clearInterval(this.timer);
        this.timer = null;
      });
    });
  }
  /**
   * @param {chartIns} echarts实例
   * @param {chartDate} echarts数据
   * @param {curIndex} 当前要展示的数据index
   */
  private autoPlayTool(): void {
    const dataLen = this.option.series[0].data.length;
    // 取消之前高亮的图形
    (chart as any).dispatchAction({
      type: "downplay",
      seriesIndex: 0,
      dataIndex: this.chartCurrentIndex,
    });
    this.chartCurrentIndex =
      this.chartCurrentIndex + 1 >= dataLen ? 0 : this.chartCurrentIndex + 1;

    const curData = this.PieChartData.dataList[this.chartCurrentIndex];

    this.toolTipName = curData.name;
    this.toolTipVal = curData.value;

    // 高亮当前图形
    (chart as any).dispatchAction({
      type: "highlight",
      seriesIndex: 0,
      dataIndex: this.chartCurrentIndex,
    });
  }
}
</script>

<style lang="less" scoped>
.container {
  position: relative;

  .tool-tip-box {
    position: absolute;
    top: -10px;
    left: 0;
    background-color: #1d67ff;
    font-size: 12px;
    padding: 5px;
    border-radius: 6px;
  }
}
</style>
