import request from "@/utils/request";
import { AxiosPromise } from "axios";

export function riverCount(startDate: string, endDate: string): AxiosPromise<any> {
  return request({
    url: "/water/bigData/river-assessment-goal-month/riverCount",
    method: "get",
    params: {
      startDate,
      endDate,
    },
  });
}

export function riverList(): AxiosPromise<any> {
  return request({
    url: "/water/bigData/river-assessment-goal-month/riverList",
    method: "get",
  });
}

export function noiseStreetCount(params): AxiosPromise<any> {
  return request({
    url: "/water/bigData/noise-street-days/streetCount",
    method: "get",
    params,
  });
}

export function streetFindByTime(params): AxiosPromise<any> {
  return request({
    url: "/air/bigData/aqhi/streetFindByTime",
    method: "get",
    params,
  });
}

export function countyHour(): AxiosPromise<any> {
  return request({
    url: "/air/bigData/aqhi/countyHour",
    method: "get",
  });
}

/**
 * 获取最新的国民体育健康指数
*/
export function getNationalSportsIndex(): AxiosPromise<any> {
  return request({
    url: "/air/bigData/cdMotionIndex/getLast",
    method: "get",
  });
}
